#!/bin/bash

# 检查仓库中的大文件

MAX_SIZE=52428800  # 50MB
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}检查仓库中大于50MB的文件...${NC}"
echo ""

large_files_found=0

# 查找大文件
find . -type f -not -path './.git/*' | while read -r file; do
    if [ -f "$file" ]; then
        # 兼容不同系统
        if command -v stat >/dev/null 2>&1; then
            file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo 0)
        else
            file_size=$(ls -l "$file" | awk '{print $5}')
        fi
        
        if [ "$file_size" -gt "$MAX_SIZE" ]; then
            file_size_mb=$((file_size / 1024 / 1024))
            echo -e "${RED}✗ 大文件: $file (${file_size_mb}MB)${NC}"
            large_files_found=$((large_files_found + 1))
        fi
    fi
done

if [ $large_files_found -eq 0 ]; then
    echo -e "${GREEN}✓ 未发现大于50MB的文件${NC}"
fi
