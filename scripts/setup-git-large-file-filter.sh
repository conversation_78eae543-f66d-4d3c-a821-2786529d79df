#!/bin/bash

# Git大文件过滤配置脚本
# 功能：配置Git仓库忽略大于50M的文件
# 作者：AI Assistant
# 版本：1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置参数
MAX_FILE_SIZE="50M"
MAX_FILE_SIZE_BYTES=52428800  # 50MB

echo -e "${BLUE}=== Git大文件过滤器配置脚本 ===${NC}"
echo -e "${YELLOW}最大文件大小限制: ${MAX_FILE_SIZE}${NC}"
echo ""

# 检查是否在Git仓库中
check_git_repo() {
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        echo -e "${RED}错误: 当前目录不是Git仓库${NC}"
        echo "请在Git仓库根目录下运行此脚本"
        exit 1
    fi
    echo -e "${GREEN}✓ 检测到Git仓库${NC}"
}

# 创建pre-commit钩子
create_pre_commit_hook() {
    echo -e "${BLUE}创建pre-commit钩子...${NC}"
    
    mkdir -p .git/hooks
    
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash

# Git pre-commit钩子：检查文件大小
# 阻止提交大于50MB的文件

MAX_SIZE=52428800  # 50MB
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m'

echo "检查文件大小..."

# 获取即将提交的文件列表
files=$(git diff --cached --name-only --diff-filter=ACM)

large_files=()
total_large_files=0

for file in $files; do
    if [ -f "$file" ]; then
        # 兼容不同系统的stat命令
        if command -v stat >/dev/null 2>&1; then
            file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo 0)
        else
            file_size=$(ls -l "$file" | awk '{print $5}')
        fi
        
        if [ "$file_size" -gt "$MAX_SIZE" ]; then
            large_files+=("$file")
            # 计算MB大小
            file_size_mb=$((file_size / 1024 / 1024))
            echo -e "${RED}✗ 文件过大: $file (${file_size_mb}MB)${NC}"
            total_large_files=$((total_large_files + 1))
        fi
    fi
done

if [ $total_large_files -gt 0 ]; then
    echo ""
    echo -e "${RED}发现 $total_large_files 个大于50MB的文件，提交被阻止！${NC}"
    echo ""
    echo -e "${YELLOW}解决方案:${NC}"
    echo "1. 将大文件添加到 .gitignore"
    echo "2. 使用 Git LFS 管理大文件"
    echo "3. 压缩或分割文件"
    echo "4. 移除大文件后重新提交"
    echo ""
    echo -e "${YELLOW}如需强制提交，使用: git commit --no-verify${NC}"
    exit 1
fi

echo -e "${GREEN}✓ 所有文件大小检查通过${NC}"
exit 0
EOF

    chmod +x .git/hooks/pre-commit
    echo -e "${GREEN}✓ pre-commit钩子创建成功${NC}"
}

# 配置Git设置
setup_git_config() {
    echo -e "${BLUE}配置Git设置...${NC}"
    
    # 设置文件大小警告阈值
    git config core.bigFileThreshold 50m
    
    # 设置pack文件大小限制
    git config pack.packSizeLimit 50m
    
    echo -e "${GREEN}✓ Git配置完成${NC}"
}

# 更新.gitignore文件
update_gitignore() {
    echo -e "${BLUE}更新.gitignore文件...${NC}"
    
    # 检查.gitignore是否已存在大文件规则
    if ! grep -q "# 大文件过滤规则" .gitignore 2>/dev/null; then
        cat >> .gitignore << 'EOF'

# 大文件过滤规则 (>50MB)
# 压缩文件
*.zip
*.tar.gz
*.tar.bz2
*.rar
*.7z
*.iso
*.dmg

# 媒体文件
*.mp4
*.avi
*.mkv
*.mov
*.wmv
*.mp3
*.wav
*.flac

# 数据库文件
*.db
*.sqlite
*.mdb

# 日志和临时文件
*.log
*.tmp
*.cache
*.bak

# 编译产物
*.exe
*.dll
*.so
*.dylib
EOF
        echo -e "${GREEN}✓ .gitignore文件已更新${NC}"
    else
        echo -e "${YELLOW}! .gitignore文件已包含大文件规则${NC}"
    fi
}

# 创建大文件检查脚本
create_check_script() {
    echo -e "${BLUE}创建大文件检查脚本...${NC}"
    
    cat > scripts/check-large-files.sh << 'EOF'
#!/bin/bash

# 检查仓库中的大文件

MAX_SIZE=52428800  # 50MB
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}检查仓库中大于50MB的文件...${NC}"
echo ""

large_files_found=0

# 查找大文件
find . -type f -not -path './.git/*' | while read -r file; do
    if [ -f "$file" ]; then
        # 兼容不同系统
        if command -v stat >/dev/null 2>&1; then
            file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo 0)
        else
            file_size=$(ls -l "$file" | awk '{print $5}')
        fi
        
        if [ "$file_size" -gt "$MAX_SIZE" ]; then
            file_size_mb=$((file_size / 1024 / 1024))
            echo -e "${RED}✗ 大文件: $file (${file_size_mb}MB)${NC}"
            large_files_found=$((large_files_found + 1))
        fi
    fi
done

if [ $large_files_found -eq 0 ]; then
    echo -e "${GREEN}✓ 未发现大于50MB的文件${NC}"
fi
EOF

    chmod +x scripts/check-large-files.sh
    echo -e "${GREEN}✓ 大文件检查脚本创建成功${NC}"
}

# 创建使用说明
create_readme() {
    echo -e "${BLUE}创建使用说明...${NC}"
    
    cat > scripts/README.md << 'EOF'
# Git大文件过滤器

## 功能说明

此脚本配置Git仓库自动忽略大于50MB的文件，防止意外提交大文件。

## 已配置的功能

1. **pre-commit钩子**: 提交前自动检查文件大小
2. **Git配置**: 设置文件大小警告阈值
3. **gitignore规则**: 添加常见大文件类型过滤
4. **检查脚本**: 扫描现有大文件

## 使用方法

### 运行配置脚本
```bash
./scripts/setup-git-large-file-filter.sh
```

### 检查大文件
```bash
./scripts/check-large-files.sh
```

### 强制提交（跳过检查）
```bash
git commit --no-verify -m "提交信息"
```

## 文件说明

- `setup-git-large-file-filter.sh`: 主配置脚本
- `check-large-files.sh`: 大文件检查脚本
- `.git/hooks/pre-commit`: 提交前检查钩子
- `.gitignore`: 更新的忽略规则

## 注意事项

- 配置后所有大于50MB的文件提交都会被阻止
- 如需提交大文件，建议使用Git LFS
- 强制提交会跳过所有检查，请谨慎使用
EOF

    echo -e "${GREEN}✓ 使用说明创建成功${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}开始配置Git大文件过滤器...${NC}"
    echo ""
    
    # 执行配置步骤
    check_git_repo
    create_pre_commit_hook
    setup_git_config
    update_gitignore
    create_check_script
    create_readme
    
    echo ""
    echo -e "${GREEN}=== 配置完成 ===${NC}"
    echo ""
    echo -e "${YELLOW}已创建/更新的文件:${NC}"
    echo "• .git/hooks/pre-commit - 提交前检查钩子"
    echo "• scripts/check-large-files.sh - 大文件检查脚本"
    echo "• scripts/README.md - 使用说明"
    echo "• .gitignore - 大文件过滤规则"
    echo ""
    echo -e "${YELLOW}使用方法:${NC}"
    echo "• 检查大文件: ./scripts/check-large-files.sh"
    echo "• 正常提交时会自动检查文件大小"
    echo "• 强制提交: git commit --no-verify"
    echo ""
    echo -e "${GREEN}✓ Git仓库已配置为自动忽略大于50MB的文件${NC}"
}

# 运行主函数
main "$@"
