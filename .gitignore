robot/mpp/mpp

**/.history
**/.lh
**/.trash

**/*sync-conf*

.stfolder
.DS_Store
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
.obsidian

.stversions
.smtcmp_json_db
.smtcmp_vector_db.tar.gz

# 编辑器文件
.idea/
*.swp
*.swo
*~

# 临时文件
*.tmp
*.temp
*.log
*.bak
*.backup

# 系统文件
Thumbs.db
.DS_Store
.fseventsd
.Spotlight-V100
.Trashes

# 压缩文件
*.tar.gz
*.zip
*.rar
*.7z

# 编译文件
*.o
*.so
*.a
*.exe
*.dll
*.dylib

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 其他
*.pid
*.seed
*.pid.lock


large_files_backup
projects/ble/build
projects/network-seamless-switching/bin
cursor-deb-package
Code/LubanCat_SDK
cursor-deb-package/usr/share/cursor/cursor
# 大文件过滤规则 (>50MB)
# 压缩文件
*.zip
*.tar.gz
*.tar.bz2
*.rar
*.7z
*.iso
*.dmg

# 媒体文件
*.mp4
*.avi
*.mkv
*.mov
*.wmv
*.mp3
*.wav
*.flac

# 数据库文件
*.db
*.sqlite
*.mdb

# 日志和临时文件
*.log
*.tmp
*.cache
*.bak

# 编译产物
*.exe
*.dll
*.so
*.dylib
