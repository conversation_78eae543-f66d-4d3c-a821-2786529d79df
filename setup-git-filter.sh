#!/bin/bash

# Git大文件过滤配置脚本
# 配置Git仓库忽略大于50M的文件

echo "=== Git大文件过滤器配置 ==="
echo "配置仓库忽略大于50MB的文件..."

# 检查是否在Git仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "错误: 当前目录不是Git仓库"
    exit 1
fi

# 1. 创建pre-commit钩子
echo "创建pre-commit钩子..."
mkdir -p .git/hooks

cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# 检查文件大小，阻止提交大于50MB的文件

MAX_SIZE=52428800  # 50MB
files=$(git diff --cached --name-only --diff-filter=ACM)

for file in $files; do
    if [ -f "$file" ]; then
        size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null)
        if [ "$size" -gt "$MAX_SIZE" ]; then
            echo "错误: 文件 $file 大小超过50MB，提交被阻止"
            echo "文件大小: $(echo "scale=2; $size/1024/1024" | bc)MB"
            exit 1
        fi
    fi
done
EOF

chmod +x .git/hooks/pre-commit

# 2. 配置Git设置
echo "配置Git设置..."
git config core.bigFileThreshold 50m

# 3. 更新.gitignore
echo "更新.gitignore..."
cat >> .gitignore << 'EOF'

# 大文件过滤 (>50MB)
*.zip
*.tar.gz
*.rar
*.7z
*.iso
*.mp4
*.avi
*.mkv
*.mov
*.mp3
*.wav
*.db
*.sqlite
*.log
*.tmp
*.bak
EOF

# 4. 创建检查脚本
echo "创建大文件检查脚本..."
cat > check-large-files.sh << 'EOF'
#!/bin/bash
echo "检查仓库中的大文件..."
find . -type f -not -path './.git/*' -exec ls -lh {} \; | awk '$5 ~ /[0-9]+M/ && $5+0 > 50 {print "大文件: " $9 " (" $5 ")"}'
EOF

chmod +x check-large-files.sh

echo ""
echo "✓ 配置完成！"
echo ""
echo "功能说明:"
echo "• 提交时自动检查文件大小"
echo "• 大于50MB的文件会被阻止提交"
echo "• 运行 ./check-large-files.sh 检查现有大文件"
echo "• 如需强制提交: git commit --no-verify"
echo ""
echo "Git仓库已配置为忽略大于50MB的文件"
