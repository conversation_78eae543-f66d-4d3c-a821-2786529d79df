# 🚀 外设检测Web平台部署指南

## 快速部署选择

| 部署方式 | 适用场景 | 难度 | 推荐指数 |
|---------|---------|------|---------|
| Docker部署 | 生产环境 | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| 传统部署 | 资源受限环境 | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| 开发环境 | 本地开发测试 | ⭐ | ⭐⭐⭐ |

## 🐳 Docker部署（推荐）

### 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- 2GB+ 内存
- 10GB+ 磁盘空间

### 部署步骤

```bash
# 1. 下载项目
git clone <repository-url>
cd web

# 2. 配置环境（重要！）
cp .env.production .env
nano .env  # 修改数据库密码和密钥

# 3. 一键部署
chmod +x deploy.sh
./deploy.sh

# 4. 验证部署
curl http://localhost/health
```

### 访问地址
- **前端应用**: http://localhost
- **API文档**: http://localhost/docs
- **健康检查**: http://localhost/health

### 管理命令
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f web

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 更新部署
git pull
docker-compose build
docker-compose up -d
```

## 🖥️ 传统部署

### 环境要求
- Ubuntu 20.04+ / CentOS 8+
- Python 3.8+
- Node.js 16+
- PostgreSQL 12+ (可选)
- Redis 6+ (可选)

### 部署步骤

```bash
# 1. 安装依赖
sudo apt update
sudo apt install python3 python3-pip nodejs npm postgresql redis-server

# 2. 下载项目
git clone <repository-url>
cd web

# 3. 执行部署脚本
chmod +x deploy_traditional.sh
sudo ./deploy_traditional.sh

# 4. 验证部署
curl http://localhost:8000/health
```

### 服务管理
```bash
# 查看服务状态
sudo systemctl status peripheral-web

# 启动/停止/重启服务
sudo systemctl start peripheral-web
sudo systemctl stop peripheral-web
sudo systemctl restart peripheral-web

# 查看日志
sudo journalctl -u peripheral-web -f

# 开机自启
sudo systemctl enable peripheral-web
```

## 💻 开发环境

### 环境要求
- Python 3.8+
- Node.js 16+

### 快速启动

```bash
# 1. 进入项目目录
cd web

# 2. 一键启动（推荐）
python start_all.py

# 或者分别启动
python start_server.py    # 后端
python start_frontend.py  # 前端（新终端）
```

### 访问地址
- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 🔧 配置说明

### 环境变量配置

编辑 `.env` 文件：

```env
# 基础配置
DEBUG=false                    # 生产环境设为false
HOST=0.0.0.0                  # 监听地址
PORT=8000                     # 监听端口

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/peripheral_test

# 安全配置（必须修改）
SECRET_KEY=your-random-secret-key-here

# CORS配置（生产环境限制域名）
ALLOWED_HOSTS=["your-domain.com"]
```

### 数据库配置

#### SQLite（开发环境）
```env
DATABASE_URL=sqlite:///./peripheral_test.db
```

#### PostgreSQL（生产环境）
```env
DATABASE_URL=postgresql://postgres:password@localhost:5432/peripheral_test
```

## 🔒 安全配置

### 1. 修改默认密码

```bash
# 生成随机密钥
python -c "import secrets; print(secrets.token_urlsafe(32))"

# 修改 .env 文件
SECRET_KEY=生成的随机密钥
```

### 2. 配置HTTPS

```bash
# 安装Certbot
sudo apt install certbot

# 获取SSL证书
sudo certbot certonly --standalone -d your-domain.com

# 配置Nginx（编辑nginx.conf）
```

### 3. 防火墙设置

```bash
# Ubuntu/Debian
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

## 📊 监控和维护

### 健康检查

```bash
# 使用监控脚本
chmod +x monitor.sh
./monitor.sh --status      # 查看状态
./monitor.sh --monitor     # 执行检查

# 设置定时监控（每5分钟检查一次）
echo "*/5 * * * * /path/to/web/monitor.sh" | crontab -
```

### 日志管理

```bash
# Docker方式
docker-compose logs -f web

# 系统服务方式
sudo journalctl -u peripheral-web -f

# 应用日志
tail -f logs/web_platform_*.log
```

### 数据备份

```bash
# PostgreSQL备份
pg_dump -U postgres peripheral_test > backup_$(date +%Y%m%d).sql

# 恢复数据
psql -U postgres peripheral_test < backup_20240101.sql

# 文件备份
tar -czf backup_$(date +%Y%m%d).tar.gz data/ logs/ uploads/
```

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   sudo netstat -tlnp | grep :8000
   
   # 检查日志
   sudo journalctl -u peripheral-web -n 50
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   sudo systemctl status postgresql
   
   # 测试连接
   psql -U postgres -h localhost -d peripheral_test
   ```

3. **前端无法访问**
   ```bash
   # 检查Nginx状态
   sudo systemctl status nginx
   
   # 检查配置
   sudo nginx -t
   ```

### 性能优化

1. **数据库优化**
   ```sql
   -- 创建索引
   CREATE INDEX idx_devices_status ON devices(status);
   CREATE INDEX idx_test_results_timestamp ON test_results(timestamp);
   ```

2. **缓存配置**
   ```env
   REDIS_URL=redis://localhost:6379/0
   CACHE_TTL=300
   ```

## 📞 技术支持

如遇到部署问题，请提供以下信息：

1. 操作系统版本
2. 部署方式（Docker/传统）
3. 错误日志
4. 配置文件内容（隐藏敏感信息）

联系方式：[技术支持邮箱或Issue地址]
