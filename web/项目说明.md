# 外设检测Web平台

基于FastAPI + React的完整Web前后端系统，用于设备测试数据的上报、展示和管控。

## 🏗️ 技术栈

**后端**:
- FastAPI - 现代、快速的Web框架
- SQLAlchemy - ORM数据库操作
- WebSocket - 实时通信
- SQLite/PostgreSQL - 数据存储
- Redis - 缓存和会话管理

**前端**:
- React 18 - 用户界面框架
- TypeScript - 类型安全
- Ant Design - UI组件库
- ECharts - 数据可视化
- WebSocket - 实时数据更新

## 📁 项目结构

```
web/
├── backend/                    # 后端服务
│   ├── app/
│   │   ├── api/               # API路由
│   │   ├── core/              # 核心配置
│   │   ├── models/            # 数据模型
│   │   ├── services/          # 业务服务
│   │   ├── utils/             # 工具类
│   │   └── main.py            # 主应用
│   └── requirements.txt       # Python依赖
├── frontend/                   # 前端应用
│   ├── src/
│   │   ├── components/        # 组件
│   │   ├── pages/             # 页面
│   │   ├── services/          # 服务
│   │   ├── types/             # 类型定义
│   │   └── App.tsx            # 主应用
│   ├── public/                # 静态文件
│   └── package.json           # Node.js依赖
├── start_server.py            # 启动脚本
└── README.md                  # 项目文档
```

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- Node.js 16+
- npm 或 yarn

### 2. 一键启动（推荐）

```bash
# 进入项目目录
cd web

# 一键启动前后端
python start_all.py
```

### 3. 分别启动

#### 后端启动
```bash
cd web
pip install -r backend/requirements.txt
python start_server.py
```

#### 前端启动
```bash
cd web
python start_frontend.py
# 或者
cd frontend
npm install
npm start
```

### 4. 服务地址

- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs
- WebSocket: ws://localhost:8000/ws

### 5. 功能测试

```bash
# 运行集成测试
python test_integration.py
```

### 6. 生产部署

#### Docker部署（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd web

# 2. 配置环境变量
cp .env.production .env
# 编辑 .env 文件，修改数据库密码和密钥

# 3. 一键部署
chmod +x deploy.sh
./deploy.sh
```

#### 传统部署

```bash
# 1. 准备环境
# 确保已安装 Python 3.8+, Node.js 16+

# 2. 执行部署脚本
chmod +x deploy_traditional.sh
sudo ./deploy_traditional.sh
```

#### 手动部署

```bash
# 1. 构建前端
cd frontend
npm install
npm run build

# 2. 安装后端依赖
cd ..
pip install -r backend/requirements.txt

# 3. 配置环境
cp .env.production .env
# 编辑配置文件

# 4. 启动服务
python start_server.py
```

## 📊 核心功能

### 1. 设备管理
- ✅ 设备列表查看和筛选
- ✅ 设备详情展示
- ✅ 设备状态实时更新
- ✅ 设备数据上报接口

### 2. 测试结果管理
- ✅ 测试结果记录和查询
- ✅ 测试历史追踪
- ✅ 测试统计分析
- ✅ 测试趋势图表

### 3. 实时数据
- ✅ WebSocket实时通信
- ✅ 设备状态实时更新
- ✅ 测试结果实时推送
- ✅ 系统告警通知

### 4. 数据可视化
- ✅ 仪表板统计图表
- ✅ 设备状态分布图
- ✅ 测试趋势分析
- ✅ 实时数据监控

## 🔌 API接口

### WebSocket消息格式

#### 设备数据上报
```json
{
  "type": "device_report",
  "device_data": {
    "device_id": "SN123456789",
    "ip_address": "*************",
    "device_type": "RobDog",
    "firmware_version": "1.0.0",
    "test_results": {
      "light": {
        "status": "passed",
        "timestamp": "2024-01-01T12:00:00",
        "details": {},
        "duration_ms": 1500
      }
    }
  }
}
```

#### 获取设备列表
```json
{
  "type": "get_device_list",
  "page": 1,
  "page_size": 20,
  "filters": {
    "device_type": "RobDog",
    "status": "passed"
  }
}
```

### REST API

#### 获取设备列表
```
GET /api/v1/devices?page=1&page_size=20&device_type=RobDog
```

#### 获取设备详情
```
GET /api/v1/devices/{device_id}
```

#### 上报设备数据
```
POST /api/v1/devices/report
```

#### 获取仪表板统计
```
GET /api/v1/dashboard/stats
```

## 🔧 配置说明

### 环境变量

创建 `.env` 文件配置环境变量：

```env
# 基础配置
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=sqlite:///./peripheral_test.db
# DATABASE_URL=postgresql://user:password@localhost/peripheral_test

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your-secret-key-change-in-production

# CORS配置
ALLOWED_HOSTS=["*"]
```

### 数据库配置

默认使用SQLite，生产环境建议使用PostgreSQL：

```python
# SQLite (开发)
DATABASE_URL = "sqlite:///./peripheral_test.db"

# PostgreSQL (生产)
DATABASE_URL = "postgresql://user:password@localhost/peripheral_test"
```

## 🧪 测试

### 后端测试
```bash
cd backend
pytest
```

### 前端测试
```bash
cd frontend
npm test
```

## 📝 开发指南

### 添加新的API端点

1. 在 `backend/app/api/` 中创建路由文件
2. 在 `backend/app/main.py` 中注册路由
3. 在前端 `src/services/api.ts` 中添加API调用

### 添加新的页面

1. 在 `frontend/src/pages/` 中创建页面组件
2. 在 `frontend/src/App.tsx` 中添加路由
3. 在布局组件中添加菜单项

### WebSocket消息处理

1. 在后端 `WebSocketManager` 中添加消息处理器
2. 在前端使用 `useWebSocket` Hook订阅消息

## 🐛 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查后端服务是否启动
   - 确认端口没有被占用
   - 检查防火墙设置

2. **前端无法连接后端**
   - 检查代理配置 (package.json中的proxy)
   - 确认后端CORS设置
   - 检查网络连接

3. **数据库错误**
   - 检查数据库连接字符串
   - 确认数据库权限
   - 查看错误日志

### 日志查看

```bash
# 后端日志
tail -f logs/web_platform_*.log

# 前端开发日志
# 在浏览器控制台查看
```

## 🚀 部署方案

### 1. Docker部署（生产推荐）

**优势**: 环境一致性、易于扩展、便于维护

```bash
# 快速部署
git clone <repository-url>
cd web
chmod +x deploy.sh
./deploy.sh
```

**访问地址**: http://localhost

### 2. 传统部署

**优势**: 资源占用少、便于调试

```bash
# 系统要求: Ubuntu 20.04+, Python 3.8+, Node.js 16+
sudo ./deploy_traditional.sh
```

**访问地址**: http://localhost:8000

### 3. 开发环境

```bash
# 一键启动
python start_all.py
```

**访问地址**:
- 前端: http://localhost:3000
- 后端: http://localhost:8000

## 🔧 运维管理

### 服务管理

```bash
# Docker方式
docker-compose ps          # 查看状态
docker-compose logs -f     # 查看日志
docker-compose restart     # 重启服务
docker-compose down        # 停止服务

# 系统服务方式
sudo systemctl status peripheral-web    # 查看状态
sudo systemctl restart peripheral-web   # 重启服务
sudo journalctl -u peripheral-web -f    # 查看日志
```

### 监控检查

```bash
# 使用监控脚本
chmod +x monitor.sh
./monitor.sh --status      # 查看状态
./monitor.sh --logs        # 查看日志
./monitor.sh --monitor     # 执行检查

# 设置定时监控
echo "*/5 * * * * /path/to/monitor.sh" | crontab -
```

### 数据备份

```bash
# Docker方式备份数据库
docker-compose exec db pg_dump -U postgres peripheral_test > backup.sql

# 传统方式备份
pg_dump -U postgres -h localhost peripheral_test > backup.sql
```

## 🔒 安全配置

### 1. 修改默认密码

编辑 `.env` 文件：
```env
SECRET_KEY=your-random-secret-key-here
DATABASE_URL=postgresql://postgres:your_secure_password@localhost:5432/peripheral_test
```

### 2. 配置HTTPS

```bash
# 获取SSL证书（Let's Encrypt）
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# 配置Nginx SSL
# 编辑 nginx.conf，取消注释HTTPS配置部分
```

### 3. 防火墙设置

```bash
# 开放必要端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

## 📊 性能优化

### 1. 数据库优化

```sql
-- 创建索引
CREATE INDEX idx_devices_status ON devices(status);
CREATE INDEX idx_test_results_timestamp ON test_results(timestamp);
```

### 2. 缓存配置

```env
# Redis缓存配置
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=300
```

### 3. 负载均衡

使用Nginx配置多实例负载均衡：
```nginx
upstream web_backend {
    server web1:8000;
    server web2:8000;
    server web3:8000;
}
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 联系方式

如有问题，请联系开发团队。
