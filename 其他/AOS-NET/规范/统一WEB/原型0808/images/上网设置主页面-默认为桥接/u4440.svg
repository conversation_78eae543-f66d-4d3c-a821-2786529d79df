﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="835px" height="12px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="253px" y="750px" width="835px" height="12px" filterUnits="userSpaceOnUse" id="filter22">
      <feOffset dx="5" dy="5" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.34901960784313724 0  " in="shadowComposite" />
    </filter>
    <g id="widget23">
      <path d="M 253 750.5  L 1077 750.5  " stroke-width="1" stroke="#000000" fill="none" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -253 -750 )">
    <use xlink:href="#widget23" filter="url(#filter22)" />
    <use xlink:href="#widget23" />
  </g>
</svg>