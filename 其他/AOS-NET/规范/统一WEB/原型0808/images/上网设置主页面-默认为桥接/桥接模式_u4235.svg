﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="229px" height="274px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="0px" y="0px" width="229px" height="274px" filterUnits="userSpaceOnUse" id="filter8">
      <feOffset dx="5" dy="5" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2588235294117647 0  " in="shadowComposite" />
    </filter>
    <g id="widget9">
      <path d="M 0.5 15.000000000000004  A 14.5 14.5 0 0 1 14.999999999999996 0.5 L 204 0.5  A 14.5 14.5 0 0 1 218.5 15 L 218.5 249  A 14.5 14.5 0 0 1 204 263.5 L 15 263.5  A 14.5 14.5 0 0 1 0.5 249 L 0.5 15  Z " fill-rule="nonzero" fill="#d3d3d3" stroke="none" fill-opacity="0.9921568627450981" />
      <path d="M 0.5 15.000000000000004  A 14.5 14.5 0 0 1 14.999999999999996 0.5 L 204 0.5  A 14.5 14.5 0 0 1 218.5 15 L 218.5 249  A 14.5 14.5 0 0 1 204 263.5 L 15 263.5  A 14.5 14.5 0 0 1 0.5 249 L 0.5 15  Z " stroke-width="1" stroke="#797979" fill="none" />
    </g>
  </defs>
  <g>
    <use xlink:href="#widget9" filter="url(#filter8)" />
    <use xlink:href="#widget9" />
  </g>
</svg>