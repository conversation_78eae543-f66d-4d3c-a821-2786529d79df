﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="592px" height="52px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="305px" y="705px" width="592px" height="52px" filterUnits="userSpaceOnUse" id="filter20">
      <feOffset dx="5" dy="5" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.34901960784313724 0  " in="shadowComposite" />
    </filter>
    <g id="widget21">
      <path d="M 305 705  L 887 705  L 887 747  L 305 747  L 305 705  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -305 -705 )">
    <use xlink:href="#widget21" filter="url(#filter20)" />
    <use xlink:href="#widget21" />
  </g>
</svg>