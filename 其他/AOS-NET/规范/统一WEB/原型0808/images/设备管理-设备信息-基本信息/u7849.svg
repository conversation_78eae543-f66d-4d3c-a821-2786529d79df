﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="114px" height="52px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="335px" y="299px" width="114px" height="52px" filterUnits="userSpaceOnUse" id="filter24">
      <feOffset dx="5" dy="5" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.34901960784313724 0  " in="shadowComposite" />
    </filter>
    <g id="widget25">
      <path d="M 336 300  L 438 300  L 438 340  L 336 340  L 336 300  Z " fill-rule="nonzero" fill="#b4b4b4" stroke="none" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -335 -299 )">
    <use xlink:href="#widget25" filter="url(#filter24)" />
    <use xlink:href="#widget25" />
  </g>
</svg>