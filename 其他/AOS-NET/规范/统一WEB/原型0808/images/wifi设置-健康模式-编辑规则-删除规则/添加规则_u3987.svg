﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="590px" height="401px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="462px" y="1052px" width="590px" height="401px" filterUnits="userSpaceOnUse" id="filter6">
      <feOffset dx="5" dy="5" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.34901960784313724 0  " in="shadowComposite" />
    </filter>
    <g id="widget7">
      <path d="M 463 1072  A 19 19 0 0 1 482 1053 L 1022 1053  A 19 19 0 0 1 1041 1072 L 1041 1423  A 19 19 0 0 1 1022 1442 L 482 1442  A 19 19 0 0 1 463 1423 L 463 1072  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" />
      <path d="M 462.5 1072  A 19.5 19.5 0 0 1 482 1052.5 L 1022 1052.5  A 19.5 19.5 0 0 1 1041.5 1072 L 1041.5 1423  A 19.5 19.5 0 0 1 1022 1442.5 L 482 1442.5  A 19.5 19.5 0 0 1 462.5 1423 L 462.5 1072  Z " stroke-width="1" stroke="#797979" fill="none" stroke-opacity="0.9921568627450981" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -462 -1052 )">
    <use xlink:href="#widget7" filter="url(#filter6)" />
    <use xlink:href="#widget7" />
  </g>
</svg>