﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="590px" height="401px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="609px" y="324px" width="590px" height="401px" filterUnits="userSpaceOnUse" id="filter3">
      <feOffset dx="5" dy="5" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.34901960784313724 0  " in="shadowComposite" />
    </filter>
    <g id="widget4">
      <path d="M 610 344  A 19 19 0 0 1 629 325 L 1169 325  A 19 19 0 0 1 1188 344 L 1188 695  A 19 19 0 0 1 1169 714 L 629 714  A 19 19 0 0 1 610 695 L 610 344  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -609 -324 )">
    <use xlink:href="#widget4" filter="url(#filter3)" />
    <use xlink:href="#widget4" />
  </g>
</svg>