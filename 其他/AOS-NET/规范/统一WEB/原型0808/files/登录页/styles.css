﻿body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:2000px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1600px;
  height:900px;
  background:inherit;
  background-color:rgba(194, 194, 194, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:0px;
  width:1600px;
  height:900px;
  display:flex;
}
#u3 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:306px;
  height:56px;
}
#u4 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:35px;
  width:306px;
  height:56px;
  display:flex;
}
#u4 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:486px;
  height:585px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:40px;
}
#u5 {
  border-width:0px;
  position:absolute;
  left:553px;
  top:85px;
  width:486px;
  height:585px;
  display:flex;
  font-size:40px;
}
#u5 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:350px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
}
#u6 {
  border-width:0px;
  position:absolute;
  left:625px;
  top:109px;
  width:350px;
  height:26px;
  display:flex;
  font-size:28px;
}
#u6 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u8_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:204px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8 {
  border-width:0px;
  position:absolute;
  left:709px;
  top:186px;
  width:182px;
  height:204px;
  display:flex;
}
#u8 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u9_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:213px;
}
#u9 {
  border-width:0px;
  position:absolute;
  left:698px;
  top:177px;
  width:205px;
  height:213px;
  display:flex;
}
#u9 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u9_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u10_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:277px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u10 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:150px;
  width:277px;
  height:18px;
  display:flex;
  font-size:18px;
}
#u10 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u10_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u11_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:17px;
}
#u11 {
  border-width:0px;
  position:absolute;
  left:689px;
  top:521px;
  width:141px;
  height:18px;
  display:flex;
  font-size:17px;
}
#u11 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u11_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:17px;
  color:#797979;
}
#u12 {
  border-width:0px;
  position:absolute;
  left:796px;
  top:521px;
  width:145px;
  height:18px;
  display:flex;
  font-size:17px;
  color:#797979;
}
#u12 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13_input {
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:41px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:41px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:41px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:41px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13 {
  border-width:0px;
  position:absolute;
  left:669px;
  top:408px;
  width:252px;
  height:41px;
  display:flex;
}
#u13 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13.hint {
}
#u13_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:41px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13.disabled {
}
#u13_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:41px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13.hint.disabled {
}
#u14_input {
  position:absolute;
  left:0px;
  top:0px;
  width:213px;
  height:41px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:40px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u14_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:213px;
  height:41px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:40px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u14_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:213px;
  height:41px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:40px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u14_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:213px;
  height:41px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:40px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u14_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:213px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:40px;
}
#u14 {
  border-width:0px;
  position:absolute;
  left:669px;
  top:463px;
  width:213px;
  height:41px;
  display:flex;
  font-size:40px;
}
#u14 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u14_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:213px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:40px;
}
#u14.hint {
}
#u14_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:213px;
  height:41px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:40px;
}
#u14.disabled {
}
#u14_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:213px;
  height:41px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:40px;
}
#u14.hint.disabled {
}
#u15_input {
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:50px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:30px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u15_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:50px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:30px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u15_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:50px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:30px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u15_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:50px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:30px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u15_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:50px;
  background:inherit;
  background-color:rgba(127, 127, 127, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:30px;
  text-align:center;
}
#u15 {
  border-width:0px;
  position:absolute;
  left:665px;
  top:555px;
  width:260px;
  height:50px;
  display:flex;
  font-size:30px;
  text-align:center;
}
#u15 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u15_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:50px;
  background:inherit;
  background-color:rgba(127, 127, 127, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:30px;
  text-align:center;
}
#u15.hint {
}
#u15_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:50px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:30px;
  text-align:center;
}
#u15.disabled {
}
#u15_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:50px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:30px;
  text-align:center;
}
#u15.hint.disabled {
}
#u16 label {
  left:0px;
  width:100%;
}
#u16_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u16 {
  border-width:0px;
  position:absolute;
  left:665px;
  top:521px;
  width:21px;
  height:20px;
  display:flex;
  font-size:50px;
}
#u16 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
}
#u16_img.selected {
}
#u16.selected {
}
#u16_img.disabled {
}
#u16.disabled {
}
#u16_img.selected.error {
}
#u16.selected.error {
}
#u16_img.selected.hint {
}
#u16.selected.hint {
}
#u16_img.selected.error.hint {
}
#u16.selected.error.hint {
}
#u16_img.mouseOver.selected {
}
#u16.mouseOver.selected {
}
#u16_img.mouseOver.selected.error {
}
#u16.mouseOver.selected.error {
}
#u16_img.mouseOver.selected.hint {
}
#u16.mouseOver.selected.hint {
}
#u16_img.mouseOver.selected.error.hint {
}
#u16.mouseOver.selected.error.hint {
}
#u16_img.mouseDown.selected {
}
#u16.mouseDown.selected {
}
#u16_img.mouseDown.selected.error {
}
#u16.mouseDown.selected.error {
}
#u16_img.mouseDown.selected.hint {
}
#u16.mouseDown.selected.hint {
}
#u16_img.mouseDown.selected.error.hint {
}
#u16.mouseDown.selected.error.hint {
}
#u16_img.mouseOver.mouseDown.selected {
}
#u16.mouseOver.mouseDown.selected {
}
#u16_img.mouseOver.mouseDown.selected.error {
}
#u16.mouseOver.mouseDown.selected.error {
}
#u16_img.mouseOver.mouseDown.selected.hint {
}
#u16.mouseOver.mouseDown.selected.hint {
}
#u16_img.mouseOver.mouseDown.selected.error.hint {
}
#u16.mouseOver.mouseDown.selected.error.hint {
}
#u16_img.focused.selected {
}
#u16.focused.selected {
}
#u16_img.focused.selected.error {
}
#u16.focused.selected.error {
}
#u16_img.focused.selected.hint {
}
#u16.focused.selected.hint {
}
#u16_img.focused.selected.error.hint {
}
#u16.focused.selected.error.hint {
}
#u16_img.selected.disabled {
}
#u16.selected.disabled {
}
#u16_img.selected.hint.disabled {
}
#u16.selected.hint.disabled {
}
#u16_img.selected.error.disabled {
}
#u16.selected.error.disabled {
}
#u16_img.selected.error.hint.disabled {
}
#u16.selected.error.hint.disabled {
}
#u16_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:2px;
  width:-3px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u16_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u17_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:91px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:22px;
}
#u17 {
  border-width:0px;
  position:absolute;
  left:617px;
  top:471px;
  width:91px;
  height:23px;
  display:flex;
  font-size:22px;
}
#u17 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u17_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u18_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:22px;
}
#u18 {
  border-width:0px;
  position:absolute;
  left:596px;
  top:417px;
  width:89px;
  height:23px;
  display:flex;
  font-size:22px;
}
#u18 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u18_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u19_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u19 {
  border-width:0px;
  position:absolute;
  left:753px;
  top:620px;
  width:86px;
  height:16px;
  display:flex;
  font-size:18px;
}
#u19 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u19_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u20_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  color:#AAAAAA;
}
#u20 {
  border-width:0px;
  position:absolute;
  left:674px;
  top:417px;
  width:149px;
  height:26px;
  display:flex;
  font-size:20px;
  color:#AAAAAA;
}
#u20 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u20_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u21_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:24px;
}
#u21 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:473px;
  width:29px;
  height:24px;
  display:flex;
}
#u21 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u21_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u22 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u23_input {
  position:absolute;
  left:0px;
  top:0px;
  width:256px;
  height:41px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:40px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u23_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:256px;
  height:41px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:40px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u23_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:256px;
  height:41px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:40px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u23_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:256px;
  height:41px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:40px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u23_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:256px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:40px;
}
#u23 {
  border-width:0px;
  position:absolute;
  left:669px;
  top:463px;
  width:256px;
  height:41px;
  display:flex;
  font-size:40px;
}
#u23 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u23_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:256px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:40px;
}
#u23.hint {
}
#u23_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:256px;
  height:41px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:40px;
}
#u23.disabled {
}
#u23_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:256px;
  height:41px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:40px;
}
#u23.hint.disabled {
}
#u24_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:26px;
}
#u24 {
  border-width:0px;
  position:absolute;
  left:882px;
  top:471px;
  width:34px;
  height:26px;
  display:flex;
}
#u24 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u24_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u25_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  color:#AAAAAA;
}
#u25 {
  border-width:0px;
  position:absolute;
  left:674px;
  top:470px;
  width:142px;
  height:26px;
  display:flex;
  font-size:20px;
  color:#AAAAAA;
}
#u25 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u25_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u26 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u27_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u27 {
  border-width:0px;
  position:absolute;
  left:553px;
  top:834px;
  width:86px;
  height:16px;
  display:flex;
  font-size:18px;
}
#u27 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u27_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u28_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:2px;
}
#u28 {
  border-width:0px;
  position:absolute;
  left:628px;
  top:842px;
  width:21px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90.18024149494667deg);
  -moz-transform:rotate(90.18024149494667deg);
  -ms-transform:rotate(90.18024149494667deg);
  transform:rotate(90.18024149494667deg);
}
#u28 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u28_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u29_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u29 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:835px;
  width:108px;
  height:20px;
  display:flex;
  font-size:18px;
}
#u29 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u29_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u30_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:2px;
}
#u30 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:844px;
  width:21px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90.18024149494667deg);
  -moz-transform:rotate(90.18024149494667deg);
  -ms-transform:rotate(90.18024149494667deg);
  transform:rotate(90.18024149494667deg);
}
#u30 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u30_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u31 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:835px;
  width:72px;
  height:19px;
  display:flex;
  font-size:18px;
}
#u31 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u31_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u32_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:2px;
}
#u32 {
  border-width:0px;
  position:absolute;
  left:870px;
  top:845px;
  width:21px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90.18024149494667deg);
  -moz-transform:rotate(90.18024149494667deg);
  -ms-transform:rotate(90.18024149494667deg);
  transform:rotate(90.18024149494667deg);
}
#u32 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u32_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u33_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u33 {
  border-width:0px;
  position:absolute;
  left:901px;
  top:834px;
  width:141px;
  height:19px;
  display:flex;
  font-size:18px;
}
#u33 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u33_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u34 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u35_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:550px;
  height:401px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u35 {
  border-width:0px;
  position:absolute;
  left:519px;
  top:177px;
  width:550px;
  height:401px;
  display:flex;
}
#u35 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u35_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u36_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:30px;
}
#u36 {
  border-width:0px;
  position:absolute;
  left:731px;
  top:198px;
  width:120px;
  height:34px;
  display:flex;
  font-size:30px;
}
#u36 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u36_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u37_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:25px;
}
#u37 {
  border-width:0px;
  position:absolute;
  left:551px;
  top:280px;
  width:116px;
  height:29px;
  display:flex;
  font-size:25px;
}
#u37 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u37_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u38_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:176px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:22px;
  color:#7F7F7F;
}
#u38 {
  border-width:0px;
  position:absolute;
  left:565px;
  top:311px;
  width:176px;
  height:26px;
  display:flex;
  font-size:22px;
  color:#7F7F7F;
}
#u38 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u38_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u39_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:25px;
}
#u39 {
  border-width:0px;
  position:absolute;
  left:552px;
  top:388px;
  width:116px;
  height:29px;
  display:flex;
  font-size:25px;
}
#u39 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u39_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u40_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:475px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:22px;
  color:#7F7F7F;
}
#u40 {
  border-width:0px;
  position:absolute;
  left:566px;
  top:422px;
  width:475px;
  height:26px;
  display:flex;
  font-size:22px;
  color:#7F7F7F;
}
#u40 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u40_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u41_input {
  position:absolute;
  left:0px;
  top:0px;
  width:353px;
  height:57px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:30px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u41_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:353px;
  height:57px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:30px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u41_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:353px;
  height:57px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:30px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u41_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:353px;
  height:57px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:30px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u41_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:353px;
  height:57px;
  background:inherit;
  background-color:rgba(170, 170, 170, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:30px;
  color:#FFFFFF;
  text-align:center;
}
#u41 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:482px;
  width:353px;
  height:57px;
  display:flex;
  font-size:30px;
  color:#FFFFFF;
  text-align:center;
}
#u41 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u41_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:353px;
  height:57px;
  background:inherit;
  background-color:rgba(170, 170, 170, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:30px;
  color:#FFFFFF;
  text-align:center;
}
#u41.hint {
}
#u41_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:353px;
  height:57px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:30px;
  color:#FFFFFF;
  text-align:center;
}
#u41.disabled {
}
#u41_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:353px;
  height:57px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:30px;
  color:#FFFFFF;
  text-align:center;
}
#u41.hint.disabled {
}
#u42_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:516px;
  background:inherit;
  background-color:rgba(251, 225, 89, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u42 {
  border-width:0px;
  position:absolute;
  left:1618px;
  top:177px;
  width:328px;
  height:516px;
  display:flex;
}
#u42 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u42_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
