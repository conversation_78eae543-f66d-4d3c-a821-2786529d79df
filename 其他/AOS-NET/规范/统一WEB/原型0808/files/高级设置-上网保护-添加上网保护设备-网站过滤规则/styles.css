﻿body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1600px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u31600 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31601_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1600px;
  height:900px;
  background:inherit;
  background-color:rgba(170, 170, 170, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31601 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:0px;
  width:1600px;
  height:900px;
  display:flex;
}
#u31601 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31601_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31602_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:306px;
  height:56px;
}
#u31602 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:35px;
  width:306px;
  height:56px;
  display:flex;
}
#u31602 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31602_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31603 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31604_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u31604 {
  border-width:0px;
  position:absolute;
  left:553px;
  top:834px;
  width:86px;
  height:16px;
  display:flex;
  font-size:18px;
}
#u31604 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u31604_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31605_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:2px;
}
#u31605 {
  border-width:0px;
  position:absolute;
  left:628px;
  top:842px;
  width:21px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90.18024149494667deg);
  -moz-transform:rotate(90.18024149494667deg);
  -ms-transform:rotate(90.18024149494667deg);
  transform:rotate(90.18024149494667deg);
}
#u31605 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31605_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31606_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u31606 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:835px;
  width:108px;
  height:20px;
  display:flex;
  font-size:18px;
}
#u31606 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u31606_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u31607_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:2px;
}
#u31607 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:844px;
  width:21px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90.18024149494667deg);
  -moz-transform:rotate(90.18024149494667deg);
  -ms-transform:rotate(90.18024149494667deg);
  transform:rotate(90.18024149494667deg);
}
#u31607 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31607_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31608_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u31608 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:835px;
  width:72px;
  height:19px;
  display:flex;
  font-size:18px;
}
#u31608 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u31608_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u31609_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:2px;
}
#u31609 {
  border-width:0px;
  position:absolute;
  left:870px;
  top:845px;
  width:21px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90.18024149494667deg);
  -moz-transform:rotate(90.18024149494667deg);
  -ms-transform:rotate(90.18024149494667deg);
  transform:rotate(90.18024149494667deg);
}
#u31609 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31609_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31610_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u31610 {
  border-width:0px;
  position:absolute;
  left:901px;
  top:834px;
  width:141px;
  height:19px;
  display:flex;
  font-size:18px;
}
#u31610 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u31610_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u31611_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:115px;
  height:43px;
}
#u31611 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:35px;
  width:115px;
  height:43px;
  display:flex;
}
#u31611 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31611_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31612 {
  position:absolute;
  left:116px;
  top:110px;
}
#u31612_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:1364px;
  height:55px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31612_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31613_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31613_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31613_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31613_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31613_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31613 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31613 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31613_img.hint {
}
#u31613.hint {
}
#u31613_img.disabled {
}
#u31613.disabled {
}
#u31613_img.hint.disabled {
}
#u31613.hint.disabled {
}
#u31614_input {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31614_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31614_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31614_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31614_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
}
#u31614 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:0px;
  width:236px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31614 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31614_img.hint {
}
#u31614.hint {
}
#u31614_img.disabled {
}
#u31614.disabled {
}
#u31614_img.hint.disabled {
}
#u31614.hint.disabled {
}
#u31615_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31615_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31615_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31615_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31615_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31615 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31615 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31615_img.hint {
}
#u31615.hint {
}
#u31615_img.disabled {
}
#u31615.disabled {
}
#u31615_img.hint.disabled {
}
#u31615.hint.disabled {
}
#u31616_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31616_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31616_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31616_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31616_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31616 {
  border-width:0px;
  position:absolute;
  left:1130px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  color:#FFFFFF;
  text-align:center;
}
#u31616 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31616_img.hint {
}
#u31616.hint {
}
#u31616_img.disabled {
}
#u31616.disabled {
}
#u31616_img.hint.disabled {
}
#u31616.hint.disabled {
}
#u31617_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31617_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31617_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31617_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31617_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31617 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31617 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31617_img.hint {
}
#u31617.hint {
}
#u31617_img.disabled {
}
#u31617.disabled {
}
#u31617_img.hint.disabled {
}
#u31617.hint.disabled {
}
#u31618_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31618_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31618_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31618_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31618_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31618 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31618 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31618_img.hint {
}
#u31618.hint {
}
#u31618_img.disabled {
}
#u31618.disabled {
}
#u31618_img.hint.disabled {
}
#u31618.hint.disabled {
}
#u31619_input {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31619_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31619_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31619_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31619_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
}
#u31619 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:0px;
  width:236px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31619 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31619_img.hint {
}
#u31619.hint {
}
#u31619_img.disabled {
}
#u31619.disabled {
}
#u31619_img.hint.disabled {
}
#u31619.hint.disabled {
}
#u31620_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31620_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31620_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31620_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31620_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31620 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31620 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31620_img.hint {
}
#u31620.hint {
}
#u31620_img.disabled {
}
#u31620.disabled {
}
#u31620_img.hint.disabled {
}
#u31620.hint.disabled {
}
#u31621_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31621_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31621_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31621_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31621_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31621 {
  border-width:0px;
  position:absolute;
  left:1130px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31621 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31621_img.hint {
}
#u31621.hint {
}
#u31621_img.disabled {
}
#u31621.disabled {
}
#u31621_img.hint.disabled {
}
#u31621.hint.disabled {
}
#u31622_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31622_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31622_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31622_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31622_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31622 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31622 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31622_img.hint {
}
#u31622.hint {
}
#u31622_img.disabled {
}
#u31622.disabled {
}
#u31622_img.hint.disabled {
}
#u31622.hint.disabled {
}
#u31612_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:1364px;
  height:55px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31612_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31623_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31623_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31623_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31623_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31623_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31623 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  color:#000000;
  text-align:center;
}
#u31623 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31623_img.hint {
}
#u31623.hint {
}
#u31623_img.disabled {
}
#u31623.disabled {
}
#u31623_img.hint.disabled {
}
#u31623.hint.disabled {
}
#u31624_input {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31624_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31624_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31624_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31624_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
}
#u31624 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:0px;
  width:236px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31624 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31624_img.hint {
}
#u31624.hint {
}
#u31624_img.disabled {
}
#u31624.disabled {
}
#u31624_img.hint.disabled {
}
#u31624.hint.disabled {
}
#u31625_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31625_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31625_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31625_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31625_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31625 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  color:#FFFFFF;
  text-align:center;
}
#u31625 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31625_img.hint {
}
#u31625.hint {
}
#u31625_img.disabled {
}
#u31625.disabled {
}
#u31625_img.hint.disabled {
}
#u31625.hint.disabled {
}
#u31626_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31626_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31626_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31626_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31626_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31626 {
  border-width:0px;
  position:absolute;
  left:1130px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31626 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31626_img.hint {
}
#u31626.hint {
}
#u31626_img.disabled {
}
#u31626.disabled {
}
#u31626_img.hint.disabled {
}
#u31626.hint.disabled {
}
#u31627_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31627_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31627_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31627_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31627_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31627 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31627 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31627_img.hint {
}
#u31627.hint {
}
#u31627_img.disabled {
}
#u31627.disabled {
}
#u31627_img.hint.disabled {
}
#u31627.hint.disabled {
}
#u31628_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31628_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31628_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31628_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31628_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31628 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31628 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31628_img.hint {
}
#u31628.hint {
}
#u31628_img.disabled {
}
#u31628.disabled {
}
#u31628_img.hint.disabled {
}
#u31628.hint.disabled {
}
#u31629_input {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31629_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31629_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31629_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31629_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
}
#u31629 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:0px;
  width:236px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31629 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31629_img.hint {
}
#u31629.hint {
}
#u31629_img.disabled {
}
#u31629.disabled {
}
#u31629_img.hint.disabled {
}
#u31629.hint.disabled {
}
#u31630_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31630_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31630_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31630_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31630_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31630 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  color:#FFFFFF;
  text-align:center;
}
#u31630 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31630_img.hint {
}
#u31630.hint {
}
#u31630_img.disabled {
}
#u31630.disabled {
}
#u31630_img.hint.disabled {
}
#u31630.hint.disabled {
}
#u31631_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31631_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31631_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31631_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31631_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31631 {
  border-width:0px;
  position:absolute;
  left:1130px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31631 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31631_img.hint {
}
#u31631.hint {
}
#u31631_img.disabled {
}
#u31631.disabled {
}
#u31631_img.hint.disabled {
}
#u31631.hint.disabled {
}
#u31632_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31632_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31632_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31632_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31632_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31632 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31632 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31632_img.hint {
}
#u31632.hint {
}
#u31632_img.disabled {
}
#u31632.disabled {
}
#u31632_img.hint.disabled {
}
#u31632.hint.disabled {
}
#u31612_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:1364px;
  height:55px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31612_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31633_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31633_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31633_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31633_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31633_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31633 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  color:#000000;
  text-align:center;
}
#u31633 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31633_img.hint {
}
#u31633.hint {
}
#u31633_img.disabled {
}
#u31633.disabled {
}
#u31633_img.hint.disabled {
}
#u31633.hint.disabled {
}
#u31634_input {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31634_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31634_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31634_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31634_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
}
#u31634 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:0px;
  width:236px;
  height:55px;
  display:flex;
  font-size:32px;
  color:#FFFFFF;
  text-align:center;
}
#u31634 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31634_img.hint {
}
#u31634.hint {
}
#u31634_img.disabled {
}
#u31634.disabled {
}
#u31634_img.hint.disabled {
}
#u31634.hint.disabled {
}
#u31635_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31635_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31635_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31635_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31635_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31635 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31635 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31635_img.hint {
}
#u31635.hint {
}
#u31635_img.disabled {
}
#u31635.disabled {
}
#u31635_img.hint.disabled {
}
#u31635.hint.disabled {
}
#u31636_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31636_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31636_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31636_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31636_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31636 {
  border-width:0px;
  position:absolute;
  left:1130px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31636 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31636_img.hint {
}
#u31636.hint {
}
#u31636_img.disabled {
}
#u31636.disabled {
}
#u31636_img.hint.disabled {
}
#u31636.hint.disabled {
}
#u31637_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31637_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31637_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31637_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31637_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31637 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31637 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31637_img.hint {
}
#u31637.hint {
}
#u31637_img.disabled {
}
#u31637.disabled {
}
#u31637_img.hint.disabled {
}
#u31637.hint.disabled {
}
#u31638_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31638_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31638_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31638_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31638_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31638 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31638 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31638_img.hint {
}
#u31638.hint {
}
#u31638_img.disabled {
}
#u31638.disabled {
}
#u31638_img.hint.disabled {
}
#u31638.hint.disabled {
}
#u31639_input {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31639_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31639_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31639_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31639_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
}
#u31639 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:0px;
  width:236px;
  height:55px;
  display:flex;
  font-size:32px;
  color:#FFFFFF;
  text-align:center;
}
#u31639 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31639_img.hint {
}
#u31639.hint {
}
#u31639_img.disabled {
}
#u31639.disabled {
}
#u31639_img.hint.disabled {
}
#u31639.hint.disabled {
}
#u31640_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31640_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31640_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31640_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31640_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31640 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31640 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31640_img.hint {
}
#u31640.hint {
}
#u31640_img.disabled {
}
#u31640.disabled {
}
#u31640_img.hint.disabled {
}
#u31640.hint.disabled {
}
#u31641_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31641_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31641_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31641_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31641_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31641 {
  border-width:0px;
  position:absolute;
  left:1130px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31641 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31641_img.hint {
}
#u31641.hint {
}
#u31641_img.disabled {
}
#u31641.disabled {
}
#u31641_img.hint.disabled {
}
#u31641.hint.disabled {
}
#u31642_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31642_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31642_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31642_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31642_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31642 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31642 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31642_img.hint {
}
#u31642.hint {
}
#u31642_img.disabled {
}
#u31642.disabled {
}
#u31642_img.hint.disabled {
}
#u31642.hint.disabled {
}
#u31612_state3 {
  position:relative;
  left:0px;
  top:0px;
  width:1364px;
  height:55px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31612_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31643_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31643_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31643_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31643_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31643_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31643 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  color:#FFFFFF;
  text-align:center;
}
#u31643 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31643_img.hint {
}
#u31643.hint {
}
#u31643_img.disabled {
}
#u31643.disabled {
}
#u31643_img.hint.disabled {
}
#u31643.hint.disabled {
}
#u31644_input {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31644_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31644_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31644_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31644_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
}
#u31644 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:0px;
  width:236px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31644 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31644_img.hint {
}
#u31644.hint {
}
#u31644_img.disabled {
}
#u31644.disabled {
}
#u31644_img.hint.disabled {
}
#u31644.hint.disabled {
}
#u31645_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31645_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31645_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31645_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31645_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31645 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31645 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31645_img.hint {
}
#u31645.hint {
}
#u31645_img.disabled {
}
#u31645.disabled {
}
#u31645_img.hint.disabled {
}
#u31645.hint.disabled {
}
#u31646_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31646_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31646_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31646_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31646_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31646 {
  border-width:0px;
  position:absolute;
  left:1130px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31646 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31646_img.hint {
}
#u31646.hint {
}
#u31646_img.disabled {
}
#u31646.disabled {
}
#u31646_img.hint.disabled {
}
#u31646.hint.disabled {
}
#u31647_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31647_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31647_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31647_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31647_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31647 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31647 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31647_img.hint {
}
#u31647.hint {
}
#u31647_img.disabled {
}
#u31647.disabled {
}
#u31647_img.hint.disabled {
}
#u31647.hint.disabled {
}
#u31612_state4 {
  position:relative;
  left:0px;
  top:0px;
  width:1364px;
  height:55px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31612_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31648_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31648_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31648_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31648_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31648_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31648 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31648 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31648_img.hint {
}
#u31648.hint {
}
#u31648_img.disabled {
}
#u31648.disabled {
}
#u31648_img.hint.disabled {
}
#u31648.hint.disabled {
}
#u31649_input {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31649_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31649_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31649_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31649_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
}
#u31649 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:0px;
  width:236px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31649 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31649_img.hint {
}
#u31649.hint {
}
#u31649_img.disabled {
}
#u31649.disabled {
}
#u31649_img.hint.disabled {
}
#u31649.hint.disabled {
}
#u31650_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31650_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31650_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31650_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31650_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31650 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31650 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31650_img.hint {
}
#u31650.hint {
}
#u31650_img.disabled {
}
#u31650.disabled {
}
#u31650_img.hint.disabled {
}
#u31650.hint.disabled {
}
#u31651_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31651_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31651_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31651_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31651_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31651 {
  border-width:0px;
  position:absolute;
  left:1130px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31651 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31651_img.hint {
}
#u31651.hint {
}
#u31651_img.disabled {
}
#u31651.disabled {
}
#u31651_img.hint.disabled {
}
#u31651.hint.disabled {
}
#u31652_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31652_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31652_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#FFFFFF;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31652_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31652_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31652 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  color:#FFFFFF;
  text-align:center;
}
#u31652 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31652_img.hint {
}
#u31652.hint {
}
#u31652_img.disabled {
}
#u31652.disabled {
}
#u31652_img.hint.disabled {
}
#u31652.hint.disabled {
}
#u31653_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31653_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31653_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31653_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31653_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31653 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31653 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31653_img.hint {
}
#u31653.hint {
}
#u31653_img.disabled {
}
#u31653.disabled {
}
#u31653_img.hint.disabled {
}
#u31653.hint.disabled {
}
#u31654_input {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31654_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31654_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31654_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31654_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:236px;
  height:55px;
}
#u31654 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:0px;
  width:236px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31654 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31654_img.hint {
}
#u31654.hint {
}
#u31654_img.disabled {
}
#u31654.disabled {
}
#u31654_img.hint.disabled {
}
#u31654.hint.disabled {
}
#u31655_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31655_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31655_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31655_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31655_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31655 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31655 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31655_img.hint {
}
#u31655.hint {
}
#u31655_img.disabled {
}
#u31655.disabled {
}
#u31655_img.hint.disabled {
}
#u31655.hint.disabled {
}
#u31656_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31656_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31656_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31656_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31656_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31656 {
  border-width:0px;
  position:absolute;
  left:1130px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31656 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31656_img.hint {
}
#u31656.hint {
}
#u31656_img.disabled {
}
#u31656.disabled {
}
#u31656_img.hint.disabled {
}
#u31656.hint.disabled {
}
#u31657_input {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31657_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31657_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31657_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:32px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31657_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:55px;
}
#u31657 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:0px;
  width:234px;
  height:55px;
  display:flex;
  font-size:32px;
  text-align:center;
}
#u31657 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31657_img.hint {
}
#u31657.hint {
}
#u31657_img.disabled {
}
#u31657.disabled {
}
#u31657_img.hint.disabled {
}
#u31657.hint.disabled {
}
#u31658 {
  position:absolute;
  left:116px;
  top:190px;
}
#u31658_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:251px;
  height:634px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31658_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31659 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31660_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:634px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31660 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:634px;
  display:flex;
}
#u31660 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31660_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31661_input {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31661_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31661_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31661_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31661_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
}
#u31661 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:29px;
  width:221px;
  height:38px;
  display:flex;
  font-size:25px;
}
#u31661 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31661_img.hint {
}
#u31661.hint {
}
#u31661_img.disabled {
}
#u31661.disabled {
}
#u31661_img.hint.disabled {
}
#u31661.hint.disabled {
}
#u31662_input {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31662_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31662_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31662_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31662_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
}
#u31662 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:197px;
  width:193px;
  height:38px;
  display:flex;
  font-size:25px;
}
#u31662 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31662_img.hint {
}
#u31662.hint {
}
#u31662_img.disabled {
}
#u31662.disabled {
}
#u31662_img.hint.disabled {
}
#u31662.hint.disabled {
}
#u31663_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31663 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:29px;
  width:38px;
  height:38px;
  display:flex;
}
#u31663 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31663_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31664_input {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31664_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31664_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31664_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31664_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
}
#u31664 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:141px;
  width:193px;
  height:38px;
  display:flex;
  font-size:25px;
}
#u31664 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31664_img.hint {
}
#u31664.hint {
}
#u31664_img.disabled {
}
#u31664.disabled {
}
#u31664_img.hint.disabled {
}
#u31664.hint.disabled {
}
#u31665_input {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31665_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31665_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31665_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31665_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
}
#u31665 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:85px;
  width:193px;
  height:38px;
  display:flex;
  font-size:25px;
}
#u31665 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31665_img.hint {
}
#u31665.hint {
}
#u31665_img.disabled {
}
#u31665.disabled {
}
#u31665_img.hint.disabled {
}
#u31665.hint.disabled {
}
#u31666_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31666 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:85px;
  width:38px;
  height:38px;
  display:flex;
}
#u31666 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31666_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31667_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31667 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:141px;
  width:38px;
  height:38px;
  display:flex;
}
#u31667 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31667_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31668_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31668 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:197px;
  width:38px;
  height:38px;
  display:flex;
}
#u31668 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31668_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31669_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31669_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31669_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31669_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31669_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31669 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:244px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31669 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31669_img.hint {
}
#u31669.hint {
}
#u31669_img.disabled {
}
#u31669.disabled {
}
#u31669_img.hint.disabled {
}
#u31669.hint.disabled {
}
#u31670_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31670 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:253px;
  width:38px;
  height:38px;
  display:flex;
}
#u31670 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31670_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31671_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31671_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31671_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31671_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31671_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31671 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:297px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31671 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31671_img.hint {
}
#u31671.hint {
}
#u31671_img.disabled {
}
#u31671.disabled {
}
#u31671_img.hint.disabled {
}
#u31671.hint.disabled {
}
#u31672_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31672 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:306px;
  width:38px;
  height:38px;
  display:flex;
}
#u31672 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31672_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31673_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31673_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31673_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31673_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31673_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31673 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:353px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31673 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31673_img.hint {
}
#u31673.hint {
}
#u31673_img.disabled {
}
#u31673.disabled {
}
#u31673_img.hint.disabled {
}
#u31673.hint.disabled {
}
#u31674_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31674 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:362px;
  width:38px;
  height:38px;
  display:flex;
}
#u31674 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31674_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31675_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31675_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31675_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31675_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31675_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31675 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:408px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31675 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31675_img.hint {
}
#u31675.hint {
}
#u31675_img.disabled {
}
#u31675.disabled {
}
#u31675_img.hint.disabled {
}
#u31675.hint.disabled {
}
#u31676_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31676 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:417px;
  width:38px;
  height:38px;
  display:flex;
}
#u31676 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31676_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31677_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31677_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31677_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31677_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31677_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31677 {
  border-width:0px;
  position:absolute;
  left:68px;
  top:465px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31677 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31677_img.hint {
}
#u31677.hint {
}
#u31677_img.disabled {
}
#u31677.disabled {
}
#u31677_img.hint.disabled {
}
#u31677.hint.disabled {
}
#u31678_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31678 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:473px;
  width:38px;
  height:38px;
  display:flex;
}
#u31678 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31678_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31658_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:251px;
  height:634px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31658_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31679 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31680_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:634px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31680 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:634px;
  display:flex;
}
#u31680 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31680_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31681_input {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31681_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31681_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31681_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31681_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
}
#u31681 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:29px;
  width:221px;
  height:38px;
  display:flex;
  font-size:25px;
}
#u31681 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31681_img.hint {
}
#u31681.hint {
}
#u31681_img.disabled {
}
#u31681.disabled {
}
#u31681_img.hint.disabled {
}
#u31681.hint.disabled {
}
#u31682_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31682 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:29px;
  width:38px;
  height:38px;
  display:flex;
}
#u31682 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31682_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31683_input {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31683_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31683_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31683_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31683_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
}
#u31683 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:141px;
  width:193px;
  height:38px;
  display:flex;
  font-size:25px;
}
#u31683 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31683_img.hint {
}
#u31683.hint {
}
#u31683_img.disabled {
}
#u31683.disabled {
}
#u31683_img.hint.disabled {
}
#u31683.hint.disabled {
}
#u31684_input {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31684_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31684_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31684_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31684_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
}
#u31684 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:85px;
  width:193px;
  height:38px;
  display:flex;
  font-size:25px;
}
#u31684 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31684_img.hint {
}
#u31684.hint {
}
#u31684_img.disabled {
}
#u31684.disabled {
}
#u31684_img.hint.disabled {
}
#u31684.hint.disabled {
}
#u31685_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31685 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:85px;
  width:38px;
  height:38px;
  display:flex;
}
#u31685 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31685_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31686_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31686 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:141px;
  width:38px;
  height:38px;
  display:flex;
}
#u31686 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31686_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31687_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31687_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31687_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31687_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31687_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31687 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:188px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31687 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31687_img.hint {
}
#u31687.hint {
}
#u31687_img.disabled {
}
#u31687.disabled {
}
#u31687_img.hint.disabled {
}
#u31687.hint.disabled {
}
#u31688_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31688 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:197px;
  width:38px;
  height:38px;
  display:flex;
}
#u31688 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31688_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31689_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31689_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31689_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31689_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31689_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31689 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:244px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31689 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31689_img.hint {
}
#u31689.hint {
}
#u31689_img.disabled {
}
#u31689.disabled {
}
#u31689_img.hint.disabled {
}
#u31689.hint.disabled {
}
#u31690_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31690 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:253px;
  width:38px;
  height:38px;
  display:flex;
}
#u31690 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31690_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31691_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31691_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31691_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31691_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31691_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31691 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:297px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31691 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31691_img.hint {
}
#u31691.hint {
}
#u31691_img.disabled {
}
#u31691.disabled {
}
#u31691_img.hint.disabled {
}
#u31691.hint.disabled {
}
#u31692_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31692 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:306px;
  width:38px;
  height:38px;
  display:flex;
}
#u31692 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31692_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31693_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31693_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31693_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31693_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31693_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31693 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:353px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31693 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31693_img.hint {
}
#u31693.hint {
}
#u31693_img.disabled {
}
#u31693.disabled {
}
#u31693_img.hint.disabled {
}
#u31693.hint.disabled {
}
#u31694_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31694 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:362px;
  width:38px;
  height:38px;
  display:flex;
}
#u31694 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31694_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31695_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31695_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31695_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31695_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31695_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31695 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:408px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31695 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31695_img.hint {
}
#u31695.hint {
}
#u31695_img.disabled {
}
#u31695.disabled {
}
#u31695_img.hint.disabled {
}
#u31695.hint.disabled {
}
#u31696_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31696 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:417px;
  width:38px;
  height:38px;
  display:flex;
}
#u31696 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31696_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31697_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31697_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31697_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31697_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31697_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31697 {
  border-width:0px;
  position:absolute;
  left:68px;
  top:465px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31697 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31697_img.hint {
}
#u31697.hint {
}
#u31697_img.disabled {
}
#u31697.disabled {
}
#u31697_img.hint.disabled {
}
#u31697.hint.disabled {
}
#u31698_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31698 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:473px;
  width:38px;
  height:38px;
  display:flex;
}
#u31698 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31698_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31658_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:251px;
  height:634px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31658_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31699 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31700_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:634px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31700 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:634px;
  display:flex;
}
#u31700 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31700_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31701_input {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31701_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31701_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31701_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31701_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
}
#u31701 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:29px;
  width:221px;
  height:38px;
  display:flex;
  font-size:25px;
}
#u31701 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31701_img.hint {
}
#u31701.hint {
}
#u31701_img.disabled {
}
#u31701.disabled {
}
#u31701_img.hint.disabled {
}
#u31701.hint.disabled {
}
#u31702_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31702 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:29px;
  width:38px;
  height:38px;
  display:flex;
}
#u31702 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31702_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31703_input {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31703_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31703_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31703_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31703_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:38px;
}
#u31703 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:85px;
  width:193px;
  height:38px;
  display:flex;
  font-size:25px;
}
#u31703 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31703_img.hint {
}
#u31703.hint {
}
#u31703_img.disabled {
}
#u31703.disabled {
}
#u31703_img.hint.disabled {
}
#u31703.hint.disabled {
}
#u31704_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31704 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:85px;
  width:38px;
  height:38px;
  display:flex;
}
#u31704 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31704_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31705_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31705_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31705_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31705_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31705_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31705 {
  border-width:0px;
  position:absolute;
  left:72px;
  top:132px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31705 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31705_img.hint {
}
#u31705.hint {
}
#u31705_img.disabled {
}
#u31705.disabled {
}
#u31705_img.hint.disabled {
}
#u31705.hint.disabled {
}
#u31706_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31706 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:141px;
  width:38px;
  height:38px;
  display:flex;
}
#u31706 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31706_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31707_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31707_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31707_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31707_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31707_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31707 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:188px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31707 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31707_img.hint {
}
#u31707.hint {
}
#u31707_img.disabled {
}
#u31707.disabled {
}
#u31707_img.hint.disabled {
}
#u31707.hint.disabled {
}
#u31708_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31708 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:197px;
  width:38px;
  height:38px;
  display:flex;
}
#u31708 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31708_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31709_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31709_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31709_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31709_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31709_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31709 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:244px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31709 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31709_img.hint {
}
#u31709.hint {
}
#u31709_img.disabled {
}
#u31709.disabled {
}
#u31709_img.hint.disabled {
}
#u31709.hint.disabled {
}
#u31710_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31710 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:253px;
  width:38px;
  height:38px;
  display:flex;
}
#u31710 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31710_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31711_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31711_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31711_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31711_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31711_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31711 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:297px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31711 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31711_img.hint {
}
#u31711.hint {
}
#u31711_img.disabled {
}
#u31711.disabled {
}
#u31711_img.hint.disabled {
}
#u31711.hint.disabled {
}
#u31712_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31712 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:306px;
  width:38px;
  height:38px;
  display:flex;
}
#u31712 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31712_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31713_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31713_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31713_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31713_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31713_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31713 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:353px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31713 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31713_img.hint {
}
#u31713.hint {
}
#u31713_img.disabled {
}
#u31713.disabled {
}
#u31713_img.hint.disabled {
}
#u31713.hint.disabled {
}
#u31714_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31714 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:362px;
  width:38px;
  height:38px;
  display:flex;
}
#u31714 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31714_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31715_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31715_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31715_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31715_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31715_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31715 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:408px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31715 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31715_img.hint {
}
#u31715.hint {
}
#u31715_img.disabled {
}
#u31715.disabled {
}
#u31715_img.hint.disabled {
}
#u31715.hint.disabled {
}
#u31716_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31716 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:417px;
  width:38px;
  height:38px;
  display:flex;
}
#u31716 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31716_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31717_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31717_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31717_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31717_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31717_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31717 {
  border-width:0px;
  position:absolute;
  left:68px;
  top:465px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31717 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31717_img.hint {
}
#u31717.hint {
}
#u31717_img.disabled {
}
#u31717.disabled {
}
#u31717_img.hint.disabled {
}
#u31717.hint.disabled {
}
#u31718_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31718 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:473px;
  width:38px;
  height:38px;
  display:flex;
}
#u31718 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31718_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31658_state3 {
  position:relative;
  left:0px;
  top:0px;
  width:251px;
  height:634px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31658_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31719 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31720_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:634px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31720 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:634px;
  display:flex;
}
#u31720 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31720_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31721_input {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31721_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31721_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31721_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31721_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:38px;
}
#u31721 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:29px;
  width:221px;
  height:38px;
  display:flex;
  font-size:25px;
}
#u31721 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31721_img.hint {
}
#u31721.hint {
}
#u31721_img.disabled {
}
#u31721.disabled {
}
#u31721_img.hint.disabled {
}
#u31721.hint.disabled {
}
#u31722_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31722 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:29px;
  width:38px;
  height:38px;
  display:flex;
}
#u31722 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31722_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31723_input {
  position:absolute;
  left:0px;
  top:0px;
  width:164px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31723_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:164px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31723_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:164px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31723_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:164px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31723_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:164px;
  height:56px;
}
#u31723 {
  border-width:0px;
  position:absolute;
  left:72px;
  top:76px;
  width:164px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31723 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31723_img.hint {
}
#u31723.hint {
}
#u31723_img.disabled {
}
#u31723.disabled {
}
#u31723_img.hint.disabled {
}
#u31723.hint.disabled {
}
#u31724_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31724 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:85px;
  width:38px;
  height:38px;
  display:flex;
}
#u31724 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31724_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31725_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31725_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31725_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31725_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31725_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31725 {
  border-width:0px;
  position:absolute;
  left:72px;
  top:132px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31725 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31725_img.hint {
}
#u31725.hint {
}
#u31725_img.disabled {
}
#u31725.disabled {
}
#u31725_img.hint.disabled {
}
#u31725.hint.disabled {
}
#u31726_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31726 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:141px;
  width:38px;
  height:38px;
  display:flex;
}
#u31726 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31726_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31727_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31727_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31727_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31727_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31727_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31727 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:188px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31727 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31727_img.hint {
}
#u31727.hint {
}
#u31727_img.disabled {
}
#u31727.disabled {
}
#u31727_img.hint.disabled {
}
#u31727.hint.disabled {
}
#u31728_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31728 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:197px;
  width:38px;
  height:38px;
  display:flex;
}
#u31728 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31728_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31729_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31729_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31729_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31729_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31729_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31729 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:244px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31729 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31729_img.hint {
}
#u31729.hint {
}
#u31729_img.disabled {
}
#u31729.disabled {
}
#u31729_img.hint.disabled {
}
#u31729.hint.disabled {
}
#u31730_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31730 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:253px;
  width:38px;
  height:38px;
  display:flex;
}
#u31730 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31730_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31731_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31731_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31731_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31731_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31731_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31731 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:297px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31731 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31731_img.hint {
}
#u31731.hint {
}
#u31731_img.disabled {
}
#u31731.disabled {
}
#u31731_img.hint.disabled {
}
#u31731.hint.disabled {
}
#u31732_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31732 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:306px;
  width:38px;
  height:38px;
  display:flex;
}
#u31732 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31732_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31733_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31733_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31733_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31733_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31733_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31733 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:353px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31733 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31733_img.hint {
}
#u31733.hint {
}
#u31733_img.disabled {
}
#u31733.disabled {
}
#u31733_img.hint.disabled {
}
#u31733.hint.disabled {
}
#u31734_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31734 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:362px;
  width:38px;
  height:38px;
  display:flex;
}
#u31734 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31734_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31735_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31735_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31735_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31735_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31735_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31735 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:408px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31735 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31735_img.hint {
}
#u31735.hint {
}
#u31735_img.disabled {
}
#u31735.disabled {
}
#u31735_img.hint.disabled {
}
#u31735.hint.disabled {
}
#u31736_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31736 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:417px;
  width:38px;
  height:38px;
  display:flex;
}
#u31736 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31736_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31737_input {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31737_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31737_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31737_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31737_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:56px;
}
#u31737 {
  border-width:0px;
  position:absolute;
  left:68px;
  top:465px;
  width:160px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31737 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31737_img.hint {
}
#u31737.hint {
}
#u31737_img.disabled {
}
#u31737.disabled {
}
#u31737_img.hint.disabled {
}
#u31737.hint.disabled {
}
#u31738_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:38px;
}
#u31738 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:473px;
  width:38px;
  height:38px;
  display:flex;
}
#u31738 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31738_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31739 {
  position:absolute;
  left:376px;
  top:190px;
}
#u31739_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  background-color:rgba(240, 176, 3, 1);
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31739_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31740 {
  position:absolute;
  left:0px;
  top:0px;
}
#u31740_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31740_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31741 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31742_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31742 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  display:flex;
}
#u31742 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31742_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31743_input {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31743_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31743_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31743_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31743_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
}
#u31743 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:10px;
  width:186px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31743 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31743_img.hint {
}
#u31743.hint {
}
#u31743_img.disabled {
}
#u31743.disabled {
}
#u31743_img.hint.disabled {
}
#u31743.hint.disabled {
}
#u31744_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:980px;
  height:2px;
}
#u31744 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:71px;
  width:979px;
  height:1px;
  display:flex;
}
#u31744 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31744_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31745_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u31745 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:28px;
  width:24px;
  height:24px;
  display:flex;
}
#u31745 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31745_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31746_input {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#545353;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31746_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31746_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#545353;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31746_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31746_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u31746 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:87px;
  width:98px;
  height:40px;
  display:flex;
  font-size:19px;
  color:#545353;
}
#u31746 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31746_img.hint {
}
#u31746.hint {
}
#u31746_img.disabled {
}
#u31746.disabled {
}
#u31746_img.hint.disabled {
}
#u31746.hint.disabled {
}
#u31747_input {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31747_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31747_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31747_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31747_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
}
#u31747 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:10px;
  width:186px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31747 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31747_img.hint {
}
#u31747.hint {
}
#u31747_img.disabled {
}
#u31747.disabled {
}
#u31747_img.hint.disabled {
}
#u31747.hint.disabled {
}
#u31748_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:2px;
}
#u31748 {
  border-width:0px;
  position:absolute;
  left:860px;
  top:31px;
  width:18px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(136.59469514123444deg);
  -moz-transform:rotate(136.59469514123444deg);
  -ms-transform:rotate(136.59469514123444deg);
  transform:rotate(136.59469514123444deg);
}
#u31748 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31748_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31749_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:2px;
}
#u31749 {
  border-width:0px;
  position:absolute;
  left:859px;
  top:44px;
  width:20px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-136.0251807247957deg);
  -moz-transform:rotate(-136.0251807247957deg);
  -ms-transform:rotate(-136.0251807247957deg);
  transform:rotate(-136.0251807247957deg);
}
#u31749 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31749_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31750_input {
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31750_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31750_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31750_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31750_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
}
#u31750 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:123px;
  width:548px;
  height:40px;
  display:flex;
  font-size:17px;
  color:#908F8F;
}
#u31750 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31750_img.hint {
}
#u31750.hint {
}
#u31750_img.disabled {
}
#u31750.disabled {
}
#u31750_img.hint.disabled {
}
#u31750.hint.disabled {
}
#u31751_input {
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31751_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31751_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31751_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31751_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
}
#u31751 {
  border-width:0px;
  position:absolute;
  left:465px;
  top:186px;
  width:548px;
  height:40px;
  display:flex;
  font-size:17px;
  color:#908F8F;
}
#u31751 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31751_img.hint {
}
#u31751.hint {
}
#u31751_img.disabled {
}
#u31751.disabled {
}
#u31751_img.hint.disabled {
}
#u31751.hint.disabled {
}
#u31752_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:184px;
  height:54px;
  background:inherit;
  background-color:rgba(215, 158, 2, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(44, 44, 44, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:17px;
  text-align:left;
}
#u31752 {
  border-width:0px;
  position:absolute;
  left:781px;
  top:176px;
  width:184px;
  height:54px;
  display:flex;
  font-size:17px;
  text-align:left;
}
#u31752 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31752_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31753_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-10px;
  width:144px;
  height:22px;
}
#u31753p000 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-1px;
  width:138px;
  height:4px;
  -webkit-transform:rotate(0.19508699399341367deg);
  -moz-transform:rotate(0.19508699399341367deg);
  -ms-transform:rotate(0.19508699399341367deg);
  transform:rotate(0.19508699399341367deg);
}
#u31753p000_img {
  border-width:0px;
  position:absolute;
  left:-0px;
  top:0px;
  width:138px;
  height:4px;
}
#u31753p001 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:4px;
  height:6px;
  -webkit-transform:rotate(179.1950869939934deg);
  -moz-transform:rotate(179.1950869939934deg);
  -ms-transform:rotate(179.1950869939934deg);
  transform:rotate(179.1950869939934deg);
}
#u31753p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:6px;
}
#u31753p002 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:-12px;
  width:26px;
  height:24px;
  -webkit-transform:rotate(180.1950869939934deg);
  -moz-transform:rotate(180.1950869939934deg);
  -ms-transform:rotate(180.1950869939934deg);
  transform:rotate(180.1950869939934deg);
}
#u31753p002_img {
  border-width:0px;
  position:absolute;
  left:-0px;
  top:-0px;
  width:26px;
  height:24px;
}
#u31753 {
  border-width:0px;
  position:absolute;
  left:647px;
  top:207px;
  width:133px;
  height:2px;
  display:flex;
  -webkit-transform:rotate(-179.1950869939934deg);
  -moz-transform:rotate(-179.1950869939934deg);
  -ms-transform:rotate(-179.1950869939934deg);
  transform:rotate(-179.1950869939934deg);
}
#u31753 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31753_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31754_input {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#545353;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31754_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31754_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#545353;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31754_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31754_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u31754 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:260px;
  width:98px;
  height:40px;
  display:flex;
  font-size:19px;
  color:#545353;
}
#u31754 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31754_img.hint {
}
#u31754.hint {
}
#u31754_img.disabled {
}
#u31754.disabled {
}
#u31754_img.hint.disabled {
}
#u31754.hint.disabled {
}
#u31755_input {
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31755_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31755_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31755_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31755_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:548px;
  height:40px;
}
#u31755 {
  border-width:0px;
  position:absolute;
  left:137px;
  top:292px;
  width:548px;
  height:40px;
  display:flex;
  font-size:17px;
  color:#908F8F;
}
#u31755 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31755_img.hint {
}
#u31755.hint {
}
#u31755_img.disabled {
}
#u31755.disabled {
}
#u31755_img.hint.disabled {
}
#u31755.hint.disabled {
}
#u31756 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31757_input {
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:33px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31757_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:33px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31757_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:33px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31757_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:33px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31757_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:33px;
}
#u31757 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:499px;
  width:76px;
  height:33px;
  display:flex;
  font-size:16px;
}
#u31757 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31757_img.hint {
}
#u31757.hint {
}
#u31757_img.disabled {
}
#u31757.disabled {
}
#u31757_img.hint.disabled {
}
#u31757.hint.disabled {
}
#u31758 {
  position:absolute;
  left:131px;
  top:500px;
}
#u31758_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31758_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31758_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31758_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31759_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31759 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31759 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31759_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31758_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31758_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31760_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31760 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31760 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31760_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31758_state3 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31758_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31761_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31761 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31761 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31761_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31758_state4 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31758_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31762_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31762 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31762 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31762_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31758_state5 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31758_state5_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31763_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31763 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31763 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31763_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31758_state6 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31758_state6_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31764_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31764 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31764 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31764_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31758_state7 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31758_state7_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31765_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31765 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31765 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31765_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31758_state8 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31758_state8_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31766_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31766 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31766 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31766_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31758_state9 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31758_state9_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31767_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31767 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31767 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31767_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31758_state10 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31758_state10_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31768_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31768 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31768 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31768_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31758_state11 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31758_state11_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31769_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31769 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31769 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31769_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31758_state12 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31758_state12_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31770_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31770 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31770 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31770_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31758_state13 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31758_state13_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31771_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31771 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31771 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31771_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772 {
  border-width:0px;
  position:absolute;
  left:131px;
  top:504px;
  width:27px;
  height:25px;
}
#u31772_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31772_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31773_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31773 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31773 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31773_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31772_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31774_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31774 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31774 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31774_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772_state2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31772_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31775_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31775 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31775 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31775_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772_state3 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31772_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31776_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31776 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31776 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31776_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772_state4 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31772_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31777_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31777 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31777 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31777_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772_state5 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31772_state5_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31778_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31778 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31778 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31778_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772_state6 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31772_state6_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31779_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31779 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31779 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31779_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772_state7 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31772_state7_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31780_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31780 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31780 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31780_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772_state8 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31772_state8_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31781_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31781 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31781 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31781_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772_state9 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31772_state9_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31782_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31782 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31782 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31782_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772_state10 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31772_state10_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31783_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31783 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31783 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31783_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772_state11 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31772_state11_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31784_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31784 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31784 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31784_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772_state12 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31772_state12_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31785_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31785 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31785 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31785_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31772_state13 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31772_state13_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31786_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31786 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31786 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31786_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787 {
  position:absolute;
  left:171px;
  top:504px;
}
#u31787_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31787_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31788_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31788 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31788 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31788_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31787_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31789_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31789 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31789 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31789_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31787_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31790_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31790 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31790 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31790_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787_state3 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31787_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31791_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31791 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31791 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31791_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787_state4 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31787_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31792_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31792 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31792 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31792_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787_state5 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31787_state5_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31793_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31793 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31793 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31793_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787_state6 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31787_state6_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31794_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31794 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31794 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31794_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787_state7 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31787_state7_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31795_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31795 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31795 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31795_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787_state8 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31787_state8_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31796_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31796 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31796 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31796_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787_state9 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31787_state9_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31797_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31797 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31797 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31797_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787_state10 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31787_state10_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31798_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31798 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31798 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31798_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787_state11 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31787_state11_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31799_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31799 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31799 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31799_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787_state12 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31787_state12_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31800_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31800 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31800 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31800_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31787_state13 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31787_state13_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31801_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31801 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31801 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31801_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802 {
  position:absolute;
  left:211px;
  top:504px;
}
#u31802_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31802_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31803_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31803 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31803 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31803_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31802_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31804_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31804 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31804 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31804_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31802_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31805_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31805 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31805 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31805_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802_state3 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31802_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31806_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31806 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31806 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31806_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802_state4 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31802_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31807_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31807 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31807 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31807_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802_state5 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31802_state5_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31808_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31808 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31808 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31808_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802_state6 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31802_state6_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31809_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31809 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31809 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31809_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802_state7 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31802_state7_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31810_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31810 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31810 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802_state8 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31802_state8_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31811_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31811 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31811 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31811_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802_state9 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31802_state9_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31812_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31812 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31812 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31812_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802_state10 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31802_state10_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31813_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31813 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31813 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802_state11 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31802_state11_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31814_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31814 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31814 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31814_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802_state12 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31802_state12_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31815_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31815 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31815 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31815_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31802_state13 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31802_state13_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31816_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31816 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31816 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31816_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817 {
  position:absolute;
  left:251px;
  top:504px;
}
#u31817_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31817_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31818_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31818 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31818 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31818_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31817_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31819_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31819 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31819 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31819_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31817_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31820_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31820 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31820 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31820_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817_state3 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31817_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31821_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31821 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31821 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31821_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817_state4 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31817_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31822_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31822 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31822 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31822_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817_state5 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31817_state5_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31823_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31823 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31823 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31823_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817_state6 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31817_state6_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31824_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31824 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31824 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31824_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817_state7 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31817_state7_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31825_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31825 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31825 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31825_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817_state8 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31817_state8_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31826_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31826 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31826 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31826_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817_state9 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31817_state9_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31827_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31827 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31827 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31827_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817_state10 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31817_state10_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31828_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31828 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31828 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817_state11 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31817_state11_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31829_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31829 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31829 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31829_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817_state12 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31817_state12_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31830_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31830 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31830 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31830_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31817_state13 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31817_state13_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31831_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31831 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31831 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31831_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832 {
  position:absolute;
  left:292px;
  top:504px;
}
#u31832_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31832_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31833_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31833 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31833 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31833_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31832_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31834_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31834 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31834 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31834_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31832_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31835_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31835 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31835 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31835_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832_state3 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31832_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31836_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31836 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31836 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31836_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832_state4 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31832_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31837_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31837 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31837 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31837_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832_state5 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31832_state5_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31838_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31838 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31838 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832_state6 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31832_state6_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31839_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31839 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31839 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832_state7 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31832_state7_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31840_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31840 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31840 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31840_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832_state8 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31832_state8_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31841_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31841 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31841 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31841_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832_state9 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31832_state9_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31842_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31842 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31842 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31842_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832_state10 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31832_state10_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31843_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31843 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31843 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832_state11 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31832_state11_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31844_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31844 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832_state12 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31832_state12_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31845_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31845 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31845 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31845_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31832_state13 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31832_state13_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31846_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31846 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31846 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31846_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847 {
  position:absolute;
  left:333px;
  top:504px;
}
#u31847_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31847_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31848_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31848 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31848 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31847_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31849_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31849 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31849 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31847_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31850_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31850 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31850 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847_state3 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31847_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31851_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31851 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31851 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847_state4 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31847_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31852_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31852 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31852 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847_state5 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31847_state5_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31853_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31853 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31853 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847_state6 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31847_state6_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31854_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31854 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31854 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31854_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847_state7 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31847_state7_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31855_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31855 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31855 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31855_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847_state8 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31847_state8_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31856_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31856 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31856 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31856_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847_state9 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31847_state9_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31857_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31857 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31857 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847_state10 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31847_state10_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31858_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31858 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31858 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847_state11 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31847_state11_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31859_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31859 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31859 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31859_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847_state12 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31847_state12_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31860_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31860 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31860 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31860_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31847_state13 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31847_state13_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31861_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31861 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31861 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862 {
  position:absolute;
  left:379px;
  top:504px;
}
#u31862_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31862_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31863_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31863 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31863 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:23px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31862_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31864_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31864 {
  border-width:0px;
  position:absolute;
  left:-4px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31864 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31864_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31862_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31865_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31865 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31865_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862_state3 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31862_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31866_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31866 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31866 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862_state4 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31862_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31867_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31867 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31867_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862_state5 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31862_state5_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31868_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31868 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862_state6 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31862_state6_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31869_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31869 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31869 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862_state7 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31862_state7_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31870_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u31870 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u31870 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862_state8 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31862_state8_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31871_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31871 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31871 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862_state9 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31862_state9_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31872_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31872 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31872 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31872_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862_state10 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31862_state10_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31873_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31873 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31873 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862_state11 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31862_state11_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31874_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31874 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31874 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31874_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862_state12 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31862_state12_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31875_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31875 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31875 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31862_state13 {
  position:relative;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31862_state13_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31876_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  background:inherit;
  background-color:rgba(125, 123, 123, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(125, 123, 123, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#454545;
}
#u31876 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:25px;
  display:flex;
  color:#454545;
}
#u31876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31877_input {
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#545353;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31877_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31877_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#545353;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31877_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31877_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:40px;
}
#u31877 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:410px;
  width:142px;
  height:40px;
  display:flex;
  font-size:19px;
  color:#545353;
}
#u31877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31877_img.hint {
}
#u31877.hint {
}
#u31877_img.disabled {
}
#u31877.disabled {
}
#u31877_img.hint.disabled {
}
#u31877.hint.disabled {
}
#u31878_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:34px;
  background:inherit;
  background-color:rgba(167, 167, 167, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(86, 86, 86, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
}
#u31878 {
  border-width:0px;
  position:absolute;
  left:171px;
  top:413px;
  width:117px;
  height:34px;
  display:flex;
  font-size:15px;
}
#u31878 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31879_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:7px;
}
#u31879 {
  border-width:0px;
  position:absolute;
  left:296px;
  top:427px;
  width:42px;
  height:6px;
  display:flex;
}
#u31879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31880_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  height:54px;
  background:inherit;
  background-color:rgba(215, 158, 2, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(44, 44, 44, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  text-align:left;
}
#u31880 {
  border-width:0px;
  position:absolute;
  left:602px;
  top:485px;
  width:436px;
  height:54px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u31880 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31881_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-10px;
  width:144px;
  height:22px;
}
#u31881p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
  width:138px;
  height:6px;
}
#u31881p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:6px;
}
#u31881p001 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
  width:4px;
  height:4px;
  -webkit-transform:rotate(-180deg);
  -moz-transform:rotate(-180deg);
  -ms-transform:rotate(-180deg);
  transform:rotate(-180deg);
}
#u31881p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u31881p002 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:-10px;
  width:24px;
  height:22px;
  -webkit-transform:rotate(-180deg);
  -moz-transform:rotate(-180deg);
  -ms-transform:rotate(-180deg);
  transform:rotate(-180deg);
}
#u31881p002_img {
  border-width:0px;
  position:absolute;
  left:-0px;
  top:0px;
  width:24px;
  height:22px;
}
#u31881 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:516px;
  width:133px;
  height:2px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u31881 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31881_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31882_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:47px;
  background:inherit;
  background-color:rgba(119, 118, 118, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:7px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:23px;
  color:#FFFFFF;
}
#u31882 {
  border-width:0px;
  position:absolute;
  left:131px;
  top:563px;
  width:271px;
  height:47px;
  display:flex;
  font-size:23px;
  color:#FFFFFF;
}
#u31882 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31882_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31883_input {
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31883_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31883_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:26px;
}
#u31883 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:417px;
  width:53px;
  height:26px;
  display:flex;
}
#u31883 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31883_img.disabled {
}
#u31883.disabled {
}
.u31883_input_option {
}
#u31884_input {
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31884_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31884_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:26px;
}
#u31884 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:417px;
  width:53px;
  height:26px;
  display:flex;
}
#u31884 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31884_img.disabled {
}
#u31884.disabled {
}
.u31884_input_option {
}
#u31885_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:34px;
  background:inherit;
  background-color:rgba(167, 167, 167, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(86, 86, 86, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
}
#u31885 {
  border-width:0px;
  position:absolute;
  left:346px;
  top:413px;
  width:117px;
  height:34px;
  display:flex;
  font-size:15px;
}
#u31885 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31885_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31886_input {
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31886_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31886_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:26px;
}
#u31886 {
  border-width:0px;
  position:absolute;
  left:351px;
  top:417px;
  width:53px;
  height:26px;
  display:flex;
}
#u31886 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31886_img.disabled {
}
#u31886.disabled {
}
.u31886_input_option {
}
#u31887_input {
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31887_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31887_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:26px;
}
#u31887 {
  border-width:0px;
  position:absolute;
  left:404px;
  top:417px;
  width:53px;
  height:26px;
  display:flex;
}
#u31887 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31887_img.disabled {
}
#u31887.disabled {
}
.u31887_input_option {
}
#u31888_input {
  position:absolute;
  left:0px;
  top:0px;
  width:244px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  letter-spacing:normal;
  color:#E00101;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31888_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:244px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31888_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:244px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  letter-spacing:normal;
  color:#E00101;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31888_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:244px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31888_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:244px;
  height:40px;
}
#u31888 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:227px;
  width:244px;
  height:40px;
  display:flex;
  font-size:14px;
  color:#E00101;
}
#u31888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31888_img.hint {
}
#u31888.hint {
}
#u31888_img.disabled {
}
#u31888.disabled {
}
#u31888_img.hint.disabled {
}
#u31888.hint.disabled {
}
#u31889_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:184px;
  height:54px;
  background:inherit;
  background-color:rgba(215, 158, 2, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(44, 44, 44, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  text-align:left;
}
#u31889 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:279px;
  width:184px;
  height:54px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u31889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31890_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-10px;
  width:255px;
  height:22px;
}
#u31890p000 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-4px;
  width:254px;
  height:10px;
  -webkit-transform:rotate(0.4031458163964885deg);
  -moz-transform:rotate(0.4031458163964885deg);
  -ms-transform:rotate(0.4031458163964885deg);
  transform:rotate(0.4031458163964885deg);
}
#u31890p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-0px;
  width:254px;
  height:10px;
}
#u31890p001 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-1px;
  width:4px;
  height:4px;
  -webkit-transform:rotate(166.4031458163965deg);
  -moz-transform:rotate(166.4031458163965deg);
  -ms-transform:rotate(166.4031458163965deg);
  transform:rotate(166.4031458163965deg);
}
#u31890p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u31890p002 {
  border-width:0px;
  position:absolute;
  left:226px;
  top:-12px;
  width:26px;
  height:24px;
  -webkit-transform:rotate(180.4031458163965deg);
  -moz-transform:rotate(180.4031458163965deg);
  -ms-transform:rotate(180.4031458163965deg);
  transform:rotate(180.4031458163965deg);
}
#u31890p002_img {
  border-width:0px;
  position:absolute;
  left:-0px;
  top:0px;
  width:26px;
  height:24px;
}
#u31890 {
  border-width:0px;
  position:absolute;
  left:423px;
  top:276px;
  width:244px;
  height:2px;
  display:flex;
  -webkit-transform:rotate(-166.4031458163965deg);
  -moz-transform:rotate(-166.4031458163965deg);
  -ms-transform:rotate(-166.4031458163965deg);
  transform:rotate(-166.4031458163965deg);
}
#u31890 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31890_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31891_input {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  letter-spacing:normal;
  color:#545353;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31891_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31891_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  letter-spacing:normal;
  color:#545353;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31891_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31891_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u31891 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:497px;
  width:98px;
  height:40px;
  display:flex;
  font-size:14px;
  color:#545353;
}
#u31891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31891_img.hint {
}
#u31891.hint {
}
#u31891_img.disabled {
}
#u31891.disabled {
}
#u31891_img.hint.disabled {
}
#u31891.hint.disabled {
}
#u31892 label {
  left:0px;
  width:100%;
}
#u31892_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:20px;
  height:20px;
}
#u31892 {
  border-width:0px;
  position:absolute;
  left:137px;
  top:96px;
  width:148px;
  height:22px;
  display:flex;
  font-size:19px;
}
#u31892 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
}
#u31892_img.selected {
}
#u31892.selected {
}
#u31892_img.disabled {
}
#u31892.disabled {
}
#u31892_img.selected.error {
}
#u31892.selected.error {
}
#u31892_img.selected.hint {
}
#u31892.selected.hint {
}
#u31892_img.selected.error.hint {
}
#u31892.selected.error.hint {
}
#u31892_img.mouseOver.selected {
}
#u31892.mouseOver.selected {
}
#u31892_img.mouseOver.selected.error {
}
#u31892.mouseOver.selected.error {
}
#u31892_img.mouseOver.selected.hint {
}
#u31892.mouseOver.selected.hint {
}
#u31892_img.mouseOver.selected.error.hint {
}
#u31892.mouseOver.selected.error.hint {
}
#u31892_img.mouseDown.selected {
}
#u31892.mouseDown.selected {
}
#u31892_img.mouseDown.selected.error {
}
#u31892.mouseDown.selected.error {
}
#u31892_img.mouseDown.selected.hint {
}
#u31892.mouseDown.selected.hint {
}
#u31892_img.mouseDown.selected.error.hint {
}
#u31892.mouseDown.selected.error.hint {
}
#u31892_img.mouseOver.mouseDown.selected {
}
#u31892.mouseOver.mouseDown.selected {
}
#u31892_img.mouseOver.mouseDown.selected.error {
}
#u31892.mouseOver.mouseDown.selected.error {
}
#u31892_img.mouseOver.mouseDown.selected.hint {
}
#u31892.mouseOver.mouseDown.selected.hint {
}
#u31892_img.mouseOver.mouseDown.selected.error.hint {
}
#u31892.mouseOver.mouseDown.selected.error.hint {
}
#u31892_img.focused.selected {
}
#u31892.focused.selected {
}
#u31892_img.focused.selected.error {
}
#u31892.focused.selected.error {
}
#u31892_img.focused.selected.hint {
}
#u31892.focused.selected.hint {
}
#u31892_img.focused.selected.error.hint {
}
#u31892.focused.selected.error.hint {
}
#u31892_img.selected.disabled {
}
#u31892.selected.disabled {
}
#u31892_img.selected.hint.disabled {
}
#u31892.selected.hint.disabled {
}
#u31892_img.selected.error.disabled {
}
#u31892.selected.error.disabled {
}
#u31892_img.selected.error.hint.disabled {
}
#u31892.selected.error.hint.disabled {
}
#u31892_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:124px;
  word-wrap:break-word;
  text-transform:none;
}
#u31892_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u31893 label {
  left:0px;
  width:100%;
}
#u31893_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:20px;
  height:20px;
}
#u31893 {
  border-width:0px;
  position:absolute;
  left:296px;
  top:95px;
  width:127px;
  height:23px;
  display:flex;
  font-size:20px;
}
#u31893 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
}
#u31893_img.selected {
}
#u31893.selected {
}
#u31893_img.disabled {
}
#u31893.disabled {
}
#u31893_img.selected.error {
}
#u31893.selected.error {
}
#u31893_img.selected.hint {
}
#u31893.selected.hint {
}
#u31893_img.selected.error.hint {
}
#u31893.selected.error.hint {
}
#u31893_img.mouseOver.selected {
}
#u31893.mouseOver.selected {
}
#u31893_img.mouseOver.selected.error {
}
#u31893.mouseOver.selected.error {
}
#u31893_img.mouseOver.selected.hint {
}
#u31893.mouseOver.selected.hint {
}
#u31893_img.mouseOver.selected.error.hint {
}
#u31893.mouseOver.selected.error.hint {
}
#u31893_img.mouseDown.selected {
}
#u31893.mouseDown.selected {
}
#u31893_img.mouseDown.selected.error {
}
#u31893.mouseDown.selected.error {
}
#u31893_img.mouseDown.selected.hint {
}
#u31893.mouseDown.selected.hint {
}
#u31893_img.mouseDown.selected.error.hint {
}
#u31893.mouseDown.selected.error.hint {
}
#u31893_img.mouseOver.mouseDown.selected {
}
#u31893.mouseOver.mouseDown.selected {
}
#u31893_img.mouseOver.mouseDown.selected.error {
}
#u31893.mouseOver.mouseDown.selected.error {
}
#u31893_img.mouseOver.mouseDown.selected.hint {
}
#u31893.mouseOver.mouseDown.selected.hint {
}
#u31893_img.mouseOver.mouseDown.selected.error.hint {
}
#u31893.mouseOver.mouseDown.selected.error.hint {
}
#u31893_img.focused.selected {
}
#u31893.focused.selected {
}
#u31893_img.focused.selected.error {
}
#u31893.focused.selected.error {
}
#u31893_img.focused.selected.hint {
}
#u31893.focused.selected.hint {
}
#u31893_img.focused.selected.error.hint {
}
#u31893.focused.selected.error.hint {
}
#u31893_img.selected.disabled {
}
#u31893.selected.disabled {
}
#u31893_img.selected.hint.disabled {
}
#u31893.selected.hint.disabled {
}
#u31893_img.selected.error.disabled {
}
#u31893.selected.error.disabled {
}
#u31893_img.selected.error.hint.disabled {
}
#u31893.selected.error.hint.disabled {
}
#u31893_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:103px;
  word-wrap:break-word;
  text-transform:none;
}
#u31893_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u31894_input {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#414141;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31894_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31894_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#414141;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31894_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31894_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u31894 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:189px;
  width:98px;
  height:40px;
  display:flex;
  font-size:19px;
  color:#414141;
}
#u31894 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31894_img.hint {
}
#u31894.hint {
}
#u31894_img.disabled {
}
#u31894.disabled {
}
#u31894_img.hint.disabled {
}
#u31894.hint.disabled {
}
#u31895_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(230, 19, 19, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#E40606;
}
#u31895 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:181px;
  width:51px;
  height:48px;
  display:flex;
  font-size:18px;
  color:#E40606;
}
#u31895 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31895_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31896_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(230, 19, 19, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#E40606;
}
#u31896 {
  border-width:0px;
  position:absolute;
  left:205px;
  top:181px;
  width:51px;
  height:48px;
  display:flex;
  font-size:18px;
  color:#E40606;
}
#u31896 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31896_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31897_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 0, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#969696;
  text-align:left;
}
#u31897 {
  border-width:0px;
  position:absolute;
  left:268px;
  top:181px;
  width:51px;
  height:48px;
  display:flex;
  font-size:18px;
  color:#969696;
  text-align:left;
}
#u31897 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31897_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31898_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 0, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#969696;
  text-align:left;
}
#u31898 {
  border-width:0px;
  position:absolute;
  left:335px;
  top:181px;
  width:51px;
  height:48px;
  display:flex;
  font-size:18px;
  color:#969696;
  text-align:left;
}
#u31898 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31898_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31899_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 0, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#969696;
  text-align:left;
}
#u31899 {
  border-width:0px;
  position:absolute;
  left:402px;
  top:181px;
  width:51px;
  height:48px;
  display:flex;
  font-size:18px;
  color:#969696;
  text-align:left;
}
#u31899 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31899_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31900_input {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#414141;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31900_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31900_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#414141;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31900_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31900_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
}
#u31900 {
  border-width:0px;
  position:absolute;
  left:191px;
  top:181px;
  width:20px;
  height:40px;
  display:flex;
  font-size:20px;
  color:#414141;
}
#u31900 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31900_img.hint {
}
#u31900.hint {
}
#u31900_img.disabled {
}
#u31900.disabled {
}
#u31900_img.hint.disabled {
}
#u31900.hint.disabled {
}
#u31901_input {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#414141;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31901_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31901_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#414141;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31901_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31901_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
}
#u31901 {
  border-width:0px;
  position:absolute;
  left:254px;
  top:181px;
  width:20px;
  height:40px;
  display:flex;
  font-size:20px;
  color:#414141;
}
#u31901 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31901_img.hint {
}
#u31901.hint {
}
#u31901_img.disabled {
}
#u31901.disabled {
}
#u31901_img.hint.disabled {
}
#u31901.hint.disabled {
}
#u31902_input {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#414141;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31902_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31902_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#414141;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31902_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31902_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
}
#u31902 {
  border-width:0px;
  position:absolute;
  left:319px;
  top:181px;
  width:20px;
  height:40px;
  display:flex;
  font-size:20px;
  color:#414141;
}
#u31902 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31902_img.hint {
}
#u31902.hint {
}
#u31902_img.disabled {
}
#u31902.disabled {
}
#u31902_img.hint.disabled {
}
#u31902.hint.disabled {
}
#u31903_input {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#414141;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31903_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31903_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#414141;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31903_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31903_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:40px;
}
#u31903 {
  border-width:0px;
  position:absolute;
  left:386px;
  top:181px;
  width:20px;
  height:40px;
  display:flex;
  font-size:20px;
  color:#414141;
}
#u31903 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31903_img.hint {
}
#u31903.hint {
}
#u31903_img.disabled {
}
#u31903.disabled {
}
#u31903_img.hint.disabled {
}
#u31903.hint.disabled {
}
#u31904 label {
  left:0px;
  width:100%;
}
#u31904_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:20px;
  height:20px;
}
#u31904 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:267px;
  width:148px;
  height:22px;
  display:flex;
  font-size:19px;
}
#u31904 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
}
#u31904_img.selected {
}
#u31904.selected {
}
#u31904_img.disabled {
}
#u31904.disabled {
}
#u31904_img.selected.error {
}
#u31904.selected.error {
}
#u31904_img.selected.hint {
}
#u31904.selected.hint {
}
#u31904_img.selected.error.hint {
}
#u31904.selected.error.hint {
}
#u31904_img.mouseOver.selected {
}
#u31904.mouseOver.selected {
}
#u31904_img.mouseOver.selected.error {
}
#u31904.mouseOver.selected.error {
}
#u31904_img.mouseOver.selected.hint {
}
#u31904.mouseOver.selected.hint {
}
#u31904_img.mouseOver.selected.error.hint {
}
#u31904.mouseOver.selected.error.hint {
}
#u31904_img.mouseDown.selected {
}
#u31904.mouseDown.selected {
}
#u31904_img.mouseDown.selected.error {
}
#u31904.mouseDown.selected.error {
}
#u31904_img.mouseDown.selected.hint {
}
#u31904.mouseDown.selected.hint {
}
#u31904_img.mouseDown.selected.error.hint {
}
#u31904.mouseDown.selected.error.hint {
}
#u31904_img.mouseOver.mouseDown.selected {
}
#u31904.mouseOver.mouseDown.selected {
}
#u31904_img.mouseOver.mouseDown.selected.error {
}
#u31904.mouseOver.mouseDown.selected.error {
}
#u31904_img.mouseOver.mouseDown.selected.hint {
}
#u31904.mouseOver.mouseDown.selected.hint {
}
#u31904_img.mouseOver.mouseDown.selected.error.hint {
}
#u31904.mouseOver.mouseDown.selected.error.hint {
}
#u31904_img.focused.selected {
}
#u31904.focused.selected {
}
#u31904_img.focused.selected.error {
}
#u31904.focused.selected.error {
}
#u31904_img.focused.selected.hint {
}
#u31904.focused.selected.hint {
}
#u31904_img.focused.selected.error.hint {
}
#u31904.focused.selected.error.hint {
}
#u31904_img.selected.disabled {
}
#u31904.selected.disabled {
}
#u31904_img.selected.hint.disabled {
}
#u31904.selected.hint.disabled {
}
#u31904_img.selected.error.disabled {
}
#u31904.selected.error.disabled {
}
#u31904_img.selected.error.hint.disabled {
}
#u31904.selected.error.hint.disabled {
}
#u31904_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:124px;
  word-wrap:break-word;
  text-transform:none;
}
#u31904_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u31905 label {
  left:0px;
  width:100%;
}
#u31905_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:20px;
  height:20px;
}
#u31905 {
  border-width:0px;
  position:absolute;
  left:301px;
  top:266px;
  width:127px;
  height:23px;
  display:flex;
  font-size:20px;
}
#u31905 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
}
#u31905_img.selected {
}
#u31905.selected {
}
#u31905_img.disabled {
}
#u31905.disabled {
}
#u31905_img.selected.error {
}
#u31905.selected.error {
}
#u31905_img.selected.hint {
}
#u31905.selected.hint {
}
#u31905_img.selected.error.hint {
}
#u31905.selected.error.hint {
}
#u31905_img.mouseOver.selected {
}
#u31905.mouseOver.selected {
}
#u31905_img.mouseOver.selected.error {
}
#u31905.mouseOver.selected.error {
}
#u31905_img.mouseOver.selected.hint {
}
#u31905.mouseOver.selected.hint {
}
#u31905_img.mouseOver.selected.error.hint {
}
#u31905.mouseOver.selected.error.hint {
}
#u31905_img.mouseDown.selected {
}
#u31905.mouseDown.selected {
}
#u31905_img.mouseDown.selected.error {
}
#u31905.mouseDown.selected.error {
}
#u31905_img.mouseDown.selected.hint {
}
#u31905.mouseDown.selected.hint {
}
#u31905_img.mouseDown.selected.error.hint {
}
#u31905.mouseDown.selected.error.hint {
}
#u31905_img.mouseOver.mouseDown.selected {
}
#u31905.mouseOver.mouseDown.selected {
}
#u31905_img.mouseOver.mouseDown.selected.error {
}
#u31905.mouseOver.mouseDown.selected.error {
}
#u31905_img.mouseOver.mouseDown.selected.hint {
}
#u31905.mouseOver.mouseDown.selected.hint {
}
#u31905_img.mouseOver.mouseDown.selected.error.hint {
}
#u31905.mouseOver.mouseDown.selected.error.hint {
}
#u31905_img.focused.selected {
}
#u31905.focused.selected {
}
#u31905_img.focused.selected.error {
}
#u31905.focused.selected.error {
}
#u31905_img.focused.selected.hint {
}
#u31905.focused.selected.hint {
}
#u31905_img.focused.selected.error.hint {
}
#u31905.focused.selected.error.hint {
}
#u31905_img.selected.disabled {
}
#u31905.selected.disabled {
}
#u31905_img.selected.hint.disabled {
}
#u31905.selected.hint.disabled {
}
#u31905_img.selected.error.disabled {
}
#u31905.selected.error.disabled {
}
#u31905_img.selected.error.hint.disabled {
}
#u31905.selected.error.hint.disabled {
}
#u31905_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:103px;
  word-wrap:break-word;
  text-transform:none;
}
#u31905_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u31906_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:37px;
}
#u31906 {
  border-width:0px;
  position:absolute;
  left:544px;
  top:410px;
  width:37px;
  height:37px;
  display:flex;
  font-size:27px;
  color:#FFFFFF;
}
#u31906 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31906_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31907_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:37px;
}
#u31907 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:410px;
  width:37px;
  height:37px;
  display:flex;
  font-size:27px;
  color:#FFFFFF;
}
#u31907 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31908_input {
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#545353;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31908_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31908_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#545353;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31908_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31908_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:40px;
}
#u31908 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:358px;
  width:142px;
  height:40px;
  display:flex;
  font-size:19px;
  color:#545353;
}
#u31908 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31908_img.hint {
}
#u31908.hint {
}
#u31908_img.disabled {
}
#u31908.disabled {
}
#u31908_img.hint.disabled {
}
#u31908.hint.disabled {
}
#u31909_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:34px;
  background:inherit;
  background-color:rgba(167, 167, 167, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(86, 86, 86, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#ADADAD;
  text-align:left;
}
#u31909 {
  border-width:0px;
  position:absolute;
  left:171px;
  top:361px;
  width:292px;
  height:34px;
  display:flex;
  font-size:14px;
  color:#ADADAD;
  text-align:left;
}
#u31909 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31909_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31910_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:37px;
}
#u31910 {
  border-width:0px;
  position:absolute;
  left:544px;
  top:358px;
  width:37px;
  height:37px;
  display:flex;
  font-size:27px;
  color:#FFFFFF;
}
#u31910 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31910_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31911_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:37px;
}
#u31911 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:358px;
  width:37px;
  height:37px;
  display:flex;
  font-size:27px;
  color:#FFFFFF;
}
#u31911 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31912_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31912_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31912_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31912_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31912_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:25px;
}
#u31912 {
  border-width:0px;
  position:absolute;
  left:171px;
  top:444px;
  width:69px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#908F8F;
}
#u31912 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31912_img.hint {
}
#u31912.hint {
}
#u31912_img.disabled {
}
#u31912.disabled {
}
#u31912_img.hint.disabled {
}
#u31912.hint.disabled {
}
#u31913_input {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31913_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31913_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31913_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31913_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:25px;
}
#u31913 {
  border-width:0px;
  position:absolute;
  left:346px;
  top:443px;
  width:69px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#908F8F;
}
#u31913 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31913_img.hint {
}
#u31913.hint {
}
#u31913_img.disabled {
}
#u31913.disabled {
}
#u31913_img.hint.disabled {
}
#u31913.hint.disabled {
}
#u31739_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  background-color:rgba(240, 176, 3, 1);
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31739_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31914 {
  position:absolute;
  left:0px;
  top:0px;
}
#u31914_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31914_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31915 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31916_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31916 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  display:flex;
}
#u31916 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31916_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31917_input {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31917_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31917_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31917_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31917_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
}
#u31917 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:10px;
  width:186px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31917 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31917_img.hint {
}
#u31917.hint {
}
#u31917_img.disabled {
}
#u31917.disabled {
}
#u31917_img.hint.disabled {
}
#u31917.hint.disabled {
}
#u31918_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:980px;
  height:2px;
}
#u31918 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:71px;
  width:979px;
  height:1px;
  display:flex;
}
#u31918 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31919_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u31919 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:28px;
  width:24px;
  height:24px;
  display:flex;
}
#u31919 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31739_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  background-color:rgba(240, 176, 3, 1);
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31739_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31920 {
  position:absolute;
  left:0px;
  top:0px;
}
#u31920_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31920_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31921 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31922_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31922 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  display:flex;
}
#u31922 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31922_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31923_input {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31923_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31923_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31923_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31923_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
}
#u31923 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:10px;
  width:186px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31923 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31923_img.hint {
}
#u31923.hint {
}
#u31923_img.disabled {
}
#u31923.disabled {
}
#u31923_img.hint.disabled {
}
#u31923.hint.disabled {
}
#u31924_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:980px;
  height:2px;
}
#u31924 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:71px;
  width:979px;
  height:1px;
  display:flex;
}
#u31924 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31924_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31925_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u31925 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:28px;
  width:24px;
  height:24px;
  display:flex;
}
#u31925 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31925_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31926_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:630px;
  height:525px;
}
#u31926 {
  border-width:0px;
  position:absolute;
  left:175px;
  top:83px;
  width:630px;
  height:525px;
  display:flex;
}
#u31926 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31926_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31927_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:85px;
  background:inherit;
  background-color:rgba(234, 145, 0, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(6, 6, 6, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  text-align:left;
}
#u31927 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:297px;
  width:112px;
  height:85px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u31927 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31927_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31928_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-10px;
  width:64px;
  height:22px;
}
#u31928p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-1px;
  width:58px;
  height:4px;
  -webkit-transform:rotate(-0.10032397857853549deg);
  -moz-transform:rotate(-0.10032397857853549deg);
  -ms-transform:rotate(-0.10032397857853549deg);
  transform:rotate(-0.10032397857853549deg);
}
#u31928p000_img {
  border-width:0px;
  position:absolute;
  left:-0px;
  top:0px;
  width:58px;
  height:4px;
}
#u31928p001 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:4px;
  height:4px;
  -webkit-transform:rotate(-0.10032397857853549deg);
  -moz-transform:rotate(-0.10032397857853549deg);
  -ms-transform:rotate(-0.10032397857853549deg);
  transform:rotate(-0.10032397857853549deg);
}
#u31928p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-0px;
  width:4px;
  height:4px;
}
#u31928p002 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:-11px;
  width:26px;
  height:24px;
  -webkit-transform:rotate(-180.10032397857853deg);
  -moz-transform:rotate(-180.10032397857853deg);
  -ms-transform:rotate(-180.10032397857853deg);
  transform:rotate(-180.10032397857853deg);
}
#u31928p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:24px;
}
#u31928 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:343px;
  width:53px;
  height:2px;
  display:flex;
  -webkit-transform:rotate(0.10032397857853549deg);
  -moz-transform:rotate(0.10032397857853549deg);
  -ms-transform:rotate(0.10032397857853549deg);
  transform:rotate(0.10032397857853549deg);
}
#u31928 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31739_state3 {
  position:relative;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  background-color:rgba(240, 176, 3, 1);
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u31739_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31929 {
  position:absolute;
  left:0px;
  top:0px;
}
#u31929_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31929_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u31930 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31931_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31931 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1088px;
  height:634px;
  display:flex;
}
#u31931 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31931_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31932_input {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31932_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31932_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31932_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:25px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31932_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:56px;
}
#u31932 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:10px;
  width:186px;
  height:56px;
  display:flex;
  font-size:25px;
}
#u31932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31932_img.hint {
}
#u31932.hint {
}
#u31932_img.disabled {
}
#u31932.disabled {
}
#u31932_img.hint.disabled {
}
#u31932.hint.disabled {
}
#u31933_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:980px;
  height:2px;
}
#u31933 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:71px;
  width:979px;
  height:1px;
  display:flex;
}
#u31933 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31933_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31934_input {
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31934_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31934_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31934_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:17px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31934_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:40px;
}
#u31934 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:134px;
  width:750px;
  height:40px;
  display:flex;
  font-size:17px;
  color:#908F8F;
}
#u31934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31934_img.hint {
}
#u31934.hint {
}
#u31934_img.disabled {
}
#u31934.disabled {
}
#u31934_img.hint.disabled {
}
#u31934.hint.disabled {
}
#u31935_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:28px;
  background:inherit;
  background-color:rgba(163, 163, 163, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31935 {
  border-width:0px;
  position:absolute;
  left:238px;
  top:26px;
  width:70px;
  height:28px;
  display:flex;
}
#u31935 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31935_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31936_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u31936 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:28px;
  width:24px;
  height:24px;
  display:flex;
}
#u31936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31937 label {
  left:0px;
  width:100%;
}
#u31937_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:20px;
  height:20px;
}
#u31937 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:107px;
  width:107px;
  height:22px;
  display:flex;
  font-size:19px;
}
#u31937 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
}
#u31937_img.selected {
}
#u31937.selected {
}
#u31937_img.disabled {
}
#u31937.disabled {
}
#u31937_img.selected.error {
}
#u31937.selected.error {
}
#u31937_img.selected.hint {
}
#u31937.selected.hint {
}
#u31937_img.selected.error.hint {
}
#u31937.selected.error.hint {
}
#u31937_img.mouseOver.selected {
}
#u31937.mouseOver.selected {
}
#u31937_img.mouseOver.selected.error {
}
#u31937.mouseOver.selected.error {
}
#u31937_img.mouseOver.selected.hint {
}
#u31937.mouseOver.selected.hint {
}
#u31937_img.mouseOver.selected.error.hint {
}
#u31937.mouseOver.selected.error.hint {
}
#u31937_img.mouseDown.selected {
}
#u31937.mouseDown.selected {
}
#u31937_img.mouseDown.selected.error {
}
#u31937.mouseDown.selected.error {
}
#u31937_img.mouseDown.selected.hint {
}
#u31937.mouseDown.selected.hint {
}
#u31937_img.mouseDown.selected.error.hint {
}
#u31937.mouseDown.selected.error.hint {
}
#u31937_img.mouseOver.mouseDown.selected {
}
#u31937.mouseOver.mouseDown.selected {
}
#u31937_img.mouseOver.mouseDown.selected.error {
}
#u31937.mouseOver.mouseDown.selected.error {
}
#u31937_img.mouseOver.mouseDown.selected.hint {
}
#u31937.mouseOver.mouseDown.selected.hint {
}
#u31937_img.mouseOver.mouseDown.selected.error.hint {
}
#u31937.mouseOver.mouseDown.selected.error.hint {
}
#u31937_img.focused.selected {
}
#u31937.focused.selected {
}
#u31937_img.focused.selected.error {
}
#u31937.focused.selected.error {
}
#u31937_img.focused.selected.hint {
}
#u31937.focused.selected.hint {
}
#u31937_img.focused.selected.error.hint {
}
#u31937.focused.selected.error.hint {
}
#u31937_img.selected.disabled {
}
#u31937.selected.disabled {
}
#u31937_img.selected.hint.disabled {
}
#u31937.selected.hint.disabled {
}
#u31937_img.selected.error.disabled {
}
#u31937.selected.error.disabled {
}
#u31937_img.selected.error.hint.disabled {
}
#u31937.selected.error.hint.disabled {
}
#u31937_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:83px;
  word-wrap:break-word;
  text-transform:none;
}
#u31937_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u31938 label {
  left:0px;
  width:100%;
}
#u31938_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:20px;
  height:20px;
}
#u31938 {
  border-width:0px;
  position:absolute;
  left:181px;
  top:106px;
  width:127px;
  height:23px;
  display:flex;
  font-size:20px;
}
#u31938 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
}
#u31938_img.selected {
}
#u31938.selected {
}
#u31938_img.disabled {
}
#u31938.disabled {
}
#u31938_img.selected.error {
}
#u31938.selected.error {
}
#u31938_img.selected.hint {
}
#u31938.selected.hint {
}
#u31938_img.selected.error.hint {
}
#u31938.selected.error.hint {
}
#u31938_img.mouseOver.selected {
}
#u31938.mouseOver.selected {
}
#u31938_img.mouseOver.selected.error {
}
#u31938.mouseOver.selected.error {
}
#u31938_img.mouseOver.selected.hint {
}
#u31938.mouseOver.selected.hint {
}
#u31938_img.mouseOver.selected.error.hint {
}
#u31938.mouseOver.selected.error.hint {
}
#u31938_img.mouseDown.selected {
}
#u31938.mouseDown.selected {
}
#u31938_img.mouseDown.selected.error {
}
#u31938.mouseDown.selected.error {
}
#u31938_img.mouseDown.selected.hint {
}
#u31938.mouseDown.selected.hint {
}
#u31938_img.mouseDown.selected.error.hint {
}
#u31938.mouseDown.selected.error.hint {
}
#u31938_img.mouseOver.mouseDown.selected {
}
#u31938.mouseOver.mouseDown.selected {
}
#u31938_img.mouseOver.mouseDown.selected.error {
}
#u31938.mouseOver.mouseDown.selected.error {
}
#u31938_img.mouseOver.mouseDown.selected.hint {
}
#u31938.mouseOver.mouseDown.selected.hint {
}
#u31938_img.mouseOver.mouseDown.selected.error.hint {
}
#u31938.mouseOver.mouseDown.selected.error.hint {
}
#u31938_img.focused.selected {
}
#u31938.focused.selected {
}
#u31938_img.focused.selected.error {
}
#u31938.focused.selected.error {
}
#u31938_img.focused.selected.hint {
}
#u31938.focused.selected.hint {
}
#u31938_img.focused.selected.error.hint {
}
#u31938.focused.selected.error.hint {
}
#u31938_img.selected.disabled {
}
#u31938.selected.disabled {
}
#u31938_img.selected.hint.disabled {
}
#u31938.selected.hint.disabled {
}
#u31938_img.selected.error.disabled {
}
#u31938.selected.error.disabled {
}
#u31938_img.selected.error.hint.disabled {
}
#u31938.selected.error.hint.disabled {
}
#u31938_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:103px;
  word-wrap:break-word;
  text-transform:none;
}
#u31938_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u31939_input {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31939_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31939_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31939_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31939_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u31939 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:236px;
  width:98px;
  height:40px;
  display:flex;
  font-size:19px;
  color:#908F8F;
}
#u31939 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31939_img.hint {
}
#u31939.hint {
}
#u31939_img.disabled {
}
#u31939.disabled {
}
#u31939_img.hint.disabled {
}
#u31939.hint.disabled {
}
#u31940_input {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31940_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31940_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31940_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31940_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u31940 {
  border-width:0px;
  position:absolute;
  left:446px;
  top:236px;
  width:98px;
  height:40px;
  display:flex;
  font-size:19px;
  color:#908F8F;
}
#u31940 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31940_img.hint {
}
#u31940.hint {
}
#u31940_img.disabled {
}
#u31940.disabled {
}
#u31940_img.hint.disabled {
}
#u31940.hint.disabled {
}
#u31941_input {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31941_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31941_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#908F8F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31941_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:19px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u31941_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u31941 {
  border-width:0px;
  position:absolute;
  left:868px;
  top:236px;
  width:98px;
  height:40px;
  display:flex;
  font-size:19px;
  color:#908F8F;
}
#u31941 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31941_img.hint {
}
#u31941.hint {
}
#u31941_img.disabled {
}
#u31941.disabled {
}
#u31941_img.hint.disabled {
}
#u31941.hint.disabled {
}
#u31942_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:980px;
  height:2px;
}
#u31942 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:278px;
  width:979px;
  height:1px;
  display:flex;
  color:#B2B2B2;
}
#u31942 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31942_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31943_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:39px;
  background:inherit;
  background-color:rgba(119, 118, 118, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:17px;
  color:#F2F2F2;
}
#u31943 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:182px;
  width:132px;
  height:39px;
  display:flex;
  font-size:17px;
  color:#F2F2F2;
}
#u31943 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31943_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31944_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:42px;
  background:inherit;
  background-color:rgba(232, 144, 0, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(4, 4, 4, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u31944 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:17px;
  width:281px;
  height:42px;
  display:flex;
  font-size:18px;
}
#u31944 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31944_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31945_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-10px;
  width:105px;
  height:22px;
}
#u31945p000 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:98px;
  height:6px;
}
#u31945p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:6px;
}
#u31945p001 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:4px;
  height:4px;
  -webkit-transform:rotate(-180deg);
  -moz-transform:rotate(-180deg);
  -ms-transform:rotate(-180deg);
  transform:rotate(-180deg);
}
#u31945p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u31945p002 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:-10px;
  width:24px;
  height:22px;
  -webkit-transform:rotate(-180deg);
  -moz-transform:rotate(-180deg);
  -ms-transform:rotate(-180deg);
  transform:rotate(-180deg);
}
#u31945p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:22px;
}
#u31945 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:38px;
  width:94px;
  height:2px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u31945 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31945_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
