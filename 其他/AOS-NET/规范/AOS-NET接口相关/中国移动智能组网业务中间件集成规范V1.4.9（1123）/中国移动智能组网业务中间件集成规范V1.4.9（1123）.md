
**中国移动智能组网业务中间件集成规范**

**V1.4.98**


# 目录
[1、组网中间件架构说明	3](#_toc106289309)

[1.1 组网中间件系统架构	3](#_toc106289310)

[2、组网中间件ubus封装定义	3](#_toc106289311)

[2.1 ubus介绍	3](#_toc106289312)

[2.2 ubus 依赖动态库	4](#_toc106289313)

[2.3 ubus 消息总线工作原理	4](#_toc106289314)

[2.4 ubus接口定义	5](#_toc106289315)

[2.4.1 组网设备基本信息	5](#_toc106289316)

[2.4.2 组网设备硬件信息	98]()

[2.4.3 组网设备端口信息	109]()

[2.4.4 组网设备上行信息	1210]()

[2.4.5 组网设备下挂设备信息	1816]()

[2.4.6 组网设备WIFI配置	2321]()

[2.4.7 组网设备WIFI统计信息	2926]()

[2.4.8 组网设备周边WIFI信息	3128]()

[2.4.9 组网设备漫游配置	3329]()

[2.4.10 组网设备定时任务	3834]()

[2.4.11 组网设备配置黑白名单	4036]()

[2.4.12 组网设备操控指令	4137]()

[2.4.13 组网设备配置保存	4541]()

[2.4.14 组网设备Mesh	4643]()

[2.4.15 组网设备网络限速	5348]()

[2.4.16 组网设备持续测速	5651]()

[2.4.17 组网设备WIFI信道状态信息(CSI)【可选】	5752]()

[2.4.18 组网设备Qos配置	6560]()

[2.4.19 组网设备 VLAN配置	6862]()

[附录A 响应码	7064]()

[附录B 修订记录	7064]()





# <a name="_toc106289309"></a>1、组网中间件架构说明
# <a name="_toc106289310"></a>1.1 组网中间件系统架构
组网中间件系统架构如图1.1 所示，组网能力集有ubusd、ahsapi、ahsapd 三部分组成

ubusd是ubus 总线消息服务器，负责进程间消息转递。

ahsapi向插件提供调用方法,负责将插件调用消息通过ubusd消息服务器转递给ahsapd。

` `ahsapd 模块需厂商实现，厂家应将组网设备基础能力封装成ubus接口能力模块ahsapd，提供对组网设备管理配置能力的开放。

![](Aspose.Words.7ba04225-5786-40de-88d8-a4bd7cd3bdea.001.png)

`                           `图1.1 组网中间件架构


# <a name="_toc106289311"></a>2、组网中间件ubus封装定义
# <a name="_toc106289312"></a>2.1 ubus介绍
ubus作为进程件的通信总线，采用linux socket进程间通信方式做了封装：

1) ubus提供了一个socket server：ubusd
1) ubus对client和server之间通信的消息格式进行了定义：client和server都必须将消息封装成json消息格式。
1) ubus对client端的消息处理抽象出“对象（object）”和“方法（method）”及“事件（event）”的概念。一个对象中包含多个方法，client需要向server注册收到特定json事件时的处理方法。对象和方法都有自己的名字，发送请求方只需在消息中指定要调用的对象和方法的名字即可。
# <a name="_toc106289313"></a>2.2 ubus 依赖动态库
使用ubus时需要引用一些动态库，主要包括：

1) libubus.so：ubus向外部提供的编程接口，例如创建socket，进行监听和连接，发送消息等接口函数。
1) libubox.so：ubus向外部提供的编程接口，例如等待和读取消息。
1) libblobmsg.so，libjson.so：提供了封装和解析json数据的接口，编程时不需要直接使用libjson.so，而是使用libblobmsg.so提供的更灵活的接口函数。
# <a name="_toc106289314"></a>2.3 ubus 消息总线工作原理
以图2.1为例，下面描述ubus消息总线的工作原理

![](Aspose.Words.7ba04225-5786-40de-88d8-a4bd7cd3bdea.002.png)

`                           `图2.1 ubus消息工作原理实例

client2进行请求的整个过程为：

1）client1向ubusd注册了两个对象：“interface”和“test”，其中“interface”对象中注册了两个method：“getlanip”和“setlanip”，对应的处理函数分别为func1()和func2()。“test”对象中注册了两个method：“hello”和“watch”，对应的处理函数分别为func3()和func4()。

2）接着创建一个client2用来与client1通信，注意，两个client之间不能直接通信，需要经ubusd（server）中转。

3）消息发到server后，server根据对象名找到应该将请求转发给client1，然后将消息发送到client1，client1进而调用func2()接受参数并处理，如果处理完成后需要回复client2，则发送回复消息。

Ubus内部工作机制如下图2.2所示，client1注册对象和方法，其实可认为是服务提供端，只不过对于ubusd来讲是一个socket client，client2去调用client1注册的方法。


`							`![](Aspose.Words.7ba04225-5786-40de-88d8-a4bd7cd3bdea.003.png)

`	                             `图2.2 ubus消息工作机制



# <a name="_toc106289315"></a>2.4 ubus接口定义
本节定义了组网中间件需要实现的ubus封装接口对象和通知信息及详细参数

插件调用接口为ahsapi向应用层提供调用接口，不涉及跟厂商开发ahsapd的封装内容

ubus connct连接path默认为null

# <a name="_toc106289316"></a>2.4.1 组网设备基本信息
插件调用接口：

int ahsapi\_get\_devbasic\_info(cJSON \*\* basicInfo)  //获取组网设备基本信息

basicInfo 为JSON字符串 ，包含参数内容如下表

获取设备基本信息对象方法定义如表2.4.1 -1所示：

`					 `表2.4.1 -1 设备基本信息对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|ahsapd. basic|getBasicInfo|无|||

|<p>{</p><p>"ahsapd. basic": {</p><p>"productToken": "",</p><p>"deviceType": "",</p><p>"deviceVendor": "",</p><p>"deviceModel": "",</p><p>"deviceSn": "",</p><p>"ponSn": " ",</p><p>"cmei": "",</p><p>"softwareVersion":"",</p><p>"hardwareVersion": "",</p><p>"ahsapdVersion": "",</p><p>"authId": "",</p><p>"devKey": "",</p><p>"dmKey": "",</p><p>"powerSupplyMode": 3,</p><p>"provinceCode":"",</p><p>"captureEth": "",</p><p>"deviceMac": "",</p><p>"ipAddress": "",</p><p>"ipv6IPAddress": "",</p><p>"meshType": 1,</p><p>"meshRole": 1,</p><p>"uplinkType": "",</p><p>"workingMode": "",</p><p>"radio5": 1，</p><p>"pppoeUser": ""，</p><p>"wifiSpecification":7,</p><p>"reserve": ""</p><p>}</p><p>}</p><p></p>||1\.0|
| - | - | - |

|||||获取组网设备基本信息|
| - | - | - | - | :- |

获取设备基本信息返回值参数详细定义如下表：

表2.4.1 - 2 设备基本信息返回值参数

|<a name="ole_link2"></a><a name="ole_link1"></a>参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|productToken|String|只读|设备在开发者门户注册的产品类型Token|Y|
|deviceType|String|只读|家庭终端的产品类型，数字家庭合作伙伴门户申请|Y|
|deviceVendor|String|只读|智能组网终端的设备厂商编码，数字家庭合作伙伴门户申请|Y|
|deviceModel|String|只读|智能组网终端的产品型号编码，数字家庭合作伙伴门户申请|Y|
|deviceSn|String|只读|智能组网终端的序列号，序列号格式要求见《中国移动家庭智能组网终端技术规范》|Y|
|ponSn|String|只读|PON序列号，光网关终端必选，按网关技术规范要求生成|N|
|cmei|String|只读|智能组网终端唯一标识|Y|
|softwareVersion|String|只读|智能组网固件版本|Y|
|hardwareVersion|String|只读|智能组网硬件版本|Y|
|ahsapdVersion|String|只读|ahsapd版本|Y|
|authId|String|只读|设备认证唯一标识，数字家庭合作伙伴门户申请,一机一密必选|Y|
|devKey|String|只读|设备密钥，一机一密必选|Y|
|dmKey|String|只读|终端公司key(可选,预留接口)|Y|
|powerSupplyMode|Number（uint32）|只读|<p>供电方式（枚举值）：</p><p>1：电池供电</p><p>2：POE供电</p><p>3: 市电供电</p><p>4：USB供电</p><p>5：其它供电</p>|Y|
|provinceCode|String|只读|设备预置省份码|Y|
|captureEth|String|只读|设备网口名，用于数据抓包监测|Y|
|deviceMac|String|只读|智能组网终端Mac地址，格式大写去掉冒号|Y|
|ipAddress|String|只读|智能组网终端ip，非ip终端则为空|Y|
|ipv6IPAddress|String|只读|智能组网终端IPv6地址|Y|
|meshType|Number(uint32)|只读|<p>0：非mesh终端</p><p>1：easyMesh，</p><p>2：其它mesh</p>|Y|
|meshRole|Number(uint32)|只读|<p>0：非mesh终端</p><p>1：controller</p><p>2：agent</p><p>3：AC</p><p>4:  AP</p>|Y|
|uplinkType|String|只读|上行接入方式：WLAN、Ethernet、PLC、Cable、Optical Ethernet、GPON、XGPON、XGSPON、PoE|Y|
|workingMode|Number(int32)|只读|<p>设备工作模式，</p><p>0: 桥接模式</p><p>1：路由模式</p><p>2：中继模式</p>|Y|
|radio5|Number(int32)|只读|<p>支持频段：</p><p>0：单频</p><p>1：双频</p><p>2：三频</p><p>3：无WI-FI终端</p>|Y|
|pppoeUser|String|只读|pppoe宽带账号,pppoe拨号是必选，非拨号时值为空|Y|
|wifiSpecification|Number(int32)|只读|<p>wifi的无线规格采用枚举值</p><p>1：AC1200（空间流：2.4G：2 x 2， 5G： 2 x 2）</p><p>2：AC1900（空间流：2.4G：3 x 3， 5G： 3 x 3）</p><p>3：AC2100 （空间流：2.4G：2 x 2， 5G： 4 x4）</p><p>4：AC2100 （空间流：2.4G：2 x 2， 5G： 2 x2）</p><p>5：AC2600 （空间流：2.4G：4 x 4， 5G： 4 x4）</p><p>6：AX1500 （空间流：2.4G：2 x 2， 5G： 2 x2）</p><p>7：AX1800 （空间流：2.4G：2 x 2， 5G： 2 x2）</p><p>8：AX3000 （空间流：2.4G：2 x 2， 5G： 2 x2）</p><p>9:  AX3000 （空间流：2.4G：2 x 2， 5G:4 x 4）</p><p>10: AX3600 （空间流：2.4G：4 x 4， 5G： 4 x 4）</p><p>11: AX5400 （空间流：2.4G：2 x 2， 5G： 4 x4）</p><p>12: AX6000（空间流：2.4G：4 x 4， 5G： 4 x4）</p>|Y|
|reserve|String|只读|厂商特殊标识字段,预留可选|N|


# <a name="_toc106289317"></a>2.4.2 组网设备硬件信息
插件调用接口：

int ahsapi\_get\_hardware\_info(cJSON \*\* hardWareInfo) //获取组网设备硬件信息

hardwareInfo为JSON字符串，包含参数内容如下表

获取设备硬件信息对象方法定义如表2.4.2-1所示：

`					`表2.4.2 -1设备硬件信息对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|ahsapd. hardware|getHardware|无|||

|<p>{</p><p>"ahsapd. hardware": {</p><p>"cpuType": "",</p><p>"flashSize":  ,</p><p>"ramSize":  ,</p><p>"cpuOccupancyRate": ,</p><p>"ramOccupancyRate": </p><p>}</p><p>}</p>||1\.0|
| - | - | - |

|||||获取组网设备硬件信息|
| :- | - | - | - | :- |

获取设备硬件信息返回值参数详细定义如下表：

表2.4.2 – 2 设备硬件信息返回值参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|cpuType|String|只读|智能组网终端CPU型号|Y|
|flashSize|Number(uint32)|只读|Flash大小（单位：Mbytes）|Y|
|ramSize|Number(uint32)|只读|Ram大小（单位：Mbytes）|Y|
|cpuOccupancyRate|Number(uint32)|只读|0-100（CPU占用率）|Y|
|ramOccupancyRate|Number(uint32)|只读|0-100（RAM占用率）|Y|


# <a name="_toc106289318"></a>2.4.3 组网设备端口信息
插件调用接口：

int ahsapi\_get\_port\_info(cJSON \*\* portInfo) //获取组网设备端口信息

int ahsapi\_get\_lan\_info(cJSON \*\* lanInfo) //获取组网设备LAN侧地址信息

int ahsapi\_set\_lan\_config(cJSON \* lanConfig) //配置组网设备LAN侧地址信息

int ahsapi\_set\_lan\_bind\_sta\_config(cJSON \*lanStaConfig) //配置组网设备LAN侧绑定STA信息

portInfo为JSON字符串，包含参数内容如下表

获取设备端口信息对象方法定义如表2.4.3 - 1所示：

表2.4.3 -1设备端口信息对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|ahsapd. port|getPortInfo|无|||

|<p><a name="ole_link18"></a>{</p><p>"ahsapd. port":{</p><p>` `"portInfoList"：[{</p><p>"index": 1 ,</p><p>"portType": 0 ,</p><p>"portRate": "XXX" ,</p><p>"portMac": "XXX",</p><p>"portConnection":X</p><p>}，</p><p>{</p><p>"index": 2 ,</p><p>"portType": 1 ,</p><p>"portRate": "XXX" ,</p><p>"portMac": "XXX" ,</p><p>"portConnection": X</p><p>}</p><p>]</p><p>}</p><p>}</p>||1\.0|
| - | - | - |

|||||获取组网设备端口信息|
| - | - | - | - | :- |
||getLanInfo|无|<p>{</p><p>"ahsapd. port":{</p><p>"lanInfo"：{</p><p>"lanIpAddress":"************" ,</p><p>"lanMask":"*************"  ,</p><p>"lanAddressStart": "XXX" ,</p><p>"lanAddressEnd": "XXX",</p><p>"<a name="ole_link20"></a>leaseTime": 1000，</p><p>"staDevices": [{</p><p>"ipAddress": " " ,</p><p>"macAddress":" ",</p><p>} ，</p><p>{</p><p>"ipAddress": " " ,</p><p>"macAddress":" ",</p><p>}</p><p>]</p><p>}</p><p>}</p>|获取LAN侧配置信息|
||setLanConfig|<p>{</p><p>"lanIpAddress": 1 ,</p><p>"lanMask": 0 ,</p><p>"lanAddressStart": "XXX" ,</p><p>"lanAddressEnd": "XXX",</p><p>"leaseTime":1000</p><p>}</p>|无|配置LAN侧地址|
||setLanStaConfig|<p>{</p><p>"action":1，</p><p><a name="ole_link19"></a>"staDevices": [</p><p>{</p><p>"ipAddress": " " ,</p><p>"macAddress":" ",</p><p>} ，</p><p>{</p><p>"ipAddress": " " ,</p><p>"macAddress":" ",</p><p>}]</p><p>}</p>|无|配置LAN侧绑定下挂设备列表信息|

获取组网设备端口返回值详细定义如表2.4.3-2所示：

`					`表2.4.3- 2 设备端口返回值参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|portInfoList|Array of  Object|只读|端口号信息的数组|Y|
|index|Number(uint32)|只读|端口index,从1开始|Y|
|portType|Number(uint32)|只读|端口类型:0 wan, 1 lan|Y|
|portRate|String|只读|端口协商速率（单位Mbps）|Y|
|portMac|String|只读|组网设备端口mac，格式大写去掉冒号|Y|
|portConnection|Number(uint32)|只读|端口连接情况: 0已连接 1未连接|Y|
|lanInfo|Object|只读|Lan 侧端口信息，包含lan地址信息和绑定下挂设备信息|Y|
|lanIpAddress|String|读写|Lan 侧IP地址|Y|
|lanMask|String|读写|Lan侧子网掩码|Y|
|lanAddressStart|String|读写|Lan侧DHCP分配起始地址|Y|
|lanAddressEnd|String|读写|Lan侧DHCP分配结束地址|Y|
|leaseTime|Number(uint64)|读写|租约时间|Y|
|action|Number(uint32)|只写|<p>Lan侧绑定下挂设备操作，枚举值：</p><p>1：添加</p><p>2：修改</p><p>3：删除</p>|Y|
|staDevices|Array of Object|读写|Lan侧绑定下挂设备信息列表|N|
|ipAddress|String|读写|下挂设备的IPAddress；该字段在下挂设备数非零时必选|N|
|macAddress|String|读写|下挂设备的MAC；该字段在下挂设备数非零时必选|N|


# <a name="_toc106289319"></a>2.4.4 组网设备上行信息
插件调用接口：

int ahsapi\_get\_uplink\_info(cJSON \*\*uplinkInfo)     //获取组网设备上行信息

int ahsapi\_set\_pppoe(char \*user, char \*pwd)        //配置pppoe账号密码

int ahsapi\_set\_wlan\_connect(char \*ssid, char \*pwd,char \* securityMode)  //配置wlan上行时接入ssid和密码

int ahsapi\_set\_work\_mode(int mode，int type)     //配置组网设备工作模式和上网类型

int ahsapi\_set\_uplink\_rate\_config（char\* ifType ,char \*reportInterval ,char \* sampleInterval）//配置上行口平台接口、周期和采样时间

uplinkInfo为JSON字符串，包含参数内容如下表

获取组网设备上行状态信息对象方法定义如表2.4.4-1所示：	

表2.4.4 -1设备上行状态信息对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|<p>ahsapd.uplink</p><p></p><p></p>|getUplinkInfo|无|||

|<p>{</p><p>"ahsapd.uplink": {</p><p>"uplinkType": "",</p><p>"uplinkMac": " ",</p><p>"gatewayMac": " ",</p><p>"workingMode": X,</p><p>"internetType": ,</p><p>"pppoeUser": "",</p><p>"pppoePwd": "",</p><p>"runningTime": "",</p><p>"radio": "",</p><p>"ssid": "",</p><p>"channel": "",</p><p>"noise":"",</p><p>"snr": "",</p><p>"rssi": "",</p><p>"fiberStatus": 5,</p><p>"fiberTXPower":" ",</p><p>"fiberRXPower":" "，</p><p>"transceiverTemperature":256 ,</p><p>"supplyVottage":10,</p><p>"biasCurrent":10 ,</p><p>"distance":1 ,</p><p>"rxRate": "",</p><p>"txRate":"",</p><p>"rxRate\_rt": "",</p><p>"txRate\_rt": "",</p><p>"totalBytesSent": ,</p><p>"totalBytesReceived": ,</p><p>"totalPacketsSent": ,</p><p>"totalPacketsReceived": ,</p><p>"errorsSent": ,</p><p>"errorsReceived": ,</p><p>"discardPacketsSent": ,</p><p>"discardPacketsReceived": ,</p><p>"ipv6ConnStatus": "",</p><p>"ipv6IPAddressOrigin ": "",</p><p>"ipv6IPAddress": "",</p><p>"ipv6DNSServers":"",</p><p>"ipv6PrefixDelegationEnabled": 1,</p><p>"ipv6PrefixOrigin": "",</p><p>"ipv6Prefix": "",</p><p>"ipv6PrefixPltime":,</p><p>"ipv6PrefixVltime": ,</p><p>"defaultIPv6Gateway": "",</p><p>"dsliteEnable": 1，</p><p>"rateArray": [</p><p>{</p><p>"interface": " " ,</p><p>"averRxRate": "",</p><p>"averTxRate":"",</p><p>"maxRxRate": "",</p><p>"maxTxRate": ""</p><p>}</p><p>]</p><p>}</p><p>}</p>||1\.0|
| - | - | - |

|||||获取组网设备上行状态信息|
| - | - | - | - | :- |
||<a name="ole_link3"></a><a name="ole_link4"></a>setPppoe|<p>{</p><p>"pppoeUser": "",</p><p>"pppoePwd": ""</p><p>}</p>|无|配置pppoe账号密码|
||wlanConnect|<p>{</p><p>"ssid":" ",</p><p>"pwd":" "，</p><p>"securityMode":" "}</p>|无|配置WLAN上联时组网设备接入的SSID名称和密码|
||setWorkingMode|<p>{</p><p>"workingMode ":1 </p><p>"<a name="ole_link16"></a><a name="ole_link15"></a>internetType"：0</p><p>}</p>|无|配置组网设备工作模式和上网方法|
||<a name="_hlk67674871"></a>setUplinkRateConfig|<p>{</p><p>"interface":"IF6",</p><p>"<a name="_hlk67674991"></a>reportInterval":"",</p><p>"<a name="_hlk67675022"></a>sampleInterval":""</p><p>}</p>|无|配置上行网口速率运算所需周期和采样时间|

获取组网设备上行状态返回值详细定义如表2.4.4-2所示：

`					`表2.4.4 - 2设备上行状态返回值参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|uplinkType|String|只读|<p>当前的上行接入方式：</p><p>WLAN、Ethernet、PLC、Cable、Optical Etbernet 、GPON、XGPON、XGSPON、PoE</p>|Y|
|<a name="ole_link14"></a><a name="ole_link13"></a>uplinkMac|String|只读|设备上行mac，格式大写去掉冒号|Y|
|gatewayMac|String|只读|设备上行网元（网关）mac, 格式大写去掉冒号, 非PPPoE拨号时必选|Y|
|workingMode|Number(uint32)|读写|<p>AP工作模式：</p><p>0：桥接，默认</p><p>1：路由</p><p>2：中继</p>|Y|
|internetType|Number(uint32)|读写|<p>AP上网方法：</p><p>0: dhcp (默认)</p><p>1: pppoe</p><p>2: static</p>|Y|
|pppoeUser|String|读写|pppoe宽带账号,pppoe拨号是必选，非拨号时值为空|N|
|pppoePwd|String|读写|pppoe密码 |N|
|runningTime|String|只读|设备运行时长，时间单位S(秒)|Y|
|radio|String|只读|WLAN上联时使用的频段，取值2.4G或5G；设备工作于WLAN上联时，该字段必选；|N|
|ssid|String|读写|WLAN上联时接入的SSID名称；设备工作于WLAN上联时，该字段必选；|N|
|pwd|String|读写|WLAN上联时接入的SSID的密码，不应显示此参数|N|
|securityMode|String|只写|<p>认证加密模式，枚举取值：</p><p>None</p><p>WEP-64</p><p>WEP-128</p><p>WPA-Personal</p><p>WPA2-Personal</p><p>MIXED-WPAPSK2</p><p>WPA3-SAE</p><p>MIXED-WPA2WPA3</p>|N|
|channel|String|只读|WLAN上联时的工作信道号，如存在多信道，引用“,”逗号分隔，例：“1 ,6”；设备工作于WLAN上联时，该字段必选；|N|
|noise|String|只读|WLAN上联时的噪声强度，单位dBm；设备工作于WLAN上联时，该字段必选；|N|
|snr|String|只读|WLAN上联时的信噪比，单位dB；设备工作于WLAN上联时，该字段必选；|N|
|rssi|String|只读|WLAN上联时的上联信号强度，单位为dBm；设备工作于WLAN上联时，该字段必选；|N|
|fiberStatus|Number(uint32)|只读|<p>光口连接状态，采用枚举取值：</p><p>0：Up；</p><p>1：Initializing</p><p>2：EstablishingLink</p><p>3：NoSignal</p><p>4：Error</p><p>5：Disabled</p>|N|
|fiberTXPower|String|只读|光口上联时发射光功率，单位dBm；上行光接入时必选；|N|
|fiberRXPower|String|只读|光口上联时接收光功率，单位dBm；上行光接入时必选；|N|
|transceiverTemperature|Number（int32）|只读|光模块的工作温度，单位：1/256摄氏度；|N|
|supplyVottage|Number（uint64）|只读|光模块的供电电压，单位：100毫伏；|N|
|<a name="ole_link27"></a>biasCurrent|Number(uint64)|只读|光发送机的偏置电流，单位：2微安；|N|
|distance|Number (uint64)|只读|<p>对于光网关，该值为光网关和OLT之间的距离；单位：米；</p><p>对于关路由，该值为光路由与光网关之间的距离，单位：米；由光路由参考ITU规范的测距算法得出；</p><p>说明：该字段仅对P2MP的光路由做要求，P2P接入的光路由该值可为空；</p>|N|
|rxRate|String|只读|上行协商接收速率，单位Mbps|Y|
|txRate |String|只读|上行协商发送速率，单位Mbps|Y|
|rxRate\_rt|String|只读|上行实时接收速率(Mbps)|Y|
|txRate\_rt|String|只读|上行实时发送速率(Mbps)|Y|
|totalBytesSent|Number(uint64)|只读|上行总发送字节数|Y|
|totalBytesReceived|Number(uint64)|只读|上行总接收字节数|Y|
|totalPacketsSent|Number(uint64)|只读|上行总发送包数|Y|
|totalPacketsReceived|Number(uint64)|只读|上行总接收包数|Y|
|errorsSent|Number(uint64)|只读|上行发送错误包数|Y|
|errorsReceived|Number(uint64)|只读|上行接收错误包数|Y|
|discardPacketsSent|Number(uint64)|只读|上行发送丢失的包数|Y|
|discardPacketsReceived|Number(uint64)|只读|上行接收丢失的包数|Y|
|ipv6ConnStatus|String|只读|<p>IPv6协议的连接状态，枚举取值： Connected</p><p>Unconnected </p>|Y|
|ipv6IPAddressOrigin|String|只读|<p>IPv6地址分配机制，枚举取值：</p><p>AutoConfigured</p><p>DHCPv6 </p><p>Static </p>|Y|
|ipv6IPAddress|String|只读|WAN连接的IPv6地址；|Y|
|ipv6DNSServers|String|只读|DNS Server地址。|Y|
|ipv6PrefixDelegationEnabled|Number(uint32)|只读|<p>Prefix Delegation使能状态；</p><p>1:表示启用，0:表示禁用。</p>|Y|
|ipv6PrefixOrigin|String|只读|<p>前缀地址分配机制，枚举取值： </p><p>PrefixDelegation</p><p>Static  </p>|Y|
|ipv6Prefix|String|只读|前缀地址。|Y|
|ipv6PrefixPltime|Number(uint32)|只读|公告前缀的preferred lifetime，单位：秒 ；|Y|
|ipv6PrefixVltime|Number(uint32)|只读|公告前缀的valid lifetime，单位：秒 ；|Y|
|defaultIPv6Gateway|String|只读|<p>IPv6默认网关 ；</p><p></p>|Y|
|dsliteEnable|Number(uint32)|只读|<p>是否启用DS-lite功能。</p><p>1: TRUE表示启用，0: FALSE表示禁用。 </p>|Y|
|reportInterval|String|读写|周期时间，单位s(秒)，周期时间 用于averRxRate、averTxRate、maxRxRate和maxTxRate运算,当周期值为0 时停止速率统计|Y|
|sampleInterval|String|读写|数据采样间隔，单位s(秒) , 采样间隔只用于统计收发速率信息，用于计算maxRxRate和maxTxRate运算，当周期值和采样值都为0时停止速率统计|Y|
|rateArray|Array of Object|只读|上行速率数组：包含interface; averRxRate;averTxRate; maxRxRate; maxTxRate 5个字段|Y|
|interface|String|读写|配置平台接口：值为字符串：IF5；IF6；IF8|Y|
|averRxRate|String|只读|<p>下挂设备接收流量周期均值，单位Mbps</p><p>该字段在下挂设备数非零时必选</p>|Y|
|averTxRate|String|只读|上行发送流量周期均值，单位Mbps|Y|
|maxRxRate|String|只读|上行发送流量周期最大值，单位Mbps|Y|
|maxTxRate|String|只读|上行发送流量周期最大值，单位Mbps|Y|

# <a name="_toc106289320"></a>2.4.5 组网设备下挂设备信息
插件调用接口：

int ahsapi\_get\_sta\_info(cJSON \*\* staInfo)  //获取下挂设备列表信息

int ahsapi\_get\_sta\_num(cJSON \*\* staNum) //获取下挂设备数量信息

int ahsapi\_set\_sta\_rate\_config（char\* ifType,char \*reportInterval , char \*sampleInterval）//配置下挂设备平台接口、周期和采样时间

staInfo、staNum为JSON字符串，包含参数内容如下表

获取组网设备下挂设备信息对象方法定义如表2.4.5-1所示：	

表2.4.5 - 1下挂设备信息对象方法 

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|ahsapd. sta|<a name="ole_link5"></a><a name="ole_link6"></a>getStaInfo|无|||

|<p>{</p><p>"ahsapd. sta": {</p><p>"staDevices": [</p><p>{</p><p>"ipAddress": " " ,</p><p>"<a name="ole_link26"></a>staIPv6IPAddress": " ",</p><p>"hostName":" ",</p><p>"staVendor":" ",</p><p>"macAddress": " " ,</p><p>"vmacAddress": " ",</p><p>"staType":" ",</p><p>"upTime": " " ,</p><p>"accessTime": " " ,</p><p>"radio": " ",</p><p>"ssid":" ",</p><p>"rssi": " " ,</p><p>"channel": "",</p><p>"rxRate": " ",</p><p>"txRate":" ",</p><p>"rxRate\_rt": " ",</p><p>"txRate\_rt":" ",</p><p>"totalBytesSent":1000,</p><p>"totalBytesReceived":1000,</p><p>"totalPacketsSent ":1000,</p><p>"totalPacketsReceived ":1000，</p><p>"rateArray": [</p><p>{</p><p>"interface": " " ,</p><p>"averRxRate": "",</p><p>"averTxRate":"",</p><p>"maxRxRate": "",</p><p>"maxTxRate": ""</p><p>}</p><p>]</p><p>}</p><p>]</p><p>}</p>||1\.0|
| - | - | - |

|||||获取组网设备下挂设备列表|
| - | - | - | - | :- |
||getStaNum|无|<p>{</p><p>"ahsapd. sta": {</p><p>"wire": X ,</p><p>"2.4G": X ,</p><p>"5G":  X</p><p>}，</p><p>}</p>|获取下挂设备数量|
||setStaRateConfig|<p>{</p><p>"interface":"IF6",</p><p>"reportInterval":"",</p><p>"sampleInterval":""</p><p>}</p>|无|配置下挂设备运算上下行平均速率和最大速率所需要周期和采样时间|

下挂设备信息变化主动event上报定义如下表2.4.5 -2 所示

表2.4.5 - 2下挂设备信息主动上报消息定义 

|消息类型|消息内容|描述|是否必须|
| :-: | :-: | :-: | :-: |
|staUpDown|<p>{</p><p>"hostName":" ",</p><p>"staVendor":" ",</p><p>"macAddress": " " ,</p><p>"vmacAddress": " ",</p><p>"staType":" ",</p><p>` `accessTime": " " ,</p><p>"radio":" "，</p><p>"rssi": " " ,</p><p>"rxRate": " ",</p><p>"txRate":" ",</p><p>"online": 1</p><p>}</p>|下挂设备上下线信息通知,事件触发条件下挂设备发生上线和下线时|<p></p><p>Y</p>|

获取组网设备下挂设备列表返回值参数详细定义如表2.4.5-3所示：

`					`表2.4.5 -3下挂设备列表信息返回值参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|staDevices|Array of Object|只读|连接的下挂设备信息数组，下挂设备数非零时必选|Y|
|ipAddress|String|只读|下挂设备的IPAddress；该字段在下挂设备数非零时必选|Y|
|staIPv6IPAddress|String|只读|下挂设备的IPV6地址；该字段在下挂设备数非零，且支持IPv6时必选|N|
|hostName|String|只读|下挂设备的HostName；该字段在下挂设备数非零时必选|Y|
|staVendor|String|只读|下挂设备归属厂商信息|N|
|macAddress|String|只读|<p>下挂设备MAC地址，格式为不带冒号且大写；</p><p>该字段在下挂设备数非零时必选</p>|Y|
|vmacAddress|String|只读|下挂设备在智能组网终端下的虚拟MAC，如果未转换，则和真实MAC一致。格式为不带冒号且大写；该字段在下挂设备数非零时必选|Y|
|staType|String|只读|下挂设备类型（PC、Phone、TV、Router、IoT、IPC、others）；该字段在下挂设备数非零时必选|Y|
|upTime|String|只读|已连接时间，单位：S(秒)；该字段在下挂设备数非零时必选|Y|
|accessTime|String|只读|下挂设备接入时间，时间格式：2021-01-07 16:50:30 |Y|
|online|Number(uint32)|只读|<p>1 : 下挂设备上线(在线)</p><p>0：下挂设备下线（不在线）</p>|Y|
|radio|String|只读|<p>WLAN下挂设备的接入频段，取值2.4G或5G，5G-2，对非WLAN下挂设备应填空值；该字段在下挂设备数非零时必选</p><p>注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；</p>|N|
|ssid|String|只读|<p>WLAN下挂设备的接入SSID名称，非WLAN下挂设备应填空值；</p><p>该字段在下挂设备数非零时必选</p>|N|
|rssi|String|只读|WLAN下挂设备当前的信号强度，单位为dBm，对非WLAN下挂设备应填空值；该字段在下挂设备数非零时必选|N|
|channel|String|只读|WLAN下挂设备当前的工作信道，单对非WLAN下挂设备应填空值；该字段在下挂设备数非零时必选|N|
|rxRate|String|只读|下挂设备协商接收速率；该字段在下挂设备数非零时必选(Mbps)|Y|
|txRate|String|只读|下挂设备协商发送速率；该字段在下挂设备数非零时必选(Mbps)|Y|
|rxRate\_rt|String|只读|<p>实时接收速率(Mbps)；</p><p>该字段在下挂设备数非零时必选；</p>|` `Y|
|txRate\_rt|String|只读|<p>实时发送速率(Mbps) ；</p><p>该字段在下挂设备数非零时必选；</p>|Y|
|totalBytesSent|Number(uint64)|只读|下挂设备总发送字节数|Y|
|totalBytesReceived|Number(uint64)|只读|下挂设备总接收字节数|Y|
|totalPacketsSent|Number(uint64)|只读|下挂设备总发送包数|Y|
|totalPacketsReceived|Number(uint64)|只读|下挂设备总接收包数|Y|
|reportInterval|String|读写|周期时间，单位s(秒)，周期时间 用于averRxRate、averTxRate、maxRxRate和maxTxRate运算，当周期值为0 时停止下挂设备速率统计|Y|
|sampleInterval|String|读写|数据采样间隔，单位s(秒) , 采样间隔只用于统计收发速率信息，用于计算maxRxRate和maxTxRate运算，当周期值和采样值都为0时停止速率统计|Y|
|rateArray|Array of Object|只读|速率数组：包含interface; averRxRate;averTxRate; maxRxRate; maxTxRate 5个字段|Y|
|interface|String|读写|配置平台接口：值为字符串：IF5；IF6；IF8|Y|
|averRxRate|String|只读|下挂设备接收流量周期均值，单位Mbps|Y|
|averTxRate|String|只读|下挂设备发送流量周期均值，单位Mbps|Y|
|maxRxRate|String|只读|下挂设备发送流量周期最大值，单位Mbps|Y|
|maxTxRate|String|只读|下挂设备发送流量周期最大值，单位Mbps|Y|

获取组网设备下挂设备数量信息返回值字段详细定义如表2.4.5-4所示：

`					`表2.4.5- 4下挂设备数量信息返回值字段

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|wire|Number(uint32)|只读|下挂设备的有线连接数量|Y|
|2\.4G|Number(uint32)|只读|下挂设备通过wifi 2.4G连接数量|Y|
|5G|Number(uint32)|只读|下挂设备通过wifi 5G 连接数量|Y|


# <a name="_toc106289321"></a>2.4.6 组网设备WIFI配置
插件调用接口：

int ahsapi\_get\_wifi\_info(cJSON \*\* wifiInfo) 	     // 获取wifi配置信息

int ahsapi\_set\_wifi\_parameter(cJSON \* wifiParamter)	 // 配置wifi参数

int ahsapi\_set\_wifi\_switch(char\* radio, int enable，int index)	 // 配置wifi开关

int ahsapi\_set\_radio\_config(cJSON \* radioConfig)    // 配置wifi射频参数

int ahsapi\_set\_wps(char\* radio)                   // 配置wps

int ahsapi\_set\_5GPreferred(char\* preferredSta，int limitTime)  //配置5G频段优先

wifiInfo、wifiParameter为JSON字符串，包含参数内容如下表 

组网设备WiFi配置对象方法定义如表2.4.6 -1所示：	

表2.4.6 - 1设备WIFI配置信息对象方法

|<a name="ole_link17"></a>对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|ahsapd. wifi|getWifiInfo|无|||

|<p>{</p><p>"ahsapd. wifi": {</p><p>"radios": [</p><p>{</p><p>"radio": " " ,</p><p>"enable": " ",</p><p>"transmitPower ":" ",</p><p>"ssidStandard ":"802.11ax ",</p><p>"noiseLevel":" -70",</p><p>"interferencePercent ":80,</p><p><a name="ole_link25"></a>"channel": " " ,</p><p>"bandwidth"：　</p><p>}</p><p>]</p><p>"configurations": [</p><p>{</p><p>"radio":" ",</p><p>"index":  ,</p><p>"enable":  ,</p><p>"ssid":" ",</p><p>"securityMode": " ",</p><p>"encrypt":  ,</p><p>"pwd":" ",</p><p>"wlanMac":" ",</p><p>"maxAssociateNum": ,</p><p>"ssidAdvertisementEnabled":1,</p><p>,</p><p>`  `"ssidStandard ":"802.11a/n""</p><p>}</p><p>]</p><p>}</p>||1\.0|
| - | - | - |

|||||获取组网设备wifi配置信息列表|
| - | - | - | - | :- |
||setWifiParameter|<p>{</p><p>"configurations": [</p><p>{</p><p>"radio":" ",</p><p>"index":  ,</p><p>"enable": ,</p><p>"ssid":" ",</p><p>"securityMode": " ,</p><p>"encrypt":  ,</p><p>"pwd":" ",</p><p>"maxAssociateNum": ,</p><p>"ssidAdvertisementEnabled": 1,</p><p>"ssidStandard ":"802.11a/n"</p><p>}]</p><p>}</p>|无|配置组网设备wifi参数|
||setWifiSwitch|<p>{</p><p>"<a name="_hlk56689650"></a>radio": " " ,</p><p>"enable": ，</p><p>"index": </p><p>}</p>|无|配置组网设备WiFi开关|
||setRadioConfig|<p>{</p><p>"radio": " " ,</p><p>"<a name="_hlk56689724"></a>transmitPower":" ",</p><p>"channel": " " ,</p><p>"bandwidth"：</p><p>}</p>|无|配置组网设备WiFi射频参数|
||setWps|<p>{</p><p>"radio": " " </p><p>}</p>|无|配置组网设备WPS触发频段|
||5GPreferred|<p>{</p><p>"<a name="ole_link32"></a>5GPreferredSta":"" ，</p><p>"limitTime": ，</p><p>}</p>|无|<p>组网设备收到该指令要求时，将指定STA临时限制接入2.4G，限制时间建议为limitTime(单位为毫秒，缺省3000ms)，并引导切换到5GHz频段；指令只执行一次。</p><p></p>|


wifi信息变化主动event上报定义如下表2.4.6 -2 所示

表2.4.6 – 2 wifi信息变化主动上报消息定义 

|消息类型|消息内容|描述|是否必须|
| :-: | :-: | :-: | :-: |
|wifiInfoChange|<p>{</p><p>"configurations": [</p><p>{</p><p>"radio":" ",</p><p>"index":   ,</p><p>"enable":  ,</p><p>"ssid":" ",</p><p>"securityMode": " ",</p><p>"encrypt":  ,</p><p>"pwd":" ",</p><p>"maxAssociateNum": ,</p><p>"ssidAdvertisementEnabled": </p><p>}</p><p>]</p><p>}</p>|Wifi配置信息改变主动通知事件，触发条件当wifi 的ssid和pwd 改变时，主动上报wifi配置发生改变的wifi信息|<p></p><p>Y</p>|


获取和配置组网设备WiFi参数信息、配置WiFi开关、配置WiFi功率、配置WPS触发频段参数详细定义如表2.4.6 - 3所示：

`					`表2.4.6 - 3设备WIFI配置信息参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|radios|Array of Object|只读|<p>WiFi射频信息的数组；</p><p>包含以下字段：Radio，Enable，TransmitPower，ssidStandard,nosieLevel, interferencePercent ,Channel，bandwidth</p>|Y|
|radio|String|读写|<p>Radios数组之一，频段，枚举取值：</p><p>2\.4G</p><p>5G</p><p>5G-2</p><p>注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段</p>|Y|
|enable|Number(uint32)|读写|Radios数组之一，该频段是否启用，1为启用，0为禁用|Y|
|<a name="ole_link8"></a><a name="ole_link7"></a>transmitPower|String|读写|Radios数组之一，WiFi发射功率级别（百分比）|Y|
|ssidStandard|String|只读|<p>支持的无线传输标准，取值802.11a/802.11b/802.11g/802.11n/802.11ac/802.11ax</p><p>802\.11a</p><p>802\.11b</p><p>802\.11b/g</p><p>802\.11b/g/n</p><p>802\.11a/n/ac</p><p>802\.11b/g/n/ax</p><p>802\.11a/n/ac/ax</p><p>802\.11a/n</p><p>802\.11n</p><p>802\.11g</p><p>802\.11g/n等</p>|Y|
|noiseLevel|String|只读|当前信道底噪|Y|
|interferencePercent|Number(uint32)|只读|<p>当前信道干扰信号的占空比(</p><p>当前信道使用率（%），数据范围：0-100；)</p><p>说明：应包含自身数据收发和周边干扰之和；</p>|Y|
|channel|String|读写|Radios数组之一，当前工作信道号,若存在多个信道，可用“,”逗号分隔，例如：“1,6”|Y|
|bandwidth|Number(uint32)|读写|<p>当前Wi-Fi频宽，采用枚举取值：</p><p>0：自动频宽</p><p>1：20MHz</p><p>2：40MHz</p><p>3：80MHz</p><p>4：160MHz</p><p>5：80+80MHz</p><p>说明：对于2.4G频段，自动频宽功能为20/40MHz频宽自适应；对于5G频段，自动频宽功能对应为20/40/80/160MHz频宽自适应</p>|Y|
|configurations|Array of Object|读写|WiFi信息的数组,包含 radio, index, enable, ssid, securityMode, encrypt,pwd, maxAssociateNum, ssidAdvertisementEnabled、wlanMa c|Y|
|radio|String|读写|<p>频段，枚举取值：</p><p>2\.4G</p><p>5G</p><p>5G-2</p><p>注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；</p>|Y|
|index|Number(uint32)|读写|<p>SSID的索引,2.4G:1-4,5G: 5-8,5G-2采用9-12；值为0时对指定radio的全局控制；</p><p>注：双频设备忽略5G-2频段；</p>|Y|
|enable|Number(uint32)|读写|是否启用，1为启用，0为禁用|Y|
|ssid|String|读写|SSID名称|Y|
|securityMode|String|读写|<p>认证加密模式，枚举取值：</p><p>None</p><p>WEP-64</p><p>WEP-128</p><p>WPA-Personal</p><p>WPA2-Personal</p><p>MIXED-WPAPSK2</p><p>WPA3-SAE</p><p>MIXED-WPA2WPA3</p>|Y|
|encrypt|Number(uint32)|`    `读写|<p>加密算法，枚举取值：</p><p>0：None:</p><p>1：AES</p><p>2：TKIP</p><p>3：AES+TKIP</p>|Y|
|pwd|String|读写|WiFi密钥，WEP Key或Pre-shared Key，管理平台不应存储和显示此参数。|Y|
|wlanMac|String|只读|每个SSID对应Mac地址，格式大写去掉冒号|Y|
|maxAssociateNum|Number(uint32)|读写|该SSID最大允许接入的用户数，0表示不限制|Y|
|ssidAdvertisementEnabled|Number(uint32)|读写|是否广播SSID，1为广播，0为不广播|Y|
|ssidStandard|String|读写|<p>支持的无线传输标准，取值</p><p>802\.11a</p><p>802\.11b</p><p>802\.11b/g</p><p>802\.11b/g/n</p><p>802\.11a/n/ac</p><p>802\.11b/g/n/ax</p><p>802\.11a/n/ac/ax</p><p>802\.11a/n</p><p>802\.11n</p><p>802\.11g</p><p>802\.11g/n等</p>|Y|
|5GPreferredSta|String|读写|下挂设备名单列表：“mac地址/设备名”格式；多信息之间以逗号分开，最多支持32组。示例："AABBCCDDEE00/iphone, 112233445566/Mi|Y|
|limitTime|Number(uint32)|读写|指定STA临时限制接入2.4G，限制时间,单位mS（毫秒）缺省值3000|Y|


# <a name="_toc106289322"></a>2.4.7 组网设备WIFI统计信息
插件调用接口：

int ahsapi\_get\_wifi\_stats\_info(cJSON \*\* wifiStatsInfo)  //获取组网设备wifi统计信息

wifiStatsInfo 为JSON字符串，包含参数内容如下表

组网设备WiFi统计信息对象方法定义如表2.4.7-1所示：

表2.4.7 - 1 设备WIFI统计信息对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|ahsapd.wifi.stats|getWifiStatsInfo|无|||

|<p>{</p><p>"ahsapd. wifi.stats": {</p><p>"stats": [</p><p>{</p><p>"radio": " " ,</p><p>"index":  ,</p><p>"ssid":" " ,</p><p>"totalBytesSent":  ,</p><p>"totalBytesReceived": ,</p><p>" totalPacketsSent": ,</p><p>"totalPacketsReceived ": ,</p><p>"errorsSent":  ,</p><p>"errorsReceived": ,</p><p>"discardPacketsSent": ,</p><p>"discardPacketsReceived": </p><p>}</p><p>]</p><p>}</p><p></p>||1\.0|
| - | - | - |

|||||获取组网设备WIFI统计信息|
| - | - | - | - | :- |

获取组网设备WiFi统计信息返回值参数详细定义如表2.4.7 - 2所示：

`					  `表2.4.7 - 2设备WIFI统计信息参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|stats|Array of Object|只读|每个用户侧接口的报文统计数组（WLAN接口基于SSID统计；以太网接口基于端口统计，并按接口序号从小到大的次序依次出现在响应消息中）|Y|
|radio|String|只读|WLAN接口的频段，取值2.4G或5G,5G-2,以太有线接口填 WAN 或 LAN|Y|
|index|Number(uint32)|只读|WLAN接口的SSID索引和以太接口序号2.4G:1- 4, 5G: 5- 8,, 5G-2:9-12;WAN 和LAN :从1开始|Y|
|ssid|String|只读|WLAN接口的SSID名称，非WLAN接口填空值|Y|
|totalBytesSent|Number(uint64)|只读|总发送字节数|Y|
|totalBytesReceived|Number(uint64)|只读|总接收字节数|Y|
|totalPacketsSent|Number(uint64)|只读|总发送包数|Y|
|totalPacketsReceived|Number(uint64)|只读|总接收包数|Y|
|errorsSent|Number(uint64)|只读|发送出错的包数|Y|
|errorsReceived|Number(uint64)|只读|接收的错误包数|Y|
|discardPacketsSent|Number(uint64)|只读|发送时丢弃的包数|Y|
|discardPacketsReceived|Number(uint64)|只读|接收时丢弃的包数|Y|


# <a name="_toc106289323"></a>2.4.8 组网设备周边WIFI信息
插件调用接口：

int ahsapi\_get\_wlan\_neighbor\_info(cJSON \*\* wlanNeighborInfo)  //获取组网设备周边wifi信息

wlanNeighborInfo为JSON字符串，包含参数内容如下表

组网设备周边WiFi信息对象方法定义如表2.4.8-1所示：

表2.4.8 - 1 设备周边WIFI信息对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|ahsapd. neighbor|getWlanNeighborInfo|无|||

|<p>{</p><p>"ahsapd.neighbor": {</p><p>"number":  ,</p><p>" radios": [</p><p>{</p><p>"radio": " " ,</p><p>"ssid":" " ,</p><p>"macAddress": " " ,</p><p>"channel": " ",</p><p>"rssi": " ",</p><p>"bandwidth":  ,</p><p>"wifiStandard":  ,</p><p>"securityMode":"MIXED-WPAPSK2" </p><p>}</p><p>]</p><p>}</p><p>}</p>||1\.0|
| - | - | - |

|||||获取组网设备周边WIFI信息|
| - | - | - | - | :- |


获取组网设备周边WiFi信息返回值参数详细定义如表2.4.8 - 2所示：

`					  `表2.4.8 - 2设备周边WIFI信息参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|number|Number|`     `只读|周边WIFI数量，当值为0时，WIFI信息数组为空|Y|
|radios|Array of Object|只读|周边WIFI信息数组，周边WIFI数量为非零时必选|Y|
|radio|String|只读|频段：2.4G、5G；该字段在周边WIFI数量非零时必选|Y|
|ssid|String|只读|周边wifi的SSID名称；该字段在周边WIFI数量非零时必选|Y|
|macAddress|String|只读|<p>周边WiFi 的MAC地址，格式为不带冒号并且大写；</p><p>该字段在周边WIFI数量非零时必选</p>|Y|
|channel|String|只读|<p>周边WiFi信道, 如存在多信道，引用“,”逗号分隔，例：“1 ,6”；</p><p>该字段在周边WIFI数量非零时必选</p>|Y|
|rssi|String|只读|周边wifi的弱信号值；该字段在周边WIFI数量非零时必选|Y|
|bandwidth|Number|只读|<p>周边Wi-Fi频宽，采用枚举取值：周边Wi-Fi数量非零时必选；</p><p><a name="_hlk84694843"></a>1：20MHz</p><p>2：40MHz</p><p>3：80MHz</p><p>4：160MHz</p><p>5：80+80MHz</p>|Y|
|wifiStandard|Number|只读|<p>周边Wi-Fi使用的无线标准，采用枚举取值：周边Wi-Fi数量非零时必选（取最高标准上报，低于802.11g的报0）；</p><p>0：其它</p><p>1：802.11g</p><p>2：802.11n</p><p>3：802.11ac</p><p>4：802.11ax</p><p>0：802.11a或802.11b</p><p>1：802.11b/g</p><p>2：802.11b/g/n</p><p>3：802.11a/n/ac</p><p>4：802.11b/g/n/ax</p><p>5：802.11a/n/ac/ax</p><p>6：802.11a/n</p><p>7：802.11n</p><p>8:  802.11g</p><p>9:  802.11g/n</p><p></p>|Y|
|securityMode|String|只读|<p>认证加密模式，枚举取值：</p><p>None</p><p>WEP-64</p><p>WEP-128</p><p>WPA-Personal</p><p>WPA2-Personal</p><p>MIXED-WPAPSK2</p><p>WPA3-SAE</p><p>MIXED-WPA2WPA3</p>|Y|

# <a name="_toc106289324"></a>2.4.9 组网设备漫游配置
插件调用接口：

int ahsapi\_get\_roaming\_config(cJSON \*\* roamingConfig) // 获取组网设备漫游配置信息

int ahsapi\_set\_roaming\_config(cJSON\* roamingConfig) // 配置漫游参数

int ahsapi\_sta\_disconnect(char\* mac，int dismissTime)  // 断开STA连接   

int ahsapi\_sta\_rssi(char\* mac，cJSON \*\* staRssi)     // 查询STA 弱信号

int ahsapi\_sta\_stop\_detect(char\* mac,long long stopDetectTime) // 停止STA 漫游策略 

int ahsapi\_set\_band\_steering\_config(cJSON \* bandSteeringConfig) //配置双频合一

int ahsapi\_get\_band\_steering\_config(cJSON \*\* bandSteeringConfig) //获取双频合一配置

roamingConfig和bandSteeringConfig为JSON字符串，包含参数内容如下表

组网设备漫游配置对象方法定义如表2.4.9-1所示：

表2.4.9 - 1 设备漫游配置信息对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|ahsapd. roaming|getRoamingConfig|无|||

|<p>{</p><p>"ahsapd.roaming": {</p><p>"roamingSwitch":  ,</p><p>"lowRSSI2.4G": ,</p><p>"lowRSSI5G": ,</p><p>"packetLoss": ,</p><p>"retryRatio": ,</p><p>"lowRSSIPeriod": ,</p><p>"detectPeriod": </p><p>}</p><p>}</p>||1\.0|
| :- | - | - |

|||||获取组网设备漫游配置信息|
| - | - | - | - | :- |
||setRoamingConfig|<p>{</p><p>"seqId":"XXXX",</p><p>"<a name="ole_link9"></a><a name="ole_link10"></a>roamingSwitch":  ,</p><p>"lowRSSI2.4G": ,</p><p>"lowRSSI5G": ,</p><p>"packetLoss": ,</p><p>"retryRatio": ,</p><p>"lowRSSIPeriod": ,</p><p>"detectPeriod": </p><p>}</p>|无|配置组网设备漫游参数|
||staDisconnect|<p>{</p><p>"macAddress":" "，</p><p>"dismissTime":5000</p><p>}</p>|无|组网设备断开与该STA的连接，并限制接入dismissTime毫秒|
||staRssi|<p>{</p><p>"macAddress":" "</p><p>}</p>|<p>{</p><p>"ahsapd.roaming": {</p><p>"radio": " " ,</p><p>"rssi": " ",</p><p>"channel": " "</p><p>}</p><p>}</p><p></p>|<p>查询组网设备与STA的弱信号；</p><p>优先监测并上报5GHz频段信号强度</p>|
||staStopDetect|<p>{</p><p>"macAddress":" "，</p><p>"stopDetectTime":1000</p><p>}</p>|无|<p>停止该STA漫游策略；</p><p>暂停该STA的信号质量监测和通过roamingDetect告警消息上报，stopDetectTime字段为空或者值为0 一直停止漫游策略；stopDetectTime值大于0，停止策略时间单位为秒（S）;</p><p>组网设备重启恢复监测和上报。</p>|
||setBandSteeringConfig|<p>{</p><p>"bandSteeringStatus":1，</p><p>"rssiThreshold": -60 ,</p><p>"rssiThreshold5G":  -40 </p><p>}</p>|无|设置双频合一|
||getBandSteeringConfig|无|<p>"ahsapd.roaming": {</p><p>"bandSteeringStatus":1，</p><p>"rssiThreshold": -60 ,</p><p>"rssiThreshold5G": -40 </p><p>}</p>|获取双频合一配置|

组网设备漫游告警信号定义如表2.4.9 - 2所示：

表2.4.9 - 2 漫游告警信号定义

|消息类型|消息内容|描述|是否必须|
| :-: | :-: | :-: | :-: |
|roamingDetect|<p>{</p><p>"seqId":"XXXX",</p><p>"macAddress":" ",</p><p>"radio": " " ,</p><p>"rssi": " ",</p><p>"rxRate":" ",</p><p>"txRate":" ",</p><p>"channel":" ",</p><p>"frequencyWidth":" ",</p><p>"packetLoss":  ,</p><p>"retryRatio": </p><p>}</p>|下挂设备信号质量超过阈值，主动告警|Y|

组网设备漫游配置参数详细定义如表2.4.9-3所示：

表2.4.9- 3 设备漫游配置信息参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :- | :-: | :-: | :-: |
|roamingSwitch|Number(uint32)|读写|<p>是否启用WiFi漫游控制：</p><p>0: 关闭</p><p>1: 启用</p>|Y|
|lowRSSI2.4G|Number(int32)|读写|<p>弱信号阈值（2.4G），单位dBm。</p><p>当STA接入当前智能组网终端的RSSI小于该阈值时，智能组网终端需对该STA执行漫游切换控制。</p>|Y|
|lowRSSI5G|Number(int32)|读写|<p>弱信号阈值（5G），单位dBm。</p><p>当STA接入当前智能组网终端的RSSI小于该阈值时，智能组网终端需对该STA执行漫游切换控制。</p>|Y|
|packetLoss|Number(uint32)|` `读写|丢包比率 |N|
|retryRatio|Number(uint32)|读写|重传比率 |N|
|lowRSSIPeriod|Number(uint32)|读写|<p>触发弱信号上报时延，单位：毫秒，缺省值为20000；</p><p>举例：对于连接5G频段的STA，组网终端监测STA的RSSI强度持续低于lowRSSI5G的时间超过20秒时，发起漫游告警roamingDetect，同步该STA网络质量； </p>|N|
|detectPeriod |Number(uint32)|读写|基于事件触发时两次上报间隔不小于该时长，基于周期检测时使用该周期，单位毫秒|N|
|dismissTime |Number(int32)|读写|限制接入的最长时间(毫秒) ，缺省5000毫秒|Y|
|stopDetectTime|Number(uint64)|只写|停止该STA漫游策略的时间单位秒（S）；如果值为0或者空值，一直停止至到设备重启；|N|
|macAddress|String|读写|漫游下挂设备MAC地址，格式为不带冒号且大写；|Y|
|radio|String|只读|<p>漫游下挂设备的接入频段，取值2.4G ；5G ；5G-2 </p><p>注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；</p>|Y|
|rssi|String|只读|漫游下挂设备当前的信号强度，单位为dBm |Y|
|channel|String|只读|当前工作信道号,若存在多个信道，可用“,”逗号分隔，例如：“1,6”|Y|
|frequencyWidth|String|只读|频宽，取值如下： 20M/40M/Auto20M40M/Auto20M40M80M(仅针对5GWifi) |Y|
|rxRate|String|只读|当前协商接收速率（Mbps）|Y|
|txRate|String|只读|当前协商发送速率(Mpbs)|Y|
|bandSteeringStatus|Number(uint32)|读写|<p>双频合一开关：</p><p>0：关闭</p><p>1：打开</p>|Y|
|rssiThreshold|Number(int32)|读写|<p>接收信号强度阈值，当2.4G频段检测到某一下挂STA接收到的信号强度高于此阈值，将当前双频STA切换至5G频段。单位：dbm.</p><p>取值范围（-100~0）</p>|N|
|rssiThreshold5G|Number(int32)|读写|<p>5G接收信号强度阈值，当双频客户端首次连接或者当前工作在5G频段时，检测到某一下挂双频STA接收到的信号强度低于此阈值将下挂双频客户端切换至2.4G频段</p><p>单位：dbm.</p><p>取值范围（-100~0）</p>|N|
|seqId|String|读写|配置对应唯一标识,如配置下发携带seqId，event上报必选携带下发的seqId；|N|


# <a name="_toc106289325"></a>2.4.10 组网设备定时任务
插件调用接口：

int ahsapi\_get\_all\_timed\_task(cJSON\*\* allTimedTask)  //获取所有定时任务配置信息

int ahsapi\_add\_timed\_task(cJSON\* timedTask)       //添加和更新定时任务

int ahsapi\_delete\_timed\_task(int taskId)             // 删除定时任务

allTimedTask、timedTask为JSON字符串，包含参数内容如下表

组网设备定时任务对象方法定义如表2.4.10 -1所示：

表2.4.10 - 1 设备定时任务对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|ahsapd. timedtask|getTimedTask|无|||

|<p>{</p><p>"ahsapd.timedtask": {</p><p>"tasks": [</p><p>{</p><p>"taskId":  ,</p><p>"timeOffset":  ,</p><p>"week":  ,</p><p>"enable":  ,</p><p>"action": ,</p><p>"timeOffset2":  ,</p><p>"index": </p><p>}</p><p>]</p><p>}</p><p></p>||1\.0|
| :- | - | - |

|||||获取组网设备定时任务所有配置信息|
| - | - | - | - | :- |
||addTimedTask|<p>{</p><p>"taskId":  ,</p><p>"timeOffset":  ,</p><p>"week":  ,</p><p>"enable":  ,</p><p>"action": ,</p><p>"timeOffset2":  ,</p><p>"index": </p><p>}</p>|无|<p>配置组网设备定时任务</p><p>当taskId（非0） 已经存在时，为更新原有定时任务</p><p>taskId不存在时为新添加定时任务，taskId 为 0时为新增，由ahsapd生成新的非0 taskId</p>|
||deleteTimedTask|<p>{</p><p>"taskId":  </p><p>}</p><p></p>|无|删除定时任务|

组网设备定时任务获取和配置参数定义如表2.4.10 - 2所示：

表2.4.10 - 2 设备定时任务参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|tasks|Array of Object|只读|[{以下字段组成的json},…]|Y|
|taskId|Number(uint32)|读写|任务Id, 值为0 时为新增任务，ahsapd自己生成新的非0 taskId值|Y|
|timeOffset|Number(uint64)|读写|执行时间偏移量（执行任务的时间点与当天凌晨0点的时间差，以秒为单位）|Y|
|week|Number(uint32)|读写|使用int值二进制位的低7位代表星期，由高到低位依次代表星期六五四三二一日。如7(00000111B)表示周二，周一和周日。若仅需单次执行，值为0。|Y|
|enable|Number(uint32)|读写|任务是否启用0：不启用， 1：启用|Y|
|action|Number(uint32)|读写|<p>任务指令，可能取值如下：</p><p>1: ToReboot（重启）</p><p>2: ToSetHealthMode（健康模式，定时开、关Wifi，（健康模式，定时开、关Wifi，在TimeOffset对应的时间关闭指定index的Wifi，在TimeOffset2对应的时间开启它。当TimeOffset2小于TimeOffset时，TimeOffset2代表相对于第二天0点的时间偏移量）</p>|Y|
|timeOffset2|Number(uint64)|读写|仅当Action为ToSetHealthMode时，才携带该字段, 执行时间偏移量（执行任务的时间点与当天凌晨0点的时间差，以秒为单位）|N|
|index|Number(uint32)|读写|<p>仅当Action为ToSetHealthMode时，才携带该字段</p><p>，Wifi通道。2.4G用1-4，5G用5-8，0表示所有Wifi通道</p>|N|


# <a name="_toc106289326"></a>2.4.11 组网设备配置黑白名单
插件调用接口：

int ahsapi\_set\_mac\_filter(cJSON\* macFilter) // 设置黑白名单

int ahsapi\_get\_mac\_filter(cJSON\*\* macFilter) // 获取黑白名单配置

macFilter为JSON字符串，包含参数内容如下表 

组网设备配置黑白名单对象方法定义如表2.4.11-1所示：

表2.4.11 - 1 设备黑白名单对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|ahsapd. macfilter|getMacFilter|无|||

|<p>{</p><p>"ahsapd.macfilter": {</p><p>"macFilterEnable": ,</p><p>"macFilterPolicy": ,</p><p>"macFilterEntries":" </p><p>}</p><p>}</p>||1\.0|
| :- | - | - |

|||||获取组网设备黑白名单配置信息|
| - | - | - | - | :- |
||setMacFilter|<p>{</p><p>"macFilterEnable": ,</p><p>"macFilterPolicy": ,</p><p>"macFilterEntries ":""   </p><p>}</p>|无|<p>配置黑白名单策略，macFilterEntries 包含所有过滤的地址</p><p></p>|

组网设备配置黑白名单对象方法定义如表2.4.11- 2所示：

表2.4.11 - 2 设备黑白名单配置参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|macFilterEnable|Number(uint32)|读写|是否使能MAC地址过滤；1为过滤；0为不过滤|Y|
|macFilterPolicy|Number(uint32)|读写|MAC地址过滤策略；0为黑名单；1为白名单|Y|
|macFilterEntries|String|读写|黑白名单信息：包含MAC地址及设备名（一组最长为64字节），以“/”隔开；两组信息之间以逗号分开，最多支持32组。示例： 001111334455/pro,80717a33ccf3/android|Y|


# <a name="_toc106289327"></a>2.4.12 组网设备操控指令
插件调用接口：

int ahsapi\_set\_reboot(int controlType)   // 组网设备重启操作

int ahsapi\_set\_reset()                 // 组网设备恢复出厂

int ahsapi\_set\_led\_control(int ledOnOFF) //组网设备led灯控

int ahsapi\_set\_firmware\_upgrade(cJSON \* upgrade) //组网设备固件升级

int ahsapi\_set\_lock\_net (int lockStatus) // 锁网配置

int ahsapi\_set\_pmf\_ctrl(int pmfEnable) //配置管理帧加密，家宽优选插件使用接口

int ahsapi\_get\_control\_status(cJSON \*\* controlStatus) // 获取led灯控、锁网

int ahsapi\_set\_hwnat\_clean()  // 配置清空硬加速链路跟踪表，家宽优选插件使用接口

int ahsapi\_set\_kernel\_count(int count) //配置硬加速，软件协议栈报文处理数量，家宽优选插件使用接口

int ahsapi\_set\_dev\_log\_stats(cJSON \* devLog, char\* logPath , int len) //配置日志采集

int ahsapi\_set\_ipv6\_control(int ipv6Enable)//配置ipv6双栈开关

int ahsapi\_set\_multicast\_enable( int multicastEnable) //配置组播转单播开关

int ahsapi\_get\_multicast\_enable(int \*multicastEnable)//获取组播转单播开关状态

upgrade和controlStatus、devLog为为JSON字符串，包含参数内容如下：

组网设备操控指令对象方法定义如表2.4.12 - 1所示：

表2.4.12 - 1 设备操作指令对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|<p>ahsapd. control</p><p></p>|reboot|<p>{</p><p>"controlType": 1   </p><p>}</p>|||

|无||1\.0|
| :- | - | - |

|||||组网设备重启操作|
| - | - | :- | - | :- |
||reset|无|无|组网设备恢复出厂|
||ledControl|<p>{</p><p>"ledOnOff": 1   </p><p>}</p>|无|组网设备led灯控|
||firmwareUpgrade|<p>{</p><p>"seqId":"XXXX",</p><p>"fileType": 1,</p><p>"localPath":"XXX",</p><p>` `"reboot": 0</p><p>}</p>|`    `无|组网设备固件升级；ahsapd收到指令后返回结果（非阻塞），执行升级过程结果通过controlReport 事件返回升级结果（如需立刻重启的先发送结果等待2秒后执行重启动作），如升级路径是网络下载地址，需求ahsapd操作下载固件过程的需要下载完成后通过controlReport返回下载结果|
||setLockNet|<p>{</p><p>"lockStatus":1</p><p>}</p>|无|设置组网设备锁网|
||pmfCtrl|<p>{</p><p>"<a name="_hlk55912994"></a>pmfEnable":1</p><p>}</p>|无|Protected Management Frame 开关（管理帧开关），家宽优选插件使用|
||ipv6Control|<p>{</p><p>ipv6Enable</p><p>}</p>|无|ipv4/ipv6 双栈功能开关，同时支持ipv4和ipv6协议|
||getControlStatus|无|<p>{</p><p>"ahsapd. control"：</p><p>{</p><p>"ledOnOff ": 1,</p><p>"lockStatus":1，</p><p>"pmfEnable":1，</p><p>"ipv6Enable":1，</p><p>"count":"0",</p><p>"logSwitch":0,</p><p>"logLevel":0,</p><p>"logTime": 0</p><p>}</p><p>}</p>|获取组网设备led灯和锁网，ip双栈等状态信息|
||hwnatClean|无|无|清空硬加速链路跟踪表，家宽优选插件使用|
||kernelCount|<p>{</p><p>"count":"0"</p><p>}</p>|无|配置硬件加速，软件协议栈报文处理数量，家宽优选插件使用|
||devLogStats|<p>{</p><p>"seqId":"XXXX",</p><p>"logSwitch":1,</p><p><a name="ole_link22"></a>"logLevel":0,</p><p>"logTime": 100</p><p>}</p>|<p>{</p><p>"ahsapd. control"：</p><p>{</p><p>"logPath": " " </p><p>}</p><p>}</p>|配置设备日志收集功能；日志收集指令立刻返回日志路径（非阻塞），到日志收集执行完成后通过controlReport通知日志收集执行结果|
||<p>setMulticastEnabl</p><p>e</p>|<p>{</p><p><a name="ole_link23"></a>"multicastEnable":0</p><p>}</p>|无|配置组播转单播开关|
||<p>getMulticastEnabl</p><p>e</p>|无|<p>{</p><p>"ahsapd. control"：</p><p>{</p><p>"multicastEnable":0</p><p>}</p><p>}</p>|获取组播转单播开关状态|


操控指令event响应上报升定义如下表2.4.12 - 2 所示

表2.4.12 – 2 操控指令event响应上报消息定义 

|消息类型|消息内容|描述|是否必须|
| :-: | :-: | :-: | :-: |
|controlReport|<p>{</p><p>"seqId":"XXXX",</p><p>"respType": 3 ,</p><p>"respCode": 0</p><p>}</p>|需要长时间执行的操作指令，执行完成后通过事件上报执行结果,seqId为对应配置下发的标识,|<p></p><p>Y</p>|


组网设备操控指令参数定义如表2.4.12 - 3所示

表2.4.12 - 3设备操控指令参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|controlType|Number(uint32)|只写|<p>1:  reboot立刻重启；</p><p>2:  2.4GChanReSelect"：重新自动选择2.4G频段WiFi工作信道（下挂STA设备使用的WiFi工作信道）</p><p>3:  "5GChanReSelect"：重新自动选择5G频段WiFi工作信道（下挂STA设备使用的WiFi工作信道）</p>|Y|
|ledOnOff|Number(uint32)|读写|0或1（0: 关闭，1: 打开）|Y|
|lockStatus|Number(uin32)|读写|0: 锁网关闭，1: 锁网开启|Y|
|fileType|Number(uint32)|只写|<p>1: 升级固件 </p><p>2：升级配置文件</p><p>3：预留</p>|Y|
|localPath|String|只写|升级文件本地的路径或下载路径URL，当路径为设备本地文件，设备直接升级，当路径为网络下载地址时，设备需先下载升级文件在升级|Y|
|reboot|Number(uint32)|只写|<p>升级后智能组网终端是否立即重启生效，取值为：</p><p>0: 不立即重启，等待下次重启后生效，</p><p>1: 立即重启生效</p>|Y|
|pmfEnable|Number(uint32)|读写|<p>PMF（Protected Management Frame）</p><p>1：打开PMF开关</p><p>0：关闭PMF开关</p>|Y|
|count|String|读写|<p>设备硬加速，软件协议栈处理报文个数</p><p>-1：关闭硬加速</p><p>0：报文均走硬加速</p><p>其它(正值)：报文在协议栈处理的数量</p><p></p>|Y|
|logSwitch|Number(uint32)|读写|日志采集开关 0 ：关闭 1：开启|Y|
|logLevel|Number(uint32)|读写|<p>日志采集级别:</p><p>0：ALL</p><p>1:  INFO</p><p>2:  ERROR</p><p>3： DEBUG</p>|Y|
|logTime|Number(uint64)|读写|设备日志采集时间单位秒(S),日志开启后采集时间，值为0时就返回当前存量日志，值大于0时表示再存量日志的基础上再采集多少时间，日志大小根据硬件空间自行定义|Y|
|logPath|String|只读|日志文件保存路径，日志文件需可读|Y|
|ipv6Enable|Number(uint32)|读写|<p>组网终端IPv4/IPv6双栈状态，枚举取值；</p><p>0：关闭IPv4/IPv6双栈</p><p>1：开启Ipv4/IPv6双栈</p>|Y|
|respType|Number(uint32)|只读|<p>操作指令event上报类型：</p><p>1: 固件/文件下载</p><p>2: 固件升级</p><p>3：日志获取</p><p>4：待扩展</p>|Y|
|respCode|Number(uint32)|只读|<p>对应操作指令事件执行结果：</p><p>0：执行成功</p><p>1：执行失败</p><p>2：待扩展</p>|Y|
|seqId|String|读写|配置对应唯一标识；如配置下发携带seqId，event上报必选携带下发的seqId|N|
|multicastEnable|Number(uint32)|读写|组播转单播控制开关|Y|


# <a name="_toc106289328"></a>2.4.13 组网设备配置保存
插件调用接口：

int ahsapi\_cfg\_add\_item(char\* item , char\* value)   //配置数据保存

int ahsapi\_cfg\_get\_item(char\* item, char\* value, int len)数据获取

int ahsapi\_cfg\_delete\_item(char\* item)  //配置数据删除

int ahsapi\_get\_cfg\_path(int filetype, char\* cfgPath, int len)   //配置文件路径

item、value、 cfgPath 、len为普通字符串

组网设备配置保存对象方法定义如表2.4.13-1所示：

表2.4.13 - 1 设备配置保存对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|<p>ahsapd. config</p><p></p>|addItem|<p>{</p><p>"item":"XXX",</p><p>"value":"XXX"</p><p>}</p>|||

|`   `无||1\.0|
| :- | - | - |

|||||配置保存和更新，如果item项值不存在，新增；如果item项值存在，更新value|
| - | - | :- | - | :- |
||getItem|<p>{</p><p>"item":"XXX"</p><p>}</p>|<p>{</p><p>"ahsapd.config":{</p><p>"value":"XXX"</p><p>` `}</p><p>}</p>|配置获取|
||deleteItem|<p>{</p><p>"item":"XXX"</p><p>}</p>|`    `无|配置删除|
||getCfgPath|<p>{</p><p>"fileType": 1</p><p>}</p>|<p>{</p><p>"ahsapd.config":{</p><p>"cfgPath":"XXX"</p><p>` `}</p><p>}</p>|获取配置文件路径|


组网设备配置保存参数定义如表2.4.13 - 2所示：

表2.4.13 - 2 设备配置保存参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|item|String|只写|配置项名称|Y|
|value|String|只写|配置项的值|Y|
|fileType|Number(uint32)|只写|<p>1: 设备保存配置数据文件；</p><p>2：组网插件（AHS-NET）配置文件；</p><p>3：其它预留</p><p></p>|Y|
|cfgPath|String|只读|文件路径|Y|


# <a name="_toc106289329"></a>2.4.14 组网设备Mesh
组网设备mesh配置基于支持mesh功能的设备，非mesh设备不支持本章节内容

插件调用接口：

int ahsapi\_map\_set\_mesh\_role(int role)      //配置mesh角色

int ahsapi\_map\_set\_mesh\_roaming(int enable) //配置mesh 漫游开关

int ahsapi\_map\_get\_mesh\_info(cJSON \*\*meshInfo)  //获取mesh 状态信息

int ahsapi\_map\_get\_mesh\_topology(cJSON \*\*meshTopology)  //获取mesh拓扑结构

meshInfo 和meshTopology 为JSON字符串，包含参数内容如下表

组网设备Mesh对象方法定义如表2.4.14 - 1所示：

表2.4.14 - 1 设备配置保存对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|<p>ahsapd.mesh</p><p></p>|setMeshRole|<p>{</p><p>"meshRole": 1</p><p>}</p>|||

|无||1\.0|
| :- | - | - |

|||||设备mesh 角色|
| - | - | :- | - | :- |
||<a name="ole_link12"></a><a name="ole_link11"></a>setMeshRoamingEnable|<p>{</p><p>"meshRoamingEnable ":1</p><p>}</p>|` `无|设置mesh 漫游开关|
||getMeshInfo|无|<p>{</p><p>"ahsapd.mesh":{</p><p>"meshRoamingEnable ":1,</p><p>"meshType":1,</p><p>"meshRole":1,</p><p>"upTime":"100"，</p><p>"ipAddress":"************"，</p><p>"ipv6IPAddress":"2001:DA8:1000::F1"，</p><p>"deviceVendor":"01"，</p><p>"deviceMode":"1101"，</p><p>"deviceMac":"00112233ABCD"，</p><p>"meshStatus": 1,</p><p>"staNumber": 2,</p><p>"bhInfo":{</p><p>"bhType":"WLAN",</p><p>"bhalmac":"08107A33ABCD",</p><p>"radio":"2.4G",</p><p>"ssid":"cmcc-test",</p><p>"pwd":"123456 ",</p><p>"securityMode":"MIXED-WPAPSK2</p><p>",</p><p>"rxRate": "100",</p><p>"txRate": "100"</p><p>}</p><p>}</p><p>}</p>|获取mesh状态信息|
||getMeshTopology|无|<p>{</p><p>"ahsapd.mesh":{</p><p>"meshTopology":[</p><p>{</p><p>"meshType":1,</p><p>"meshRole": 1,</p><p>"upTime":"100"，</p><p>"ipAddress":"************"，</p><p>"ipv6IPAddress":"2001:DA8:1000::F1"，</p><p>"deviceVendor":"01"，</p><p>"deviceMode":"1101"，</p><p>"deviceMac": "00112233ABCD",</p><p>"meshStatus": 1,</p><p>"staNumber": 2,</p><p>"bhInfo":{</p><p>"bhType":"WLAN",</p><p>"bhalmac":" ",</p><p>"ssid":"1234",</p><p>"pwd":" 86745test",</p><p>"securityMode":"",</p><p>"rxRate": "100",</p><p>"txRate": "100"</p><p>}</p><p>}</p><p>]</p><p>}</p><p>}</p>|获取mesh 拓扑结构，|

Mesh状态信息变化主动event上报定义如下表2.4.14 - 2 所示

`                             `表2.4.14 -2 mesh信息变化消息通知

|消息类型|消息内容|描述|是否必须|
| :-: | :-: | :-: | :-: |
|meshInfo|<p>{</p><p>"meshType":1,</p><p>"meshRole":1,</p><p>"upTime":"100"，</p><p>"ipAddress":"************"，</p><p>"ipv6IPAddress":"2001:DA8:1000::F1"，</p><p>"deviceVendor":"01"，</p><p>"deviceMode":"1101"，</p><p>"deviceMac":"00112233ABCD"，</p><p>"meshStatus": 0</p><p>"bhInfo":{</p><p>"bhType":"WLAN",</p><p>"bhalmac":"08107A33ABCD",</p><p>"radio":"2.4G",</p><p>"ssid":"cmcc-test",</p><p>"pwd":"123456 ",</p><p>"securityMode":"MIXED-WPAPSK2",</p><p>"rxRate": "300",</p><p>"txRate": "300"</p><p>}</p><p>}</p><p></p>|Mesh状态信息变化消息通知,meshRole, IP地址，meshStatus, bhType等因素发生变化，主动通知；|Y|

组网设备EasyMesh参数定义如表2.4.14 - 3所示：

表2.4.14 - 3设备EasyMesh参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|meshRoamingEnable|Number(uint32)|读写|Mesh 漫游开关|Y|
|meshTopology|Array of Object|读写|拓扑网络信息|Y|
|meshType|Number(uint32)|只读|<p>0：非mesh终端</p><p>1：easyMesh</p><p>2：其它mesh</p>|<p>Y</p><p></p>|
|meshRole|Number(uint32)|读写|<p>Mesh角色</p><p>0：非mesh终端</p><p>1: Controller</p><p>2: Agent</p><p>3: AC</p><p>4: AP</p><p>5: Auto(自动选择角色)</p><p>注：当读取meshRole时，值范围为0 - 4，单配置meshRole 是值范围为1-5；非mesh终端不需要配置mesh角色；</p>|Y|
|upTime|String|只读|在线时长，单位秒；|Y|
|ipAddress|String|只读|拓扑设备IP地址|Y|
|ipv6IPAddress|String|只读|拓扑设备IPV6 地址|Y|
|deviceVendor|String|只读|拓扑设备厂商编码|N|
|deviceMode|String|只读|拓扑设备型号编码|N|
|deviceMac|String|只读|设备MAC地址，格式大写去掉冒“00112233ABCD”|Y|
|meshStatus|Number(uint32)|只读|<p>组网设备是否加入mesh组网</p><p>1：是</p><p>0：否</p>|Y|
|bhInfo|Object|只读|mesh设备的上联backhaul BSS信息,包含bhType、bhAlmac、radio、ssid、pwd、securityMode、rxRate、txRate、fiberRxPower、fiberTxPower |Y|
|bhType|String|只读|上联backhaul的连接方式，枚举取值为WLAN、Ethernet、PLC、Cable、Optical Ethernet、GPON、XGPON、PoE|Y|
|bhAlmac|String|只读|上联设备的almac，格式：”00112233ABCD”，如果 meshRole取值为”controller”或” AC”时，则该字段表示上联设备的链路mac地址|Y|
|radio|String|只读|<p>上联接入方式是WLAN上联时使用的频段，取值2.4G、5G或5G-2，bhType为WLAN时必选；</p><p>注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；</p>|N|
|ssid|String|只读|ssid名称，上联WLAN接入的SSID;bhType 为WLAN时必选；|N|
|pwd|String|只读|ssid密码，上联WLAN接入的密码，bhType为WLAN时必选；|N|
|securityMode|String|只读|<p>认证加密模式，bhType为WLAN时必选，枚举取值：</p><p>None</p><p>WEP-64</p><p>WEP-128</p><p>WPA-Personal</p><p>WPA2-Personal</p><p><a name="ole_link29"></a>MIXED-WPAPSK2</p><p>WPA3-SAE</p><p>MIXED-WPA2WPA3</p>|N|
|rxRate|String|只读|协商接收速率，单位Mbps|Y|
|txRate|String|只读|协商发送速率，单位Mbps|Y|
|fiberRxPower|String|只读|光上联时发射光功率，单位dBm；光接入时必选；|N|
|fiberTxPower|String|只读|光上联时接收光功率，单位dBm；光接入时必选； |N|
|staNumber|Number(uint32)|只读|下挂设备数据，当值为0时，下挂设备信息数组为空|Y|

# <a name="_toc106289330"></a>2.4.15 组网设备网络限速
插件调用接口：

int ahsapi\_set\_device\_limit(cJSON\* deviceLimit) // 配置设备限速

int ahsapi\_get\_device\_limit(cJSON\*\* deviceLimit) // 获取所有设备限速配置

deviceLimit为JSON字符串，包含参数内容如下表

组网设备网络限速对象方法定义如表2.4.15-1所示：

表2.4.15 - 1 设备网络限速对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|<p>ahsapd. limit</p><p></p>|setDeviceLimit|<p>{</p><p>" devices": [</p><p>{</p><p>"deviceMac":"AABBCCDDEEFF ",</p><p>"upSpeed": " 10" ,</p><p>"downSpeed": "10 "</p><p>},</p><p>{</p><p>"deviceMac":"ABABCDCDEFEF ",</p><p>"upSpeed": "20" ,</p><p>"downSpeed": "20",</p><p>"limitedUrl":"www.taobao.com",</p><p>"deviceLimitMode":"1",</p><p>"deviceLimitWeek":127,</p><p>"deviceLimitTimeOffset1":100,</p><p>"deviceLimitTimeOffset2":10000</p><p>}</p><p>]</p><p>}</p>|||

|`   `无||1\.0|
| :- | - | - |

|||||配置设备限速|
| - | - | :- | - | :- |
||getDeviceLimit|无|<p>{</p><p>"ahsapd.limit":{</p><p>"devices": [</p><p>{</p><p>"deviceMac":"AABBCCDDEEFF",</p><p>"upSpeed": " 10" ,</p><p>"downSpeed": "10 "</p><p>},</p><p>{</p><p>"deviceMac":" AABBCCDDEEFF ",</p><p>"upSpeed": "20" ,</p><p>"downSpeed": "20 ",</p><p>"limitedUrl":"www.taobao.com",</p><p>"deviceLimitMode":"1",</p><p>"deviceLimitWeek":127,</p><p>"deviceLimitTimeOffset1":100,</p><p>"deviceLimitTimeOffset2":10000</p><p></p><p>},</p><p>….</p><p>]</p><p>}</p><p>}</p>|获取所有限速配置|

组网设备deviceLimit参数定义如表2.4.15-2所示：

表2.4.15 - 2设备网络限速参数


|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|devices|Array of Object|读写|设备限速配置列表|Y|
|deviceMac|String|读写|组网设备MAC地址或组网下挂设备MAC地址（如配置的MAC是组网设备的MAC限速对所有下挂设备有效，如配置MAC地址是下挂设备MAC限速对当前下挂设备有效；MAC地址为组网设备的配置优先级最高）|Y|
|upSpeed|String|读写|设备上行限速(单位Kpbs)，值为正值是有效限速值，为0取消限速|Y|
|downSpeed|String|读写|设备下行限速(单位Kpbs)，值为正值是有效限速值，为0取消限速|Y|
|limitedUrl|String|读写|限制访问域名列表（路由模式需要支持）；多组域名之间以逗号分开，最多支持32组。示例："www.taobao.com,www.baidu.com"|N|
|deviceLimitMode|Number(uint32)|读写|<p>是否周期启用设备限速和访问限制：</p><p>0：关闭周期控制，立即执行</p><p>1：启用周期控制，按周期执行；(在DeviceLimitTimeOffset1对应时间加入速率限制，在DeviceLimitTimeOffset2对应时间解除速率限制。当DeviceLimitTimeOffset2小于DeviceLimitTimeOffset1时，DeviceLimitTimeOffset2代表相对于第二天0点的时间偏移量）</p>|N|
|deviceLimitWeek|Number(uint32)|读写|使用int值二进制位的低7位代表星期，由高到低位依次代表星期六五四三二一日。如7(00000111B)表示周二，周一和周日。若仅需单次执行，值为0。|N|
|deviceLimitTimeOffset1|Number(uint64)|读写|执行时间偏移量1（执行任务的时间点与当天凌晨0点的时间差，以秒为单位），该时刻启动速率限制；|N|
|deviceLimitTimeOffset2|Number(uint64)|读写|行时间偏移量2（执行任务的时间点与当天凌晨0点的时间差，以秒为单位），该时刻停止速率限制；|N|

# <a name="_toc106289331"></a>2.4.16 组网设备持续测速
插件调用接口：

int ahsapi\_http\_speed\_test(cJSON\* speedTest) //组网设备 Http持续测速

speedTest为JSON字符串，包含参数内容如下表

组网设备探测对象方法定义如表2.4.16 -1所示：

表2.4.16 - 1 设备探测对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|<p>ahsapd. detection</p><p></p>|httpSpeedTest|<p>{</p><p>"seqId":"XXXX",</p><p>"httpSpeedTestUrl":"",</p><p>"httpSpeedTestTime": 200,</p><p>"httpSpeedTestInterval": 100</p><p>}</p>|||

|`   `无||1\.0|
| :- | - | - |

|||||http持续测速|
| - | - | :- | - | :- |


设备http持续测速结果主动event上报定义如下表2.4.16 -2 所示

表2.4.16 - 2设备http持续测速结果信息主动上报消息定义 

|<a name="ole_link21"></a>消息类型|消息内容|描述|是否必须|
| :-: | :-: | :-: | :-: |
|httpSpeedReport|<p>{</p><p>"seqId":"XXXX",</p><p>"httpSpeedTestRate":" ",</p><p>"transmitRatio": 1</p><p>}</p>|设备http持续测速结果事件上报，根据下发指令间隔触发事件上报，测试时间httpSpeedTestTime到结束上报|<p></p><p>Y</p>|


组网设备探测参数详细定义如表2.4.16 -3所示：

`					`表2.4.16 -3组网设备探测参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|httpSpeedTestUrl|String|读写|Http持续测速文件下载地址|Y|
|httpSpeedTestTime|Number(uint32)|读写|Http持续测速测试时间，单位S（秒）|Y|
|httpSpeedTestInterval|Number(uint32)|读写|Http持续测速上报间隔时间,单位 ms(毫秒)|Y|
|httpSpeedTestRate|String|只读|Http持续测速事件上报间隔平均速率值，单位Mpbs|Y|
|transmitRatio|Number(uint32)|只读|传输进度，0：测速结果事件上报未完成，1：测速结果事件上报完成|Y|
|seqId|String|读写|配置对应唯一标识,如配置下发携带seqId，event上报必选携带下发的seqId；|N|

# <a name="_toc106289332"></a>2.4.17 组网设备WIFI信道状态信息(CSI)【可选】

插件调用接口：

int ahsapi\_set\_csi\_enable(cJSON \*csiEnable ) //配置CSI数据采集开关

int ahsapi\_get\_csi\_enable(cJSON \*\*csiEnable) //获取CSI数据采集开关状态

int ahsapi\_add\_csi\_mac(cJSON \*csiMac) //配置CSI采集下挂设备MAC白名单

int ahsapi\_del\_csi\_mac(cJSON \*csiMac) //删除CSI采集下挂设备MAC白名单

int ahsapi\_get\_csi\_mac(cJSON \*\*csiMacEntries) //获取CSI MAC地址白名单

int ahsapi\_get\_csi\_version(cJSON \*\*csiVersion) //获取csi算法库版本

int ahsapi\_get\_csi\_chip(cJSON \*\*csiChip) //获取wifi芯片信息

组网设备WiFi信道状态信息对象方法定义如表2.4.17 – 1所示：	

表2.4.17 – 1设备WIFI信道状态信息对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|ahsapd.wifi.csi|setCSIEnable|<p>{</p><p>"seqId ":"\*\*\*",</p><p>"radio":"",</p><p>“csiEnable”:</p><p>}</p>|无|<p>配置CSI数据采集开关</p><p>[apd侧]</p>|
||getCSIEnable|无|<p>{</p><p>" ahsapd.wifi.csi": {</p><p>"csiEnable": [{</p><p>"radio":"",</p><p>"csiEnable ":</p><p>}]</p><p>}</p><p>}</p>|<p>获取CSI数据采集状态</p><p>[apd侧]</p>|
||addCSIMac|<p>{</p><p>"seqId ":"\*\*\*",</p><p>"radio":"",</p><p>"csiMac ":""</p><p>}</p>|无|<p>添加CSI数据采集下挂设备MAC地址白名单(累加式添加单个mac)</p><p>[apd侧]</p>|
||delCSIMac|<p>{</p><p>"seqId ":"\*\*\*",</p><p>"radio":"",</p><p>"csiMac":""</p><p>}</p>|无|<p>删除CSI MAC地址白名单</p><p>[apd侧]</p>|
||getCSIMac|<p></p><p>无</p>|<p>{</p><p>" ahsapd.wifi.csi": {</p><p>"csiMac":[{</p><p>"radio":"",</p><p>" csiMacEntries ":"",</p><p>}]</p><p>` `}</p><p>}</p>|<p>获取CSI MAC地址白名单（mac用逗号隔开，拼接字符串；包含2.4G和5G信息）</p><p>[apd侧]</p>|
||getCSIVersion|无|<p>{</p><p>" ahsapd.wifi.csi": {</p><p>"ubusVersion": "X.X.X"</p><p>"csiVersion": "X.X.X"</p><p>}</p>|<p>获取版本号（ubusVersion对应集成规范版本号；csiVersion对应驱动版本号）</p><p>[apd侧]</p>|
||getChipInfo|无|<p>{</p><p>" ahsapd.wifi.csi ": {</p><p>"chip1Type": "",</p><p>" chip2Type": "",</p><p>" chip3Type": "",</p><p>}</p><p>}</p>|<p>获取组网设备wifi芯片信息。</p><p>如果wifi芯片为一个，则chip1Type对应型号，chip2Type、chip3Type为缺省。</p><p>如果三频wifi是不同芯片，则chip1Type对应2.4G，chip2Type 对应5.2G，chip3Type 对于5.8G。</p>|


组网设备WIFI信道状态信息参数详细定义如表2.4.17 – 2所示：

`					 `表2.4.17 – 2组网设备WIFI信息状态信息

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|radio|String|读写|<p>频段，枚举取值：</p><p>2\.4G</p><p>5G</p><p>5G-2</p><p>5G+5G-2</p>|Y|
|csiEnable|Number(uint32)|读写|<p>该频段是否启用CSI采集状态，</p><p>1为启用，0为禁用</p>|Y|
|csiMac|String|读写|白名单MAC信息：包含单个MAC地址，格式大写|Y|
|csiMacEntries|String|读写|白名单MAC信息：包含MAC地址，格式大写，两组信息之间以逗号分开，最多支持32组。示例：001111334455,80717A33CCF3|Y|
|speedInterval|Number|读写|移动侦测结果上报时间间隔（秒）|Y|
|SpeedFlag|Number|读写|<p>移动侦测结果上报模式</p><p>0：到达间隔时间（speedInterval）即上报最新一包的数据；</p><p>1：到达间隔时间（speedInterval）并且触发了活跃/非活跃切换，才上报数据</p>|Y|
|ubusVersion|String|只读|ubusVersion对应集成规范版本号； |Y|
|csiVersion|String|只读|csiVersion对应驱动版本号|Y|
|chip1Type|String|只读|Wifi-2.4G芯片型号（厂商缩写+型号编码），如“MT7915”|Y|
|Chip2Type|String|只读|Wifi-5.2G芯片型号。若集成为1个wifi芯片，则缺省|N|
|Chip3Type|String|只读|Wifi-5.8G芯片型号。若集成为1个wifi芯片，则缺省|N|

# csi stream数据传输方式：netlink协议
1、配置和获取配置通过ubus进行通信

2、csi算法运行在用户态，由应用层控制csi stream的上报间隔，通过netlink方式周期性向内核请求。内核成功收到请求后、响应最新的csi stream数据。

3、采用user to kernel的netlink通讯方式，driver必须先向内核注册一个struct genl\_family，并且注册一些cmd的处理函数。这些cmd是跟某个family关联起来的。

套接字协议值：NETLINK\_GENERIC

family name: “csi\_genl”

cmd:

enum {

`    `CSI\_OPS\_UNSPEC,

` 	`CSI\_OPS\_REPORT,//上报最新的一包csi数据

`    `CSI\_OPS\_MAX,

};

用户态请求数据时，cmd msg:{“radio”:”5G”,"reqNum":4}，5G表示请求5G的csi数据（radio还可以为2.4G），4表示请求4个包数据，1包数据包含一个chain的rssi，snr，i,q等

4、数据格式

预估4个chain，子载波最大个数为256的情况下，总数据长度小于5KB

1）nlattr明细

|attr1|attr2|attr3|Tag|type|备注|
| :- | :- | :- | :- | :- | :- |
|dataHeader|||1|nest|以上所有nlattr包含在此属性中|
||timeStamp||4|u64|csi采集时间戳(ms)|
||raMac||5|u8[6]|接收端mac|
||taMac||6|u8[6]|发送端mac|
||frequencyBand||7|u8|<p>使用的频段</p><p>`   `0-2.4G</p><p>`   `1-5G</p>|
||channelBandwidth||8|u8|<p>设定带宽，枚举值：</p><p>0: 20M</p><p>1: 40M</p><p>2: 80M</p><p>` `3: 160M</p>|
||dataBandwidth||9|u8|<p>实际带宽，枚举值：</p><p>0: 20M</p><p>1: 40M</p><p>2: 80M</p><p>` `3: 160M</p>|
||protocolMode||10|u32|<p>协议模式，枚举值：</p><p>1\.CSI\_FRAME\_TYPE\_11A</p><p>2\.CSI\_FRAME\_TYPE\_11B</p><p>4\.CSI\_FRAME\_TYPE\_11G</p><p>8\.CSI\_FRAME\_TYPE\_11N</p><p>16\.CSI\_FRAME\_TYPE\_11AC</p><p>32\.CSI\_FRAME\_TYPE\_11AX</p>|
||frameType||11|u8|帧类型，同Wi-Fi标准协议，有效值6bit，包括type和subtype.例：frame\_type=0x12(00010010) 取6bit(010010)type=10, subtype=0100|
||chainNum||12|u8|Chain个数，值为0–4（如2发2收，会产生4个chain的csi）csi chain(链路)的最大个数|
||primaryChannelIndex||13|u8|主通道索引，802.11协议标准|
||phyerr||14|u8|物理层上报错误 ID，如成功值为 0|
||rate||15|u8|MCS 速率，同Wi-Fi标准协议，填写MCS索引；如果芯片无法获取可以直接填 0|
||extraInformation||16|u32|附加信息，缺省为0|
||channel||17|u16|Radio信道,当前 Wi-Fi 使用的信道|
||csiLen||18|u16|CSI子载波个数（实际采集到的csi子载波个数），不同芯片厂商、不同协议会有不同；为了统一接口，所以需要直接获取csi子载波个数值。|
||packetIdx||19|u32|上报序号（每次上报+1）|
||chainHeader||2|nest|<p>与chain相关的属性</p><p>[18,25]包含在此属性中</p>|
|||chainIdx|20|u8|chanIdx(0,1,2...)|
|||rssi|21|s8|信号强度，单位为 dBm|
|||snr|22|u8|信噪比，单位为 dBm，如果芯片无法获取可以直接填 0|
|||noise|23|u8|<p>noise floor 本底噪声（或称本体噪声、固有噪声），单位dbm。</p><p>`   `如果芯片无法获取可以直接填 0,长度为1</p>|
|||agcCode|24|u16|Agc code，自动增益控制码；根据厂商芯片规格填写，如果芯片无法获取可以直接填0|
|||phaseIncr|25|s16|频偏，如果芯片无法获取可以直接填 0|
|||txStreamIdx|26|u16|对端tx stream序号|
|||rxStreamIdx|27|u16|自身rx stream序号|
|||csiComPlexI|28|s16[csiLen]|i0,i1,i2,i3...|
|||csiComPlexQ|29|s16[csiLen]|q0,q1,q2,q3...|
|<p>reportMsg</p><p></p>|||3|NLA\_STRING|<p>用户态->内核态：发送请求json，包含radio和请求的包数</p><p>内核态->用户态：返回状态信息或错误信息（一般不会用到）</p>|

2）枚举

/\*csi cmd\*/

enum {

CSI\_OPS\_UNSPEC,

CSI\_OPS\_REPORT,

CSI\_OPS\_MAX,

};



/\* netlink attributes \*/

enum CSI\_NL\_ATTR {

CSI\_ATTR\_UNSPEC = 0,

`    `CSI\_ATTR\_DATA\_HEADER,

`    `CSI\_ATTR\_CHAIN\_HEADER,

`    `CSI\_ATTR\_REPORT\_MSG,

`    `//

`    `CSI\_ATTR\_TS, ///< 时间戳

`    `CSI\_ATTR\_RA, ///< 接收端MAC地址

`    `CSI\_ATTR\_TA, ///< 发送端MAC地址

`    `CSI\_ATTR\_FREQUENCY\_BAND, ///< 使用频段

`    `CSI\_ATTR\_CBW,  ///< channel 设定带宽 0-20M,1-40M,2-80M,3-160M

`    `CSI\_ATTR\_DBW,  ///< \*实际data带宽,0-20M,1-40M,2-80M,3-160M

`    `CSI\_ATTR\_PROTO, ///< protocol\_mode

`    `CSI\_ATTR\_FRAME\_TYPE, ///< 帧类型，同Wi-Fi标准协议，有效值6bit，

`    `CSI\_ATTR\_CHAIN\_NUM,  ///< Chain个数

`    `CSI\_ATTR\_PRI\_CHANNEL\_IDX,  ///< 主通道索引

`    `CSI\_ATTR\_ERR, ///< 物理层上报的错误ID（如成功，则设置0)

`    `CSI\_ATTR\_MCS,  ///< mcs速率

`    `CSI\_ATTR\_EXTRA\_INFO, ///< 附加信息

`    `CSI\_ATTR\_CHANNEL,  ///< 信道

`    `CSI\_ATTR\_CSI\_LEN, ///< CSI个数

`    `CSI\_ATTR\_PACKET\_IDX,  ///< 上报序号

`    `CSI\_ATTR\_CHAIN\_IDX,  ///< 上报chain序号

`    `CSI\_ATTR\_RSSI, ///< 综合信号强度

`    `CSI\_ATTR\_SNR, ///< SNR

`    `CSI\_ATTR\_NOISE, ///< 本底噪声

`    `CSI\_ATTR\_AGC\_CDOE,  ///< agc code

`    `CSI\_ATTR\_PHASE\_INCR, ///< 频偏

`    `CSI\_ATTR\_TX\_IDX,  ///< 对端tx stream序号

`    `CSI\_ATTR\_RX\_IDX,  ///< 自身rx stream序号

`    `CSI\_ATTR\_CSI\_I,

`    `CSI\_ATTR\_CSI\_Q,

`    `\_\_CSI\_ATTR\_MAX,

};

3）结构体

typedef struct{

unsigned long long timeStamp;///< 时间戳

unsigned char raMac[6];///< 接收端MAC地址

unsigned char taMac[6];///< 发送端MAC地址

unsigned char frequencyBand;///< 使用频段，0-2.4G,1-5G

unsigned char channelBandwidth;///< 设定带宽,0-20M,1-40M,2-80M,3-160M

unsigned char dataBandwidth;///< 实际带宽,0-20M,1-40M,2-80M,3-160M

unsigned int protocolMode;///< protocol\_mode

unsigned char frameType;///< 帧类型，同Wi-Fi标准协议，有效值6bit，

unsigned char chainNum;///< Chain个数

unsigned char primaryChannelIndex; ///< 主通道索引

unsigned char phyerr;///< 物理层上报的错误ID（如成功，则设置0)

unsigned char rate; ///< mcs速率

unsigned int extraInformation; ///< 附加信息

unsigned short channel; ///< 信道

unsigned short csiLen; ///< CSI个数

// chainHeader

unsigned int packetIdx; ///< 上报序号

unsigned char chainIdx;///< chain序号

char rssi;///< 综合信号强度

unsigned char snr;///< SNR

unsigned char noise;///< 本底噪声

unsigned short agcCode; ///< agc code

short phaseIncr;///< 频偏

unsigned short txStreamIdx;///< 对端tx stream序号

unsigned short rxStreamIdx;///< 自身rx stream序号

short \*csiComPlexI;///< 

short \*csiComPlexQ;///< 

}csi\_item\_t;


# csi stream参数定义表
csi开启状态下，采集信息需匹配protocolMode，如不匹配就无csi数据，不进行上报，待有符合条件的数据再上报。

Csi采集开启状态，若无配置frame模式，则不进行上报。

其中参数详细定义如表2.4.17 – 3所示：

表2.4.17 – 3组网设备csi stream数据

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|csiStream|Object|只读|CSI 采集数据信息|Y|
|timestamp|Number(uint64)|只写|时间戳，单位毫秒（ms）|Y|
|raMac|Array(uint8)|只读|接收端MAC地址|Y|
|taMac|Array (uint8)|只读|发送端MAC地址|Y|
|frequencyBand|Number(uint8)|只读|<p>使用的频段，枚举值：</p><p>0：error </p><p>1：2.4G</p><p>2：5G</p>|Y|
|bandwidth|Number(uint8)|只读|<p>带宽，枚举值：</p><p>0: 20M</p><p>1: 40M</p><p>2: 80M</p><p>3: 160M</p>|Y|
|rssi|Number(int8)|只读|信号强度，单位为dBm|Y|
|snr|Number(int8)|只读|信噪比，单位为dBm|Y|
|protocolMode|Number(uint32)|只读|<p>协议模式，枚举值：</p><p>1\.CSI\_FRAME\_TYPE\_11A</p><p>2\.CSI\_FRAME\_TYPE\_11B</p><p>4\.CSI\_FRAME\_TYPE\_11G</p><p>8\.CSI\_FRAME\_TYPE\_11N</p><p>16\.CSI\_FRAME\_TYPE\_11AC</p><p>32\.CSI\_FRAME\_TYPE\_11AX</p>|Y|
|frameType|Number(uint8)|只读|<p>帧类型，同Wi-Fi标准协议，有效值6bit，包括type和subtype</p><p>例：frame\_type=0x12(00010010)</p><p>取6bit(010010)type=10, subtype=0100</p>|N|
|chainNum|Number(uint8)|只读|Chain 个数，值为0 – 4（如2发2收，会产生4个chain的csi）csi chain(链路) 的最大个数|Y|
|csiLen|Number(uint16)|只读|CSI子载波个数0 -256，最大值为256（实际采集到的csi子载波个数），不同芯片厂商、不同协议会有不同；为了统一接口，所以需要直接获取csi子载波个数值。|Y|
|primaryChannelIndex|Number(uint32)|只读|主通道索引，802.11 协议标准|Y|
|rate|Number(uint32)|只读|MCS 速率|N|
|agcCode|Number(uint32)|只读|<p>Agc  code，自动增益控制码。</p><p>如缺省则填0</p>|N|
|phaseIncr|Number(uint32)|只读|频偏。如缺省则填0|N|
|channel|Number(uint16)|只读|Radio信道, 当前Wi-Fi使用的信道,如存在多信道，引用“,”逗号分隔，例：“136 ,149”|Y|
|packetIndex|Number(uint32)|只读|上报序号（每次上报+1）|Y|
|csiComplex|Array(int16)|只读|每4个字节为一个复合体（前2个字节为WIFI的i路（实部），后2个字节为WIFI的q路（虚部）；|Y|
|nosie|Number(int8)|只读|<p>noise floor本底噪声（或称本体噪声、固有噪声），单位dbm。</p><p>如果芯片无法获取可以直接填0</p>|N|
|phyerr|Number(int8)|只读|物理层上报错误ID，如成功值为 0|N|
|exterInformation|Number(uint32)|只读|附加信息,缺省为0|N|
|csiRate|Number(uint32)|读写|csi采样率，单位毫秒/包（ms/p），驱动自身采集csi的速率|N|
|csiChain|Number(uint32)|只读|csi只上报哪些chain(以bit位表示，1100则代表只上报chain2和chain3)|N|


# <a name="_toc106289333"></a>2.4.18 组网设备Qos配置
插件调用接口：

int ahsapi\_set\_qos\_config(cJSON\* qosConfig) //组网设备Qos配置

int ahsapi\_get\_qos\_config(cJSON\*\* qosConfig) //获取组网设备Qos配置信息

qosConfig 为JSON字符串，包含参数内容如下表

组网设备Qos对象方法定义如表2.4.18 – 1所示：	

表2.4.18 – 1组网设备Qos对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|ahsapd.qos|setQosConfig|<p>{</p><p>“qosEnable”:1,</p><p>“qosParameter”:[</p><p>{</p><p>“qosPriority”: 5,</p><p>“dscpEnable”:1,</p><p>“dscpPriority”:45,</p><p>“sourceMac”:” “,</p><p>“destinationMac”:” “</p><p>},</p><p>{</p><p>“qosPriority”:6,</p><p>“vlan8021pEnable”:1,</p><p>“vlan8021p”:6,</p><p>“sourceIp”:” “,</p><p>“sourcePort”:” “,</p><p>“destinationIp”:” “,</p><p>“destinationPort”:” “,</p><p>“protocol”:”ICMP”</p><p>}</p><p>]</p><p>}</p>|无|<p>该配置仅需组网设备工作在路由模式下支持;</p><p>通过该接口实现路由器qoS优先级配置，组网终端应支持通过MAC（包含源MAC和目的MAC）或五元组信息（包含源IP地址、目的IP地址、协议号、源端口、目的端口）对相关业务进行加速。</p>|
||getQosConfig|无|<p>{</p><p>“ahsapd.qos”:</p><p>{</p><p>“qosEnable”:1,</p><p>“qosParameter”:[</p><p>{</p><p>“qosPriority”:5,</p><p>“dscpEnable”:1,</p><p>“dscpPriority”:45,</p><p>“sourceMac”:” “,</p><p>“destinationMac”:” “</p><p>},</p><p>{</p><p>“qosPriority”:6,</p><p>“vlan8021pEnable”:1,</p><p>“vlan8021p”:6,</p><p>“sourceIp”:” “,</p><p>“sourcePort”:” “,</p><p>“destinationIp”:” “,</p><p>“destinationPort”:” “,</p><p>“protocol”:”ICMP”</p><p>}</p><p>]</p><p>}</p><p>}</p>|获取Qos配置信息|


组网设备Qos配置参数定义如表2.4.18 – 2所示：

表2.4.18 – 2 设备Qos配置参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|qosEnable|Number(uint32)|读写|QoS开关；1：打开，0：关闭|Y|
|qosParameter|Array of Object|读写|QoS分类规则，最大下发8个实例；|N|
|qosPriority|Number(uint32)|读写|QoS优先级；取值范围：0-7；数值越大优先级越高;|N|
|dscpEnable|Number(uint32)|读写|DSCP优先级开关；1：打开，0：关闭|N|
|dscpPriority|Number(uint32)|读写|DSCP优先级；取值范围：0-63；数值越大优先级越高；|N|
|vlan8021pEnable|Number(uint32)|读写|802\.1P启用开关；1：打开，0关闭|N|
|vlan8021p|Number(uint32)|读写|VLAN优先级；取值范围：0-7；数值越大优先级越高；|N|
|sourceIp|String|读写|源IP|N|
|sourcePort|String|读写|源端口|N|
|<a name="ole_link30"></a>destinationIp|String|读写|目的地IP|N|
|destinationPort|String|读写|目的地端口|N|
|protocol|String|读写|传输协议列表，多种协议时使用逗号进行分割；取值为：TCP，UDP，ICMP|N|
|sourceMac|String|读写|源MAC（一般指路由器铭牌MAC）|N|
|destinationMac|String|读写|目的MAC（一般指下挂设备MAC）|N|


# <a name="_toc106289334"></a>2.4.19 组网设备 VLAN配置
插件调用接口：

int ahsapi\_set\_iptv\_vlan\_config(cJSON\* iptvVlan) //组网设IPTV VLAN配置

int ahsapi\_get\_iptv\_vlan\_config(cJSON\*\* iptvVlan ) //获取组网设备IPTV VLAN配置信息

iptvVlan 为JSON字符串，包含参数内容如下表

组网设备对象方法定义如表2.4.18 – 1所示：	

表2.4.18 – 1组网设备VLAN配置对象方法

|对象名|方法名|参数|返回参数|描述|
| - | - | - | - | - |
|ahsapd.vlan|setIptvVlan|<p><a name="ole_link31"></a>{</p><p>“vlanEnable”:1,</p><p>“vlanId”:46,</p><p>“iptvStatus”:[</p><p>{</p><p>“radio”:”5G”,</p><p>“index”:5</p><p>},</p><p>{</p><p>“radio”:”2.4G”,</p><p>“index”:1</p><p>},</p><p>{</p><p>“radio”:”LAN”,</p><p>“index”:1</p><p>}</p><p>]</p><p>}</p>|无|<p>IPTV VLAN配置</p><p>1、绑定了IPTV业务的有线端口和无线SSID，其下行报文需删除VLAN tag，其上行报文需添加VLAN tag后再进行转发。</p><p>2、无论路由器自身工作模式（路由/桥接/中继），IPTV业务都应工作在桥接（或中继）模式；</p>|
||getIptvVlan|无|<p>{</p><p>“ahsapd.vlan”:</p><p>{</p><p>“vlanEnable”:1,</p><p>“vlanId”:46,</p><p>“iptvStatus”:[</p><p>{</p><p>“radio”:”5G”,</p><p>“index”:5</p><p>},</p><p>{</p><p>“radio”:”2.4G”,</p><p>“index”:1</p><p>},</p><p>{</p><p>“radio”:”LAN”,</p><p>“index”:1</p><p>}</p><p>]</p><p>}</p><p>}</p>|获取IPTV VLAN配置信息|


组网设备VLAN配置参数定义如表2.4.19 – 2所示：

表2.4.19 – 2 设备VLAN配置参数

|参数名|类型|读写权限|描述|是否必须|
| :-: | :-: | :-: | :-: | :-: |
|vlanEnable|Number(uint32)|读写|IPTV业务开关；1：打开，0：关闭;|Y|
|vlanId|Number(uint32)|读写|IPTV业务的VLAN ID；IPTV业务开关打开时必选;|Y|
|iptvStatus|Array of Object|读写|用户侧接口的VLAN参数；IPTV业务开关打开时必选；|N|
|radio|String|读写|<p>端口信息，对于WLAN接口的频段，取值2.4G或5G或5G-2，对于以太接口，取值为LAN； </p><p>注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；</p>|N|
|index|Number(uint32)|读写|WLAN接口的SSID索引或以太接口序号；2.4G索引1-4 ; 5G索引：5-8；5G-2索引:9-12；LAN索引从1开始；|N|




# <a name="_toc106289335"></a>附录A 响应码
`                     		`表 A – 1 ubus响应码

|` `**返回值**|**描述**|**描述**|
| - | - | - |
|0|Success|成功|
|1|Invalid command|无效的指令|
|2|Invalid argument|存在不支持的输入参数|
|3|Method not found|方法不存在|
|4|Not found|不存在|
|5|No data|无数据响应|
|6|Permission denied|无权限|
|7|Request timed out|请求超时|
|8|Operation not supported|操作不支持|
|9|Unknow error|未知错误|
|10|Connection failed|连接失败|



# <a name="_toc106289336"></a>附录B 修订记录
表 B-1 修订记录

|版本号|更新时间|主要内容或重大修改|编制人|
| - | - | - | - |
|V0.1|2020-10-30|初稿|魏飞|
|V0.2|2020-11-06|<p>1、修改ubus connect 路径默认使用null</p><p>2、修订插件调用接口函数以ahspai开始</p>|魏飞|
|V0.3|2020-11-12|<p>1、basic中meshRole修改为meshType同时修改值内容</p><p>2、2.4.5下挂设备增加收发字节和包数（增加4个字段）</p><p>3、2.4.4 增加uplinkMac字段</p><p>4、完善2.4.14 mesh章节内容</p>|魏飞|
|V0.4|2020-11-18|<p>1、删除返回参数中respCode和respCont</p><p>2、更新附录A 响应码</p><p>3、更新图2.1和图2.2</p>|魏飞|
|V0.5||1、插件调用接口get类型函数入参cJSON相关修改为 \*\*|魏飞|
|V0.6||1、2.4.14 章增加配置获取接口|魏飞|
|V0.7|2020-12-22|<p>1、2.4.7 中wifi统计中radio 有线定义为WAN 和LAN</p><p>2、规范中uint8/int8 全部修改为uint32/int32</p><p>3、2.4.9 漫游配置中staDisconnet修改为staDisconnect</p>|魏飞|
|V0.8|2021-01-05|<p>1、2.4.4章增加配置各种模式方法setWorkingMode</p><p>2、2.4.5下挂设备信息增加staVendor 字段</p><p>3、2.4.12 增加硬件加速方法hwnatClean 和kernelCount, 并增加count字段内容</p>|魏飞|
|V0.9|2021-01-07|<p>1、2.4.5章增加accessTime字段定义</p><p>2、2.4.4章增加index字段定义</p>|魏飞|
|V1.0|2021-03-01|1、增加2.4.15 章节组网设备网络限速|魏飞|
|V1.1|2021-03-23|<p>1、2.4.1 基本信息增加ahsapdVersion 版本、radio5、pppoeUser 3个字段</p><p>2、2.4.4  uplinkMac字段重新定义为设备上行mac , 新增gatewayMac字段 为设备上行网元mac </p><p>3、2.4.5 合并下挂设备上下线（staUpDown）内容,删除newSta信息event</p><p>4、2.4.12 中增加reboot 组网设备升级后是否立刻重启字段</p><p></p>|魏飞|
|V1.2|2021-03-30|<p>1、2.4.4 上行状态增加setUplinkRateConfig和getUplinkRateConfig 2个method，增加 averRxRate 、 averTxRate 、 maxRxRate 、maxTxRate 、reportInterval、sampleInterval 6个字段内容</p><p>2、2.4.5 下挂设备信息增加channel、averRxRate 、 averTxRate 、 maxRxRate 、maxTxRate 、reportInterval、sampleInterval 7个字段内容，增加setStaRateConfig和getStaRateConfig 2个method</p><p>3、2.4.6 wifi配置setWifiSwitch增加index字段 </p><p>4、2.4.9章节增加配置和获取双频合一配置的方setBandSteeringConfig和getBandSteeringConfig</p><p>5、2.4.12 章节增加日志采集 方法devLogStats，增加日志相关字段logSwitch, logLevel，logTime, logPath</p><p>6、增加2.4.16 章节组网设备探测</p><p>7、更新2.4章节中表格的标识为 2.4.X-X 样式</p>|魏飞|
|1\.3.0|2021-04-26|<p>1、 2.4.1 组网设备基本信息 中Ipv6IPAddress修改为ipv6IPAddress</p><p>2、2.4.6 章节wifi 信息中增加ssidStandard, <a name="ole_link24"></a>nosieLevel, interferencePercent 3个字段</p><p>3、2.4.4章节ipv6PrefixDelegationEnabled 字段修改为Number类型</p><p>4、2.4.6 章节增加wifi信息变化主动上报event，详见2.4.6-2 wifi信息变化主动上报消息定义</p><p>5、表2.4.16-2 中 例子中事件上报中的字段HttpSpeedTestRate修复为httpSpeedTestRate</p><p>6、2.4.12章节 增加操控指令event响应上报（详细见表2.4.12-2），同时增加respType、respCode 2个event上报所需字段内容</p><p>7、2.4.14 章节setMeshRoamingEnable的参数修改为meshRoamingEnable</p><p>8、更新插件调用接口</p>|魏飞|
|V1.4.0|2021-05-25|<p>1、2.4.4 增加interface 和rateArray字段，interface ,averRxRate, averTxRate,maxRxRate ，</p><p>maxTxRate 5个为rateArray 包含内容，可通过getUplinkInfo 返回值获取，setUplinkRateConfig 方法配置增加interface字段,删除getUplinkRateConfig方法</p><p>2、2.4.5增加interface 和rateArray字段，interface ,averRxRate, averTxRate,maxRxRate ，</p><p>maxTxRate 5个为rateArray 包含内容，可通过getStaInfo 返回值获取，setStaRateConfig方法配置增加interface字段,删除getStaRateConfig方法</p><p>3、2.4.4 wlanConnect增加securityMode字段</p>|魏飞|
|V1.4.1|2021-06-08|1、2.4.1 组网设备基本信息增加powerSupplyMode 供电方式字段，dmKey 修改为必须 ||
|V1.4.2|2021-07-30|1、增加2.4.17章节组网设备WIFI信道状态信息||
|V1.4.3|2022-01-12|<p>1、2.4.1 组网设备基本信息增加meshRole 字段；</p><p>2、2.4.4 组网设备上行信息 字段uplinkType值增加Optical Etbernet 、GPON、XGPON、PoE； 增加字段fiberTXPower , fiberRXPower , totalBytesSent ,totalBytesReceived , totalPacketsSent , totalPacketsReceived , errorsSent , errorsReceived , discardPacketsSent ,</p><p>discardPacketsReceived ;</p><p>3、2.4.5 组网设备下挂设备信息 字段staType修订值范围为：PC、Phone、TV、Router、IoT、IPC、others，字段radio值增加5G-2 ;</p><p>4、2.4.6 组网设备WIFI配置 增加字段bandwidth，encrypt ；字段radio值增加5G-2 ;</p><p>5、2.4.8 组网设备周边WIFI信息增加字段bandwidth , wifiStandard；</p><p>2\.4.9 组网设备漫游配置 增加staStopDetect 方法和接口函数，增加rxRate，txRate字段；字段radio值增加5G-2;</p><p>2\.4.12 组网设备操控指令 增加ipv6Control 方式和函数；增加ipv6Enable字段；</p><p>8、修订2.4.14组网设备Mesh章节内容；</p><p>9、2.4.15 组网设备网络限速增加limitedUrl ,</p><p>deviceLimitMode , deviceLimitWeek ,deviceLimitTimeOffset1 ,</p><p>deviceLimitTimeOffset2 字段；</p>||
|V1.4.4|2022-02-18|<p>1、2.4.6 章节增加5G优先配置方法</p><p>2、 增加2.4.18组网设备Qos配置章节</p><p>3、 增加2.4.19组网VLAN配置章节</p><p>4、 2.4.9章节，2.4.12章节，2.4.16章节增加seqId字段，如配置携带seqId下发，对应的event上报携带下发的seqId；</p><p>5、 2.4.6组网设备WIFI配置 5G优先增加limitTime字段；</p>||
|V1.4.5|2022-03-08|<p>1、 2.4.5 下挂设备主动上报消息增加rssi，rxRate和txRate 字段；</p><p>2、 修订2.4.9章节漫游配置xindex修改为LowRSSIPeriod字段，删除rssiCheckDelta字段；</p><p>3、 2.4.9章节dismissTime有漫游配置修订为通过断开连接下发；</p>||
|V1.4.6|2022-05-20|<p>4、 修改workingMode 错误值；</p><p>5、 2.4.14 简化mesh拓扑结构，去掉下挂设备信息</p><p>6、 修订2.4.12日志上报 logTime的定义</p><p>7、 2.4.7 radio 补充5G-2 频段</p><p>8、 2.4.3 返回参数缺少portInfoList</p>||
|V1.4.7|2022-6-15|<p>修订2.4.17章节（wifi感知功能）：</p><p>更新csi管控接口</p><p>更新csi类名</p><p>补充csi数据结构说明</p>|张依东|
|V1.4.7|2022-6-28|<p>修订2.4.17章节（wifi感知功能）：</p><p>函数入参变量名未定义</p>|张依东|
|V1.4.7|2022-7-13|<p>修订2.4.17章节（wifi感知功能）：</p><p>函数新增字段radio，区分频段</p>|张依东|
|V1.4.7|2022-8-8|<p>修订2.4.17章节（wifi感知功能）：</p><p>新增csi数据传输方式：共享内存</p>|张依东|
|V1.4.8|2022-8-15|<p>1、2.4.1 radio5 字段增加3 无WI-FI终端枚举值；uplinkType字段增加值类型</p><p>2、2.4.4 章节增加fiberStatus、transceiverTemperature、supplyVottage、biasCurrent、distance字段</p>|魏飞|
|V1.4.8|2022-8-22|<p>修订2.4.17章节（wifi感知功能）：</p><p>新增csi数据传输方式：netlink</p>|张依东|
|V1.4.8|2022-8-30|<p>1、2.4.1 章节增加ponSn字段</p><p>2、2.4.6章节获取wifi配置信息增加wlanMac字段</p>|魏飞|
|V1.4.8|2022-9-22|<p>修订2.4.17章节（wifi感知功能）：</p><p>更新csi stream数据结构；</p><p>删除对象setCSIConfig、getCSIConfig、setCSIDriver、getCSIDriver；</p><p>新增对象getChipInfo </p>|张依东|
|V1.4.9|2022-11-23|<p>修订2.4.1章节增加wifiSpecification字段</p><p>修订2.4.3章节，增加LAN侧地址配置，LAN侧绑定下挂设备，获取LAN信息</p><p>修订2.4.12增加组播转单播管控开关</p><p>2\.4.6 WIFI配置修改ssidStandard 值定义，2.4.8周边WIFI修改wifiStandard值定义</p>|魏飞|





