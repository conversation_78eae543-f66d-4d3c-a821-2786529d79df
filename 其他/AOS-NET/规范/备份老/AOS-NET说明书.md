
## 终端要求

### **1.1 适配说明（重要）**

    AOS-NET版本适配需关注 1和2章节，适配详情请关注2.4章节附件ubus接口适配文档。
	业务插件研发组网设备能力集调用需关注3和4章节。

### **1.2系统环境要求**

    应采用Linux操作系统，Kernel版本宜不低于4.5，支持Namespace机制和Control Groups V2功能，支持openssl和zlib。预留RAM容量应不低于32MB，预留FLASH容量应不低于16MB。
	需要预留一个可读写路径/tmpapp/(**且要求该目录下文件断电保留，恢复出厂保留预置文件**)，用于业务插件安装，升级和预置软件更新，**该路径可通过hynetwork.conf文件进行配置**，由组网业务插件管理进程读取。

### **1.3终端硬件要求**

    建议终端硬件配置要求：Flash：128M及以上，RAM：128M及以上。

### **1.4依赖开源库**

    AOS-NET依赖的curl、paho-mqtt和pcap等库已经包含在protocol目录中。若厂商若本地已有相同库文件，可替代使用自己本地库。

    考虑到各厂商设备本地的ubus能力集、openssl和zlib库版本差异，为防止兼容问题，AOS-NET不额外提供。请厂商申请AOS-NET时，主动提供芯片方案工具链和设备本地的ubus能力集、openssl和zlib（库和对应头文件）

    AOS-NET依赖ubus能力集：

    ubus库: libubus.a libubus.so
			ubox库: libubox.a libubox.so
			json-c库: libblobmsg_json.a libblobmsg_json.so  libjson_script.so libjson-c.a libjson-c.so
			zlib建议使用1.2.8版本及以上版本
			openssl 库建议使用openssl1.1.1以上版本

## AOS-NET目录和功能

### **2.1AOS-NET输出目录**

组网中间件AOS-NET软件包输出目录结构：

AOS-NET

```
├── bin #bin目录
│ └── aosnet
│ ├── start.sh #插件启动脚本
│ ├── mangePlugin #纳管插件
│ ├── pluginDaemon #业务插件管理模块
│ └── uniwebPlugin #统一web后端插件
├── tmpapp #预留可读写目录
│ ├── dpiPlugin_V2.1.0 #预置探针插件(**预置文件)**
│ └── dpiPlugin
│ ├── vasdocker_V2.1.0 #家宽优先插件**（预置文件）**
│ └── vasdocket
│ ├── wifiRouterWeb #统一web前端文件
│ ├── iot_wifi_mesh #极简协议管理进程文件[(**可选预置文件**)]{.mark}
│ ├── ssl #纳管和探针所需证书文件目录(**预置文件)**
│ └── aosnetlib
│ ├── libalsec.so #一机一密库文件(**预置文件)**
│ └── libahsapi.so #组网能力集接口库文件(**预置文件)**
│ └── libsec2_router.so #家宽优选能力集接口库文件(**预置文件)**
[│ └── libandlink_route.so #中控能力库文件(**可选预置文件**)]{.mark}
├── inc
│ └── ahsapi_interface.h #组网能力集接口库头文件
├── etc
│ ├── hynetwork.conf #组网纳管和探针插件配置文件
│ └── ahsapi.conf #组网中间集能力集配置文件
├── protocol #第三方开源库目录，根据需要使用
│ ├── include
│ └── lib
│ ├── libcoap.so #coap开源库
│ ├── libcurl.so #curl开源库
│ ├── libpcap.so #pcap开源库
│ └── libpaho-mqtt3as.so #MQTT开源库
│ └── libjson.so #cjson开源库
│
├── doc #说明书
```

注：厂商需要把 /tmpapp/aosnetlib/路径加到动态库默认链接路径（类似 lib；usr/lib的默认路径），或者通过软连接方式把aosnetlib下面so库文件链接的lib 或usr/lib 目录使插件程序可以找到对应的库文件

## **AOS-NET运行目录**

适配AOS-NET软件及路径要求见表2.2-1所示：

|  路径&nbsp;<br>                                                                     |  功能说明                                     |
|:----------------------------------------------------------------------------------|:------------------------------------------|
|  /bin/aosnet/managePlugin                                                         |  组网纳管插件                                   |
|  /bin/aosnet/pluginDaemon                                                         |  业务插件管理进程                                 |
|  /bin/aosnet/start.sh                                                             |  组网插件启动守护脚本                               |
|  &nbsp;/tmpapp/aosnetlib/libahsapi.so&nbsp;                                       |  组网能力集库文件                                 |
|  /tmpapp/aosnetlib/libsec2_router.so                                              |  家宽优选能力集库文件                               |
|  /tmpapp/aosnetlib/libalsec.so&nbsp;                                              |  一机一密库文件                                  |
|  /tmpapp/aosnetlib/libandlink_route.so                                            |  中控能力库文件                                  |
| 依赖库文件                                                                             |                                           |
| /tmpapp/aosnetlib/libahsapi.so                                                    | 组网能力集库文件                                  |
| /tmpapp/aosnetlib/libsec2_router.so                                               | 家宽优选能力集库文件                                |
| /tmpapp/aosnetlib/libalsec.so&nbsp;                                               | 一机一密库文件                                   |
| /tmpapp/aosnetlib/libandlink_route.so                                             | 中控能力库文件                                   |
| 三方依赖库文件                                                                           |                                           |
| /lib/libcoap.so&nbsp;                                                             | coap开源库                                   |
| /lib/libcurl.so                                                                   | curl开源库                                   |
| /lib/libpcap.so                                                                   | pcap开源库                                   |
| /lib/libpaho-mqtt3as.so                                                           | MQTT开源库                                   |
| /lib/libjson.so                                                                   | cjson开源库                                  |
| 头文件                                                                               |                                           |
| /include/ahsapi_interface.h                                                       | 组网能力集API接口头文件                             |
| /etc/hynetwork.conf&nbsp;                                                         | 组网插件配置文件                                  |
| &nbsp;/etc/ahsapi.conf                                                            | &nbsp;组网能力集配置文件                           |
| 预置业务插件                                                                            |                                           |
| &nbsp;/tmpapp/dpiPlugin_V2.1.0/dpiPlugin                                          | 预置探针插件，插件名dpiPlugin，版本号为V2.1.0            |
| /tmpapp/vasdocker_V2.1.0/vasdocker                                                | 预置家宽优选插件，插件名vasdocker，版本号为V2.1.0          |
| /tmpapp/iot_wifi_mesh/iot_wifi_mesh&nbsp;                                         | 极简协议管理进程文件                                |
| 证书文件                                                                              |                                           |
| /tmpapp/ssl/xxx&nbsp;                                                             | &nbsp;ssl目录下证书需根据hynetwork.conf中路径放置对应目录下 |  

注：

厂商需要把 /tmpapp/aosnetlib/ 路径加到动态库默认链接路径（类似 /lib
；usr/lib的默认路径），或者通过软连接方式把aosnetlib
下面so库文件链接的lib 或usr/lib 目录使插件程序可以找到对应的库文件

其中第三方业务插件安装包格式要求为tar.gz压缩包，必须包含主程序文件，非必须包含主程序所需的配置文件、脚本文件、so动态库文件等，详见第3章。

## **AOS-NET配置文件说明**

配置格式为 **配置项=配置内容**，具体配置各项见表2.3-1所示：

表2.3-1 中间件配置文件说明

## **2.4** **基础组网授权软件运行说明**

启动脚本start.h有多个配置命令，可通过“-H|--help”来查看。
除启动脚本sart.h外，在调试时、厂商也可手动启动managePlugin或dpiPlugin等业务插件。插件启动时，也可同样设置多个命令。
##### 屏幕打印模式：
"-D | enable as debug mode(debug_print)"
example:
```
./start.sh -D
./managePlugin -D
./dpiPlugin -D
```

##### 静默时间调试模式：
"-T | enable as debug test(debug_print, and set quiet_time %X(unit:s))"
example: 
```
./start.sh -T 1
./ managePlugin -T 2
./dpiPlugin -T 3
```
备注：
if5 DM-规范要求在[0,24h]区间随机值；
if8-周期上报功能，启动时设置了随机静默值[0,300];默认开启、上报周期为30min;
if8-业务监测上报功能，启动时设置了随机静默值[0,300]; 默认关闭;
if8-配置指令强校验:reportInterval基数为600s, sampleInterval基数为60s; 
(设置setDmDebug(XX)后，静默值改为XX，并去除配置指令强校验)

##### 统一认证配网模式（仅managePlugin）：
"-N | enable: set IOT net with Unified authentication(by GW)"
example: 
```
./start.sh -N 1
./managePlugin -N
```



## 2.5 UBUS接口适配

详细UBUS接口适配开发内容请使用《中国移动智慧家庭智能组网操作系统集成规范V1.5.0（0809）》

## 2.6 统一web适配项功能

(1)	当前统一web功能采用插件方式运行，通过pluginDaemon进程完成管控
(2)	当前默认端口已采用80端口
(3)	相关文件已采用固定地址，放置于tmpapp目录下，文件夹命名为uniweb_VX.X.X,包含前端页面文件及后端功能插件
(4)	厂家需要按照最新的ubus接口规范实现全部接口功能
(5)	当前we侧的mesh相关功能调用为厂家自己的mesh接口
(6)	需要厂家提供自己公司图标,格式为 PNG, 高度为 32像素，长度厂家可以按照自己的要求裁剪,命名为 logo.png,分别放置到前端页面的web/wifiRouterWeb和web/wifiRouterMobile路径。要求背景适配当前的 web 页面的主题
(7)	需要自行放置4个pdf文档：《用户许可协议.pdf》，《隐私声明.pdf》，《开源软件申明.pdf》，《安全隐患.pdf》到前端页面的web/wifiRouterWeb路径

## 2.7 进程隔离功能说明
进程隔离功能嵌入在pluginDaemon插件应用中，为其功能子模块，该模块依赖Linux Namespace和Cgroup机制（包括Cgroup和Cgroup2）。由于涉及较多系统调用和内核功能依赖，该功能使用需要如下支持项：
1. 支持clone()，setns()，unshare()，以及/proc下部分文件权限，由于使用的C库不同，对于uclib等裁剪库则需要支持对应syscall调用方式（ulibc默认不支持setns）；
2. 支持ip link add/set/delete全量功能，若不支持对应指令或支持不全，如ip link add type等，则需要提供相关rtnetlink/netlink库（3.0版本及以上版本）；
3. 需要使用ip link创建veth类型的虚拟网卡，请开启对应的内核config；
4. 内核版本低于4.5的请支持cgroup V1，高于此版本将使用cgroup V2接口。由于V1版本相对混乱，建议内核版本高于4.5；
5. 为控制插件/应用的使用权限和系统调用，采用seccmp + bpf方式跟踪和限制进程,确保容器安全，请开启内核中的CONFIG_SECCMP功能；

## 2.8 极简协议功能说明

(1)  驱动修改，见《移动闪联极简配网方案适配 - 路由器芯片端》；
(2)  修改/etc/hynetwork.conf文件，增加2.4gWlanName字段，此字段是2.4G网卡名称，用于闪联极简协议与内核通信，获取wifi管理帧数据，例如2.4gWlanName=wlan0；
(3)  iwmPlugin进程作为预置插件，被业务插件管理模块统一管理和守护
(4)  使用支持移动闪联极简协议子设备和路由器进行绑定、管控操作（当前阶段需要来杭研进行现场调试）；

# 业务插件开发说明

## **业务插件安装包结构说明**

以智能组网探针插件为例，插件名为dpiPlguin，插件版本号为V2.2.0，安装包名称应为dpiPlguin_V2.2.0.tar.gz，具体目录结构如下：

dpiPlguin_V2.2.0

├─ lib #动态库文件(非开源库)文件夹

│ └──··· #探针插件运行所需动态库文件

├─ include #头文件文件夹

│ └──··· #探针插件运行所需头文件

├─ etc #配置文件文件夹

│ └──··· #探针插件运行配置头文件

├─ dpiPlguin
#探针插件同名运行文件，以是否存在该名称的进程作为运行状态依据

├─ XXX #探针插件运行所需其他文件的文件夹

└──··· #探针插件运行所需其他文件均在此处

## **业务插件开发须知**

1. 业务插件的所有操作（除写操作）均只限于安装目录下，安装路径为*{tmpapp}*/*{pluginName}*\_*{pluginVendor}*。其中*{tmpapp}*需要从/etc/hynetwork.conf中读取，*{pluginName}*为业务插件名称，*{pluginVendor}*为业务插件版本号。
2. 业务插件管理模块启动业务插件的方式为调用业务插件同名运行文件，停用业务插件的方式杀死业务插件同名进程。
3. 业务插件管理模块以是否存在业务插件同名的进程作为判断运行状态的依据。
4. 业务插件所有配置文件和其它运行程序所需文件获取路径都为当前业务插件安装包所在目录下的相对路径。
5. 业务插件涉及设备底层能力接口可通过章节4组网能力集API接口调用，使用API库文件ahsapi.so提供。

# 组网能力集API接口定义

该能力集API为业务插件提供标准调用组网设备能力的接口，组网能力集API接口定义表4-1。具体实现见《中国移动智慧家庭智能组网操作系统集成规范V1.5.0（0809）》

表4-1 组网能力集API接口

| API接口                                                                                          | 说明                                        |
| ------------------------------------------------------------------------------------------------ | ------------------------------------------- |
| 设备基本信息                                                                                     |                                             |
|                                                                                                  |                                             |
| int ahsapi_get_devbasic_info(cJSON \*\*basicInfo);                                               | 获取组网设备基本信息                        |
| 设备硬件信息                                                                                     |                                             |
|                                                                                                  |                                             |
| int ahsapi_get_hardware_info(cJSON \*\*hardWareInfo);                                            | 获取组网设备硬件信息                        |
| 设备端口信息                                                                                     |                                             |
|                                                                                                  |                                             |
| int ahsapi_get_port_info(cJSON \*\*portInfo);                                                    | 获取组网设备端口信息                        |
| int ahsapi_get_lan_info(cJSON \*\*lanInfo)                                                       | 获取组网设备LAN侧地址信息                   |
| int ahsapi_set_lan_config(cJSON \*lanConfig)                                                     | 配置组网设备LAN侧地址信息                   |
| int ahsapi_set_lan_bind_sta_config(cJSON \*lanStaConfig)                                         | 配置组网设备LAN侧绑定STA信息                |
| 设备上行信息                                                                                     |                                             |
|                                                                                                  |                                             |
| int ahsapi_get_uplink_info(cJSON \*\*uplinkInfo);                                                | 获取组网设备上行状态信息                    |
| int ahsapi_set_pppoe(char \*user, char \*pwd);                                                   | 配置pppoe账号密码                           |
| int ahsapi_set_wlan_connect(char \*ssid, char \*pwd, char \*securityMode);                       | 配置wlan上行时接入SSID和密码                |
| int ahsapi_set_work_mode(int mode, int type);                                                    | 配置工作模式                                |
| int ahsapi_set_uplink_rate_config(char \*ifType , char \*reportInterval, char \*sampleInterval); | 配置上行周期和采样                          |
| 下挂设备信息                                                                                     |                                             |
|                                                                                                  |                                             |
| int ahsapi_get_sta_info(cJSON\*\*staInfo);                                                       | 获取下挂设备列表信息                        |
| int ahsapi_get_sta_num(cJSON \*\*staNum);                                                        | 获取下挂设备数量信息                        |
| int ahsapi_set_sta_rate_config(char \*ifType, char \*reportInterval, char \*sampleInterval);     | 配置下挂设备周期和采样                      |
| 设备WIFI信息                                                                                     |                                             |
|                                                                                                  |                                             |
| int ahsapi_get_wifi_info(cJSON \*\*wifiInfo);                                                    | 获取wifi配置信息                            |
| int ahsapi_set_wifi_parameter(cJSON \*wifiParameter);                                            | 配置wifi参数                                |
| int ahsapi_set_wifi_switch(char \*radio, int enable,int index);                                  | 配置wifi开关                                |
| int ahsapi_set_radio_config(CJSON \*radioConfig);                                                | 配置wifi功率                                |
| int ahsapi_set_wps(char \*radio);                                                                | 配置wps                                     |
| int ahsapi_set_5GPreferred(char \*preferredSta，int limitTime)                                   | 配置5G频段优先                              |
| 获取WIFI统计                                                                                     |                                             |
|                                                                                                  |                                             |
| int ahsapi_get_wifi_stats_info(cJSON \*\*wifiStatsInfo);                                         | 获取wifi统计信息                            |
| 设备周边WIFI                                                                                     |                                             |
|                                                                                                  |                                             |
| int ahsapi_get_wlan_neighbor_info(cJSON \*\*wlanNeighborInfo);                                   | 获取周边wifi信息                            |
| 设备漫游                                                                                         |                                             |
|                                                                                                  |                                             |
| int ahsapi_get_roaming_config(cJSON \*\*roamingConfig);                                          | 获取组网设备漫游配置信息                    |
| int ahsapi_set_roaming_config(cJSON \*roamingConfig);                                            | 配置漫游                                    |
| int ahsapi_sta_disconnect(char \*mac，int dismissTime);                                          | 断开该STA 的连接，并限制接入dismissTime毫秒 |
| int ahsapi_sta_rssi(char \*mac, cJSON \*\*staRssi);                                              | 查询组网设备与STA的弱信号                   |
| int ahsapi_sta_stop_detect(char \*mac,long long stopDetectTime)；                                | 停止STA 漫游策略                            |
| int ahsapi_set_band_steering_config(cJSON \*bandSteeringConfig);                                | 配置双频合一                                |
| int ahsapi_get_band_steering_config(cJSON \*\*bandSteeringConfig);                              | 获取双频合一配置                            |
| 设备定时任务                                                                                     |                                             |
|                                                                                                  |                                             |
| int ahsapi_get_all_timed_task(cJSON** allTimedTask);                                             | 获取所有定时任务配置信息                    |
| int ahsapi_add_timed_task(cJSON* timedTask);                                                     | 添加和更新定时任务                          |
| int ahsapi_delete_timed_task(int taskId);                                                        | 删除定时任务                                |
| 设备黑白名单                                                                                     |                                             |
|                                                                                                  |                                             |
| int ahsapi_set_mac_filter(cJSON* macFilter);                                                     | 设置黑白名单                                |
| int ahsapi_get_mac_filter(cJSON** macFilter);                                                    | 获取黑白名单配置                            |
| 设备操控                                                                                         |                                             |
|                                                                                                  |                                             |
| int ahsapi_set_reboot(int controlType);                                                          | 组网设备重启操作                            |
| int ahsapi_set_reset();                                                                          | 组网设备恢复出厂                            |
| int ahsapi_set_led_control(int ledOnOFF);                                                        | 组网设备led灯控                             |
| int ahsapi_set_firmware_upgrade(cJSON * upgrade);                                                | 组网设备固件升级                            |
| int ahsapi_set_lock_net (int lockStatus);                                                        | 配置锁网                                    |
| int ahsapi_set_pmf_ctrl(int pmfEnable);                                                          | 配置管理帧加密                              |
| int ahsapi_get_control_status(cJSON \*\*controlStatus);                                            | 获取led灯控和锁网状态                       |
| int ahsapi_set_hwnat_clean();                                                                    | 清空硬加速链路表                            |
| int ahsapi_set_kernel_count(int count);                                                          | 配置硬加速，软件协议栈数据包数量            |
| int ahsapi_set_dev_log_stats(cJSON \*devLog, char \*logPath , int len)                            | 日志采集配置                                |
| int ahsapi_set_ipv6_control(int ipv6Enable)                                                      | 配置ipv6双栈开关                            |
| int ahsapi_set_multicast_enable( int multicastEnable)                                            | 配置组播转单播开关                          |
| int ahsapi_get_multicast_enable(int \*multicastEnable)                                            | 获取组播转单播开关状态                      |
|                                                                                                  |                                             |
| int ahsapi_set_ntp_calibration(int timeAdjust)                                                   | 配置是否需要进行系统时间校准                |
| 设备配置保存                                                                                     |                                             |
|                                                                                                  |                                             |
| int ajsapi_cfg_add_item(char \*item , char *value);                                               | 配置保存                                    |
| int ahsapi_cfg_get_item(char* item , char \*value, int len);                                      | 配置获取                                    |
| int ahsapi_cfg_delete_item(char \*item);                                                          | 配置删除                                    |
| int ahsapi_get_cfg_path(int filetype, char \*cfgPath, int len);                                   | 获取配置文件路径                            |
| Mesh                                                                                             |                                             |
|                                                                                                  |                                             |
| int ahsapi_map_set_mesh_role(int role);                                                          | 配置mesh角色                                |
| int ahsapi_map_set_mesh_roaming(int enable);                                                     | 配置mesh漫游开关                            |
| int ahsapi_map_get_mesh_topology(cJSON \*\*meshTopology);                                          | 获取mesh拓扑结构                            |
| int ahsapi_map_get_mesh_info(cJSON  \*\*meshInfo);                                                  | 获取mesh 信息                               |
| 组网设备网络限速                                                                                 |                                             |
| int ahsapi_set_device_limit(cJSON \*deviceLimit);                                                 | 配置设备网络限速                            |
| int ahsapi_get_device_limit(cJSON \*\*deviceLimit);                                                | 获取设备网络限速                            |
| 组网设备持续测速                                                                                 |                                             |
| int ahsapi_http_speed_test(cJSON \*speedTest);                                                    | 组网设备 Http持续测速                       |
| 组网设备WIFI信道状态信息(CSI)【可选】                                                            |                                             |
| int ahsapi_set_csi_enable(char \*radio, int csiEnable )                                          | 配置CSI数据采集开关                         |
| int ahsapi_get_csi_enable(char \*radio, int \*csiEnable)                                          | 获取CSI数据采集开关状态                     |
| int ahsapi_add_csi_mac(char \*csiMacEntris)                                                       | 配置CSI采集下挂设备MAC白名单                |
| int ahsapi_del_csi_mac(char \*csiMacEntris)                                                       | 删除CSI采集下挂设备MAC白名单                |
| int ahsapi_get_csi_mac(cJSON \*\*csiMacEntris)                                                     | 获取CSI MAC地址白名单                       |
| int ahsapi_set_csi_config(cJSON \*\*)                                                              | 配置CSI数据上报间隔时间和frame类型          |
| int ahsapi_get_csi_config(cJSON \*\*csiConfig )                                                    | 获取CSI配置信息                             |
| int ahsapi_set_csi_driver(cJSON \*\*)                                                              | 配置csi驱动相关信息                         |
| int ahsapi_get_csi_driver(cJSON \*\*)                                                              | 获取csi驱动相关信息                         |
| int ahsapi_set_csi_speed(cJSON \*\*)                                                               | 配置移动侦测上报速度和上报模式              |
| int ahsapi_get_csi_speed(cJSON \*\*)                                                               | 获取移动侦测上报速度和上报模式              |
| int ahsapi_get_csi_version(cJSON \*\*)                                                             | 获取csi算法库版本                           |
| int ahsapi_set_csi_score(cJSON \*\*, cJSON \*\*)                                                     | 移动侦测功能对设备进行评分，并返回评分      |
| int ahsapi_set_csi_device(cJSON \*\*)                                                              | 设置某STA设备为移动侦测STA                  |
| int ahsapi_get_csi_device(cJSON \*\*)                                                              | 获取移动侦测STA                             |
| 组网设备Qos配置                                                                                  |                                             |
| int ahsapi_set_qos_config(cJSON \*qosConfig)                                                      | 组网设备Qos配置                             |
| int ahsapi_get_qos_config(cJSON \*\*qosConfig)                                                     | 获取组网设备Qos配置信息                     |
| 组网设备 VLAN配置                                                                                |                                             |
| int ahsapi_set_iptv_vlan_config(cJSON \*iptvVlan)                                                 | 组网设IPTV VLAN配置                         |
| int ahsapi_get_iptv_vlan_config(cJSON \*\*iptvVlan )                                               | 获取组网设备IPTV VLAN配置信息               |
| int ahsapi_set_dmz(cJSON \*dmzConfig)                                                             | 设置DMZ配置信息                             |
| int ahsapi_get_dmz(cJSON \*\*dmzConfig)                                                            | 获取DMZ配置信息                             |
| int ahsapi_set_upnp (cJSON \*upnpConfig)                                                          | 设置upnp配置信息                            |


## **Event事件上报**


```
typedef int (*ahsapi_event_cb_handler_t)(char *event_type, cJSON *event_data); //事件回调函数声明
```



```
int **ahsapi_event_listen_callback**(ahsapi_event_cb_handler_tevent_cb); //设置事件监听回调函数
```



```
int **ahsapi_event_listen_cancel**(); //取消事件监听
```


组网设备部分信息变化主动上报，或需要长时间执行的操作指令结果上报，其参数内容如表4.20-1所示：

表4.1-1 设备事件上报消息参数



# 附录A 统一WEB功能表

---

  

---

# 附录B 修订记录

s