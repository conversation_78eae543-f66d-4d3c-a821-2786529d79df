# 目录 {#目录 .TOC-标题1}

[1、组网中间件架构说明
[3](#aos-net组网中间件架构说明)](#aos-net组网中间件架构说明)

[1.1 组网中间件系统架构
[3](#aos-net组网中间件系统架构)](#aos-net组网中间件系统架构)

[2、组网中间件ubus封装定义
[3](#aos-net组网中间件ubus封装定义)](#aos-net组网中间件ubus封装定义)

[2.1 ubus介绍 [3](#ubus介绍)](#ubus介绍)

[2.2 ubus 依赖动态库 [4](#ubus-依赖动态库)](#ubus-依赖动态库)

[2.3 ubus 消息总线工作原理
[4](#ubus-消息总线工作原理)](#ubus-消息总线工作原理)

[2.4 ubus接口定义 [5](#ubus接口定义)](#ubus接口定义)

[2.4.1 组网设备基本信息 [5](#组网设备基本信息)](#组网设备基本信息)

[2.4.2 组网设备硬件信息 [9](#组网设备硬件信息)](#组网设备硬件信息)

[2.4.3 组网设备端口信息 [10](#组网设备端口信息)](#组网设备端口信息)

[2.4.4 组网设备上行信息 [12](#组网设备上行信息)](#组网设备上行信息)

[2.4.5 组网设备下挂设备信息
[18](#组网设备下挂设备信息)](#组网设备下挂设备信息)

[2.4.6 组网设备WIFI配置 [23](#组网设备wifi配置)](#组网设备wifi配置)

[2.4.7 组网设备WIFI统计信息
[29](#组网设备wifi统计信息)](#组网设备wifi统计信息)

[2.4.8 组网设备周边WIFI信息
[31](#组网设备周边wifi信息)](#组网设备周边wifi信息)

[2.4.9 组网设备漫游配置 [33](#组网设备漫游配置)](#组网设备漫游配置)

[2.4.10 组网设备定时任务 [38](#组网设备定时任务)](#组网设备定时任务)

[2.4.11 组网设备配置黑白名单
[40](#组网设备配置黑白名单)](#组网设备配置黑白名单)

[2.4.12 组网设备操控指令 [41](#组网设备操控指令)](#组网设备操控指令)

[2.4.13 组网设备配置保存 [45](#组网设备配置保存)](#组网设备配置保存)

[2.4.14 组网设备Mesh [46](#组网设备mesh)](#组网设备mesh)

[2.4.15 组网设备网络限速 [53](#组网设备网络限速)](#组网设备网络限速)

[2.4.16 组网设备持续测速 [56](#组网设备持续测速)](#组网设备持续测速)

[[2.4.17 组网设备WIFI信道状态信息(CSI)【可选】]{.mark}
[57](#组网设备wifi信道状态信息csi可选)](#组网设备wifi信道状态信息csi可选)

[2.4.18 组网设备Qos配置 [65](#组网设备qos配置)](#组网设备qos配置)

[2.4.19 组网设备 VLAN配置 [68](#组网设备-vlan配置)](#组网设备-vlan配置)

[附录A 响应码 [70](#附录a-响应码)](#附录a-响应码)

[附录B 修订记录 [70](#附录b-修订记录)](#附录b-修订记录)

# 1、AOS-NET组网中间件架构说明

## 1.1 AOS-NET组网中间件系统架构 
AOS-NET组网中间件系统架构如图1.1 所示，组网能力集有ubusd、ahsapi、ahsapd
三部分组成

ubusd是ubus 总线消息服务器，负责进程间消息转递。

ahsapi向插件提供调用方法,负责将插件调用消息通过ubusd消息服务器转递给ahsapd。

ahsapd
模块需厂商实现，厂家应将组网设备基础能力封装成ubus接口能力模块ahsapd，提供对组网设备管理配置能力的开放。

![](.\images/media/image1.emf)

图1.1 AOS-NET组网中间件架构

# 2、AOS-NET组网中间件ubus封装定义
# 2.1 ubus介绍
ubus作为进程件的通信总线，采用linux socket进程间通信方式做了封装：

1)  ubus提供了一个socket server：ubusd

2)  ubus对client和server之间通信的消息格式进行了定义：client和server都必须将消息封装成json消息格式。

3)  ubus对client端的消息处理抽象出"对象（object）"和"方法（method）"及"事件（event）"的概念。一个对象中包含多个方法，client需要向server注册收到特定json事件时的处理方法。对象和方法都有自己的名字，发送请求方只需在消息中指定要调用的对象和方法的名字即可。

# 2.2 ubus 依赖动态库 
使用ubus时需要引用一些动态库，主要包括：

1)  libubus.so：ubus向外部提供的编程接口，例如创建socket，进行监听和连接，发送消息等接口函数。

2)  libubox.so：ubus向外部提供的编程接口，例如等待和读取消息。

3)  libblobmsg.so，libjson.so：提供了封装和解析json数据的接口，编程时不需要直接使用libjson.so，而是使用libblobmsg.so提供的更灵活的接口函数。

# 2.3 ubus 消息总线工作原理 {#ubus-消息总线工作原理 .QB标题2}

以图2.1为例，下面描述ubus消息总线的工作原理

![](.\images/media/image2.emf)

图2.1 ubus消息工作原理实例

client2进行请求的整个过程为：

1）client1向ubusd注册了两个对象："interface"和"test"，其中"interface"对象中注册了两个method："getlanip"和"setlanip"，对应的处理函数分别为func1()和func2()。"test"对象中注册了两个method："hello"和"watch"，对应的处理函数分别为func3()和func4()。

2）接着创建一个client2用来与client1通信，注意，两个client之间不能直接通信，需要经ubusd（server）中转。

3）消息发到server后，server根据对象名找到应该将请求转发给client1，然后将消息发送到client1，client1进而调用func2()接受参数并处理，如果处理完成后需要回复client2，则发送回复消息。

Ubus内部工作机制如下图2.2所示，client1注册对象和方法，其实可认为是服务提供端，只不过对于ubusd来讲是一个socket
client，client2去调用client1注册的方法。

![](.\images/media/image3.emf)

图2.2 ubus消息工作机制

# 2.4 ubus接口定义 {#ubus接口定义 .QB标题2}

本节定义了AOS-NET组网中间件需要实现的ubus封装接口对象和通知信息及详细参数

插件调用接口为ahsapi向应用层提供调用接口，不涉及跟厂商开发ahsapd的封装内容

ubus connct连接path默认为null

# 2.4.1 组网设备基本信息 {#组网设备基本信息 .QB标题3}

插件调用接口：

int ahsapi_get_devbasic_info(cJSON \*\* basicInfo)
//获取组网设备基本信息

basicInfo 为JSON字符串 ，包含参数内容如下表

获取设备基本信息对象方法定义如表2.4.1 -1所示：

表2.4.1 -1 设备基本信息对象方法

+-----------+--------+------------+---------------------+-------------+
| 对象名    | 方法名 | 参数       | 返回参数            | 描述        |
+===========+========+============+=====================+=============+
| ahsapd.   | getBas | 无         | <table>             | 获取组网设  |
| basic     | icInfo |            | <colgroup>          | 备基本信息  |
|           |        |            | <col                |             |
|           |        |            | st                  |             |
|           |        |            | yle="width: 46%" /> |             |
|           |        |            | <col                |             |
|           |        |            | s                   |             |
|           |        |            | tyle="width: 7%" /> |             |
|           |        |            | <col                |             |
|           |        |            | st                  |             |
|           |        |            | yle="width: 46%" /> |             |
|           |        |            | </colgroup>         |             |
|           |        |            | <thead>             |             |
|           |        |            | <tr                 |             |
|           |        |            | class="header">     |             |
|           |        |            | <th><p>{</p>        |             |
|           |        |            | <p>"ahsapd.         |             |
|           |        |            | basic": {</p>       |             |
|           |        |            | <p>"productToken":  |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"deviceType":    |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"deviceVendor":  |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"deviceModel":   |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"deviceSn":      |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"ponSn": "       |             |
|           |        |            | ",</p>              |             |
|           |        |            | <p>"cmei":          |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"softw           |             |
|           |        |            | areVersion":"",</p> |             |
|           |        |            | <p                  |             |
|           |        |            | >"hardwareVersion": |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"ahsapdVersion": |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"authId":        |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"devKey":        |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"dmKey":         |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p                  |             |
|           |        |            | >"powerSupplyMode": |             |
|           |        |            | 3,</p>              |             |
|           |        |            | <p>"pr              |             |
|           |        |            | ovinceCode":"",</p> |             |
|           |        |            | <p>"captureEth":    |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"deviceMac":     |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"ipAddress":     |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"ipv6IPAddress": |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"meshType":      |             |
|           |        |            | 1,</p>              |             |
|           |        |            | <p>"meshRole":      |             |
|           |        |            | 1,</p>              |             |
|           |        |            | <p>"uplinkType":    |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"workingMode":   |             |
|           |        |            | "",</p>             |             |
|           |        |            | <p>"radio5":        |             |
|           |        |            | 1，</p>             |             |
|           |        |            | <p>"pppoeUser":     |             |
|           |        |            | ""，</p>            |             |
|           |        |            | <p>"wifiSp          |             |
|           |        |            | ecification":7,</p> |             |
|           |        |            | <p>"reserve":       |             |
|           |        |            | ""</p>              |             |
|           |        |            | <p>}</p>            |             |
|           |        |            | <p>}</p></th>       |             |
|           |        |            | <th></th>           |             |
|           |        |            | <th>1.0</th>        |             |
|           |        |            | </tr>               |             |
|           |        |            | </thead>            |             |
|           |        |            | <tbody>             |             |
|           |        |            | </tbody>            |             |
|           |        |            | </table>            |             |
+-----------+--------+------------+---------------------+-------------+

获取设备基本信息返回值参数详细定义如下表：

> 表2.4.1 - 2 设备基本信息返回值参数

+---------+-----------+----------+-------------------------+---------+
| 参数名  | 类型      | 读写权限 | 描述                    | 是      |
|         |           |          |                         | 否必须  |
+=========+===========+==========+=========================+=========+
| produ   | String    | 只读     | 设备在开发者            | Y       |
| ctToken |           |          | 门户注册的产品类型Token |         |
+---------+-----------+----------+-------------------------+---------+
| dev     | String    | 只读     | 家庭终端的产品类型，数  | Y       |
| iceType |           |          | 字家庭合作伙伴门户申请  |         |
+---------+-----------+----------+-------------------------+---------+
| devic   | String    | 只读     | 智能组网                | Y       |
| eVendor |           |          | 终端的设备厂商编码，数  |         |
|         |           |          | 字家庭合作伙伴门户申请  |         |
+---------+-----------+----------+-------------------------+---------+
| devi    | String    | 只读     | 智能组网                | Y       |
| ceModel |           |          | 终端的产品型号编码，数  |         |
|         |           |          | 字家庭合作伙伴门户申请  |         |
+---------+-----------+----------+-------------------------+---------+
| d       | String    | 只读     | 智能组网                | Y       |
| eviceSn |           |          | 终端的序列号，序列号格  |         |
|         |           |          | 式要求见《中国移动家庭  |         |
|         |           |          | 智能组网终端技术规范》  |         |
+---------+-----------+----------+-------------------------+---------+
| ponSn   | String    | 只读     | PON序                   | N       |
|         |           |          | 列号，光网关终端必选，  |         |
|         |           |          | 按网关技术规范要求生成  |         |
+---------+-----------+----------+-------------------------+---------+
| cmei    | String    | 只读     | 智能组网终端唯一标识    | Y       |
+---------+-----------+----------+-------------------------+---------+
| s       | String    | 只读     | 智能组网固件版本        | Y       |
| oftware |           |          |                         |         |
| Version |           |          |                         |         |
+---------+-----------+----------+-------------------------+---------+
| h       | String    | 只读     | 智能组网硬件版本        | Y       |
| ardware |           |          |                         |         |
| Version |           |          |                         |         |
+---------+-----------+----------+-------------------------+---------+
| ahsapd  | String    | 只读     | ahsapd版本              | Y       |
| Version |           |          |                         |         |
+---------+-----------+----------+-------------------------+---------+
| authId  | String    | 只读     | 设备认证唯              | Y       |
|         |           |          | 一标识，数字家庭合作伙  |         |
|         |           |          | 伴门户申请,一机一密必选 |         |
+---------+-----------+----------+-------------------------+---------+
| devKey  | String    | 只读     | 设备密钥，一机一密必选  | Y       |
+---------+-----------+----------+-------------------------+---------+
| dmKey   | String    | 只读     | 终端                    | Y       |
|         |           |          | 公司key(可选,预留接口)  |         |
+---------+-----------+----------+-------------------------+---------+
| p       | Number（  | 只读     | 供电方式（枚举值）：    | Y       |
| owerSup | uint32）  |          |                         |         |
| plyMode |           |          | 1：电池供电             |         |
|         |           |          |                         |         |
|         |           |          | 2：POE供电              |         |
|         |           |          |                         |         |
|         |           |          | 3: 市电供电             |         |
|         |           |          |                         |         |
|         |           |          | 4：USB供电              |         |
|         |           |          |                         |         |
|         |           |          | 5：其它供电             |         |
+---------+-----------+----------+-------------------------+---------+
| provi   | String    | 只读     | 设备预置省份码          | Y       |
| nceCode |           |          |                         |         |
+---------+-----------+----------+-------------------------+---------+
| cap     | String    | 只读     | 设备网                  | Y       |
| tureEth |           |          | 口名，用于数据抓包监测  |         |
+---------+-----------+----------+-------------------------+---------+
| de      | String    | 只读     | 智能组网终端Ma          | Y       |
| viceMac |           |          | c地址，格式大写去掉冒号 |         |
+---------+-----------+----------+-------------------------+---------+
| ip      | String    | 只读     | 智能组网                | Y       |
| Address |           |          | 终端ip，非ip终端则为空  |         |
+---------+-----------+----------+-------------------------+---------+
| ipv6IP  | String    | 只读     | 智能组网终端IPv6地址    | Y       |
| Address |           |          |                         |         |
+---------+-----------+----------+-------------------------+---------+
| m       | Numbe     | 只读     | 0：非mesh终端           | Y       |
| eshType | r(uint32) |          |                         |         |
|         |           |          | 1：easyMesh，           |         |
|         |           |          |                         |         |
|         |           |          | 2：其它mesh             |         |
+---------+-----------+----------+-------------------------+---------+
| m       | Numbe     | 只读     | 0：非mesh终端           | Y       |
| eshRole | r(uint32) |          |                         |         |
|         |           |          | 1：controller           |         |
|         |           |          |                         |         |
|         |           |          | 2：agent                |         |
|         |           |          |                         |         |
|         |           |          | 3：AC                   |         |
|         |           |          |                         |         |
|         |           |          | 4: AP                   |         |
+---------+-----------+----------+-------------------------+---------+
| upl     | String    | 只读     | 上行                    | Y       |
| inkType |           |          | 接入方式：WLAN、Ethern  |         |
|         |           |          | et、PLC、Cable、Optical |         |
|         |           |          | Ethernet、G             |         |
|         |           |          | PON、XGPON、XGSPON、PoE |         |
+---------+-----------+----------+-------------------------+---------+
| work    | Numb      | 只读     | 设备工作模式，          | Y       |
| ingMode | er(int32) |          |                         |         |
|         |           |          | 0: 桥接模式             |         |
|         |           |          |                         |         |
|         |           |          | 1：路由模式             |         |
|         |           |          |                         |         |
|         |           |          | 2：中继模式             |         |
+---------+-----------+----------+-------------------------+---------+
| radio5  | Numb      | 只读     | 支持频段：              | Y       |
|         | er(int32) |          |                         |         |
|         |           |          | 0：单频                 |         |
|         |           |          |                         |         |
|         |           |          | 1：双频                 |         |
|         |           |          |                         |         |
|         |           |          | 2：三频                 |         |
|         |           |          |                         |         |
|         |           |          | 3：无WI-FI终端          |         |
+---------+-----------+----------+-------------------------+---------+
| pp      | String    | 只读     | pppoe宽带账号,pppoe拨号 | Y       |
| poeUser |           |          | 是必选，非拨号时值为空  |         |
+---------+-----------+----------+-------------------------+---------+
| wif     | Numb      | 只读     | w                       | Y       |
| iSpecif | er(int32) |          | ifi的无线规格采用枚举值 |         |
| ication |           |          |                         |         |
|         |           |          | 1：                     |         |
|         |           |          | AC1200（空间流：2.4G：2 |         |
|         |           |          | x 2， 5G： 2 x 2）      |         |
|         |           |          |                         |         |
|         |           |          | 2：                     |         |
|         |           |          | AC1900（空间流：2.4G：3 |         |
|         |           |          | x 3， 5G： 3 x 3）      |         |
|         |           |          |                         |         |
|         |           |          | 3：AC2100               |         |
|         |           |          | （空间流：2.4G：2 x 2， |         |
|         |           |          | 5G： 4 x4）             |         |
|         |           |          |                         |         |
|         |           |          | 4：AC2100               |         |
|         |           |          | （空间流：2.4G：2 x 2， |         |
|         |           |          | 5G： 2 x2）             |         |
|         |           |          |                         |         |
|         |           |          | 5：AC2600               |         |
|         |           |          | （空间流：2.4G：4 x 4， |         |
|         |           |          | 5G： 4 x4）             |         |
|         |           |          |                         |         |
|         |           |          | 6：AX1500               |         |
|         |           |          | （空间流：2.4G：2 x 2， |         |
|         |           |          | 5G： 2 x2）             |         |
|         |           |          |                         |         |
|         |           |          | 7：AX1800               |         |
|         |           |          | （空间流：2.4G：2 x 2， |         |
|         |           |          | 5G： 2 x2）             |         |
|         |           |          |                         |         |
|         |           |          | 8：AX3000               |         |
|         |           |          | （空间流：2.4G：2 x 2， |         |
|         |           |          | 5G： 2 x2）             |         |
|         |           |          |                         |         |
|         |           |          | 9: AX3000               |         |
|         |           |          | （空间流：2.4G：2 x 2， |         |
|         |           |          | 5G:4 x 4）              |         |
|         |           |          |                         |         |
|         |           |          | 10: AX3600              |         |
|         |           |          | （空间流：2.4G：4 x 4， |         |
|         |           |          | 5G： 4 x 4）            |         |
|         |           |          |                         |         |
|         |           |          | 11: AX5400              |         |
|         |           |          | （空间流：2.4G：2 x 2， |         |
|         |           |          | 5G： 4 x4）             |         |
|         |           |          |                         |         |
|         |           |          | 12:                     |         |
|         |           |          | AX6000（空间流：2.4G：4 |         |
|         |           |          | x 4， 5G： 4 x4）       |         |
+---------+-----------+----------+-------------------------+---------+
| reserve | String    | 只读     | 厂                      | N       |
|         |           |          | 商特殊标识字段,预留可选 |         |
+---------+-----------+----------+-------------------------+---------+

# 2.4.2 组网设备硬件信息 {#组网设备硬件信息 .QB标题3}

插件调用接口：

int ahsapi_get_hardware_info(cJSON \*\* hardWareInfo)
//获取组网设备硬件信息

hardwareInfo为JSON字符串，包含参数内容如下表

获取设备硬件信息对象方法定义如表2.4.2-1所示：

表2.4.2 -1设备硬件信息对象方法

+-----------+-----------+-----------+---------------------+------------+
| 对象名    | 方法名    | 参数      | 返回参数            | 描述       |
+===========+===========+===========+=====================+============+
| ahsapd.   | ge        | 无        | <table>             | 获取组网设 |
| hardware  | tHardware |           | <colgroup>          | 备硬件信息 |
|           |           |           | <col                |            |
|           |           |           | st                  |            |
|           |           |           | yle="width: 33%" /> |            |
|           |           |           | <col                |            |
|           |           |           | st                  |            |
|           |           |           | yle="width: 33%" /> |            |
|           |           |           | <col                |            |
|           |           |           | st                  |            |
|           |           |           | yle="width: 33%" /> |            |
|           |           |           | </colgroup>         |            |
|           |           |           | <thead>             |            |
|           |           |           | <tr                 |            |
|           |           |           | class="header">     |            |
|           |           |           | <th><p>{</p>        |            |
|           |           |           | <p>"ahsapd.         |            |
|           |           |           | hardware": {</p>    |            |
|           |           |           | <p>"cpuType":       |            |
|           |           |           | "",</p>             |            |
|           |           |           | <p>"flashSize":     |            |
|           |           |           | ,</p>               |            |
|           |           |           | <p>"ramSize":       |            |
|           |           |           | ,</p>               |            |
|           |           |           | <p>                 |            |
|           |           |           | "cpuOccupancyRate": |            |
|           |           |           | ,</p>               |            |
|           |           |           | <p>"ram             |            |
|           |           |           | OccupancyRate":</p> |            |
|           |           |           | <p>}</p>            |            |
|           |           |           | <p>}</p></th>       |            |
|           |           |           | <th></th>           |            |
|           |           |           | <th>1.0</th>        |            |
|           |           |           | </tr>               |            |
|           |           |           | </thead>            |            |
|           |           |           | <tbody>             |            |
|           |           |           | </tbody>            |            |
|           |           |           | </table>            |            |
+-----------+-----------+-----------+---------------------+------------+

获取设备硬件信息返回值参数详细定义如下表：

> 表2.4.2 -- 2 设备硬件信息返回值参数

  ---------------------------------------------------------------------------------------
  参数名             类型             读写权限    描述                        是否必须
  ------------------ ---------------- ----------- --------------------------- -----------
  cpuType            String           只读        智能组网终端CPU型号         Y

  flashSize          Number(uint32)   只读        Flash大小（单位：Mbytes）   Y

  ramSize            Number(uint32)   只读        Ram大小（单位：Mbytes）     Y

  cpuOccupancyRate   Number(uint32)   只读        0-100（CPU占用率）          Y

  ramOccupancyRate   Number(uint32)   只读        0-100（RAM占用率）          Y
  ---------------------------------------------------------------------------------------

# 2.4.3 组网设备端口信息 {#组网设备端口信息 .QB标题3}

插件调用接口：

int ahsapi_get_port_info(cJSON \*\* portInfo) //获取组网设备端口信息

int ahsapi_get_lan_info(cJSON \*\* lanInfo) //获取组网设备LAN侧地址信息

int ahsapi_set_lan_config(cJSON \* lanConfig)
//配置组网设备LAN侧地址信息

int ahsapi_set_lan_bind_sta_config(cJSON \*lanStaConfig)
//配置组网设备LAN侧绑定STA信息

portInfo为JSON字符串，包含参数内容如下表

获取设备端口信息对象方法定义如表2.4.3 - 1所示：

> 表2.4.3 -1设备端口信息对象方法

+------------+-----------+-------------+-------------------+---------+
| 对象名     | 方法名    | 参数        | 返回参数          | 描述    |
+============+===========+=============+===================+=========+
| ahsapd.    | ge        | 无          | <table>           | 获      |
| port       | tPortInfo |             | <colgroup>        | 取组网  |
|            |           |             | <col              | 设备端  |
|            |           |             | styl              | 口信息  |
|            |           |             | e="width: 33%" /> |         |
|            |           |             | <col              |         |
|            |           |             | styl              |         |
|            |           |             | e="width: 33%" /> |         |
|            |           |             | <col              |         |
|            |           |             | styl              |         |
|            |           |             | e="width: 33%" /> |         |
|            |           |             | </colgroup>       |         |
|            |           |             | <thead>           |         |
|            |           |             | <tr               |         |
|            |           |             | class="header">   |         |
|            |           |             | <th><p>{</p>      |         |
|            |           |             | <p>"ahsapd.       |         |
|            |           |             | port":{</p>       |         |
|            |           |             | <p>"port          |         |
|            |           |             | InfoList"：[{</p> |         |
|            |           |             | <p>"index": 1     |         |
|            |           |             | ,</p>             |         |
|            |           |             | <p>"portType":    |         |
|            |           |             | 0 ,</p>           |         |
|            |           |             | <p>"portRate":    |         |
|            |           |             | "XXX" ,</p>       |         |
|            |           |             | <p>"portMac":     |         |
|            |           |             | "XXX",</p>        |         |
|            |           |             | <p>"port          |         |
|            |           |             | Connection":X</p> |         |
|            |           |             | <p>}，</p>        |         |
|            |           |             | <p>{</p>          |         |
|            |           |             | <p>"index": 2     |         |
|            |           |             | ,</p>             |         |
|            |           |             | <p>"portType":    |         |
|            |           |             | 1 ,</p>           |         |
|            |           |             | <p>"portRate":    |         |
|            |           |             | "XXX" ,</p>       |         |
|            |           |             | <p>"portMac":     |         |
|            |           |             | "XXX" ,</p>       |         |
|            |           |             | <p>               |         |
|            |           |             | "portConnection": |         |
|            |           |             | X</p>             |         |
|            |           |             | <p>}</p>          |         |
|            |           |             | <p>]</p>          |         |
|            |           |             | <p>}</p>          |         |
|            |           |             | <p>}</p></th>     |         |
|            |           |             | <th></th>         |         |
|            |           |             | <th>1.0</th>      |         |
|            |           |             | </tr>             |         |
|            |           |             | </thead>          |         |
|            |           |             | <tbody>           |         |
|            |           |             | </tbody>          |         |
|            |           |             | </table>          |         |
+------------+-----------+-------------+-------------------+---------+
|            | g         | 无          | {                 | 获取    |
|            | etLanInfo |             |                   | LAN侧配 |
|            |           |             | \"ahsapd.         | 置信息  |
|            |           |             | port\":{          |         |
|            |           |             |                   |         |
|            |           |             | \"lanInfo\"：{    |         |
|            |           |             |                   |         |
|            |           |             | \"lanIpAddress\"  |         |
|            |           |             | :\"************\" |         |
|            |           |             | ,                 |         |
|            |           |             |                   |         |
|            |           |             | \"lanMask\":      |         |
|            |           |             | \"*************\" |         |
|            |           |             | ,                 |         |
|            |           |             |                   |         |
|            |           |             | \"l               |         |
|            |           |             | anAddressStart\": |         |
|            |           |             | \"XXX\" ,         |         |
|            |           |             |                   |         |
|            |           |             | \                 |         |
|            |           |             | "lanAddressEnd\": |         |
|            |           |             | \"XXX\",          |         |
|            |           |             |                   |         |
|            |           |             | \"leaseTime\":    |         |
|            |           |             | 1000，            |         |
|            |           |             |                   |         |
|            |           |             | \"staDevices\":   |         |
|            |           |             | \[{               |         |
|            |           |             |                   |         |
|            |           |             | \"ipAddress\": \" |         |
|            |           |             | \" ,              |         |
|            |           |             |                   |         |
|            |           |             | \"macAddress\":\" |         |
|            |           |             | \",               |         |
|            |           |             |                   |         |
|            |           |             | } ，              |         |
|            |           |             |                   |         |
|            |           |             | {                 |         |
|            |           |             |                   |         |
|            |           |             | \"ipAddress\": \" |         |
|            |           |             | \" ,              |         |
|            |           |             |                   |         |
|            |           |             | \"macAddress\":\" |         |
|            |           |             | \",               |         |
|            |           |             |                   |         |
|            |           |             | }                 |         |
|            |           |             |                   |         |
|            |           |             | \]                |         |
|            |           |             |                   |         |
|            |           |             | }                 |         |
|            |           |             |                   |         |
|            |           |             | }                 |         |
+------------+-----------+-------------+-------------------+---------+
|            | set       | {           | 无                | 配置LA  |
|            | LanConfig |             |                   | N侧地址 |
|            |           | \"lanI      |                   |         |
|            |           | pAddress\": |                   |         |
|            |           | 1 ,         |                   |         |
|            |           |             |                   |         |
|            |           | \           |                   |         |
|            |           | "lanMask\": |                   |         |
|            |           | 0 ,         |                   |         |
|            |           |             |                   |         |
|            |           | \"lanAddr   |                   |         |
|            |           | essStart\": |                   |         |
|            |           | \"XXX\" ,   |                   |         |
|            |           |             |                   |         |
|            |           | \"lanAd     |                   |         |
|            |           | dressEnd\": |                   |         |
|            |           | \"XXX\",    |                   |         |
|            |           |             |                   |         |
|            |           | \"lease     |                   |         |
|            |           | Time\":1000 |                   |         |
|            |           |             |                   |         |
|            |           | }           |                   |         |
+------------+-----------+-------------+-------------------+---------+
|            | setLan    | {           | 无                | 配置    |
|            | StaConfig |             |                   | LAN侧绑 |
|            |           | \"a         |                   | 定下挂  |
|            |           | ction\":1， |                   | 设备列  |
|            |           |             |                   | 表信息  |
|            |           | \"st        |                   |         |
|            |           | aDevices\": |                   |         |
|            |           | \[          |                   |         |
|            |           |             |                   |         |
|            |           | {           |                   |         |
|            |           |             |                   |         |
|            |           | \"i         |                   |         |
|            |           | pAddress\": |                   |         |
|            |           | \" \" ,     |                   |         |
|            |           |             |                   |         |
|            |           | \"macA      |                   |         |
|            |           | ddress\":\" |                   |         |
|            |           | \",         |                   |         |
|            |           |             |                   |         |
|            |           | } ，        |                   |         |
|            |           |             |                   |         |
|            |           | {           |                   |         |
|            |           |             |                   |         |
|            |           | \"i         |                   |         |
|            |           | pAddress\": |                   |         |
|            |           | \" \" ,     |                   |         |
|            |           |             |                   |         |
|            |           | \"macA      |                   |         |
|            |           | ddress\":\" |                   |         |
|            |           | \",         |                   |         |
|            |           |             |                   |         |
|            |           | }\]         |                   |         |
|            |           |             |                   |         |
|            |           | }           |                   |         |
+------------+-----------+-------------+-------------------+---------+

获取组网设备端口返回值详细定义如表2.4.3-2所示：

表2.4.3- 2 设备端口返回值参数

+-----------+-----------+-----------+---------------------+----------+
| 参数名    | 类型      | 读写权限  | 描述                | 是否必须 |
+===========+===========+===========+=====================+==========+
| por       | Array of  | 只读      | 端口号信息的数组    | Y        |
| tInfoList | Object    |           |                     |          |
+-----------+-----------+-----------+---------------------+----------+
| index     | Numbe     | 只读      | 端口index,从1开始   | Y        |
|           | r(uint32) |           |                     |          |
+-----------+-----------+-----------+---------------------+----------+
| portType  | Numbe     | 只读      | 端口类型:0 wan, 1   | Y        |
|           | r(uint32) |           | lan                 |          |
+-----------+-----------+-----------+---------------------+----------+
| portRate  | String    | 只读      | 端口协              | Y        |
|           |           |           | 商速率（单位Mbps）  |          |
+-----------+-----------+-----------+---------------------+----------+
| portMac   | String    | 只读      | 组网设备端口ma      | Y        |
|           |           |           | c，格式大写去掉冒号 |          |
+-----------+-----------+-----------+---------------------+----------+
| portC     | Numbe     | 只读      | 端口连接情况:       | Y        |
| onnection | r(uint32) |           | 0已连接 1未连接     |          |
+-----------+-----------+-----------+---------------------+----------+
| lanInfo   | Object    | 只读      | Lan                 | Y        |
|           |           |           | 侧端口信            |          |
|           |           |           | 息，包含lan地址信息 |          |
|           |           |           | 和绑定下挂设备信息  |          |
+-----------+-----------+-----------+---------------------+----------+
| lan       | String    | 读写      | Lan 侧IP地址        | Y        |
| IpAddress |           |           |                     |          |
+-----------+-----------+-----------+---------------------+----------+
| lanMask   | String    | 读写      | Lan侧子网掩码       | Y        |
+-----------+-----------+-----------+---------------------+----------+
| lanAdd    | String    | 读写      | La                  | Y        |
| ressStart |           |           | n侧DHCP分配起始地址 |          |
+-----------+-----------+-----------+---------------------+----------+
| lanA      | String    | 读写      | La                  | Y        |
| ddressEnd |           |           | n侧DHCP分配结束地址 |          |
+-----------+-----------+-----------+---------------------+----------+
| leaseTime | Numbe     | 读写      | 租约时间            | Y        |
|           | r(uint64) |           |                     |          |
+-----------+-----------+-----------+---------------------+----------+
| action    | Numbe     | 只写      | Lan侧绑定下挂       | Y        |
|           | r(uint32) |           | 设备操作，枚举值：  |          |
|           |           |           |                     |          |
|           |           |           | 1：添加             |          |
|           |           |           |                     |          |
|           |           |           | 2：修改             |          |
|           |           |           |                     |          |
|           |           |           | 3：删除             |          |
+-----------+-----------+-----------+---------------------+----------+
| s         | Array of  | 读写      | Lan侧绑             | N        |
| taDevices | Object    |           | 定下挂设备信息列表  |          |
+-----------+-----------+-----------+---------------------+----------+
| ipAddress | String    | 读写      | 下挂设备的IP        | N        |
|           |           |           | Address；该字段在下 |          |
|           |           |           | 挂设备数非零时必选  |          |
+-----------+-----------+-----------+---------------------+----------+
| m         | String    | 读写      | 下挂设              | N        |
| acAddress |           |           | 备的MAC；该字段在下 |          |
|           |           |           | 挂设备数非零时必选  |          |
+-----------+-----------+-----------+---------------------+----------+

# 2.4.4 组网设备上行信息 {#组网设备上行信息 .QB标题3}

插件调用接口：

int ahsapi_get_uplink_info(cJSON \*\*uplinkInfo) //获取组网设备上行信息

int ahsapi_set_pppoe(char \*user, char \*pwd) //配置pppoe账号密码

int ahsapi_set_wlan_connect(char \*ssid, char \*pwd,char \*
securityMode) //配置wlan上行时接入ssid和密码

int ahsapi_set_work_mode(int mode，int type)
//配置组网设备工作模式和上网类型

int ahsapi_set_uplink_rate_config（char\* ifType ,char \*reportInterval
,char \* sampleInterval）//配置上行口平台接口、周期和采样时间

uplinkInfo为JSON字符串，包含参数内容如下表

获取组网设备上行状态信息对象方法定义如表2.4.4-1所示：

> 表2.4.4 -1设备上行状态信息对象方法

+-----------+-----------+-----------+---------------------+-----------+
| 对象名    | 方法名    | 参数      | 返回参数            | 描述      |
+===========+===========+===========+=====================+===========+
| ahsa      | getU      | 无        | <table>             | 获取组网  |
| pd.uplink | plinkInfo |           | <colgroup>          | 设备上行  |
|           |           |           | <col                | 状态信息  |
|           |           |           | st                  |           |
|           |           |           | yle="width: 42%" /> |           |
|           |           |           | <col                |           |
|           |           |           | st                  |           |
|           |           |           | yle="width: 24%" /> |           |
|           |           |           | <col                |           |
|           |           |           | st                  |           |
|           |           |           | yle="width: 32%" /> |           |
|           |           |           | </colgroup>         |           |
|           |           |           | <thead>             |           |
|           |           |           | <tr                 |           |
|           |           |           | class="header">     |           |
|           |           |           | <th><p>{</p>        |           |
|           |           |           | <p>"ahsapd.uplink": |           |
|           |           |           | {</p>               |           |
|           |           |           | <p>"uplinkType":    |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"uplinkMac": "   |           |
|           |           |           | ",</p>              |           |
|           |           |           | <p>"gatewayMac":    |           |
|           |           |           | " ",</p>            |           |
|           |           |           | <p>"workingMode":   |           |
|           |           |           | X,</p>              |           |
|           |           |           | <p>"internetType":  |           |
|           |           |           | ,</p>               |           |
|           |           |           | <p>"pppoeUser":     |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"pppoePwd":      |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"runningTime":   |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"radio":         |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"ssid":          |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"channel":       |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"noise":"",</p>  |           |
|           |           |           | <p>"snr": "",</p>   |           |
|           |           |           | <p>"rssi":          |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"fiberStatus":   |           |
|           |           |           | 5,</p>              |           |
|           |           |           | <p>"fiberTXPower":" |           |
|           |           |           | ",</p>              |           |
|           |           |           | <p>"fiberRXPower":" |           |
|           |           |           | "，</p>             |           |
|           |           |           | <p>"transcei        |           |
|           |           |           | verTemperature":256 |           |
|           |           |           | ,</p>               |           |
|           |           |           | <p>"sup             |           |
|           |           |           | plyVottage":10,</p> |           |
|           |           |           | <p>"biasCurrent":10 |           |
|           |           |           | ,</p>               |           |
|           |           |           | <p>"distance":1     |           |
|           |           |           | ,</p>               |           |
|           |           |           | <p>"rxRate":        |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"txRate":"",</p> |           |
|           |           |           | <p>"rxRate_rt":     |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"txRate_rt":     |           |
|           |           |           | "",</p>             |           |
|           |           |           | <                   |           |
|           |           |           | p>"totalBytesSent": |           |
|           |           |           | ,</p>               |           |
|           |           |           | <p>"t               |           |
|           |           |           | otalBytesReceived": |           |
|           |           |           | ,</p>               |           |
|           |           |           | <p>                 |           |
|           |           |           | "totalPacketsSent": |           |
|           |           |           | ,</p>               |           |
|           |           |           | <p>"tot             |           |
|           |           |           | alPacketsReceived": |           |
|           |           |           | ,</p>               |           |
|           |           |           | <p>"errorsSent":    |           |
|           |           |           | ,</p>               |           |
|           |           |           | <                   |           |
|           |           |           | p>"errorsReceived": |           |
|           |           |           | ,</p>               |           |
|           |           |           | <p>"d               |           |
|           |           |           | iscardPacketsSent": |           |
|           |           |           | ,</p>               |           |
|           |           |           | <p>"disca           |           |
|           |           |           | rdPacketsReceived": |           |
|           |           |           | ,</p>               |           |
|           |           |           | <                   |           |
|           |           |           | p>"ipv6ConnStatus": |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"                |           |
|           |           |           | ipv6IPAddressOrigin |           |
|           |           |           | ": "",</p>          |           |
|           |           |           | <p>"ipv6IPAddress": |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"ipv6            |           |
|           |           |           | DNSServers":"",</p> |           |
|           |           |           | <p>"ipv6Prefix      |           |
|           |           |           | DelegationEnabled": |           |
|           |           |           | 1,</p>              |           |
|           |           |           | <p>                 |           |
|           |           |           | "ipv6PrefixOrigin": |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"ipv6Prefix":    |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"ipv6            |           |
|           |           |           | PrefixPltime":,</p> |           |
|           |           |           | <p>                 |           |
|           |           |           | "ipv6PrefixVltime": |           |
|           |           |           | ,</p>               |           |
|           |           |           | <p>"d               |           |
|           |           |           | efaultIPv6Gateway": |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"dsliteEnable":  |           |
|           |           |           | 1，</p>             |           |
|           |           |           | <p>"rateArray":     |           |
|           |           |           | [</p>               |           |
|           |           |           | <p>{</p>            |           |
|           |           |           | <p>"interface": "   |           |
|           |           |           | " ,</p>             |           |
|           |           |           | <p>"averRxRate":    |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"                |           |
|           |           |           | averTxRate":"",</p> |           |
|           |           |           | <p>"maxRxRate":     |           |
|           |           |           | "",</p>             |           |
|           |           |           | <p>"maxTxRate":     |           |
|           |           |           | ""</p>              |           |
|           |           |           | <p>}</p>            |           |
|           |           |           | <p>]</p>            |           |
|           |           |           | <p>}</p>            |           |
|           |           |           | <p>}</p></th>       |           |
|           |           |           | <th></th>           |           |
|           |           |           | <th>1.0</th>        |           |
|           |           |           | </tr>               |           |
|           |           |           | </thead>            |           |
|           |           |           | <tbody>             |           |
|           |           |           | </tbody>            |           |
|           |           |           | </table>            |           |
+-----------+-----------+-----------+---------------------+-----------+
|           | setPppoe  | {         | 无                  | 配置pppo  |
|           |           |           |                     | e账号密码 |
|           |           | \"ppp     |                     |           |
|           |           | oeUser\": |                     |           |
|           |           | \"\",     |                     |           |
|           |           |           |                     |           |
|           |           | \"pp      |                     |           |
|           |           | poePwd\": |                     |           |
|           |           | \"\"      |                     |           |
|           |           |           |                     |           |
|           |           | }         |                     |           |
+-----------+-----------+-----------+---------------------+-----------+
|           | wl        | {         | 无                  | 配        |
|           | anConnect |           |                     | 置WLAN上  |
|           |           | \"        |                     | 联时组网  |
|           |           | ssid\":\" |                     | 设备接入  |
|           |           | \",       |                     | 的SSID名  |
|           |           |           |                     | 称和密码  |
|           |           | \         |                     |           |
|           |           | "pwd\":\" |                     |           |
|           |           | \"，      |                     |           |
|           |           |           |                     |           |
|           |           | \         |                     |           |
|           |           | "security |                     |           |
|           |           | Mode\":\" |                     |           |
|           |           | \"}       |                     |           |
+-----------+-----------+-----------+---------------------+-----------+
|           | setWo     | {         | 无                  | 配置组    |
|           | rkingMode |           |                     | 网设备工  |
|           |           | \"wo      |                     | 作模式和  |
|           |           | rkingMode |                     | 上网方法  |
|           |           | \":1      |                     |           |
|           |           |           |                     |           |
|           |           | \         |                     |           |
|           |           | "internet |                     |           |
|           |           | Type\"：0 |                     |           |
|           |           |           |                     |           |
|           |           | }         |                     |           |
+-----------+-----------+-----------+---------------------+-----------+
|           | s         | {         | 无                  | 配置上    |
|           | etUplinkR |           |                     | 行网口速  |
|           | ateConfig | \"in      |                     | 率运算所  |
|           |           | terface\" |                     | 需周期和  |
|           |           | :\"IF6\", |                     | 采样时间  |
|           |           |           |                     |           |
|           |           | \"repo    |                     |           |
|           |           | rtInterva |                     |           |
|           |           | l\":\"\", |                     |           |
|           |           |           |                     |           |
|           |           | \"sam     |                     |           |
|           |           | pleInterv |                     |           |
|           |           | al\":\"\" |                     |           |
|           |           |           |                     |           |
|           |           | }         |                     |           |
+-----------+-----------+-----------+---------------------+-----------+

获取组网设备上行状态返回值详细定义如表2.4.4-2所示：

表2.4.4 - 2设备上行状态返回值参数

+-------------+-----------+----------+----------------------+--------+
| 参数名      | 类型      | 读写权限 | 描述                 | 是     |
|             |           |          |                      | 否必须 |
+=============+===========+==========+======================+========+
| uplinkType  | String    | 只读     | 当前的上行接入方式： | Y      |
|             |           |          |                      |        |
|             |           |          | WLAN、Ethernet、     |        |
|             |           |          | PLC、Cable、Optical  |        |
|             |           |          | Etbernet             |        |
|             |           |          | 、GPON               |        |
|             |           |          | 、XGPON、XGSPON、PoE |        |
+-------------+-----------+----------+----------------------+--------+
| uplinkMac   | String    | 只读     | 设备上行m            | Y      |
|             |           |          | ac，格式大写去掉冒号 |        |
+-------------+-----------+----------+----------------------+--------+
| gatewayMac  | String    | 只读     | 设备                 | Y      |
|             |           |          | 上行网元（网关）mac, |        |
|             |           |          | 格式大写去掉冒号,    |        |
|             |           |          | 非PPPoE拨号时必选    |        |
+-------------+-----------+----------+----------------------+--------+
| workingMode | Numbe     | 读写     | AP工作模式：         | Y      |
|             | r(uint32) |          |                      |        |
|             |           |          | 0：桥接，默认        |        |
|             |           |          |                      |        |
|             |           |          | 1：路由              |        |
|             |           |          |                      |        |
|             |           |          | 2：中继              |        |
+-------------+-----------+----------+----------------------+--------+
| i           | Numbe     | 读写     | AP上网方法：         | Y      |
| nternetType | r(uint32) |          |                      |        |
|             |           |          | 0: dhcp (默认)       |        |
|             |           |          |                      |        |
|             |           |          | 1: pppoe             |        |
|             |           |          |                      |        |
|             |           |          | 2: static            |        |
+-------------+-----------+----------+----------------------+--------+
| pppoeUser   | String    | 读写     | pppoe                | N      |
|             |           |          | 宽带账号,pppoe拨号是 |        |
|             |           |          | 必选，非拨号时值为空 |        |
+-------------+-----------+----------+----------------------+--------+
| pppoePwd    | String    | 读写     | pppoe密码            | N      |
+-------------+-----------+----------+----------------------+--------+
| runningTime | String    | 只读     | 设备运行             | Y      |
|             |           |          | 时长，时间单位S(秒)  |        |
+-------------+-----------+----------+----------------------+--------+
| radio       | String    | 只读     | WLAN上联时           | N      |
|             |           |          | 使用的频段，取值2.4G |        |
|             |           |          | 或5G；设备工作于WLAN |        |
|             |           |          | 上联时，该字段必选； |        |
+-------------+-----------+----------+----------------------+--------+
| ssid        | String    | 读写     | WLAN上联时接入的SSID | N      |
|             |           |          | 名称；设备工作于WLAN |        |
|             |           |          | 上联时，该字段必选； |        |
+-------------+-----------+----------+----------------------+--------+
| pwd         | String    | 读写     | WL                   | N      |
|             |           |          | AN上联时接入的SSID的 |        |
|             |           |          | 密码，不应显示此参数 |        |
+-------------+-----------+----------+----------------------+--------+
| s           | String    | 只写     | 认证                 | N      |
| ecurityMode |           |          | 加密模式，枚举取值： |        |
|             |           |          |                      |        |
|             |           |          | None                 |        |
|             |           |          |                      |        |
|             |           |          | WEP-64               |        |
|             |           |          |                      |        |
|             |           |          | WEP-128              |        |
|             |           |          |                      |        |
|             |           |          | WPA-Personal         |        |
|             |           |          |                      |        |
|             |           |          | WPA2-Personal        |        |
|             |           |          |                      |        |
|             |           |          | MIXED-WPAPSK2        |        |
|             |           |          |                      |        |
|             |           |          | WPA3-SAE             |        |
|             |           |          |                      |        |
|             |           |          | MIXED-WPA2WPA3       |        |
+-------------+-----------+----------+----------------------+--------+
| channel     | String    | 只读     | WL                   | N      |
|             |           |          | AN上联时的工作信道号 |        |
|             |           |          | ，如存在多信道，引用 |        |
|             |           |          | ","逗号分隔，例："1  |        |
|             |           |          | ,6"；设备工作于WLAN  |        |
|             |           |          | 上联时，该字段必选； |        |
+-------------+-----------+----------+----------------------+--------+
| noise       | String    | 只读     | WLAN上               | N      |
|             |           |          | 联时的噪声强度，单位 |        |
|             |           |          | dBm；设备工作于WLAN  |        |
|             |           |          | 上联时，该字段必选； |        |
+-------------+-----------+----------+----------------------+--------+
| snr         | String    | 只读     | WL                   | N      |
|             |           |          | AN上联时的信噪比，单 |        |
|             |           |          | 位dB；设备工作于WLAN |        |
|             |           |          | 上联时，该字段必选； |        |
+-------------+-----------+----------+----------------------+--------+
| rssi        | String    | 只读     | WLAN上联时的         | N      |
|             |           |          | 上联信号强度，单位为 |        |
|             |           |          | dBm；设备工作于WLAN  |        |
|             |           |          | 上联时，该字段必选； |        |
+-------------+-----------+----------+----------------------+--------+
| fiberStatus | Numbe     | 只读     | 光口连接             | N      |
|             | r(uint32) |          | 状态，采用枚举取值： |        |
|             |           |          |                      |        |
|             |           |          | 0：Up；              |        |
|             |           |          |                      |        |
|             |           |          | 1：Initializing      |        |
|             |           |          |                      |        |
|             |           |          | 2：EstablishingLink  |        |
|             |           |          |                      |        |
|             |           |          | 3：NoSignal          |        |
|             |           |          |                      |        |
|             |           |          | 4：Error             |        |
|             |           |          |                      |        |
|             |           |          | 5：Disabled          |        |
+-------------+-----------+----------+----------------------+--------+
| f           | String    | 只读     | 光口上联时           | N      |
| iberTXPower |           |          | 发射光功率，单位dBm  |        |
|             |           |          | ；上行光接入时必选； |        |
+-------------+-----------+----------+----------------------+--------+
| f           | String    | 只读     | 光口上联时           | N      |
| iberRXPower |           |          | 接收光功率，单位dBm  |        |
|             |           |          | ；上行光接入时必选； |        |
+-------------+-----------+----------+----------------------+--------+
| transceiver | Number    | 只读     | 光模块的工作温度，   | N      |
| Temperature | （int32） |          | 单位：1/256摄氏度；  |        |
+-------------+-----------+----------+----------------------+--------+
| su          | Number（  | 只读     | 光模块的供电电       | N      |
| pplyVottage | uint64）  |          | 压，单位：100毫伏；  |        |
+-------------+-----------+----------+----------------------+--------+
| biasCurrent | Numbe     | 只读     | 光发送机的偏置       | N      |
|             | r(uint64) |          | 电流，单位：2微安；  |        |
+-------------+-----------+----------+----------------------+--------+
| distance    | Number    | 只读     | 对于光网关，         | N      |
|             | (uint64)  |          | 该值为光网关和OLT之  |        |
|             |           |          | 间的距离；单位：米； |        |
|             |           |          |                      |        |
|             |           |          | 对于关路由           |        |
|             |           |          | ，该值为光路由与光网 |        |
|             |           |          | 关之间的距离，单位： |        |
|             |           |          | 米；由光路由参考ITU  |        |
|             |           |          | 规范的测距算法得出； |        |
|             |           |          |                      |        |
|             |           |          | 说明                 |        |
|             |           |          | ：该字段仅对P2MP的光 |        |
|             |           |          | 路由做要求，P2P接入  |        |
|             |           |          | 的光路由该值可为空； |        |
+-------------+-----------+----------+----------------------+--------+
| rxRate      | String    | 只读     | 上行协               | Y      |
|             |           |          | 商接收速率，单位Mbps |        |
+-------------+-----------+----------+----------------------+--------+
| txRate      | String    | 只读     | 上行协               | Y      |
|             |           |          | 商发送速率，单位Mbps |        |
+-------------+-----------+----------+----------------------+--------+
| rxRate_rt   | String    | 只读     | 上                   | Y      |
|             |           |          | 行实时接收速率(Mbps) |        |
+-------------+-----------+----------+----------------------+--------+
| txRate_rt   | String    | 只读     | 上                   | Y      |
|             |           |          | 行实时发送速率(Mbps) |        |
+-------------+-----------+----------+----------------------+--------+
| tot         | Numbe     | 只读     | 上行总发送字节数     | Y      |
| alBytesSent | r(uint64) |          |                      |        |
+-------------+-----------+----------+----------------------+--------+
| totalBy     | Numbe     | 只读     | 上行总接收字节数     | Y      |
| tesReceived | r(uint64) |          |                      |        |
+-------------+-----------+----------+----------------------+--------+
| total       | Numbe     | 只读     | 上行总发送包数       | Y      |
| PacketsSent | r(uint64) |          |                      |        |
+-------------+-----------+----------+----------------------+--------+
| totalPack   | Numbe     | 只读     | 上行总接收包数       | Y      |
| etsReceived | r(uint64) |          |                      |        |
+-------------+-----------+----------+----------------------+--------+
| errorsSent  | Numbe     | 只读     | 上行发送错误包数     | Y      |
|             | r(uint64) |          |                      |        |
+-------------+-----------+----------+----------------------+--------+
| err         | Numbe     | 只读     | 上行接收错误包数     | Y      |
| orsReceived | r(uint64) |          |                      |        |
+-------------+-----------+----------+----------------------+--------+
| discard     | Numbe     | 只读     | 上行发送丢失的包数   | Y      |
| PacketsSent | r(uint64) |          |                      |        |
+-------------+-----------+----------+----------------------+--------+
| discardPack | Numbe     | 只读     | 上行接收丢失的包数   | Y      |
| etsReceived | r(uint64) |          |                      |        |
+-------------+-----------+----------+----------------------+--------+
| ipv         | String    | 只读     | IPv6协议的           | Y      |
| 6ConnStatus |           |          | 连接状态，枚举取值： |        |
|             |           |          | Connected            |        |
|             |           |          |                      |        |
|             |           |          | Unconnected          |        |
+-------------+-----------+----------+----------------------+--------+
| ipv6IPAd    | String    | 只读     | IPv6地址             | Y      |
| dressOrigin |           |          | 分配机制，枚举取值： |        |
|             |           |          |                      |        |
|             |           |          | AutoConfigured       |        |
|             |           |          |                      |        |
|             |           |          | DHCPv6               |        |
|             |           |          |                      |        |
|             |           |          | Static               |        |
+-------------+-----------+----------+----------------------+--------+
| ip          | String    | 只读     | WAN连接的IPv6地址；  | Y      |
| v6IPAddress |           |          |                      |        |
+-------------+-----------+----------+----------------------+--------+
| ipv         | String    | 只读     | DNS Server地址。     | Y      |
| 6DNSServers |           |          |                      |        |
+-------------+-----------+----------+----------------------+--------+
| ipv6P       | Numbe     | 只读     | Prefix               | Y      |
| refixDelega | r(uint32) |          | Delegation使能状态； |        |
| tionEnabled |           |          |                      |        |
|             |           |          | 1:表                 |        |
|             |           |          | 示启用，0:表示禁用。 |        |
+-------------+-----------+----------+----------------------+--------+
| ipv6P       | String    | 只读     | 前缀地址             | Y      |
| refixOrigin |           |          | 分配机制，枚举取值： |        |
|             |           |          |                      |        |
|             |           |          | PrefixDelegation     |        |
|             |           |          |                      |        |
|             |           |          | Static               |        |
+-------------+-----------+----------+----------------------+--------+
| ipv6Prefix  | String    | 只读     | 前缀地址。           | Y      |
+-------------+-----------+----------+----------------------+--------+
| ipv6P       | Numbe     | 只读     | 公告前缀的preferred  | Y      |
| refixPltime | r(uint32) |          | lifetime，单位：秒   |        |
|             |           |          | ；                   |        |
+-------------+-----------+----------+----------------------+--------+
| ipv6P       | Numbe     | 只读     | 公告前缀的valid      | Y      |
| refixVltime | r(uint32) |          | lifetime，单位：秒   |        |
|             |           |          | ；                   |        |
+-------------+-----------+----------+----------------------+--------+
| default     | String    | 只读     | IPv6默认网关 ；      | Y      |
| IPv6Gateway |           |          |                      |        |
+-------------+-----------+----------+----------------------+--------+
| d           | Numbe     | 只读     | 是                   | Y      |
| sliteEnable | r(uint32) |          | 否启用DS-lite功能。  |        |
|             |           |          |                      |        |
|             |           |          | 1: TRUE表示启用，0:  |        |
|             |           |          | FALSE表示禁用。      |        |
+-------------+-----------+----------+----------------------+--------+
| rep         | String    | 读写     | 周期时间，           | Y      |
| ortInterval |           |          | 单位s(秒)，周期时间  |        |
|             |           |          | 用于                 |        |
|             |           |          | averRxRate、averTxRa |        |
|             |           |          | te、maxRxRate和maxTx |        |
|             |           |          | Rate运算,当周期值为0 |        |
|             |           |          | 时停止速率统计       |        |
+-------------+-----------+----------+----------------------+--------+
| sam         | String    | 读写     | 数据                 | Y      |
| pleInterval |           |          | 采样间隔，单位s(秒)  |        |
|             |           |          | ,                    |        |
|             |           |          | 采                   |        |
|             |           |          | 样间隔只用于统计收发 |        |
|             |           |          | 速率信息，用于计算ma |        |
|             |           |          | xRxRate和maxTxRate运 |        |
|             |           |          | 算，当周期值和采样值 |        |
|             |           |          | 都为0时停止速率统计  |        |
+-------------+-----------+----------+----------------------+--------+
| rateArray   | Array of  | 只读     | 上行速率             | Y      |
|             | Object    |          | 数组：包含interface; |        |
|             |           |          | av                   |        |
|             |           |          | erRxRate;averTxRate; |        |
|             |           |          | maxRxRate; maxTxRate |        |
|             |           |          | 5个字段              |        |
+-------------+-----------+----------+----------------------+--------+
| interface   | String    | 读写     | 配置平台接口：值为字 | Y      |
|             |           |          | 符串：IF5；IF6；IF8  |        |
+-------------+-----------+----------+----------------------+--------+
| averRxRate  | String    | 只读     | 下挂设备接收流       | Y      |
|             |           |          | 量周期均值，单位Mbps |        |
|             |           |          |                      |        |
|             |           |          | 该字段在             |        |
|             |           |          | 下挂设备数非零时必选 |        |
+-------------+-----------+----------+----------------------+--------+
| averTxRate  | String    | 只读     | 上行发送流           | Y      |
|             |           |          | 量周期均值，单位Mbps |        |
+-------------+-----------+----------+----------------------+--------+
| maxRxRate   | String    | 只读     | 上行发送流量         | Y      |
|             |           |          | 周期最大值，单位Mbps |        |
+-------------+-----------+----------+----------------------+--------+
| maxTxRate   | String    | 只读     | 上行发送流量         | Y      |
|             |           |          | 周期最大值，单位Mbps |        |
+-------------+-----------+----------+----------------------+--------+

# 2.4.5 组网设备下挂设备信息 {#组网设备下挂设备信息 .QB标题3}

插件调用接口：

int ahsapi_get_sta_info(cJSON \*\* staInfo) //获取下挂设备列表信息

int ahsapi_get_sta_num(cJSON \*\* staNum) //获取下挂设备数量信息

int ahsapi_set_sta_rate_config（char\* ifType,char \*reportInterval ,
char \*sampleInterval）//配置下挂设备平台接口、周期和采样时间

staInfo、staNum为JSON字符串，包含参数内容如下表

获取组网设备下挂设备信息对象方法定义如表2.4.5-1所示：

> 表2.4.5 - 1下挂设备信息对象方法

+-----------+-----------+-----------+---------------------+----------+
| 对象名    | 方法名    | 参数      | 返回参数            | 描述     |
+===========+===========+===========+=====================+==========+
| ahsapd.   | g         | 无        | <table>             | 获取组网 |
| sta       | etStaInfo |           | <colgroup>          | 设备下挂 |
|           |           |           | <col                | 设备列表 |
|           |           |           | st                  |          |
|           |           |           | yle="width: 43%" /> |          |
|           |           |           | <col                |          |
|           |           |           | st                  |          |
|           |           |           | yle="width: 23%" /> |          |
|           |           |           | <col                |          |
|           |           |           | st                  |          |
|           |           |           | yle="width: 32%" /> |          |
|           |           |           | </colgroup>         |          |
|           |           |           | <thead>             |          |
|           |           |           | <tr                 |          |
|           |           |           | class="header">     |          |
|           |           |           | <th><p>{</p>        |          |
|           |           |           | <p>"ahsapd. sta":   |          |
|           |           |           | {</p>               |          |
|           |           |           | <p>"staDevices":    |          |
|           |           |           | [</p>               |          |
|           |           |           | <p>{</p>            |          |
|           |           |           | <p>"ipAddress": "   |          |
|           |           |           | " ,</p>             |          |
|           |           |           | <p>                 |          |
|           |           |           | "staIPv6IPAddress": |          |
|           |           |           | " ",</p>            |          |
|           |           |           | <p>"hostName":"     |          |
|           |           |           | ",</p>              |          |
|           |           |           | <p>"staVendor":"    |          |
|           |           |           | ",</p>              |          |
|           |           |           | <p>"macAddress":    |          |
|           |           |           | " " ,</p>           |          |
|           |           |           | <p>"vmacAddress":   |          |
|           |           |           | " ",</p>            |          |
|           |           |           | <p>"staType":"      |          |
|           |           |           | ",</p>              |          |
|           |           |           | <p>"upTime": " "    |          |
|           |           |           | ,</p>               |          |
|           |           |           | <p>"accessTime":    |          |
|           |           |           | " " ,</p>           |          |
|           |           |           | <p>"radio": "       |          |
|           |           |           | ",</p>              |          |
|           |           |           | <p>"ssid":"         |          |
|           |           |           | ",</p>              |          |
|           |           |           | <p>"rssi": " "      |          |
|           |           |           | ,</p>               |          |
|           |           |           | <p>"channel":       |          |
|           |           |           | "",</p>             |          |
|           |           |           | <p>"rxRate": "      |          |
|           |           |           | ",</p>              |          |
|           |           |           | <p>"txRate":"       |          |
|           |           |           | ",</p>              |          |
|           |           |           | <p>"rxRate_rt": "   |          |
|           |           |           | ",</p>              |          |
|           |           |           | <p>"txRate_rt":"    |          |
|           |           |           | ",</p>              |          |
|           |           |           | <p>"totalB          |          |
|           |           |           | ytesSent":1000,</p> |          |
|           |           |           | <p>"totalBytes      |          |
|           |           |           | Received":1000,</p> |          |
|           |           |           | <                   |          |
|           |           |           | p>"totalPacketsSent |          |
|           |           |           | ":1000,</p>         |          |
|           |           |           | <p>"t               |          |
|           |           |           | otalPacketsReceived |          |
|           |           |           | ":1000，</p>        |          |
|           |           |           | <p>"rateArray":     |          |
|           |           |           | [</p>               |          |
|           |           |           | <p>{</p>            |          |
|           |           |           | <p>"interface": "   |          |
|           |           |           | " ,</p>             |          |
|           |           |           | <p>"averRxRate":    |          |
|           |           |           | "",</p>             |          |
|           |           |           | <p>"                |          |
|           |           |           | averTxRate":"",</p> |          |
|           |           |           | <p>"maxRxRate":     |          |
|           |           |           | "",</p>             |          |
|           |           |           | <p>"maxTxRate":     |          |
|           |           |           | ""</p>              |          |
|           |           |           | <p>}</p>            |          |
|           |           |           | <p>]</p>            |          |
|           |           |           | <p>}</p>            |          |
|           |           |           | <p>]</p>            |          |
|           |           |           | <p>}</p></th>       |          |
|           |           |           | <th></th>           |          |
|           |           |           | <th>1.0</th>        |          |
|           |           |           | </tr>               |          |
|           |           |           | </thead>            |          |
|           |           |           | <tbody>             |          |
|           |           |           | </tbody>            |          |
|           |           |           | </table>            |          |
+-----------+-----------+-----------+---------------------+----------+
|           | getStaNum | 无        | {                   | 获取下挂 |
|           |           |           |                     | 设备数量 |
|           |           |           | \"ahsapd. sta\": {  |          |
|           |           |           |                     |          |
|           |           |           | \"wire\": X ,       |          |
|           |           |           |                     |          |
|           |           |           | \"2.4G\": X ,       |          |
|           |           |           |                     |          |
|           |           |           | \"5G\": X           |          |
|           |           |           |                     |          |
|           |           |           | }，                 |          |
|           |           |           |                     |          |
|           |           |           | }                   |          |
+-----------+-----------+-----------+---------------------+----------+
|           | setStaR   | {         | 无                  | 配置     |
|           | ateConfig |           |                     | 下挂设备 |
|           |           | \"in      |                     | 运算上下 |
|           |           | terface\" |                     | 行平均速 |
|           |           | :\"IF6\", |                     | 率和最大 |
|           |           |           |                     | 速率所需 |
|           |           | \"repo    |                     | 要周期和 |
|           |           | rtInterva |                     | 采样时间 |
|           |           | l\":\"\", |                     |          |
|           |           |           |                     |          |
|           |           | \"sam     |                     |          |
|           |           | pleInterv |                     |          |
|           |           | al\":\"\" |                     |          |
|           |           |           |                     |          |
|           |           | }         |                     |          |
+-----------+-----------+-----------+---------------------+----------+

下挂设备信息变化主动event上报定义如下表2.4.5 -2 所示

> 表2.4.5 - 2下挂设备信息主动上报消息定义

+----------------+------------------+------------------------+---------+
| 消息类型       | 消息内容         | 描述                   | 是      |
|                |                  |                        | 否必须  |
+================+==================+========================+=========+
| staUpDown      | {                | 下挂设备上下线信       | Y       |
|                |                  | 息通知,事件触发条件下  |         |
|                | \"hostName\":\"  | 挂设备发生上线和下线时 |         |
|                | \",              |                        |         |
|                |                  |                        |         |
|                | \"staVendor\":\" |                        |         |
|                | \",              |                        |         |
|                |                  |                        |         |
|                | \"macAddress\":  |                        |         |
|                | \" \" ,          |                        |         |
|                |                  |                        |         |
|                | \"vmacAddress\": |                        |         |
|                | \" \",           |                        |         |
|                |                  |                        |         |
|                | \"staType\":\"   |                        |         |
|                | \",              |                        |         |
|                |                  |                        |         |
|                | accessTime\": \" |                        |         |
|                | \" ,             |                        |         |
|                |                  |                        |         |
|                | \"radio\":\"     |                        |         |
|                | \"，             |                        |         |
|                |                  |                        |         |
|                | \"rssi\": \" \"  |                        |         |
|                | ,                |                        |         |
|                |                  |                        |         |
|                | \"rxRate\": \"   |                        |         |
|                | \",              |                        |         |
|                |                  |                        |         |
|                | \"txRate\":\"    |                        |         |
|                | \",              |                        |         |
|                |                  |                        |         |
|                | \"online\": 1    |                        |         |
|                |                  |                        |         |
|                | }                |                        |         |
+----------------+------------------+------------------------+---------+

获取组网设备下挂设备列表返回值参数详细定义如表2.4.5-3所示：

表2.4.5 -3下挂设备列表信息返回值参数

+-----------+-----------+----------+------------------------+---------+
| 参数名    | 类型      | 读写权限 | 描述                   | 是      |
|           |           |          |                        | 否必须  |
+===========+===========+==========+========================+=========+
| s         | Array of  | 只读     | 连接的下挂设备信息数组 | Y       |
| taDevices | Object    |          | ，下挂设备数非零时必选 |         |
+-----------+-----------+----------+------------------------+---------+
| ipAddress | String    | 只读     | 下挂设                 | Y       |
|           |           |          | 备的IPAddress；该字段  |         |
|           |           |          | 在下挂设备数非零时必选 |         |
+-----------+-----------+----------+------------------------+---------+
| staIPv6   | String    | 只读     | 下挂设备的IPV6地       | N       |
| IPAddress |           |          | 址；该字段在下挂设备数 |         |
|           |           |          | 非零，且支持IPv6时必选 |         |
+-----------+-----------+----------+------------------------+---------+
| hostName  | String    | 只读     | 下挂                   | Y       |
|           |           |          | 设备的HostName；该字段 |         |
|           |           |          | 在下挂设备数非零时必选 |         |
+-----------+-----------+----------+------------------------+---------+
| staVendor | String    | 只读     | 下挂设备归属厂商信息   | N       |
+-----------+-----------+----------+------------------------+---------+
| m         | String    | 只读     | 下挂设备MAC地址，      | Y       |
| acAddress |           |          | 格式为不带冒号且大写； |         |
|           |           |          |                        |         |
|           |           |          | 该字段                 |         |
|           |           |          | 在下挂设备数非零时必选 |         |
+-----------+-----------+----------+------------------------+---------+
| vm        | String    | 只读     | 下挂设                 | Y       |
| acAddress |           |          | 备在智能组网终端下的虚 |         |
|           |           |          | 拟MAC，如果未转换，则  |         |
|           |           |          | 和真实MAC一致。格式为  |         |
|           |           |          | 不带冒号且大写；该字段 |         |
|           |           |          | 在下挂设备数非零时必选 |         |
+-----------+-----------+----------+------------------------+---------+
| staType   | String    | 只读     | 下挂设备类型（PC、Ph   | Y       |
|           |           |          | one、TV、Router、IoT、 |         |
|           |           |          | IPC、others）；该字段  |         |
|           |           |          | 在下挂设备数非零时必选 |         |
+-----------+-----------+----------+------------------------+---------+
| upTime    | String    | 只读     | 已连接时间             | Y       |
|           |           |          | ，单位：S(秒)；该字段  |         |
|           |           |          | 在下挂设备数非零时必选 |         |
+-----------+-----------+----------+------------------------+---------+
| a         | String    | 只读     | 下挂设备接入时间       | Y       |
| ccessTime |           |          | ，时间格式：2021-01-07 |         |
|           |           |          | 16:50:30               |         |
+-----------+-----------+----------+------------------------+---------+
| online    | Numbe     | 只读     | 1 : 下挂设备上线(在线) | Y       |
|           | r(uint32) |          |                        |         |
|           |           |          | 0：                    |         |
|           |           |          | 下挂设备下线（不在线） |         |
+-----------+-----------+----------+------------------------+---------+
| radio     | String    | 只读     | WLAN下挂设             | N       |
|           |           |          | 备的接入频段，取值2.4G |         |
|           |           |          | 或5G，5G-2，对非WLAN下 |         |
|           |           |          | 挂设备应填空值；该字段 |         |
|           |           |          | 在下挂设备数非零时必选 |         |
|           |           |          |                        |         |
|           |           |          | 注：对于               |         |
|           |           |          | 三频设备，5G表示5.8G频 |         |
|           |           |          | 段，5G-2表示5.2G频段； |         |
|           |           |          | 双频设备忽略5G-2频段； |         |
+-----------+-----------+----------+------------------------+---------+
| ssid      | String    | 只读     | WLAN下挂               | N       |
|           |           |          | 设备的接入SSID名称，非 |         |
|           |           |          | WLAN下挂设备应填空值； |         |
|           |           |          |                        |         |
|           |           |          | 该字段                 |         |
|           |           |          | 在下挂设备数非零时必选 |         |
+-----------+-----------+----------+------------------------+---------+
| rssi      | String    | 只读     | WLAN下                 | N       |
|           |           |          | 挂设备当前的信号强度， |         |
|           |           |          | 单位为dBm，对非WLAN下  |         |
|           |           |          | 挂设备应填空值；该字段 |         |
|           |           |          | 在下挂设备数非零时必选 |         |
+-----------+-----------+----------+------------------------+---------+
| channel   | String    | 只读     | WLAN下挂设备当前的     | N       |
|           |           |          | 工作信道，单对非WLAN下 |         |
|           |           |          | 挂设备应填空值；该字段 |         |
|           |           |          | 在下挂设备数非零时必选 |         |
+-----------+-----------+----------+------------------------+---------+
| rxRate    | String    | 只读     | 下挂设备协商           | Y       |
|           |           |          | 接收速率；该字段在下挂 |         |
|           |           |          | 设备数非零时必选(Mbps) |         |
+-----------+-----------+----------+------------------------+---------+
| txRate    | String    | 只读     | 下挂设备协商           | Y       |
|           |           |          | 发送速率；该字段在下挂 |         |
|           |           |          | 设备数非零时必选(Mbps) |         |
+-----------+-----------+----------+------------------------+---------+
| rxRate_rt | String    | 只读     | 实时接收速率(Mbps)；   | Y       |
|           |           |          |                        |         |
|           |           |          | 该字段在               |         |
|           |           |          | 下挂设备数非零时必选； |         |
+-----------+-----------+----------+------------------------+---------+
| txRate_rt | String    | 只读     | 实时发送速率(Mbps) ；  | Y       |
|           |           |          |                        |         |
|           |           |          | 该字段在               |         |
|           |           |          | 下挂设备数非零时必选； |         |
+-----------+-----------+----------+------------------------+---------+
| total     | Numbe     | 只读     | 下挂设备总发送字节数   | Y       |
| BytesSent | r(uint64) |          |                        |         |
+-----------+-----------+----------+------------------------+---------+
| totalByte | Numbe     | 只读     | 下挂设备总接收字节数   | Y       |
| sReceived | r(uint64) |          |                        |         |
+-----------+-----------+----------+------------------------+---------+
| totalPa   | Numbe     | 只读     | 下挂设备总发送包数     | Y       |
| cketsSent | r(uint64) |          |                        |         |
+-----------+-----------+----------+------------------------+---------+
| to        | Numbe     | 只读     | 下挂设备总接收包数     | Y       |
| talPacket | r(uint64) |          |                        |         |
| sReceived |           |          |                        |         |
+-----------+-----------+----------+------------------------+---------+
| repor     | String    | 读写     | 周期时间               | Y       |
| tInterval |           |          | ，单位s(秒)，周期时间  |         |
|           |           |          | 用于averRxRate、averT  |         |
|           |           |          | xRate、maxRxRate和maxT |         |
|           |           |          | xRate运算，当周期值为0 |         |
|           |           |          | 时停止下挂设备速率统计 |         |
+-----------+-----------+----------+------------------------+---------+
| sampl     | String    | 读写     | 数                     | Y       |
| eInterval |           |          | 据采样间隔，单位s(秒)  |         |
|           |           |          | ,                      |         |
|           |           |          | 采样间隔只用于         |         |
|           |           |          | 统计收发速率信息，用于 |         |
|           |           |          | 计算maxRxRate和maxTxRa |         |
|           |           |          | te运算，当周期值和采样 |         |
|           |           |          | 值都为0时停止速率统计  |         |
+-----------+-----------+----------+------------------------+---------+
| rateArray | Array of  | 只读     | 速                     | Y       |
|           | Object    |          | 率数组：包含interface; |         |
|           |           |          | averRxRate;averTxRate; |         |
|           |           |          | maxRxRate; maxTxRate   |         |
|           |           |          | 5个字段                |         |
+-----------+-----------+----------+------------------------+---------+
| interface | String    | 读写     | 配置平台接口：值为     | Y       |
|           |           |          | 字符串：IF5；IF6；IF8  |         |
+-----------+-----------+----------+------------------------+---------+
| a         | String    | 只读     | 下挂设备接收           | Y       |
| verRxRate |           |          | 流量周期均值，单位Mbps |         |
+-----------+-----------+----------+------------------------+---------+
| a         | String    | 只读     | 下挂设备发送           | Y       |
| verTxRate |           |          | 流量周期均值，单位Mbps |         |
+-----------+-----------+----------+------------------------+---------+
| maxRxRate | String    | 只读     | 下挂设备发送流         | Y       |
|           |           |          | 量周期最大值，单位Mbps |         |
+-----------+-----------+----------+------------------------+---------+
| maxTxRate | String    | 只读     | 下挂设备发送流         | Y       |
|           |           |          | 量周期最大值，单位Mbps |         |
+-----------+-----------+----------+------------------------+---------+

获取组网设备下挂设备数量信息返回值字段详细定义如表2.4.5-4所示：

表2.4.5- 4下挂设备数量信息返回值字段

  -----------------------------------------------------------------------------
  参数名       类型             读写权限    描述                     是否必须
  ------------ ---------------- ----------- ------------------------ ----------
  wire         Number(uint32)   只读        下挂设备的有线连接数量   Y

  2.4G         Number(uint32)   只读        下挂设备通过wifi         Y
                                            2.4G连接数量             

  5G           Number(uint32)   只读        下挂设备通过wifi 5G      Y
                                            连接数量                 
  -----------------------------------------------------------------------------

# 2.4.6 组网设备WIFI配置 {#组网设备wifi配置 .QB标题3}

插件调用接口：

int ahsapi_get_wifi_info(cJSON \*\* wifiInfo) // 获取wifi配置信息

int ahsapi_set_wifi_parameter(cJSON \* wifiParamter) // 配置wifi参数

int ahsapi_set_wifi_switch(char\* radio, int enable，int index) //
配置wifi开关

int ahsapi_set_radio_config(cJSON \* radioConfig) // 配置wifi射频参数

int ahsapi_set_wps(char\* radio) // 配置wps

int ahsapi_set_5GPreferred(char\* preferredSta，int limitTime)
//配置5G频段优先

wifiInfo、wifiParameter为JSON字符串，包含参数内容如下表

组网设备WiFi配置对象方法定义如表2.4.6 -1所示：

> 表2.4.6 - 1设备WIFI配置信息对象方法

+----------+------------+---------------+--------------------+-------+
| 对象名   | 方法名     | 参数          | 返回参数           | 描述  |
+==========+============+===============+====================+=======+
| ahsapd.  | g          | 无            | <table>            | 获    |
| wifi     | etWifiInfo |               | <colgroup>         | 取组  |
|          |            |               | <col               | 网设  |
|          |            |               | sty                | 备wif |
|          |            |               | le="width: 43%" /> | i配置 |
|          |            |               | <col               | 信息  |
|          |            |               | sty                | 列表  |
|          |            |               | le="width: 23%" /> |       |
|          |            |               | <col               |       |
|          |            |               | sty                |       |
|          |            |               | le="width: 32%" /> |       |
|          |            |               | </colgroup>        |       |
|          |            |               | <thead>            |       |
|          |            |               | <tr                |       |
|          |            |               | class="header">    |       |
|          |            |               | <th><p>{</p>       |       |
|          |            |               | <p>"ahsapd.        |       |
|          |            |               | wifi": {</p>       |       |
|          |            |               | <p>"radios":       |       |
|          |            |               | [</p>              |       |
|          |            |               | <p>{</p>           |       |
|          |            |               | <p>"radio": " "    |       |
|          |            |               | ,</p>              |       |
|          |            |               | <p>"enable": "     |       |
|          |            |               | ",</p>             |       |
|          |            |               | <p>"transmitPower  |       |
|          |            |               | ":" ",</p>         |       |
|          |            |               | <p>"ssidStandard   |       |
|          |            |               | ":"802.11a         |       |
|          |            |               | ",</p>             |       |
|          |            |               | <p>"noiseLevel":"  |       |
|          |            |               | -70",</p>          |       |
|          |            |               | <p>"i              |       |
|          |            |               | nterferencePercent |       |
|          |            |               | ":80,</p>          |       |
|          |            |               | <p>"channel": "    |       |
|          |            |               | " ,</p>            |       |
|          |            |               | <p>"               |       |
|          |            |               | bandwidth"：　</p> |       |
|          |            |               | <p>}</p>           |       |
|          |            |               | <p>]</p>           |       |
|          |            |               | <p                 |       |
|          |            |               | >"configurations": |       |
|          |            |               | [</p>              |       |
|          |            |               | <p>{</p>           |       |
|          |            |               | <p>"radio":"       |       |
|          |            |               | ",</p>             |       |
|          |            |               | <p>"index":        |       |
|          |            |               | ,</p>              |       |
|          |            |               | <p>"enable":       |       |
|          |            |               | ,</p>              |       |
|          |            |               | <p>"ssid":"        |       |
|          |            |               | ",</p>             |       |
|          |            |               | <p>"securityMode": |       |
|          |            |               | " ",</p>           |       |
|          |            |               | <p>"encrypt":      |       |
|          |            |               | ,</p>              |       |
|          |            |               | <p>"pwd":"         |       |
|          |            |               | ",</p>             |       |
|          |            |               | <p>"wlanMac":"     |       |
|          |            |               | ",</p>             |       |
|          |            |               | <p>                |       |
|          |            |               | "maxAssociateNum": |       |
|          |            |               | ,</p>              |       |
|          |            |               | <p>"ssidAdvertisem |       |
|          |            |               | entEnabled":1,</p> |       |
|          |            |               | <p>,</p>           |       |
|          |            |               | <p>"ssidStandard   |       |
|          |            |               | ":"802.11a/n"</p>  |       |
|          |            |               | <p>}</p>           |       |
|          |            |               | <p>]</p>           |       |
|          |            |               | <p>}</p></th>      |       |
|          |            |               | <th></th>          |       |
|          |            |               | <th>1.0</th>       |       |
|          |            |               | </tr>              |       |
|          |            |               | </thead>           |       |
|          |            |               | <tbody>            |       |
|          |            |               | </tbody>           |       |
|          |            |               | </table>           |       |
+----------+------------+---------------+--------------------+-------+
|          | setWif     | {             | 无                 | 配    |
|          | iParameter |               |                    | 置组  |
|          |            | \"conf        |                    | 网设  |
|          |            | igurations\": |                    | 备wif |
|          |            | \[            |                    | i参数 |
|          |            |               |                    |       |
|          |            | {             |                    |       |
|          |            |               |                    |       |
|          |            | \"radio\":\"  |                    |       |
|          |            | \",           |                    |       |
|          |            |               |                    |       |
|          |            | \"index\": ,  |                    |       |
|          |            |               |                    |       |
|          |            | \"enable\": , |                    |       |
|          |            |               |                    |       |
|          |            | \"ssid\":\"   |                    |       |
|          |            | \",           |                    |       |
|          |            |               |                    |       |
|          |            | \"se          |                    |       |
|          |            | curityMode\": |                    |       |
|          |            | \" ,          |                    |       |
|          |            |               |                    |       |
|          |            | \"encrypt\":  |                    |       |
|          |            | ,             |                    |       |
|          |            |               |                    |       |
|          |            | \"pwd\":\"    |                    |       |
|          |            | \",           |                    |       |
|          |            |               |                    |       |
|          |            | \"maxAs       |                    |       |
|          |            | sociateNum\": |                    |       |
|          |            | ,             |                    |       |
|          |            |               |                    |       |
|          |            | \"s           |                    |       |
|          |            | sidAdvertisem |                    |       |
|          |            | entEnabled\": |                    |       |
|          |            | 1,            |                    |       |
|          |            |               |                    |       |
|          |            | \             |                    |       |
|          |            | "ssidStandard |                    |       |
|          |            | \":           |                    |       |
|          |            | \"802.11a/n\" |                    |       |
|          |            |               |                    |       |
|          |            | }\]           |                    |       |
|          |            |               |                    |       |
|          |            | }             |                    |       |
+----------+------------+---------------+--------------------+-------+
|          | set        | {             | 无                 | 配    |
|          | WifiSwitch |               |                    | 置组  |
|          |            | \"radio\": \" |                    | 网设  |
|          |            | \" ,          |                    | 备WiF |
|          |            |               |                    | i开关 |
|          |            | \"enable\":   |                    |       |
|          |            | ，            |                    |       |
|          |            |               |                    |       |
|          |            | \"index\":    |                    |       |
|          |            |               |                    |       |
|          |            | }             |                    |       |
+----------+------------+---------------+--------------------+-------+
|          | setR       | {             | 无                 | 配    |
|          | adioConfig |               |                    | 置组  |
|          |            | \"radio\": \" |                    | 网设  |
|          |            | \" ,          |                    | 备WiF |
|          |            |               |                    | i射频 |
|          |            | \"trans       |                    | 参数  |
|          |            | mitPower\":\" |                    |       |
|          |            | \",           |                    |       |
|          |            |               |                    |       |
|          |            | \"channel\":  |                    |       |
|          |            | \" \" ,       |                    |       |
|          |            |               |                    |       |
|          |            | \"            |                    |       |
|          |            | bandwidth\"： |                    |       |
|          |            |               |                    |       |
|          |            | }             |                    |       |
+----------+------------+---------------+--------------------+-------+
|          | setWps     | {             | 无                 | 配    |
|          |            |               |                    | 置组  |
|          |            | \"radio\": \" |                    | 网设  |
|          |            | \"            |                    | 备WP  |
|          |            |               |                    | S触发 |
|          |            | }             |                    | 频段  |
+----------+------------+---------------+--------------------+-------+
|          | 5          | {             | 无                 | 组网  |
|          | GPreferred |               |                    | 设备  |
|          |            | \"5GPrefer    |                    | 收到  |
|          |            | redSta\":\"\" |                    | 该指  |
|          |            | ，            |                    | 令要  |
|          |            |               |                    | 求时  |
|          |            | \             |                    | ，将  |
|          |            | "limitTime\": |                    | 指定  |
|          |            | ，            |                    | STA临 |
|          |            |               |                    | 时限  |
|          |            | }             |                    | 制接  |
|          |            |               |                    | 入2.4 |
|          |            |               |                    | G，限 |
|          |            |               |                    | 制时  |
|          |            |               |                    | 间建  |
|          |            |               |                    | 议为  |
|          |            |               |                    | limi  |
|          |            |               |                    | tTime |
|          |            |               |                    | (单位 |
|          |            |               |                    | 为毫  |
|          |            |               |                    | 秒，  |
|          |            |               |                    | 缺省  |
|          |            |               |                    | 3000  |
|          |            |               |                    | ms)， |
|          |            |               |                    | 并引  |
|          |            |               |                    | 导切  |
|          |            |               |                    | 换到5 |
|          |            |               |                    | GHz频 |
|          |            |               |                    | 段；  |
|          |            |               |                    | 指令  |
|          |            |               |                    | 只执  |
|          |            |               |                    | 行一  |
|          |            |               |                    | 次。  |
+----------+------------+---------------+--------------------+-------+

wifi信息变化主动event上报定义如下表2.4.6 -2 所示

> 表2.4.6 -- 2 wifi信息变化主动上报消息定义

+----------------+------------------+------------------------+---------+
| 消息类型       | 消息内容         | 描述                   | 是      |
|                |                  |                        | 否必须  |
+================+==================+========================+=========+
| wifiInfoChange | {                | Wifi配置信息改变主动通 | Y       |
|                |                  | 知事件，触发条件当wifi |         |
|                | \"c              | 的ssid和pwd            |         |
|                | onfigurations\": | 改变时，主动上报wifi   |         |
|                | \[               | 配置发生改变的wifi信息 |         |
|                |                  |                        |         |
|                | {                |                        |         |
|                |                  |                        |         |
|                | \"radio\":\" \", |                        |         |
|                |                  |                        |         |
|                | \"index\": ,     |                        |         |
|                |                  |                        |         |
|                | \"enable\": ,    |                        |         |
|                |                  |                        |         |
|                | \"ssid\":\" \",  |                        |         |
|                |                  |                        |         |
|                | \                |                        |         |
|                | "securityMode\": |                        |         |
|                | \" \",           |                        |         |
|                |                  |                        |         |
|                | \"encrypt\": ,   |                        |         |
|                |                  |                        |         |
|                | \"pwd\":\" \",   |                        |         |
|                |                  |                        |         |
|                | \"ma             |                        |         |
|                | xAssociateNum\": |                        |         |
|                | ,                |                        |         |
|                |                  |                        |         |
|                | \"ssidAdverti    |                        |         |
|                | sementEnabled\": |                        |         |
|                |                  |                        |         |
|                | }                |                        |         |
|                |                  |                        |         |
|                | \]               |                        |         |
|                |                  |                        |         |
|                | }                |                        |         |
+----------------+------------------+------------------------+---------+

获取和配置组网设备WiFi参数信息、配置WiFi开关、配置WiFi功率、配置WPS触发频段参数详细定义如表2.4.6 -
3所示：

表2.4.6 - 3设备WIFI配置信息参数

+------------+-----------+----------+---------------------+-----------+
| 参数名     | 类型      | 读写权限 | 描述                | 是否必须  |
+============+===========+==========+=====================+===========+
| radios     | Array of  | 只读     | W                   | Y         |
|            | Object    |          | iFi射频信息的数组； |           |
|            |           |          |                     |           |
|            |           |          | 包含以下字段        |           |
|            |           |          | ：Radio，Enable，T  |           |
|            |           |          | ransmitPower，ssidS |           |
|            |           |          | tandard,nosieLevel, |           |
|            |           |          | interferencePercent |           |
|            |           |          | ,Channel，bandwidth |           |
+------------+-----------+----------+---------------------+-----------+
| radio      | String    | 读写     | Radios数组之一      | Y         |
|            |           |          | ，频段，枚举取值：  |           |
|            |           |          |                     |           |
|            |           |          | 2.4G                |           |
|            |           |          |                     |           |
|            |           |          | 5G                  |           |
|            |           |          |                     |           |
|            |           |          | 5G-2                |           |
|            |           |          |                     |           |
|            |           |          | 注：对于三频设备    |           |
|            |           |          | ，5G表示5.8G频段，5 |           |
|            |           |          | G-2表示5.2G频段；双 |           |
|            |           |          | 频设备忽略5G-2频段  |           |
+------------+-----------+----------+---------------------+-----------+
| enable     | Numbe     | 读写     | Radios数组之        | Y         |
|            | r(uint32) |          | 一，该频段是否启用  |           |
|            |           |          | ，1为启用，0为禁用  |           |
+------------+-----------+----------+---------------------+-----------+
| tra        | String    | 读写     | Radio               | Y         |
| nsmitPower |           |          | s数组之一，WiFi发射 |           |
|            |           |          | 功率级别（百分比）  |           |
+------------+-----------+----------+---------------------+-----------+
| ss         | String    | 只读     | 支持的              | Y         |
| idStandard |           |          | 无线传输标准，取值  |           |
|            |           |          |                     |           |
|            |           |          | 802.11a             |           |
|            |           |          |                     |           |
|            |           |          | 802.11b             |           |
|            |           |          |                     |           |
|            |           |          | 802.11b/g           |           |
|            |           |          |                     |           |
|            |           |          | 802.11b/g/n         |           |
|            |           |          |                     |           |
|            |           |          | 802.11a/n/ac        |           |
|            |           |          |                     |           |
|            |           |          | 802.11b/g/n/ax      |           |
|            |           |          |                     |           |
|            |           |          | 802.11a/n/ac/ax     |           |
|            |           |          |                     |           |
|            |           |          | 802.11a/n           |           |
|            |           |          |                     |           |
|            |           |          | 802.11n             |           |
|            |           |          |                     |           |
|            |           |          | 802.11g             |           |
|            |           |          |                     |           |
|            |           |          | 802.11g/n等         |           |
+------------+-----------+----------+---------------------+-----------+
| noiseLevel | String    | 只读     | 当前信道底噪        | Y         |
+------------+-----------+----------+---------------------+-----------+
| interfere  | Numbe     | 只读     | 当前信              | Y         |
| ncePercent | r(uint32) |          | 道干扰信号的占空比( |           |
|            |           |          |                     |           |
|            |           |          | 当                  |           |
|            |           |          | 前信道使用率（%）， |           |
|            |           |          | 数据范围：0-100；)  |           |
|            |           |          |                     |           |
|            |           |          | 说明                |           |
|            |           |          | ：应包含自身数据收  |           |
|            |           |          | 发和周边干扰之和；  |           |
+------------+-----------+----------+---------------------+-----------+
| channel    | String    | 读写     | Radios数组之一，当  | Y         |
|            |           |          | 前工作信道号,若存在 |           |
|            |           |          | 多个信道，可用","逗 |           |
|            |           |          | 号分隔，例如："1,6" |           |
+------------+-----------+----------+---------------------+-----------+
| bandwidth  | Numbe     | 读写     | 当前Wi-Fi频         | Y         |
|            | r(uint32) |          | 宽，采用枚举取值：  |           |
|            |           |          |                     |           |
|            |           |          | 0：自动频宽         |           |
|            |           |          |                     |           |
|            |           |          | 1：20MHz            |           |
|            |           |          |                     |           |
|            |           |          | 2：40MHz            |           |
|            |           |          |                     |           |
|            |           |          | 3：80MHz            |           |
|            |           |          |                     |           |
|            |           |          | 4：160MHz           |           |
|            |           |          |                     |           |
|            |           |          | 5：80+80MHz         |           |
|            |           |          |                     |           |
|            |           |          | 说明：对于2.4G频    |           |
|            |           |          | 段，自动频宽功能为2 |           |
|            |           |          | 0/40MHz频宽自适应； |           |
|            |           |          | 对于5G频段，自动频  |           |
|            |           |          | 宽功能对应为20/40/  |           |
|            |           |          | 80/160MHz频宽自适应 |           |
+------------+-----------+----------+---------------------+-----------+
| conf       | Array of  | 读写     | WiFi信息的数组,包含 | Y         |
| igurations | Object    |          | radio, index,       |           |
|            |           |          | enable, ssid,       |           |
|            |           |          | securityMode,       |           |
|            |           |          | encrypt,pwd,        |           |
|            |           |          | maxAssociateNum,    |           |
|            |           |          | ssidAdvertise       |           |
|            |           |          | mentEnabled、wlanMa |           |
|            |           |          | c                   |           |
+------------+-----------+----------+---------------------+-----------+
| radio      | String    | 读写     | 频段，枚举取值：    | Y         |
|            |           |          |                     |           |
|            |           |          | 2.4G                |           |
|            |           |          |                     |           |
|            |           |          | 5G                  |           |
|            |           |          |                     |           |
|            |           |          | 5G-2                |           |
|            |           |          |                     |           |
|            |           |          | 注：对于三频设备，  |           |
|            |           |          | 5G表示5.8G频段，5G- |           |
|            |           |          | 2表示5.2G频段；双频 |           |
|            |           |          | 设备忽略5G-2频段；  |           |
+------------+-----------+----------+---------------------+-----------+
| index      | Numbe     | 读写     | SSID                | Y         |
|            | r(uint32) |          | 的索引,2.4G:1-4,5G: |           |
|            |           |          | 5-8,5G-2采          |           |
|            |           |          | 用9-12；值为0时对指 |           |
|            |           |          | 定radio的全局控制； |           |
|            |           |          |                     |           |
|            |           |          | 注：双频            |           |
|            |           |          | 设备忽略5G-2频段；  |           |
+------------+-----------+----------+---------------------+-----------+
| enable     | Numbe     | 读写     | 是否启用            | Y         |
|            | r(uint32) |          | ，1为启用，0为禁用  |           |
+------------+-----------+----------+---------------------+-----------+
| ssid       | String    | 读写     | SSID名称            | Y         |
+------------+-----------+----------+---------------------+-----------+
| se         | String    | 读写     | 认证加              | Y         |
| curityMode |           |          | 密模式，枚举取值：  |           |
|            |           |          |                     |           |
|            |           |          | None                |           |
|            |           |          |                     |           |
|            |           |          | WEP-64              |           |
|            |           |          |                     |           |
|            |           |          | WEP-128             |           |
|            |           |          |                     |           |
|            |           |          | WPA-Personal        |           |
|            |           |          |                     |           |
|            |           |          | WPA2-Personal       |           |
|            |           |          |                     |           |
|            |           |          | MIXED-WPAPSK2       |           |
|            |           |          |                     |           |
|            |           |          | WPA3-SAE            |           |
|            |           |          |                     |           |
|            |           |          | MIXED-WPA2WPA3      |           |
+------------+-----------+----------+---------------------+-----------+
| encrypt    | Numbe     | 读写     | 加                  | Y         |
|            | r(uint32) |          | 密算法，枚举取值：  |           |
|            |           |          |                     |           |
|            |           |          | 0：None:            |           |
|            |           |          |                     |           |
|            |           |          | 1：AES              |           |
|            |           |          |                     |           |
|            |           |          | 2：TKIP             |           |
|            |           |          |                     |           |
|            |           |          | 3：AES+TKIP         |           |
+------------+-----------+----------+---------------------+-----------+
| pwd        | String    | 读写     | WiFi密钥，WEP       | Y         |
|            |           |          | Key或Pre-shared     |           |
|            |           |          | Key，管理平台不应   |           |
|            |           |          | 存储和显示此参数。  |           |
+------------+-----------+----------+---------------------+-----------+
| wlanMac    | String    | 只读     | 每个SSID对应Mac地址 | Y         |
|            |           |          | ，格式大写去掉冒号  |           |
+------------+-----------+----------+---------------------+-----------+
| maxAs      | Numbe     | 读写     | 该                  | Y         |
| sociateNum | r(uint32) |          | SSID最大允许接入的  |           |
|            |           |          | 用户数，0表示不限制 |           |
+------------+-----------+----------+---------------------+-----------+
| ssid       | Numbe     | 读写     | 是否广播SSID，      | Y         |
| Advertisem | r(uint32) |          | 1为广播，0为不广播  |           |
| entEnabled |           |          |                     |           |
+------------+-----------+----------+---------------------+-----------+
| ss         | String    | 读写     | 支持的              | N         |
| idStandard |           |          | 无线传输标准，取值  |           |
|            |           |          |                     |           |
|            |           |          | 802.11a             |           |
|            |           |          |                     |           |
|            |           |          | 802.11b             |           |
|            |           |          |                     |           |
|            |           |          | 802.11b/g           |           |
|            |           |          |                     |           |
|            |           |          | 802.11b/g/n         |           |
|            |           |          |                     |           |
|            |           |          | 802.11a/n/ac        |           |
|            |           |          |                     |           |
|            |           |          | 802.11b/g/n/ax      |           |
|            |           |          |                     |           |
|            |           |          | 802.11a/n/ac/ax     |           |
|            |           |          |                     |           |
|            |           |          | 802.11a/n           |           |
|            |           |          |                     |           |
|            |           |          | 802.11n             |           |
|            |           |          |                     |           |
|            |           |          | 802.11g             |           |
|            |           |          |                     |           |
|            |           |          | 802.11g/n等         |           |
+------------+-----------+----------+---------------------+-----------+
| 5GPr       | String    | 读写     | 下挂设备名          | Y         |
| eferredSta |           |          | 单列表："mac地址/设 |           |
|            |           |          | 备名"格式；多信息之 |           |
|            |           |          | 间以逗号分开，最多  |           |
|            |           |          | 支持32组。示例：\"A |           |
|            |           |          | ABBCCDDEE00/iphone, |           |
|            |           |          | 112233445566/Mi     |           |
+------------+-----------+----------+---------------------+-----------+
| limitTime  | Numbe     | 读写     | 指                  | Y         |
|            | r(uint32) |          | 定STA临时限制接入2  |           |
|            |           |          | .4G，限制时间,单位m |           |
|            |           |          | S（毫秒）缺省值3000 |           |
+------------+-----------+----------+---------------------+-----------+

# 2.4.7 组网设备WIFI统计信息 {#组网设备wifi统计信息 .QB标题3}

插件调用接口：

int ahsapi_get_wifi_stats_info(cJSON \*\* wifiStatsInfo)
//获取组网设备wifi统计信息

wifiStatsInfo 为JSON字符串，包含参数内容如下表

组网设备WiFi统计信息对象方法定义如表2.4.7-1所示：

> 表2.4.7 - 1 设备WIFI统计信息对象方法

+------------+----------+---------+------------------------+---------+
| 对象名     | 方法名   | 参数    | 返回参数               | 描述    |
+============+==========+=========+========================+=========+
| ahsapd.    | getWifiS | 无      | <table>                | 获取组  |
| wifi.stats | tatsInfo |         | <colgroup>             | 网设备  |
|            |          |         | <col                   | WIFI统  |
|            |          |         | style="width: 48%" />  | 计信息  |
|            |          |         | <col                   |         |
|            |          |         | style="width: 19%" />  |         |
|            |          |         | <col                   |         |
|            |          |         | style="width: 32%" />  |         |
|            |          |         | </colgroup>            |         |
|            |          |         | <thead>                |         |
|            |          |         | <tr class="header">    |         |
|            |          |         | <th><p>{</p>           |         |
|            |          |         | <p>"ahsapd.            |         |
|            |          |         | wifi.stats": {</p>     |         |
|            |          |         | <p>"stats": [</p>      |         |
|            |          |         | <p>{</p>               |         |
|            |          |         | <p>"radio": " "        |         |
|            |          |         | ,</p>                  |         |
|            |          |         | <p>"index": ,</p>      |         |
|            |          |         | <p>"ssid":" " ,</p>    |         |
|            |          |         | <p>"totalBytesSent":   |         |
|            |          |         | ,</p>                  |         |
|            |          |         | <p                     |         |
|            |          |         | >"totalBytesReceived": |         |
|            |          |         | ,</p>                  |         |
|            |          |         | <p>"                   |         |
|            |          |         | totalPacketsSent":     |         |
|            |          |         | ,</p>                  |         |
|            |          |         | <p                     |         |
|            |          |         | >"totalPacketsReceived |         |
|            |          |         | ": ,</p>               |         |
|            |          |         | <p>"errorsSent":       |         |
|            |          |         | ,</p>                  |         |
|            |          |         | <p>"errorsReceived":   |         |
|            |          |         | ,</p>                  |         |
|            |          |         | <p                     |         |
|            |          |         | >"discardPacketsSent": |         |
|            |          |         | ,</p>                  |         |
|            |          |         | <p>"discar             |         |
|            |          |         | dPacketsReceived":</p> |         |
|            |          |         | <p>}</p>               |         |
|            |          |         | <p>]</p>               |         |
|            |          |         | <p>}</p></th>          |         |
|            |          |         | <th></th>              |         |
|            |          |         | <th>1.0</th>           |         |
|            |          |         | </tr>                  |         |
|            |          |         | </thead>               |         |
|            |          |         | <tbody>                |         |
|            |          |         | </tbody>               |         |
|            |          |         | </table>               |         |
+------------+----------+---------+------------------------+---------+

获取组网设备WiFi统计信息返回值参数详细定义如表2.4.7 - 2所示：

表2.4.7 - 2设备WIFI统计信息参数

  ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  参数名                   类型             读写权限   描述                                                                                                                           是否必须
  ------------------------ ---------------- ---------- ------------------------------------------------------------------------------------------------------------------------------ ----------
  stats                    Array of Object  只读       每个用户侧接口的报文统计数组（WLAN接口基于SSID统计；以太网接口基于端口统计，并按接口序号从小到大的次序依次出现在响应消息中）   Y

  radio                    String           只读       WLAN接口的频段，取值2.4G或5G,5G-2,以太有线接口填 WAN 或 LAN                                                                    Y

  index                    Number(uint32)   只读       WLAN接口的SSID索引和以太接口序号2.4G:1- 4, 5G: 5- 8,, 5G-2:9-12;WAN 和LAN :从1开始                                             Y

  ssid                     String           只读       WLAN接口的SSID名称，非WLAN接口填空值                                                                                           Y

  totalBytesSent           Number(uint64)   只读       总发送字节数                                                                                                                   Y

  totalBytesReceived       Number(uint64)   只读       总接收字节数                                                                                                                   Y

  totalPacketsSent         Number(uint64)   只读       总发送包数                                                                                                                     Y

  totalPacketsReceived     Number(uint64)   只读       总接收包数                                                                                                                     Y

  errorsSent               Number(uint64)   只读       发送出错的包数                                                                                                                 Y

  errorsReceived           Number(uint64)   只读       接收的错误包数                                                                                                                 Y

  discardPacketsSent       Number(uint64)   只读       发送时丢弃的包数                                                                                                               Y

  discardPacketsReceived   Number(uint64)   只读       接收时丢弃的包数                                                                                                               Y
  ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

# 2.4.8 组网设备周边WIFI信息 {#组网设备周边wifi信息 .QB标题3}

插件调用接口：

int ahsapi_get_wlan_neighbor_info(cJSON \*\* wlanNeighborInfo)
//获取组网设备周边wifi信息

wlanNeighborInfo为JSON字符串，包含参数内容如下表

组网设备周边WiFi信息对象方法定义如表2.4.8-1所示：

> 表2.4.8 - 1 设备周边WIFI信息对象方法

+--------------+------------+--------+---------------------+---------+
| 对象名       | 方法名     | 参数   | 返回参数            | 描述    |
+==============+============+========+=====================+=========+
| ahsapd.      | getWlanNe  | 无     | <table>             | 获取    |
| neighbor     | ighborInfo |        | <colgroup>          | 组网设  |
|              |            |        | <col                | 备周边W |
|              |            |        | st                  | IFI信息 |
|              |            |        | yle="width: 41%" /> |         |
|              |            |        | <col                |         |
|              |            |        | st                  |         |
|              |            |        | yle="width: 25%" /> |         |
|              |            |        | <col                |         |
|              |            |        | st                  |         |
|              |            |        | yle="width: 32%" /> |         |
|              |            |        | </colgroup>         |         |
|              |            |        | <thead>             |         |
|              |            |        | <tr                 |         |
|              |            |        | class="header">     |         |
|              |            |        | <th><p>{</p>        |         |
|              |            |        | <p                  |         |
|              |            |        | >"ahsapd.neighbor": |         |
|              |            |        | {</p>               |         |
|              |            |        | <p>"number":        |         |
|              |            |        | ,</p>               |         |
|              |            |        | <p>" radios":       |         |
|              |            |        | [</p>               |         |
|              |            |        | <p>{</p>            |         |
|              |            |        | <p>"radio": " "     |         |
|              |            |        | ,</p>               |         |
|              |            |        | <p>"ssid":" "       |         |
|              |            |        | ,</p>               |         |
|              |            |        | <p>"macAddress":    |         |
|              |            |        | " " ,</p>           |         |
|              |            |        | <p>"channel": "     |         |
|              |            |        | ",</p>              |         |
|              |            |        | <p>"rssi": "        |         |
|              |            |        | ",</p>              |         |
|              |            |        | <p>"bandwidth":     |         |
|              |            |        | ,</p>               |         |
|              |            |        | <p>"wifiStandard":  |         |
|              |            |        | ,</p>               |         |
|              |            |        | <blockquote>        |         |
|              |            |        | <p>"securityMode":  |         |
|              |            |        | "MIXED-WPAPSK2"</p> |         |
|              |            |        | </blockquote>       |         |
|              |            |        | <p>}</p>            |         |
|              |            |        | <p>]</p>            |         |
|              |            |        | <p>}</p>            |         |
|              |            |        | <p>}</p></th>       |         |
|              |            |        | <th></th>           |         |
|              |            |        | <th>1.0</th>        |         |
|              |            |        | </tr>               |         |
|              |            |        | </thead>            |         |
|              |            |        | <tbody>             |         |
|              |            |        | </tbody>            |         |
|              |            |        | </table>            |         |
+--------------+------------+--------+---------------------+---------+

获取组网设备周边WiFi信息返回值参数详细定义如表2.4.8 - 2所示：

表2.4.8 - 2设备周边WIFI信息参数

+---------+-----------+----------+------------------------+----------+
| 参数名  | 类型      | 读写权限 | 描述                   | 是否必须 |
+=========+===========+==========+========================+==========+
| number  | Number    | 只读     | 周边WIFI数量，当值为   | Y        |
|         |           |          | 0时，WIFI信息数组为空  |          |
+---------+-----------+----------+------------------------+----------+
| radios  | Array of  | 只读     | 周边WIFI信息数组，周   | Y        |
|         | Object    |          | 边WIFI数量为非零时必选 |          |
+---------+-----------+----------+------------------------+----------+
| radio   | String    | 只读     | 频                     | Y        |
|         |           |          | 段：2.4G、5G；该字段在 |          |
|         |           |          | 周边WIFI数量非零时必选 |          |
+---------+-----------+----------+------------------------+----------+
| ssid    | String    | 只读     | 周边wi                 | Y        |
|         |           |          | fi的SSID名称；该字段在 |          |
|         |           |          | 周边WIFI数量非零时必选 |          |
+---------+-----------+----------+------------------------+----------+
| mac     | String    | 只读     | 周边WiFi               | Y        |
| Address |           |          | 的MAC地址，格          |          |
|         |           |          | 式为不带冒号并且大写； |          |
|         |           |          |                        |          |
|         |           |          | 该字段在               |          |
|         |           |          | 周边WIFI数量非零时必选 |          |
+---------+-----------+----------+------------------------+----------+
| channel | String    | 只读     | 周边WiFi信道,          | Y        |
|         |           |          | 如存在多信道，引       |          |
|         |           |          | 用","逗号分隔，例："1  |          |
|         |           |          | ,6"；                  |          |
|         |           |          |                        |          |
|         |           |          | 该字段在               |          |
|         |           |          | 周边WIFI数量非零时必选 |          |
+---------+-----------+----------+------------------------+----------+
| rssi    | String    | 只读     | 周边wi                 | Y        |
|         |           |          | fi的弱信号值；该字段在 |          |
|         |           |          | 周边WIFI数量非零时必选 |          |
+---------+-----------+----------+------------------------+----------+
| ba      | Number    | 只读     | 周边Wi-Fi频            | Y        |
| ndwidth |           |          | 宽，采用枚举取值：周边 |          |
|         |           |          | Wi-Fi数量非零时必选；  |          |
|         |           |          |                        |          |
|         |           |          | 1：20MHz               |          |
|         |           |          |                        |          |
|         |           |          | 2：40MHz               |          |
|         |           |          |                        |          |
|         |           |          | 3：80MHz               |          |
|         |           |          |                        |          |
|         |           |          | 4：160MHz              |          |
|         |           |          |                        |          |
|         |           |          | 5：80+80MHz            |          |
+---------+-----------+----------+------------------------+----------+
| wifiS   | Number    | 只读     | 周边Wi-Fi使用的无线    | Y        |
| tandard |           |          | 标准，采用枚举取值：周 |          |
|         |           |          | 边Wi-Fi数量非零时必选  |          |
|         |           |          |                        |          |
|         |           |          | 0：802.11a或802.11b    |          |
|         |           |          |                        |          |
|         |           |          | 1：802.11b/g           |          |
|         |           |          |                        |          |
|         |           |          | 2：802.11b/g/n         |          |
|         |           |          |                        |          |
|         |           |          | 3：802.11a/n/ac        |          |
|         |           |          |                        |          |
|         |           |          | 4：802.11b/g/n/ax      |          |
|         |           |          |                        |          |
|         |           |          | 5：802.11a/n/ac/ax     |          |
|         |           |          |                        |          |
|         |           |          | 6：802.11a/n           |          |
|         |           |          |                        |          |
|         |           |          | 7：802.11n             |          |
|         |           |          |                        |          |
|         |           |          | 8: 802.11g             |          |
|         |           |          |                        |          |
|         |           |          | 9: 802.11g/n           |          |
+---------+-----------+----------+------------------------+----------+
| secur   | String    | 只读     | 认                     | Y        |
| ityMode |           |          | 证加密模式，枚举取值： |          |
|         |           |          |                        |          |
|         |           |          | None                   |          |
|         |           |          |                        |          |
|         |           |          | WEP-64                 |          |
|         |           |          |                        |          |
|         |           |          | WEP-128                |          |
|         |           |          |                        |          |
|         |           |          | WPA-Personal           |          |
|         |           |          |                        |          |
|         |           |          | WPA2-Personal          |          |
|         |           |          |                        |          |
|         |           |          | MIXED-WPAPSK2          |          |
|         |           |          |                        |          |
|         |           |          | WPA3-SAE               |          |
|         |           |          |                        |          |
|         |           |          | MIXED-WPA2WPA3         |          |
+---------+-----------+----------+------------------------+----------+

# 2.4.9 组网设备漫游配置 {#组网设备漫游配置 .QB标题3}

插件调用接口：

int ahsapi_get_roaming_config(cJSON \*\* roamingConfig) //
获取组网设备漫游配置信息

int ahsapi_set_roaming_config(cJSON\* roamingConfig) // 配置漫游参数

int ahsapi_sta_disconnect(char\* mac，int dismissTime) // 断开STA连接

int ahsapi_sta_rssi(char\* mac，cJSON \*\* staRssi) // 查询STA 弱信号

int ahsapi_sta_stop_detect(char\* mac,long long stopDetectTime) //
停止STA 漫游策略

int ahsapi_set_band_steering_config(cJSON \* bandSteeringConfig)
//配置双频合一

int ahsapi_get_band_steering_config(cJSON \*\* bandSteeringConfig)
//获取双频合一配置

roamingConfig和bandSteeringConfig为JSON字符串，包含参数内容如下表

组网设备漫游配置对象方法定义如表2.4.9-1所示：

> 表2.4.9 - 1 设备漫游配置信息对象方法

+---------+----------+--------------+-------------------+------------+
| 对象名  | 方法名   | 参数         | 返回参数          | 描述       |
+=========+==========+==============+===================+============+
| ahsapd. | getRoami | 无           | <table>           | 获取       |
| roaming | ngConfig |              | <colgroup>        | 组网设备漫 |
|         |          |              | <col              | 游配置信息 |
|         |          |              | styl              |            |
|         |          |              | e="width: 41%" /> |            |
|         |          |              | <col              |            |
|         |          |              | styl              |            |
|         |          |              | e="width: 25%" /> |            |
|         |          |              | <col              |            |
|         |          |              | styl              |            |
|         |          |              | e="width: 32%" /> |            |
|         |          |              | </colgroup>       |            |
|         |          |              | <thead>           |            |
|         |          |              | <tr               |            |
|         |          |              | class="header">   |            |
|         |          |              | <th><p>{</p>      |            |
|         |          |              | <p>               |            |
|         |          |              | "ahsapd.roaming": |            |
|         |          |              | {</p>             |            |
|         |          |              | <p                |            |
|         |          |              | >"roamingSwitch": |            |
|         |          |              | ,</p>             |            |
|         |          |              | <p>"lowRSSI2.4G": |            |
|         |          |              | ,</p>             |            |
|         |          |              | <p>"lowRSSI5G":   |            |
|         |          |              | ,</p>             |            |
|         |          |              | <p>"packetLoss":  |            |
|         |          |              | ,</p>             |            |
|         |          |              | <p>"retryRatio":  |            |
|         |          |              | ,</p>             |            |
|         |          |              | <p                |            |
|         |          |              | >"lowRSSIPeriod": |            |
|         |          |              | ,</p>             |            |
|         |          |              | <p>"d             |            |
|         |          |              | etectPeriod":</p> |            |
|         |          |              | <p>}</p>          |            |
|         |          |              | <p>}</p></th>     |            |
|         |          |              | <th></th>         |            |
|         |          |              | <th>1.0</th>      |            |
|         |          |              | </tr>             |            |
|         |          |              | </thead>          |            |
|         |          |              | <tbody>           |            |
|         |          |              | </tbody>          |            |
|         |          |              | </table>          |            |
+---------+----------+--------------+-------------------+------------+
|         | setRoami | {            | 无                | 配置组网设 |
|         | ngConfig |              |                   | 备漫游参数 |
|         |          | \"seqId      |                   |            |
|         |          | \":\"XXXX\", |                   |            |
|         |          |              |                   |            |
|         |          | \"roam       |                   |            |
|         |          | ingSwitch\": |                   |            |
|         |          | ,            |                   |            |
|         |          |              |                   |            |
|         |          | \"lo         |                   |            |
|         |          | wRSSI2.4G\": |                   |            |
|         |          | ,            |                   |            |
|         |          |              |                   |            |
|         |          | \"           |                   |            |
|         |          | lowRSSI5G\": |                   |            |
|         |          | ,            |                   |            |
|         |          |              |                   |            |
|         |          | \"p          |                   |            |
|         |          | acketLoss\": |                   |            |
|         |          | ,            |                   |            |
|         |          |              |                   |            |
|         |          | \"r          |                   |            |
|         |          | etryRatio\": |                   |            |
|         |          | ,            |                   |            |
|         |          |              |                   |            |
|         |          | \"lowR       |                   |            |
|         |          | SSIPeriod\": |                   |            |
|         |          | ,            |                   |            |
|         |          |              |                   |            |
|         |          | \"det        |                   |            |
|         |          | ectPeriod\": |                   |            |
|         |          |              |                   |            |
|         |          | }            |                   |            |
+---------+----------+--------------+-------------------+------------+
|         | staDi    | {            | 无                | 组网       |
|         | sconnect |              |                   | 设备断开与 |
|         |          | \"mac        |                   | 该STA的连  |
|         |          | Address\":\" |                   | 接，并限制 |
|         |          | \"，         |                   | 接入dismi  |
|         |          |              |                   | ssTime毫秒 |
|         |          | \"dismis     |                   |            |
|         |          | sTime\":5000 |                   |            |
|         |          |              |                   |            |
|         |          | }            |                   |            |
+---------+----------+--------------+-------------------+------------+
|         | staRssi  | {            | {                 | 查询组网   |
|         |          |              |                   | 设备与STA  |
|         |          | \"mac        | \"                | 的弱信号； |
|         |          | Address\":\" | ahsapd.roaming\": |            |
|         |          | \"           | {                 | 优先监测并 |
|         |          |              |                   | 上报5GHz频 |
|         |          | }            | \"radio\": \" \"  | 段信号强度 |
|         |          |              | ,                 |            |
|         |          |              |                   |            |
|         |          |              | \"rssi\": \" \",  |            |
|         |          |              |                   |            |
|         |          |              | \"channel\": \"   |            |
|         |          |              | \"                |            |
|         |          |              |                   |            |
|         |          |              | }                 |            |
|         |          |              |                   |            |
|         |          |              | }                 |            |
+---------+----------+--------------+-------------------+------------+
|         | staSt    | {            | 无                | 停止该STA  |
|         | opDetect |              |                   | 漫游策略； |
|         |          | \"mac        |                   |            |
|         |          | Address\":\" |                   | 暂停该STA  |
|         |          | \"，         |                   | 的信号质量 |
|         |          |              |                   | 监测和通过 |
|         |          | \"stopDetec  |                   | roamingDe  |
|         |          | tTime\":1000 |                   | tect告警消 |
|         |          |              |                   | 息上报，st |
|         |          | }            |                   | opDetectTi |
|         |          |              |                   | me字段为空 |
|         |          |              |                   | 或者值为0  |
|         |          |              |                   | 一直停止   |
|         |          |              |                   | 漫游策略； |
|         |          |              |                   | stopDetect |
|         |          |              |                   | Time值大于 |
|         |          |              |                   | 0，停止策  |
|         |          |              |                   | 略时间单位 |
|         |          |              |                   | 为秒（S）; |
|         |          |              |                   |            |
|         |          |              |                   | 组网设备   |
|         |          |              |                   | 重启恢复监 |
|         |          |              |                   | 测和上报。 |
+---------+----------+--------------+-------------------+------------+
|         | setBa    | {            | 无                | 设         |
|         | ndSteeri |              |                   | 置双频合一 |
|         | ngConfig | \"           |                   |            |
|         |          | bandSteering |                   |            |
|         |          | Status\":1， |                   |            |
|         |          |              |                   |            |
|         |          | \"rssi       |                   |            |
|         |          | Threshold\": |                   |            |
|         |          | -60 ,        |                   |            |
|         |          |              |                   |            |
|         |          | \"rssiTh     |                   |            |
|         |          | reshold5G\": |                   |            |
|         |          | -40          |                   |            |
|         |          |              |                   |            |
|         |          | }            |                   |            |
+---------+----------+--------------+-------------------+------------+
|         | getBa    | 无           | \"                | 获取双     |
|         | ndSteeri |              | ahsapd.roaming\": | 频合一配置 |
|         | ngConfig |              | {                 |            |
|         |          |              |                   |            |
|         |          |              | \"bandSte         |            |
|         |          |              | eringStatus\":1， |            |
|         |          |              |                   |            |
|         |          |              | \                 |            |
|         |          |              | "rssiThreshold\": |            |
|         |          |              | -60 ,             |            |
|         |          |              |                   |            |
|         |          |              | \"r               |            |
|         |          |              | ssiThreshold5G\": |            |
|         |          |              | -40               |            |
|         |          |              |                   |            |
|         |          |              | }                 |            |
+---------+----------+--------------+-------------------+------------+

组网设备漫游告警信号定义如表2.4.9 - 2所示：

> 表2.4.9 - 2 漫游告警信号定义

+--------------+-------------------+-----------------------+---------+
| 消息类型     | 消息内容          | 描述                  | 是      |
|              |                   |                       | 否必须  |
+==============+===================+=======================+=========+
| r            | {                 | 下挂设备信号质        | Y       |
| oamingDetect |                   | 量超过阈值，主动告警  |         |
|              | \"                |                       |         |
|              | seqId\":\"XXXX\", |                       |         |
|              |                   |                       |         |
|              | \"macAddress\":\" |                       |         |
|              | \",               |                       |         |
|              |                   |                       |         |
|              | \"radio\": \" \"  |                       |         |
|              | ,                 |                       |         |
|              |                   |                       |         |
|              | \"rssi\": \" \",  |                       |         |
|              |                   |                       |         |
|              | \"rxRate\":\" \", |                       |         |
|              |                   |                       |         |
|              | \"txRate\":\" \", |                       |         |
|              |                   |                       |         |
|              | \"channel\":\"    |                       |         |
|              | \",               |                       |         |
|              |                   |                       |         |
|              | \"fr              |                       |         |
|              | equencyWidth\":\" |                       |         |
|              | \",               |                       |         |
|              |                   |                       |         |
|              | \"packetLoss\": , |                       |         |
|              |                   |                       |         |
|              | \"retryRatio\":   |                       |         |
|              |                   |                       |         |
|              | }                 |                       |         |
+--------------+-------------------+-----------------------+---------+

组网设备漫游配置参数详细定义如表2.4.9-3所示：

> 表2.4.9- 3 设备漫游配置信息参数

+-----------+------------+---------+------------------------+---------+
| 参数名    | 类型       | 读      | 描述                   | 是      |
|           |            | 写权限  |                        | 否必须  |
+===========+============+=========+========================+=========+
| roam      | Numb       | 读写    | 是否启用WiFi漫游控制： | Y       |
| ingSwitch | er(uint32) |         |                        |         |
|           |            |         | 0: 关闭                |         |
|           |            |         |                        |         |
|           |            |         | 1: 启用                |         |
+-----------+------------+---------+------------------------+---------+
| lo        | Num        | 读写    | 弱信号阈               | Y       |
| wRSSI2.4G | ber(int32) |         | 值（2.4G），单位dBm。  |         |
|           |            |         |                        |         |
|           |            |         | 当STA接入当前智能组    |         |
|           |            |         | 网终端的RSSI小于该阈值 |         |
|           |            |         | 时，智能组网终端需对该 |         |
|           |            |         | STA执行漫游切换控制。  |         |
+-----------+------------+---------+------------------------+---------+
| lowRSSI5G | Num        | 读写    | 弱信号                 | Y       |
|           | ber(int32) |         | 阈值（5G），单位dBm。  |         |
|           |            |         |                        |         |
|           |            |         | 当STA接入当前智能组    |         |
|           |            |         | 网终端的RSSI小于该阈值 |         |
|           |            |         | 时，智能组网终端需对该 |         |
|           |            |         | STA执行漫游切换控制。  |         |
+-----------+------------+---------+------------------------+---------+
| p         | Numb       | 读写    | 丢包比率               | N       |
| acketLoss | er(uint32) |         |                        |         |
+-----------+------------+---------+------------------------+---------+
| r         | Numb       | 读写    | 重传比率               | N       |
| etryRatio | er(uint32) |         |                        |         |
+-----------+------------+---------+------------------------+---------+
| lowR      | Numb       | 读写    | 触发                   | N       |
| SSIPeriod | er(uint32) |         | 弱信号上报时延，单位： |         |
|           |            |         | 毫秒，缺省值为20000；  |         |
|           |            |         |                        |         |
|           |            |         | 举                     |         |
|           |            |         | 例：对于连接5G频段的S  |         |
|           |            |         | TA，组网终端监测STA的R |         |
|           |            |         | SSI强度持续低于lowRSSI |         |
|           |            |         | 5G的时间超过20秒时，发 |         |
|           |            |         | 起漫游告警roamingDetec |         |
|           |            |         | t，同步该STA网络质量； |         |
+-----------+------------+---------+------------------------+---------+
| det       | Numb       | 读写    | 基于事件               | N       |
| ectPeriod | er(uint32) |         | 触发时两次上报间隔不小 |         |
|           |            |         | 于该时长，基于周期检测 |         |
|           |            |         | 时使用该周期，单位毫秒 |         |
+-----------+------------+---------+------------------------+---------+
| di        | Num        | 读写    | 限                     | Y       |
| smissTime | ber(int32) |         | 制接入的最长时间(毫秒) |         |
|           |            |         | ，缺省5000毫秒         |         |
+-----------+------------+---------+------------------------+---------+
| stopD     | Numb       | 只写    | 停止该STA漫游          | N       |
| etectTime | er(uint64) |         | 策略的时间单位秒（S）  |         |
|           |            |         | ；如果值为0或者空值，  |         |
|           |            |         | 一直停止至到设备重启； |         |
+-----------+------------+---------+------------------------+---------+
| m         | String     | 读写    | 漫游下挂设备MAC地址，  | Y       |
| acAddress |            |         | 格式为不带冒号且大写； |         |
+-----------+------------+---------+------------------------+---------+
| radio     | String     | 只读    | 漫游下挂设             | Y       |
|           |            |         | 备的接入频段，取值2.4G |         |
|           |            |         | ；5G ；5G-2            |         |
|           |            |         |                        |         |
|           |            |         | 注：对于               |         |
|           |            |         | 三频设备，5G表示5.8G频 |         |
|           |            |         | 段，5G-2表示5.2G频段； |         |
|           |            |         | 双频设备忽略5G-2频段； |         |
+-----------+------------+---------+------------------------+---------+
| rssi      | String     | 只读    | 漫游下挂设备当前       | Y       |
|           |            |         | 的信号强度，单位为dBm  |         |
+-----------+------------+---------+------------------------+---------+
| channel   | String     | 只读    | 当前工作信道号,        | Y       |
|           |            |         | 若存在多个信道，可用", |         |
|           |            |         | "逗号分隔，例如："1,6" |         |
+-----------+------------+---------+------------------------+---------+
| frequ     | String     | 只读    | 频宽，取值如下：       | Y       |
| encyWidth |            |         | 20                     |         |
|           |            |         | M/40M/Auto20M40M/Auto2 |         |
|           |            |         | 0M40M80M(仅针对5GWifi) |         |
+-----------+------------+---------+------------------------+---------+
| rxRate    | String     | 只读    | 当                     | Y       |
|           |            |         | 前协商接收速率（Mbps） |         |
+-----------+------------+---------+------------------------+---------+
| txRate    | String     | 只读    | 当前协商发送速率(Mpbs) | Y       |
+-----------+------------+---------+------------------------+---------+
| bandSteer | Numb       | 读写    | 双频合一开关：         | Y       |
| ingStatus | er(uint32) |         |                        |         |
|           |            |         | 0：关闭                |         |
|           |            |         |                        |         |
|           |            |         | 1：打开                |         |
+-----------+------------+---------+------------------------+---------+
| rssi      | Num        | 读写    | 接                     | N       |
| Threshold | ber(int32) |         | 收信号强度阈值，当2.4  |         |
|           |            |         | G频段检测到某一下挂STA |         |
|           |            |         | 接收到的信号强度高于此 |         |
|           |            |         | 阈值，将当前双频STA切  |         |
|           |            |         | 换至5G频段。单位：dbm. |         |
|           |            |         |                        |         |
|           |            |         | 取值范围（-100\~0）    |         |
+-----------+------------+---------+------------------------+---------+
| rssiTh    | Num        | 读写    | 5G接收信号             | N       |
| reshold5G | ber(int32) |         | 强度阈值，当双频客户端 |         |
|           |            |         | 首次连接或者当前工作在 |         |
|           |            |         | 5G频段时，检测到某一下 |         |
|           |            |         | 挂双频STA接收到的信号  |         |
|           |            |         | 强度低于此阈值将下挂双 |         |
|           |            |         | 频客户端切换至2.4G频段 |         |
|           |            |         |                        |         |
|           |            |         | 单位：dbm.             |         |
|           |            |         |                        |         |
|           |            |         | 取值范围（-100\~0）    |         |
+-----------+------------+---------+------------------------+---------+
| seqId     | String     | 读写    | 配置                   | N       |
|           |            |         | 对应唯一标识,如配置下  |         |
|           |            |         | 发携带seqId，event上报 |         |
|           |            |         | 必选携带下发的seqId；  |         |
+-----------+------------+---------+------------------------+---------+

# 2.4.10 组网设备定时任务 {#组网设备定时任务 .QB标题3}

插件调用接口：

int ahsapi_get_all_timed_task(cJSON\*\* allTimedTask)
//获取所有定时任务配置信息

int ahsapi_add_timed_task(cJSON\* timedTask) //添加和更新定时任务

int ahsapi_delete_timed_task(int taskId) // 删除定时任务

allTimedTask、timedTask为JSON字符串，包含参数内容如下表

组网设备定时任务对象方法定义如表2.4.10 -1所示：

> 表2.4.10 - 1 设备定时任务对象方法

+-------------+-----------+------------+-----------------+-------------+
| 对象名      | 方法名    | 参数       | 返回参数        | 描述        |
+=============+===========+============+=================+=============+
| ahsapd.     | get       | 无         | <table>         | 获          |
| timedtask   | TimedTask |            | <colgroup>      | 取组网设备  |
|             |           |            | <col            | 定时任务所  |
|             |           |            | style=          | 有配置信息  |
|             |           |            | "width: 37%" /> |             |
|             |           |            | <col            |             |
|             |           |            | style=          |             |
|             |           |            | "width: 29%" /> |             |
|             |           |            | <col            |             |
|             |           |            | style=          |             |
|             |           |            | "width: 32%" /> |             |
|             |           |            | </colgroup>     |             |
|             |           |            | <thead>         |             |
|             |           |            | <tr             |             |
|             |           |            | class="header"> |             |
|             |           |            | <th><p>{</p>    |             |
|             |           |            | <p>"ahs         |             |
|             |           |            | apd.timedtask": |             |
|             |           |            | {</p>           |             |
|             |           |            | <p>"tasks":     |             |
|             |           |            | [</p>           |             |
|             |           |            | <p>{</p>        |             |
|             |           |            | <p>"taskId":    |             |
|             |           |            | ,</p>           |             |
|             |           |            | <               |             |
|             |           |            | p>"timeOffset": |             |
|             |           |            | ,</p>           |             |
|             |           |            | <p>"week":      |             |
|             |           |            | ,</p>           |             |
|             |           |            | <p>"enable":    |             |
|             |           |            | ,</p>           |             |
|             |           |            | <p>"action":    |             |
|             |           |            | ,</p>           |             |
|             |           |            | <p              |             |
|             |           |            | >"timeOffset2": |             |
|             |           |            | ,</p>           |             |
|             |           |            | <p>"index":</p> |             |
|             |           |            | <p>}</p>        |             |
|             |           |            | <p>]</p>        |             |
|             |           |            | <p>}</p></th>   |             |
|             |           |            | <th></th>       |             |
|             |           |            | <th>1.0</th>    |             |
|             |           |            | </tr>           |             |
|             |           |            | </thead>        |             |
|             |           |            | <tbody>         |             |
|             |           |            | </tbody>        |             |
|             |           |            | </table>        |             |
+-------------+-----------+------------+-----------------+-------------+
|             | add       | {          | 无              | 配置组网设  |
|             | TimedTask |            |                 | 备定时任务  |
|             |           | \          |                 |             |
|             |           | "taskId\": |                 | 当ta        |
|             |           | ,          |                 | skId（非0） |
|             |           |            |                 | 已经存在时  |
|             |           | \"tim      |                 | ，为更新原  |
|             |           | eOffset\": |                 | 有定时任务  |
|             |           | ,          |                 |             |
|             |           |            |                 | taskId不    |
|             |           | \"week\":  |                 | 存在时为新  |
|             |           | ,          |                 | 添加定时任  |
|             |           |            |                 | 务，taskId  |
|             |           | \          |                 | 为          |
|             |           | "enable\": |                 | 0时为新增   |
|             |           | ,          |                 | ，由ahsapd  |
|             |           |            |                 | 生成新的非0 |
|             |           | \          |                 | taskId      |
|             |           | "action\": |                 |             |
|             |           | ,          |                 |             |
|             |           |            |                 |             |
|             |           | \"time     |                 |             |
|             |           | Offset2\": |                 |             |
|             |           | ,          |                 |             |
|             |           |            |                 |             |
|             |           | \"index\": |                 |             |
|             |           |            |                 |             |
|             |           | }          |                 |             |
+-------------+-----------+------------+-----------------+-------------+
|             | delete    | {          | 无              | 删          |
|             | TimedTask |            |                 | 除定时任务  |
|             |           | \          |                 |             |
|             |           | "taskId\": |                 |             |
|             |           |            |                 |             |
|             |           | }          |                 |             |
+-------------+-----------+------------+-----------------+-------------+

组网设备定时任务获取和配置参数定义如表2.4.10 - 2所示：

> 表2.4.10 - 2 设备定时任务参数

+-------------+-----------+----------+--------------------+-----------+
| 参数名      | 类型      | 读写权限 | 描述               | 是否必须  |
+=============+===========+==========+====================+===========+
| tasks       | Array of  | 只读     | \[{以下字段        | Y         |
|             | Object    |          | 组成的json},...\]  |           |
+-------------+-----------+----------+--------------------+-----------+
| taskId      | Numbe     | 读写     | 任务Id, 值为0      | Y         |
|             | r(uint32) |          | 时为新增任务，ahs  |           |
|             |           |          | apd自己生成新的非0 |           |
|             |           |          | taskId值           |           |
+-------------+-----------+----------+--------------------+-----------+
| timeOffset  | Numbe     | 读写     | 执行时间偏移量     | Y         |
|             | r(uint64) |          | （执行任务的时间点 |           |
|             |           |          | 与当天凌晨0点的时  |           |
|             |           |          | 间差，以秒为单位） |           |
+-------------+-----------+----------+--------------------+-----------+
| week        | Numbe     | 读写     | 使用in             | Y         |
|             | r(uint32) |          | t值二进制位的低7位 |           |
|             |           |          | 代表星期，由高到低 |           |
|             |           |          | 位依次代表星期六五 |           |
|             |           |          | 四三二一日。如7(00 |           |
|             |           |          | 000111B)表示周二， |           |
|             |           |          | 周一和周日。若仅需 |           |
|             |           |          | 单次执行，值为0。  |           |
+-------------+-----------+----------+--------------------+-----------+
| enable      | Numbe     | 读写     | 任务是             | Y         |
|             | r(uint32) |          | 否启用0：不启用，  |           |
|             |           |          | 1：启用            |           |
+-------------+-----------+----------+--------------------+-----------+
| action      | Numbe     | 读写     | 任务指             | Y         |
|             | r(uint32) |          | 令，可能取值如下： |           |
|             |           |          |                    |           |
|             |           |          | 1:                 |           |
|             |           |          | ToReboot（重启）   |           |
|             |           |          |                    |           |
|             |           |          | 2:                 |           |
|             |           |          | ToSetHe            |           |
|             |           |          | althMode（健康模式 |           |
|             |           |          | ，定时开、关Wifi， |           |
|             |           |          | （健康模式，定时开 |           |
|             |           |          | 、关Wifi，在TimeOf |           |
|             |           |          | fset对应的时间关闭 |           |
|             |           |          | 指定index的Wifi，  |           |
|             |           |          | 在TimeOffset2对应  |           |
|             |           |          | 的时间开启它。当Ti |           |
|             |           |          | meOffset2小于TimeO |           |
|             |           |          | ffset时，TimeOffse |           |
|             |           |          | t2代表相对于第二天 |           |
|             |           |          | 0点的时间偏移量）  |           |
+-------------+-----------+----------+--------------------+-----------+
| timeOffset2 | Numbe     | 读写     | 仅当Acti           | N         |
|             | r(uint64) |          | on为ToSetHealthMod |           |
|             |           |          | e时，才携带该字段, |           |
|             |           |          | 执行时间偏移量     |           |
|             |           |          | （执行任务的时间点 |           |
|             |           |          | 与当天凌晨0点的时  |           |
|             |           |          | 间差，以秒为单位） |           |
+-------------+-----------+----------+--------------------+-----------+
| index       | Numbe     | 读写     | 仅当Act            | N         |
|             | r(uint32) |          | ion为ToSetHealthMo |           |
|             |           |          | de时，才携带该字段 |           |
|             |           |          |                    |           |
|             |           |          | ，Wifi通道。2.     |           |
|             |           |          | 4G用1-4，5G用5-8， |           |
|             |           |          | 0表示所有Wifi通道  |           |
+-------------+-----------+----------+--------------------+-----------+

# 2.4.11 组网设备配置黑白名单 {#组网设备配置黑白名单 .QB标题3}

插件调用接口：

int ahsapi_set_mac_filter(cJSON\* macFilter) // 设置黑白名单

int ahsapi_get_mac_filter(cJSON\*\* macFilter) // 获取黑白名单配置

macFilter为JSON字符串，包含参数内容如下表

组网设备配置黑白名单对象方法定义如表2.4.11-1所示：

> 表2.4.11 - 1 设备黑白名单对象方法

+-------------+---------+--------------+-----------------+------------+
| 对象名      | 方法名  | 参数         | 返回参数        | 描述       |
+=============+=========+==============+=================+============+
| ahsapd.     | getMa   | 无           | <table>         | 获取组网   |
| macfilter   | cFilter |              | <colgroup>      | 设备黑白名 |
|             |         |              | <col            | 单配置信息 |
|             |         |              | style=          |            |
|             |         |              | "width: 36%" /> |            |
|             |         |              | <col            |            |
|             |         |              | style=          |            |
|             |         |              | "width: 30%" /> |            |
|             |         |              | <col            |            |
|             |         |              | style=          |            |
|             |         |              | "width: 32%" /> |            |
|             |         |              | </colgroup>     |            |
|             |         |              | <thead>         |            |
|             |         |              | <tr             |            |
|             |         |              | class="header"> |            |
|             |         |              | <th><p>{</p>    |            |
|             |         |              | <p>"ahs         |            |
|             |         |              | apd.macfilter": |            |
|             |         |              | {</p>           |            |
|             |         |              | <p>"ma          |            |
|             |         |              | cFilterEnable": |            |
|             |         |              | ,</p>           |            |
|             |         |              | <p>"ma          |            |
|             |         |              | cFilterPolicy": |            |
|             |         |              | ,</p>           |            |
|             |         |              | <p>"macFilte    |            |
|             |         |              | rEntries":"</p> |            |
|             |         |              | <p>}</p>        |            |
|             |         |              | <p>}</p></th>   |            |
|             |         |              | <th></th>       |            |
|             |         |              | <th>1.0</th>    |            |
|             |         |              | </tr>           |            |
|             |         |              | </thead>        |            |
|             |         |              | <tbody>         |            |
|             |         |              | </tbody>        |            |
|             |         |              | </table>        |            |
+-------------+---------+--------------+-----------------+------------+
|             | setMa   | {            | 无              | 配置       |
|             | cFilter |              |                 | 黑白名单策 |
|             |         | \"macFil     |                 | 略，macFil |
|             |         | terEnable\": |                 | terEntries |
|             |         | ,            |                 | 包含所有   |
|             |         |              |                 | 过滤的地址 |
|             |         | \"macFil     |                 |            |
|             |         | terPolicy\": |                 |            |
|             |         | ,            |                 |            |
|             |         |              |                 |            |
|             |         | \"macF       |                 |            |
|             |         | ilterEntries |                 |            |
|             |         | \":\"\"      |                 |            |
|             |         |              |                 |            |
|             |         | }            |                 |            |
+-------------+---------+--------------+-----------------+------------+

组网设备配置黑白名单对象方法定义如表2.4.11- 2所示：

> 表2.4.11 - 2 设备黑白名单配置参数

  ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  参数名             类型             读写权限   描述                                                                                                             是否必须
  ------------------ ---------------- ---------- ---------------------------------------------------------------------------------------------------------------- -----------
  macFilterEnable    Number(uint32)   读写       是否使能MAC地址过滤；1为过滤；0为不过滤                                                                          Y

  macFilterPolicy    Number(uint32)   读写       MAC地址过滤策略；0为黑名单；1为白名单                                                                            Y

  macFilterEntries   String           读写       黑白名单信息：包含MAC地址及设备名（一组最长为64字节），以"/"隔开；两组信息之间以逗号分开，最多支持32组。示例：   Y
                                                 001111334455/pro,80717a33ccf3/android                                                                            
  ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------

# 2.4.12 组网设备操控指令 {#组网设备操控指令 .QB标题3}

插件调用接口：

int ahsapi_set_reboot(int controlType) // 组网设备重启操作

int ahsapi_set_reset() // 组网设备恢复出厂

int ahsapi_set_led_control(int ledOnOFF) //组网设备led灯控

int ahsapi_set_firmware_upgrade(cJSON \* upgrade) //组网设备固件升级

int ahsapi_set_lock_net (int lockStatus) // 锁网配置

int ahsapi_set_pmf_ctrl(int pmfEnable)
//配置管理帧加密，家宽优选插件使用接口

int ahsapi_get_control_status(cJSON \*\* controlStatus) //
获取led灯控、锁网

int ahsapi_set_hwnat_clean() //
配置清空硬加速链路跟踪表，家宽优选插件使用接口

int ahsapi_set_kernel_count(int count)
//配置硬加速，软件协议栈报文处理数量，家宽优选插件使用接口

int ahsapi_set_dev_log_stats(cJSON \* devLog, char\* logPath , int len)
//配置日志采集

int ahsapi_set_ipv6_control(int ipv6Enable)//配置ipv6双栈开关

int ahsapi_set_multicast_enable( int multicastEnable)
//配置组播转单播开关

int ahsapi_get_multicast_enable(int
\*multicastEnable)//获取组播转单播开关状态

int ahsapi_set_DMZ(cJSON\* DMZConfig) // 设置DMZ配置信息

int ahsapi_get_DMZ(cJSON\*\* DMZConfig) // 获取DMZ配置信息

int ahsapi_set_upnp(cJSON\* upnpConfig) // 设置upnp配置信息

int ahsapi_get_upnp(cJSON\*\* upnpConfig) // 获取upnp配置信息

upgrade和controlStatus、devLog为为JSON字符串，包含参数内容如下：

组网设备操控指令对象方法定义如表2.4.12 - 1所示：

> 表2.4.12 - 1 设备操作指令对象方法

+----------+------------+------------+--------------+-----------------+
| 对象名   | 方法名     | 参数       | 返回参数     | 描述            |
+==========+============+============+==============+=================+
| ahsapd.  | reboot     | {          |   --         | 组              |
| control  |            |            | ------------ | 网设备重启操作  |
|          |            | \"cont     |              |                 |
|          |            | rolType\": | 无       1.0 |                 |
|          |            | 1          |   --         |                 |
|          |            |            | -- --- ----- |                 |
|          |            | }          |              |                 |
|          |            |            |   --         |                 |
|          |            |            | ------------ |                 |
+----------+------------+------------+--------------+-----------------+
|          | reset      | 无         | 无           | 组              |
|          |            |            |              | 网设备恢复出厂  |
+----------+------------+------------+--------------+-----------------+
|          | ledControl | {          | 无           | 组网设备led灯控 |
|          |            |            |              |                 |
|          |            | \"l        |              |                 |
|          |            | edOnOff\": |              |                 |
|          |            | 1          |              |                 |
|          |            |            |              |                 |
|          |            | }          |              |                 |
+----------+------------+------------+--------------+-----------------+
|          | firmw      | {          | 无           | 组              |
|          | areUpgrade |            |              | 网设备固件升级  |
|          |            | \"seqId\"  |              | ；ahsapd收到指  |
|          |            | :\"XXXX\", |              | 令后返回结果（  |
|          |            |            |              | 非阻塞），执行  |
|          |            | \"f        |              | 升级过程结果通  |
|          |            | ileType\": |              | 过controlReport |
|          |            | 1,         |              | 事件返回        |
|          |            |            |              | 升级结果（如需  |
|          |            | \"         |              | 立刻重启的先发  |
|          |            | localPath\ |              | 送结果等待2秒后 |
|          |            | ":\"XXX\", |              | 执行重启动作）  |
|          |            |            |              | ，如升级路径是  |
|          |            | \          |              | 网络下载地址，  |
|          |            | "reboot\": |              | 需求ahsapd操作  |
|          |            | 0          |              | 下载固件过程的  |
|          |            |            |              | 需要下载完成后  |
|          |            | }          |              | 通过controlRep  |
|          |            |            |              | ort返回下载结果 |
+----------+------------+------------+--------------+-----------------+
|          | setLockNet | {          | 无           | 设              |
|          |            |            |              | 置组网设备锁网  |
|          |            | \"lock     |              |                 |
|          |            | Status\":1 |              |                 |
|          |            |            |              |                 |
|          |            | }          |              |                 |
+----------+------------+------------+--------------+-----------------+
|          | pmfCtrl    | {          | 无           | Protected       |
|          |            |            |              | Management      |
|          |            | \"pmf      |              | Frame           |
|          |            | Enable\":1 |              | 开关（管        |
|          |            |            |              | 理帧开关），家  |
|          |            | }          |              | 宽优选插件使用  |
+----------+------------+------------+--------------+-----------------+
|          | i          | {          | 无           | ipv4/ipv6       |
|          | pv6Control |            |              | 双栈功能        |
|          |            | ipv6Enable |              | 开关，同时支持  |
|          |            |            |              | ipv4和ipv6协议  |
|          |            | }          |              |                 |
+----------+------------+------------+--------------+-----------------+
|          | getCon     | 无         | {            | 获取组网设备    |
|          | trolStatus |            |              | led灯和锁网，i  |
|          |            |            | \"ahsapd.    | p双栈等状态信息 |
|          |            |            | control\"：  |                 |
|          |            |            |              |                 |
|          |            |            | {            |                 |
|          |            |            |              |                 |
|          |            |            | \"ledOnOff   |                 |
|          |            |            | \": 1,       |                 |
|          |            |            |              |                 |
|          |            |            | \"lock       |                 |
|          |            |            | Status\":1， |                 |
|          |            |            |              |                 |
|          |            |            | \"pmf        |                 |
|          |            |            | Enable\":1， |                 |
|          |            |            |              |                 |
|          |            |            | \"ipv6       |                 |
|          |            |            | Enable\":1， |                 |
|          |            |            |              |                 |
|          |            |            | \"co         |                 |
|          |            |            | unt\":\"0\", |                 |
|          |            |            |              |                 |
|          |            |            | \"lo         |                 |
|          |            |            | gSwitch\":0, |                 |
|          |            |            |              |                 |
|          |            |            | \"l          |                 |
|          |            |            | ogLevel\":0, |                 |
|          |            |            |              |                 |
|          |            |            | \"logTime\": |                 |
|          |            |            | 0            |                 |
|          |            |            |              |                 |
|          |            |            | }            |                 |
|          |            |            |              |                 |
|          |            |            | }            |                 |
+----------+------------+------------+--------------+-----------------+
|          | hwnatClean | 无         | 无           | 清空硬加速      |
|          |            |            |              | 链路跟踪表，家  |
|          |            |            |              | 宽优选插件使用  |
+----------+------------+------------+--------------+-----------------+
|          | k          | {          | 无           | 配置硬件加速    |
|          | ernelCount |            |              | ，软件协议栈报  |
|          |            | \"cou      |              | 文处理数量，家  |
|          |            | nt\":\"0\" |              | 宽优选插件使用  |
|          |            |            |              |                 |
|          |            | }          |              |                 |
+----------+------------+------------+--------------+-----------------+
|          | d          | {          | {            | 配置设          |
|          | evLogStats |            |              | 备日志收集功能  |
|          |            | \"seqId\"  | \"ahsapd.    | ；日志收集指令  |
|          |            | :\"XXXX\", | control\"：  | 立刻返回日志路  |
|          |            |            |              | 径（非阻塞），  |
|          |            | \"logS     | {            | 到日志收集执行  |
|          |            | witch\":1, |              | 完成后通过cont  |
|          |            |            | \"logPath\": | rolReport通知日 |
|          |            | \"log      | \" \"        | 志收集执行结果  |
|          |            | Level\":0, |              |                 |
|          |            |            | }            |                 |
|          |            | \"         |              |                 |
|          |            | logTime\": | }            |                 |
|          |            | 100        |              |                 |
|          |            |            |              |                 |
|          |            | }          |              |                 |
+----------+------------+------------+--------------+-----------------+
|          | setMult    | {          | 无           | 配置            |
|          | icastEnabl |            |              | 组播转单播开关  |
|          |            | \          |              |                 |
|          | e          | "multicast |              |                 |
|          |            | Enable\":0 |              |                 |
|          |            |            |              |                 |
|          |            | }          |              |                 |
+----------+------------+------------+--------------+-----------------+
|          | getMult    | 无         | {            | 获取组播        |
|          | icastEnabl |            |              | 转单播开关状态  |
|          |            |            | \"ahsapd.    |                 |
|          | e          |            | control\"：  |                 |
|          |            |            |              |                 |
|          |            |            | {            |                 |
|          |            |            |              |                 |
|          |            |            | \"multica    |                 |
|          |            |            | stEnable\":0 |                 |
|          |            |            |              |                 |
|          |            |            | }            |                 |
|          |            |            |              |                 |
|          |            |            | }            |                 |
+----------+------------+------------+--------------+-----------------+
|          | setDMZ     | {          |              |                 |
|          |            |            |              |                 |
|          |            | \"DMZE     |              |                 |
|          |            | nable\":1, |              |                 |
|          |            |            |              |                 |
|          |            | \"ipAddres |              |                 |
|          |            | s\":\"192. |              |                 |
|          |            | 168.10.2\" |              |                 |
|          |            |            |              |                 |
|          |            | }          |              |                 |
+----------+------------+------------+--------------+-----------------+
|          | getDMZ     | 无         | {            |                 |
|          |            |            |              |                 |
|          |            |            | \"ahsapd.    |                 |
|          |            |            | control\"：  |                 |
|          |            |            |              |                 |
|          |            |            | DMZList:\[   |                 |
|          |            |            |              |                 |
|          |            |            | {            |                 |
|          |            |            |              |                 |
|          |            |            | \"DM         |                 |
|          |            |            | ZEnable\":1, |                 |
|          |            |            |              |                 |
|          |            |            | \"ipAd       |                 |
|          |            |            | dress\":\"19 |                 |
|          |            |            | **********\" |                 |
|          |            |            |              |                 |
|          |            |            | }            |                 |
|          |            |            |              |                 |
|          |            |            | }            |                 |
|          |            |            |              |                 |
|          |            |            | \]           |                 |
+----------+------------+------------+--------------+-----------------+
|          | setUPnP    | {          |              |                 |
|          |            |            |              |                 |
|          |            | \"UPnPE    |              |                 |
|          |            | nable\":1, |              |                 |
|          |            |            |              |                 |
|          |            | \"         |              |                 |
|          |            | ipAddress\ |              |                 |
|          |            | ":\"192.16 |              |                 |
|          |            | 8.10.2\"， |              |                 |
|          |            |            |              |                 |
|          |            | \"protocol |              |                 |
|          |            | \":\"TCP\" |              |                 |
|          |            |            |              |                 |
|          |            | \"src      |              |                 |
|          |            | port\":80, |              |                 |
|          |            |            |              |                 |
|          |            | \"dstpr    |              |                 |
|          |            | ot\":8080, |              |                 |
|          |            |            |              |                 |
|          |            | \"d        |              |                 |
|          |            | esc\":\"80 |              |                 |
|          |            | 端口映射\" |              |                 |
|          |            |            |              |                 |
|          |            | }          |              |                 |
+----------+------------+------------+--------------+-----------------+
|          | getUPnP    |            | {            |                 |
|          |            |            |              |                 |
|          |            |            | \"ahsapd.    |                 |
|          |            |            | control\"：  |                 |
|          |            |            |              |                 |
|          |            |            | UPnPList:\[  |                 |
|          |            |            |              |                 |
|          |            |            | {            |                 |
|          |            |            |              |                 |
|          |            |            | \"upn        |                 |
|          |            |            | pEnable\":1, |                 |
|          |            |            |              |                 |
|          |            |            | \"ipAddr     |                 |
|          |            |            | ess\":\"192. |                 |
|          |            |            | 168.10.2\"， |                 |
|          |            |            |              |                 |
|          |            |            | \"protoc     |                 |
|          |            |            | ol\":\"TCP\" |                 |
|          |            |            |              |                 |
|          |            |            | \"s          |                 |
|          |            |            | rcPort\":80, |                 |
|          |            |            |              |                 |
|          |            |            | \"dst        |                 |
|          |            |            | Prot\":8080, |                 |
|          |            |            |              |                 |
|          |            |            | \"desc\":\"  |                 |
|          |            |            | 80端口映射\" |                 |
|          |            |            |              |                 |
|          |            |            | }            |                 |
|          |            |            |              |                 |
|          |            |            | }            |                 |
|          |            |            |              |                 |
|          |            |            | \]           |                 |
+----------+------------+------------+--------------+-----------------+

操控指令event响应上报升定义如下表2.4.12 - 2 所示

> 表2.4.12 -- 2 操控指令event响应上报消息定义

+------------+-------------------+-------------------------+---------+
| 消息类型   | 消息内容          | 描述                    | 是      |
|            |                   |                         | 否必须  |
+============+===================+=========================+=========+
| con        | {                 | 需要长时间执行的        | Y       |
| trolReport |                   | 操作指令，执行完成后通  |         |
|            | \"                | 过事件上报执行结果,seq  |         |
|            | seqId\":\"XXXX\", | Id为对应配置下发的标识, |         |
|            |                   |                         |         |
|            | \"respType\": 3 , |                         |         |
|            |                   |                         |         |
|            | \"respCode\": 0   |                         |         |
|            |                   |                         |         |
|            | }                 |                         |         |
+------------+-------------------+-------------------------+---------+

组网设备操控指令参数定义如表2.4.12 - 3所示

> 表2.4.12 - 3设备操控指令参数

+-----------+-----------+----------+-----------------------+----------+
| 参数名    | 类型      | 读写权限 | 描述                  | 是否必须 |
+===========+===========+==========+=======================+==========+
| co        | Numbe     | 只写     | 1: reboot立刻重启；   | Y        |
| ntrolType | r(uint32) |          |                       |          |
|           |           |          | 2:                    |          |
|           |           |          | 2.4                   |          |
|           |           |          | GChanReSelect\"：重新 |          |
|           |           |          | 自动选择2.4G频段WiFi  |          |
|           |           |          | 工作信道（下挂STA设备 |          |
|           |           |          | 使用的WiFi工作信道）  |          |
|           |           |          |                       |          |
|           |           |          | 3:                    |          |
|           |           |          | \                     |          |
|           |           |          | "5GChanReSelect\"：重 |          |
|           |           |          | 新自动选择5G频段WiFi  |          |
|           |           |          | 工作信道（下挂STA设备 |          |
|           |           |          | 使用的WiFi工作信道）  |          |
+-----------+-----------+----------+-----------------------+----------+
| ledOnOff  | Numbe     | 读写     | 0或1（0: 关闭，1:     | Y        |
|           | r(uint32) |          | 打开）                |          |
+-----------+-----------+----------+-----------------------+----------+
| l         | Numb      | 读写     | 0: 锁网关闭，1:       | Y        |
| ockStatus | er(uin32) |          | 锁网开启              |          |
+-----------+-----------+----------+-----------------------+----------+
| fileType  | Numbe     | 只写     | 1: 升级固件           | Y        |
|           | r(uint32) |          |                       |          |
|           |           |          | 2：升级配置文件       |          |
|           |           |          |                       |          |
|           |           |          | 3：预留               |          |
+-----------+-----------+----------+-----------------------+----------+
| localPath | String    | 只写     | 升级文件本地的路径    | Y        |
|           |           |          | 或下载路径URL，当路径 |          |
|           |           |          | 为设备本地文件，设备  |          |
|           |           |          | 直接升级，当路径为网  |          |
|           |           |          | 络下载地址时，设备需  |          |
|           |           |          | 先下载升级文件在升级  |          |
+-----------+-----------+----------+-----------------------+----------+
| reboot    | Numbe     | 只写     | 升级                  | Y        |
|           | r(uint32) |          | 后智能组网终端是否立  |          |
|           |           |          | 即重启生效，取值为：  |          |
|           |           |          |                       |          |
|           |           |          | 0:                    |          |
|           |           |          | 不立即重启，          |          |
|           |           |          | 等待下次重启后生效，  |          |
|           |           |          |                       |          |
|           |           |          | 1: 立即重启生效       |          |
+-----------+-----------+----------+-----------------------+----------+
| pmfEnable | Numbe     | 读写     | PMF（Protected        | Y        |
|           | r(uint32) |          | Management Frame）    |          |
|           |           |          |                       |          |
|           |           |          | 1：打开PMF开关        |          |
|           |           |          |                       |          |
|           |           |          | 0：关闭PMF开关        |          |
+-----------+-----------+----------+-----------------------+----------+
| count     | String    | 读写     | 设备硬加速，软        | Y        |
|           |           |          | 件协议栈处理报文个数  |          |
|           |           |          |                       |          |
|           |           |          | -1：关闭硬加速        |          |
|           |           |          |                       |          |
|           |           |          | 0：报文均走硬加速     |          |
|           |           |          |                       |          |
|           |           |          | 其它(正值)：报        |          |
|           |           |          | 文在协议栈处理的数量  |          |
+-----------+-----------+----------+-----------------------+----------+
| logSwitch | Numbe     | 读写     | 日志采集开关 0 ：关闭 | Y        |
|           | r(uint32) |          | 1：开启               |          |
+-----------+-----------+----------+-----------------------+----------+
| logLevel  | Numbe     | 读写     | 日志采集级别:         | Y        |
|           | r(uint32) |          |                       |          |
|           |           |          | 0：ALL                |          |
|           |           |          |                       |          |
|           |           |          | 1: INFO               |          |
|           |           |          |                       |          |
|           |           |          | 2: ERROR              |          |
|           |           |          |                       |          |
|           |           |          | 3： DEBUG             |          |
+-----------+-----------+----------+-----------------------+----------+
| logTime   | Numbe     | 读写     | 设备                  | Y        |
|           | r(uint64) |          | 日志采集时间单位秒(S) |          |
|           |           |          | ,日志开启后采集时间， |          |
|           |           |          | 值为0时就返回当前存量 |          |
|           |           |          | 日志，值大于0时表示再 |          |
|           |           |          | 存量日志的基础上再采  |          |
|           |           |          | 集多少时间，日志大小  |          |
|           |           |          | 根据硬件空间自行定义  |          |
+-----------+-----------+----------+-----------------------+----------+
| logPath   | String    | 只读     | 日志文件保存          | Y        |
|           |           |          | 路径，日志文件需可读  |          |
+-----------+-----------+----------+-----------------------+----------+
| i         | Numbe     | 读写     | 组网终端IPv4/IPv      | Y        |
| pv6Enable | r(uint32) |          | 6双栈状态，枚举取值； |          |
|           |           |          |                       |          |
|           |           |          | 0：关闭IPv4/IPv6双栈  |          |
|           |           |          |                       |          |
|           |           |          | 1：开启Ipv4/IPv6双栈  |          |
+-----------+-----------+----------+-----------------------+----------+
| respType  | Numbe     | 只读     | 操                    | Y        |
|           | r(uint32) |          | 作指令event上报类型： |          |
|           |           |          |                       |          |
|           |           |          | 1: 固件/文件下载      |          |
|           |           |          |                       |          |
|           |           |          | 2: 固件升级           |          |
|           |           |          |                       |          |
|           |           |          | 3：日志获取           |          |
|           |           |          |                       |          |
|           |           |          | 4：待扩展             |          |
+-----------+-----------+----------+-----------------------+----------+
| respCode  | Numbe     | 只读     | 对应操                | Y        |
|           | r(uint32) |          | 作指令事件执行结果：  |          |
|           |           |          |                       |          |
|           |           |          | 0：执行成功           |          |
|           |           |          |                       |          |
|           |           |          | 1：执行失败           |          |
|           |           |          |                       |          |
|           |           |          | 2：待扩展             |          |
+-----------+-----------+----------+-----------------------+----------+
| seqId     | String    | 读写     | 配置对                | N        |
|           |           |          | 应唯一标识；如配置下  |          |
|           |           |          | 发携带seqId，event上  |          |
|           |           |          | 报必选携带下发的seqId |          |
+-----------+-----------+----------+-----------------------+----------+
| multic    | Numbe     | 读写     | 组播转单播控制开关    | Y        |
| astEnable | r(uint32) |          |                       |          |
+-----------+-----------+----------+-----------------------+----------+
| DMZList   | Array of  | 读写     | 暂定                  | Y        |
|           | Object    |          |                       |          |
+-----------+-----------+----------+-----------------------+----------+
| DMZEnable | Numbe     | 读写     | 暂定                  | Y        |
|           | r(uint32) |          |                       |          |
+-----------+-----------+----------+-----------------------+----------+
| ipAddress | String    | 读写     | 暂定                  | Y        |
+-----------+-----------+----------+-----------------------+----------+
| UPnPList  | Array of  | 读写     | 暂定                  | Y        |
|           | Object    |          |                       |          |
+-----------+-----------+----------+-----------------------+----------+
| U         | Numbe     | 读写     | 暂定                  | Y        |
| PnPEnable | r(uint32) |          |                       |          |
+-----------+-----------+----------+-----------------------+----------+
| ipAddress | String    | 读写     | 暂定                  | Y        |
+-----------+-----------+----------+-----------------------+----------+
| protocol  | String    | 读写     | 暂定                  | Y        |
+-----------+-----------+----------+-----------------------+----------+
| srcPort   | Numbe     | 读写     | 暂定                  | Y        |
|           | r(uint32) |          |                       |          |
+-----------+-----------+----------+-----------------------+----------+
| dstProt   | Numbe     | 读写     | 暂定                  | Y        |
|           | r(uint32) |          |                       |          |
+-----------+-----------+----------+-----------------------+----------+
| desc      | String    | 读写     | 暂定                  | Y        |
+-----------+-----------+----------+-----------------------+----------+

# 2.4.13 组网设备配置保存 {#组网设备配置保存 .QB标题3}

插件调用接口：

int ahsapi_cfg_add_item(char\* item , char\* value) //配置数据保存

int ahsapi_cfg_get_item(char\* item, char\* value, int len)数据获取

int ahsapi_cfg_delete_item(char\* item) //配置数据删除

int ahsapi_get_cfg_path(int filetype, char\* cfgPath, int len)
//配置文件路径

item、value、 cfgPath 、len为普通字符串

组网设备配置保存对象方法定义如表2.4.13-1所示：

> 表2.4.13 - 1 设备配置保存对象方法

+-----------+-----------+--------------+-----------------+------------+
| 对象名    | 方法名    | 参数         | 返回参数        | 描述       |
+===========+===========+==============+=================+============+
| ahsapd.   | addItem   | {            |                 | 配置保存和 |
| config    |           |              | --------------- | 更新，如果 |
|           |           | \"ite        |   无        1.0 | item项值不 |
|           |           | m\":\"XXX\", |                 | 存在，新增 |
|           |           |              | ---- ---- ----- | ；如果item |
|           |           | \"val        |                 | 项值存在， |
|           |           | ue\":\"XXX\" |                 | 更新value  |
|           |           |              | --------------- |            |
|           |           | }            |                 |            |
+-----------+-----------+--------------+-----------------+------------+
|           | getItem   | {            | {               | 配置获取   |
|           |           |              |                 |            |
|           |           | \"it         | \"ah            |            |
|           |           | em\":\"XXX\" | sapd.config\":{ |            |
|           |           |              |                 |            |
|           |           | }            | \"              |            |
|           |           |              | value\":\"XXX\" |            |
|           |           |              |                 |            |
|           |           |              | }               |            |
|           |           |              |                 |            |
|           |           |              | }               |            |
+-----------+-----------+--------------+-----------------+------------+
|           | d         | {            | 无              | 配置删除   |
|           | eleteItem |              |                 |            |
|           |           | \"it         |                 |            |
|           |           | em\":\"XXX\" |                 |            |
|           |           |              |                 |            |
|           |           | }            |                 |            |
+-----------+-----------+--------------+-----------------+------------+
|           | g         | {            | {               | 获取配     |
|           | etCfgPath |              |                 | 置文件路径 |
|           |           | \            | \"ah            |            |
|           |           | "fileType\": | sapd.config\":{ |            |
|           |           | 1            |                 |            |
|           |           |              | \"cf            |            |
|           |           | }            | gPath\":\"XXX\" |            |
|           |           |              |                 |            |
|           |           |              | }               |            |
|           |           |              |                 |            |
|           |           |              | }               |            |
+-----------+-----------+--------------+-----------------+------------+

组网设备配置保存参数定义如表2.4.13 - 2所示：

> 表2.4.13 - 2 设备配置保存参数

+-----------+-----------+------------+-----------------+------------+
| 参数名    | 类型      | 读写权限   | 描述            | 是否必须   |
+===========+===========+============+=================+============+
| item      | String    | 只写       | 配置项名称      | Y          |
+-----------+-----------+------------+-----------------+------------+
| value     | String    | 只写       | 配置项的值      | Y          |
+-----------+-----------+------------+-----------------+------------+
| fileType  | Numbe     | 只写       | 1:              | Y          |
|           | r(uint32) |            | 设备保存        |            |
|           |           |            | 配置数据文件；  |            |
|           |           |            |                 |            |
|           |           |            | 2：             |            |
|           |           |            | 组网插件（AHS-  |            |
|           |           |            | NET）配置文件； |            |
|           |           |            |                 |            |
|           |           |            | 3：其它预留     |            |
+-----------+-----------+------------+-----------------+------------+
| cfgPath   | String    | 只读       | 文件路径        | Y          |
+-----------+-----------+------------+-----------------+------------+

# 2.4.14 组网设备Mesh {#组网设备mesh .QB标题3}

组网设备mesh配置基于支持mesh功能的设备，非mesh设备不支持本章节内容

插件调用接口：

int ahsapi_map_set_mesh_role(int role) //配置mesh角色

int ahsapi_map_set_mesh_roaming(int enable) //配置mesh 漫游开关

int ahsapi_map_get_mesh_info(cJSON \*\*meshInfo) //获取mesh 状态信息

int ahsapi_map_get_mesh_topology(cJSON \*\*meshTopology)
//获取mesh拓扑结构

meshInfo 和meshTopology 为JSON字符串，包含参数内容如下表

组网设备Mesh对象方法定义如表2.4.14 - 1所示：

> 表2.4.14 - 1 设备配置保存对象方法

+------------+----------+-----------------+---------------+----------+
| 对象名     | 方法名   | 参数            | 返回参数      | 描述     |
+============+==========+=================+===============+==========+
| a          | set      | {               |               | 设备mesh |
| hsapd.mesh | MeshRole |                 | ------------- | 角色     |
|            |          | \"meshRole\": 1 |   无      1.0 |          |
|            |          |                 |               |          |
|            |          | }               | ---- -- ----- |          |
|            |          |                 |               |          |
|            |          |                 |               |          |
|            |          |                 | ------------- |          |
+------------+----------+-----------------+---------------+----------+
|            | setM     | {               | 无            | 设置mesh |
|            | eshRoami |                 |               | 漫游开关 |
|            | ngEnable | \"me            |               |          |
|            |          | shRoamingEnable |               |          |
|            |          | \":1            |               |          |
|            |          |                 |               |          |
|            |          | }               |               |          |
+------------+----------+-----------------+---------------+----------+
|            | get      | 无              | {             | 获取mesh |
|            | MeshInfo |                 |               | 状态信息 |
|            |          |                 | \"ah          |          |
|            |          |                 | sapd.mesh\":{ |          |
|            |          |                 |               |          |
|            |          |                 | \"mesh        |          |
|            |          |                 | RoamingEnable |          |
|            |          |                 | \":1,         |          |
|            |          |                 |               |          |
|            |          |                 | \"            |          |
|            |          |                 | meshType\":1, |          |
|            |          |                 |               |          |
|            |          |                 | \"            |          |
|            |          |                 | meshRole\":1, |          |
|            |          |                 |               |          |
|            |          |                 | \"upTim       |          |
|            |          |                 | e\":\"100\"， |          |
|            |          |                 |               |          |
|            |          |                 | \"ipAd        |          |
|            |          |                 | dress\":\"192 |          |
|            |          |                 | .168.1.10\"， |          |
|            |          |                 |               |          |
|            |          |                 | \"            |          |
|            |          |                 | ipv6IPAddress |          |
|            |          |                 | \":\"2001:DA8 |          |
|            |          |                 | :1000::F1\"， |          |
|            |          |                 |               |          |
|            |          |                 | \"deviceVend  |          |
|            |          |                 | or\":\"01\"， |          |
|            |          |                 |               |          |
|            |          |                 | \"deviceModel |          |
|            |          |                 | \":\"1101\"， |          |
|            |          |                 |               |          |
|            |          |                 | \"devi        |          |
|            |          |                 | ceMac\":\"001 |          |
|            |          |                 | 12233ABCD\"， |          |
|            |          |                 |               |          |
|            |          |                 | \"            |          |
|            |          |                 | meshStatus\": |          |
|            |          |                 | 1,            |          |
|            |          |                 |               |          |
|            |          |                 | \             |          |
|            |          |                 | "staNumber\": |          |
|            |          |                 | 2,            |          |
|            |          |                 |               |          |
|            |          |                 | \"bhInfo\":{  |          |
|            |          |                 |               |          |
|            |          |                 | \"bhTyp       |          |
|            |          |                 | e\":\"WLAN\", |          |
|            |          |                 |               |          |
|            |          |                 | \"b           |          |
|            |          |                 | hAlmac\":\"08 |          |
|            |          |                 | 107A33ABCD\", |          |
|            |          |                 |               |          |
|            |          |                 | \"radi        |          |
|            |          |                 | o\":\"2.4G\", |          |
|            |          |                 |               |          |
|            |          |                 | \"ssid\":\    |          |
|            |          |                 | "cmcc-test\", |          |
|            |          |                 |               |          |
|            |          |                 | \"p           |          |
|            |          |                 | wd\":\"123456 |          |
|            |          |                 | \",           |          |
|            |          |                 |               |          |
|            |          |                 | \"secu        |          |
|            |          |                 | rityMode\":\" |          |
|            |          |                 | MIXED-WPAPSK2 |          |
|            |          |                 |               |          |
|            |          |                 | \",           |          |
|            |          |                 |               |          |
|            |          |                 | \"rxRate\":   |          |
|            |          |                 | \"100\",      |          |
|            |          |                 |               |          |
|            |          |                 | \"txRate\":   |          |
|            |          |                 | \"100\"       |          |
|            |          |                 |               |          |
|            |          |                 | }             |          |
|            |          |                 |               |          |
|            |          |                 | }             |          |
|            |          |                 |               |          |
|            |          |                 | }             |          |
+------------+----------+-----------------+---------------+----------+
|            | getMesh  | 无              | {             | 获取mesh |
|            | Topology |                 |               | 拓       |
|            |          |                 | \"ah          | 扑结构， |
|            |          |                 | sapd.mesh\":{ |          |
|            |          |                 |               |          |
|            |          |                 | \"mesh        |          |
|            |          |                 | Topology\":\[ |          |
|            |          |                 |               |          |
|            |          |                 | {             |          |
|            |          |                 |               |          |
|            |          |                 | \"            |          |
|            |          |                 | meshType\":1, |          |
|            |          |                 |               |          |
|            |          |                 | \"meshRole\": |          |
|            |          |                 | 1,            |          |
|            |          |                 |               |          |
|            |          |                 | \"upTim       |          |
|            |          |                 | e\":\"100\"， |          |
|            |          |                 |               |          |
|            |          |                 | \"ipAd        |          |
|            |          |                 | dress\":\"192 |          |
|            |          |                 | .168.1.10\"， |          |
|            |          |                 |               |          |
|            |          |                 | \"            |          |
|            |          |                 | ipv6IPAddress |          |
|            |          |                 | \":\"2001:DA8 |          |
|            |          |                 | :1000::F1\"， |          |
|            |          |                 |               |          |
|            |          |                 | \"deviceVend  |          |
|            |          |                 | or\":\"01\"， |          |
|            |          |                 |               |          |
|            |          |                 | \"deviceModel |          |
|            |          |                 | \":\"1101\"， |          |
|            |          |                 |               |          |
|            |          |                 | \             |          |
|            |          |                 | "deviceMac\": |          |
|            |          |                 | \"00          |          |
|            |          |                 | 112233ABCD\", |          |
|            |          |                 |               |          |
|            |          |                 | \"            |          |
|            |          |                 | meshStatus\": |          |
|            |          |                 | 1,            |          |
|            |          |                 |               |          |
|            |          |                 | \             |          |
|            |          |                 | "staNumber\": |          |
|            |          |                 | 2,            |          |
|            |          |                 |               |          |
|            |          |                 | \"bhInfo\":{  |          |
|            |          |                 |               |          |
|            |          |                 | \"bhTyp       |          |
|            |          |                 | e\":\"WLAN\", |          |
|            |          |                 |               |          |
|            |          |                 | \             |          |
|            |          |                 | "bhAlmac\":\" |          |
|            |          |                 | \",           |          |
|            |          |                 |               |          |
|            |          |                 | \"ssi         |          |
|            |          |                 | d\":\"1234\", |          |
|            |          |                 |               |          |
|            |          |                 | \"pwd\":\"    |          |
|            |          |                 | 86745test\",  |          |
|            |          |                 |               |          |
|            |          |                 | \"securit     |          |
|            |          |                 | yMode\":\"\", |          |
|            |          |                 |               |          |
|            |          |                 | \"rxRate\":   |          |
|            |          |                 | \"100\",      |          |
|            |          |                 |               |          |
|            |          |                 | \"txRate\":   |          |
|            |          |                 | \"100\"       |          |
|            |          |                 |               |          |
|            |          |                 | }             |          |
|            |          |                 |               |          |
|            |          |                 | }             |          |
|            |          |                 |               |          |
|            |          |                 | \]            |          |
|            |          |                 |               |          |
|            |          |                 | }             |          |
|            |          |                 |               |          |
|            |          |                 | }             |          |
+------------+----------+-----------------+---------------+----------+

Mesh状态信息变化主动event上报定义如下表2.4.14 - 2 所示

表2.4.14 -2 mesh信息变化消息通知

+------------+-------------------+-------------------------+---------+
| 消息类型   | 消息内容          | 描述                    | 是      |
|            |                   |                         | 否必须  |
+============+===================+=========================+=========+
| meshInfo   | {                 | Mesh状态信息            | Y       |
|            |                   | 变化消息通知,meshRole,  |         |
|            | \"meshType\":1,   | IP地址，meshStatus,     |         |
|            |                   | bhType等因              |         |
|            | \"meshRole\":1,   | 素发生变化，主动通知；  |         |
|            |                   |                         |         |
|            | \"u               |                         |         |
|            | pTime\":\"100\"， |                         |         |
|            |                   |                         |         |
|            | \"ipAddress\":\   |                         |         |
|            | "************\"， |                         |         |
|            |                   |                         |         |
|            | \"ipv6I           |                         |         |
|            | PAddress\":\"2001 |                         |         |
|            | :DA8:1000::F1\"， |                         |         |
|            |                   |                         |         |
|            | \"device          |                         |         |
|            | Vendor\":\"01\"， |                         |         |
|            |                   |                         |         |
|            | \"deviceM         |                         |         |
|            | odel\":\"1101\"， |                         |         |
|            |                   |                         |         |
|            | \"deviceMac\":\   |                         |         |
|            | "00112233ABCD\"， |                         |         |
|            |                   |                         |         |
|            | \"meshStatus\": 0 |                         |         |
|            |                   |                         |         |
|            | \"bhInfo\":{      |                         |         |
|            |                   |                         |         |
|            | \"b               |                         |         |
|            | hType\":\"WLAN\", |                         |         |
|            |                   |                         |         |
|            | \"bhAlmac\":      |                         |         |
|            | \"08107A33ABCD\", |                         |         |
|            |                   |                         |         |
|            | \"                |                         |         |
|            | radio\":\"2.4G\", |                         |         |
|            |                   |                         |         |
|            | \"ssid            |                         |         |
|            | \":\"cmcc-test\", |                         |         |
|            |                   |                         |         |
|            | \"pwd\":\"123456  |                         |         |
|            | \",               |                         |         |
|            |                   |                         |         |
|            | \                 |                         |         |
|            | "securityMode\":\ |                         |         |
|            | "MIXED-WPAPSK2\", |                         |         |
|            |                   |                         |         |
|            | \"rxRate\":       |                         |         |
|            | \"300\",          |                         |         |
|            |                   |                         |         |
|            | \"txRate\":       |                         |         |
|            | \"300\"           |                         |         |
|            |                   |                         |         |
|            | }                 |                         |         |
|            |                   |                         |         |
|            | }                 |                         |         |
+------------+-------------------+-------------------------+---------+

组网设备EasyMesh参数定义如表2.4.14 - 3所示：

> 表2.4.14 - 3设备EasyMesh参数

+-----------+-----------+----------+------------------------+---------+
| 参数名    | 类型      | 读写权限 | 描述                   | 是      |
|           |           |          |                        | 否必须  |
+===========+===========+==========+========================+=========+
| meshRoam  | Numbe     | 读写     | Mesh 漫游开关          | Y       |
| ingEnable | r(uint32) |          |                        |         |
+-----------+-----------+----------+------------------------+---------+
| mes       | Array of  | 读写     | 拓扑网络信息           | Y       |
| hTopology | Object    |          |                        |         |
+-----------+-----------+----------+------------------------+---------+
| meshType  | Numbe     | 只读     | 0：非mesh终端          | Y       |
|           | r(uint32) |          |                        |         |
|           |           |          | 1：easyMesh            |         |
|           |           |          |                        |         |
|           |           |          | 2：其它mesh            |         |
+-----------+-----------+----------+------------------------+---------+
| meshRole  | Numbe     | 读写     | Mesh角色               | Y       |
|           | r(uint32) |          |                        |         |
|           |           |          | 0：非mesh终端          |         |
|           |           |          |                        |         |
|           |           |          | 1: Controller          |         |
|           |           |          |                        |         |
|           |           |          | 2: Agent               |         |
|           |           |          |                        |         |
|           |           |          | 3: AC                  |         |
|           |           |          |                        |         |
|           |           |          | 4: AP                  |         |
|           |           |          |                        |         |
|           |           |          | 5: Auto(自动选择角色)  |         |
|           |           |          |                        |         |
|           |           |          | 注：当读取m            |         |
|           |           |          | eshRole时，值范围为0 - |         |
|           |           |          | 4，单配置meshRole      |         |
|           |           |          | 是                     |         |
|           |           |          | 值范围为1-5；非mesh终  |         |
|           |           |          | 端不需要配置mesh角色； |         |
+-----------+-----------+----------+------------------------+---------+
| upTime    | String    | 只读     | 在线时长，单位秒；     | Y       |
+-----------+-----------+----------+------------------------+---------+
| ipAddress | String    | 只读     | 拓扑设备IP地址         | Y       |
+-----------+-----------+----------+------------------------+---------+
| ipv6      | String    | 只读     | 拓扑设备IPV6 地址      | Y       |
| IPAddress |           |          |                        |         |
+-----------+-----------+----------+------------------------+---------+
| dev       | String    | 只读     | 拓扑设备厂商编码       | N       |
| iceVendor |           |          |                        |         |
+-----------+-----------+----------+------------------------+---------+
| de        | String    | 只读     | 拓扑设备型号编码       | N       |
| viceModel |           |          |                        |         |
+-----------+-----------+----------+------------------------+---------+
| deviceMac | String    | 只读     | 设备MAC地址，格式大    | Y       |
|           |           |          | 写去掉冒"00112233ABCD" |         |
+-----------+-----------+----------+------------------------+---------+
| m         | Numbe     | 只读     | 组                     | Y       |
| eshStatus | r(uint32) |          | 网设备是否加入mesh组网 |         |
|           |           |          |                        |         |
|           |           |          | 1：是                  |         |
|           |           |          |                        |         |
|           |           |          | 0：否                  |         |
+-----------+-----------+----------+------------------------+---------+
| bhInfo    | Object    | 只读     | mesh设备的上联backhaul | Y       |
|           |           |          | BSS信息,包含bhT        |         |
|           |           |          | ype、bhAlmac、radio、s |         |
|           |           |          | sid、pwd、securityMode |         |
|           |           |          | 、rxRate、txRate、fibe |         |
|           |           |          | rRxPower、fiberTxPower |         |
+-----------+-----------+----------+------------------------+---------+
| bhType    | String    | 只读     | 上联                   | Y       |
|           |           |          | backhaul的连接方式，枚 |         |
|           |           |          | 举取值为WLAN、Etherne  |         |
|           |           |          | t、PLC、Cable、Optical |         |
|           |           |          | Ethe                   |         |
|           |           |          | rnet、GPON、XGPON、PoE |         |
+-----------+-----------+----------+------------------------+---------+
| bhAlmac   | String    | 只读     | 上联设备的almac，格式  | Y       |
|           |           |          | ："00112233ABCD"，如果 |         |
|           |           |          | meshRol                |         |
|           |           |          | e取值为"controller"或" |         |
|           |           |          | AC"时，则该字段表示    |         |
|           |           |          | 上联设备的链路mac地址  |         |
+-----------+-----------+----------+------------------------+---------+
| radio     | String    | 只读     | 上联接入方             | N       |
|           |           |          | 式是WLAN上联时使用的频 |         |
|           |           |          | 段，取值2.4G、5G或5G-2 |         |
|           |           |          | ，bhType为WLAN时必选； |         |
|           |           |          |                        |         |
|           |           |          | 注：对于               |         |
|           |           |          | 三频设备，5G表示5.8G频 |         |
|           |           |          | 段，5G-2表示5.2G频段； |         |
|           |           |          | 双频设备忽略5G-2频段； |         |
+-----------+-----------+----------+------------------------+---------+
| ssid      | String    | 只读     | ssid名称，上联         | N       |
|           |           |          | WLAN接入的SSID;bhType  |         |
|           |           |          | 为WLAN时必选；         |         |
+-----------+-----------+----------+------------------------+---------+
| pwd       | String    | 只读     | ssid密                 | N       |
|           |           |          | 码，上联WLAN接入的密码 |         |
|           |           |          | ，bhType为WLAN时必选； |         |
+-----------+-----------+----------+------------------------+---------+
| sec       | String    | 只读     | 认证加密模式，bhType为 | N       |
| urityMode |           |          | WLAN时必选，枚举取值： |         |
|           |           |          |                        |         |
|           |           |          | None                   |         |
|           |           |          |                        |         |
|           |           |          | WEP-64                 |         |
|           |           |          |                        |         |
|           |           |          | WEP-128                |         |
|           |           |          |                        |         |
|           |           |          | WPA-Personal           |         |
|           |           |          |                        |         |
|           |           |          | WPA2-Personal          |         |
|           |           |          |                        |         |
|           |           |          | MIXED-WPAPSK2          |         |
|           |           |          |                        |         |
|           |           |          | WPA3-SAE               |         |
|           |           |          |                        |         |
|           |           |          | MIXED-WPA2WPA3         |         |
+-----------+-----------+----------+------------------------+---------+
| rxRate    | String    | 只读     | 协商接收速率，单位Mbps | Y       |
+-----------+-----------+----------+------------------------+---------+
| txRate    | String    | 只读     | 协商发送速率，单位Mbps | Y       |
+-----------+-----------+----------+------------------------+---------+
| fib       | String    | 只读     | 光上联时发射光功率，单 | N       |
| erRxPower |           |          | 位dBm；光接入时必选；  |         |
+-----------+-----------+----------+------------------------+---------+
| fib       | String    | 只读     | 光上联时接收光功率，单 | N       |
| erTxPower |           |          | 位dBm；光接入时必选；  |         |
+-----------+-----------+----------+------------------------+---------+
| staNumber | Numbe     | 只读     | 下                     | Y       |
|           | r(uint32) |          | 挂设备数据，当值为0时  |         |
|           |           |          | ，下挂设备信息数组为空 |         |
+-----------+-----------+----------+------------------------+---------+

# 2.4.15 组网设备网络限速 {#组网设备网络限速 .QB标题3}

插件调用接口：

int ahsapi_set_device_limit(cJSON\* deviceLimit) // 配置设备限速

int ahsapi_get_device_limit(cJSON\*\* deviceLimit) //
获取所有设备限速配置

deviceLimit为JSON字符串，包含参数内容如下表

组网设备网络限速对象方法定义如表2.4.15-1所示：

> 表2.4.15 - 1 设备网络限速对象方法

+---------+-----------+--------------------+----------------+--------+
| 对象名  | 方法名    | 参数               | 返回参数       | 描述   |
+=========+===========+====================+================+========+
| ahsapd. | setDe     | {                  |                | 配置设 |
| limit   | viceLimit |                    | -------------- | 备限速 |
|         |           | \" devices\": \[   |   无       1.0 |        |
|         |           |                    |                |        |
|         |           | {                  | ---- --- ----- |        |
|         |           |                    |                |        |
|         |           | \"deviceMa         |                |        |
|         |           | c\":\"AABBCCDDEEFF | -------------- |        |
|         |           | \",                |                |        |
|         |           |                    |                |        |
|         |           | \"upSpeed\": \"    |                |        |
|         |           | 10\" ,             |                |        |
|         |           |                    |                |        |
|         |           | \"downSpeed\":     |                |        |
|         |           | \"10 \"            |                |        |
|         |           |                    |                |        |
|         |           | },                 |                |        |
|         |           |                    |                |        |
|         |           | {                  |                |        |
|         |           |                    |                |        |
|         |           | \"deviceMa         |                |        |
|         |           | c\":\"ABABCDCDEFEF |                |        |
|         |           | \",                |                |        |
|         |           |                    |                |        |
|         |           | \"upSpeed\":       |                |        |
|         |           | \"20\" ,           |                |        |
|         |           |                    |                |        |
|         |           | \"downSpeed\":     |                |        |
|         |           | \"20\",            |                |        |
|         |           |                    |                |        |
|         |           | \"limitedUrl\":\   |                |        |
|         |           | "www.taobao.com\", |                |        |
|         |           |                    |                |        |
|         |           | \"device           |                |        |
|         |           | LimitMode\":\"1\", |                |        |
|         |           |                    |                |        |
|         |           | \"devi             |                |        |
|         |           | ceLimitWeek\":127, |                |        |
|         |           |                    |                |        |
|         |           | \"deviceLimit      |                |        |
|         |           | TimeOffset1\":100, |                |        |
|         |           |                    |                |        |
|         |           | \"deviceLimitT     |                |        |
|         |           | imeOffset2\":10000 |                |        |
|         |           |                    |                |        |
|         |           | }                  |                |        |
|         |           |                    |                |        |
|         |           | \]                 |                |        |
|         |           |                    |                |        |
|         |           | }                  |                |        |
+---------+-----------+--------------------+----------------+--------+
|         | getDe     | 无                 | {              | 获取   |
|         | viceLimit |                    |                | 所有限 |
|         |           |                    | \"ah           | 速配置 |
|         |           |                    | sapd.limit\":{ |        |
|         |           |                    |                |        |
|         |           |                    | \"devices\":   |        |
|         |           |                    | \[             |        |
|         |           |                    |                |        |
|         |           |                    | {              |        |
|         |           |                    |                |        |
|         |           |                    | \"d            |        |
|         |           |                    | eviceMac\":\"A |        |
|         |           |                    | ABBCCDDEEFF\", |        |
|         |           |                    |                |        |
|         |           |                    | \"upSpeed\":   |        |
|         |           |                    | \" 10\" ,      |        |
|         |           |                    |                |        |
|         |           |                    | \"downSpeed\": |        |
|         |           |                    | \"10 \"        |        |
|         |           |                    |                |        |
|         |           |                    | },             |        |
|         |           |                    |                |        |
|         |           |                    | {              |        |
|         |           |                    |                |        |
|         |           |                    | \"             |        |
|         |           |                    | deviceMac\":\" |        |
|         |           |                    | AABBCCDDEEFF   |        |
|         |           |                    | \",            |        |
|         |           |                    |                |        |
|         |           |                    | \"upSpeed\":   |        |
|         |           |                    | \"20\" ,       |        |
|         |           |                    |                |        |
|         |           |                    | \"downSpeed\": |        |
|         |           |                    | \"20 \",       |        |
|         |           |                    |                |        |
|         |           |                    | \"limi         |        |
|         |           |                    | tedUrl\":\"www |        |
|         |           |                    | .taobao.com\", |        |
|         |           |                    |                |        |
|         |           |                    | \"deviceLimi   |        |
|         |           |                    | tMode\":\"1\", |        |
|         |           |                    |                |        |
|         |           |                    | \"deviceLi     |        |
|         |           |                    | mitWeek\":127, |        |
|         |           |                    |                |        |
|         |           |                    | \"d            |        |
|         |           |                    | eviceLimitTime |        |
|         |           |                    | Offset1\":100, |        |
|         |           |                    |                |        |
|         |           |                    | \"de           |        |
|         |           |                    | viceLimitTimeO |        |
|         |           |                    | ffset2\":10000 |        |
|         |           |                    |                |        |
|         |           |                    | },             |        |
|         |           |                    |                |        |
|         |           |                    | ....           |        |
|         |           |                    |                |        |
|         |           |                    | \]             |        |
|         |           |                    |                |        |
|         |           |                    | }              |        |
|         |           |                    |                |        |
|         |           |                    | }              |        |
+---------+-----------+--------------------+----------------+--------+

组网设备deviceLimit参数定义如表2.4.15-2所示：

> 表2.4.15 - 2设备网络限速参数

+-------------+------------+--------+----------------------+----------+
| 参数名      | 类型       | 读     | 描述                 | 是否必须 |
|             |            | 写权限 |                      |          |
+=============+============+========+======================+==========+
| devices     | Array of   | 读写   | 设备限速配置列表     | Y        |
|             | Object     |        |                      |          |
+-------------+------------+--------+----------------------+----------+
| deviceMac   | String     | 读写   | 组网设备MAC地址或    | Y        |
|             |            |        | 组网下挂设备MAC地址  |          |
|             |            |        | （如配置的MAC是组网  |          |
|             |            |        | 设备的MAC限速对所有  |          |
|             |            |        | 下挂设备有效，如配置 |          |
|             |            |        | MAC地址是下挂设备MAC |          |
|             |            |        | 限速对当前下挂设备有 |          |
|             |            |        | 效；MAC地址为组网设  |          |
|             |            |        | 备的配置优先级最高） |          |
+-------------+------------+--------+----------------------+----------+
| upSpeed     | String     | 读写   | 设备上行限速(单位K   | Y        |
|             |            |        | pbs)，值为正值是有效 |          |
|             |            |        | 限速值，为0取消限速  |          |
+-------------+------------+--------+----------------------+----------+
| downSpeed   | String     | 读写   | 设备下行限速(单位K   | Y        |
|             |            |        | pbs)，值为正值是有效 |          |
|             |            |        | 限速值，为0取消限速  |          |
+-------------+------------+--------+----------------------+----------+
| limitedUrl  | String     | 读写   | 限制访问域名列       | N        |
|             |            |        | 表（路由模式需要支持 |          |
|             |            |        | ）；多组域名之间以逗 |          |
|             |            |        | 号分开，最多支持32组 |          |
|             |            |        | 。示例：\"www.taobao |          |
|             |            |        | .com,www.baidu.com\" |          |
+-------------+------------+--------+----------------------+----------+
| devi        | Numb       | 读写   | 是否周期启用         | N        |
| ceLimitMode | er(uint32) |        | 设备限速和访问限制： |          |
|             |            |        |                      |          |
|             |            |        | 0：关                |          |
|             |            |        | 闭周期控制，立即执行 |          |
|             |            |        |                      |          |
|             |            |        | 1：启用周期控        |          |
|             |            |        | 制，按周期执行；(在  |          |
|             |            |        | DeviceLimitTimeOffse |          |
|             |            |        | t1对应时间加入速率限 |          |
|             |            |        | 制，在DeviceLimitTim |          |
|             |            |        | eOffset2对应时间解除 |          |
|             |            |        | 速率限制。当DeviceLi |          |
|             |            |        | mitTimeOffset2小于De |          |
|             |            |        | viceLimitTimeOffset1 |          |
|             |            |        | 时，DeviceLimitTimeO |          |
|             |            |        | ffset2代表相对于第二 |          |
|             |            |        | 天0点的时间偏移量）  |          |
+-------------+------------+--------+----------------------+----------+
| devi        | Numb       | 读写   | 使用int值二进        | N        |
| ceLimitWeek | er(uint32) |        | 制位的低7位代表星期  |          |
|             |            |        | ，由高到低位依次代表 |          |
|             |            |        | 星期六五四三二一日。 |          |
|             |            |        | 如7(00000111B)表示周 |          |
|             |            |        | 二，周一和周日。若仅 |          |
|             |            |        | 需单次执行，值为0。  |          |
+-------------+------------+--------+----------------------+----------+
| deviceLimit | Numb       | 读写   | 执行时间偏移         | N        |
| TimeOffset1 | er(uint64) |        | 量1（执行任务的时间  |          |
|             |            |        | 点与当天凌晨0点的时  |          |
|             |            |        | 间差，以秒为单位）， |          |
|             |            |        | 该时刻启动速率限制； |          |
+-------------+------------+--------+----------------------+----------+
| deviceLimit | Numb       | 读写   | 行时间偏移           | N        |
| TimeOffset2 | er(uint64) |        | 量2（执行任务的时间  |          |
|             |            |        | 点与当天凌晨0点的时  |          |
|             |            |        | 间差，以秒为单位）， |          |
|             |            |        | 该时刻停止速率限制； |          |
+-------------+------------+--------+----------------------+----------+

# 2.4.16 组网设备持续测速 {#组网设备持续测速 .QB标题3}

插件调用接口：

int ahsapi_http_speed_test(cJSON\* speedTest) //组网设备 Http持续测速

speedTest为JSON字符串，包含参数内容如下表

组网设备探测对象方法定义如表2.4.16 -1所示：

> 表2.4.16 - 1 设备探测对象方法

+---------+------------+---------------------+-------------+---------+
| 对象名  | 方法名     | 参数                | 返回参数    | 描述    |
+=========+============+=====================+=============+=========+
| ahsapd. | htt        | {                   |   --        | http持  |
| de      | pSpeedTest |                     | ----------- | 续测速  |
| tection |            | \"seqId\":\"XXXX\", |             |         |
|         |            |                     | 无      1.0 |         |
|         |            | \"httpS             |   --        |         |
|         |            | peedTestUrl\":\"\", | -- -- ----- |         |
|         |            |                     |             |         |
|         |            | \"h                 |   --        |         |
|         |            | ttpSpeedTestTime\": | ----------- |         |
|         |            | 200,                |             |         |
|         |            |                     |             |         |
|         |            | \"httpS             |             |         |
|         |            | peedTestInterval\": |             |         |
|         |            | 100                 |             |         |
|         |            |                     |             |         |
|         |            | }                   |             |         |
+---------+------------+---------------------+-------------+---------+

设备http持续测速结果主动event上报定义如下表2.4.16 -2 所示

表2.4.16 - 2设备http持续测速结果信息主动上报消息定义

+-------------+-------------------+-------------------------+---------+
| 消息类型    | 消息内容          | 描述                    | 是      |
|             |                   |                         | 否必须  |
+=============+===================+=========================+=========+
| http        | {                 | 设备h                   | Y       |
| SpeedReport |                   | ttp持续测速结果事件上报 |         |
|             | \"                | ，根据下发指令间隔触发  |         |
|             | seqId\":\"XXXX\", | 事件上报，测试时间http  |         |
|             |                   | SpeedTestTime到结束上报 |         |
|             | \"httpS           |                         |         |
|             | peedTestRate\":\" |                         |         |
|             | \",               |                         |         |
|             |                   |                         |         |
|             | \                 |                         |         |
|             | "transmitRatio\": |                         |         |
|             | 1                 |                         |         |
|             |                   |                         |         |
|             | }                 |                         |         |
+-------------+-------------------+-------------------------+---------+

组网设备探测参数详细定义如表2.4.16 -3所示：

表2.4.16 -3组网设备探测参数

  --------------------------------------------------------------------------------------------------------------------------------------
  参数名                  类型             读写权限   描述                                                                   是否必须
  ----------------------- ---------------- ---------- ---------------------------------------------------------------------- -----------
  httpSpeedTestUrl        String           读写       Http持续测速文件下载地址                                               Y

  httpSpeedTestTime       Number(uint32)   读写       Http持续测速测试时间，单位S（秒）                                      Y

  httpSpeedTestInterval   Number(uint32)   读写       Http持续测速上报间隔时间,单位 ms(毫秒)                                 Y

  httpSpeedTestRate       String           只读       Http持续测速事件上报间隔平均速率值，单位Mpbs                           Y

  transmitRatio           Number(uint32)   只读       传输进度，0：测速结果事件上报未完成，1：测速结果事件上报完成           Y

  seqId                   String           读写       配置对应唯一标识,如配置下发携带seqId，event上报必选携带下发的seqId；   N
  --------------------------------------------------------------------------------------------------------------------------------------

# [2.4.17 组网设备WIFI信道状态信息(CSI)【可选】]{.mark} {#组网设备wifi信道状态信息csi可选 .QB标题3}

插件调用接口：

int ahsapi_set_csi_enable(cJSON \*csiEnable ) //配置CSI数据采集开关

int ahsapi_get_csi_enable(cJSON \*\*csiEnable) //获取CSI数据采集开关状态

int ahsapi_add_csi_mac(cJSON \*csiMac) //配置CSI采集下挂设备MAC白名单

int ahsapi_del_csi_mac(cJSON \*csiMac) //删除CSI采集下挂设备MAC白名单

int ahsapi_get_csi_mac(cJSON \*\*csiMacEntries) //获取CSI MAC地址白名单

int ahsapi_get_csi_version(cJSON \*\*csiVersion) //获取csi算法库版本

int ahsapi_get_csi_chip(cJSON \*\*csiChip) //获取wifi芯片信息

组网设备WiFi信道状态信息对象方法定义如表2.4.17 -- 1所示：

> 表2.4.17 -- 1设备WIFI信道状态信息对象方法

+------------+----------+--------------+-----------------+-----------+
| 对象名     | 方法名   | 参数         | 返回参数        | 描述      |
+============+==========+==============+=================+===========+
| ahsap      | setC     | {            | 无              | 配        |
| d.wifi.csi | SIEnable |              |                 | 置CSI数据 |
|            |          | \"seqId      |                 | 采集开关  |
|            |          | \"           |                 |           |
|            |          | :\"\*\*\*\", |                 | \[apd侧\] |
|            |          |              |                 |           |
|            |          | \"r          |                 |           |
|            |          | adio\":\"\", |                 |           |
|            |          |              |                 |           |
|            |          | "csiEnable": |                 |           |
|            |          |              |                 |           |
|            |          | }            |                 |           |
+------------+----------+--------------+-----------------+-----------+
|            | getC     | 无           | {               | 获        |
|            | SIEnable |              |                 | 取CSI数据 |
|            |          |              | \"              | 采集状态  |
|            |          |              | ahs             |           |
|            |          |              | apd.wifi.csi\": | \[apd侧\] |
|            |          |              | {               |           |
|            |          |              |                 |           |
|            |          |              | \"csiEnable\":  |           |
|            |          |              | \[{             |           |
|            |          |              |                 |           |
|            |          |              | \"radio\":\"\", |           |
|            |          |              |                 |           |
|            |          |              | \"csiEnable \": |           |
|            |          |              |                 |           |
|            |          |              | }\]             |           |
|            |          |              |                 |           |
|            |          |              | }               |           |
|            |          |              |                 |           |
|            |          |              | }               |           |
+------------+----------+--------------+-----------------+-----------+
|            | a        | {            | 无              | 添加      |
|            | ddCSIMac |              |                 | CSI数据采 |
|            |          | \"seqId      |                 | 集下挂设  |
|            |          | \"           |                 | 备MAC地址 |
|            |          | :\"\*\*\*\", |                 | 白名单(累 |
|            |          |              |                 | 加式添加  |
|            |          | \"r          |                 | 单个mac)  |
|            |          | adio\":\"\", |                 |           |
|            |          |              |                 | \[apd侧\] |
|            |          | \"csiMac     |                 |           |
|            |          | \":\"\"      |                 |           |
|            |          |              |                 |           |
|            |          | }            |                 |           |
+------------+----------+--------------+-----------------+-----------+
|            | d        | {            | 无              | 删除CSI   |
|            | elCSIMac |              |                 | MAC地     |
|            |          | \"seqId      |                 | 址白名单  |
|            |          | \"           |                 |           |
|            |          | :\"\*\*\*\", |                 | \[apd侧\] |
|            |          |              |                 |           |
|            |          | \"r          |                 |           |
|            |          | adio\":\"\", |                 |           |
|            |          |              |                 |           |
|            |          | \"c          |                 |           |
|            |          | siMac\":\"\" |                 |           |
|            |          |              |                 |           |
|            |          | }            |                 |           |
+------------+----------+--------------+-----------------+-----------+
|            | g        | 无           | {               | 获取CSI   |
|            | etCSIMac |              |                 | MA        |
|            |          |              | \"              | C地址白名 |
|            |          |              | ahs             | 单（mac用 |
|            |          |              | apd.wifi.csi\": | 逗号隔开  |
|            |          |              | {               | ，拼接字  |
|            |          |              |                 | 符串；包  |
|            |          |              | \"csiMac\":\[{  | 含2.4G和  |
|            |          |              |                 | 5G信息）  |
|            |          |              | \"radio\":\"\", |           |
|            |          |              |                 | \[apd侧\] |
|            |          |              | \"csiMacEntries |           |
|            |          |              | \":\"\",        |           |
|            |          |              |                 |           |
|            |          |              | }\]             |           |
|            |          |              |                 |           |
|            |          |              | }               |           |
|            |          |              |                 |           |
|            |          |              | }               |           |
+------------+----------+--------------+-----------------+-----------+
|            | getCS    | 无           | {               | 获        |
|            | IVersion |              |                 | 取版本号  |
|            |          |              | \"              | （ubusVe  |
|            |          |              | ahs             | rsion对应 |
|            |          |              | apd.wifi.csi\": | 集成规范  |
|            |          |              | {               | 版本号；  |
|            |          |              |                 | csiVersio |
|            |          |              | \               | n对应驱动 |
|            |          |              | "ubusVersion\": | 版本号）  |
|            |          |              | \"X.X.X\"       |           |
|            |          |              |                 | \[apd侧\] |
|            |          |              | \"csiVersion\": |           |
|            |          |              | \"X.X.X\"       |           |
|            |          |              |                 |           |
|            |          |              | }               |           |
+------------+----------+--------------+-----------------+-----------+
|            | get      | 无           | {               | 获        |
|            | ChipInfo |              |                 | 取组网设  |
|            |          |              | \"              | 备wifi芯  |
|            |          |              | ahsapd.wifi.csi | 片信息。  |
|            |          |              | \": {           |           |
|            |          |              |                 | 如        |
|            |          |              | \"chip1Type\":  | 果wifi芯  |
|            |          |              | \"\",           | 片为一个  |
|            |          |              |                 | ，则chip  |
|            |          |              | \" chip2Type\": | 1Type对应 |
|            |          |              | \"\",           | 型号，ch  |
|            |          |              |                 | ip2Type、 |
|            |          |              | \" chip3Type\": | chip3Typ  |
|            |          |              | \"\",           | e为缺省。 |
|            |          |              |                 |           |
|            |          |              | }               | 如        |
|            |          |              |                 | 果三频wif |
|            |          |              | }               | i是不同芯 |
|            |          |              |                 | 片，则ch  |
|            |          |              |                 | ip1Type对 |
|            |          |              |                 | 应2.4G，  |
|            |          |              |                 | chip2Type |
|            |          |              |                 | 对        |
|            |          |              |                 | 应5.2G，  |
|            |          |              |                 | chip3Type |
|            |          |              |                 | 对        |
|            |          |              |                 | 于5.8G。  |
+------------+----------+--------------+-----------------+-----------+

组网设备WIFI信道状态信息参数详细定义如表2.4.17 -- 2所示：

表2.4.17 -- 2组网设备WIFI信息状态信息

+-----------+-----------+--------+-----------------------+------------+
| 参数名    | 类型      | 读     | 描述                  | 是否必须   |
|           |           | 写权限 |                       |            |
+===========+===========+========+=======================+============+
| radio     | String    | 读写   | 频段，枚举取值：      | Y          |
|           |           |        |                       |            |
|           |           |        | 2.4G                  |            |
|           |           |        |                       |            |
|           |           |        | 5G                    |            |
|           |           |        |                       |            |
|           |           |        | 5G-2                  |            |
|           |           |        |                       |            |
|           |           |        | 5G+5G-2               |            |
+-----------+-----------+--------+-----------------------+------------+
| csiEnable | Numbe     | 读写   | 该频段                | Y          |
|           | r(uint32) |        | 是否启用CSI采集状态， |            |
|           |           |        |                       |            |
|           |           |        | 1为启用，0为禁用      |            |
+-----------+-----------+--------+-----------------------+------------+
| csiMac    | String    | 读写   | 白名单MAC信息：包含   | Y          |
|           |           |        | 单个MAC地址，格式大写 |            |
+-----------+-----------+--------+-----------------------+------------+
| csiM      | String    | 读写   | 白名单                | Y          |
| acEntries |           |        | MAC信息：包含MAC地址  |            |
|           |           |        | ，格式大写，两组信息  |            |
|           |           |        | 之间以逗号分开，最多  |            |
|           |           |        | 支持32组。示例：0011  |            |
|           |           |        | 11334455,80717A33CCF3 |            |
+-----------+-----------+--------+-----------------------+------------+
| spee      | Number    | 读写   | 移动侦测结            | Y          |
| dInterval |           |        | 果上报时间间隔（秒）  |            |
+-----------+-----------+--------+-----------------------+------------+
| SpeedFlag | Number    | 读写   | 移动侦测结果上报模式  | Y          |
|           |           |        |                       |            |
|           |           |        | 0：到达间隔时         |            |
|           |           |        | 间（speedInterval）即 |            |
|           |           |        | 上报最新一包的数据；  |            |
|           |           |        |                       |            |
|           |           |        | 1：到达               |            |
|           |           |        | 间隔时间（speedInterv |            |
|           |           |        | al）并且触发了活跃/非 |            |
|           |           |        | 活跃切换，才上报数据  |            |
+-----------+-----------+--------+-----------------------+------------+
| ub        | String    | 只读   | ubusVersio            | Y          |
| usVersion |           |        | n对应集成规范版本号； |            |
+-----------+-----------+--------+-----------------------+------------+
| c         | String    | 只读   | csi                   | Y          |
| siVersion |           |        | Version对应驱动版本号 |            |
+-----------+-----------+--------+-----------------------+------------+
| chip1Type | String    | 只读   | Wifi-2.4G             | Y          |
|           |           |        | 芯片型号（厂商缩写+型 |            |
|           |           |        | 号编码），如"MT7915"  |            |
+-----------+-----------+--------+-----------------------+------------+
| Chip2Type | String    | 只读   | Wifi                  | N          |
|           |           |        | -5.2G芯片型号。若集成 |            |
|           |           |        | 为1个wifi芯片，则缺省 |            |
+-----------+-----------+--------+-----------------------+------------+
| Chip3Type | String    | 只读   | Wifi                  | N          |
|           |           |        | -5.8G芯片型号。若集成 |            |
|           |           |        | 为1个wifi芯片，则缺省 |            |
+-----------+-----------+--------+-----------------------+------------+

# csi stream数据传输方式：netlink协议 {#csi-stream数据传输方式netlink协议 .QB标题4}

1、配置和获取配置通过ubus进行通信

2、csi算法运行在用户态，由应用层控制csi
stream的上报间隔，通过netlink方式周期性向内核请求。内核成功收到请求后、响应最新的csi
stream数据。

3、采用user to kernel的netlink通讯方式，driver必须先向内核注册一个struct
genl_family，并且注册一些cmd的处理函数。这些cmd是跟某个family关联起来的。

套接字协议值：NETLINK_GENERIC

family name: "csi_genl"

cmd:

> enum {
>
> CSI_OPS_UNSPEC,
>
> CSI_OPS_REPORT,//上报最新的一包csi数据
>
> CSI_OPS_MAX,
>
> };

用户态请求数据时，cmd
msg:{"radio":"5G",\"reqNum\":4}，5G表示请求5G的csi数据（radio还可以为2.4G），4表示请求4个包数据，1包数据包含一个chain的rssi，snr，i,q等

4、数据格式

预估4个chain，子载波最大个数为256的情况下，总数据长度小于5KB

1）nlattr明细

+------+-----------+----------+---+----------+------------------------+
| a    | attr2     | attr3    | T | type     | 备注                   |
| ttr1 |           |          | a |          |                        |
|      |           |          | g |          |                        |
+======+===========+==========+===+==========+========================+
| da   |           |          | 1 | nest     | 以上所                 |
| taHe |           |          |   |          | 有nlattr包含在此属性中 |
| ader |           |          |   |          |                        |
+------+-----------+----------+---+----------+------------------------+
|      | timeStamp |          | 4 | u64      | csi采集时间戳(ms)      |
+------+-----------+----------+---+----------+------------------------+
|      | raMac     |          | 5 | u8\[6\]  | 接收端mac              |
+------+-----------+----------+---+----------+------------------------+
|      | taMac     |          | 6 | u8\[6\]  | 发送端mac              |
+------+-----------+----------+---+----------+------------------------+
|      | freq      |          | 7 | u8       | 使用的频段             |
|      | uencyBand |          |   |          |                        |
|      |           |          |   |          | 0-2.4G                 |
|      |           |          |   |          |                        |
|      |           |          |   |          | 1-5G                   |
+------+-----------+----------+---+----------+------------------------+
|      | channel   |          | 8 | u8       | 设定带宽，枚举值：     |
|      | Bandwidth |          |   |          |                        |
|      |           |          |   |          | 0: 20M                 |
|      |           |          |   |          |                        |
|      |           |          |   |          | 1: 40M                 |
|      |           |          |   |          |                        |
|      |           |          |   |          | 2: 80M                 |
|      |           |          |   |          |                        |
|      |           |          |   |          | 3: 160M                |
+------+-----------+----------+---+----------+------------------------+
|      | data      |          | 9 | u8       | 实际带宽，枚举值：     |
|      | Bandwidth |          |   |          |                        |
|      |           |          |   |          | 0: 20M                 |
|      |           |          |   |          |                        |
|      |           |          |   |          | 1: 40M                 |
|      |           |          |   |          |                        |
|      |           |          |   |          | 2: 80M                 |
|      |           |          |   |          |                        |
|      |           |          |   |          | 3: 160M                |
+------+-----------+----------+---+----------+------------------------+
|      | pro       |          | 1 | u32      | 协议模式，枚举值：     |
|      | tocolMode |          | 0 |          |                        |
|      |           |          |   |          | 1.CSI_FRAME_TYPE_11A   |
|      |           |          |   |          |                        |
|      |           |          |   |          | 2.CSI_FRAME_TYPE_11B   |
|      |           |          |   |          |                        |
|      |           |          |   |          | 4.CSI_FRAME_TYPE_11G   |
|      |           |          |   |          |                        |
|      |           |          |   |          | 8.CSI_FRAME_TYPE_11N   |
|      |           |          |   |          |                        |
|      |           |          |   |          | 16.CSI_FRAME_TYPE_11AC |
|      |           |          |   |          |                        |
|      |           |          |   |          | 32.CSI_FRAME_TYPE_11AX |
+------+-----------+----------+---+----------+------------------------+
|      | frameType |          | 1 | u8       | 帧类型，同Wi-Fi标准    |
|      |           |          | 1 |          | 协议，有效值6bit，包括 |
|      |           |          |   |          | type和subtype.例：fra  |
|      |           |          |   |          | me_type=0x12(00010010) |
|      |           |          |   |          | 取6bit(010010)type=10, |
|      |           |          |   |          | subtype=0100           |
+------+-----------+----------+---+----------+------------------------+
|      | chainNum  |          | 1 | u8       | Chain个数，            |
|      |           |          | 2 |          | 值为0--4（如2发2收，会 |
|      |           |          |   |          | 产生4个chain的csi）csi |
|      |           |          |   |          | chain(链路)的最大个数  |
+------+-----------+----------+---+----------+------------------------+
|      | p         |          | 1 | u8       | 主通                   |
|      | rimaryCha |          | 3 |          | 道索引，802.11协议标准 |
|      | nnelIndex |          |   |          |                        |
+------+-----------+----------+---+----------+------------------------+
|      | phyerr    |          | 1 | u8       | 物理层上报错误         |
|      |           |          | 4 |          | ID，如成功值为 0       |
+------+-----------+----------+---+----------+------------------------+
|      | rate      |          | 1 | u8       | MCS                    |
|      |           |          | 5 |          | 速率，同Wi-Fi标准协    |
|      |           |          |   |          | 议，填写MCS索引；如果  |
|      |           |          |   |          | 芯片无法获取可以直接填 |
|      |           |          |   |          | 0                      |
+------+-----------+----------+---+----------+------------------------+
|      | extraIn   |          | 1 | u32      | 附加信息，缺省为0      |
|      | formation |          | 6 |          |                        |
+------+-----------+----------+---+----------+------------------------+
|      | channel   |          | 1 | u16      | Radio信道,当前 Wi-Fi   |
|      |           |          | 7 |          | 使用的信道             |
+------+-----------+----------+---+----------+------------------------+
|      | csiLen    |          | 1 | u16      | CSI子载波个数（        |
|      |           |          | 8 |          | 实际采集到的csi子载波  |
|      |           |          |   |          | 个数），不同芯片厂商、 |
|      |           |          |   |          | 不同协议会有不同；为了 |
|      |           |          |   |          | 统一接口，所以需要直接 |
|      |           |          |   |          | 获取csi子载波个数值。  |
+------+-----------+----------+---+----------+------------------------+
|      | packetIdx |          | 1 | u32      | 上报序号（每次上报+1） |
|      |           |          | 9 |          |                        |
+------+-----------+----------+---+----------+------------------------+
|      | ch        |          | 2 | nest     | 与chain相关的属性      |
|      | ainHeader |          |   |          |                        |
|      |           |          |   |          | \                      |
|      |           |          |   |          | [18,25\]包含在此属性中 |
+------+-----------+----------+---+----------+------------------------+
|      |           | chainIdx | 2 | u8       | chanIdx(0,1,2\...)     |
|      |           |          | 0 |          |                        |
+------+-----------+----------+---+----------+------------------------+
|      |           | rssi     | 2 | s8       | 信号强度，单位为 dBm   |
|      |           |          | 1 |          |                        |
+------+-----------+----------+---+----------+------------------------+
|      |           | snr      | 2 | u8       | 信噪比，单位为         |
|      |           |          | 2 |          | dBm，如果              |
|      |           |          |   |          | 芯片无法获取可以直接填 |
|      |           |          |   |          | 0                      |
+------+-----------+----------+---+----------+------------------------+
|      |           | noise    | 2 | u8       | noise floor            |
|      |           |          | 3 |          | 本                     |
|      |           |          |   |          | 底噪声（或称本体噪声、 |
|      |           |          |   |          | 固有噪声），单位dbm。  |
|      |           |          |   |          |                        |
|      |           |          |   |          | 如果                   |
|      |           |          |   |          | 芯片无法获取可以直接填 |
|      |           |          |   |          | 0,长度为1              |
+------+-----------+----------+---+----------+------------------------+
|      |           | agcCode  | 2 | u16      | Agc                    |
|      |           |          | 4 |          | code，                 |
|      |           |          |   |          | 自动增益控制码；根据厂 |
|      |           |          |   |          | 商芯片规格填写，如果芯 |
|      |           |          |   |          | 片无法获取可以直接填0  |
+------+-----------+----------+---+----------+------------------------+
|      |           | p        | 2 | s16      | 频偏，如果             |
|      |           | haseIncr | 5 |          | 芯片无法获取可以直接填 |
|      |           |          |   |          | 0                      |
+------+-----------+----------+---+----------+------------------------+
|      |           | txS      | 2 | u16      | 对端tx stream序号      |
|      |           | treamIdx | 6 |          |                        |
+------+-----------+----------+---+----------+------------------------+
|      |           | rxS      | 2 | u16      | 自身rx stream序号      |
|      |           | treamIdx | 7 |          |                        |
+------+-----------+----------+---+----------+------------------------+
|      |           | csi      | 2 | s16\[    | i0,i1,i2,i3\...        |
|      |           | ComPlexI | 8 | csiLen\] |                        |
+------+-----------+----------+---+----------+------------------------+
|      |           | csi      | 2 | s16\[    | q0,q1,q2,q3\...        |
|      |           | ComPlexQ | 9 | csiLen\] |                        |
+------+-----------+----------+---+----------+------------------------+
| r    |           |          | 3 | NL       | 用户态-\>              |
| epor |           |          |   | A_STRING | 内核态：发送请求json， |
| tMsg |           |          |   |          | 包含radio和请求的包数  |
|      |           |          |   |          |                        |
|      |           |          |   |          | 内核态-\>用            |
|      |           |          |   |          | 户态：返回状态信息或错 |
|      |           |          |   |          | 误信息（一般不会用到） |
+------+-----------+----------+---+----------+------------------------+

2）枚举

[/\*csi cmd\*/]{.mark}

enum {

CSI_OPS_UNSPEC,

CSI_OPS_REPORT,

CSI_OPS_MAX,

};

[/\* netlink attributes \*/]{.mark}

enum CSI_NL_ATTR {

CSI_ATTR_UNSPEC = 0,

CSI_ATTR_DATA_HEADER,

CSI_ATTR_CHAIN_HEADER,

CSI_ATTR_REPORT_MSG,

//

CSI_ATTR_TS, ///\< 时间戳

CSI_ATTR_RA, ///\< 接收端MAC地址

CSI_ATTR_TA, ///\< 发送端MAC地址

CSI_ATTR_FREQUENCY_BAND, ///\< 使用频段

CSI_ATTR_CBW, ///\< channel 设定带宽 0-20M,1-40M,2-80M,3-160M

CSI_ATTR_DBW, ///\< \*实际data带宽,0-20M,1-40M,2-80M,3-160M

CSI_ATTR_PROTO, ///\< protocol_mode

CSI_ATTR_FRAME_TYPE, ///\< 帧类型，同Wi-Fi标准协议，有效值6bit，

CSI_ATTR_CHAIN_NUM, ///\< Chain个数

CSI_ATTR_PRI_CHANNEL_IDX, ///\< 主通道索引

CSI_ATTR_ERR, ///\< 物理层上报的错误ID（如成功，则设置0)

CSI_ATTR_MCS, ///\< mcs速率

CSI_ATTR_EXTRA_INFO, ///\< 附加信息

CSI_ATTR_CHANNEL, ///\< 信道

CSI_ATTR_CSI_LEN, ///\< CSI个数

CSI_ATTR_PACKET_IDX, ///\< 上报序号

CSI_ATTR_CHAIN_IDX, ///\< 上报chain序号

CSI_ATTR_RSSI, ///\< 综合信号强度

CSI_ATTR_SNR, ///\< SNR

CSI_ATTR_NOISE, ///\< 本底噪声

CSI_ATTR_AGC_CDOE, ///\< agc code

CSI_ATTR_PHASE_INCR, ///\< 频偏

CSI_ATTR_TX_IDX, ///\< 对端tx stream序号

CSI_ATTR_RX_IDX, ///\< 自身rx stream序号

CSI_ATTR_CSI_I,

CSI_ATTR_CSI_Q,

\_\_CSI_ATTR_MAX,

};

3）结构体

typedef struct{

> unsigned long long timeStamp;///\< 时间戳
>
> unsigned char raMac\[6\];///\< 接收端MAC地址
>
> unsigned char taMac\[6\];///\< 发送端MAC地址
>
> unsigned char frequencyBand;///\< 使用频段，0-2.4G,1-5G
>
> unsigned char channelBandwidth;///\< 设定带宽,0-20M,1-40M,2-80M,3-160M
>
> unsigned char dataBandwidth;///\< 实际带宽,0-20M,1-40M,2-80M,3-160M
>
> unsigned int protocolMode;///\< protocol_mode
>
> unsigned char frameType;///\< 帧类型，同Wi-Fi标准协议，有效值6bit，
>
> unsigned char chainNum;///\< Chain个数
>
> unsigned char primaryChannelIndex; ///\< 主通道索引
>
> unsigned char phyerr;///\< 物理层上报的错误ID（如成功，则设置0)
>
> unsigned char rate; ///\< mcs速率
>
> unsigned int extraInformation; ///\< 附加信息
>
> unsigned short channel; ///\< 信道
>
> unsigned short csiLen; ///\< CSI个数

// chainHeader

> unsigned int packetIdx; ///\< 上报序号
>
> unsigned char chainIdx;///\< chain序号
>
> char rssi;///\< 综合信号强度
>
> unsigned char snr;///\< SNR
>
> unsigned char noise;///\< 本底噪声
>
> unsigned short agcCode; ///\< agc code
>
> short phaseIncr;///\< 频偏
>
> unsigned short txStreamIdx;///\< 对端tx stream序号
>
> unsigned short rxStreamIdx;///\< 自身rx stream序号
>
> short \*csiComPlexI;///\<
>
> short \*csiComPlexQ;///\<

}csi_item_t;

# csi stream参数定义表 {#csi-stream参数定义表 .QB标题4}

csi开启状态下，采集信息需匹配protocolMode，如不匹配就无csi数据，不进行上报，待有符合条件的数据再上报。

Csi采集开启状态，若无配置frame模式，则不进行上报。

其中参数详细定义如表2.4.17 -- 3所示：

表2.4.17 -- 3组网设备csi stream数据

+-----------+-----------+--------+-----------------------+------------+
| 参数名    | 类型      | 读     | 描述                  | 是否必须   |
|           |           | 写权限 |                       |            |
+===========+===========+========+=======================+============+
| csiStream | Object    | 只读   | CSI 采集数据信息      | Y          |
+-----------+-----------+--------+-----------------------+------------+
| timestamp | Numbe     | 只写   | 时                    | Y          |
|           | r(uint64) |        | 间戳，单位毫秒（ms）  |            |
+-----------+-----------+--------+-----------------------+------------+
| raMac     | Arr       | 只读   | 接收端MAC地址         | Y          |
|           | ay(uint8) |        |                       |            |
+-----------+-----------+--------+-----------------------+------------+
| taMac     | Array     | 只读   | 发送端MAC地址         | Y          |
|           | (uint8)   |        |                       |            |
+-----------+-----------+--------+-----------------------+------------+
| freq      | Numb      | 只读   | 使用的频段，枚举值：  | Y          |
| uencyBand | er(uint8) |        |                       |            |
|           |           |        | 0：error              |            |
|           |           |        |                       |            |
|           |           |        | 1：2.4G               |            |
|           |           |        |                       |            |
|           |           |        | 2：5G                 |            |
+-----------+-----------+--------+-----------------------+------------+
| bandwidth | Numb      | 只读   | 带宽，枚举值：        | Y          |
|           | er(uint8) |        |                       |            |
|           |           |        | 0: 20M                |            |
|           |           |        |                       |            |
|           |           |        | 1: 40M                |            |
|           |           |        |                       |            |
|           |           |        | 2: 80M                |            |
|           |           |        |                       |            |
|           |           |        | 3: 160M               |            |
+-----------+-----------+--------+-----------------------+------------+
| rssi      | Num       | 只读   | 信号强度，单位为dBm   | Y          |
|           | ber(int8) |        |                       |            |
+-----------+-----------+--------+-----------------------+------------+
| snr       | Num       | 只读   | 信噪比，单位为dBm     | Y          |
|           | ber(int8) |        |                       |            |
+-----------+-----------+--------+-----------------------+------------+
| pro       | Numbe     | 只读   | 协议模式，枚举值：    | Y          |
| tocolMode | r(uint32) |        |                       |            |
|           |           |        | 1.CSI_FRAME_TYPE_11A  |            |
|           |           |        |                       |            |
|           |           |        | 2.CSI_FRAME_TYPE_11B  |            |
|           |           |        |                       |            |
|           |           |        | 4.CSI_FRAME_TYPE_11G  |            |
|           |           |        |                       |            |
|           |           |        | 8.CSI_FRAME_TYPE_11N  |            |
|           |           |        |                       |            |
|           |           |        | 1                     |            |
|           |           |        | 6.CSI_FRAME_TYPE_11AC |            |
|           |           |        |                       |            |
|           |           |        | 3                     |            |
|           |           |        | 2.CSI_FRAME_TYPE_11AX |            |
+-----------+-----------+--------+-----------------------+------------+
| frameType | Numb      | 只读   | 帧类型，同Wi          | N          |
|           | er(uint8) |        | -Fi标准协议，有效值6b |            |
|           |           |        | it，包括type和subtype |            |
|           |           |        |                       |            |
|           |           |        | 例：fram              |            |
|           |           |        | e_type=0x12(00010010) |            |
|           |           |        |                       |            |
|           |           |        | 取                    |            |
|           |           |        | 6bit(010010)type=10,  |            |
|           |           |        | subtype=0100          |            |
+-----------+-----------+--------+-----------------------+------------+
| chainNum  | Numb      | 只读   | Chain 个数，值为0 --  | Y          |
|           | er(uint8) |        | 4（如2发2收，会产     |            |
|           |           |        | 生4个chain的csi）csi  |            |
|           |           |        | chain(链路)           |            |
|           |           |        | 的最大个数            |            |
+-----------+-----------+--------+-----------------------+------------+
| csiLen    | Numbe     | 只读   | CSI子载波个数0        | Y          |
|           | r(uint16) |        | -256                  |            |
|           |           |        | ，最大值为256（实际采 |            |
|           |           |        | 集到的csi子载波个数） |            |
|           |           |        | ，不同芯片厂商、不同  |            |
|           |           |        | 协议会有不同；为了统  |            |
|           |           |        | 一接口，所以需要直接  |            |
|           |           |        | 获取csi子载波个数值。 |            |
+-----------+-----------+--------+-----------------------+------------+
| p         | Numbe     | 只读   | 主通道索引，802.11    | Y          |
| rimaryCha | r(uint32) |        | 协议标准              |            |
| nnelIndex |           |        |                       |            |
+-----------+-----------+--------+-----------------------+------------+
| rate      | Numbe     | 只读   | MCS 速率              | N          |
|           | r(uint32) |        |                       |            |
+-----------+-----------+--------+-----------------------+------------+
| agcCode   | Numbe     | 只读   | Agc                   | N          |
|           | r(uint32) |        | c                     |            |
|           |           |        | ode，自动增益控制码。 |            |
|           |           |        |                       |            |
|           |           |        | 如缺省则填0           |            |
+-----------+-----------+--------+-----------------------+------------+
| phaseIncr | Numbe     | 只读   | 频偏。如缺省则填0     | N          |
|           | r(uint32) |        |                       |            |
+-----------+-----------+--------+-----------------------+------------+
| channel   | Numbe     | 只读   | Radio信道,            | Y          |
|           | r(uint16) |        | 当前Wi-Fi使用的信     |            |
|           |           |        | 道,如存在多信道，引用 |            |
|           |           |        | ","逗号分隔，例："136 |            |
|           |           |        | ,149"                 |            |
+-----------+-----------+--------+-----------------------+------------+
| pa        | Numbe     | 只读   | 上                    | Y          |
| cketIndex | r(uint32) |        | 报序号（每次上报+1）  |            |
+-----------+-----------+--------+-----------------------+------------+
| c         | Arr       | 只读   | 每4个字节为一个复合体 | Y          |
| siComplex | ay(int16) |        | （前2个字节为WIFI的i  |            |
|           |           |        | 路（实部），后2个字节 |            |
|           |           |        | 为WIFI的q路（虚部）； |            |
+-----------+-----------+--------+-----------------------+------------+
| nosie     | Num       | 只读   | noise                 | N          |
|           | ber(int8) |        | floor本底             |            |
|           |           |        | 噪声（或称本体噪声、  |            |
|           |           |        | 固有噪声），单位dbm。 |            |
|           |           |        |                       |            |
|           |           |        | [如果芯片无法获取可以 |            |
|           |           |        | 直接填0]{.underline}  |            |
+-----------+-----------+--------+-----------------------+------------+
| phyerr    | Num       | 只读   | 物理层上              | N          |
|           | ber(int8) |        | 报错误ID，如成功值为  |            |
|           |           |        | 0                     |            |
+-----------+-----------+--------+-----------------------+------------+
| exterIn   | Numbe     | 只读   | 附加信息,缺省为0      | N          |
| formation | r(uint32) |        |                       |            |
+-----------+-----------+--------+-----------------------+------------+
| csiRate   | Numbe     | 读写   | csi采样率，           | N          |
|           | r(uint32) |        | 单位毫秒/包（ms/p）， |            |
|           |           |        | 驱动自身采集csi的速率 |            |
+-----------+-----------+--------+-----------------------+------------+
| csiChain  | Numbe     | 只读   | csi只上报哪些chain(以 | N          |
|           | r(uint32) |        | bit位表示，1100则代表 |            |
|           |           |        | 只上报chain2和chain3) |            |
+-----------+-----------+--------+-----------------------+------------+

# 2.4.18 组网设备Qos配置 {#组网设备qos配置 .QB标题3}

插件调用接口：

int ahsapi_set_qos_config(cJSON\* qosConfig) //组网设备Qos配置

int ahsapi_get_qos_config(cJSON\*\* qosConfig) //获取组网设备Qos配置信息

qosConfig 为JSON字符串，包含参数内容如下表

组网设备Qos对象方法定义如表2.4.18 -- 1所示：

> 表2.4.18 -- 1组网设备Qos对象方法

+------------+----------+--------------+-----------------+-----------+
| 对象名     | 方法名   | 参数         | 返回参数        | 描述      |
+============+==========+==============+=================+===========+
| ahsapd.qos | setQ     | {            | 无              | 该配置    |
|            | osConfig |              |                 | 仅需组网  |
|            |          | "q           |                 | 设备工作  |
|            |          | osEnable":1, |                 | 在路由模  |
|            |          |              |                 | 式下支持; |
|            |          | "qosP        |                 |           |
|            |          | arameter":\[ |                 | 通过该    |
|            |          |              |                 | 接口实现  |
|            |          | {            |                 | 路由器qo  |
|            |          |              |                 | S优先级配 |
|            |          | "q           |                 | 置，组网  |
|            |          | osPriority": |                 | 终端应支  |
|            |          | 5,           |                 | 持通过MA  |
|            |          |              |                 | C（包含源 |
|            |          | "ds          |                 | MAC和目的 |
|            |          | cpEnable":1, |                 | MAC）或五 |
|            |          |              |                 | 元组信息  |
|            |          | "dscpP       |                 | （包含源I |
|            |          | riority":45, |                 | P地址、目 |
|            |          |              |                 | 的IP地址  |
|            |          | "            |                 | 、协议号  |
|            |          | sourceMac":" |                 | 、源端口  |
|            |          | ",           |                 | 、目的端  |
|            |          |              |                 | 口）对相  |
|            |          | "desti       |                 | 关业务进  |
|            |          | nationMac":" |                 | 行加速。  |
|            |          | "            |                 |           |
|            |          |              |                 |           |
|            |          | },           |                 |           |
|            |          |              |                 |           |
|            |          | {            |                 |           |
|            |          |              |                 |           |
|            |          | "qos         |                 |           |
|            |          | Priority":6, |                 |           |
|            |          |              |                 |           |
|            |          | "vlan802     |                 |           |
|            |          | 1pEnable":1, |                 |           |
|            |          |              |                 |           |
|            |          | "v           |                 |           |
|            |          | lan8021p":6, |                 |           |
|            |          |              |                 |           |
|            |          | "sourceIp":" |                 |           |
|            |          | ",           |                 |           |
|            |          |              |                 |           |
|            |          | "s           |                 |           |
|            |          | ourcePort":" |                 |           |
|            |          | ",           |                 |           |
|            |          |              |                 |           |
|            |          | "dest        |                 |           |
|            |          | inationIp":" |                 |           |
|            |          | ",           |                 |           |
|            |          |              |                 |           |
|            |          | "destin      |                 |           |
|            |          | ationPort":" |                 |           |
|            |          | ",           |                 |           |
|            |          |              |                 |           |
|            |          | "prot        |                 |           |
|            |          | ocol":"ICMP" |                 |           |
|            |          |              |                 |           |
|            |          | }            |                 |           |
|            |          |              |                 |           |
|            |          | \]           |                 |           |
|            |          |              |                 |           |
|            |          | }            |                 |           |
+------------+----------+--------------+-----------------+-----------+
|            | getQ     | 无           | {               | 获取Qo    |
|            | osConfig |              |                 | s配置信息 |
|            |          |              | "ahsapd.qos":   |           |
|            |          |              |                 |           |
|            |          |              | {               |           |
|            |          |              |                 |           |
|            |          |              | "qosEnable":1,  |           |
|            |          |              |                 |           |
|            |          |              | "q              |           |
|            |          |              | osParameter":\[ |           |
|            |          |              |                 |           |
|            |          |              | {               |           |
|            |          |              |                 |           |
|            |          |              | "               |           |
|            |          |              | qosPriority":5, |           |
|            |          |              |                 |           |
|            |          |              | "dscpEnable":1, |           |
|            |          |              |                 |           |
|            |          |              | "ds             |           |
|            |          |              | cpPriority":45, |           |
|            |          |              |                 |           |
|            |          |              | "sourceMac":"   |           |
|            |          |              | ",              |           |
|            |          |              |                 |           |
|            |          |              | "de             |           |
|            |          |              | stinationMac":" |           |
|            |          |              | "               |           |
|            |          |              |                 |           |
|            |          |              | },              |           |
|            |          |              |                 |           |
|            |          |              | {               |           |
|            |          |              |                 |           |
|            |          |              | "               |           |
|            |          |              | qosPriority":6, |           |
|            |          |              |                 |           |
|            |          |              | "vlan           |           |
|            |          |              | 8021pEnable":1, |           |
|            |          |              |                 |           |
|            |          |              | "vlan8021p":6,  |           |
|            |          |              |                 |           |
|            |          |              | "sourceIp":" ", |           |
|            |          |              |                 |           |
|            |          |              | "sourcePort":"  |           |
|            |          |              | ",              |           |
|            |          |              |                 |           |
|            |          |              | "d              |           |
|            |          |              | estinationIp":" |           |
|            |          |              | ",              |           |
|            |          |              |                 |           |
|            |          |              | "des            |           |
|            |          |              | tinationPort":" |           |
|            |          |              | ",              |           |
|            |          |              |                 |           |
|            |          |              | "p              |           |
|            |          |              | rotocol":"ICMP" |           |
|            |          |              |                 |           |
|            |          |              | }               |           |
|            |          |              |                 |           |
|            |          |              | \]              |           |
|            |          |              |                 |           |
|            |          |              | }               |           |
|            |          |              |                 |           |
|            |          |              | }               |           |
+------------+----------+--------------+-----------------+-----------+

组网设备Qos配置参数定义如表2.4.18 -- 2所示：

> 表2.4.18 -- 2 设备Qos配置参数

  ------------------------------------------------------------------------------------------------------------------------------
  参数名            类型             读写权限   描述                                                               是否必须
  ----------------- ---------------- ---------- ------------------------------------------------------------------ -------------
  qosEnable         Number(uint32)   读写       QoS开关；1：打开，0：关闭                                          Y

  qosParameter      Array of Object  读写       QoS分类规则，最大下发8个实例；                                     N

  qosPriority       Number(uint32)   读写       QoS优先级；取值范围：0-7；数值越大优先级越高;                      N

  dscpEnable        Number(uint32)   读写       DSCP优先级开关；1：打开，0：关闭                                   N

  dscpPriority      Number(uint32)   读写       DSCP优先级；取值范围：0-63；数值越大优先级越高；                   N

  vlan8021pEnable   Number(uint32)   读写       802.1P启用开关；1：打开，0关闭                                     N

  vlan8021p         Number(uint32)   读写       VLAN优先级；取值范围：0-7；数值越大优先级越高；                    N

  sourceIp          String           读写       源IP                                                               N

  sourcePort        String           读写       源端口                                                             N

  destinationIp     String           读写       目的地IP                                                           N

  destinationPort   String           读写       目的地端口                                                         N

  protocol          String           读写       传输协议列表，多种协议时使用逗号进行分割；取值为：TCP，UDP，ICMP   N

  sourceMac         String           读写       源MAC（一般指路由器铭牌MAC）                                       N

  destinationMac    String           读写       目的MAC（一般指下挂设备MAC）                                       N
  ------------------------------------------------------------------------------------------------------------------------------

# 2.4.19 组网设备 VLAN配置 {#组网设备-vlan配置 .QB标题3}

插件调用接口：

int ahsapi_set_iptv_vlan_config(cJSON\* iptvVlan) //组网设IPTV VLAN配置

int ahsapi_get_iptv_vlan_config(cJSON\*\* iptvVlan ) //获取组网设备IPTV
VLAN配置信息

iptvVlan 为JSON字符串，包含参数内容如下表

组网设备对象方法定义如表2.4.18 -- 1所示：

> 表2.4.18 -- 1组网设备VLAN配置对象方法

+------------+----------+--------------+-----------------+-----------+
| 对象名     | 方法名   | 参数         | 返回参数        | 描述      |
+============+==========+==============+=================+===========+
| a          | set      | {            | 无              | IPTV      |
| hsapd.vlan | IptvVlan |              |                 | VLAN配置  |
|            |          | "vl          |                 |           |
|            |          | anEnable":1, |                 | 1、绑定了 |
|            |          |              |                 | IPTV业务  |
|            |          | "vlanId":46, |                 | 的有线端  |
|            |          |              |                 | 口和无线S |
|            |          | "ip          |                 | SID，其下 |
|            |          | tvStatus":\[ |                 | 行报文需  |
|            |          |              |                 | 删除VLAN  |
|            |          | {            |                 | tag，其上 |
|            |          |              |                 | 行报文需  |
|            |          | "            |                 | 添加VLAN  |
|            |          | radio":"5G", |                 | tag后再进 |
|            |          |              |                 | 行转发。  |
|            |          | "index":5    |                 |           |
|            |          |              |                 | 2、无论路 |
|            |          | },           |                 | 由器自身  |
|            |          |              |                 | 工作模式  |
|            |          | {            |                 | （路由/桥 |
|            |          |              |                 | 接/中继） |
|            |          | "ra          |                 | ，IPTV业  |
|            |          | dio":"2.4G", |                 | 务都应工  |
|            |          |              |                 | 作在桥接  |
|            |          | "index":1    |                 | （或中继  |
|            |          |              |                 | ）模式；  |
|            |          | },           |                 |           |
|            |          |              |                 |           |
|            |          | {            |                 |           |
|            |          |              |                 |           |
|            |          | "r           |                 |           |
|            |          | adio":"LAN", |                 |           |
|            |          |              |                 |           |
|            |          | "index":1    |                 |           |
|            |          |              |                 |           |
|            |          | }            |                 |           |
|            |          |              |                 |           |
|            |          | \]           |                 |           |
|            |          |              |                 |           |
|            |          | }            |                 |           |
+------------+----------+--------------+-----------------+-----------+
|            | get      | 无           | {               | 获取IPTV  |
|            | IptvVlan |              |                 | VLA       |
|            |          |              | "ahsapd.vlan":  | N配置信息 |
|            |          |              |                 |           |
|            |          |              | {               |           |
|            |          |              |                 |           |
|            |          |              | "vlanEnable":1, |           |
|            |          |              |                 |           |
|            |          |              | "vlanId":46,    |           |
|            |          |              |                 |           |
|            |          |              | "iptvStatus":\[ |           |
|            |          |              |                 |           |
|            |          |              | {               |           |
|            |          |              |                 |           |
|            |          |              | "radio":"5G",   |           |
|            |          |              |                 |           |
|            |          |              | "index":5       |           |
|            |          |              |                 |           |
|            |          |              | },              |           |
|            |          |              |                 |           |
|            |          |              | {               |           |
|            |          |              |                 |           |
|            |          |              | "radio":"2.4G", |           |
|            |          |              |                 |           |
|            |          |              | "index":1       |           |
|            |          |              |                 |           |
|            |          |              | },              |           |
|            |          |              |                 |           |
|            |          |              | {               |           |
|            |          |              |                 |           |
|            |          |              | "radio":"LAN",  |           |
|            |          |              |                 |           |
|            |          |              | "index":1       |           |
|            |          |              |                 |           |
|            |          |              | }               |           |
|            |          |              |                 |           |
|            |          |              | \]              |           |
|            |          |              |                 |           |
|            |          |              | }               |           |
|            |          |              |                 |           |
|            |          |              | }               |           |
+------------+----------+--------------+-----------------+-----------+

组网设备VLAN配置参数定义如表2.4.19 -- 2所示：

> 表2.4.19 -- 2 设备VLAN配置参数

+-----------+-----------+--------+-----------------------+------------+
| 参数名    | 类型      | 读     | 描述                  | 是否必须   |
|           |           | 写权限 |                       |            |
+===========+===========+========+=======================+============+
| v         | Numbe     | 读写   | IPTV业务开            | Y          |
| lanEnable | r(uint32) |        | 关；1：打开，0：关闭; |            |
+-----------+-----------+--------+-----------------------+------------+
| vlanId    | Numbe     | 读写   | IPTV业务的VLAN        | Y          |
|           | r(uint32) |        | ID；IP                |            |
|           |           |        | TV业务开关打开时必选; |            |
+-----------+-----------+--------+-----------------------+------------+
| i         | Array of  | 读写   | 用户                  | N          |
| ptvStatus | Object    |        | 侧接口的VLAN参数；IPT |            |
|           |           |        | V业务开关打开时必选； |            |
+-----------+-----------+--------+-----------------------+------------+
| radio     | String    | 读写   | 端口信息，对于        | N          |
|           |           |        | WLAN接口的频段，取值  |            |
|           |           |        | 2.4G或5G或5G-2，对于  |            |
|           |           |        | 以太接口，取值为LAN； |            |
|           |           |        |                       |            |
|           |           |        | 注：对于三频设        |            |
|           |           |        | 备，5G表示5.8G频段，  |            |
|           |           |        | 5G-2表示5.2G频段；双  |            |
|           |           |        | 频设备忽略5G-2频段；  |            |
+-----------+-----------+--------+-----------------------+------------+
| index     | Numbe     | 读写   | WLA                   | N          |
|           | r(uint32) |        | N接口的SSID索引或以太 |            |
|           |           |        | 接口序号；2.4G索引1-4 |            |
|           |           |        | ;                     |            |
|           |           |        | 5G                    |            |
|           |           |        | 索引：5-8；5G-2索引:9 |            |
|           |           |        | -12；LAN索引从1开始； |            |
+-----------+-----------+--------+-----------------------+------------+

# 附录A 响应码 {#附录a-响应码 .QB标题1}

表 A -- 1 ubus响应码

  -----------------------------------------------------------------------
  **返回值**         **描述**             **描述**
  ------------------ -------------------- -------------------------------
  0                  Success              成功

  1                  Invalid command      无效的指令

  2                  Invalid argument     存在不支持的输入参数

  3                  Method not found     方法不存在

  4                  Not found            不存在

  5                  No data              无数据响应

  6                  Permission denied    无权限

  7                  Request timed out    请求超时

  8                  Operation not        操作不支持
                     supported            

  9                  Unknow error         未知错误

  10                 Connection failed    连接失败
  -----------------------------------------------------------------------

# 附录B 修订记录 {#附录b-修订记录 .QB标题1}

表 B-1 修订记录

+-----------+-----------+--------------------------------+-------------+
| 版本号    | 更新时间  | 主要内容或重大修改             | 编制人      |
+===========+===========+================================+=============+
| V0.1      | 2         | 初稿                           | 魏飞        |
|           | 020-10-30 |                                |             |
+-----------+-----------+--------------------------------+-------------+
| V0.2      | 2         | 1、修改ubus connect            | 魏飞        |
|           | 020-11-06 | 路径默认使用null               |             |
|           |           |                                |             |
|           |           | 2、修                          |             |
|           |           | 订插件调用接口函数以ahspai开始 |             |
+-----------+-----------+--------------------------------+-------------+
| V0.3      | 2         | 1、basic中meshRo               | 魏飞        |
|           | 020-11-12 | le修改为meshType同时修改值内容 |             |
|           |           |                                |             |
|           |           | 2、2.4.5下挂设备增加           |             |
|           |           | 收发字节和包数（增加4个字段）  |             |
|           |           |                                |             |
|           |           | 3、2.4.4 增加uplinkMac字段     |             |
|           |           |                                |             |
|           |           | 4、完善2.4.14 mesh章节内容     |             |
+-----------+-----------+--------------------------------+-------------+
| V0.4      | 2         | 1、删                          | 魏飞        |
|           | 020-11-18 | 除返回参数中respCode和respCont |             |
|           |           |                                |             |
|           |           | 2、更新附录A 响应码            |             |
|           |           |                                |             |
|           |           | 3、更新图2.1和图2.2            |             |
+-----------+-----------+--------------------------------+-------------+
| V0.5      |           | 1、插件调用接口                | 魏飞        |
|           |           | get类型函数入参cJSON相关修改为 |             |
|           |           | \*\*                           |             |
+-----------+-----------+--------------------------------+-------------+
| V0.6      |           | 1、2.4.14 章增加配置获取接口   | 魏飞        |
+-----------+-----------+--------------------------------+-------------+
| V0.7      | 2         | 1、2.4.7 中wifi统计中radio     | 魏飞        |
|           | 020-12-22 | 有线定义为WAN 和LAN            |             |
|           |           |                                |             |
|           |           | 2、规范中uint8/int8            |             |
|           |           | 全部修改为uint32/int32         |             |
|           |           |                                |             |
|           |           | 3、2.4.9                       |             |
|           |           | 漫游配置中s                    |             |
|           |           | taDisconnet修改为staDisconnect |             |
+-----------+-----------+--------------------------------+-------------+
| V0.8      | 2         | 1、2.4.4章增加                 | 魏飞        |
|           | 021-01-05 | 配置各种模式方法setWorkingMode |             |
|           |           |                                |             |
|           |           | 2、                            |             |
|           |           | 2.4.5下挂设备信息增加staVendor |             |
|           |           | 字段                           |             |
|           |           |                                |             |
|           |           | 3、2.4.12                      |             |
|           |           | 增加硬件加速方法hwnatClean     |             |
|           |           | 和kernelCount,                 |             |
|           |           | 并增加count字段内容            |             |
+-----------+-----------+--------------------------------+-------------+
| V0.9      | 2         | 1、                            | 魏飞        |
|           | 021-01-07 | 2.4.5章增加accessTime字段定义  |             |
|           |           |                                |             |
|           |           | 2、2.4.4章增加index字段定义    |             |
+-----------+-----------+--------------------------------+-------------+
| V1.0      | 2         | 1、增加2.4.15                  | 魏飞        |
|           | 021-03-01 | 章节组网设备网络限速           |             |
+-----------+-----------+--------------------------------+-------------+
| V1.1      | 2         | 1、2.4.1                       | 魏飞        |
|           | 021-03-23 | 基本信息增加ahsapdVersion      |             |
|           |           | 版本、radio5、pppoeUser        |             |
|           |           | 3个字段                        |             |
|           |           |                                |             |
|           |           | 2、2.4.4                       |             |
|           |           | upli                           |             |
|           |           | nkMac字段重新定义为设备上行mac |             |
|           |           | , 新增gatewayMac字段           |             |
|           |           | 为设备上行网元mac              |             |
|           |           |                                |             |
|           |           | 3、2.4.5                       |             |
|           |           | 合并下挂设备上下线（staUp      |             |
|           |           | Down）内容,删除newSta信息event |             |
|           |           |                                |             |
|           |           | 4、2.4.12 中增加reboot         |             |
|           |           | 组网设备升级后是否立刻重启字段 |             |
+-----------+-----------+--------------------------------+-------------+
| V1.2      | 2         | 1、2.4.4                       | 魏飞        |
|           | 021-03-30 | 上行状态增加setUplinkR         |             |
|           |           | ateConfig和getUplinkRateConfig |             |
|           |           | 2个method，增加 averRxRate 、  |             |
|           |           | averTxRate 、 maxRxRate        |             |
|           |           | 、maxTxRate                    |             |
|           |           | 、                             |             |
|           |           | reportInterval、sampleInterval |             |
|           |           | 6个字段内容                    |             |
|           |           |                                |             |
|           |           | 2、2.4.5                       |             |
|           |           | 下挂设                         |             |
|           |           | 备信息增加channel、averRxRate  |             |
|           |           | 、 averTxRate 、 maxRxRate     |             |
|           |           | 、maxTxRate                    |             |
|           |           | 、                             |             |
|           |           | reportInterval、sampleInterval |             |
|           |           | 7个字段内容，增加setS          |             |
|           |           | taRateConfig和getStaRateConfig |             |
|           |           | 2个method                      |             |
|           |           |                                |             |
|           |           | 3、2.4.6                       |             |
|           |           | wifi                           |             |
|           |           | 配置setWifiSwitch增加index字段 |             |
|           |           |                                |             |
|           |           | 4、2.4.9章节增加配置和获取     |             |
|           |           | 双频合一配置的方setBandSteerin |             |
|           |           | gConfig和getBandSteeringConfig |             |
|           |           |                                |             |
|           |           | 5、2.4.12 章节增加日志采集     |             |
|           |           | 方法devLogSta                  |             |
|           |           | ts，增加日志相关字段logSwitch, |             |
|           |           | logLevel，logTime, logPath     |             |
|           |           |                                |             |
|           |           | 6、增加2.4.16 章节组网设备探测 |             |
|           |           |                                |             |
|           |           | 7、更新2.4章节中表格的标识为   |             |
|           |           | 2.4.X-X 样式                   |             |
+-----------+-----------+--------------------------------+-------------+
| 1.3.0     | 2         | 1、 2.4.1 组网设备基本信息     | 魏飞        |
|           | 021-04-26 | 中Ip                           |             |
|           |           | v6IPAddress修改为ipv6IPAddress |             |
|           |           |                                |             |
|           |           | 2、2.4.6 章节wifi              |             |
|           |           | 信息中增加ssidStandard,        |             |
|           |           | nosieLevel,                    |             |
|           |           | interferencePercent 3个字段    |             |
|           |           |                                |             |
|           |           | 3、2.4.4章                     |             |
|           |           | 节ipv6PrefixDelegationEnabled  |             |
|           |           | 字段修改为Number类型           |             |
|           |           |                                |             |
|           |           | 4、2.4.6                       |             |
|           |           | 章节增加wifi信息               |             |
|           |           | 变化主动上报event，详见2.4.6-2 |             |
|           |           | wifi信息变化主动上报消息定义   |             |
|           |           |                                |             |
|           |           | 5、表2.4.16-2 中               |             |
|           |           | 例                             |             |
|           |           | 子中事件上报中的字段HttpSpeedT |             |
|           |           | estRate修复为httpSpeedTestRate |             |
|           |           |                                |             |
|           |           | 6、2.4.12章节                  |             |
|           |           | 增加操控指令e                  |             |
|           |           | vent响应上报（详细见表2.4.12-2 |             |
|           |           | ），同时增加respType、respCode |             |
|           |           | 2个event上报所需字段内容       |             |
|           |           |                                |             |
|           |           | 7、2.4.14                      |             |
|           |           | 章节setMeshRoamingEnabl        |             |
|           |           | e的参数修改为meshRoamingEnable |             |
|           |           |                                |             |
|           |           | 8、更新插件调用接口            |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.0    | 2         | 1、2.4.4 增加interface         | 魏飞        |
|           | 021-05-25 | 和rateArray字段，interface     |             |
|           |           | ,averRxRate,                   |             |
|           |           | averTxRate,maxRxRate ，        |             |
|           |           |                                |             |
|           |           | maxTxRate 5个为rateArray       |             |
|           |           | 包含内容，可通过getUplinkInfo  |             |
|           |           | 返                             |             |
|           |           | 回值获取，setUplinkRateConfig  |             |
|           |           | 方法配置增加interface字        |             |
|           |           | 段,删除getUplinkRateConfig方法 |             |
|           |           |                                |             |
|           |           | 2、2.4.5增加interface          |             |
|           |           | 和rateArray字段，interface     |             |
|           |           | ,averRxRate,                   |             |
|           |           | averTxRate,maxRxRate ，        |             |
|           |           |                                |             |
|           |           | maxTxRate 5个为rateArray       |             |
|           |           | 包含内容，可通过getStaInfo     |             |
|           |           | 返回值获取，setSta             |             |
|           |           | RateConfig方法配置增加interfac |             |
|           |           | e字段,删除getStaRateConfig方法 |             |
|           |           |                                |             |
|           |           | 3、2.4.4                       |             |
|           |           | w                              |             |
|           |           | lanConnect增加securityMode字段 |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.1    | 2         | 1、2.4.1                       |             |
|           | 021-06-08 | 组网设                         |             |
|           |           | 备基本信息增加powerSupplyMode  |             |
|           |           | 供电方式字段，dmKey 修改为必须 |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.2    | 2         | 1、增加2.4.                    |             |
|           | 021-07-30 | 17章节组网设备WIFI信道状态信息 |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.3    | 2         | 1、2.4.1                       |             |
|           | 022-01-12 | 组网设备基本信息增加meshRole   |             |
|           |           | 字段；                         |             |
|           |           |                                |             |
|           |           | 2、2.4.4 组网设备上行信息      |             |
|           |           | 字段uplinkType值增加Optical    |             |
|           |           | Etbernet 、GPON、XGPON、PoE；  |             |
|           |           | 增加字段fiberTXPower ,         |             |
|           |           | fiberRXPower , totalBytesSent  |             |
|           |           | ,totalBytesReceived ,          |             |
|           |           | totalPacketsSent ,             |             |
|           |           | totalPacketsReceived ,         |             |
|           |           | errorsSent , errorsReceived ,  |             |
|           |           | discardPacketsSent ,           |             |
|           |           |                                |             |
|           |           | discardPacketsReceived ;       |             |
|           |           |                                |             |
|           |           | 3、2.4.5 组网设备下挂设备信息  |             |
|           |           | 字段staType修订值范围为：      |             |
|           |           | PC、Phone、TV、Router、IoT、IP |             |
|           |           | C、others，字段radio值增加5G-2 |             |
|           |           | ;                              |             |
|           |           |                                |             |
|           |           | 4、2.4.6 组网设备WIFI配置      |             |
|           |           | 增加字段bandwidth，encrypt     |             |
|           |           | ；字段radio值增加5G-2 ;        |             |
|           |           |                                |             |
|           |           | 5、2.4.8                       |             |
|           |           | 组网设备                       |             |
|           |           | 周边WIFI信息增加字段bandwidth  |             |
|           |           | , wifiStandard；               |             |
|           |           |                                |             |
|           |           | 2.4.9 组网设备漫游配置         |             |
|           |           | 增加staStopDetect              |             |
|           |           | 方法和接口函数，增加rxRate，tx |             |
|           |           | Rate字段；字段radio值增加5G-2; |             |
|           |           |                                |             |
|           |           | 2.4.12 组网设备操控指令        |             |
|           |           | 增加ipv6Control                |             |
|           |           | 方                             |             |
|           |           | 式和函数；增加ipv6Enable字段； |             |
|           |           |                                |             |
|           |           | 8、修                          |             |
|           |           | 订2.4.14组网设备Mesh章节内容； |             |
|           |           |                                |             |
|           |           | 9、2.4.15                      |             |
|           |           | 组网设备网络限速增加limitedUrl |             |
|           |           | ,                              |             |
|           |           |                                |             |
|           |           | deviceLimitMode ,              |             |
|           |           | deviceLimitWeek                |             |
|           |           | ,deviceLimitTimeOffset1 ,      |             |
|           |           |                                |             |
|           |           | deviceLimitTimeOffset2 字段；  |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.4    | 2         | 1、2.4.6                       |             |
|           | 022-02-18 | 章节增加5G优先配置方法         |             |
|           |           |                                |             |
|           |           | 2.                             |             |
|           |           |  增加2.4.18组网设备Qos配置章节 |             |
|           |           |                                |             |
|           |           | 3.  增加2.4.19组网VLAN配置章节 |             |
|           |           |                                |             |
|           |           | 4.  2.4.9章节，                |             |
|           |           | 2.4.12章节，2.4.16章节增加seqI |             |
|           |           | d字段，如配置携带seqId下发，对 |             |
|           |           | 应的event上报携带下发的seqId； |             |
|           |           |                                |             |
|           |           | 5.  2.4.6组网设备WIFI配置      |             |
|           |           |     5G优先增加limitTime字段；  |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.5    | 2         | 1.  2.4.5                      |             |
|           | 022-03-08 |     下挂设备主动上             |             |
|           |           | 报消息增加rssi，rxRate和txRate |             |
|           |           |     字段；                     |             |
|           |           |                                |             |
|           |           | 2.  修订2.4.9章节漫游          |             |
|           |           | 配置xindex修改为LowRSSIPeriod  |             |
|           |           | 字段，删除rssiCheckDelta字段； |             |
|           |           |                                |             |
|           |           | 3.  2.4.9章节dismissTime有漫   |             |
|           |           | 游配置修订为通过断开连接下发； |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.6    | 2         | 4.  修改workingMode 错误值；   |             |
|           | 022-05-20 |                                |             |
|           |           | 5.  2.4.14                     |             |
|           |           |     简化                       |             |
|           |           | mesh拓扑结构，去掉下挂设备信息 |             |
|           |           |                                |             |
|           |           | 6.  修订2.4.12日志上报         |             |
|           |           |     logTime的定义              |             |
|           |           |                                |             |
|           |           | 7.  2.4.7 radio 补充5G-2 频段  |             |
|           |           |                                |             |
|           |           | 8.  2.4.3                      |             |
|           |           |     返回参数缺少portInfoList   |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.7    | 2022-6-15 | 修                             | 张依东      |
|           |           | 订2.4.17章节（wifi感知功能）： |             |
|           |           |                                |             |
|           |           | 更新csi管控接口                |             |
|           |           |                                |             |
|           |           | 更新csi类名                    |             |
|           |           |                                |             |
|           |           | 补充csi数据结构说明            |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.7    | 2022-6-28 | 修                             | 张依东      |
|           |           | 订2.4.17章节（wifi感知功能）： |             |
|           |           |                                |             |
|           |           | 函数入参变量名未定义           |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.7    | 2022-7-13 | 修                             | 张依东      |
|           |           | 订2.4.17章节（wifi感知功能）： |             |
|           |           |                                |             |
|           |           | 函数新增字段radio，区分频段    |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.7    | 2022-8-8  | 修                             | 张依东      |
|           |           | 订2.4.17章节（wifi感知功能）： |             |
|           |           |                                |             |
|           |           | 新增csi数据传输方式：共享内存  |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.8    | 2022-8-15 | 1、2.4.1 radio5 字段增加3      | 魏飞        |
|           |           | 无WI-FI终端枚                  |             |
|           |           | 举值；uplinkType字段增加值类型 |             |
|           |           |                                |             |
|           |           | 2、2.4.4                       |             |
|           |           | 章节增加fiberStatus、tran      |             |
|           |           | sceiverTemperature、supplyVott |             |
|           |           | age、biasCurrent、distance字段 |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.8    | 2022-8-22 | 修                             | 张依东      |
|           |           | 订2.4.17章节（wifi感知功能）： |             |
|           |           |                                |             |
|           |           | 新增csi数据传输方式：netlink   |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.8    | 2022-8-30 | 1、2.4.1 章节增加ponSn字段     | 魏飞        |
|           |           |                                |             |
|           |           | 2、2.4.6章节获                 |             |
|           |           | 取wifi配置信息增加wlanMac字段  |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.8    | 2022-9-22 | 修                             | 张依东      |
|           |           | 订2.4.17章节（wifi感知功能）： |             |
|           |           |                                |             |
|           |           | 更新csi stream数据结构；       |             |
|           |           |                                |             |
|           |           | 删除                           |             |
|           |           | 对象setCSIConfig、getCSIConfig |             |
|           |           | 、setCSIDriver、getCSIDriver； |             |
|           |           |                                |             |
|           |           | 新增对象getChipInfo            |             |
+-----------+-----------+--------------------------------+-------------+
| V1.4.9    | 2         | 1.  修订2.4.                   | 魏飞        |
|           | 022-11-23 | 1章节增加wifiSpecification字段 |             |
|           |           |                                |             |
|           |           | 2.  修订                       |             |
|           |           | 2.4.3章节，增加LAN侧地址配置， |             |
|           |           | LAN侧绑定下挂设备，获取LAN信息 |             |
|           |           |                                |             |
|           |           | 3.  修                         |             |
|           |           | 订2.4.12增加组播转单播管控开关 |             |
|           |           |                                |             |
|           |           | 4.  2.4.6                      |             |
|           |           |     WIFI配置修改ssidStandard   |             |
|           |           |     值定义，2.4.8              |             |
|           |           | 周边WIFI修改wifiStandard值定义 |             |
+-----------+-----------+--------------------------------+-------------+
|           | 2         | 1、修订2.4.6                   | 张依东      |
|           | 022-11-24 | 章节，字段ssidStandard为非必填 |             |
+-----------+-----------+--------------------------------+-------------+
|           | 2         | 1、修订2.4                     | 张依东      |
|           | 022-11-25 | .14章节，示例中字段bhAlmac笔误 |             |
+-----------+-----------+--------------------------------+-------------+
