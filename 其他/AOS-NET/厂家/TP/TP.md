### TP
当前几个问题

1. Download=/tmp/aosnet    该文件未默认创建
2. wifi设置成功或者失败  未有任何响应    异步通知也无响应
3. 恢复出厂后/bin/tmpapp文件夹下内容丢失



```
4xnc3u7u
```


登陆页面 [路由器lan ip]:8080，（密码见标贴），按F12进入控制台，输入以下

```
$.modify({"custom_system":{"telnet":{"enable":"1"}}}, res=>{console.log(res)})
```
wifi6
然后就可以telnet了，进去后用户名是root，密码是  t
```
tdmpwma302v4
```

WIFI7



打开telnet
账号
    然后 把 路径后面的 替换为 /api/misystem/set_telnet?enable=1
root  
telnet是root ，管理密码


```
goash
PfujOXhSJgs22rbc4kHkQgo2X3PKJ8pNqbSSJjELi/oEtyGEsATsbrYRJEbFKACX7SJollBoVAJ9KvqlDzweQspQnKXEfa5a+4u0Rs98YtRfrsJRx3t0eWJWIGsDcRP7NqATGC6BtUjTpFQNY3FFFljSGA8FkO2OxnXec5D4OYM=
```
