针对华为FTTR设备在四川落地测试发现的问题，现在杭研定位反馈如下：

### 问题现象：
**长时间挂侧AOS-NET相关插件线程锁死**


### 问题分析过程：
1. 通过telnet远程登陆设备后台，采用命令方式拉起AOS-NET相关插件，经过一段时间的运行，发现设备的telnet自动断开，重新telnet登陆后，通过ps命令查看插件进程，发现进程正常运行，后台打印异常，AOS-NET相关插件全部进入死锁状态(一小时以上未有任何日志文件输出);
2. 通过telnet远程登陆设备后台，采用命令方式拉起AOS-NET相关插件，经过一段时间的运行，手动强制关闭ssh后，重新telnet登陆后，通过ps命令查看插件进程，发现进程正常运行，AOS-NET相关插件运行正常(周期上报相关日志正常输出);
3. 通过telnet远程登陆设备后台，采用命令方式拉起AOS-NET相关插件，保持telnet长时间连接，从从下午5点到第二天上午9点，AOS-NET相关插件运行正常;
4. 采用测试代码，printf打印和sprintf文件写打印，见附件，验证结果一致，telnet超时退出，进程锁死（长时间无文件打印信息);
	

### 问题结果：
**相关问题跟AOS-NET插件无关**。为华为设备telnet超时退出后这个终端下运行的进程都会出现异常（具体问题待华为定位);
	
### 结论：
**不影响现网设备，AOS-NET插件为设备启动后自动拉起，不存在任何问题;**
在测试过程中需要验证功能，希望通过telnet方式加"-D"运行，建议如下：
		1）确保telnet一直保活，进程在前台正常运行；
		2）华为需要优化当前超时退出telnet的内核处理机制，可以参考通过手动方式断开telnet，保证退出telnet后相关进程后台还能正常运行。

	
	
rrrr
