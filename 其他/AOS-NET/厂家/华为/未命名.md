getUplinklnfo不仅返回“ip、netmask、gateway还需返回“dnsSetEnable、preDnsServer、altDnsServer”，不管这个wan是静态wan还是pppoe wan或者dhcp wan。理解是不是对的?

答复：当前只对静态IP的设置增加了dnsSetEnable、preDnsServer、altDnsServer   获取还是通过getLanDns接口获取的

setStaticlp设置“ip、netmask、gatewaydnsSetEnable、preDnsServer、altDnsServer”这6个属性，此方法只针wan是static wan时才有意义，如果wan口pppoe wan或者dhcp wan时，失败返回错误。理解是不是对的?
答复：除了桥接模式无法手动设置dns外，其他模式应该都可以手动设置dns，这几个字段其他模式都是通用的


基于第2点，如果当前wan是pppoe wan，需要先3.调用setWorkingMode方法，将internetType属性配置成2(static)，然后再调用setStaticlp方法配置静态ip那6个属性才有意义。理解是不是对的?
答复：pppoe模式如果想设置静态IP可以调用setLanDns接口


4、setLanDns方法中新增的defaultStatus是一个可写的属性，如果设置defaultStatus为0表示什么含义?是
答复：不会存在下发0的情况
