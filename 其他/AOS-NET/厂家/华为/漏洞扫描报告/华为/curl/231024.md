| CVE            | 问题描述                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | 备注                  |
| -------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------- |
| CVE-2023-27533 | A vulnerability in input validation exists in curl <8.0 during communication using the TELNET protocol may allow an attacker to pass on maliciously<br />crafted user name and "telnet options" during server negotiation. <br />The lack of proper input scrubbing allows an attacker to send content or perform option negotiation <br />without the application's intent. This vulnerability could be exploited if an application allows user input, thereby enabling attackers to execute arbitrary code on the system.                                                                                                                                                                             | 未涉及telnet,无需关注 |
| CVE-2023-27534 | An authentication bypass vulnerability exists in libcurl <8.0.0 in the FTP connection reuse feature that can result in wrong credentials being used during<br />subsequent transfers. Previously created connections are kept in a connection pool for reuse if they match the current setup. However, certain FTP settings<br /> such as CURLOPT_FTP_ACCOUNT, CURLOPT_FTP_ALTERNATIVE_TO_USER, CURLOPT_FTP_SSL_CCC, and CURLOPT_USE_SSL were not included in the<br /> configuration match checks, causing them to match too easily. This could lead to libcurl using the wrong credentials when performing a transfer, <br />potentially allowing unauthorized access to sensitive information.       | 未涉及tfp,无需关注    |
| CVE-2023-27535 | An authentication bypass vulnerability exists in libcurl <8.0.0 in the FTP connection reuse feature that can result in wrong<br />credentials being used during subsequent transfers. Previously created connections are kept in a connection pool for reuse<br /> if they match the current setup. However, certain FTP settings such as CURLOPT_FTP_ACCOUNT, <br />CURLOPT_FTP_ALTERNATIVE_TO_USER, CURLOPT_FTP_SSL_CCC, and CURLOPT_USE_SSL were not <br />included in the configuration match checks, causing them to match too easily. This could lead to libcurl using the<br /> wrong credentials when performing a transfer, potentially allowing unauthorized access to sensitive information. | 未涉及tfp,无需关注    |
