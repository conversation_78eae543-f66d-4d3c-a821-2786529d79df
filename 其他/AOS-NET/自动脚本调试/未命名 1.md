
```
./auto_complie.sh --root=/opt/HI5671Y/arm-mix510-linux  --compile=arm-mix510-linux- --openssl=openssl-1.1.1a  --host=ARM --size=64 --release_dir=  --tgt_name= --cpu_type=  --version=V2.4.1 --taskid=test3344 -e/opt/test2
```


```
./auto_complie.sh --taskid=ARM-EN7581-cfECONET-8530000_2024.04.12.161522 --root=/opt/ahs/compile/ARM-EN7581-cfECONET-8530000/toolchain/buildroot-gcc1030-glibc232-arm64_kernel5_4 --compile=aarch64-buildroot-linux-gnu- --openssl=openssl-1.1.1b --host=ARM --size=64 --lib_path= --cpu_type=EN7581 --version=V2.4.1 -e/opt/ahs/compile/ARM-EN7581-cfECONET-8530000/libs/zdzw
```


```
./auto_complie.sh --taskid=111111 --root=/mine/AOS-NET/autocomplie/toolchain/BE36M_toolchain/arm-mix510-linux  --compile=arm-mix510-linux- --openssl=openssl-1.1.1a --host=ARM --size=64 --lib_path= --cpu_type=IPQ5312 --version=V2.4.1 -e/mine/AOS-NET/out/BE36M_toolchain
```
