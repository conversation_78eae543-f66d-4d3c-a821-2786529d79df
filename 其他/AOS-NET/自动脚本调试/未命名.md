
```
./auto_complie.sh -kARM-EN7581-cfECONET-8530000_2024.04.11.164750 -r/opt/ahs/compile/ARM-EN7581-cfECONET-8530000/toolchain/buildroot-gcc1030-glibc232-arm64_kernel5_4 -caarch64-buildroot-linux-gnu- -oopenssl-1.1.1b -hARM -s64 --lib_path= --cpu_type=EN7581 --version=V2.4.1 -e/opt/ahs/compile/ARM-EN7581-cfECONET-8530000/libs/zdzw
```



```
./auto_complie.sh -ktest1 -d/opt/HI5671Y/arm-mix510-linux   -r/opt/HI5671Y/arm-mix510-linux  -carm-mix510-linux- -oopenssl-1.1.1a --host=ARM -s64 --lib_path= --cpu_type=EN7581 --version=V2.4.1 -e/opt/release_test1
```


```
export TOOLCHAIN_DIR=/opt/HI5671Y
export SYS_ROOT=${TOOLCHAIN_DIR}/arm-mix510-linux
export CROSS_COMPILE=arm-mix510-linux-
export OPENSSL_PREFIX=${TOOLCHAIN_DIR}/openssl-1.1.1a
export ZLIB=${TOOLCHAIN_DIR}/zlib-1.2.8
export UBUS_PREFIX=$(TOOLCHAIN_DIR)/ubus
export DEVICE_VENDOR=fiber
export DEVICE_MODEL=XXX
```
