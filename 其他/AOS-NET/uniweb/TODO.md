1、 日志文件打包
2、https


fix：

多用户登陆逻辑确定
managePlugin的mesh打开配置需要ubus接口都发送


## 2024-03-09 

异步获取接口

## 2023-11-29

#### TODO:

##### makefile相关：

* 第三方库的distclean功能异常，主要包含pcap等
* cmake编译生成的ubus相关库目前只用于uniweb,需要跟当前厂家提供的ubus区分开
* 

##### managePlugin:

* 加速功能优化

##### pluginDaemon:

* 插件管理优化
* 当前的处理逻辑需要设备有网络情况下才会拉起start.sh。目前会影响统一web的相关功能正常初始化，后续计划通过dns检测网络连通性

#### 统一web相关需求记录

* 前后端接口加密
* 可通过https登录
* 更加准确的手机和电脑识别
* 配置文件加密  (当前的ubus接口无法传输字节流，只能传输字符串流，需要改造)
* 杭研mesh与厂家mesh接口显示逻辑适配   doing
* 与fttr接口
* 

#### 其他杂

* 将说明书转换为md格式，便于后续更新跟踪
* 当前各插件的启动顺序问题还需要讨论
*