

## 1.	网关信息查询接口
### 1.1.	需求说明
需求描述：装维SDK通过内网访问接口地址，获取网关信息。
接口实现：实时接口。
### 1.2.	接口描述
接口地址：http://192.168.1.1:5080/gateway
HTTP请求方式：GET
请求数据格式:不涉及
返回数据格式：JSON
编码格式:UTF8
消息发送方向：SDK  AOS-NET插件
### 1.3.	请求参数
不涉及
### 1.4.	响应报文定义

| 参数      | 类型     | 必填  | 备注                   |
| ------- | ------ | --- | -------------------- |
| gwId    | String | M   | 网关MAC，使用全大写字符串，无冒号分隔 |
| sn      | String | M   | 网关SN                 |
| version | String | O   | AOS-NET插件版本          |

### 1.5.	响应报文示例


```
{"gwId":"CCF0FD438428","sn":"CMDCB2098D55","version":"2.3.2-180822"}
```


