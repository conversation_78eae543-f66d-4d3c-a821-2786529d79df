<mxfile host="Electron" modified="2024-04-23T03:04:27.664Z" agent="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.2.5 Chrome/120.0.6099.291 Electron/28.3.1 Safari/537.36" etag="-wYf4Zur-bdhS27q9aKG" version="24.2.5" type="device">
  <diagram id="C5RBs43oDa-KdzZeNtuy" name="Page-1">
    <mxGraphModel dx="2074" dy="1355" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" background="none" math="0" shadow="0">
      <root>
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-16" value="" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="761.5" y="140" width="559" height="380" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-56" value="" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="1140" y="153" width="160" height="357" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-0" value="" style="text;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="360" y="630" width="60" height="50" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y-bxeUDeMXDuZ1Vb3qlk-6" target="y-bxeUDeMXDuZ1Vb3qlk-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-6" value="" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="70" y="140" width="480" height="325" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y-bxeUDeMXDuZ1Vb3qlk-3" target="y-bxeUDeMXDuZ1Vb3qlk-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-3" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;&lt;font style=&quot;&quot;&gt;动态生成一个AES密钥AESKey&lt;/font&gt;&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="240" y="212.5" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-7" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;用Base64工具将AESKeySecert转码为密钥字符串AESKeySecertStr&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="390" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y-bxeUDeMXDuZ1Vb3qlk-8" target="y-bxeUDeMXDuZ1Vb3qlk-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-8" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;使用&lt;/span&gt;&lt;font style=&quot;margin: 0px; padding: 0px; -webkit-font-smoothing: antialiased; font-family: 默认字体;&quot;&gt;RSA公钥&lt;/font&gt;&lt;span style=&quot;font-family: 默认字体;&quot;&gt;对AESKey加密生成AESKeySecert字符串&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="390" y="212.5" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O73-rj1MKx33bHre1ThI-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y-bxeUDeMXDuZ1Vb3qlk-9" target="O73-rj1MKx33bHre1ThI-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-9" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;使用AES密钥对json_Str字符串加密生成bodyStrAes&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="240" y="312.5" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-10" value="&lt;span style=&quot;font-family: 默认字体; font-size: 14px; font-weight: 700;&quot;&gt;加密请求报文&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="250" y="152.5" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-11" value="&lt;span style=&quot;font-family: 默认字体; font-size: 14px; font-weight: 700;&quot;&gt;加密AES密钥&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="390" y="160" width="120" height="25" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.25;entryDx=0;entryDy=0;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y-bxeUDeMXDuZ1Vb3qlk-12" target="y-bxeUDeMXDuZ1Vb3qlk-16" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-12" value="" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="70" y="620" width="480" height="240" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y-bxeUDeMXDuZ1Vb3qlk-13" target="y-bxeUDeMXDuZ1Vb3qlk-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-13" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;构建http post请求对象&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="130" y="670" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y-bxeUDeMXDuZ1Vb3qlk-14" target="y-bxeUDeMXDuZ1Vb3qlk-15" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-14" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;将AESkeySecertStr放到http请求头中的SecurityKey中&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="130" y="770" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-15" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;将bodyStr放置到httpbody中&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="300" y="700" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y-bxeUDeMXDuZ1Vb3qlk-16" target="y-bxeUDeMXDuZ1Vb3qlk-22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-53" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y-bxeUDeMXDuZ1Vb3qlk-17" target="y-bxeUDeMXDuZ1Vb3qlk-19" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-17" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;从http请求头SecurityKey中获取AESKeySecertStr&lt;/span&gt;&lt;div&gt;&lt;span style=&quot;font-family: 默认字体;&quot;&gt;字符串&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="810" y="200" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-18" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;使用RSA私钥对AESKeySecert做解密操作获取AESkey&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="810" y="400" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-54" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y-bxeUDeMXDuZ1Vb3qlk-19" target="y-bxeUDeMXDuZ1Vb3qlk-18" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-19" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;用Base64工具将&lt;/span&gt;&lt;span style=&quot;font-family: 默认字体;&quot;&gt;AESKeySecertStr&lt;/span&gt;&lt;span style=&quot;font-family: 默认字体;&quot;&gt;解码获取&lt;/span&gt;&lt;span style=&quot;font-family: 默认字体;&quot;&gt;AESKeySecert&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="810" y="296.25" width="120" height="63.75" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y-bxeUDeMXDuZ1Vb3qlk-20" target="y-bxeUDeMXDuZ1Vb3qlk-21" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-20" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;从httpbody中获取请求报文主体httpBody&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="980.5" y="200" width="120" height="57.5" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-21" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;用Base64工具将&lt;/span&gt;&lt;span style=&quot;font-family: 默认字体;&quot;&gt;httpBody&lt;/span&gt;&lt;span style=&quot;font-family: 默认字体;&quot;&gt;解码获取httpBodyStrBase64&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="980.5" y="286.25" width="120" height="67.5" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-22" value="" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="771" y="600" width="539" height="330" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-23" value="&lt;span style=&quot;font-family: 默认字体; font-size: 14px;&quot;&gt;使用AESkey对返回结果做加密处理&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="981" y="735" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-24" value="" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="70" y="990" width="520" height="260" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-25" value="&lt;span style=&quot;font-family: 默认字体; font-size: 14px;&quot;&gt;接收请求返回结果&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="110" y="1090" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-52" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y-bxeUDeMXDuZ1Vb3qlk-26" target="y-bxeUDeMXDuZ1Vb3qlk-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-26" value="&lt;span style=&quot;font-family: 默认字体; font-size: 14px;&quot;&gt;使用AESKey解密返回结果&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="320" y="1090" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1.002;entryY=0.627;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="y-bxeUDeMXDuZ1Vb3qlk-22" target="y-bxeUDeMXDuZ1Vb3qlk-24" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1051" y="980" />
              <mxPoint x="686" y="980" />
              <mxPoint x="686" y="1153" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-38" value="&lt;span style=&quot;font-family: 默认字体; font-size: 14px; font-weight: 700;&quot;&gt;获取AESKey&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="800" y="167.5" width="130" height="25" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-39" value="&lt;span style=&quot;font-family: 默认字体; font-size: 14px; font-weight: 700;&quot;&gt;获取bodyStr&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="985.5" y="177.5" width="110" height="5" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-41" value="&lt;span style=&quot;font-family: 默认字体; font-size: 14px; font-weight: 700;&quot;&gt;验证签名&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="1190" y="162.5" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-42" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;使用MD5摘要算法对bodyStr做处理生成摘要字符串md5Str&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="1160" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-43" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;获取http请求头Authentication中的signature字符串&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="1160" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-44" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;使用RSA私钥对signature字符串做解密操作获取rsaDecrytStr字符串&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="1160" y="360" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-45" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;比较md5Str和rsaDe&lt;/span&gt;&lt;div style=&quot;margin: 0px; padding: 0px; -webkit-font-smoothing: antialiased; font-family: 默认字体;&quot;&gt;cryptStr是否一致，如果一致则验签通过&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="1160" y="430" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-55" value="" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="80" y="150" width="150" height="300" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-46" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;将传输对象转换成json字符串jsonStr&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="90" y="213" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-47" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;使用md5对json字符串做摘要处理生成md5摘要&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="90" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-48" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;使用&lt;/span&gt;&lt;font style=&quot;margin: 0px; padding: 0px; -webkit-font-smoothing: antialiased; font-family: 默认字体;&quot;&gt;RSA公钥&lt;/font&gt;&lt;span style=&quot;font-family: 默认字体;&quot;&gt;对md5数据做加密生成signature&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="90" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y-bxeUDeMXDuZ1Vb3qlk-49" value="&lt;span style=&quot;font-family: 默认字体; font-size: 14px; font-weight: 700;&quot;&gt;生成签名&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=default;labelBackgroundColor=default;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="120" y="155" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="O73-rj1MKx33bHre1ThI-0" value="&lt;p style=&quot;margin: 0pt 0pt 0.0001pt; padding: 0px; font-size: 10.5pt; background-color: rgb(248, 249, 250); text-align: justify; font-family: &amp;quot;Times New Roman&amp;quot;; text-indent: 21pt;&quot; class=&quot;MsoNormal&quot;&gt;&lt;span style=&quot;margin: 0px; padding: 0px; font-size: 10.5pt;&quot;&gt;&lt;font style=&quot;margin: 0px; padding: 0px;&quot; face=&quot;宋体&quot;&gt;body报文加密算法采用&lt;/font&gt;AES-128&lt;font style=&quot;margin: 0px; padding: 0px;&quot; face=&quot;宋体&quot;&gt;，对全部&lt;/font&gt;&lt;font style=&quot;margin: 0px; padding: 0px;&quot; face=&quot;Times New Roman&quot;&gt;JSON&lt;/font&gt;&lt;font style=&quot;margin: 0px; padding: 0px;&quot; face=&quot;宋体&quot;&gt;数据加密，详细方式约定：&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;margin: 0px; padding: 0px; font-size: 10.5pt;&quot;&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;margin: 0px; padding: 0px; font-family: 默认字体; background-color: rgb(248, 249, 250); font-size: medium; text-align: start; text-indent: 21pt;&quot; class=&quot;17&quot;&gt;&lt;span style=&quot;margin: 0px; padding: 0px; font-family: &amp;quot;Times New Roman&amp;quot;; font-size: 10.5pt;&quot;&gt;&lt;font style=&quot;margin: 0px; padding: 0px;&quot; face=&quot;宋体&quot;&gt;加密模式：&lt;/font&gt;CBC&lt;/span&gt;&lt;span style=&quot;margin: 0px; padding: 0px; font-family: &amp;quot;Times New Roman&amp;quot;; font-size: 10.5pt;&quot;&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;margin: 0px; padding: 0px; font-family: 默认字体; background-color: rgb(248, 249, 250); font-size: medium; text-align: start; text-indent: 21pt;&quot; class=&quot;17&quot;&gt;&lt;span style=&quot;margin: 0px; padding: 0px; font-family: &amp;quot;Times New Roman&amp;quot;; font-size: 10.5pt;&quot;&gt;&lt;font style=&quot;margin: 0px; padding: 0px;&quot; face=&quot;宋体&quot;&gt;填充方式：&lt;/font&gt;PKCS7Padding&lt;/span&gt;&lt;span style=&quot;margin: 0px; padding: 0px; font-family: &amp;quot;Times New Roman&amp;quot;; font-size: 10.5pt;&quot;&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;margin: 0px; padding: 0px; font-family: 默认字体; background-color: rgb(248, 249, 250); font-size: medium; text-align: start; text-indent: 21pt;&quot; class=&quot;17&quot;&gt;&lt;span style=&quot;margin: 0px; padding: 0px; font-family: &amp;quot;Times New Roman&amp;quot;; font-size: 10.5pt;&quot;&gt;&lt;font style=&quot;margin: 0px; padding: 0px;&quot; face=&quot;宋体&quot;&gt;加密密钥：客户端动态生成AESKey后采用RSA公钥进行加密传输&lt;/font&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;margin: 0px; padding: 0px; font-family: 默认字体; background-color: rgb(248, 249, 250); font-size: medium; text-align: start; text-indent: 21pt;&quot; class=&quot;17&quot;&gt;&lt;span style=&quot;margin: 0px; padding: 0px; font-family: &amp;quot;Times New Roman&amp;quot;; font-size: 10.5pt;&quot;&gt;&lt;font style=&quot;margin: 0px; padding: 0px;&quot; face=&quot;宋体&quot;&gt;偏移量：&lt;/font&gt;&lt;font style=&quot;margin: 0px; padding: 0px;&quot; face=&quot;Times New Roman&quot;&gt;””(16&lt;/font&gt;&lt;font style=&quot;margin: 0px; padding: 0px;&quot; face=&quot;宋体&quot;&gt;个&lt;/font&gt;&lt;font style=&quot;margin: 0px; padding: 0px;&quot; face=&quot;Times New Roman&quot;&gt;0)&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;margin: 0px; padding: 0px; font-family: &amp;quot;Times New Roman&amp;quot;; font-size: 10.5pt;&quot;&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;margin: 0px; padding: 0px; font-family: 默认字体; background-color: rgb(248, 249, 250); font-size: medium; text-align: start; text-indent: 21pt;&quot; class=&quot;17&quot;&gt;&lt;span style=&quot;margin: 0px; padding: 0px; font-family: &amp;quot;Times New Roman&amp;quot;; font-size: 10.5pt;&quot;&gt;&lt;font style=&quot;margin: 0px; padding: 0px;&quot; face=&quot;宋体&quot;&gt;输出格式：&lt;/font&gt;base64&lt;/span&gt;&lt;span style=&quot;margin: 0px; padding: 0px; font-family: &amp;quot;Times New Roman&amp;quot;; font-size: 10.5pt;&quot;&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;margin: 0px; padding: 0px; font-family: 默认字体; background-color: rgb(248, 249, 250); font-size: medium; text-align: start; text-indent: 21pt;&quot; class=&quot;17&quot;&gt;&lt;span style=&quot;margin: 0px; padding: 0px; font-family: &amp;quot;Times New Roman&amp;quot;; font-size: 10.5pt;&quot;&gt;&lt;font style=&quot;margin: 0px; padding: 0px;&quot; face=&quot;宋体&quot;&gt;字符集：&lt;/font&gt;utf-8&lt;/span&gt;&lt;/p&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="1530" y="313" width="520" height="227" as="geometry" />
        </mxCell>
        <mxCell id="O73-rj1MKx33bHre1ThI-1" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;使用Base64对&lt;/span&gt;&lt;span style=&quot;font-family: 默认字体;&quot;&gt;bodyStr&lt;/span&gt;&lt;span style=&quot;font-family: 默认字体;&quot;&gt;字符串加密生成bodyStr&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="240" y="395" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O73-rj1MKx33bHre1ThI-3" value="&lt;span style=&quot;font-family: 默认字体;&quot;&gt;使用AESKey对httpBodyStrBase做解密操作取的的bodyStr&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;labelBackgroundColor=default;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="981" y="392.75" width="120" height="67.5" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
