<mxfile host="Electron" modified="2024-05-20T09:13:55.484Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.2.5 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="WF-TGYg8KsJfenj-hYEP" version="24.2.5" type="device">
  <diagram name="第 1 页" id="Q6UJ8aG-sG7cmK2qaA7U">
    <mxGraphModel dx="1292" dy="885" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="qqY04VDjTgi4tnwkZm9l-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="qqY04VDjTgi4tnwkZm9l-1" target="qqY04VDjTgi4tnwkZm9l-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-1" value="socket()" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="290" y="170" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="qqY04VDjTgi4tnwkZm9l-2" target="qqY04VDjTgi4tnwkZm9l-19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-2" value="sendto()" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="290" y="330" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="qqY04VDjTgi4tnwkZm9l-3" target="qqY04VDjTgi4tnwkZm9l-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-3" value="socket()" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="170" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="qqY04VDjTgi4tnwkZm9l-4" target="qqY04VDjTgi4tnwkZm9l-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-4" value="bind()" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="290" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="qqY04VDjTgi4tnwkZm9l-5">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="620" y="640" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-13" value="阻塞到客户发送数据" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="qqY04VDjTgi4tnwkZm9l-11">
          <mxGeometry x="-0.6364" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-18" value="处理请求" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="qqY04VDjTgi4tnwkZm9l-11">
          <mxGeometry x="0.2138" y="4" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-5" value="recefrom()" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="410" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-9" value="设备侧" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="320" y="100" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-10" value="平台侧" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="590" y="108" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="qqY04VDjTgi4tnwkZm9l-12" target="qqY04VDjTgi4tnwkZm9l-19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-21" value="应答" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="qqY04VDjTgi4tnwkZm9l-20">
          <mxGeometry x="0.2625" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-12" value="sendto()" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="570" y="640" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-17" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="370" as="sourcePoint" />
            <mxPoint x="620" y="520" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-24" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="qqY04VDjTgi4tnwkZm9l-19" target="qqY04VDjTgi4tnwkZm9l-23">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-19" value="recefrom()" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="290" y="640" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qqY04VDjTgi4tnwkZm9l-23" value="close()" style="whiteSpace=wrap;html=1;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="290" y="780" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
