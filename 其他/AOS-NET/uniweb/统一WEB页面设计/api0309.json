[{"index": 0, "name": "三期需求相关", "desc": null, "add_time": 1689241363, "up_time": 1689241363, "list": [{"query_path": {"path": "/api/getRequestDeviceInfo", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 12784, "method": "GET", "catid": 2187, "title": "1.1 获取当前请求设备信息", "path": "/api/getRequestDeviceInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "64dd8ac0f183d274162080fd", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "64dd8ac0f183d274162080fc", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"deviceIP\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"4段\"},\"description\":\"当前请求设备IP信息\"}},\"required\":[\"deviceIP\"]},\"description\":\"响应数据\"}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_other": "{\"type\":\"object\",\"properties\":{}}", "req_body_type": "json", "uid": 368, "add_time": 1691049217, "up_time": 1693886946, "__v": 0}, {"query_path": {"path": "/api/getNeighborInfo", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 1, "tag": [], "_id": 12764, "method": "GET", "catid": 2187, "title": "1.2 获取周边wifi信息", "path": "/api/getNeighborInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "64e2f906f183d27416208196", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "64e2f906f183d27416208195", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"radio\":{\"type\":\"string\",\"description\":\"频段：2.4G、5G\"},\"ssid\":{\"type\":\"string\",\"description\":\"周边wifi的SSID名称\"},\"rssi\":{\"type\":\"number\",\"description\":\"周边wifi的弱信号值  信号强度\"},\"securityMode\":{\"type\":\"number\",\"description\":\"认证加密模式，枚举取值：0: None 1:WEP-64 2:WEP-128 3:WPA-Personal 4:WPA2-Personal 5:MIXED-WPAPSK2 6:WPA3-SAE 7::MIXED-WPA2WPA3\\t\"},\"securityModeText\":{\"type\":\"string\",\"description\":\"认证加密模式，字符串取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\\t\"}},\"required\":[\"radio\",\"ssid\",\"rssi\",\"securityMode\",\"securityModeText\"]},\"description\":\"响应数据\"}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_other": "{\"type\":\"object\",\"properties\":{}}", "req_body_type": "json", "uid": 368, "add_time": 1690187685, "up_time": 1693886951, "__v": 0}, {"query_path": {"path": "/api/login", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 2, "tag": [], "_id": 12744, "method": "POST", "catid": 2187, "title": "1.3 web登录验证", "path": "/api/login", "project_id": 60, "req_params": [], "res_body_type": "json", "uid": 368, "add_time": 1689562324, "up_time": 1693886960, "req_query": [], "req_headers": [{"required": "1", "_id": "64dd8ad1f183d27416208100", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "__v": 0, "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"user\":{\"type\":\"string\",\"description\":\"登录用户名\"},\"password\":{\"type\":\"string\",\"description\":\"登录密码\"}},\"required\":[\"user\",\"password\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值\"},\"data\":{\"type\":\"object\",\"properties\":{\"authorToken\":{\"type\":\"string\",\"description\":\"验证成功，返回的token值\"}},\"required\":[\"authorToken\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json"}, {"query_path": {"path": "/api/configWifiInfo", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 3, "tag": [], "_id": 12739, "method": "POST", "catid": 2187, "title": "1.4 配置组网设备wifi信息(异步)", "path": "/api/configWifiInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65d5b929f183d27416209059", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "65d5b929f183d27416209058", "name": "authorToken", "value": "", "example": "", "desc": ""}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_other": "{\"type\":\"object\",\"properties\":{\"transmitPower1\":{\"type\":\"number\",\"description\":\"Radios数组之一，WiFi发射功率级别（百分比） 信号强度\"},\"channel1\":{\"type\":\"string\",\"description\":\"Radios数组之一，当前工作信道号\"},\"bandwidth1\":{\"type\":\"number\",\"description\":\"当前Wi-Fi频宽，采用枚举取值： 0：自动频宽 1：20MHz 2：40MHz 3：80MHz 4：160MHz 5：80+80MHz 说明：对于2.4G频段，自动频宽功能为20/40MHz频宽自适应；对于5G频段，自动频宽功能对应为20/40/80/160MHz频宽自适应\"},\"radio1\":{\"type\":\"string\",\"description\":\"频段，枚举取值： 2.4G 5G 5G-2 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"enable1\":{\"type\":\"number\",\"description\":\"是否启用，1为启用，0为禁用\"},\"ssid1\":{\"type\":\"string\",\"description\":\"SSID名称\"},\"securityMode1\":{\"type\":\"string\",\"description\":\"认证加密模式，枚举取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\"},\"encrypt1\":{\"type\":\"number\",\"description\":\"加密算法，枚举取值： 0：None: 1：AES 2：TKIP 3：AES+TKIP\"},\"pwd1\":{\"type\":\"string\",\"description\":\"WiFi密钥，WEP Key或Pre-shared Key，管理平台不应存储和显示此参数。\"},\"maxAssociateNum1\":{\"type\":\"number\",\"description\":\"该SSID最大允许接入的用户数，0表示不限制\"},\"ssidAdvertisementEnabled1\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播\"},\"ssidStandard1\":{\"type\":\"string\",\"description\":\"支持的无线传输标准，取值 802.11a 802.11b 802.11b/g 802.11b/g/n 802.11a/n/ac 802.11b/g/n/ax 802.11a/n/ac/ax 802.11a/n 802.11n 802.11g 802.11g/n等\"},\"transmitPower4\":{\"type\":\"number\",\"description\":\"Radios数组之一，WiFi发射功率级别（百分比）  信号强度\"},\"channel4\":{\"type\":\"string\",\"description\":\"Radios数组之一，当前工作信道号\"},\"bandwidth4\":{\"type\":\"number\",\"description\":\"当前Wi-Fi频宽，采用枚举取值： 0：自动频宽 1：20MHz 2：40MHz 3：80MHz 4：160MHz 5：80+80MHz 说明：对于2.4G频段，自动频宽功能为20/40MHz频宽自适应；对于5G频段，自动频宽功能对应为20/40/80/160MHz频宽自适应\"},\"radio4\":{\"type\":\"string\",\"description\":\"频段，枚举取值： 2.4G 5G 5G-2 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"enable4\":{\"type\":\"number\",\"description\":\"是否启用，1为启用，0为禁用\"},\"ssid4\":{\"type\":\"string\",\"description\":\"SSID名称\"},\"securityMode4\":{\"type\":\"string\",\"description\":\"认证加密模式，枚举取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\"},\"encrypt4\":{\"type\":\"number\",\"description\":\"加密算法，枚举取值： 0：None: 1：AES 2：TKIP 3：AES+TKIP\"},\"pwd4\":{\"type\":\"string\",\"description\":\"WiFi密钥，WEP Key或Pre-shared Key，管理平台不应存储和显示此参数。\"},\"maxAssociateNum4\":{\"type\":\"number\",\"description\":\"该SSID最大允许接入的用户数，0表示不限制\"},\"ssidAdvertisementEnabled4\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播\"},\"ssidStandard4\":{\"type\":\"string\",\"description\":\"支持的无线传输标准，取值 802.11a 802.11b 802.11b/g 802.11b/g/n 802.11a/n/ac 802.11b/g/n/ax 802.11a/n/ac/ax 802.11a/n 802.11n 802.11g 802.11g/n等\"},\"transmitPower5\":{\"type\":\"number\",\"description\":\"Radios数组之一，WiFi发射功率级别（百分比）信号强度\"},\"channel5\":{\"type\":\"string\",\"description\":\"Radios数组之一，当前工作信道号\"},\"bandwidth5\":{\"type\":\"number\",\"description\":\"当前Wi-Fi频宽，采用枚举取值： 0：自动频宽 1：20MHz 2：40MHz 3：80MHz 4：160MHz 5：80+80MHz 说明：对于2.4G频段，自动频宽功能为20/40MHz频宽自适应；对于5G频段，自动频宽功能对应为20/40/80/160MHz频宽自适应\"},\"radio5\":{\"type\":\"string\",\"description\":\"频段，枚举取值： 2.4G 5G 5G-2 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"enable5\":{\"type\":\"number\",\"description\":\"是否启用，1为启用，0为禁用\"},\"ssid5\":{\"type\":\"string\",\"description\":\"SSID名称\"},\"securityMode5\":{\"type\":\"string\",\"description\":\"认证加密模式，枚举取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\"},\"encrypt5\":{\"type\":\"number\",\"description\":\"加密算法，枚举取值： 0：None: 1：AES 2：TKIP 3：AES+TKIP\"},\"pwd5\":{\"type\":\"string\",\"description\":\"WiFi密钥，WEP Key或Pre-shared Key，管理平台不应存储和显示此参数。\"},\"maxAssociateNum5\":{\"type\":\"number\",\"description\":\"该SSID最大允许接入的用户数，0表示不限制\"},\"ssidAdvertisementEnabled5\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播\"},\"ssidStandard5\":{\"type\":\"string\",\"description\":\"支持的无线传输标准，取值 802.11a 802.11b 802.11b/g 802.11b/g/n 802.11a/n/ac 802.11b/g/n/ax 802.11a/n/ac/ax 802.11a/n 802.11n 802.11g 802.11g/n等\"},\"transmitPower8\":{\"type\":\"number\",\"description\":\"Radios数组之一，WiFi发射功率级别（百分比）信号强度\"},\"channel8\":{\"type\":\"string\",\"description\":\"Radios数组之一，当前工作信道号\"},\"bandwidth8\":{\"type\":\"number\",\"description\":\"当前Wi-Fi频宽，采用枚举取值： 0：自动频宽 1：20MHz 2：40MHz 3：80MHz 4：160MHz 5：80+80MHz 说明：对于2.4G频段，自动频宽功能为20/40MHz频宽自适应；对于5G频段，自动频宽功能对应为20/40/80/160MHz频宽自适应\"},\"radio8\":{\"type\":\"string\",\"description\":\"频段，枚举取值： 2.4G 5G 5G-2 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"enable8\":{\"type\":\"number\",\"description\":\"是否启用，1为启用，0为禁用\"},\"ssid8\":{\"type\":\"string\",\"description\":\"SSID名称\"},\"securityMode8\":{\"type\":\"string\",\"description\":\"认证加密模式，枚举取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\"},\"encrypt8\":{\"type\":\"number\",\"description\":\"加密算法，枚举取值： 0：None: 1：AES 2：TKIP 3：AES+TKIP\"},\"pwd8\":{\"type\":\"string\",\"description\":\"WiFi密钥，WEP Key或Pre-shared Key，管理平台不应存储和显示此参数。\"},\"maxAssociateNum8\":{\"type\":\"number\",\"description\":\"该SSID最大允许接入的用户数，0表示不限制\"},\"ssidAdvertisementEnabled8\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播\"},\"ssidStandard8\":{\"type\":\"string\",\"description\":\"支持的无线传输标准，取值 802.11a 802.11b 802.11b/g 802.11b/g/n 802.11a/n/ac 802.11b/g/n/ax 802.11a/n/ac/ax 802.11a/n 802.11n 802.11g 802.11g/n等\"},\"bandSteeringStatus\":{\"type\":\"number\",\"description\":\"双频合一开关： 0：关闭 1：打开\"},\"mloStatus\":{\"type\":\"number\",\"description\":\"MLO使能状态： 0：关闭 1：打开\"},\"compatibilityStatus\":{\"type\":\"number\",\"description\":\"兼容模式开关： 0：关闭 1：打开\"},\"ssid15\":{\"type\":\"string\",\"description\":\"双频合一的ssid1，ssid5的名称\"},\"pwd15\":{\"type\":\"string\",\"description\":\"双频合一的ssid1，ssid5的密码\"},\"ssidAdvertisementEnabled15\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播   是否隐藏\"},\"ssid48\":{\"type\":\"string\",\"description\":\"双频合一的ssid4，ssid8的名称\"},\"pwd48\":{\"type\":\"string\",\"description\":\"双频合一的ssid4，ssid8的名称\"},\"ssidAdvertisementEnabled48\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播 是否隐藏\"},\"transmitPower3\":{\"type\":\"number\",\"description\":\"Radios数组之一，WiFi发射功率级别（百分比） 信号强度\"},\"channel3\":{\"type\":\"string\",\"description\":\"Radios数组之一，当前工作信道号\"},\"bandwidth3\":{\"type\":\"number\",\"description\":\"当前Wi-Fi频宽，采用枚举取值： 0：自动频宽 1：20MHz 2：40MHz 3：80MHz 4：160MHz 5：80+80MHz 说明：对于2.4G频段，自动频宽功能为20/40MHz频宽自适应；对于5G频段，自动频宽功能对应为20/40/80/160MHz频宽自适应\"},\"radio3\":{\"type\":\"string\",\"description\":\"频段，枚举取值： 2.4G 5G 5G-2 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"enable3\":{\"type\":\"number\",\"description\":\"是否启用，1为启用，0为禁用\"},\"ssid3\":{\"type\":\"string\",\"description\":\"SSID名称\"},\"securityMode3\":{\"type\":\"string\",\"description\":\"认证加密模式，枚举取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\"},\"encrypt3\":{\"type\":\"number\",\"description\":\"加密算法，枚举取值： 0：None: 1：AES 2：TKIP 3：AES+TKIP\"},\"pwd3\":{\"type\":\"string\",\"description\":\"WiFi密钥，WEP Key或Pre-shared Key，管理平台不应存储和显示此参数。\"},\"maxAssociateNum3\":{\"type\":\"number\",\"description\":\"该SSID最大允许接入的用户数，0表示不限制\"},\"ssidAdvertisementEnabled3\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播   是否隐藏\"},\"ssidStandard3\":{\"type\":\"string\",\"description\":\"支持的无线传输标准，取值 802.11a 802.11b 802.11b/g 802.11b/g/n 802.11a/n/ac 802.11b/g/n/ax 802.11a/n/ac/ax 802.11a/n 802.11n 802.11g 802.11g/n等\"},\"ssidTimeout3\":{\"type\":\"number\",\"description\":\"启动时间   0 表示无限制 其他数值以小时为单位的设置时间\"},\"transmitPower7\":{\"type\":\"number\",\"description\":\"Radios数组之一，WiFi发射功率级别（百分比） 信号强度\"},\"channel7\":{\"type\":\"string\",\"description\":\"Radios数组之一，当前工作信道号\"},\"bandwidth7\":{\"type\":\"number\",\"description\":\"当前Wi-Fi频宽，采用枚举取值： 0：自动频宽 1：20MHz 2：40MHz 3：80MHz 4：160MHz 5：80+80MHz 说明：对于2.4G频段，自动频宽功能为20/40MHz频宽自适应；对于5G频段，自动频宽功能对应为20/40/80/160MHz频宽自适应\"},\"radio7\":{\"type\":\"string\",\"description\":\"频段，枚举取值： 2.4G 5G 5G-2 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"enable7\":{\"type\":\"number\",\"description\":\"是否启用，1为启用，0为禁用\"},\"ssid7\":{\"type\":\"string\",\"description\":\"SSID名称\"},\"securityMode7\":{\"type\":\"string\",\"description\":\"认证加密模式，枚举取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\"},\"encrypt7\":{\"type\":\"string\",\"description\":\"加密算法，枚举取值： 0：None: 1：AES 2：TKIP 3：AES+TKIP\"},\"pwd7\":{\"type\":\"string\",\"description\":\"WiFi密钥，WEP Key或Pre-shared Key，管理平台不应存储和显示此参数。\"},\"maxAssociateNum7\":{\"type\":\"string\",\"description\":\"该SSID最大允许接入的用户数，0表示不限制\"},\"ssidAdvertisementEnabled7\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播   是否隐藏\"},\"ssidStandard7\":{\"type\":\"string\",\"description\":\"支持的无线传输标准，取值 802.11a 802.11b 802.11b/g 802.11b/g/n 802.11a/n/ac 802.11b/g/n/ax 802.11a/n/ac/ax 802.11a/n 802.11n 802.11g 802.11g/n等\"},\"ssidTimeout7\":{\"type\":\"number\",\"description\":\"启动时间   0 表示无限制 其他数值以小时为单位的设置时间\"}}}", "req_body_type": "json", "uid": 368, "add_time": 1689561079, "up_time": 1708505385, "__v": 0}, {"query_path": {"path": "/api/getWifiInfo", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 4, "tag": [], "_id": 12714, "method": "GET", "catid": 2187, "title": "1.5 获取组网设备wifi信息", "path": "/api/getWifiInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65d5b890f183d27416209055", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "65d5b890f183d27416209054", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"transmitPower1\":{\"type\":\"number\",\"description\":\"Radios数组之一，WiFi发射功率级别（百分比）\"},\"channel1\":{\"type\":\"string\",\"description\":\"Radios数组之一，当前工作信道号\"},\"bandwidth1\":{\"type\":\"number\",\"description\":\"当前Wi-Fi频宽，采用枚举取值： 0：自动频宽 1：20MHz 2：40MHz 3：80MHz 4：160MHz 5：80+80MHz 说明：对于2.4G频段，自动频宽功能为20/40MHz频宽自适应；对于5G频段，自动频宽功能对应为20/40/80/160MHz频宽自适应\"},\"radio1\":{\"type\":\"string\",\"description\":\"频段，枚举取值： 2.4G 5G 5G-2 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"enable1\":{\"type\":\"number\",\"description\":\"是否启用，1为启用，0为禁用\"},\"ssid1\":{\"type\":\"string\",\"description\":\"SSID名称\"},\"securityMode1\":{\"type\":\"string\",\"description\":\"认证加密模式，枚举取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\"},\"encrypt1\":{\"type\":\"number\",\"description\":\"加密算法，枚举取值： 0：None: 1：AES 2：TKIP 3：AES+TKIP 4、AES + SAE\"},\"pwd1\":{\"type\":\"string\",\"description\":\"WiFi密钥，WEP Key或Pre-shared Key，管理平台不应存储和显示此参数。\"},\"maxAssociateNum1\":{\"type\":\"number\",\"description\":\"该SSID最大允许接入的用户数，0表示不限制\"},\"ssidAdvertisementEnabled1\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播\"},\"ssidStandard1\":{\"type\":\"string\",\"description\":\"支持的无线传输标准，取值 802.11a 802.11b 802.11b/g 802.11b/g/n 802.11a/n/ac 802.11b/g/n/ax 802.11a/n/ac/ax 802.11a/n 802.11n 802.11g 802.11g/n等\"},\"transmitPower4\":{\"type\":\"number\",\"description\":\"Radios数组之一，WiFi发射功率级别（百分比）\"},\"channel4\":{\"type\":\"string\",\"description\":\"Radios数组之一，当前工作信道号\"},\"bandwidth4\":{\"type\":\"number\",\"description\":\"当前Wi-Fi频宽，采用枚举取值： 0：自动频宽 1：20MHz 2：40MHz 3：80MHz 4：160MHz 5：80+80MHz 说明：对于2.4G频段，自动频宽功能为20/40MHz频宽自适应；对于5G频段，自动频宽功能对应为20/40/80/160MHz频宽自适应\"},\"radio4\":{\"type\":\"string\",\"description\":\"频段，枚举取值： 2.4G 5G 5G-2 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"enable4\":{\"type\":\"number\",\"description\":\"是否启用，1为启用，0为禁用\"},\"ssid4\":{\"type\":\"string\",\"description\":\"SSID名称\"},\"securityMode4\":{\"type\":\"string\",\"description\":\"认证加密模式，枚举取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\"},\"encrypt4\":{\"type\":\"number\",\"description\":\"加密算法，枚举取值： 0：None: 1：AES 2：TKIP 3：AES+TKIP 4、AES + SAE\"},\"pwd4\":{\"type\":\"string\",\"description\":\"WiFi密钥，WEP Key或Pre-shared Key，管理平台不应存储和显示此参数。\"},\"maxAssociateNum4\":{\"type\":\"number\",\"description\":\"该SSID最大允许接入的用户数，0表示不限制\"},\"ssidAdvertisementEnabled4\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播\"},\"ssidStandard4\":{\"type\":\"string\",\"description\":\"支持的无线传输标准，取值 802.11a 802.11b 802.11b/g 802.11b/g/n 802.11a/n/ac 802.11b/g/n/ax 802.11a/n/ac/ax 802.11a/n 802.11n 802.11g 802.11g/n等\"},\"transmitPower5\":{\"type\":\"number\",\"description\":\"Radios数组之一，WiFi发射功率级别（百分比）\"},\"channel5\":{\"type\":\"string\",\"description\":\"Radios数组之一，当前工作信道号\"},\"bandwidth5\":{\"type\":\"number\",\"description\":\"当前Wi-Fi频宽，采用枚举取值： 0：自动频宽 1：20MHz 2：40MHz 3：80MHz 4：160MHz 5：80+80MHz 说明：对于2.4G频段，自动频宽功能为20/40MHz频宽自适应；对于5G频段，自动频宽功能对应为20/40/80/160MHz频宽自适应\"},\"radio5\":{\"type\":\"string\",\"description\":\"频段，枚举取值： 2.4G 5G 5G-2 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"enable5\":{\"type\":\"number\",\"description\":\"是否启用，1为启用，0为禁用\"},\"ssid5\":{\"type\":\"string\",\"description\":\"SSID名称\"},\"securityMode5\":{\"type\":\"string\",\"description\":\"认证加密模式，枚举取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\"},\"encrypt5\":{\"type\":\"number\",\"description\":\"加密算法，枚举取值： 0：None: 1：AES 2：TKIP 3：AES+TKIP 4、AES + SAE\"},\"pwd5\":{\"type\":\"string\",\"description\":\"WiFi密钥，WEP Key或Pre-shared Key，管理平台不应存储和显示此参数。\"},\"maxAssociateNum5\":{\"type\":\"number\",\"description\":\"该SSID最大允许接入的用户数，0表示不限制\"},\"ssidAdvertisementEnabled5\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播\"},\"ssidStandard5\":{\"type\":\"string\",\"description\":\"支持的无线传输标准，取值 802.11a 802.11b 802.11b/g 802.11b/g/n 802.11a/n/ac 802.11b/g/n/ax 802.11a/n/ac/ax 802.11a/n 802.11n 802.11g 802.11g/n等\"},\"transmitPower8\":{\"type\":\"number\",\"description\":\"Radios数组之一，WiFi发射功率级别（百分比）\"},\"channel8\":{\"type\":\"string\",\"description\":\"Radios数组之一，当前工作信道号\"},\"bandwidth8\":{\"type\":\"number\",\"description\":\"当前Wi-Fi频宽，采用枚举取值： 0：自动频宽 1：20MHz 2：40MHz 3：80MHz 4：160MHz 5：80+80MHz 说明：对于2.4G频段，自动频宽功能为20/40MHz频宽自适应；对于5G频段，自动频宽功能对应为20/40/80/160MHz频宽自适应\"},\"radio8\":{\"type\":\"string\",\"description\":\"频段，枚举取值： 2.4G 5G 5G-2 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"enable8\":{\"type\":\"number\",\"description\":\"是否启用，1为启用，0为禁用\"},\"ssid8\":{\"type\":\"string\",\"description\":\"SSID名称\"},\"securityMode8\":{\"type\":\"string\",\"description\":\"认证加密模式，枚举取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\"},\"encrypt8\":{\"type\":\"number\",\"description\":\"加密算法，枚举取值： 0：None: 1：AES 2：TKIP 3：AES+TKIP 4、AES + SAE\"},\"pwd8\":{\"type\":\"string\",\"description\":\"WiFi密钥，WEP Key或Pre-shared Key，管理平台不应存储和显示此参数。\"},\"maxAssociateNum8\":{\"type\":\"number\",\"description\":\"该SSID最大允许接入的用户数，0表示不限制\"},\"ssidAdvertisementEnabled8\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播\"},\"ssidStandard8\":{\"type\":\"string\",\"description\":\"支持的无线传输标准，取值 802.11a 802.11b 802.11b/g 802.11b/g/n 802.11a/n/ac 802.11b/g/n/ax 802.11a/n/ac/ax 802.11a/n 802.11n 802.11g 802.11g/n等\"},\"bandSteeringStatus\":{\"type\":\"number\",\"description\":\"双频合一开关： 0：关闭 1：打开\"},\"mloStatus\":{\"type\":\"number\",\"description\":\"MLO使能状态： 0：关闭 1：打开\"},\"compatibilityStatus\":{\"type\":\"number\",\"description\":\"兼容模式开关： 0：关闭 1：打开\"},\"ssid15\":{\"type\":\"string\",\"description\":\"双频合一的ssid1，ssid5的名称\"},\"pwd15\":{\"type\":\"string\",\"description\":\"双频合一的ssid1，ssid5的密码\"},\"ssidAdvertisementEnabled15\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播   是否隐藏\"},\"ssid48\":{\"type\":\"string\",\"description\":\"双频合一的ssid4，ssid8的名称\"},\"pwd48\":{\"type\":\"string\",\"description\":\"双频合一的ssid4，ssid8的名称\"},\"ssidAdvertisementEnabled48\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播   是否隐藏\"},\"readOnlyEnable\":{\"type\":\"number\",\"description\":\"是否允许修改wifi账号密码  0：允许  1：不允许\"},\"transmitPower3\":{\"type\":\"number\",\"description\":\"Radios数组之一，WiFi发射功率级别（百分比）\"},\"channel3\":{\"type\":\"string\",\"description\":\"Radios数组之一，当前工作信道号\"},\"bandwidth3\":{\"type\":\"number\",\"description\":\"当前Wi-Fi频宽，采用枚举取值： 0：自动频宽 1：20MHz 2：40MHz 3：80MHz 4：160MHz 5：80+80MHz 说明：对于2.4G频段，自动频宽功能为20/40MHz频宽自适应；对于5G频段，自动频宽功能对应为20/40/80/160MHz频宽自适应\"},\"radio3\":{\"type\":\"string\",\"description\":\"频段，枚举取值： 2.4G 5G 5G-2 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"enable3\":{\"type\":\"number\",\"description\":\"是否启用，1为启用，0为禁用\"},\"ssid3\":{\"type\":\"string\",\"description\":\"SSID名称\"},\"securityMode3\":{\"type\":\"string\",\"description\":\"认证加密模式，枚举取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\"},\"encrypt3\":{\"type\":\"number\",\"description\":\"加密算法，枚举取值： 0：None: 1：AES 2：TKIP 3：AES+TKIP 4、AES + SAE\"},\"pwd3\":{\"type\":\"string\",\"description\":\"WiFi密钥，WEP Key或Pre-shared Key，管理平台不应存储和显示此参数。\"},\"maxAssociateNum3\":{\"type\":\"number\",\"description\":\"该SSID最大允许接入的用户数，0表示不限制\"},\"ssidAdvertisementEnabled3\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播\"},\"ssidStandard3\":{\"type\":\"string\",\"description\":\"支持的无线传输标准，取值 802.11a 802.11b 802.11b/g 802.11b/g/n 802.11a/n/ac 802.11b/g/n/ax 802.11a/n/ac/ax 802.11a/n 802.11n 802.11g 802.11g/n等\"},\"ssidTimeout3\":{\"type\":\"number\",\"description\":\"启动时间   0 表示无限制 其他数值以小时为单位的设置时间 -1为关闭\"},\"transmitPower7\":{\"type\":\"number\",\"description\":\"Radios数组之一，WiFi发射功率级别（百分比）\"},\"channel7\":{\"type\":\"string\",\"description\":\"Radios数组之一，当前工作信道号\"},\"bandwidth7\":{\"type\":\"number\",\"description\":\"当前Wi-Fi频宽，采用枚举取值： 0：自动频宽 1：20MHz 2：40MHz 3：80MHz 4：160MHz 5：80+80MHz 说明：对于2.4G频段，自动频宽功能为20/40MHz频宽自适应；对于5G频段，自动频宽功能对应为20/40/80/160MHz频宽自适应\"},\"radio7\":{\"type\":\"string\",\"description\":\"频段，枚举取值： 2.4G 5G 5G-2 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"enable7\":{\"type\":\"string\",\"description\":\"是否启用，1为启用，0为禁用\"},\"ssid7\":{\"type\":\"string\",\"description\":\"SSID名称\"},\"securityMode7\":{\"type\":\"string\",\"description\":\"认证加密模式，枚举取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\"},\"encrypt7\":{\"type\":\"number\",\"description\":\"加密算法，枚举取值： 0：None: 1：AES 2：TKIP 3：AES+TKIP 4、AES + SAE\"},\"pwd7\":{\"type\":\"string\",\"description\":\"WiFi密钥，WEP Key或Pre-shared Key，管理平台不应存储和显示此参数。\"},\"maxAssociateNum7\":{\"type\":\"number\",\"description\":\"该SSID最大允许接入的用户数，0表示不限制\"},\"ssidAdvertisementEnabled7\":{\"type\":\"number\",\"description\":\"是否广播SSID，1为广播，0为不广播\"},\"ssidStandard7\":{\"type\":\"string\",\"description\":\"支持的无线传输标准，取值 802.11a 802.11b 802.11b/g 802.11b/g/n 802.11a/n/ac 802.11b/g/n/ax 802.11a/n/ac/ax 802.11a/n 802.11n 802.11g 802.11g/n等\"},\"ssidTimeout7\":{\"type\":\"number\",\"description\":\"启动时间   0 表示无限制 其他数值以小时为单位的设置时间 -1为关闭\"},\"timeout\":{\"type\":\"number\",\"description\":\"设置wifi超时时间，单位秒\"}},\"description\":\"响应数据\",\"required\":[\"timeout\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "uid": 368, "add_time": 1689303491, "up_time": 1708505232, "__v": 0, "req_body_other": "{\"type\":\"object\",\"properties\":{}}", "req_body_type": "json"}, {"query_path": {"path": "/api/configWorkMode", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 5, "tag": [], "_id": 12749, "method": "POST", "catid": 2187, "title": "1.6 配置上网设置页(异步)", "path": "/api/configWorkMode", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65e59594f183d27416209137", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "65e59594f183d27416209136", "name": "authorToken", "value": "", "example": "", "desc": ""}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_other": "{\"type\":\"object\",\"properties\":{\"workingMode\":{\"type\":\"number\",\"description\":\"AP工作模式： 0：桥接，默认 1：自动IP路由 2：中继    3：静态IP，4：pppoe路由\"},\"pppoeUser\":{\"type\":\"string\",\"description\":\"pppoe拨号的账号\"},\"pppoePassword\":{\"type\":\"string\",\"description\":\"pppoe拨号的密码\"},\"relaySsid\":{\"type\":\"string\",\"description\":\"中继模式接入的SSID名称\"},\"relayPassword\":{\"type\":\"string\",\"description\":\"中继模式接入的SSID的密码\"},\"securityMode\":{\"type\":\"string\",\"description\":\"中继模式认证加密模式，None, WEP-64, WEP-128, WPA-Personal, WPA2-Personal, MIXED-WPAPSK2, WPA3-SAE, MIXED-WPA2WPA3\"},\"radio\":{\"type\":\"string\",\"description\":\"中继模式接入的SSID的频段\"},\"ip\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\" 4段\"},\"description\":\"静态IP模式需要配置的IP地址\"},\"netmask\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"4段\"},\"description\":\"静态IP需要设置的掩码\"},\"gateway\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"4段\"},\"description\":\"静态IP需要设置的网关\"},\"dnsSetEnable\":{\"type\":\"number\",\"description\":\"是否采用默认dns  0：默认   1：自定义\"},\"dnsList\":{\"type\":\"array\",\"items\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"4段IP段\"}},\"description\":\"自定义dns地址列表\"}}}", "req_body_type": "json", "uid": 368, "add_time": 1690166914, "up_time": 1709544852, "__v": 0}, {"query_path": {"path": "/api/getWorkMode", "params": []}, "edit_uid": 368, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 6, "tag": [], "_id": 12754, "method": "GET", "catid": 2187, "title": "1.7 获取上网设置页", "path": "/api/getWorkMode", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65e5956cf183d27416209133", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "65e5956cf183d27416209132", "name": "authorToken", "value": "", "example": "", "desc": ""}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值\"},\"data\":{\"type\":\"object\",\"properties\":{\"workingMode\":{\"type\":\"number\",\"description\":\"AP工作模式： 0：桥接，默认 1：自动IP路由 2：中继  3：静态IP  4：pppoe路由\"},\"pppoeUser\":{\"type\":\"string\",\"description\":\"pppoe拨号的账号\"},\"pppoePassword\":{\"type\":\"string\",\"description\":\"pppoe拨号的密码\"},\"relaySsid\":{\"type\":\"string\",\"description\":\"中继模式接入的SSID名称\"},\"relayPassword\":{\"type\":\"string\",\"description\":\"中继模式接入的SSID的密码\"},\"securityMode\":{\"type\":\"string\",\"description\":\"中继模式认证加密模式，None, WEP-64, WEP-128, WPA-Personal, WPA2-Personal, MIXED-WPAPSK2, WPA3-SAE, MIXED-WPA2WPA3\\t\"},\"radio\":{\"type\":\"string\",\"description\":\"中继模式接入的SSID的频段\"},\"ip\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"4段\"},\"description\":\"静态IP模式需要配置的IP地址\"},\"netmask\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"4段\"},\"description\":\"静态IP需要设置的掩码\"},\"gateway\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"4段\"},\"description\":\"静态IP需要设置的网关\"},\"dnsSetEnable\":{\"type\":\"number\",\"description\":\"是否采用默认dns  0：默认   1：自定义\"},\"dnsList\":{\"type\":\"array\",\"items\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"4段IP段\"}},\"description\":\"自定义dns地址列表\"}},\"required\":[\"field_17\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_other": "{\"type\":\"object\",\"properties\":{}}", "req_body_type": "json", "uid": 368, "add_time": 1690167577, "up_time": 1709544812, "__v": 0}, {"query_path": {"path": "/api/getSetUpdateStatus", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 7, "tag": [], "_id": 12759, "method": "GET", "catid": 2187, "title": "1.8 获取设置完成状态", "path": "/api/getSetUpdateStatus", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "64dd8afcf183d2741620810a", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "64dd8afcf183d27416208109", "name": "authorToken", "value": "", "example": "", "desc": ""}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_other": "{\"type\":\"object\",\"properties\":{}}", "req_body_type": "json", "uid": 368, "add_time": 1690181003, "up_time": 1692240636, "__v": 0}]}, {"index": 0, "name": "一二期需求相关1", "desc": null, "add_time": 1691460248, "up_time": 1692240766, "list": [{"query_path": {"path": "/api/pingTest", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 13084, "method": "POST", "catid": 2223, "title": "1.10 ping诊断", "path": "/api/pingTest", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b783ef183d274162087d1", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b783ef183d274162087d0", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"pingHost\":{\"type\":\"string\",\"description\":\"Ping 诊断测试目标服务器IP或域名\"},\"pingSize\":{\"type\":\"number\",\"description\":\"Ping 发包字节大小（默认32）\"},\"pingCount\":{\"type\":\"number\",\"description\":\"Ping发包数（默认4次）\"}},\"required\":[\"pingHost\",\"pingSize\",\"pingCount\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"result\":{\"type\":\"string\"},\"delay\":{\"type\":\"string\",\"description\":\"平均延时时间  \"}},\"required\":[\"result\",\"delay\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1692086166, "up_time": 1698396222, "__v": 0}, {"query_path": {"path": "/api/traceRouteTest", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 13089, "method": "POST", "catid": 2223, "title": "1.11 traceroute诊断", "path": "/api/traceRouteTest", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7845f183d274162087d3", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b7845f183d274162087d2", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"tracerouteHost\":{\"type\":\"string\",\"description\":\"Traceroute目标IP或者域名\"},\"maxhops\":{\"type\":\"number\",\"description\":\"路由诊断目标最大跃点数\"},\"timeout\":{\"type\":\"number\",\"description\":\"等待回复的超时时间，单位ms(毫秒)\"},\"protocol\":{\"type\":\"string\",\"description\":\"协议\"}},\"required\":[\"tracerouteHost\",\"maxhops\",\"timeout\",\"protocol\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"result\":{\"type\":\"string\"}},\"required\":[\"result\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1692086169, "up_time": 1698396229, "__v": 0}, {"query_path": {"path": "/api/getDeviceBasicInfo", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 13104, "method": "GET", "catid": 2223, "title": "1.12 获取设备基本信息", "path": "/api/getDeviceBasicInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\"},\"respCont\":{\"type\":\"string\"},\"data\":{\"type\":\"object\",\"properties\":{\"productToken\":{\"type\":\"string\",\"description\":\"设备在开发者门户注册的产品类型Token\"},\"deviceType\":{\"type\":\"string\",\"description\":\"家庭终端的产品类型，数字家庭合作伙伴门户申请\"},\" deviceName\":{\"type\":\"string\",\"description\":\"设备信号  用文字描述  \"},\"manufacturerName\":{\"type\":\"string\",\"description\":\"厂家名称   用文字描述\"},\"deviceVendor\":{\"type\":\"string\",\"description\":\"智能组网终端的产品型号编码，数字家庭合作伙伴门户申请\"},\"deviceModel\":{\"type\":\"string\",\"description\":\"智能组网终端的产品型号编码，数字家庭合作伙伴门户申请\"},\"deviceSn\":{\"type\":\"string\",\"description\":\"智能组网终端的序列号，序列号格式要求见《中国移动家庭智能组网终端技术规范》\"},\"ponSn\":{\"type\":\"string\",\"description\":\"PON序列号，光网关终端必选，按网关技术规范要求生成\"},\"cmei\":{\"type\":\"string\",\"description\":\"智能组网终端唯一标识\"},\"softwareVersion\":{\"type\":\"string\",\"description\":\"智能组网固件版本\"},\"hardwareVersion\":{\"type\":\"string\",\"description\":\"智能组网硬件版本\"},\"ahsapdVersion\":{\"type\":\"string\",\"description\":\"\\tahsapd版本\"},\"authId\":{\"type\":\"string\",\"description\":\"设备认证唯一标识，数字家庭合作伙伴门户申请,一机一密必选\"},\"devKey\":{\"type\":\"string\",\"description\":\"设备密钥，一机一密必选\"},\"dmKey\":{\"type\":\"string\",\"description\":\"终端公司key(可选,预留接口)\"},\"powerSupplyMode\":{\"type\":\"number\",\"description\":\"供电方式（枚举值）： 1：电池供电 2：POE供电 3: 市电供电 4：USB供电 5：其它供电\"},\"provinceCode\":{\"type\":\"string\",\"description\":\"设备预置省份码\"},\"captureEth\":{\"type\":\"string\",\"description\":\"设备网口名，用于数据抓包监测\"},\"deviceMac\":{\"type\":\"string\",\"description\":\"智能组网终端Mac地址，格式大写去掉冒号\"},\"ipAddress\":{\"type\":\"string\",\"description\":\"智能组网终端ip，非ip终端则为空\"},\"ipv6IPAddress\":{\"type\":\"string\",\"description\":\"智能组网终端IPv6地址\"},\"meshType\":{\"type\":\"number\",\"description\":\"\\t0：非mesh终端 1：easyMesh， 2：其它mesh\"},\"meshRole\":{\"type\":\"number\",\"description\":\"\\t0：非mesh终端 1：controller 2：agent 3：AC 4:  AP\"},\"uplinkType\":{\"type\":\"string\",\"description\":\"上行接入方式：WLAN、Ethernet、PLC、Cable、Optical Ethernet、GPON、XGPON、XGSPON、PoE\"},\"workingMode\":{\"type\":\"number\",\"description\":\"\\t设备工作模式， 0: 桥接模式 1：路由模式 2：中继模式\"},\"radio5\":{\"type\":\"number\",\"description\":\"\\t支持频段： 0：单频 1：双频 2：三频 3：无WI-FI终端\"},\"pppoeUser\":{\"type\":\"string\",\"description\":\"\\tpppoe宽带账号,pppoe拨号是必选，非拨号时值为空\"},\"wifiSpecification\":{\"type\":\"number\",\"description\":\"\\twifi的无线规格采用枚举值 1：AC1200（空间流：2.4G：2 x 2， 5G： 2 x 2） 2：AC1900（空间流：2.4G：3 x 3， 5G： 3 x 3） 3：AC2100 （空间流：2.4G：2 x 2， 5G： 4 x4） 4：AC2100 （空间流：2.4G：2 x 2， 5G： 2 x2） 5：AC2600 （空间流：2.4G：4 x 4， 5G： 4 x4） 6：AX1500 （空间流：2.4G：2 x 2， 5G： 2 x2） 7：AX1800 （空间流：2.4G：2 x 2， 5G： 2 x2） 8：AX3000 （空间流：2.4G：2 x 2， 5G： 2 x2） 9:  AX3000 （空间流：2.4G：2 x 2， 5G:4 x 4） 10: AX3600 （空间流：2.4G：4 x 4， 5G： 4 x 4） 11: AX5400 （空间流：2.4G：2 x 2， 5G： 4 x4） 12: AX6000（空间流：2.4G：4 x 4， 5G： 4 x4）\"},\"maxBandwidth5G\":{\"type\":\"number\",\"description\":\"1：80MHZ，2：160MHZ，3：240MHZ\"},\"wifiProtocol24G\":{\"type\":\"number\",\"description\":\"当前设备支持的最高wfi协议-2.4G   1：wifi4    3：wifi6  4：wifi7\\t\"},\"wifiProtocol5G\":{\"type\":\"number\",\"description\":\"当前设备支持的最高wfi协议-5G     1：wifi4    2：wifi5   3：wifi6  4：wifi7\"},\"uniwebVersion\":{\"type\":\"string\",\"description\":\"统一web版本号\"}},\"required\":[\"uniwebVersion\"]}}}", "uid": 368, "add_time": 1692183367, "up_time": 1703648777, "__v": 0}, {"query_path": {"path": "/api/getStaInfo", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 12839, "method": "GET", "catid": 2223, "title": "1.9 获取下挂设备列表信息", "path": "/api/getStaInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65e05129f183d274162090ea", "name": "authorToken", "value": ""}, {"required": "1", "_id": "65e05129f183d274162090e9", "name": "Content-Type", "value": "application/json", "example": "", "desc": ""}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"staDevices\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"radio\":{\"type\":\"string\",\"description\":\"WLAN下挂设备的接入频段，取值2.4G、5G、5G-2、MLO(2 + 5)、MLO(5.1 + 5.8)，对非WLAN下挂设备应填空值；该字段在下挂设备数非零时必选 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"accessTime\":{\"type\":\"string\",\"description\":\"下挂设备接入时间，时间格式：2021-01-07 16:50:30 \"},\"upTime\":{\"type\":\"string\",\"description\":\"已连接时间，单位：S(秒)；该字段在下挂设备数非零时必选\"},\"staType\":{\"type\":\"string\",\"description\":\"下挂设备类型（PC、Phone、TV、Router、IoT、IPC、others）；该字段在下挂设备数非零时必选\"},\"vmacAddress\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"6段\"},\"description\":\"下挂设备在智能组网终端下的虚拟MAC，如果未转换，则和真实MAC一致。格式为不带冒号且大写；该字段在下挂设备数非零时必选\"},\"macAddress\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"6段\"},\"description\":\"下挂设备MAC地址，格式为不带冒号且大写； 该字段在下挂设备数非零时必选\"},\"staVendor\":{\"type\":\"string\",\"description\":\"下挂设备归属厂商信息\"},\"hostName\":{\"type\":\"string\",\"description\":\"下挂设备的HostName；该字段在下挂设备数非零时必选\"},\"staIPv6IPAddress\":{\"type\":\"string\",\"description\":\"下挂设备的IPV6地址；该字段在下挂设备数非零，且支持IPv6时必选\"},\"ipAddress\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"4段\"},\"description\":\"下挂设备的IPAddress；该字段在下挂设备数非零时必选\"},\"totalBytesSent\":{\"type\":\"number\",\"description\":\"下挂设备总发送字节数\"},\"txRate_rt\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"1、实时发送速率(Mbps) ； 该字段在下挂设备数非零时必选；2、当采用新增MLO两种连接方式时，下挂设备当前速率上行显示两个数值，下行显示两个数值。其中对于连接类型为MLO（2+5），第一项为2.4GHz频段的上下行速率，第二项为5GHz频段的上下行速率；对于连接类型为MLO（5.1****），第一项为5.1GHz频段的上下行速率，第二项为5.8GHz频段的上下行速率。\"},\"rxRate_rt\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"1、实时接收速率(Mbps) ； 该字段在下挂设备数非零时必选；2、当采用新增MLO两种连接方式时，下挂设备当前速率上行显示两个数值，下行显示两个数值。其中对于连接类型为MLO（2+5），第一项为2.4GHz频段的上下行速率，第二项为5GHz频段的上下行速率；对于连接类型为MLO（5.1****），第一项为5.1GHz频段的上下行速率，第二项为5.8GHz频段的上下行速率。\"},\"txRate\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"1、下挂设备协商发送速率；该字段在下挂设备数非零时必选；2、当采用新增MLO两种连接方式时，下挂设备协商速率上行显示两个数值，下行显示两个数值。其中对于连接类型为MLO（2+5），第一项为2.4GHz频段的上下行速率，第二项为5GHz频段的上下行速率；对于连接类型为MLO（5.1****），第一项为5.1GHz频段的上下行速率，第二项为5.8GHz频段的上下行速率。\"},\"rxRate\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"1、下挂设备协商接收速率；该字段在下挂设备数非零时必选；2、当采用新增MLO两种连接方式时，下挂设备协商速率上行显示两个数值，下行显示两个数值。其中对于连接类型为MLO（2+5），第一项为2.4GHz频段的上下行速率，第二项为5GHz频段的上下行速率；对于连接类型为MLO（5.1****），第一项为5.1GHz频段的上下行速率，第二项为5.8GHz频段的上下行速率。\"},\"channel\":{\"type\":\"string\",\"description\":\"WLAN下挂设备当前的工作信道，单对非WLAN下挂设备应填空值；该字段在下挂设备数非零时必选\"},\"rssi\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"1、WLAN下挂设备当前的信号强度，对非WLAN下挂设备应填空值；该字段在下挂设备数非零时必选  2、当采用新增MLO两种连接方式时，无线信号强度显示两个信号强度。对于连接类型为MLO（2+5），第一项为2.4GHz频段的信号强度，第二项为5GHz频段的信号强度；对于连接类型为MLO（5.1****），第一项为5.1GHz频段的信号强度，第二项为5.8GHz频段的信号强度\"},\"ssid\":{\"type\":\"string\",\"description\":\"WLAN下挂设备的接入SSID名称，非WLAN下挂设备应填空值； 该字段在下挂设备数非零时必选\"},\"totalPacketsReceived\":{\"type\":\"number\",\"description\":\"下挂设备总接收包数\"},\"totalPacketsSent\":{\"type\":\"number\",\"description\":\"下挂设备总发送包数\"},\"totalBytesReceived\":{\"type\":\"number\",\"description\":\"下挂设备总接收字节数\"},\"internetWorking\":{\"type\":\"number\",\"description\":\"是否允许联网\"},\"loginDevice\":{\"type\":\"number\",\"description\":\"是否本机登陆\"}},\"required\":[\"radio\",\"accessTime\",\"upTime\",\"staType\",\"vmacAddress\",\"macAddress\",\"hostName\",\"staIPv6IPAddress\",\"ipAddress\",\"totalBytesSent\",\"txRate_rt\",\"rxRate_rt\",\"txRate\",\"rxRate\",\"channel\",\"rssi\",\"ssid\",\"totalPacketsReceived\",\"totalPacketsSent\",\"totalBytesReceived\",\"internetWorking\",\"loginDevice\"]},\"description\":\"连接的下挂设备信息数组，下挂设备数非零时必选\"}},\"required\":[\"staDevices\"]}},\"required\":[\"respCont\",\"respCode\",\"data\"]}", "uid": 368, "add_time": 1691460222, "up_time": 1709199657, "__v": 0}, {"query_path": {"path": "/api/configDeviceInternetWorking", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 1, "tag": [], "_id": 12969, "method": "POST", "catid": 2223, "title": "1.13 配置下挂设备是否允许联网", "path": "/api/configDeviceInternetWorking", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65e050fcf183d274162090e8", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "65e050fcf183d274162090e7", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCont\",\"respCode\"]}", "uid": 368, "add_time": 1691551710, "up_time": 1709199612, "__v": 0, "req_body_type": "json", "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"deviceMac\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"6段\"},\"description\":\"设备MAC信息\"},\"internetWorking\":{\"type\":\"number\",\"description\":\"是否允许联网\"}},\"required\":[\"deviceMac\",\"internetWorking\"]}"}, {"query_path": {"path": "/api/getDeviceStatus", "params": []}, "edit_uid": 368, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 2, "tag": [], "_id": 12844, "method": "GET", "catid": 2223, "title": "1.14 获取路由器状态", "path": "/api/getDeviceStatus", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65d5c20ff183d27416209069", "name": "authorToken", "value": ""}, {"required": "1", "_id": "65d5c20ff183d27416209068", "name": "Content-Type", "value": "application/json", "example": "", "desc": ""}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"networkStatus\":{\"type\":\"number\",\"description\":\"联网状态： 1 联网   0  未联网  -1 待更新\"},\"networkStatusDescription\":{\"type\":\"string\",\"description\":\"联网状态文字展示\"},\"workingMode\":{\"type\":\"number\",\"description\":\"当前工作模式： 0：桥接，默认 1：自动IP路由 2：中继    4：pppoe路由\"},\"wifiConnectStatus\":{\"type\":\"number\",\"description\":\"是否有无线设备连接\"},\"wireConnectStatus\":{\"type\":\"number\",\"description\":\"是否有有线线设备连接\"},\"compatibilityStatus\":{\"type\":\"number\",\"description\":\"兼容模式状态\"},\"healthModeStatus\":{\"type\":\"number\",\"description\":\"健康模式状态\"},\"mloStatus\":{\"type\":\"number\",\"description\":\"MLO模式状态\"}},\"required\":[\"networkStatus\",\"workingMode\",\"wifiConnectStatus\",\"wireConnectStatus\",\"compatibilityStatus\",\"healthModeStatus\",\"networkStatusDescription\",\"mloStatus\"],\"description\":\"响应数据\"}},\"required\":[\"respCode\",\"data\",\"respCont\"]}", "uid": 368, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/getHealthModeInfo", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 3, "tag": [], "_id": 12854, "method": "GET", "catid": 2223, "title": "1.15 获取健康模式配置信息", "path": "/api/getHealthModeInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b785af183d274162087d9", "name": "authorToken", "value": ""}, {"required": "1", "_id": "653b785af183d274162087d8", "name": "Content-Type", "value": "application/json", "example": "", "desc": ""}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"tasks\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"taskId\":{\"type\":\"number\",\"description\":\"任务Id, 值为0 时为新增任务，ahsapd自己生成新的非0 taskId值\"},\"taskName\":{\"type\":\"string\",\"description\":\"任务名称\"},\"timeOffset\":{\"type\":\"string\",\"description\":\"HH:MM 这个格式传输  健康模式开启时间\"},\"week\":{\"type\":\"array\",\"items\":{\"type\":\"integer\",\"description\":\"[1,2,3]表示每周一二三重复，空数组表示不重复\"}},\"enable\":{\"type\":\"number\",\"description\":\"任务是否启用0：不启用， 1：启用\"},\"timeOffset2\":{\"type\":\"string\",\"description\":\"关闭健康模时间    当TimeOffset2小于TimeOffset时，TimeOffset2代表相对于第二天\"}},\"required\":[\"taskId\",\"timeOffset\",\"week\",\"enable\",\"timeOffset2\",\"taskName\"]}}},\"required\":[\"tasks\"],\"description\":\"响应数据\"}},\"required\":[\"respCode\",\"data\",\"respCont\"]}", "uid": 368, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/configHealthModeTask", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 4, "tag": [], "_id": 12859, "method": "POST", "catid": 2223, "title": "1.16 配置健康模式任务", "path": "/api/configHealthModeTask", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7860f183d274162087db", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b7860f183d274162087da", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"taskId\":{\"type\":\"number\",\"description\":\"任务Id, 值为0 时为新增任务，其他为当前任务的taskId值，\"},\"taskName\":{\"type\":\"string\",\"description\":\"任务名称\"},\"timeOffset\":{\"type\":\"string\",\"description\":\"HH:MM  这个格式传输  健康模式开启时间\"},\"week\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"[1,2,3]表示每周一二三重复，空数组表示不重复\"}},\"enable\":{\"type\":\"number\",\"description\":\"任务是否启用0：不启用， 1：启用\"},\"timeOffset2\":{\"type\":\"string\",\"description\":\"关闭健康模时间    当TimeOffset2小于TimeOffset时，TimeOffset2代表相对于第二天\"}},\"required\":[\"taskId\",\"taskName\",\"timeOffset\",\"week\",\"enable\",\"timeOffset2\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"taskId\":{\"type\":\"number\",\"description\":\"配置完成的任务id\"}},\"required\":[\"taskId\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": **********, "up_time": **********, "__v": 0}, {"query_path": {"path": "/api/deleteHealthModeTask", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 5, "tag": [], "_id": 12864, "method": "POST", "catid": 2223, "title": "1.17 删除健康模式任务", "path": "/api/deleteHealthModeTask", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7865f183d274162087dd", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b7865f183d274162087dc", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"taskId\":{\"type\":\"number\",\"description\":\"任务Id, 值为0 时为新增任务，其他为当前任务的taskId值，\"},\"taskName\":{\"type\":\"string\",\"description\":\"任务名称\"}},\"required\":[\"taskId\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691478534, "up_time": 1698396261, "__v": 0}, {"query_path": {"path": "/api/getStaticIPInfo", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 6, "tag": [], "_id": 12869, "method": "GET", "catid": 2223, "title": "1.18 获取静态IP绑定设备信息", "path": "/api/getStaticIPInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "659375fcf183d27416208cfb", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "659375fcf183d27416208cfa", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"staDevices\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"macAddress\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"6段\"},\"description\":\"下挂设备MAC地址，格式为不带冒号且大写； 该字段在下挂设备数非零时必选\"},\"deviceName\":{\"type\":\"string\",\"description\":\"下挂设备的deviceName\"},\"ipAddress\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"4段\"},\"description\":\"下挂设备的IPAddress\"},\"loginDevice\":{\"type\":\"number\",\"description\":\"是否本机\"},\"onlineStatus\":{\"type\":\"number\",\"description\":\"是否在线\"},\"timeout\":{\"type\":\"number\",\"description\":\"配置静态ip地址绑定超时时间，单位秒\"}},\"required\":[\"macAddress\",\"deviceName\",\"ipAddress\",\"loginDevice\",\"onlineStatus\",\"timeout\"]},\"description\":\"连接的下挂设备信息数组，下挂设备数非零时必选\"}},\"required\":[\"staDevices\"]}},\"required\":[\"respCont\",\"respCode\",\"data\"]}", "uid": 368, "add_time": 1691479574, "up_time": 1704162812, "__v": 0}, {"query_path": {"path": "/api/configStaticIP", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 7, "tag": [], "_id": 12874, "method": "POST", "catid": 2223, "title": "1.19 配置静态IP", "path": "/api/configStaticIP", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7870f183d274162087e1", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b7870f183d274162087e0", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"macAddress\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"6段\"},\"description\":\"下挂设备MAC地址，格式为不带冒号且大写； 该字段在下挂设备数非零时必选\"},\"deviceName\":{\"type\":\"string\",\"description\":\"下挂设备的deviceName\"},\"ipAddress\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"4段\"},\"description\":\"下挂设备的IPAddress\"}},\"required\":[\"macAddress\",\"deviceName\",\"ipAddress\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691479801, "up_time": 1698396272, "__v": 0}, {"query_path": {"path": "/api/deleteStaticIP", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 8, "tag": [], "_id": 12879, "method": "POST", "catid": 2223, "title": "1.20 删除静态IP", "path": "/api/deleteStaticIP", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7876f183d274162087e3", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b7876f183d274162087e2", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"macAddress\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"6段\"},\"description\":\"下挂设备MAC地址，格式为不带冒号且大写； 该字段在下挂设备数非零时必选\"}},\"required\":[\"macAddress\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691479843, "up_time": 1698396278, "__v": 0}, {"query_path": {"path": "/api/updateLoginPassword", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 9, "tag": [], "_id": 12884, "method": "POST", "catid": 2223, "title": "1.21 修改登录密码", "path": "/api/updateLoginPassword", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b787cf183d274162087e5", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b787cf183d274162087e4", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"currentPassword\":{\"type\":\"string\",\"description\":\"当前密码\"},\"newPassword\":{\"type\":\"string\",\"description\":\"新密码\"}},\"required\":[\"currentPassword\",\"newPassword\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691486800, "up_time": 1698396284, "__v": 0}, {"query_path": {"path": "/api/deviceReboot", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 10, "tag": [], "_id": 12889, "method": "POST", "catid": 2223, "title": "1.22 设备一键重启", "path": "/api/deviceReboot", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7881f183d274162087e7", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b7881f183d274162087e6", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691487543, "up_time": 1698396289, "__v": 0}, {"query_path": {"path": "/api/getRebootTaskInfo", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 11, "tag": [], "_id": 12899, "method": "GET", "catid": 2223, "title": "1.23 获取重启任务配置信息", "path": "/api/getRebootTaskInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7886f183d274162087e9", "name": "authorToken", "value": ""}, {"required": "1", "_id": "653b7886f183d274162087e8", "name": "Content-Type", "value": "application/json", "example": "", "desc": ""}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"tasks\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"taskId\":{\"type\":\"number\",\"description\":\"任务Id, 值为0 时为新增任务，ahsapd自己生成新的非0 taskId值\"},\"timeOffset\":{\"type\":\"string\",\"description\":\"HH:MM 这个格式传输  重启时间\"},\"week\":{\"type\":\"array\",\"items\":{\"type\":\"integer\",\"description\":\"[1,2,3]表示每周一二三重复，空数组表示不重复\"}},\"enable\":{\"type\":\"number\",\"description\":\"任务是否启用 0：不启用， 1：启用\"}},\"required\":[\"taskId\",\"timeOffset\",\"week\",\"enable\"]}}},\"required\":[\"tasks\"],\"description\":\"响应数据\"}},\"required\":[\"respCode\",\"data\",\"respCont\"]}", "uid": 368, "add_time": 1691487641, "up_time": 1698396294, "__v": 0}, {"query_path": {"path": "/api/configRebootTask", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 12, "tag": [], "_id": 12894, "method": "POST", "catid": 2223, "title": "1.24 配置重启任务", "path": "/api/configRebootTask", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b788bf183d274162087eb", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b788bf183d274162087ea", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"taskId\":{\"type\":\"number\",\"description\":\"任务Id, 值为0 时为新增任务，其他为当前任务的taskId值，\"},\"timeOffset\":{\"type\":\"string\",\"description\":\"HH:MM:SS  这个格式传输  重启任务开启时间\"},\"data\":{\"type\":\"string\"},\"week\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"[1,2,3]表示每周一二三重复，空数组表示不重复\"},\"description\":\"重启日期\"},\"enable\":{\"type\":\"number\",\"description\":\"任务是否启用0：不启用， 1：启用\"}},\"required\":[\"taskId\",\"timeOffset\",\"week\",\"enable\",\"data\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691487636, "up_time": 1698396299, "__v": 0}, {"query_path": {"path": "/api/deleteRebootTask", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 13, "tag": [], "_id": 12904, "method": "POST", "catid": 2223, "title": "1.25 删除重启任务", "path": "/api/deleteRebootTask", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7891f183d274162087ed", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b7891f183d274162087ec", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"taskId\":{\"type\":\"number\",\"description\":\"需要删除任务的taskId值\"}},\"required\":[\"taskId\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691487938, "up_time": 1698396305, "__v": 0}, {"query_path": {"path": "/api/ledStatus", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 14, "tag": [], "_id": 12374, "method": "GET", "catid": 2223, "title": "1.26 获取组网设备led灯控", "path": "/api/ledStatus", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7895f183d274162087ef", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b7895f183d274162087ee", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"ledOnOff\":{\"type\":\"number\"}},\"required\":[\"ledOnOff\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"ledOnOff\":{\"type\":\"number\"}},\"required\":[\"ledOnOff\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1686192226, "up_time": 1698396309, "__v": 0}, {"query_path": {"path": "/api/ledControl", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 15, "tag": [], "_id": 11864, "method": "POST", "catid": 2223, "title": "1.27 设置组网设备led灯控", "path": "/api/ledControl", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b789bf183d274162087f1", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b789bf183d274162087f0", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"ledOnOff\":{\"type\":\"number\",\"description\":\"1、开   0、关\"}},\"required\":[\"ledOnOff\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1678675119, "up_time": 1698396315, "__v": 0}, {"query_path": {"path": "/file/importCfgFile", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 16, "tag": [], "_id": 12909, "method": "POST", "catid": 2223, "title": "1.28 导入配置文件接口(异步)", "path": "/file/importCfgFile", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b78a0f183d274162087f3", "name": "Content-Type", "value": "multipart/form-data"}, {"required": "1", "_id": "653b78a0f183d274162087f2", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "file", "uid": 368, "add_time": 1691488374, "up_time": 1698396320, "__v": 0}, {"query_path": {"path": "/file/exportCfgFile", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 17, "tag": [], "_id": 12914, "method": "POST", "catid": 2223, "title": "1.29 导出配置文件接口", "path": "/file/exportCfgFile", "project_id": 60, "req_params": [], "res_body_type": "raw", "req_query": [], "req_headers": [{"required": "1", "_id": "653b78a5f183d274162087f5", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b78a5f183d274162087f4", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"item\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"1:WIFI名称  2：WIFI密码  3：上网配置  4：DHCP名称和局域网设置  5  路由器名称和管理界面密码\"},\"description\":\"选项  需要配置的选项\"}},\"required\":[\"item\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "req_body_type": "json", "uid": 368, "add_time": 1691490087, "up_time": 1698396325, "__v": 0}, {"query_path": {"path": "/api/deviceReset", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 18, "tag": [], "_id": 12919, "method": "POST", "catid": 2223, "title": "1.30 恢复出厂设置", "path": "/api/deviceReset", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b78abf183d274162087f7", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b78abf183d274162087f6", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"item\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"1:WIFI名称  2：WIFI密码  3：上网配置  4：DHCP名称和局域网设置  5  路由器名称和管理界面密码\"},\"description\":\"需要配置的选项\"}}}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691490715, "up_time": 1698396331, "__v": 0}, {"query_path": {"path": "/api/onekeyTest", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 19, "tag": [], "_id": 12309, "method": "POST", "catid": 2223, "title": "1.31 一键诊断", "path": "/api/onekeyTest", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b78b0f183d274162087f9", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b78b0f183d274162087f8", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"index\":{\"type\":\"number\",\"description\":\"索引，依次获取诊断状态  从1开始\"}},\"required\":[\"index\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"timeDelay\":{\"type\":\"string\",\"description\":\"针对部分ping诊断返回的参数    可选   延时时间\"}}}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1685684024, "up_time": 1698396336, "__v": 0}, {"query_path": {"path": "/file/exportLogFileLocal", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 22, "tag": [], "_id": 12929, "method": "GET", "catid": 2223, "title": "1.32 导出日志到本地接口", "path": "/file/exportLogFileLocal", "project_id": 60, "req_params": [], "res_body_type": "raw", "req_query": [], "req_headers": [{"required": "1", "_id": "653b78b5f183d274162087fb", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b78b5f183d274162087fa", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "req_body_type": "file", "uid": 368, "add_time": 1691491717, "up_time": 1698396341, "__v": 0}, {"query_path": {"path": "/api/exportLogFilePlatform", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 23, "tag": [], "_id": 12934, "method": "GET", "catid": 2223, "title": "1.33 导出日志到平台接口", "path": "/api/exportLogFilePlatform", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b78baf183d274162087fd", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b78baf183d274162087fc", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"item1\":{\"type\":\"number\",\"description\":\"选项1\"},\"item2\":{\"type\":\"number\",\"description\":\"选项2\"},\"item3\":{\"type\":\"number\",\"description\":\"选项3\"},\"item4\":{\"type\":\"number\",\"description\":\"选项4\"},\"item5\":{\"type\":\"number\",\"description\":\"选项5\"}},\"required\":[\"item1\",\"item2\",\"item3\",\"item4\",\"item5\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"string\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691491719, "up_time": 1698396346, "__v": 0}, {"query_path": {"path": "/api/getMacFilter", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 24, "tag": [], "_id": 12939, "method": "GET", "catid": 2223, "title": "1.34 获取黑白名单", "path": "/api/getMacFilter", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b78bff183d274162087ff", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b78bff183d274162087fe", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"bandSteeringStatus\":{\"type\":\"string\",\"description\":\"双频合一开关： 0：关闭 1：打开\"},\"rssiThreshold\":{\"type\":\"string\",\"description\":\"接收信号强度阈值，当2.4G频段检测到某一下挂STA接收到的信号强度高于此阈值，将当前双频STA切换至5G频段。单位：dbm. 取值范围（-100~0）\"},\"rssiThreshold5G\":{\"type\":\"string\",\"description\":\"G接收信号强度阈值，当双频客户端首次连接或者当前工作在5G频段时，检测到某一下挂双频STA接收到的信号强度低于此阈值将下挂双频客户端切换至2.4G频段 单位：dbm. 取值范围（-100~0）\"}},\"required\":[\"bandSteeringStatus\",\"rssiThreshold5G\",\"rssiThreshold\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"macFilterEnable\":{\"type\":\"number\",\"description\":\"是否使能MAC地址过滤；1为过滤；0为不过滤\"},\"macFilterPolicy\":{\"type\":\"number\",\"description\":\"MAC地址过滤策略；0为黑名单；1为白名单\"},\"macFilterEntries\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"hostName\":{\"type\":\"string\",\"description\":\"绑定设备的HostNam\"},\"macAddress\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"6段\"},\"description\":\"绑定设备MAC地址\"},\"loginDevice\":{\"type\":\"number\",\"description\":\"是否为web登陆设备\"}},\"required\":[\"hostName\",\"macAddress\",\"loginDevice\"]},\"description\":\"黑白名单信息：包含MAC地址及设备名\"}},\"required\":[\"macFilterEnable\",\"macFilterPolicy\",\"macFilterEntries\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691492442, "up_time": 1698396351, "__v": 0}, {"query_path": {"path": "/api/configMacFilter", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 25, "tag": [], "_id": 12944, "method": "POST", "catid": 2223, "title": "1.35 配置黑白名单", "path": "/api/configMacFilter", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b78c4f183d27416208801", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b78c4f183d27416208800", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"macFilterEnable\":{\"type\":\"number\",\"description\":\"是否使能MAC地址过滤；1为过滤；0为不过滤  \"},\"macFilterPolicy\":{\"type\":\"number\",\"description\":\"MAC地址过滤策略；0为黑名单；1为白名单 \"},\"macAddress\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"6段\"},\"description\":\"绑定设备的HostName\"},\"hostName\":{\"type\":\"string\",\"description\":\"绑定设备MAC地址\"}},\"required\":[\"macFilterEnable\",\"macFilterPolicy\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691492574, "up_time": 1698396356, "__v": 0}, {"query_path": {"path": "/api/deleteMacFilter", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 26, "tag": [], "_id": 12949, "method": "POST", "catid": 2223, "title": "1.36 删除黑白名单", "path": "/api/deleteMacFilter", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b78caf183d27416208803", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b78caf183d27416208802", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"macFilterPolicy\":{\"type\":\"number\",\"description\":\"MAC地址过滤策略；0为黑名单；1为白名单 \"},\"macAddress\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"6段\"},\"description\":\"绑定设备的HostName\"},\"hostName\":{\"type\":\"string\",\"description\":\"绑定设备MAC地址\"}},\"required\":[\"macFilterPolicy\",\"macAddress\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691548224, "up_time": 1698396362, "__v": 0}, {"query_path": {"path": "/api/getDeviceLimitInfo", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 27, "tag": [], "_id": 12954, "method": "GET", "catid": 2223, "title": "1.37 获取上网保护配置信息", "path": "/api/getDeviceLimitInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b78cff183d27416208805", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b78cff183d27416208804", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"devices\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"deviceMac\":{\"type\":\"string\",\"description\":\"组网设备MAC地址或组网下挂设备MAC地址（如配置的MAC是组网设备的MAC限速对所有下挂设备有效，如配置MAC地址是下挂设备MAC限速对当前下挂设备有效；MAC地址为组网设备的配置优先级最高）\"},\"upSpeed\":{\"type\":\"string\",\"description\":\"设备上行限速(单位Kpbs)，值为正值是有效限速值，为0取消限速\"},\"downSpeed\":{\"type\":\"string\",\"description\":\"设备下行限速(单位Kpbs)，值为正值是有效限速值，为0取消限速\"},\"limitedUrl\":{\"type\":\"string\",\"description\":\"限制访问域名列表（路由模式需要支持）；多组域名之间以逗号分开，最多支持32组。示例：\\\"www.taobao.com,www.baidu.com\\\"\"},\"deviceLimitMode\":{\"type\":\"string\",\"description\":\"是否周期启用设备限速和访问限制： 0：关闭周期控制，立即执行 1：启用周期控制，按周期执行；(在DeviceLimitTimeOffset1对应时间加入速率限制，在DeviceLimitTimeOffset2对应时间解除速率限制。当DeviceLimitTimeOffset2小于DeviceLimitTimeOffset1时，DeviceLimitTimeOffset2代表相对于第二天0点的时间偏移量）\"},\"deviceLimitWeek\":{\"type\":\"string\",\"description\":\"使用int值二进制位的低7位代表星期，由高到低位依次代表星期六五四三二一日。如7(00000111B)表示周二，周一和周日。若仅需单次执行，值为0\"},\"deviceLimitTimeOffset1\":{\"type\":\"string\",\"description\":\"执行时间偏移量1（执行任务的时间点与当天凌晨0点的时间差，以秒为单位），该时刻启动速率限制；\"},\"deviceLimitTimeOffset2\":{\"type\":\"string\",\"description\":\"行时间偏移量2（执行任务的时间点与当天凌晨0点的时间差，以秒为单位），该时刻停止速率限制；\"}},\"required\":[\"deviceMac\",\"downSpeed\",\"upSpeed\"]},\"description\":\"备限速配置列表\"}},\"required\":[\"devices\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"devices\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"enable\":{\"type\":\"number\",\"description\":\"开关标志位，1为开，0为关\"},\"taskId\":{\"type\":\"number\",\"description\":\"任务id\"},\"deviceLimitType\":{\"type\":\"number\",\"description\":\"1、儿童上网保护   2、智能限速\"},\"deviceMac\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"6段\"},\"description\":\"组网下挂设备MAC地址\"},\"deviceName\":{\"type\":\"string\",\"description\":\"设备名称\"},\"limitedUrl\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"限制访问域名列表\"},\"deviceLimitMode\":{\"type\":\"number\",\"description\":\"1、时间控制   2、网站过滤\"},\"deviceLimitWeek\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"[1,2,3]表示每周一二三重复，空数组表示不重复\"}},\"deviceLimitTimeOffset1\":{\"type\":\"string\",\"description\":\"HH:MM:SS  这个格式传输  模式开启时间\"},\"deviceLimitTimeOffset2\":{\"type\":\"string\",\"description\":\"HH:MM:SS  这个格式传输  模式关闭时间\"},\"loginDevice\":{\"type\":\"number\",\"description\":\"是否为web登陆设备\"},\"upSpeed\":{\"type\":\"string\",\"description\":\"设备上行限速，值为正值是有效限速值  为0取消限速\"},\"downSpeed\":{\"type\":\"string\",\"description\":\"设备下行限速，值为正值是有效限速值  为0取消限速\"},\"downSpeedUnit\":{\"type\":\"string\",\"description\":\"下行单位  KB/S MB/S  GB/S\"},\"upSpeedUnit\":{\"type\":\"string\",\"description\":\"上行单位  KB/S MB/S   GB/S\"}},\"required\":[\"enable\",\"deviceName\",\"deviceMac\",\"deviceLimitType\",\"loginDevice\",\"taskId\"]}}},\"required\":[\"devices\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691548668, "up_time": 1698396367, "__v": 0}, {"query_path": {"path": "/api/configDeviceLimit", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 28, "tag": [], "_id": 12959, "method": "POST", "catid": 2223, "title": "1.38 配置上网保护规则", "path": "/api/configDeviceLimit", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b78d4f183d27416208807", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b78d4f183d27416208806", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"enable\":{\"type\":\"number\",\"description\":\"开关标志位，1为开，0为关\"},\"taskId\":{\"type\":\"number\",\"description\":\"需要修改的任务id  0为新增\"},\"deviceLimitType\":{\"type\":\"number\",\"description\":\"1、儿童上网保护   2、智能限速\"},\"deviceMac\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"6段\"},\"description\":\"组网下挂设备MAC地址\"},\"limitedUrl\":{\"type\":\"string\",\"description\":\"限制访问域名列表（路由模式需要支持）；多组域名之间以逗号分开，最多支持32组。示例：\\\"www.taobao.com,www.baidu.com\\\"\"},\"deviceLimitMode\":{\"type\":\"number\",\"description\":\"1、时间控制   2、网站过滤\"},\"deviceLimitWeek\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"[1,2,3]表示每周一二三重复，空数组表示不重复\"}},\"deviceLimitTimeOffset1\":{\"type\":\"string\",\"description\":\"HH:MM:SS  这个格式传输  模式开启时间\"},\"deviceLimitTimeOffset2\":{\"type\":\"string\",\"description\":\"HH:MM:SS  这个格式传输  模式关闭时间\"},\"upSpeed\":{\"type\":\"string\",\"description\":\"设备上行限速，值为正值是有效限速值  为0取消限速\"},\"downSpeed\":{\"type\":\"string\",\"description\":\"设备下行限速，值为正值是有效限速值  为0取消限速\"},\"downSpeedUnit\":{\"type\":\"string\",\"description\":\"下行单位  KB/S MB/S  GB/S\"},\"upSpeedUnit\":{\"type\":\"string\",\"description\":\"上行单位  KB/S MB/S  GB/S\"}},\"required\":[\"enable\",\"deviceLimitType\",\"deviceMac\",\"deviceLimitMode\",\"taskId\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"taskId\":{\"type\":\"number\",\"description\":\"当前配置成功的任务id\"}},\"required\":[\"taskId\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691549980, "up_time": 1698396372, "__v": 0}, {"query_path": {"path": "/api/deleteDeviceLimit", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 29, "tag": [], "_id": 12964, "method": "POST", "catid": 2223, "title": "1.39 删除上网保护规则", "path": "/api/deleteDeviceLimit", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b78d9f183d27416208809", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b78d9f183d27416208808", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"taskId\":{\"type\":\"number\",\"description\":\"需要删除的任务id\"}},\"required\":[\"taskId\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691550327, "up_time": 1698396377, "__v": 0}, {"query_path": {"path": "/api/getIPTVInfo", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 30, "tag": [], "_id": 12089, "method": "GET", "catid": 2223, "title": "1.40 获取组网设备IPTV配置信息", "path": "/api/getIPTVInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "uid": 480, "add_time": 1682069583, "up_time": 1700022349, "req_query": [], "req_headers": [{"required": "1", "_id": "6554484df183d27416208a23", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "6554484df183d27416208a22", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "__v": 0, "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"vlanEnable\":{\"type\":\"number\",\"description\":\"IPTV业务开关；1：打开，0：关闭;\"},\"vlanId\":{\"type\":\"number\",\"description\":\"IPTV业务的VLAN ID；IPTV业务开关打开时必选;\"},\"bindPort\":{\"type\":\"array\",\"items\":{\"type\":\"number\"},\"description\":\"配置的IPTV的LAN口索引    1 代表LAN1  2 代码LAN2  以此类推\"},\"portNumber\":{\"type\":\"number\",\"description\":\"当前的有线端口总数量  包含wan和lan口\"},\"wanIndex\":{\"type\":\"number\",\"description\":\"Wan口的索引，从1开始\"},\"wanAdaptive\":{\"type\":\"number\",\"description\":\"1：WAN口自适应  0：不适应\"},\"loginDeviceIndex\":{\"type\":\"number\",\"description\":\"为当前登录设备连接的网口号，0为无线连接\"},\"lanConnectPort\":{\"type\":\"array\",\"items\":{\"type\":\"number\"},\"description\":\"lan口连接上的网口号列表，没有表示都没连接\"}},\"description\":\"响应数据\",\"required\":[\"vlanEnable\",\"portNumber\",\"wanIndex\",\"wanAdaptive\",\"loginDeviceIndex\",\"lanConnectPort\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}"}, {"query_path": {"path": "/api/configIPTVInfo", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 31, "tag": [], "_id": 12094, "method": "POST", "catid": 2223, "title": "1.41 配置组网设备IPTV", "path": "/api/configIPTVInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "uid": 480, "add_time": 1682069641, "up_time": 1698396389, "req_query": [], "req_headers": [{"required": "1", "_id": "653b78e5f183d2741620880d", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "653b78e5f183d2741620880c", "name": "authorToken", "example": "", "desc": ""}], "req_body_form": [], "__v": 0, "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"vlanEnable\":{\"type\":\"number\",\"description\":\"IPTV业务开关；1：打开，0：关闭;\"},\"vlanId\":{\"type\":\"number\",\"description\":\"IPTV业务的VLAN ID；IPTV业务开关打开时必选;\"},\"bindPort\":{\"type\":\"array\",\"items\":{\"type\":\"number\"},\"description\":\"配置的IPTV的LAN口索引    1 代表LAN1  2 代码LAN2  以此类推\"}}}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json"}, {"query_path": {"path": "/api/config<PERSON>an<PERSON>ddress", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 32, "tag": [], "_id": 11374, "method": "POST", "catid": 2223, "title": "1.42 Lan侧地址信息配置(异步)", "path": "/api/config<PERSON>an<PERSON>ddress", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b78ebf183d2741620880e", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"lanIpAddress\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"[192.168,10,1]\"},\"description\":\"Lan侧的Ip地址段\"},\"lanMask\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"[255,255,255,0]\"},\"description\":\"子网掩码\"},\"lanAddressStart\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"[192.168,10,2]\"},\"description\":\"lan侧dhcp起始分配地址\"},\"lanAddressEnd\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"[192.168,10,255]\"},\"description\":\"lan侧dhcp结束分配地址\"},\"leaseTime\":{\"type\":\"number\",\"description\":\"租约时间  0:无限期   单位小时\"}},\"required\":[\"lanIpAddress\",\"lanMask\",\"lanAddressEnd\",\"lanAddressStart\",\"leaseTime\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1666080381, "up_time": 1698396395, "__v": 0}, {"query_path": {"path": "/api/getLan<PERSON>ddress", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 33, "tag": [], "_id": 13034, "method": "GET", "catid": 2223, "title": "1.43 Lan侧地址信息获取", "path": "/api/getLan<PERSON>ddress", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65937780f183d27416208cfe", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"bandSteeringStatus\":{\"type\":\"string\",\"description\":\"双频合一开关： 0：关闭 1：打开\"},\"rssiThreshold\":{\"type\":\"string\",\"description\":\"接收信号强度阈值，当2.4G频段检测到某一下挂STA接收到的信号强度高于此阈值，将当前双频STA切换至5G频段。单位：dbm. 取值范围（-100~0）\"},\"rssiThreshold5G\":{\"type\":\"string\",\"description\":\"G接收信号强度阈值，当双频客户端首次连接或者当前工作在5G频段时，检测到某一下挂双频STA接收到的信号强度低于此阈值将下挂双频客户端切换至2.4G频段 单位：dbm. 取值范围（-100~0）\"}},\"required\":[\"bandSteeringStatus\",\"rssiThreshold5G\",\"rssiThreshold\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"lanIpAddress\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"[192,168,10,1]\"},\"description\":\"Lan侧的Ip地址段\"},\"lanMask\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"[255,255,255,0]\"},\"description\":\"子网掩码\"},\"lanAddressStart\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"[192,168,10,2]\"},\"description\":\"lan侧dhcp起始分配地址\"},\"lanAddressEnd\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"description\":\"[192,168,10,255]\"},\"description\":\"lan侧dhcp结束分配地址\"},\"leaseTime\":{\"type\":\"number\",\"description\":\"租约时间  0:无限期   单位小时\"},\"timeout\":{\"type\":\"number\",\"description\":\"LAN测地址设置超时时间，单位秒\"}},\"required\":[\"lanIpAddress\",\"lanMask\",\"lanAddressStart\",\"lanAddressEnd\",\"leaseTime\",\"timeout\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1691745654, "up_time": 1704163200, "__v": 0}]}, {"index": 0, "name": "一二期需求相关2", "desc": null, "add_time": 1692081630, "up_time": 1692081630, "list": [{"query_path": {"path": "/api/deviceBasicInfo", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 0, "tag": [], "_id": 13054, "method": "POST", "catid": 2241, "title": "1.44 设备信息-基本信息", "path": "/api/deviceBasicInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "uid": 368, "add_time": 1692081729, "up_time": 1698396408, "req_query": [], "req_headers": [{"required": "1", "_id": "653b78f8f183d27416208810", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "__v": 0, "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\"},\"respCont\":{\"type\":\"string\"},\"data\":{\"type\":\"object\",\"properties\":{\"deviceName\":{\"type\":\"string\",\"description\":\"设备型号\"},\"manufacturerName\":{\"type\":\"string\",\"description\":\"厂家名称\"},\"softwareVersion\":{\"type\":\"string\",\"description\":\"软件版本\"},\"hardwareVersion\":{\"type\":\"string\",\"description\":\"硬件版本\"},\"deviceSn\":{\"type\":\"string\",\"description\":\"设备sn\"},\"cmei\":{\"type\":\"string\",\"description\":\"设备cmei\"},\"aosnetVersion\":{\"type\":\"string\",\"description\":\"aosnet插件版本\"},\"dpiVersion\":{\"type\":\"string\",\"description\":\"软探针插件版本\"},\"flashSize\":{\"type\":\"string\",\"description\":\"flash大小\"},\"ramSize\":{\"type\":\"string\",\"description\":\"ram大小\"},\"cpuOccupancyRate\":{\"type\":\"string\",\"description\":\"cpu运行使用率\"},\"ramOccupancyRate\":{\"type\":\"string\",\"description\":\"ram运行使用率\"},\"runningTime\":{\"type\":\"string\",\"description\":\"持续运行时间\"},\"provinceCode\":{\"type\":\"string\",\"description\":\"省份编码\"},\"ledOnOff \":{\"type\":\"string\",\"description\":\"LED状态\"},\"lockStatus\":{\"type\":\"string\",\"description\":\"黑白名单状态\"}}}}}", "req_body_type": "json", "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}"}, {"query_path": {"path": "/api/deviceWanStatus", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 1, "tag": [], "_id": 13059, "method": "POST", "catid": 2241, "title": "1.45 设备信息-WAN状态", "path": "/api/deviceWanStatus", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65dc31aef183d2741620908b", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\"},\"respCont\":{\"type\":\"string\"},\"data\":{\"type\":\"object\",\"properties\":{\"workingMode\":{\"type\":\"string\",\"description\":\"工作模式\"},\"wanMode\":{\"type\":\"string\",\"description\":\"WAN接入方式\"},\"internetStatus\":{\"type\":\"string\",\"description\":\"联网状态\"},\"rssi\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"1、连接信号强度 2、当连接频段为MLO（2+5）、MLO(5.1 + 5.8)，连接信号强度展示两个数值对于连接类型为MLO（2+5），第一项为2.4GHz频段的信号强度，第二项为5GHz频段的信号强度；对于连接类型为MLO（5.1****），第一项为5.1GHz频段的信号强度，第二项为5.8GHz频段的信号强度\"},\"rssiLevel\":{\"type\":\"array\",\"items\":{\"type\":\"number\"},\"description\":\"1、连接信号强度等级  2、当连接频段为MLO（2+5）、MLO(5.1 + 5.8)，连接信号强度展示两个数值对于连接类型为MLO（2+5），第一项为2.4GHz频段的信号强度，第二项为5GHz频段的信号强度；对于连接类型为MLO（5.1****），第一项为5.1GHz频段的信号强度，第二项为5.8GHz频段的信号强度\"},\"radio\":{\"type\":\"string\",\"description\":\"WLAN下挂设备的接入频段，取值2.4G、5G、5G-2、MLO(2 + 5)、MLO(5.1 + 5.8)，对非WLAN下挂设备应填空值；该字段在下挂设备数非零时必选 注：对于三频设备，5G表示5.8G频段，5G-2表示5.2G频段；双频设备忽略5G-2频段；\"},\"rxRate\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"1、发送协商速率 2、当连接频段为MLO（2+5）、MLO(5.1 + 5.8)，发送协商速率展示两个数值。对于连接类型为MLO（2+5），第一项为2.4GHz频段的发送协商速率，第二项为5GHz频段的发送协商速率；对于连接类型为MLO（5.1****），第一项为5.1GHz频段的发送协商速率，第二项为5.8GHz频段的发送协商速率\"},\"txRate\":{\"type\":\"string\",\"description\":\"1、接收协商速率 2、当连接频段为MLO（2+5）、MLO(5.1 + 5.8)，接收协商速率展示两个数值对于连接类型为MLO（2+5），第一项为2.4GHz频段的接收协商速率，第二项为5GHz频段的接收协商速率；对于连接类型为MLO（5.1****），第一项为5.1GHz频段的接收协商速率，第二项为5.8GHz频段的接收协商速率\"},\"ipv6IPAddress\":{\"type\":\"string\",\"description\":\"IPV6地址\"},\"localIPAddress\":{\"type\":\"string\",\"description\":\"内网IPV4地址\"},\"publicIpAddress\":{\"type\":\"string\",\"description\":\"公网IPV4地址\"},\"doubleStack\":{\"type\":\"string\",\"description\":\"IPV4/IPV6双栈状态\"}},\"required\":[\"doubleStack\"]}}}", "req_body_type": "json", "uid": 368, "add_time": 1692082377, "up_time": 1708929454, "__v": 0, "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}"}, {"query_path": {"path": "/api/deviceLANStatus", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 2, "tag": [], "_id": 13064, "method": "POST", "catid": 2241, "title": "1.46设备信息-LAN状态", "path": "/api/deviceLANStatus", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65dd5024f183d274162090c6", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\"},\"respCont\":{\"type\":\"string\"},\"data\":{\"type\":\"object\",\"properties\":{\"lanInfoList\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"index\":{\"type\":\"string\",\"description\":\"LAN口索引\"},\"deviceName\":{\"type\":\"string\",\"description\":\"设备名称\"},\"portMac\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"设备mac\"},\"portConnection\":{\"type\":\"string\",\"description\":\"连接状态\"},\"portIP\":{\"type\":\"string\",\"description\":\"IP\"},\"protIPV6\":{\"type\":\"string\",\"description\":\"IPV6\"},\"portRate\":{\"type\":\"string\",\"description\":\"端口协商速率\"}},\"required\":[\"portRate\"]}}},\"required\":[]}}}", "req_body_type": "json", "uid": 368, "add_time": 1692084374, "up_time": 1709002788, "__v": 0, "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}"}, {"query_path": {"path": "/api/deviceWiFiStatus", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 3, "tag": [], "_id": 13069, "method": "POST", "catid": 2241, "title": "1.47 设备信息-wifi状态", "path": "/api/deviceWiFiStatus", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65e05173f183d274162090eb", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\"},\"respCont\":{\"type\":\"string\"},\"data\":{\"type\":\"object\",\"properties\":{\"wifiList\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"radio\":{\"type\":\"string\"},\"ssid\":{\"type\":\"string\"},\"wlanMac\":{\"type\":\"string\"},\"enable\":{\"type\":\"string\"},\"internetWorking\":{\"type\":\"string\"}},\"required\":[]}}}}}}", "req_body_type": "json", "uid": 368, "add_time": 1692084672, "up_time": 1709199731, "__v": 0, "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}"}, {"query_path": {"path": "/api/deviceStatsInfo", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 4, "tag": [], "_id": 13119, "method": "POST", "catid": 2241, "title": "1.48 设备信息-报文统计", "path": "/api/deviceStatsInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b790df183d27416208814", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\"},\"respCont\":{\"type\":\"string\"},\"data\":{\"type\":\"object\",\"properties\":{\"totalBytesSent\":{\"type\":\"string\",\"description\":\"总发送字节数\"},\"totalBytesReceived\":{\"type\":\"string\",\"description\":\"总接口字节数\"},\"totalPacketsSent\":{\"type\":\"string\",\"description\":\"总发送包数\"},\"totalPacketsReceived\":{\"type\":\"string\",\"description\":\"总接收包数\"},\"errorsPackets\":{\"type\":\"string\",\"description\":\"错误包数\"},\"discardPackets\":{\"type\":\"string\",\"description\":\"丢失包数\"}},\"required\":[]}}}", "req_body_type": "json", "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "uid": 368, "add_time": 1692237429, "up_time": 1698396429, "__v": 0}, {"query_path": {"path": "/api/upgradeDeviceList", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 5, "tag": [], "_id": 13094, "method": "POST", "catid": 2241, "title": "1.49 获取升级设备列表", "path": "/api/upgradeDeviceList", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65937699f183d27416208cfc", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\"},\"respCont\":{\"type\":\"string\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"deviceList\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"deviceName\":{\"type\":\"string\",\"description\":\"设备型号\"},\"manufacturerName\":{\"type\":\"string\",\"description\":\"厂家名称\"},\"softwareVersion\":{\"type\":\"string\",\"description\":\"当前软件版本\"},\"deviceType\":{\"type\":\"string\",\"description\":\"设备类型   1：主路由   2：从路由\"}},\"required\":[\"deviceName\",\"softwareVersion\",\"deviceType\",\"manufacturerName\"]}},\"timeout\":{\"type\":\"number\",\"description\":\"版本升级任务超时时间，单位秒\"}},\"required\":[\"deviceList\",\"timeout\"]}}}}", "req_body_type": "json", "uid": 368, "add_time": 1692086757, "up_time": 1704162969, "__v": 0, "req_body_other": ""}, {"query_path": {"path": "/file/firmwareUpload", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 6, "tag": [], "_id": 13074, "method": "POST", "catid": 2241, "title": "1.50 设备升级_升级文件上传", "path": "/file/firmwareUpload", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7917f183d27416208816", "name": "Content-Type", "value": "multipart/form-data"}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "file", "req_body_other": "firmware.bin", "uid": 368, "add_time": 1692085519, "up_time": 1698396439, "__v": 0}, {"query_path": {"path": "/api/upgradeStatus", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 7, "tag": [], "_id": 13099, "method": "POST", "catid": 2241, "title": "1.51 获取升级状态", "path": "/api/upgradeStatus", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b791df183d27416208817", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"1：固件下载中   2：固件下载完成   3：版本校验成功   4：升级中  -1：固件下载失败  -2：固件校验失败   -3：升级失败\"},\"respCont\":{\"type\":\"string\"}},\"required\":[\"respCode\"]}", "req_body_type": "json", "req_body_other": "", "uid": 368, "add_time": 1692088585, "up_time": 1698396445, "__v": 0}, {"query_path": {"path": "/api/getNTPTime", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 8, "tag": [], "_id": 13114, "method": "POST", "catid": 2241, "title": "1.52 获取路由器当前时间", "path": "/api/getNTPTime", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7922f183d27416208818", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\"},\"respCont\":{\"type\":\"string\"},\"data\":{\"type\":\"object\",\"properties\":{\"nowTime\":{\"type\":\"string\",\"description\":\"当前时间\"},\"nowTimeZone\":{\"type\":\"number\",\"description\":\"当前时区  东区为+   西区为-  0为格林尼治\"}},\"required\":[]}}}", "req_body_type": "json", "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "uid": 368, "add_time": 1692235642, "up_time": 1698396450, "__v": 0}, {"query_path": {"path": "/api/configNTPTime", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 9, "tag": [], "_id": 13109, "method": "POST", "catid": 2241, "title": "1.53 同步路由器网络时间接口", "path": "/api/configNTPTime", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7928f183d27416208819", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\"},\"respCont\":{\"type\":\"string\"}}}", "req_body_type": "json", "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"nowTimeZone\":{\"type\":\"string\",\"description\":\"当前时区  东区为+   西区为-  0为格林尼治\"}},\"required\":[\"nowTimeZone\"]}", "uid": 368, "add_time": 1692235566, "up_time": 1698396456, "__v": 0}, {"query_path": {"path": "/api/getDNSList", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 10, "tag": [], "_id": 13124, "method": "POST", "catid": 2241, "title": "1.54  获取DNS地址列表", "path": "/api/getDNSList", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b792cf183d2741620881a", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\"},\"respCont\":{\"type\":\"string\"},\"data\":{\"type\":\"object\",\"properties\":{\"dnsList\":{\"type\":\"array\",\"items\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"4段IP段\"}},\"description\":\"dns地址列表\"},\"dnsSetEnable\":{\"type\":\"number\"}},\"required\":[\"dnsSetEnable\"]}}}", "req_body_type": "json", "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "uid": 368, "add_time": 1692244614, "up_time": 1698396460, "__v": 0}, {"query_path": {"path": "/api/configDNSAddress", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 11, "tag": [], "_id": 13129, "method": "POST", "catid": 2241, "title": "1.55  配置DNS地址", "path": "/api/configDNSAddress", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7931f183d2741620881b", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\"},\"respCont\":{\"type\":\"string\"},\"data\":{\"type\":\"object\",\"properties\":{\"dnsList\":{\"type\":\"array\",\"items\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"4段IP段\"}},\"description\":\"dns地址列表\"},\"dnsSetEnable\":{\"type\":\"number\"}},\"required\":[\"dnsSetEnable\"]}},\"required\":[\"respCode\"]}", "req_body_type": "json", "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"dnsList\":{\"type\":\"array\",\"items\":{\"type\":\"array\",\"items\":{\"type\":\"number\"},\"description\":\"IP地址  4段 \"},\"description\":\"打开需有值   关闭可选   \"},\"dnsSetEnable\":{\"type\":\"number\"}},\"required\":[\"dnsList\",\"dnsSetEnable\"]}", "uid": 368, "add_time": 1692244802, "up_time": 1698396465, "__v": 0}, {"query_path": {"path": "/api/getMeshTopology", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 12, "tag": [], "_id": 13139, "method": "POST", "catid": 2241, "title": "1.56 获取mesh拓扑结构", "path": "/api/getMeshTopology", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65dc336ff183d2741620908e", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"deviceName\":{\"type\":\"string\",\"description\":\"设备名称\"},\"deviceMac\":{\"type\":\"string\",\"description\":\"设备mac\"},\"bhType\":{\"type\":\"string\",\"description\":\"上联连接方式  有线连接  2.4GWiFI 、5GWIFI 回传、MLO（2+5）、MLO(5.1 + 5.8)\"},\"ssid\":{\"type\":\"string\",\"description\":\"ssid名称，上联WLAN接入的SSID;bhType 为WLAN时必选；\"},\"pwd\":{\"type\":\"string\",\"description\":\"ssid密码，上联WLAN接入的密码，bhType为WLAN时必选；\"},\"securityMode\":{\"type\":\"string\",\"description\":\"认证加密模式，bhType为WLAN时必选，枚举取值： None WEP-64 WEP-128 WPA-Personal WPA2-Personal MIXED-WPAPSK2 WPA3-SAE MIXED-WPA2WPA3\"},\"rxRate\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"协商接收速率，单位Mbps ；当拓扑连接为MLO模式，发送协商速率展示两个数值。对于连接类型为MLO（2+5），第一项为2.4GHz频段的发送协商速率，第二项为5GHz频段的发送协商速率；对于连接类型为MLO（5.1****），第一项为5.1GHz频段的发送协商速率，第二项为5.8GHz频段的发送协商速率\"},\"txRate\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"协商发送速率，单位Mbps ；当拓扑连接为MLO模式，发送协商速率展示两个数值。对于连接类型为MLO（2+5），第一项为2.4GHz频段的发送协商速率，第二项为5GHz频段的发送协商速率；对于连接类型为MLO（5.1****），第一项为5.1GHz频段的发送协商速率，第二项为5.8GHz频段的发送协商速率\"},\"meshRole\":{\"type\":\"number\",\"description\":\"Mesh角色 0：非mesh终端 1: Controller 2: Agent 3: AC 4: AP 5: Auto(自动选择角色) 注：当读取meshRole时，值范围为0 - 4，单配置meshRole 是值范围为1-5；非mesh终端不需要配置mesh角色；\"},\"deviceType\":{\"type\":\"number\",\"description\":\"0:主路由；1：从路由;  2：下挂设备；\"},\"staType\":{\"type\":\"string\",\"description\":\"下挂设备类型（PC、Phone、TV、Router、IoT、IPC、others）\"},\"upTime\":{\"type\":\"number\",\"description\":\"在线时长，单位秒\"},\"rssi\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"1设备信号强度  2、当连接频段为MLO（2+5）、MLO(5.1 + 5.8)，连接信号强度展示两个数值对于连接类型为MLO（2+5），第一项为2.4GHz频段的信号强度，第二项为5GHz频段的信号强度；对于连接类型为MLO（5.1****），第一项为5.1GHz频段的信号强度，第二项为5.8GHz频段的信号强度\"},\"ipAddress\":{\"type\":\"string\",\"description\":\"拓扑设备IP地址\"},\"ipv6IPAddress\":{\"type\":\"string\",\"description\":\"拓扑设备IPV6 地址\"},\"deviceVendor\":{\"type\":\"string\",\"description\":\"拓扑设备厂商编码\"},\"deviceMode\":{\"type\":\"string\",\"description\":\"拓扑设备型号编码\"},\"meshStatus\":{\"type\":\"number\",\"description\":\"组网设备是否加入mesh组网 1：是 0：否\"},\"staNumber\":{\"type\":\"number\",\"description\":\"下挂设备数量\"},\"chilldrenType\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"下级设备连接方式 上联连接方式  有线连接  2.4GWiFI 、5GWIFI 回传等\"},\"description\":\"下级设备连接方式\"},\"children\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{}},\"description\":\"数组对象同上级data\"}},\"required\":[\"ipAddress\",\"upTime\",\"staNumber\",\"meshStatus\",\"children\",\"deviceType\",\"deviceName\",\"deviceMac\",\"rssi\",\"chilldrenType\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1692580507, "up_time": 1708929903, "__v": 0}, {"query_path": {"path": "/api/configMeshRole", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 13, "tag": [], "_id": 13144, "method": "POST", "catid": 2241, "title": "1.57 配置mesh角色", "path": "/api/configMeshRole", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "6589358cf183d27416208cb8", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"meshRole\":{\"type\":\"number\",\"description\":\"1：control   2：agent    meshMode为手动为必选\"},\"meshEnable\":{\"type\":\"number\",\"description\":\"0：关  1：开  Mesh 开关\"},\"meshMode\":{\"type\":\"number\",\"description\":\"1：手动  2 ：自动\"}},\"required\":[\"meshEnable\",\"meshMode\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1692580785, "up_time": 1703490956, "__v": 0}, {"query_path": {"path": "/api/getMeshInfo", "params": []}, "edit_uid": 368, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 14, "tag": [], "_id": 13149, "method": "POST", "catid": 2241, "title": "1.58 获取mesh状态信息", "path": "/api/getMeshInfo", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "659666ecf183d27416208d04", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"meshEnable\":{\"type\":\"number\",\"description\":\"0：关  1：开  Mesh 开关\"},\"meshRole\":{\"type\":\"number\",\"description\":\"1: Controller 2: Agent \"},\"meshMode\":{\"type\":\"number\",\"description\":\"1:手动   2：自动\"}},\"required\":[\"meshEnable\",\"meshRole\",\"meshMode\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1692580956, "up_time": 1704355564, "__v": 0}, {"query_path": {"path": "/api/configDMZ", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 15, "tag": [], "_id": 13169, "method": "POST", "catid": 2241, "title": "1.59 设置DMZ", "path": "/api/configDMZ", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7946f183d2741620881f", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"dmzEnable\":{\"type\":\"number\"},\"ipAddress\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"description\":\"4段\"}}},\"required\":[\"dmzEnable\",\"ipAddress\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1692606835, "up_time": 1698396486, "__v": 0}, {"query_path": {"path": "/api/getDMZ", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 16, "tag": [], "_id": 13174, "method": "POST", "catid": 2241, "title": "1.60 获取DMZ", "path": "/api/getDMZ", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b794bf183d27416208820", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"dmzEnable\":{\"type\":\"string\",\"description\":\"DMZ开关\"},\"ipAddress\":{\"type\":\"array\",\"items\":{\"type\":\"string\"}}},\"required\":[\"dmzEnable\",\"ipAddress\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1692606836, "up_time": 1698396491, "__v": 0}, {"query_path": {"path": "/api/configUPnP", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 17, "tag": [], "_id": 13179, "method": "POST", "catid": 2241, "title": "1.61 设置UPnP", "path": "/api/configUPnP", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7950f183d27416208821", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"upnpEnable\":{\"type\":\"number\"}},\"required\":[\"upnpEnable\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1692607207, "up_time": 1698396496, "__v": 0}, {"query_path": {"path": "/api/getUPnP", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 18, "tag": [], "_id": 13184, "method": "POST", "catid": 2241, "title": "1.62 获取UPnP", "path": "/api/getUPnP", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b7955f183d27416208822", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"upnpList\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"ipAddress\":{\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"protocol\":{\"type\":\"string\"},\"srcPort\":{\"type\":\"number\"},\"dstPort\":{\"type\":\"number\"},\"upnpDesc\":{\"type\":\"string\"}},\"required\":[\"ipAddress\",\"protocol\",\"srcPort\",\"dstPort\",\"upnpDesc\"]}},\"upnpEnable\":{\"type\":\"number\"}},\"required\":[\"upnpList\",\"upnpEnable\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1692607209, "up_time": 1698396501, "__v": 0}, {"query_path": {"path": "/api/getIOTChannel", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 19, "tag": [], "_id": 13204, "method": "POST", "catid": 2241, "title": "1.64 获取iot专属通道信息", "path": "/api/getIOTChannel", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "653b795cf183d27416208823", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"deviceName\":{\"type\":\"string\"},\"deviceMac\":{\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"deviceIp\":{\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"deviceStatus\":{\"type\":\"number\",\"description\":\"0：离线  1：在线\"}},\"required\":[\"deviceName\",\"deviceMac\",\"deviceIp\",\"deviceStatus\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1692668793, "up_time": 1698396508, "__v": 0}, {"query_path": {"path": "/api/logout", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 20, "tag": [], "_id": 14094, "method": "POST", "catid": 2241, "title": "1.67 web登入退出", "path": "/api/logout", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "655d93f5f183d27416208a9d", "name": "Content-Type", "value": "application/json"}, {"required": "1", "_id": "655d93f5f183d27416208a9c", "name": "authorToken", "value": "", "example": "", "desc": ""}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"deviceName\":{\"type\":\"string\"},\"deviceMac\":{\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"deviceIp\":{\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"deviceStatus\":{\"type\":\"number\",\"description\":\"0：离线  1：在线\"}},\"required\":[\"deviceName\",\"deviceMac\",\"deviceIp\",\"deviceStatus\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1700631338, "up_time": 1700631541, "__v": 0}, {"query_path": {"path": "/api/getUniwebVersion", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 21, "tag": [], "_id": 14279, "method": "POST", "catid": 2241, "title": "1.68 获取统一web版本号", "path": "/api/getUniwebVersion", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "658cd33ef183d27416208ce5", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "markdown": "", "desc": "", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"respCode\":{\"type\":\"number\"},\"respCont\":{\"type\":\"string\"},\"data\":{\"type\":\"object\",\"properties\":{\"uniwebVersion\":{\"type\":\"string\",\"description\":\"统一web版本号\"}},\"required\":[]}}}", "req_body_type": "json", "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "uid": 368, "add_time": 1703727818, "up_time": 1703727934, "__v": 0}, {"query_path": {"path": "/api/configWps", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 22, "tag": [], "_id": 14639, "method": "POST", "catid": 2241, "title": "1.69 配置wps", "path": "/api/configWps", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65d5ba54f183d2741620905c", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"radio\":{\"type\":\"string\",\"description\":\"可选，当前默认可不发送，默认不发送为触发全部频段。组网设备WPS触发频段包含2.4G 和5G\"}},\"required\":[]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1708505458, "up_time": 1708505684, "__v": 0}, {"query_path": {"path": "/api/configStaDisconnectRssi", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 23, "tag": [], "_id": 14644, "method": "POST", "catid": 2241, "title": "1.70 配置下挂设备阈值", "path": "/api/configStaDisconnectRssi", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65d5bbb9f183d27416209067", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"rssi\":{\"type\":\"number\",\"description\":\"阈值填写范围[-85，-75]，单位dBm，默认填写-78,\"},\"enable\":{\"type\":\"number\",\"description\":\"使能状态  1：使能    0：非使能\"}},\"required\":[\"enable\",\"rssi\"]}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1708505723, "up_time": 1708506041, "__v": 0}, {"query_path": {"path": "/api/getStaDisconnectRssi", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 24, "tag": [], "_id": 14649, "method": "POST", "catid": 2241, "title": "1.71 获取下挂设备阈值", "path": "/api/getStaDisconnectRssi", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65d5bbaef183d27416209066", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"},\"data\":{\"type\":\"object\",\"properties\":{\"enable\":{\"type\":\"number\",\"description\":\"使能状态  1：使能    0：非使能\"},\"rssi\":{\"type\":\"number\",\"description\":\"阈值填写范围[-85，-75]，单位dBm，默认填写-78\"}},\"required\":[\"enable\",\"rssi\"]}},\"required\":[\"respCode\",\"respCont\",\"data\"]}", "req_body_type": "json", "uid": 368, "add_time": 1708505932, "up_time": 1708506030, "__v": 0}, {"query_path": {"path": "/api/heartBeat", "params": []}, "edit_uid": 368, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": true, "index": 25, "tag": [], "_id": 14754, "method": "POST", "catid": 2241, "title": "1.72 心跳保活", "path": "/api/heartBeat", "project_id": 60, "req_params": [], "res_body_type": "json", "req_query": [], "req_headers": [{"required": "1", "_id": "65e59f94f183d27416209139", "name": "Content-Type", "value": "application/json"}], "req_body_form": [], "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{}}", "markdown": "", "desc": "", "res_body": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"respCode\":{\"type\":\"number\",\"description\":\"响应码， 0为成功，其他见附录A\"},\"respCont\":{\"type\":\"string\",\"description\":\"接口调用失败时的返回值；\"}},\"required\":[\"respCode\",\"respCont\"]}", "req_body_type": "json", "uid": 368, "add_time": 1709547198, "up_time": 1709547412, "__v": 0}]}]