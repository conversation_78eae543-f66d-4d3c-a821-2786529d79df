## 厂家调试

```

ubus call ahsapd.config addItem '{"item":"if6_pronvCode","value": "GXI"}'
```



```
ubus call ahsapd.config getItem '{"item":"if6_pronvCode"}'
```


```
ubus call ahsapd.config addItem '{"item":"if6_pronvCode","value":"GXI"}'
```


完成中间件框架的XX完善，实现XX


```
ubus call ahsapd.control  firmwareUpgrade  '{ "seqId": "123231423", "fileType": 1, "localPath": "/tmp/1.img", "reboot": 1 }'
```


### 华为
fttr设备
telnet，默认账号密码root/

```
Hw8@cMcc
```


telnet 在web页面，web管理员登录，web用户名：CMCCAdmin
密码：aDm8H%MdA
安全->广域网访问配人们置 里ONT访问控制配置打开telnet，默认账号密码root/Hw8@cMcc
3.aosnet插件动态库可以替换。aosnet插件路径，telnet进去 输入shell->su，在mnt/jfss2/app/plugins/work/andlink/MyPlugin

1、每次重启设备，或者退出root用户再进root，需要重新设置环境变量

```
export PATH=/mnt/jffs2/app/plugins/work/andlink/MyPlugin/bin:/mnt/jffs2/app/plugins/work/andlink/MyPlugin/bin/aosnet:${PATH}
export LD_LIBRARY_PATH=/mnt/jffs2/app/plugins/work/andlink/MyPlugin/Lib:/mnt/jffs2/app/plugins/work/andlink/MyPlugin/Lib/aosnetlib:${LD_LIBRARY_PATH}
```


```
/mnt/jffs2/app/plugins/work/andlink/MyPlugin/uniwebPlugin_V1.0.1/web
```

### ZTE
账号密码都是root  root


```
dTX6Z%bf
```


ZXHN  E2633 样机调试说明
Telnet已开放， 用户名和密码均为root
说明：
1、版本Mesh设置如下状态，“副路由”确保设备上电后不自动启动controller进程。当设置为“主路由”角色，设备上电后自动启动集成的controller。
 ![[Pasted image 20231220150416.png]]

2、版本集成的插件在/bin路径下，调试插件可以放在/tmpapp路径下，tftp client 工具可以拷贝插件到设备上。
3、手工启动插件的方法：
./bin/controller_plugin.sh &
1. ./tmpapp/controller -m -d 0 -v ZTE &
4、镜像方法（以LAN3口监听为例）：——掉电不保存
mirror add veth1 eth3    
mirror del veth1 eth3
mirror show
iqqq
### 小米
先进入web管理页面
然后 把 路径后面的 替换为 /api/misystem/set_telnet?enable=1
telnet是root ，管理密码 ********




### TP
当前几个问题

1. Download=/tmp/aosnet    该文件未默认创建
2. wifi设置成功或者失败  未有任何响应    异步通知也无响应
3. 恢复出厂后/bin/tmpapp文件夹下内容丢失



```
4xnc3u7u
```


登陆页面 [路由器lan ip]:8080，（密码见标贴），按F12进入控制台，输入以下

```
$.modify({"custom_system":{"telnet":{"enable":"1"}}}, res=>{console.log(res)})
```

然后就可以telnet了，进去后用户名是root，密码是  t
```
tdmpwma302v4
```




打开telnet
账号
    然后 把 路径后面的 替换为 /api/misystem/set_telnet?enable=1
root  
telnet是root ，管理密码


```
goash
PfujOXhSJgs22rbc4kHkQgo2X3PKJ8pNqbSSJjELi/oEtyGEsATsbrYRJEbFKACX7SJollBoVAJ9KvqlDzweQspQnKXEfa5a+4u0Rs98YtRfrsJRx3t0eWJWIGsDcRP7NqATGC6BtUjTpFQNY3FFFljSGA8FkO2OxnXec5D4OYM=
```



### 终端公司  长虹
telnet 账号密码
   账号密码 ：CMCCAdmin/wE%AnmFq

web

wifi7登陆密码
user
vu5j5x3d

user

```
kp7qjf6g
```


串口

root   Au7Q%Mq5H



### 终端公司  长虹   wifi7


进入容器：fwk.sh console
退出容器：Ctrl+a q
/etc/init.d/andlink stop
/bin/aosnet/start.sh -D &


WEB  登陆账户密码      

```
user   
```


```
vu5j5x3d
```



### 360

    telnet账号密码  root  ********

### 烽火

web  登入密码

用户名    username

```
s5mc3ed#
```

### 烽火wifi7


```
http://************:8080/telnet?enable=1&key=E8B3EF4DAA70
```

=后面是设备Mac地址

```
cfg_cmd get InternetGatewayDevice.DeviceInfo.X_FH_Account.X_FH_WebUserInfo.WebPassword
```

web密码

```
ac77m#s7
```


B2558F

这个命令可以查web登录密码，登录用户名是username


admin
nE7jA%5m
su
Fh@ED326F





mac  E8B3EF645EF0

通过隐藏URL开启telnet
`http://************/telnet?enable=1&key=E8B3EF645EF0 ` (MAC地址去掉 : 分割符)
admin   hg2x0645EF0   (hg2x0 + mac 地址后六位)
su –   (普通账户切换到 root 账户，“-” 表示切换到root账户默认的环境变量环境)
f1ber@dm!n645EF0    (f1ber@dm!n + mac 地址后六位)

串口账号密码
开启串口权限以后，账号 root
密码f1ber@dm!n

```

iptables -D CHAIN_SERVICE -i br0 -p tcp --dport 23 -j ACCEPT >/dev/null 2>&1
```

```
iptables -I CHAIN_SERVICE -i br0 -p tcp --dport 23 -j ACCEPT >/dev/null 2>&1
```

```
/fhrom/bin/telnetd -L 23 -P /var/tel_passwd >/dev/null 2>&1
```

## 代码故障

2024-03-21
uniwebPlugin_ubus.module需要crypto.so库支持




ubox的定时器设置需要放在uloop_done()后面才会生效


## 前端


前端无法打开 是存在环境 需要浏览器清除下缓存

### 1015

1、"ssifTimeout3": "4"     wifi设置  字段出错

## 编译问题

### 1129

/mine/AOS-NET/toolchain/uniweb/tp2/msdk-10.3.0-mips-EB-5.10-g2.30-m32s-210630/bin/../lib/gcc/mips-linux-gnu/10.3.0/../../../../mips-linux-gnu/bin/ld: CMakeFiles/uniweb.dir/src/auth.c.o: in function `uh_auth_check': auth.c:(.text.uh_auth_check+0x2fc): undefined reference to `crypt'

需要將交叉编译链中的libcrypt.so 软连接到cmake的set(CMAKE_FIND_ROOT_PATH ${TOOLCHAIN_DIR}/third ${CMAKE_INSTALL_PREFIX} )路径下

### 1108

出现无法编译  编译链出错 查找下 是否缺少zlib库

### 1101

软链接出现级联现象解决方案

如果是文件夹的软链接
光F是不够的会创建到软链接的目标文件夹中
需要加上-T
ln -sfT xxxx xxxxx

错误：

```
(shell ln -sf {TOPDIR}/test/ahsapd/json /tmp/json)
```

正确：

```
(shell ln -sfT {TOPDIR}/test/ahsapd/json/ /tmp/json)
```

### 1031

make[3]: *** 没有规则可制作目标“/usr/lib/libcrypt.so”，由“uniweb” 需求。 停止。

### 1027

#### 问题1：在makefile中调用bash脚本，出现cmake配置出现异常

```
$ make release_uniweb
cd /mine/AOS-NET/AOS-NET_master/Network/uniweb; bash ./cross-uniweb.sh huawei XXX ARM /mine/AOS-NET/toolchain/uniweb/huawei /mine/AOS-NET/toolchain/uniweb/huawei/arm-mix510-linux arm-mix510-linux-
-- The C compiler identification is GNU 10.3.0
-- The CXX compiler identification is GNU 10.3.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - failed
-- Check for working C compiler: /mine/AOS-NET/toolchain/uniweb/huawei/arm-mix510-linux/bin/arm-mix510-linux-gcc
-- Check for working C compiler: /mine/AOS-NET/toolchain/uniweb/huawei/arm-mix510-linux/bin/arm-mix510-linux-gcc - broken
```

查看CMakeError.log文件

```
Linking C executable cmTC_c6d5d/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c6d5d.dir/link.txt --verbose=1/mine/AOS-NET/toolchain/uniweb/huawei/arm-mix510-linux/bin/arm-mix510-linux-gcc -s -O2 -Wno-system-headers -pthread -ldl -rdynamic -funwind-tables -ffunction-sections  -DSEC_MODULE  -fPIC -Wl,-z,relro,-z,now,-z,noexecstack,-s -I/mine/AOS-NET/toolchain/uniweb/huawei/include -I/mine/AOS-NET/toolchain/uniweb/huawei/include -DMGPLUGIN_VERSION=\"V2.2.1\" -DAHS_VERSION=\"AOS-NET_V2.2.1\" -DDPIPLUGIN_VERSION=\"V2.2.1\" -DCSIPLUGIN_VERSION=\"V2.2.1\"  -Wl,-s,-z,relro,-z,now,-z,noexecstack -L/mine/AOS-NET/toolchain/uniweb/huawei/lib  -lssl -lcrypto -L/mine/AOS-NET/toolchain/uniweb/huawei/lib -lz  CMakeFiles/cmTC_c6d5d.dir/testCCompiler.c.o -o cmTC_c6d5d/mine/AOS-NET/toolchain/uniweb/huawei/arm-mix510-linux/host_bin/../lib/gcc/arm-linux-musleabi/10.3.0/../../../../arm-linux-musleabi/bin/ld: cannot find -lzcollect2: error: ld returned 1 exit statusgmake[2]: *** [CMakeFiles/cmTC_c6d5d.dir/build.make:99: cmTC_c6d5d] Error1gmake[2]: Leaving directory '/mine/AOS-NET/AOS-NET_master/Network/uniweb/build/huawei-XXX-ARM/CMakeFiles/CMakeTmp'gmake[1]: *** [Makefile:127: cmTC_c6d5d/fast] Error2gmake[1]: Leaving directory '/mine/AOS-NET/AOS-NET_master/Network/uniweb/build/huawei-XXX-ARM/CMakeFiles/CMakeTmp'
```

判断为没有相应的库文件在环境变量的中，重新编译相关的库文件即可

### 1015

1、x86环境下，ubox在静态编译的时候，uniweb_ubus.so编译出现问题

~~解决方案：  在uniweb_ubus的cmakelists.txt文件下  增加
	add_compile_options(-fPIC)~~
需要在总的cmakelists.txt文件中增加
**`<font color=#ed1c24>`addcompileoptions(-fPIC)`</font>`**

### 0925

#### 1、需要32位的交叉编译环境支持

解决方案

```
sudo apt-get install libc6:i386  
sudo apt-get install lib32ncurses5  
sudo apt-get install lib32z1  
sudo apt-get install lib32stdc++6

```

#### 2、json-c库相关函数需要更新

```
json_object_object_get  函数需要更新到  json_object_object_get_ext 
```

#### 3、部分环境变量出现问题

终端公司的设备  编译链做了限制    需要将交叉编译链根文件目录设置为usr

```
TOOLCHAIN_DIR=/mine/AOS-NET/toolchain/uniweb/zhongduan
TOOLCHAIN_NAME=usr
TOOLCHAIN_PREFIX=arm-buildroot-linux-gnueabi-
```

#### 4、ubuntu  编译 交叉编译 找不到 libmpfr.so.4问题

```
ln -s /交叉编译路径/lib/libmpfr.so.4   /usr/lib/x86_64-linux-gnu/libmpfr.so.4
```

## 需求问题记录

1、当前dns设置界面暂无关闭手动dns功能

## 厂家反馈问题

### 华为故障问题反馈

|                |                                                              |            |                |
| :------------- | :----------------------------------------------------------- | :--------- | :------------- |
| **编号** | **问题**                                               | 修复状态   | **状态** |
| 1              | web密码登录校验方案，现网通过scram算法存储密码，密码无法还原 |            | Open           |
| 2              | 页面没有防重复攻击的机制，可以通过工具POST提交修改路由器参数 |            | Open           |
| 3              | 页面数据没有加密，可以通过get查询到所以WiFi的密码信息        |            | Open           |
| 4              | http://************:8080/api/1234，插件异常退出              |            | Open           |
| 5              | 页面隐私填充方案未确定                                       |            | Open           |
| 6              | 健康模式，添加规则失败，原因是下发的自动与规范接口不一致     | 自测已修复 | Open           |
| 7              | web登录密码修改成功后，未生效，还是老密码                    | 自测已修复 | Open           |
| 8              | 版本升级过程中插件异常退出                                   |            | Open           |
| 9              | 定时重启认为添加失败                                         | 自测已修复 | Open           |
| 10             | DNS配置后无法关闭或删除                                      |            | Open           |
| 11             | 黑白名单规则添加失败                                         | 自测已修复 | Open           |
| 12             | Topo查询页面插件异常推出                                     |            | Open           |
| 13             | 儿童上网保护页面跳转到空白页面                               |            | Open           |
| 14             | IPTV页面点击开关，未生效                                     |            | Open           |
| 15             | 设备管理wan状态部分字段未显示                                |            | Open           |
| 16             | 设备管理lan状态部分字段未显示                                |            | Open           |
| 17             | 访客WiFI启用时长，修改后，配置未保存                         |            | Open           |
|                |                                                              |            |                |

### 小米故障问题反馈

|      |                        |                                                                                         |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| :--- | :--------------------- | :-------------------------------------------------------------------------------------- | :------------- | :------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 序号 |                        | 描述                                                                                    | 自测状态       | 结果                                               |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| 1    | 登录                   | 首次打开页面，跳转到登录页面，输入用户名user，密码为bdata管理密码，确认是否可以正常登录 |                | OK                                                 |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| 2    | 上网状态               | 路由模式，拔掉wan口，查看首页上网状态                                                   |                | NOK                                                | 仍然显示正常上网                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| 3    | wifi列表               | 进入首页，查看wifi列表，显示是否正常                                                    |                | OK                                                 |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| 4    | 下挂设备               | 进入首页，查看下挂设备显示是否正常                                                      |                | OK                                                 |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| 5    | WiFi设置               | 双频合一开关测试                                                                        |                | OK                                                 |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 修改双频合一ssid和密码 | OK                                                                                      |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 修改2.4g 5g 密码       | OK                                                                                      |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 修改2.4g 5g ssid       | OK                                                                                      |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 开关2.4G               | OK                                                                                      |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 访客网络               | 开关2.4g访问网络                                                                        |                | OK                                                 | 1 有ap，可关联，但是不分配ipv4，手机无法上网 （出现一次，重新关联后，正常）`<br>`2 打开2.4g时，5G访客保持关闭状态，启用时长要求选择                                                                                                                                                                                                                                                                                                                                  |
|      | 开关5g访客网络         | OK                                                                                      |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 健康模式               |                                                                                         |                | Not tested                                         |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 工作模式               | 路由模式切桥模式                                                                        | 自测已修复     | NOK                                                | 弹出UBUS接口设置失败 `<br>`ubus接口需要修改适配，cli/andlink.c setWorkingMode()                                                                                                                                                                                                                                                                                                                                                                                      |
|      | 桥模式切路由模式       | OK                                                                                      |                | 切换成功，但是显示，保存超时，（需要增加超时时间） |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 切PPPOE                | No tested                                                                               |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 切中继模式             | No tested                                                                               |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 设备信息               | 基本信息显示                                                                            | 厂家未增加参数 | NOK                                                | 不显示设备厂商和设备型号                                                                                                                                                                                                                                                                                                                                                                                                                                               |
|      | WAN状态                | NOK                                                                                     |                | 不显示联网状态、内网IPv4地址、公网IPv4地址         |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | LAN状态                | NOK                                                                                     |                | 有线状态不对                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 无线状态               | OK                                                                                      |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 报文统计               | OK                                                                                      |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 账号管理               | 修改密码                                                                                |                | OK                                                 |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 版本升级               | 本地升级                                                                                |                | NOK                                                | 固件上传失败                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
|      | 恢复设置               | 导出配置文件                                                                            |                | NOK                                                | 导出文件内容为null                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
|      | 导入配置文件           | No tested                                                                               |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 恢复出厂设置           | OK                                                                                      |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 诊断工具               | 一键诊断                                                                                |                | NOK                                                | 拔掉WAN口，执行一键诊断，上下行连接和DNS诊断返回正常，ping诊断返回异常                                                                                                                                                                                                                                                                                                                                                                                                 |
|      | 设备日志               | 导出日志                                                                                |                | OK                                                 | 可以导出，但是日志内容太少                                                                                                                                                                                                                                                                                                                                                                                                                                             |
|      | 一键上传               | No tested                                                                               |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 指示灯开关             | 开关指示灯                                                                              |                | OK                                                 |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 网络时间               | 时间显示                                                                                |                | OK                                                 |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 同步网络时间           | NOK                                                                                     |                | 崩了，isp-dp未适配ntpCalibration                   |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 重启管理               | 一键重启                                                                                |                | OK                                                 |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 定时重启               | No tested                                                                               |                |                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | DNS配置                |                                                                                         |                | NOK                                                | 崩了                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
|      | 黑白名单               |                                                                                         |                | Not test                                           |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 拓扑查询               |                                                                                         |                | NOK                                                | 崩了                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
|      | Mesh配置               |                                                                                         |                | Not test                                           |                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|      | 儿童上网保护           |                                                                                         |                | NOK                                                | 崩了                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
|      | IPTV设置               | 打开IPTV                                                                                |                | NOK                                                | E/uniweb   [2023-10-13 17:25:44] [ubus] ahsapi_ubus_invoke_handler fail ret:2  methed:`<setIptvVlan>`  request_msg:<{  `<br>`        "vlanEnable": 1,  `<br>`        "vlanId": null,  `<br>`        "iptvStatus": [  `<br>`  `<br>`        ]  `<br>`}>  `<br>`I/uniweb   [2023-10-13 17:25:44] (uh_ubus_call)[http] configIPTVInfo response msg:{  `<br>`  "respCode":-998,  `<br>`  "respCont":"输入参数异常" |

### TP故障问题反馈

#### 20231026

1. 较严重问题
   1.1 WiFi设置页面中，5G的无线频宽也显示的是“20/40MHz自适应”，应该为“20/40/80/160MHz频宽自适应”，同时缺少160MHz选项
   1.2 WiFi设置-健康模式功能，下发的指令index值为0，会对所有SSID生效
   1.3 设备管理-设备信息-基本信息页面中，ubus接口上报runningTime为4544秒，页面上显示运行时长为12天24分钟（应该为12小时）1.4 版本升级：当前固件上传后，插件未下发指令
   1.5 设备管理-恢复设置-导出配置文件，目前发现选择导出WiFi密码时调用的ubus接口是ahsapd.uplink getUplinkInfo；上网配置调用ahsapd.port getLanInfo,DHCP名称和局域网设置调用ahsapd.basic getBasicInfo，这样是否有些串位？
   1.6 设备管理-恢复设置-导入配置文件：目前该功能好像未实现
   1.7 设备管理-恢复设置-恢复出厂：保留重要配置和全部出厂的效果是一样的
   1.8 设备管理-DNS配置：规范中说明preDnsServer为首选DNS地址，但实际上preDnsServer为备选服务器
   **1.9 高级设置-黑/白名单设置：由黑名单切换至白名单时会先下发一个空的白名单，这时候将没有任何下挂设备可以登陆管理页面了**
2. 待优化问题
   2.1 拨号上网功能，规定了宽带密码不得少于8个字符，当宽带的密码确实少于8个字符时如何处理？
   2.2 设备管理-设备信息-LAN状态页面，MAC地址显示的不太美观
   2.3 设备管理-网络时间：同步时间的ubus接口仅是发起新的ntp请求，等待回复还是有一定时间，观察到uniweb插件是下发了指令后立即更新了时间的，这个时候往往ntp同步还没有完成
   2.4 在测试设备管理-DNS配置时，观察到插件下发了指令ahsapd.config addItem receive {"item":"vlanEnable","value":"1"}，看上去是IPTV功能，这样是不是会对IPTV配置有影响？
   2.5 高级设置-黑/白名单设置：观察到只有在配置白名单时候会记录macfilter_white，配置黑名单时不会记录macfilter_black吗？
   2.6 高级设置-儿童上网保护：当前仅通过ahsapd.config addItem的方式记录了配置，但是有现成的ubus接口ahsapd.limit，是否需要调用？
3. 需求咨询问题
   3.1 未找到WiFi5兼容配置选项，如何配置？
   3.2 访客网络的启用时长具体生效逻辑是怎样的？
   3.3 设置工作模式（ahsapd.uplink setWorkingMode）接口的internet字段是非必选吗？如果是的话应该更新规范？
   3.4 设置工作模式（ahsapd.uplink setWorkingMode）接口，当前没有配置路由模式-静态IP的选项？
   3.5 高级设置-Mesh配置：该功能的需求是怎样的？
4. 遗留问题
   4.1 页面登陆功能，当前默认密码为明文传递，以往的出货机型已无法获取明文页面登录密码，校验方式待评估
   4.2 首页-设备列表显示中，设备的当前速率显示最多只能支持四位数字+一个小数点，多了后会显示不全，待处理
   4.3 WiFi设置页面中，加密算法和加密方法的显示还有问题，待处理
   4.4 设备收到周边WiFi扫描指令后需要一定的时间进行扫描，不能及时响应。之前沟通时是AOS-NET仅会查询开机扫描的内容，从而规避了这个问题，现在无线桥接需要启用该功能，需要重新评估
   4.5 设备管理-设备信息-WAN状态页面不能获取到公网IPv4地址
   4.6 设备管理-设备日志：希望能够增加路由自己的日志下载；一键上传功能未生效
5. 已沟通过的问题
   5.1 高级设置-IOT专属通道为预留功能，暂不用测试
   5.2 IP地址管理功能中，leaseTime单位转换错误，单位应为秒
   5.3 IP地址管理功能中，不能同时修改LAN网段及DHCP池，配置完LAN网段后页面没有自动刷新，需要重新刷新页面才能继续配置
   5.4 IP地址管理-地址绑定功能，解绑失败，原因是下发的指令staDevices中缺少了IPAddress字段：取消IPAddress的必选选项
   5.5 黑白名单配置及WiFi配置的指令中将/转义处理（与AOS-NET插件行为不同）：新插件待测试

#### 20231021

问题1：首页选择关闭下挂设备“允许配网”键，看到ubus接口下发了黑白名单，但实际macFilterEnable为0

问题2：当前速率显示，单位不全，是否要修改上报的数据格式？

![](file:///C:/Users/<USER>/AppData/Local/Temp/msohtmlclip1/01/clip_image001.png)![1697958409438](image/统一web故障记录/1697958409438.png)

问题3：读取WIFI配置时，加密算法显示为数字，接口规范中规定如下：

| encrypt | Number(uint32) | 读写 | 加密算法，枚举取值：`` 0：None``1：AES ``2：TKIP``3：AES+TKIP | Y |
| ------- | -------------- | ---- | ------------------------------------------------------------- | - |

![1697958429913](image/统一web故障记录/1697958429913.png)![](file:///C:/Users/<USER>/AppData/Local/Temp/msohtmlclip1/01/clip_image002.png)

问题4：访客网络的加密方式读取异常，接口规范如下：

| securityMode | String | 读写 | 认证加密模式，枚举取值：`` None``WEP-64 ``WEP-128``WPA-Personal ``WPA2-Personal``MIXED-WPAPSK2 ``WPA3-SAE``MIXED-WPA2WPA3 | Y |
| ------------ | ------ | ---- | ------------------------------------------------------------------------------------------------------------------------- | - |

![1697958448455](image/统一web故障记录/1697958448455.png)![](file:///C:/Users/<USER>/AppData/Local/Temp/msohtmlclip1/01/clip_image003.png)

问题5：uniweb下发的setWifiParameter指令中有多余的斜杠：ahsapd.wifi setWifiParameter Receive {"configurations":[
{"radio":"2.4G","index":1,"enable":1,"ssid":"CMCC-Psum","securityMode":"MIXED-WPAPSK2","encrypt":1,"pwd":"********","maxAssociateNum":0,"ssidAdvertisementEnabled":1,"ssidStandard":"802.11b\/g\/n\/ax"},
{"radio":"2.4G","index":2,"enable":1,"ssid":"CMCC-GUIDE-LINK","securityMode":"WPA2-Personal","encrypt":1,"pwd":">fk2XZwL","maxAssociateNum":10,"ssidAdvertisementEnabled":0,"ssidStandard":"802.11b\/g\/n\/ax"},
{"radio":"2.4G","index":3,"enable":1,"ssid":"CMCC-GUEST-Psum","securityMode":"WPA-None","encrypt":0,"maxAssociateNum":0,"ssidAdvertisementEnabled":1,"ssidStandard":"802.11b\/g\/n\/ax"},
{"radio":"2.4G","index":4,"enable":0,"ssid":"CMCC-QLINK","securityMode":"None","encrypt":0,"pwd":"","maxAssociateNum":0,"ssidAdvertisementEnabled":0,"ssidStandard":"802.11b\/g\/n\/ax"},
{"radio":"5G","index":5,"enable":1,"ssid":"CMCC-Psum-5G","securityMode":"MIXED-WPAPSK2","encrypt":1,"pwd":"********","maxAssociateNum":0,"ssidAdvertisementEnabled":1,"ssidStandard":"802.11a\/n\/ac\/ax"},
{"radio":"5G","index":6,"enable":0,"ssid":"CMCC-Psum-5G-6","securityMode":"None","encrypt":0,"pwd":"","maxAssociateNum":0,"ssidAdvertisementEnabled":0,"ssidStandard":"802.11a\/n\/ac\/ax"},
{"radio":"5G","index":7,"enable":0,"ssid":"CMCC-GUEST-Psum-5G","securityMode":"WPA-None","encrypt":0,"maxAssociateNum":0,"ssidAdvertisementEnabled":1,"ssidStandard":"802.11a\/n\/ac\/ax"},
{"radio":"5G","index":8,"enable":0,"ssid":"CMCC-Psum-5G-8","securityMode":"None","encrypt":0,"pwd":"","maxAssociateNum":0,"ssidAdvertisementEnabled":1,"ssidStandard":"802.11a\/n\/ac\/ax"}
]}

问题6：访客网络设置时有启用时长选项，对应的是哪个接口呢？

问题7：选择健康模式时插件挂起，同时json指令不符合格式：

{"objid":1897767471,"method":"addTimedTask","data":{"mode":1,"enable":1,"taskName":"1","timeOffset":52020,"timeOffset2":52080,"taskId":0,"action":2}}

{"objid":530268869,"method":"addItem","data":{"value":{},"item":"task_name"}}

![](file:///C:/Users/<USER>/AppData/Local/Temp/msohtmlclip1/01/clip_image005.png)

问题8：设备信息-WAN状态中没有显示IPV4地址和IPV4/IPV6双栈状态

![](file:///C:/Users/<USER>/AppData/Local/Temp/msohtmlclip1/01/clip_image007.png)![1697958462914](image/统一web故障记录/1697958462914.png)

问题9：设备信息-LAN状态中显示不全，是否需要补充接口？

![](file:///C:/Users/<USER>/AppData/Local/Temp/msohtmlclip1/01/clip_image009.png)

问题10：开启NTP同步后插件挂起，同时json指令格式不对：

{"objid":634651822,"method":"ntpCalibration","data":{"nowTimeZone":"8"}}

![](file:///C:/Users/<USER>/AppData/Local/Temp/msohtmlclip1/01/clip_image011.png)

问题11：开启黑白名单后插件挂起：

![](file:///C:/Users/<USER>/AppData/Local/Temp/msohtmlclip1/01/clip_image013.png)

问题12：点击拓扑查询时插件挂起：

![](file:///C:/Users/<USER>/AppData/Local/Temp/msohtmlclip1/01/clip_image015.png)

问题13：点击儿童网络保护后网页显示空白页：

![](file:///C:/Users/<USER>/AppData/Local/Temp/msohtmlclip1/01/clip_image017.png)

问题14：DNS设置中无法关闭手动设置

问题15：PPPOE拨号要求输入的宽带密码不能少于8个字符，是否不太合理？这样会导致有些光猫无法连接。

问题16：无线桥接时json指令格式错误：

{"objid":1088394924,"method":"wlanConnect","data":{"ssid":"MERCURY_11F7","pwd":null,"securityMode":"NULL"},"user":"root","group":"root"}

{"objid":1088394924,"method":"setWorkingMode","data":{"workingMode":2}}
