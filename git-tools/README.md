# Git大文件过滤器

## 功能说明

此脚本配置Git仓库自动忽略大于50MB的文件，防止意外提交大文件。

## 已配置的功能

1. **pre-commit钩子**: 提交前自动检查文件大小
2. **Git配置**: 设置文件大小警告阈值
3. **gitignore规则**: 添加常见大文件类型过滤
4. **检查脚本**: 扫描现有大文件

## 使用方法

### 运行配置脚本
```bash
./scripts/setup-git-large-file-filter.sh
```

### 检查大文件
```bash
./scripts/check-large-files.sh
```

### 强制提交（跳过检查）
```bash
git commit --no-verify -m "提交信息"
```

## 文件说明

- `setup-git-large-file-filter.sh`: 主配置脚本
- `check-large-files.sh`: 大文件检查脚本
- `.git/hooks/pre-commit`: 提交前检查钩子
- `.gitignore`: 更新的忽略规则

## 注意事项

- 配置后所有大于50MB的文件提交都会被阻止
- 如需提交大文件，建议使用Git LFS
- 强制提交会跳过所有检查，请谨慎使用
