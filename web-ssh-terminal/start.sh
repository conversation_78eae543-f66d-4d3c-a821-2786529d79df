#!/bin/bash

# Web SSH Terminal 启动脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 脚本目录
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
cd "$SCRIPT_DIR"

echo -e "${BLUE}=== Web SSH Terminal 启动脚本 ===${NC}"
echo

# 检查Node.js是否安装
check_nodejs() {
    if ! command -v node &> /dev/null; then
        echo -e "${RED}错误：未找到Node.js${NC}"
        echo -e "${YELLOW}请先安装Node.js (版本 >= 14.0.0)${NC}"
        echo -e "${YELLOW}下载地址：https://nodejs.org/${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    echo -e "${GREEN}✓ Node.js版本：${NODE_VERSION}${NC}"
}

# 检查npm是否安装
check_npm() {
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}错误：未找到npm${NC}"
        echo -e "${YELLOW}npm通常与Node.js一起安装${NC}"
        exit 1
    fi
    
    NPM_VERSION=$(npm --version)
    echo -e "${GREEN}✓ npm版本：${NPM_VERSION}${NC}"
}

# 检查依赖
check_dependencies() {
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}⚠ 未找到node_modules目录，开始安装依赖...${NC}"
        install_dependencies
    else
        echo -e "${GREEN}✓ 依赖已安装${NC}"
    fi
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}正在安装依赖包...${NC}"
    
    if npm install; then
        echo -e "${GREEN}✓ 依赖安装成功${NC}"
    else
        echo -e "${RED}✗ 依赖安装失败${NC}"
        echo -e "${YELLOW}请检查网络连接和npm配置${NC}"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    local port=${1:-3000}
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口可用
    fi
}

# 启动服务器
start_server() {
    local port=${PORT:-3000}
    
    echo
    echo -e "${BLUE}启动Web SSH Terminal服务器...${NC}"
    
    # 检查默认端口是否被占用
    if check_port $port; then
        echo -e "${YELLOW}⚠ 端口 $port 被占用，服务器将自动寻找可用端口${NC}"
    fi
    
    echo -e "${YELLOW}服务器启动中，请稍候...${NC}"
    echo
    echo -e "${YELLOW}按 Ctrl+C 停止服务器${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo
    
    # 启动服务器
    if PORT=$port node server.js; then
        echo -e "${GREEN}服务器已正常停止${NC}"
    else
        echo -e "${RED}服务器启动失败${NC}"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "Web SSH Terminal 启动脚本"
    echo
    echo "用法："
    echo "  $0 [选项]"
    echo
    echo "选项："
    echo "  start, -s     启动服务器（默认）"
    echo "  install, -i   仅安装依赖"
    echo "  dev, -d      开发模式启动（需要nodemon）"
    echo "  help, -h     显示此帮助信息"
    echo
    echo "环境变量："
    echo "  PORT         服务器端口（默认：3000）"
    echo
    echo "示例："
    echo "  $0 start          # 启动服务器"
    echo "  PORT=8080 $0      # 在端口8080启动"
    echo "  $0 install        # 仅安装依赖"
}

# 开发模式启动
start_dev() {
    if ! command -v nodemon &> /dev/null; then
        echo -e "${YELLOW}未找到nodemon，正在安装...${NC}"
        npm install -g nodemon
    fi
    
    echo -e "${BLUE}开发模式启动（自动重启）...${NC}"
    nodemon server.js
}

# 主函数
main() {
    case "${1:-start}" in
        "start"|"-s"|"")
            check_nodejs
            check_npm
            check_dependencies
            start_server
            ;;
        "install"|"-i")
            check_nodejs
            check_npm
            install_dependencies
            echo -e "${GREEN}依赖安装完成！${NC}"
            echo -e "${YELLOW}运行 '$0 start' 启动服务器${NC}"
            ;;
        "dev"|"-d")
            check_nodejs
            check_npm
            check_dependencies
            start_dev
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}未知选项：$1${NC}"
            echo -e "${YELLOW}使用 '$0 help' 查看帮助信息${NC}"
            exit 1
            ;;
    esac
}

# 处理中断信号
trap 'echo -e "\n${YELLOW}服务器已停止${NC}"; exit 0' INT TERM

# 执行主函数
main "$@"