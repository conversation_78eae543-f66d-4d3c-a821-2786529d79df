const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const { NodeSSH } = require('node-ssh');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const cors = require('cors');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3000;

// 存储SSH连接
const sshConnections = new Map();

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// 路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// WebSocket连接处理
io.on('connection', (socket) => {
  console.log(`用户连接: ${socket.id}`);
  
  // SSH连接请求
  socket.on('ssh-connect', async (data) => {
    try {
      const { host, port, username, password, privateKey } = data;
      const connectionId = uuidv4();
      
      console.log(`尝试SSH连接到 ${username}@${host}:${port}`);
      
      const ssh = new NodeSSH();
      
      // 连接配置
      const connectConfig = {
        host: host,
        port: port || 22,
        username: username
      };
      
      // 使用密码或私钥认证
      if (privateKey) {
        connectConfig.privateKey = privateKey;
      } else if (password) {
        connectConfig.password = password;
      } else {
        socket.emit('ssh-error', { error: '请提供密码或私钥' });
        return;
      }
      
      // 建立SSH连接
      await ssh.connect(connectConfig);
      
      // 创建shell会话
      const shell = await ssh.requestShell({
        rows: 24,
        cols: 80,
        term: 'xterm-256color'
      });
      
      // 存储连接信息
      sshConnections.set(socket.id, {
        ssh: ssh,
        shell: shell,
        connectionId: connectionId
      });
      
      // 监听shell输出
      shell.on('data', (data) => {
        socket.emit('ssh-data', { data: data.toString() });
      });
      
      shell.on('close', () => {
        console.log(`SSH连接关闭: ${socket.id}`);
        socket.emit('ssh-disconnect');
        sshConnections.delete(socket.id);
      });
      
      shell.on('error', (err) => {
        console.error(`SSH Shell错误: ${err}`);
        socket.emit('ssh-error', { error: err.message });
      });
      
      socket.emit('ssh-connected', { 
        connectionId: connectionId,
        message: `已连接到 ${username}@${host}:${port}` 
      });
      
    } catch (error) {
      console.error(`SSH连接失败: ${error}`);
      socket.emit('ssh-error', { error: `连接失败: ${error.message}` });
    }
  });
  
  // 发送命令到SSH
  socket.on('ssh-input', (data) => {
    const connection = sshConnections.get(socket.id);
    if (connection && connection.shell) {
      connection.shell.write(data.input);
    } else {
      socket.emit('ssh-error', { error: '没有活动的SSH连接' });
    }
  });
  
  // 调整终端大小
  socket.on('ssh-resize', (data) => {
    const connection = sshConnections.get(socket.id);
    if (connection && connection.shell) {
      connection.shell.setWindow(data.rows, data.cols);
    }
  });
  
  // 断开连接
  socket.on('ssh-disconnect', () => {
    const connection = sshConnections.get(socket.id);
    if (connection) {
      if (connection.shell) {
        connection.shell.end();
      }
      if (connection.ssh) {
        connection.ssh.dispose();
      }
      sshConnections.delete(socket.id);
    }
  });
  
  // 客户端断开连接
  socket.on('disconnect', () => {
    console.log(`用户断开连接: ${socket.id}`);
    const connection = sshConnections.get(socket.id);
    if (connection) {
      if (connection.shell) {
        connection.shell.end();
      }
      if (connection.ssh) {
        connection.ssh.dispose();
      }
      sshConnections.delete(socket.id);
    }
  });
});

// 错误处理
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});

// 启动服务器
server.listen(PORT, () => {
  console.log(`Web SSH终端服务器运行在端口 ${PORT}`);
  console.log(`访问 http://localhost:${PORT} 来使用终端`);
});