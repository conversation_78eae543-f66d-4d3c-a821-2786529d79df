# Web SSH Terminal

一个基于Web的SSH终端模拟器，支持通过浏览器进行远程SSH连接和实时交互。

## 功能特性

- 🌐 **基于Web的SSH客户端** - 无需安装额外软件，直接在浏览器中使用
- 🔒 **多种认证方式** - 支持密码认证和SSH私钥认证
- 💻 **真实终端体验** - 基于xterm.js，提供与本地终端一致的体验
- 🎨 **现代化UI设计** - 响应式设计，支持移动端访问
- ⚡ **实时交互** - 基于WebSocket，确保低延迟的命令执行
- 🔧 **终端控制** - 支持清屏、全屏模式等常用功能
- 📱 **响应式设计** - 适配各种屏幕尺寸

## 技术栈

### 后端
- **Node.js** - 服务器运行环境
- **Express** - Web服务器框架
- **Socket.io** - WebSocket通信
- **node-ssh** - SSH连接库

### 前端
- **HTML5/CSS3/JavaScript** - 基础前端技术
- **xterm.js** - 终端模拟器
- **Socket.io-client** - WebSocket客户端

## 快速开始

### 环境要求

- Node.js >= 14.0.0
- npm >= 6.0.0

### 安装步骤

1. **克隆或下载项目**
   ```bash
   # 如果是从Git克隆
   git clone <repository-url>
   cd web-ssh-terminal
   
   # 或者直接进入项目目录
   cd web-ssh-terminal
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动服务器**
   ```bash
   # 生产模式
   npm start
   
   # 开发模式（自动重启）
   npm run dev
   ```

4. **访问应用**
   
   打开浏览器访问：`http://localhost:3000`

### 配置选项

可以通过环境变量配置服务器：

```bash
# 设置端口（默认3000）
export PORT=8080

# 启动服务器
npm start
```

## 使用说明

### SSH连接

1. **填写连接信息**
   - 主机地址：目标服务器的IP地址或域名
   - 端口：SSH服务端口（默认22）
   - 用户名：SSH登录用户名

2. **选择认证方式**
   
   **密码认证**
   - 输入SSH登录密码
   
   **私钥认证**
   - 粘贴SSH私钥内容（支持OpenSSH格式）

3. **连接服务器**
   - 点击"连接"按钮
   - 等待连接建立
   - 连接成功后即可在终端中执行命令

### 终端操作

- **命令执行**：直接在终端中输入命令并按回车
- **清屏**：点击"清屏"按钮或使用 `clear` 命令
- **全屏模式**：点击"全屏"按钮或按 F11
- **复制粘贴**：
  - 复制：选中文本后 Ctrl+C（或右键）
  - 粘贴：Ctrl+V（或右键）
- **中断命令**：Ctrl+C
- **退出程序**：Ctrl+D 或 `exit` 命令

### 快捷键

- `F11` - 切换全屏模式
- `Ctrl+C` - 中断当前命令
- `Ctrl+D` - 发送EOF信号
- `Ctrl+V` - 粘贴文本

## 安全注意事项

⚠️ **重要安全提醒**

1. **仅在可信网络环境中使用**
   - 本应用在内网环境中使用最为安全
   - 如需公网访问，请配置HTTPS和访问控制

2. **SSH凭据安全**
   - 私钥和密码在传输过程中会暂存在服务器内存中
   - 建议使用专用的SSH密钥对
   - 避免使用具有高权限的账户

3. **访问控制**
   - 考虑添加用户认证机制
   - 限制可连接的目标服务器范围
   - 启用访问日志记录

4. **网络安全**
   - 在生产环境中使用HTTPS
   - 配置防火墙规则
   - 考虑使用VPN或内网访问

## 项目结构

```
web-ssh-terminal/
├── server.js              # 后端服务器主文件
├── package.json           # 项目配置和依赖
├── README.md              # 项目说明文档
└── public/                # 前端静态文件
    ├── index.html         # 主页面
    ├── style.css          # 样式文件
    └── app.js            # 前端JavaScript逻辑
```

## 开发指南

### 自定义主题

在 `public/app.js` 中修改终端主题：

```javascript
this.terminal = new Terminal({
    theme: {
        background: '#000000',    // 背景色
        foreground: '#ffffff',    // 前景色
        cursor: '#ffffff',        // 光标色
        // ... 其他颜色配置
    }
});
```

### 添加新功能

1. **后端功能**：在 `server.js` 中添加新的socket事件处理
2. **前端功能**：在 `public/app.js` 中添加相应的客户端逻辑
3. **样式调整**：在 `public/style.css` 中添加样式

### API接口

WebSocket事件：

**客户端 → 服务器**
- `ssh-connect` - 发起SSH连接
- `ssh-input` - 发送终端输入
- `ssh-resize` - 调整终端大小
- `ssh-disconnect` - 断开SSH连接

**服务器 → 客户端**
- `ssh-connected` - SSH连接成功
- `ssh-data` - 终端输出数据
- `ssh-error` - 错误信息
- `ssh-disconnect` - 连接断开

## 故障排除

### 常见问题

1. **连接失败**
   - 检查目标服务器是否可达
   - 确认SSH服务是否正常运行
   - 验证认证信息是否正确

2. **终端显示异常**
   - 刷新页面重新连接
   - 检查浏览器控制台是否有错误
   - 尝试不同的浏览器

3. **性能问题**
   - 检查网络延迟
   - 减少终端输出量
   - 关闭不必要的后台程序

### 日志查看

服务器日志会在控制台输出，包括：
- 用户连接/断开信息
- SSH连接状态
- 错误信息

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 基本SSH连接功能
- Web终端界面
- 密码和私钥认证支持