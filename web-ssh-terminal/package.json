{"name": "web-ssh-terminal", "version": "1.0.0", "description": "基于Web的SSH终端，支持远程登录和实时交互", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "install-deps": "npm install"}, "keywords": ["ssh", "web", "terminal", "websocket", "remote"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "node-ssh": "^13.1.0", "uuid": "^9.0.0", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}