# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时文件
*.pid
*.seed
*.pid.lock

# 日志文件
logs/
*.log

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 缓存文件
.npm
.eslintcache
.cache/

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 构建输出
dist/
build/

# 测试覆盖率
coverage/

# SSH密钥文件（安全考虑）
*.pem
*.key
id_rsa*
*.ppk