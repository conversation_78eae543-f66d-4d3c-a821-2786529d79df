<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web SSH Terminal - 调试模式</title>
    <style>
        body {
            font-family: monospace;
            background: #1e1e1e;
            color: #ffffff;
            margin: 20px;
        }
        .debug-info {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .terminal-container {
            width: 100%;
            height: 400px;
            background: #000;
            border: 1px solid #444;
            margin: 20px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        #log {
            background: #222;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-size: 12px;
            border: 1px solid #444;
        }
    </style>
</head>
<body>
    <h1>Web SSH Terminal - 调试模式</h1>
    
    <div class="debug-info">
        <h3>库加载状态</h3>
        <div id="library-status">检查中...</div>
    </div>
    
    <div class="debug-info">
        <h3>终端测试</h3>
        <button onclick="createTerminal()">创建终端</button>
        <button onclick="writeToTerminal()">写入测试文本</button>
        <button onclick="clearTerminal()">清空终端</button>
        <div class="terminal-container" id="terminal-container"></div>
    </div>
    
    <div class="debug-info">
        <h3>WebSocket测试</h3>
        <button onclick="testWebSocket()">测试WebSocket连接</button>
        <div id="websocket-status">未测试</div>
    </div>
    
    <div class="debug-info">
        <h3>调试日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div id="log"></div>
    </div>

    <!-- JavaScript库 -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/xterm@5.3.0/lib/xterm.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/xterm-addon-fit@0.8.0/lib/xterm-addon-fit.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/xterm-addon-web-links@0.9.0/lib/xterm-addon-web-links.js"></script>
    
    <script>
        let terminal = null;
        let fitAddon = null;
        let socket = null;
        
        function log(message) {
            const logElement = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${time}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function checkLibraries() {
            const status = document.getElementById('library-status');
            let html = '';
            
            const libraries = [
                { name: 'Socket.IO', check: () => typeof io !== 'undefined' },
                { name: 'xterm.js', check: () => typeof Terminal !== 'undefined' },
                { name: 'FitAddon', check: () => typeof FitAddon !== 'undefined' },
                { name: 'WebLinksAddon', check: () => typeof WebLinksAddon !== 'undefined' }
            ];
            
            libraries.forEach(lib => {
                const loaded = lib.check();
                const color = loaded ? '#4CAF50' : '#f44336';
                const status = loaded ? '✓ 已加载' : '✗ 未加载';
                html += `<div style="color: ${color}">${lib.name}: ${status}</div>`;
                log(`${lib.name}: ${status}`);
            });
            
            status.innerHTML = html;
        }
        
        function createTerminal() {
            try {
                if (!Terminal) {
                    log('错误: xterm.js 未加载');
                    return;
                }
                
                log('创建终端实例...');
                terminal = new Terminal({
                    cursorBlink: true,
                    fontSize: 14,
                    theme: {
                        background: '#000000',
                        foreground: '#ffffff'
                    }
                });
                
                if (FitAddon) {
                    fitAddon = new FitAddon.FitAddon();
                    terminal.loadAddon(fitAddon);
                    log('FitAddon 已加载');
                }
                
                if (WebLinksAddon) {
                    terminal.loadAddon(new WebLinksAddon.WebLinksAddon());
                    log('WebLinksAddon 已加载');
                }
                
                const container = document.getElementById('terminal-container');
                terminal.open(container);
                
                if (fitAddon) {
                    setTimeout(() => {
                        fitAddon.fit();
                        log('终端大小已调整');
                    }, 100);
                }
                
                log('终端创建成功');
                terminal.writeln('终端创建成功！');
                terminal.writeln('这是一个测试终端');
                
            } catch (error) {
                log('创建终端失败: ' + error.message);
            }
        }
        
        function writeToTerminal() {
            if (!terminal) {
                log('请先创建终端');
                return;
            }
            
            try {
                terminal.writeln('\\x1b[1;32m绿色文本测试\\x1b[0m');
                terminal.writeln('\\x1b[1;31m红色文本测试\\x1b[0m');
                terminal.writeln('\\x1b[1;33m黄色文本测试\\x1b[0m');
                terminal.write('输入提示符 $ ');
                log('测试文本写入成功');
            } catch (error) {
                log('写入终端失败: ' + error.message);
            }
        }
        
        function clearTerminal() {
            if (!terminal) {
                log('请先创建终端');
                return;
            }
            
            terminal.clear();
            log('终端已清空');
        }
        
        function testWebSocket() {
            try {
                if (!io) {
                    log('错误: Socket.IO 未加载');
                    return;
                }
                
                log('创建WebSocket连接...');
                socket = io();
                
                socket.on('connect', () => {
                    log('WebSocket 连接成功');
                    document.getElementById('websocket-status').innerHTML = 
                        '<span style="color: #4CAF50">✓ 连接成功</span>';
                });
                
                socket.on('disconnect', () => {
                    log('WebSocket 连接断开');
                    document.getElementById('websocket-status').innerHTML = 
                        '<span style="color: #f44336">✗ 连接断开</span>';
                });
                
                socket.on('error', (error) => {
                    log('WebSocket 错误: ' + error);
                });
                
            } catch (error) {
                log('WebSocket 测试失败: ' + error.message);
            }
        }
        
        // 页面加载完成后执行检查
        window.addEventListener('load', () => {
            log('页面加载完成');
            checkLibraries();
        });
        
        // 监听错误
        window.addEventListener('error', (event) => {
            log('JavaScript 错误: ' + event.error.message);
        });
        
    </script>
</body>
</html>