/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON>solas', 'Monaco', 'Courier New', monospace;
    background: #1e1e1e;
    color: #ffffff;
    line-height: 1.6;
}

.container {
    display: flex;
    height: 100vh;
    gap: 10px;
    padding: 10px;
}

/* 面板样式 */
.panel {
    background: #252525;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

#connection-panel {
    width: 400px;
    min-width: 350px;
    max-width: 450px;
    overflow-y: auto;
}

#terminal-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 600px;
}

/* 连接面板样式 */
h2, h3 {
    color: #4CAF50;
    margin-bottom: 20px;
    text-align: center;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    color: #cccccc;
    font-weight: bold;
}

input[type="text"],
input[type="password"],
input[type="number"],
textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #444;
    border-radius: 4px;
    background: #333;
    color: #fff;
    font-family: inherit;
    font-size: 14px;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
textarea:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
}

textarea {
    resize: vertical;
    min-height: 100px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* 认证选项卡 */
.auth-tabs {
    display: flex;
    margin-bottom: 10px;
}

.tab-button {
    flex: 1;
    padding: 8px 16px;
    border: 1px solid #444;
    background: #333;
    color: #ccc;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s;
}

.tab-button:first-child {
    border-radius: 4px 0 0 4px;
}

.tab-button:last-child {
    border-radius: 0 4px 4px 0;
    border-left: none;
}

.tab-button.active {
    background: #4CAF50;
    color: white;
    border-color: #4CAF50;
}

.tab-button:hover:not(.active) {
    background: #444;
}

.auth-panel {
    display: none;
}

.auth-panel.active {
    display: block;
}

/* 按钮样式 */
.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s;
    font-family: inherit;
}

#connect-btn {
    flex: 1;
    background: #4CAF50;
    color: white;
}

#connect-btn:hover:not(:disabled) {
    background: #45a049;
}

#connect-btn:disabled {
    background: #666;
    cursor: not-allowed;
}

#disconnect-btn {
    flex: 1;
    background: #f44336;
    color: white;
}

#disconnect-btn:hover:not(:disabled) {
    background: #da190b;
}

#disconnect-btn:disabled {
    background: #666;
    cursor: not-allowed;
}

/* 状态显示 */
.status {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    text-align: center;
    min-height: 20px;
}

.status.success {
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid #4CAF50;
    color: #4CAF50;
}

.status.error {
    background: rgba(244, 67, 54, 0.2);
    border: 1px solid #f44336;
    color: #f44336;
}

.status.info {
    background: rgba(33, 150, 243, 0.2);
    border: 1px solid #2196F3;
    color: #2196F3;
}

/* 终端面板样式 */
.terminal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #444;
}

.terminal-controls {
    display: flex;
    gap: 10px;
}

.terminal-controls button {
    padding: 5px 10px;
    font-size: 12px;
    background: #444;
    color: #ccc;
}

.terminal-controls button:hover {
    background: #555;
}

#terminal-container {
    flex: 1;
    background: #000;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

/* xterm.js 终端样式覆盖 */
.xterm {
    height: 100% !important;
    width: 100% !important;
}

.xterm .xterm-viewport {
    overflow-y: auto;
}

.xterm .xterm-screen {
    height: 100%;
}

/* 全屏模式 */
.fullscreen {
    position: fixed !important;
    top: 0;
    left: 0;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999;
    background: #1e1e1e;
    padding: 20px;
}

.fullscreen #connection-panel {
    display: none;
}

.fullscreen #terminal-panel {
    width: 100%;
    height: 100%;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .container {
        flex-direction: column;
        height: auto;
        min-height: 100vh;
    }
    
    #connection-panel {
        width: 100%;
        max-width: none;
        order: 1;
    }
    
    #terminal-panel {
        min-width: auto;
        height: 500px;
        order: 2;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 5px;
        gap: 5px;
    }
    
    .panel {
        padding: 15px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .auth-tabs {
        flex-direction: column;
    }
    
    .tab-button {
        border-radius: 4px !important;
        margin-bottom: 5px;
        border: 1px solid #444 !important;
    }
    
    .tab-button:last-child {
        margin-bottom: 0;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #333;
}

::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #333;
    border-radius: 50%;
    border-top-color: #4CAF50;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}