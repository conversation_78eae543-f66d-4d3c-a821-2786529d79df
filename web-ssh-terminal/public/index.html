<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web SSH Terminal</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/xterm@5.3.0/lib/xterm.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <!-- 连接配置面板 -->
        <div id="connection-panel" class="panel">
            <h2>SSH 连接配置</h2>
            <form id="ssh-form">
                <div class="form-group">
                    <label for="host">主机地址:</label>
                    <input type="text" id="host" name="host" placeholder="例: *************" required>
                </div>
                
                <div class="form-group">
                    <label for="port">端口:</label>
                    <input type="number" id="port" name="port" value="22" min="1" max="65535">
                </div>
                
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" name="username" placeholder="例: root" required>
                </div>
                
                <div class="form-group">
                    <label>认证方式:</label>
                    <div class="auth-tabs">
                        <button type="button" id="password-tab" class="tab-button active">密码认证</button>
                        <button type="button" id="key-tab" class="tab-button">私钥认证</button>
                    </div>
                </div>
                
                <div id="password-auth" class="auth-panel active">
                    <div class="form-group">
                        <label for="password">密码:</label>
                        <input type="password" id="password" name="password" placeholder="输入SSH密码">
                    </div>
                </div>
                
                <div id="key-auth" class="auth-panel">
                    <div class="form-group">
                        <label for="private-key">私钥:</label>
                        <textarea id="private-key" name="privateKey" placeholder="粘贴SSH私钥内容..." rows="10"></textarea>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" id="connect-btn">连接</button>
                    <button type="button" id="disconnect-btn" disabled>断开连接</button>
                </div>
            </form>
            
            <div id="status" class="status"></div>
        </div>

        <!-- 终端面板 -->
        <div id="terminal-panel" class="panel">
            <div class="terminal-header">
                <h3 id="terminal-title">SSH 终端</h3>
                <div class="terminal-controls">
                    <button id="clear-btn">清屏</button>
                    <button id="fullscreen-btn">全屏</button>
                </div>
            </div>
            <div id="terminal-container"></div>
        </div>
    </div>

    <!-- JavaScript库 -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js" 
            onerror="console.error('Failed to load Socket.IO')"></script>
    <script src="https://cdn.jsdelivr.net/npm/xterm@5.3.0/lib/xterm.js" 
            onerror="console.error('Failed to load xterm.js')"></script>
    <script src="https://cdn.jsdelivr.net/npm/xterm-addon-fit@0.8.0/lib/xterm-addon-fit.js" 
            onerror="console.error('Failed to load xterm-addon-fit')"></script>
    <script src="https://cdn.jsdelivr.net/npm/xterm-addon-web-links@0.9.0/lib/xterm-addon-web-links.js" 
            onerror="console.error('Failed to load xterm-addon-web-links')"></script>
    
    <script>
        // 检查必要的库是否加载成功
        window.addEventListener('load', function() {
            let missingLibs = [];
            if (typeof io === 'undefined') missingLibs.push('Socket.IO');
            if (typeof Terminal === 'undefined') missingLibs.push('xterm.js');
            if (typeof FitAddon === 'undefined') missingLibs.push('FitAddon');
            if (typeof WebLinksAddon === 'undefined') missingLibs.push('WebLinksAddon');
            
            if (missingLibs.length > 0) {
                document.body.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #f44336;">
                        <h2>加载错误</h2>
                        <p>以下库加载失败: ${missingLibs.join(', ')}</p>
                        <p>请检查网络连接并刷新页面</p>
                        <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 10px;">刷新页面</button>
                    </div>
                `;
                return;
            }
        });
    </script>
    
    <script src="app.js"></script>
</body>
</html>