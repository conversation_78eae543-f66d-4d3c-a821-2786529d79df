// Web SSH Terminal 客户端应用
class WebSSHTerminal {
    constructor() {
        this.socket = null;
        this.terminal = null;
        this.fitAddon = null;
        this.isConnected = false;
        this.isFullscreen = false;
        
        this.init();
    }
    
    init() {
        try {
            console.log('初始化 Web SSH Terminal...');
            this.initializeSocket();
            this.initializeTerminal();
            this.bindEvents();
            this.updateStatus('就绪', 'info');
            console.log('Web SSH Terminal 初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
            this.updateStatus('初始化失败: ' + error.message, 'error');
        }
    }
    
    // 初始化WebSocket连接
    initializeSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('WebSocket连接已建立');
        });
        
        this.socket.on('disconnect', () => {
            console.log('WebSocket连接已断开');
            this.isConnected = false;
            this.updateConnectionState();
            this.updateStatus('WebSocket连接断开', 'error');
        });
        
        this.socket.on('ssh-connected', (data) => {
            this.isConnected = true;
            this.updateConnectionState();
            this.updateStatus(data.message, 'success');
            document.getElementById('terminal-title').textContent = 
                `SSH 终端 - ${document.getElementById('username').value}@${document.getElementById('host').value}`;
        });
        
        this.socket.on('ssh-data', (data) => {
            if (this.terminal) {
                try {
                    this.terminal.write(data.data);
                } catch (error) {
                    console.error('写入终端数据失败:', error);
                }
            }
        });
        
        this.socket.on('ssh-error', (data) => {
            this.updateStatus(`错误: ${data.error}`, 'error');
            this.isConnected = false;
            this.updateConnectionState();
        });
        
        this.socket.on('ssh-disconnect', () => {
            this.isConnected = false;
            this.updateConnectionState();
            this.updateStatus('SSH连接已断开', 'info');
            document.getElementById('terminal-title').textContent = 'SSH 终端';
        });
    }
    
    // 初始化终端
    initializeTerminal() {
        // 创建终端实例
        this.terminal = new Terminal({
            cursorBlink: true,
            cursorStyle: 'block',
            fontSize: 14,
            fontFamily: 'Consolas, Monaco, "Courier New", monospace',
            theme: {
                background: '#000000',
                foreground: '#ffffff',
                cursor: '#ffffff',
                selection: '#44475a',
                black: '#000000',
                red: '#ff5555',
                green: '#50fa7b',
                yellow: '#f1fa8c',
                blue: '#bd93f9',
                magenta: '#ff79c6',
                cyan: '#8be9fd',
                white: '#f8f8f2',
                brightBlack: '#6272a4',
                brightRed: '#ff6e6e',
                brightGreen: '#69ff94',
                brightYellow: '#ffffa5',
                brightBlue: '#d6acff',
                brightMagenta: '#ff92df',
                brightCyan: '#a4ffff',
                brightWhite: '#ffffff'
            },
            allowTransparency: false,
            convertEol: true,
            scrollback: 10000
        });
        
        // 添加插件
        this.fitAddon = new FitAddon.FitAddon();
        this.terminal.loadAddon(this.fitAddon);
        this.terminal.loadAddon(new WebLinksAddon.WebLinksAddon());
        
        // 挂载到DOM
        const terminalContainer = document.getElementById('terminal-container');
        this.terminal.open(terminalContainer);
        
        // 等待DOM渲染完成后调整大小
        setTimeout(() => {
            this.fitAddon.fit();
        }, 100);
        
        // 监听终端输入
        this.terminal.onData((data) => {
            if (this.isConnected && this.socket) {
                this.socket.emit('ssh-input', { input: data });
            }
        });
        
        // 监听终端大小变化
        this.terminal.onResize((size) => {
            if (this.isConnected && this.socket) {
                this.socket.emit('ssh-resize', { 
                    rows: size.rows, 
                    cols: size.cols 
                });
            }
        });
        
        // 窗口大小变化时调整终端
        window.addEventListener('resize', () => {
            setTimeout(() => {
                this.fitAddon.fit();
            }, 100);
        });
        
        // 显示欢迎信息
        this.terminal.writeln('\x1b[1;32m欢迎使用 Web SSH Terminal\x1b[0m');
        this.terminal.writeln('\x1b[36m请在左侧配置SSH连接信息\x1b[0m');
        this.terminal.writeln('');
    }
    
    // 绑定事件
    bindEvents() {
        // 认证方式切换
        document.getElementById('password-tab').addEventListener('click', () => {
            this.switchAuthMethod('password');
        });
        
        document.getElementById('key-tab').addEventListener('click', () => {
            this.switchAuthMethod('key');
        });
        
        // SSH表单提交
        document.getElementById('ssh-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleConnect();
        });
        
        // 断开连接
        document.getElementById('disconnect-btn').addEventListener('click', () => {
            this.handleDisconnect();
        });
        
        // 终端控制
        document.getElementById('clear-btn').addEventListener('click', () => {
            this.terminal.clear();
        });
        
        document.getElementById('fullscreen-btn').addEventListener('click', () => {
            this.toggleFullscreen();
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            // F11 全屏
            if (e.key === 'F11') {
                e.preventDefault();
                this.toggleFullscreen();
            }
            
            // Ctrl+C 中断（仅在终端聚焦时）
            if (e.ctrlKey && e.key === 'c' && document.activeElement === this.terminal.element) {
                // 已由xterm.js处理
            }
        });
    }
    
    // 切换认证方式
    switchAuthMethod(method) {
        const passwordTab = document.getElementById('password-tab');
        const keyTab = document.getElementById('key-tab');
        const passwordPanel = document.getElementById('password-auth');
        const keyPanel = document.getElementById('key-auth');
        
        if (method === 'password') {
            passwordTab.classList.add('active');
            keyTab.classList.remove('active');
            passwordPanel.classList.add('active');
            keyPanel.classList.remove('active');
        } else {
            passwordTab.classList.remove('active');
            keyTab.classList.add('active');
            passwordPanel.classList.remove('active');
            keyPanel.classList.add('active');
        }
    }
    
    // 处理连接
    handleConnect() {
        if (this.isConnected) {
            this.updateStatus('已有活动连接', 'error');
            return;
        }
        
        const host = document.getElementById('host').value.trim();
        const port = document.getElementById('port').value || 22;
        const username = document.getElementById('username').value.trim();
        
        if (!host || !username) {
            this.updateStatus('请填写主机地址和用户名', 'error');
            return;
        }
        
        // 获取认证信息
        const isPasswordAuth = document.getElementById('password-tab').classList.contains('active');
        let authData = {};
        
        if (isPasswordAuth) {
            const password = document.getElementById('password').value;
            if (!password) {
                this.updateStatus('请输入密码', 'error');
                return;
            }
            authData.password = password;
        } else {
            const privateKey = document.getElementById('private-key').value.trim();
            if (!privateKey) {
                this.updateStatus('请输入私钥', 'error');
                return;
            }
            authData.privateKey = privateKey;
        }
        
        // 显示连接中状态
        this.updateStatus('正在连接...', 'info');
        const connectBtn = document.getElementById('connect-btn');
        connectBtn.disabled = true;
        connectBtn.innerHTML = '<span class="loading"></span> 连接中...';
        
        // 清空终端
        this.terminal.clear();
        this.terminal.writeln(`\x1b[1;33m正在连接到 ${username}@${host}:${port}...\x1b[0m`);
        
        // 发送连接请求
        this.socket.emit('ssh-connect', {
            host: host,
            port: parseInt(port),
            username: username,
            ...authData
        });
    }
    
    // 处理断开连接
    handleDisconnect() {
        if (!this.isConnected) {
            return;
        }
        
        this.socket.emit('ssh-disconnect');
        this.isConnected = false;
        this.updateConnectionState();
        this.updateStatus('已断开连接', 'info');
        this.terminal.writeln('\x1b[1;31m连接已断开\x1b[0m');
    }
    
    // 更新连接状态
    updateConnectionState() {
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        
        if (this.isConnected) {
            connectBtn.disabled = true;
            connectBtn.textContent = '已连接';
            disconnectBtn.disabled = false;
        } else {
            connectBtn.disabled = false;
            connectBtn.textContent = '连接';
            disconnectBtn.disabled = true;
        }
    }
    
    // 更新状态显示
    updateStatus(message, type = 'info') {
        const statusElement = document.getElementById('status');
        statusElement.textContent = message;
        statusElement.className = `status ${type}`;
    }
    
    // 切换全屏模式
    toggleFullscreen() {
        const container = document.querySelector('.container');
        
        if (!this.isFullscreen) {
            container.classList.add('fullscreen');
            document.getElementById('fullscreen-btn').textContent = '退出全屏';
            this.isFullscreen = true;
        } else {
            container.classList.remove('fullscreen');
            document.getElementById('fullscreen-btn').textContent = '全屏';
            this.isFullscreen = false;
        }
        
        // 调整终端大小
        setTimeout(() => {
            this.fitAddon.fit();
        }, 100);
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.sshTerminal = new WebSSHTerminal();
});