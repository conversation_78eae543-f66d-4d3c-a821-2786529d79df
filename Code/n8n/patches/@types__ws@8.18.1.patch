diff --git a/index.d.ts b/index.d.ts
index 6d08adc155873e948d2ffebf40622fe405159bc0..b90773f2777a68408d9d1536412f80bb3c5325a0 100644
--- a/index.d.ts
+++ b/index.d.ts
@@ -62,6 +62,9 @@ declare class WebSocket extends EventEmitter {
         | typeof WebSocket.CLOSED;
     readonly url: string;
 
+    /** Indicates if the connection has replied to the last PING */
+    isAlive: boolean;
+
     /** The connection is not yet open. */
     readonly CONNECTING: 0;
     /** The connection is open and ready to communicate. */