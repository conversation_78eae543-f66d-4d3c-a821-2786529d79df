{"createdAt": "2021-02-18T16:06:30.135Z", "updatedAt": "2021-02-18T16:06:30.135Z", "id": "42", "name": "GoogleTask:create update getAll get delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [360, 250], "id": "8a9cccfd-ef20-48c6-b6fa-f21c15ab3ee9"}, {"parameters": {"task": "MDI3ODQzODIxOTE4MjA1ODY3NzM6MDow", "title": "Do Testing", "additionalFields": {}}, "name": "Google Tasks", "type": "n8n-nodes-base.googleTasks", "typeVersion": 1, "position": [550, 250], "credentials": {"googleTasksOAuth2Api": {"id": "24", "name": "Google Tasks 0auth creds"}}, "id": "c007f149-e912-4c92-a05e-faf60bc1b6d3"}, {"parameters": {"operation": "update", "title": "Do Testing", "task": "MDI3ODQzODIxOTE4MjA1ODY3NzM6MDow", "taskId": "={{$node[\"Google Tasks\"].json[\"id\"]}}", "updateFields": {"status": "completed"}}, "name": "Google Tasks1", "type": "n8n-nodes-base.googleTasks", "typeVersion": 1, "position": [720, 250], "credentials": {"googleTasksOAuth2Api": {"id": "24", "name": "Google Tasks 0auth creds"}}, "id": "a1adcb8e-7f47-448b-8dd6-208d815a8515"}, {"parameters": {"operation": "get", "title": "Do Testing", "task": "MDI3ODQzODIxOTE4MjA1ODY3NzM6MDow", "taskId": "={{$node[\"Google Tasks\"].json[\"id\"]}}"}, "name": "Google Tasks2", "type": "n8n-nodes-base.googleTasks", "typeVersion": 1, "position": [1070, 250], "credentials": {"googleTasksOAuth2Api": {"id": "24", "name": "Google Tasks 0auth creds"}}, "id": "ddc28328-f7f5-451e-9a9a-f3b4827cbb0d"}, {"parameters": {"operation": "delete", "title": "Do Testing", "task": "MDI3ODQzODIxOTE4MjA1ODY3NzM6MDow", "taskId": "={{$node[\"Google Tasks\"].json[\"id\"]}}"}, "name": "Google Tasks3", "type": "n8n-nodes-base.googleTasks", "typeVersion": 1, "position": [1230, 250], "credentials": {"googleTasksOAuth2Api": {"id": "24", "name": "Google Tasks 0auth creds"}}, "id": "f9028103-aa27-405c-a081-7ae669cf3e86"}, {"parameters": {"operation": "getAll", "title": "Do Testing", "task": "MDI3ODQzODIxOTE4MjA1ODY3NzM6MDow", "limit": 1, "additionalFields": {}}, "name": "Google Tasks4", "type": "n8n-nodes-base.googleTasks", "typeVersion": 1, "position": [890, 250], "credentials": {"googleTasksOAuth2Api": {"id": "24", "name": "Google Tasks 0auth creds"}}, "id": "96e66609-8e49-4bb0-b393-63ada8d98ccb"}], "connections": {"Google Tasks": {"main": [[{"node": "Google Tasks1", "type": "main", "index": 0}]]}, "Google Tasks1": {"main": [[{"node": "Google Tasks4", "type": "main", "index": 0}]]}, "Google Tasks2": {"main": [[{"node": "Google Tasks3", "type": "main", "index": 0}]]}, "Google Tasks4": {"main": [[{"node": "Google Tasks2", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Google Tasks", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}