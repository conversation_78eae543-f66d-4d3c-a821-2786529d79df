{"createdAt": "2021-02-25T13:03:37.942Z", "updatedAt": "2021-07-14T13:44:25.847Z", "id": "69", "name": "CoinGecko:Coin:get getAll  history candlesick market marketChart price ticker:Event:getAll", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [230, 430], "id": "a288470d-d151-4273-a50d-31c76cd1a2ce"}, {"parameters": {"resource": "event", "limit": 1, "options": {"upcoming_events_only": false}}, "name": "CoinGecko", "type": "n8n-nodes-base.coinGecko", "typeVersion": 1, "position": [400, 290], "notes": "CAP_RESULTS_LENGTH=1", "id": "b3b33313-3b7b-4569-aec4-77854b0c1980"}, {"parameters": {"limit": 1}, "name": "CoinGecko1", "type": "n8n-nodes-base.coinGecko", "typeVersion": 1, "position": [400, 550], "alwaysOutputData": true, "id": "e8e650b8-2122-4974-ba3e-74889528cd74"}, {"parameters": {"operation": "history", "coinId": "bitcoin", "date": "2021-02-24T23:00:00.000Z", "options": {"localization": false}}, "name": "CoinGecko2", "type": "n8n-nodes-base.coinGecko", "typeVersion": 1, "position": [770, 330], "alwaysOutputData": true, "id": "725e7306-f2dc-4c05-b6d8-ed75a394953b"}, {"parameters": {"operation": "get", "coinId": "bitcoin", "options": {"localization": false}}, "name": "CoinGecko3", "type": "n8n-nodes-base.coinGecko", "typeVersion": 1, "position": [630, 330], "alwaysOutputData": true, "id": "635e5c09-c4ca-4c05-ae93-7613f12c608a"}, {"parameters": {"operation": "candlestick", "baseCurrency": "bitcoin", "quoteCurrency": "eur", "days": "1"}, "name": "CoinGecko4", "type": "n8n-nodes-base.coinGecko", "typeVersion": 1, "position": [910, 330], "alwaysOutputData": true, "notes": "CAP_RESULTS_LENGTH=1", "id": "54fc5a2b-34a8-4f41-a9dc-b4b156b2448f"}, {"parameters": {"operation": "market", "baseCurrency": "btc", "limit": 1, "options": {}}, "name": "CoinGecko5", "type": "n8n-nodes-base.coinGecko", "typeVersion": 1, "position": [630, 480], "id": "43972652-a7ac-4eb2-92b3-d5ea6328b135"}, {"parameters": {"operation": "marketChart", "baseCurrency": "oxbitcoin", "quoteCurrency": "btc", "days": "1"}, "name": "CoinGecko6", "type": "n8n-nodes-base.coinGecko", "typeVersion": 1, "position": [630, 630], "notes": "CAP_RESULTS_LENGTH=1", "id": "857b16d9-deab-4a5c-9d6d-d79f83cc566e"}, {"parameters": {"operation": "price", "baseCurrencies": ["ethereum-classic"], "quoteCurrencies": ["btc"], "options": {}}, "name": "CoinGecko7", "type": "n8n-nodes-base.coinGecko", "typeVersion": 1, "position": [630, 780], "id": "c6d8a963-5bac-4841-a612-3d21b6ea71b6"}, {"parameters": {"operation": "ticker", "coinId": "bitcoin", "limit": 1, "options": {}}, "name": "CoinGecko8", "type": "n8n-nodes-base.coinGecko", "typeVersion": 1, "position": [630, 930], "notes": "IGNORED_PROPERTIES=target_coin_id", "id": "16097486-6aaf-476a-8c93-8bfbbd25b056"}], "connections": {"CoinGecko1": {"main": [[{"node": "CoinGecko3", "type": "main", "index": 0}, {"node": "CoinGecko5", "type": "main", "index": 0}, {"node": "CoinGecko6", "type": "main", "index": 0}, {"node": "CoinGecko7", "type": "main", "index": 0}, {"node": "CoinGecko8", "type": "main", "index": 0}]]}, "CoinGecko3": {"main": [[{"node": "CoinGecko2", "type": "main", "index": 0}]]}, "CoinGecko2": {"main": [[{"node": "CoinGecko4", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "CoinGecko1", "type": "main", "index": 0}, {"node": "CoinGecko", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}