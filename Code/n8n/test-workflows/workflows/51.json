{"createdAt": "2021-02-22T07:24:58.045Z", "updatedAt": "2021-06-07T09:03:59.431Z", "id": "51", "name": "Clockify:Project:create update get getAll:Tag:create update getAll delete:TimeEntry:create update get delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [290, 420], "id": "d407a858-2b0d-47c9-8004-c2aa1c1debb5"}, {"parameters": {"workspaceId": "60335ad3f24e660123d7fdee", "name": "=TestProject created {{(new Date).toGMTString()}}", "additionalFields": {}}, "name": "Clockify", "type": "n8n-nodes-base.clockify", "typeVersion": 1, "position": [500, 250], "credentials": {"clockifyApi": {"id": "36", "name": "Clockify creds"}}, "id": "b95e9179-0b3e-41de-9f8e-e5ed5fb969e9"}, {"parameters": {"operation": "update", "workspaceId": "60335ad3f24e660123d7fdee", "projectId": "={{$node[\"Clockify\"].json[\"id\"]}}", "updateFields": {"name": "=Updated {{$node[\"Clockify\"].json[\"name\"]}}"}}, "name": "Clockify1", "type": "n8n-nodes-base.clockify", "typeVersion": 1, "position": [650, 250], "credentials": {"clockifyApi": {"id": "36", "name": "Clockify creds"}}, "id": "0eec5307-ecca-4bc5-b40f-e21090c2941c"}, {"parameters": {"operation": "get", "workspaceId": "60335ad3f24e660123d7fdee", "projectId": "={{$node[\"Clockify\"].json[\"id\"]}}"}, "name": "Clockify2", "type": "n8n-nodes-base.clockify", "typeVersion": 1, "position": [800, 250], "credentials": {"clockifyApi": {"id": "36", "name": "Clockify creds"}}, "id": "dfaaf0b6-7657-42f7-b1a3-7bedbcdebfc0"}, {"parameters": {"operation": "getAll", "workspaceId": "60335ad3f24e660123d7fdee", "limit": 1, "additionalFields": {}}, "name": "Clockify3", "type": "n8n-nodes-base.clockify", "typeVersion": 1, "position": [950, 250], "credentials": {"clockifyApi": {"id": "36", "name": "Clockify creds"}}, "id": "e7648d2a-b3f5-4a72-97bc-4d923df7d32c"}, {"parameters": {"operation": "delete", "workspaceId": "60335ad3f24e660123d7fdee", "projectId": "={{$node[\"Clockify\"].json[\"id\"]}}"}, "name": "Clockify4", "type": "n8n-nodes-base.clockify", "typeVersion": 1, "position": [1100, 250], "notesInFlow": true, "credentials": {"clockifyApi": {"id": "36", "name": "Clockify creds"}}, "disabled": true, "notes": "delete require archive first", "id": "59497092-bb8d-41cb-b80e-8642e83fe865"}, {"parameters": {"resource": "tag", "workspaceId": "60335ad3f24e660123d7fdee", "name": "=TestTag{{$evaluateExpression(Math.random())}}"}, "name": "Clockify5", "type": "n8n-nodes-base.clockify", "typeVersion": 1, "position": [500, 400], "credentials": {"clockifyApi": {"id": "36", "name": "Clockify creds"}}, "id": "780628c7-b7cb-4657-9d59-add61d07e849"}, {"parameters": {"resource": "tag", "operation": "update", "workspaceId": "60335ad3f24e660123d7fdee", "tagId": "={{$node[\"Clockify5\"].json[\"id\"]}}", "updateFields": {"name": "=UpdatedTag{{$evaluateExpression(Math.random())}}"}}, "name": "Clockify6", "type": "n8n-nodes-base.clockify", "typeVersion": 1, "position": [830, 400], "credentials": {"clockifyApi": {"id": "36", "name": "Clockify creds"}}, "id": "43d5352c-56b1-4c6a-93c2-e09569209485"}, {"parameters": {"resource": "tag", "operation": "getAll", "workspaceId": "60335ad3f24e660123d7fdee", "limit": 1, "additionalFields": {}}, "name": "Clockify7", "type": "n8n-nodes-base.clockify", "typeVersion": 1, "position": [980, 400], "credentials": {"clockifyApi": {"id": "36", "name": "Clockify creds"}}, "id": "28ccc080-049d-461f-ab30-ff877b0e0a92"}, {"parameters": {"resource": "tag", "operation": "delete", "workspaceId": "60335ad3f24e660123d7fdee", "tagId": "={{$node[\"Clockify5\"].json[\"id\"]}}"}, "name": "Clockify8", "type": "n8n-nodes-base.clockify", "typeVersion": 1, "position": [1130, 400], "credentials": {"clockifyApi": {"id": "36", "name": "Clockify creds"}}, "id": "a3cc2dea-5a89-44ac-92da-bcfbeac654a2"}, {"parameters": {"resource": "timeEntry", "workspaceId": "60335ad3f24e660123d7fdee", "start": "={{(new Date()).toISOString()}}", "additionalFields": {}}, "name": "Clockify9", "type": "n8n-nodes-base.clockify", "typeVersion": 1, "position": [500, 560], "credentials": {"clockifyApi": {"id": "36", "name": "Clockify creds"}}, "id": "2297d639-4261-46d0-b269-bd240b732fca"}, {"parameters": {"resource": "timeEntry", "operation": "update", "workspaceId": "60335ad3f24e660123d7fdee", "timeEntryId": "={{$node[\"Clockify9\"].json[\"id\"]}}", "updateFields": {"description": "Updated time entry"}}, "name": "Clockify10", "type": "n8n-nodes-base.clockify", "typeVersion": 1, "position": [650, 560], "credentials": {"clockifyApi": {"id": "36", "name": "Clockify creds"}}, "id": "936d9faf-b8eb-4f28-aa6e-4ce43bf119ae"}, {"parameters": {"resource": "timeEntry", "operation": "get", "workspaceId": "60335ad3f24e660123d7fdee", "timeEntryId": "={{$node[\"Clockify9\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Clockify11", "type": "n8n-nodes-base.clockify", "typeVersion": 1, "position": [800, 560], "credentials": {"clockifyApi": {"id": "36", "name": "Clockify creds"}}, "id": "1b7e1644-9988-4794-91e4-278ec8dba2c4"}, {"parameters": {"resource": "timeEntry", "operation": "delete", "workspaceId": "60335ad3f24e660123d7fdee", "timeEntryId": "={{$node[\"Clockify9\"].json[\"id\"]}}"}, "name": "Clockify12", "type": "n8n-nodes-base.clockify", "typeVersion": 1, "position": [950, 560], "credentials": {"clockifyApi": {"id": "36", "name": "Clockify creds"}}, "id": "8af95310-ac19-43b8-a8c1-df89b2a715cc"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second", "type": "n8n-nodes-base.function", "position": [660, 400], "typeVersion": 1, "id": "be2fd4bd-f014-48b8-9e52-f18cc6595477"}], "connections": {"Clockify": {"main": [[{"node": "Clockify1", "type": "main", "index": 0}]]}, "Clockify1": {"main": [[{"node": "Clockify2", "type": "main", "index": 0}]]}, "Clockify2": {"main": [[{"node": "Clockify3", "type": "main", "index": 0}]]}, "Clockify3": {"main": [[{"node": "Clockify4", "type": "main", "index": 0}]]}, "Clockify5": {"main": [[{"node": "Sleep 0.5 second", "type": "main", "index": 0}]]}, "Clockify6": {"main": [[{"node": "Clockify7", "type": "main", "index": 0}]]}, "Clockify7": {"main": [[{"node": "Clockify8", "type": "main", "index": 0}]]}, "Clockify9": {"main": [[{"node": "Clockify10", "type": "main", "index": 0}]]}, "Clockify10": {"main": [[{"node": "Clockify11", "type": "main", "index": 0}]]}, "Clockify11": {"main": [[{"node": "Clockify12", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Clockify", "type": "main", "index": 0}, {"node": "Clockify5", "type": "main", "index": 0}, {"node": "Clockify9", "type": "main", "index": 0}]]}, "Sleep 0.5 second": {"main": [[{"node": "Clockify6", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}