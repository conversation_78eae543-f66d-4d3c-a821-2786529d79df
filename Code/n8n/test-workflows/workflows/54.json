{"createdAt": "2021-02-22T10:00:31.563Z", "updatedAt": "2021-02-22T10:50:12.567Z", "id": "54", "name": "InvoiceNinja:Client:create getAll get delete:Expense:create getAll get delete:Invoice:create getAll get email delete:Payment:create get getAll delete:Quote:crea te getAll get email delete:Task:create getAll get delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 850], "id": "1d47bc56-1b68-4ff0-8007-ae06b9c29610"}, {"parameters": {"additionalFields": {"clientName": "=TestClient{{Date.now()}}"}}, "name": "Invoice Ninja", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [500, 390], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "5b32c13a-c05e-45b6-8fdc-6174ebc941ea"}, {"parameters": {"operation": "getAll", "limit": 1, "options": {}}, "name": "Invoice Ninja1", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [650, 390], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "1db90b48-219d-4e96-a782-7ff5823e9ee5"}, {"parameters": {"operation": "get", "clientId": "={{$node[\"Invoice Ninja\"].json[\"id\"]}}", "options": {}}, "name": "Invoice Ninja2", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [800, 390], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "f3e516a2-8e52-4fdb-80ba-932c960f6976"}, {"parameters": {"operation": "delete", "clientId": "={{$node[\"Invoice Ninja\"].json[\"id\"]}}"}, "name": "Invoice Ninja3", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [950, 390], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "cd219ff6-4105-4690-8d9c-918d8f35cdd9"}, {"parameters": {"resource": "expense", "additionalFields": {"amount": 100}}, "name": "Invoice Ninja4", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [500, 550], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "1d666aef-f593-42d3-9b61-fb54fe36bf0e"}, {"parameters": {"resource": "expense", "operation": "getAll", "limit": 1}, "name": "Invoice Ninja5", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [650, 550], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "42b27738-9c2e-41e5-ba79-93efb0087729"}, {"parameters": {"resource": "expense", "operation": "get", "expenseId": "={{$node[\"Invoice Ninja4\"].json[\"id\"]}}"}, "name": "Invoice Ninja6", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [800, 550], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "578e0bbd-f7a9-4640-a3ad-de936eaacd79"}, {"parameters": {"resource": "expense", "operation": "delete", "expenseId": "={{$node[\"Invoice Ninja4\"].json[\"id\"]}}"}, "name": "Invoice Ninja7", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [950, 550], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "994c5b63-8463-49db-b22f-5019e9c0917b"}, {"parameters": {"resource": "invoice", "additionalFields": {"email": "<EMAIL>"}}, "name": "Invoice Ninja8", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [500, 710], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "4153127d-468e-4c08-9f06-768e4b192403"}, {"parameters": {"resource": "invoice", "operation": "getAll", "limit": 1, "options": {}}, "name": "Invoice Ninja9", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [1250, 710], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "9c236165-1824-44f9-8614-00e0a17a1103"}, {"parameters": {"resource": "invoice", "operation": "get", "invoiceId": "={{$node[\"Invoice Ninja8\"].json[\"id\"]}}", "options": {}}, "name": "Invoice Ninja10", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [1400, 710], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "44b48920-a293-4ca9-9033-178a18b3bf21"}, {"parameters": {"resource": "invoice", "operation": "email", "invoiceId": "={{$node[\"Invoice Ninja8\"].json[\"id\"]}}"}, "name": "Invoice Ninja11", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [1550, 710], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "aa19e0ab-0cbb-496c-a110-d16119d8a998"}, {"parameters": {"resource": "invoice", "operation": "delete", "invoiceId": "={{$node[\"Invoice Ninja8\"].json[\"id\"]}}"}, "name": "Invoice Ninja12", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [1700, 710], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "9bb13cc8-610b-42c1-9f6d-8971091ae507"}, {"parameters": {"resource": "payment", "invoice": "={{$node[\"Invoice Ninja8\"].json[\"id\"]}}", "amount": 102, "additionalFields": {}}, "name": "Invoice Ninja14", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [650, 820], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "d19e4bad-d79d-4012-b32a-257cb0ebaa05"}, {"parameters": {"resource": "quote", "additionalFields": {"email": "<EMAIL>"}}, "name": "Invoice Ninja15", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [500, 1020], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "c00c4d59-128a-41eb-9518-b132948d1054"}, {"parameters": {"resource": "quote", "operation": "getAll", "limit": 1, "options": {}}, "name": "Invoice Ninja16", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [650, 1020], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "0b4a39fa-9360-4011-b989-a42fc7d283ee"}, {"parameters": {"resource": "quote", "operation": "get", "quoteId": "={{$node[\"Invoice Ninja15\"].json[\"id\"]}}", "options": {}}, "name": "Invoice Ninja17", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [800, 1020], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "3d2b2f4b-eed9-4419-849d-cae56ae9cba3"}, {"parameters": {"resource": "quote", "operation": "email", "quoteId": "={{$node[\"Invoice Ninja15\"].json[\"id\"]}}"}, "name": "Invoice Ninja18", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [950, 1020], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "f99b80b6-5dda-4bc6-b2c4-1a43c715f23c"}, {"parameters": {"resource": "quote", "operation": "delete", "quoteId": "={{$node[\"Invoice Ninja15\"].json[\"id\"]}}"}, "name": "Invoice Ninja19", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [1100, 1020], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "0a02025d-782e-4f1f-b17a-618449393a87"}, {"parameters": {"resource": "task", "additionalFields": {}}, "name": "Invoice Ninja20", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [500, 1180], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "42fbd794-c557-4b30-863a-fbbe0012772d"}, {"parameters": {"resource": "task", "operation": "getAll", "limit": 1, "options": {}}, "name": "Invoice Ninja21", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [650, 1180], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "dd74acb7-ad5f-4a04-8b54-5d57846031dc"}, {"parameters": {"resource": "task", "operation": "get", "taskId": "={{$node[\"Invoice Ninja20\"].json[\"id\"]}}", "options": {}}, "name": "Invoice Ninja22", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [800, 1180], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "2066f094-5f18-4e14-9349-4ae32a803a4a"}, {"parameters": {"resource": "task", "operation": "delete", "taskId": "={{$node[\"Invoice Ninja20\"].json[\"id\"]}}"}, "name": "Invoice Ninja23", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [950, 1180], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "7f0705d7-cb3d-44d5-a47b-65605b653f4a"}, {"parameters": {"resource": "payment", "operation": "get", "paymentId": "={{$node[\"Invoice Ninja14\"].json[\"id\"]}}", "options": {}}, "name": "Invoice Ninja24", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [800, 820], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "a3f3f226-e931-4185-891c-b98f554a1634"}, {"parameters": {"resource": "payment", "operation": "getAll", "limit": 1, "options": {}}, "name": "Invoice Ninja25", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [950, 820], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "e4660139-dc2c-4555-88aa-ab38b4ac1f31"}, {"parameters": {"resource": "payment", "operation": "delete", "paymentId": "={{$node[\"Invoice Ninja14\"].json[\"id\"]}}"}, "name": "Invoice Ninja26", "type": "n8n-nodes-base.invoiceNinja", "typeVersion": 1, "position": [1100, 820], "credentials": {"invoiceNinjaApi": {"id": "39", "name": "Invoice Ninja creds "}}, "id": "465cbca5-b9b9-4e70-b245-a80defd3c7cd"}], "connections": {"Invoice Ninja": {"main": [[{"node": "Invoice Ninja1", "type": "main", "index": 0}]]}, "Invoice Ninja1": {"main": [[{"node": "Invoice Ninja2", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Invoice Ninja4", "type": "main", "index": 0}, {"node": "Invoice Ninja8", "type": "main", "index": 0}, {"node": "Invoice Ninja15", "type": "main", "index": 0}, {"node": "Invoice Ninja", "type": "main", "index": 0}, {"node": "Invoice Ninja20", "type": "main", "index": 0}]]}, "Invoice Ninja3": {"main": [[]]}, "Invoice Ninja2": {"main": [[{"node": "Invoice Ninja3", "type": "main", "index": 0}]]}, "Invoice Ninja4": {"main": [[{"node": "Invoice Ninja5", "type": "main", "index": 0}]]}, "Invoice Ninja5": {"main": [[{"node": "Invoice Ninja6", "type": "main", "index": 0}]]}, "Invoice Ninja6": {"main": [[{"node": "Invoice Ninja7", "type": "main", "index": 0}]]}, "Invoice Ninja8": {"main": [[{"node": "Invoice Ninja14", "type": "main", "index": 0}]]}, "Invoice Ninja9": {"main": [[{"node": "Invoice Ninja10", "type": "main", "index": 0}]]}, "Invoice Ninja10": {"main": [[{"node": "Invoice Ninja11", "type": "main", "index": 0}]]}, "Invoice Ninja11": {"main": [[{"node": "Invoice Ninja12", "type": "main", "index": 0}]]}, "Invoice Ninja15": {"main": [[{"node": "Invoice Ninja16", "type": "main", "index": 0}]]}, "Invoice Ninja16": {"main": [[{"node": "Invoice Ninja17", "type": "main", "index": 0}]]}, "Invoice Ninja17": {"main": [[{"node": "Invoice Ninja18", "type": "main", "index": 0}]]}, "Invoice Ninja18": {"main": [[{"node": "Invoice Ninja19", "type": "main", "index": 0}]]}, "Invoice Ninja20": {"main": [[{"node": "Invoice Ninja21", "type": "main", "index": 0}]]}, "Invoice Ninja21": {"main": [[{"node": "Invoice Ninja22", "type": "main", "index": 0}]]}, "Invoice Ninja22": {"main": [[{"node": "Invoice Ninja23", "type": "main", "index": 0}]]}, "Invoice Ninja14": {"main": [[{"node": "Invoice Ninja24", "type": "main", "index": 0}]]}, "Invoice Ninja24": {"main": [[{"node": "Invoice Ninja25", "type": "main", "index": 0}]]}, "Invoice Ninja25": {"main": [[{"node": "Invoice Ninja26", "type": "main", "index": 0}]]}, "Invoice Ninja26": {"main": [[{"node": "Invoice Ninja9", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}