{"name": "Sub-node errors:model", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [0, 0], "id": "077f1a05-32d1-4a9e-abd9-9ef4a1bcfe02", "name": "When clicking ‘Test workflow’"}, {"parameters": {"promptType": "define", "text": "Hey"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [200, 0], "id": "5c3f329d-6481-41c5-9747-e9fd53815ca6", "name": "Basic LLM Chain", "onError": "continueErrorOutput"}, {"parameters": {"model": "=gpt-4o-mini123", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [280, 220], "id": "00cb1410-f04f-4b2b-b42e-f2c275ba0b4a", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "Zak03cqeLUOsgkFI", "name": "OpenAi account"}}}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "56e5e220-edbd-4d7a-97f3-96b8cf952202", "meta": {"instanceId": "27cc9b56542ad45b38725555722c50a1c3fee1670bbb67980558314ee08517c4"}, "id": "255", "tags": []}