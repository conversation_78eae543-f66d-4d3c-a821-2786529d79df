{"createdAt": "2021-02-25T09:43:01.461Z", "updatedAt": "2021-02-25T09:43:01.461Z", "id": "65", "name": "Spontit:Push:create", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "e33d511e-ced8-4880-a890-e943c6aab5dc"}, {"parameters": {"content": "=Content {{Date.now()}}", "additionalFields": {"pushTitle": "=TestNotif {{Date.now()}}"}}, "name": "Spontit", "type": "n8n-nodes-base.spontit", "typeVersion": 1, "position": [470, 300], "credentials": {"spontitApi": {"id": "54", "name": "Spontit creds"}}, "id": "06d02833-8870-4d12-a60f-59c1763f0361"}, {"parameters": {"url": "={{$node[\"Spontit\"].json[\"message\"].replace(\"Pushed! Available at \",\"\")}}", "responseFormat": "string", "options": {}}, "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [650, 300], "id": "e22a1da8-01dc-476f-b449-527888f5d58e"}, {"parameters": {"extractionValues": {"values": [{"key": "data-subscription", "cssSelector": "#loadApp_script[data-subscription]", "returnValue": "attribute", "attribute": "data-subscription"}]}, "options": {}}, "name": "HTML Extract", "type": "n8n-nodes-base.htmlExtract", "typeVersion": 1, "position": [850, 300], "id": "641a91ef-f7a2-4f1a-8876-bd0b0cf133cb"}], "connections": {"Start": {"main": [[{"node": "Spontit", "type": "main", "index": 0}]]}, "Spontit": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTML Extract", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}