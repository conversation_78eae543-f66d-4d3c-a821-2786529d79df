{"createdAt": "2021-02-17T09:32:18.514Z", "updatedAt": "2021-05-21T11:18:41.858Z", "id": "27", "name": "ClickUp:Task:create update member getAll get delete:List:create update customFields member getAll get delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [160, 420], "id": "98e4c4b0-c5a3-465e-b621-662cd30ba485"}, {"parameters": {"resource": "folder", "team": "4651110", "space": "8716115", "name": "=TestFolder{{Date.now()}}"}, "name": "ClickUp", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [310, 310], "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "5e4c4ffe-c474-4ee1-a0b7-ca7023c3308d"}, {"parameters": {"resource": "list", "operation": "create", "team": "4651110", "space": "8716115", "folder": "={{$json[\"id\"]}}", "name": "=TestList{{Date.now()}}", "additionalFields": {}}, "name": "ClickUp1", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [580, 340], "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "84597659-cbe6-468b-9229-7eb3e7fc4481"}, {"parameters": {"resource": "list", "operation": "update", "team": "4651110", "space": "8716115", "folder": "={{$node[\"ClickUp\"].json[\"id\"]}}", "list": "={{$node[\"ClickUp1\"].json[\"id\"]}}", "updateFields": {"name": "=UpdateTestList{{Date.now()}}"}}, "name": "ClickUp2", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [860, 660], "retryOnFail": true, "waitBetweenTries": 500, "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "37ede561-0fc6-4a18-8909-fd7815004a6c"}, {"parameters": {"resource": "list", "team": "4651110", "space": "8716115", "folder": "={{$json[\"folder\"][\"id\"]}}", "list": "={{$json[\"id\"]}}"}, "name": "ClickUp3", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [1100, 660], "alwaysOutputData": true, "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "7653a4d3-5046-4174-a2a2-651003ef4a11"}, {"parameters": {"resource": "list", "operation": "member", "id": "={{$node[\"ClickUp2\"].json[\"id\"]}}", "returnAll": false, "limit": 1}, "name": "ClickUp4", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [1400, 660], "alwaysOutputData": true, "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "b4f551cc-edc7-4fb5-93d2-f2923b43e773"}, {"parameters": {"resource": "list", "operation": "getAll", "team": "4651110", "space": "8716115", "folder": "={{$node[\"ClickUp\"].json[\"id\"]}}", "limit": 1, "filters": {}}, "name": "ClickUp5", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [1730, 660], "alwaysOutputData": true, "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "2aaf834a-4856-4851-a723-30e92aef06da"}, {"parameters": {"resource": "list", "operation": "get", "team": "4651110", "space": "8716115", "folder": "={{$node[\"ClickUp\"].json[\"id\"]}}", "list": "={{$node[\"ClickUp1\"].json[\"id\"]}}"}, "name": "ClickUp6", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [2050, 660], "alwaysOutputData": true, "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "8f3cd1df-88e8-490e-956e-fa526e9efc06"}, {"parameters": {"resource": "list", "operation": "delete", "team": "4651110", "space": "8716115", "folder": "={{$node[\"ClickUp\"].json[\"id\"]}}", "list": "={{$node[\"ClickUp1\"].json[\"id\"]}}"}, "name": "ClickUp7", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [2380, 660], "alwaysOutputData": true, "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "6927c6fa-e5ff-4645-9a41-323ea0f4e0f3"}, {"parameters": {"resource": "folder", "operation": "delete", "team": "4651110", "space": "8716115", "folder": "={{$node[\"ClickUp\"].json[\"id\"]}}"}, "name": "ClickUp8", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [2670, 660], "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "30e40940-f242-4989-b107-6c570b7383b4"}, {"parameters": {"team": "4651110", "space": "8716115", "folder": "={{$json[\"folder\"][\"id\"]}}", "list": "={{$json[\"id\"]}}", "name": "=testTask{{Date.now()}}", "additionalFields": {}}, "name": "ClickUp9", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [850, 460], "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "eaa614eb-b42a-4e87-8778-72f56553f57d"}, {"parameters": {"operation": "update", "id": "={{$json[\"id\"]}}", "updateFields": {"name": "={{$json[\"name\"]}}Updated{{Date.now()}}"}}, "name": "ClickUp10", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [1100, 460], "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "5958b2ae-0eab-4b4d-ad0a-8eb925b6948c"}, {"parameters": {"operation": "member", "id": "={{$json[\"id\"]}}", "returnAll": false, "limit": 1}, "name": "ClickUp11", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [1400, 460], "alwaysOutputData": true, "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "9e30c9e7-cf31-4520-a08e-a90990cecd9e"}, {"parameters": {"operation": "getAll", "team": "4651110", "space": "8716115", "folder": "={{$node[\"ClickUp\"].json[\"id\"]}}", "list": "={{$node[\"ClickUp1\"].json[\"id\"]}}", "returnAll": false, "limit": 1, "filters": {}}, "name": "ClickUp12", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [1740, 460], "alwaysOutputData": true, "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "75a7b4ee-7d37-4384-8d6b-1f0785d73575"}, {"parameters": {"operation": "get", "id": "={{$node[\"ClickUp9\"].json[\"id\"]}}"}, "name": "ClickUp13", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [2050, 460], "alwaysOutputData": true, "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "6f52a60f-2a79-449e-8a8e-0bd4005bdfbf"}, {"parameters": {"operation": "delete", "id": "={{$node[\"ClickUp9\"].json[\"id\"]}}"}, "name": "ClickUp14", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [2380, 460], "alwaysOutputData": true, "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "2a32012b-e6e5-4b42-8cfd-95318e12b81d"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds", "type": "n8n-nodes-base.function", "position": [440, 310], "typeVersion": 1, "id": "b1a45946-ff9a-483d-99b3-42f6dc8d6471"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds1", "type": "n8n-nodes-base.function", "position": [710, 340], "typeVersion": 1, "id": "f780613f-41a1-4c01-b7b5-0143f87c6f3a"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds2", "type": "n8n-nodes-base.function", "position": [980, 460], "typeVersion": 1, "id": "51af53ab-592e-4417-9601-92c4714c1980"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds3", "type": "n8n-nodes-base.function", "position": [1240, 460], "typeVersion": 1, "id": "d5bfeee6-1a7e-4353-a20f-4ed4b5cba689"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds4", "type": "n8n-nodes-base.function", "position": [1570, 460], "typeVersion": 1, "id": "9ab01733-73f7-498e-a969-9515ad2b5e32"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds5", "type": "n8n-nodes-base.function", "position": [1890, 460], "typeVersion": 1, "id": "aa025cf9-c5e5-4f3b-bb62-3197d973011c"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds6", "type": "n8n-nodes-base.function", "position": [2210, 460], "typeVersion": 1, "id": "9d4d54c4-f642-4e4c-b717-10387a49989d"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds7", "type": "n8n-nodes-base.function", "position": [980, 660], "typeVersion": 1, "id": "d724d742-235c-43df-ba81-619d2f840dd5"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds8", "type": "n8n-nodes-base.function", "position": [1240, 660], "typeVersion": 1, "id": "d0b9f5e5-c66c-479b-bf37-c04b2643742f"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds9", "type": "n8n-nodes-base.function", "position": [1550, 660], "typeVersion": 1, "id": "3950ee56-9ec9-4e78-ada3-857ab17aba27"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds10", "type": "n8n-nodes-base.function", "position": [1890, 660], "typeVersion": 1, "id": "c3728ed7-486c-4a6d-865e-08a21426368c"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds11", "type": "n8n-nodes-base.function", "position": [2210, 660], "typeVersion": 1, "id": "021dbc08-7d4e-4867-808c-58f6844c6634"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds12", "type": "n8n-nodes-base.function", "position": [2530, 660], "typeVersion": 1, "id": "be53074b-6bfd-4fef-b359-034dd3be06f4"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds13", "type": "n8n-nodes-base.function", "position": [700, 660], "typeVersion": 1, "id": "ce69e0ce-50d7-416f-9349-dc3b5e378fd3"}], "connections": {"Start": {"main": [[{"node": "ClickUp", "type": "main", "index": 0}]]}, "ClickUp": {"main": [[{"node": "Sleep 8 Seconds", "type": "main", "index": 0}]]}, "ClickUp1": {"main": [[{"node": "Sleep 8 Seconds1", "type": "main", "index": 0}]]}, "ClickUp2": {"main": [[{"node": "Sleep 8 Seconds7", "type": "main", "index": 0}]]}, "ClickUp3": {"main": [[{"node": "Sleep 8 Seconds8", "type": "main", "index": 0}]]}, "ClickUp4": {"main": [[{"node": "Sleep 8 Seconds9", "type": "main", "index": 0}]]}, "ClickUp5": {"main": [[{"node": "Sleep 8 Seconds10", "type": "main", "index": 0}]]}, "ClickUp6": {"main": [[{"node": "Sleep 8 Seconds11", "type": "main", "index": 0}]]}, "ClickUp7": {"main": [[{"node": "Sleep 8 Seconds12", "type": "main", "index": 0}]]}, "ClickUp9": {"main": [[{"node": "Sleep 8 Seconds2", "type": "main", "index": 0}]]}, "ClickUp10": {"main": [[{"node": "Sleep 8 Seconds3", "type": "main", "index": 0}]]}, "ClickUp11": {"main": [[{"node": "Sleep 8 Seconds4", "type": "main", "index": 0}]]}, "ClickUp12": {"main": [[{"node": "Sleep 8 Seconds5", "type": "main", "index": 0}]]}, "ClickUp13": {"main": [[{"node": "Sleep 8 Seconds6", "type": "main", "index": 0}]]}, "ClickUp14": {"main": [[{"node": "Sleep 8 Seconds13", "type": "main", "index": 0}]]}, "Sleep 8 Seconds": {"main": [[{"node": "ClickUp1", "type": "main", "index": 0}]]}, "Sleep 8 Seconds1": {"main": [[{"node": "ClickUp9", "type": "main", "index": 0}]]}, "Sleep 8 Seconds2": {"main": [[{"node": "ClickUp10", "type": "main", "index": 0}]]}, "Sleep 8 Seconds3": {"main": [[{"node": "ClickUp11", "type": "main", "index": 0}]]}, "Sleep 8 Seconds4": {"main": [[{"node": "ClickUp12", "type": "main", "index": 0}]]}, "Sleep 8 Seconds5": {"main": [[{"node": "ClickUp13", "type": "main", "index": 0}]]}, "Sleep 8 Seconds6": {"main": [[{"node": "ClickUp14", "type": "main", "index": 0}]]}, "Sleep 8 Seconds7": {"main": [[{"node": "ClickUp3", "type": "main", "index": 0}]]}, "Sleep 8 Seconds8": {"main": [[{"node": "ClickUp4", "type": "main", "index": 0}]]}, "Sleep 8 Seconds9": {"main": [[{"node": "ClickUp5", "type": "main", "index": 0}]]}, "Sleep 8 Seconds10": {"main": [[{"node": "ClickUp6", "type": "main", "index": 0}]]}, "Sleep 8 Seconds11": {"main": [[{"node": "ClickUp7", "type": "main", "index": 0}]]}, "Sleep 8 Seconds12": {"main": [[{"node": "ClickUp8", "type": "main", "index": 0}]]}, "Sleep 8 Seconds13": {"main": [[{"node": "ClickUp2", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}