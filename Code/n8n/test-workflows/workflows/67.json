{"createdAt": "2021-02-25T11:13:34.019Z", "updatedAt": "2021-05-03T07:55:48.432Z", "id": "67", "name": " Twist:Channel:create update get getAll archive unarchive delete:MessageConversation:create get getAll update delete:Thread:create get update getAll delete:Comment:create get update getAll delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "27f84602-beb4-4f88-8218-2a97b75c2224"}, {"parameters": {"resource": "channel", "workspaceId": 164330, "name": "=TestChannel{{Date.now()}}", "additionalFields": {}}, "name": "Twist", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [400, 300], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "5f8b0940-95b9-4ceb-b582-978ecc2a3054"}, {"parameters": {"resource": "channel", "operation": "update", "channelId": "={{$node[\"Twist\"].json[\"id\"]}}", "updateFields": {"name": "=UpdatedChannel{{Date.now()}}"}}, "name": "Twist1", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [1300, 300], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "ae38c81e-4500-4794-a2be-dd13baec5a12"}, {"parameters": {"resource": "channel", "operation": "get", "channelId": "={{$node[\"Twist\"].json[\"id\"]}}"}, "name": "Twist2", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [1450, 300], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "13a50551-66d2-41c8-a223-b668d1760bb9"}, {"parameters": {"resource": "channel", "operation": "getAll", "workspaceId": 164330, "limit": 1, "filters": {}}, "name": "Twist3", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [1600, 300], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "ccd8ba04-a775-4726-8fd5-6bd7aab479be"}, {"parameters": {"workspaceId": 164330, "conversationId": 1067233, "content": "=Message {{Date.now()}}", "additionalFields": {}}, "name": "Twist4", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [400, 450], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "d71aab63-c8fc-4707-b1ef-e65c86b57889"}, {"parameters": {"resource": "channel", "operation": "archive", "channelId": "={{$node[\"Twist\"].json[\"id\"]}}"}, "name": "Twist5", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [1750, 300], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "ad43fda9-1a8c-4742-a6f4-e06fd9ca6b71"}, {"parameters": {"resource": "channel", "operation": "unarchive", "channelId": "={{$node[\"Twist\"].json[\"id\"]}}"}, "name": "Twist6", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [1900, 300], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "7694c0d8-a552-4447-b596-4fc4028286ca"}, {"parameters": {"resource": "channel", "operation": "archive", "channelId": "={{$node[\"Twist\"].json[\"id\"]}}"}, "name": "Twist7", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [2050, 300], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "117e67bf-96e6-45e2-815a-39a061c0b70f"}, {"parameters": {"resource": "channel", "operation": "delete", "channelId": "={{$node[\"Twist\"].json[\"id\"]}}"}, "name": "Twist8", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [2200, 300], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "9f2ac87f-563d-46ea-9e84-20106ae3d298"}, {"parameters": {"operation": "get", "id": "={{$node[\"Twist4\"].json[\"id\"]}}"}, "name": "Twist9", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [600, 450], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "f18044fe-f54c-4508-8d7b-fd207d7592d7"}, {"parameters": {"operation": "getAll", "workspaceId": 164330, "conversationId": 1067233, "additionalFields": {"limit": 1}}, "name": "Twist10", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [800, 450], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "9af8b264-9fd4-42d2-adb3-11c31cc4a2bc"}, {"parameters": {"operation": "update", "id": "={{$node[\"Twist4\"].json[\"id\"]}}", "updateFields": {"actionsUi": {"actionValues": [{"action": "open_url", "button_text": "community ?", "type": "action", "url": "http://community.n8n.io"}]}}}, "name": "Twist11", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [1000, 450], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "bee483bf-33e8-4dda-992a-facd5cf7e678"}, {"parameters": {"operation": "delete", "id": "={{$node[\"Twist4\"].json[\"id\"]}}"}, "name": "Twist12", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [1200, 450], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "7069faaa-b9f0-4a53-8a0f-8496697b8a0c"}, {"parameters": {"resource": "thread", "channelId": "={{$node[\"Twist\"].json[\"id\"]}}", "title": "=Thread{{Date.now()}}", "content": "test", "additionalFields": {}}, "name": "Twist13", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [550, 150], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "b7484a76-76e7-4b7f-9702-efd247ce3b7f"}, {"parameters": {"resource": "thread", "operation": "get", "threadId": "={{$node[\"Twist13\"].json[\"id\"]}}"}, "name": "Twist14", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [700, 150], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "6d2214c7-4046-48ad-8968-bd94b4fead5f"}, {"parameters": {"resource": "thread", "operation": "update", "threadId": "={{$node[\"Twist13\"].json[\"id\"]}}", "updateFields": {"content": "Updated Content"}}, "name": "Twist15", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [850, 150], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "f18cc9f8-8019-45a6-b129-4503d5e8aabe"}, {"parameters": {"resource": "thread", "operation": "delete", "threadId": "={{$node[\"Twist13\"].json[\"id\"]}}"}, "name": "Twist16", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [1150, 150], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "0d712781-9323-48c6-9cc7-72cb43bb73d1"}, {"parameters": {"resource": "thread", "operation": "getAll", "channelId": "={{$node[\"Twist\"].json[\"id\"]}}", "limit": 1, "filters": {}}, "name": "Twist17", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [1000, 150], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "6921bbfc-1f9c-4939-874d-d507221603f1"}, {"parameters": {"resource": "comment", "threadId": "={{$node[\"Twist13\"].json[\"id\"]}}", "content": "=Comment{{Date.now()}}", "additionalFields": {}}, "name": "Twist18", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [700, 0], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "0c6fb7ef-6ed1-4361-860e-757cbfcaf1d6"}, {"parameters": {"resource": "comment", "operation": "get", "commentId": "={{$node[\"Twist18\"].json[\"id\"]}}"}, "name": "Twist19", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [850, 0], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "6a268279-ca4a-4a06-ab80-79a9a7eb46bd"}, {"parameters": {"resource": "comment", "operation": "update", "commentId": "={{$node[\"Twist18\"].json[\"id\"]}}", "updateFields": {"content": "Updated Content"}}, "name": "Twist20", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [1000, 0], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "21e3d0e7-7334-4540-88ed-bacb111bb3c4"}, {"parameters": {"resource": "comment", "operation": "getAll", "threadId": "={{$node[\"Twist13\"].json[\"id\"]}}", "limit": 1, "filters": {}}, "name": "Twist21", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [1150, 0], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "1a4f872d-f05f-474f-a7d9-97f2f046533b"}, {"parameters": {"resource": "comment", "operation": "delete", "commentId": "={{$node[\"Twist18\"].json[\"id\"]}}"}, "name": "Twist22", "type": "n8n-nodes-base.twist", "typeVersion": 1, "position": [1300, 0], "credentials": {"twistOAuth2Api": {"id": "58", "name": "Twist OAuth2 creds"}}, "id": "d2496300-155c-4bd3-8acd-1d3c068dba37"}], "connections": {"Twist": {"main": [[{"node": "Twist13", "type": "main", "index": 0}]]}, "Twist1": {"main": [[{"node": "Twist2", "type": "main", "index": 0}]]}, "Twist2": {"main": [[{"node": "Twist3", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Twist", "type": "main", "index": 0}, {"node": "Twist4", "type": "main", "index": 0}]]}, "Twist3": {"main": [[{"node": "Twist5", "type": "main", "index": 0}]]}, "Twist5": {"main": [[{"node": "Twist6", "type": "main", "index": 0}]]}, "Twist6": {"main": [[{"node": "Twist7", "type": "main", "index": 0}]]}, "Twist7": {"main": [[{"node": "Twist8", "type": "main", "index": 0}]]}, "Twist4": {"main": [[{"node": "Twist9", "type": "main", "index": 0}]]}, "Twist9": {"main": [[{"node": "Twist10", "type": "main", "index": 0}]]}, "Twist10": {"main": [[{"node": "Twist11", "type": "main", "index": 0}]]}, "Twist11": {"main": [[{"node": "Twist12", "type": "main", "index": 0}]]}, "Twist13": {"main": [[{"node": "Twist18", "type": "main", "index": 0}]]}, "Twist14": {"main": [[{"node": "Twist15", "type": "main", "index": 0}]]}, "Twist15": {"main": [[{"node": "Twist17", "type": "main", "index": 0}]]}, "Twist16": {"main": [[{"node": "Twist1", "type": "main", "index": 0}]]}, "Twist17": {"main": [[{"node": "Twist16", "type": "main", "index": 0}]]}, "Twist18": {"main": [[{"node": "Twist19", "type": "main", "index": 0}]]}, "Twist19": {"main": [[{"node": "Twist20", "type": "main", "index": 0}]]}, "Twist20": {"main": [[{"node": "Twist21", "type": "main", "index": 0}]]}, "Twist21": {"main": [[{"node": "Twist22", "type": "main", "index": 0}]]}, "Twist22": {"main": [[{"node": "Twist14", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}