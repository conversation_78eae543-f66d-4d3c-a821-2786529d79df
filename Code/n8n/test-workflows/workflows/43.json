{"createdAt": "2021-02-19T07:31:35.734Z", "updatedAt": "2021-06-08T09:27:55.752Z", "id": "43", "name": "GoogleContacts:create update getAll get delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "9b031147-6a8d-44ca-85b0-8cdaf10eb659"}, {"parameters": {"familyName": "QA", "givenName": "node", "additionalFields": {"companyUi": {"companyValues": [{"current": true, "domain": "n8n", "name": "QA", "title": "n8nQA"}]}}}, "name": "Google Contacts", "type": "n8n-nodes-base.googleContacts", "typeVersion": 1, "position": [450, 300], "credentials": {"googleContactsOAuth2Api": {"id": "25", "name": "Google Contacts creds"}}, "id": "7d9e7d33-a215-4997-86a7-4067f990fdbd"}, {"parameters": {"operation": "update", "contactId": "={{$node[\"Google Contacts\"].json[\"contactId\"]}}", "fields": ["names"], "updateFields": {"givenName": "nodemation"}}, "name": "Google Contacts1", "type": "n8n-nodes-base.googleContacts", "typeVersion": 1, "position": [780, 300], "credentials": {"googleContactsOAuth2Api": {"id": "25", "name": "Google Contacts creds"}}, "id": "8577325f-5674-4c71-8d0b-15afd2eebde4"}, {"parameters": {"operation": "getAll", "limit": 1, "fields": ["*"], "options": {}}, "name": "Google Contacts2", "type": "n8n-nodes-base.googleContacts", "typeVersion": 1, "position": [940, 300], "credentials": {"googleContactsOAuth2Api": {"id": "25", "name": "Google Contacts creds"}}, "id": "a4079178-c6c0-490e-8e46-ba06237bbfce"}, {"parameters": {"operation": "get", "contactId": "={{$node[\"Google Contacts\"].json[\"contactId\"]}}", "fields": ["*"]}, "name": "Google Contacts3", "type": "n8n-nodes-base.googleContacts", "typeVersion": 1, "position": [1100, 300], "credentials": {"googleContactsOAuth2Api": {"id": "25", "name": "Google Contacts creds"}}, "id": "98244b5e-a280-4717-8c1c-b8dac07fa5f5"}, {"parameters": {"operation": "delete", "contactId": "={{$node[\"Google Contacts\"].json[\"contactId\"]}}"}, "name": "Google Contacts4", "type": "n8n-nodes-base.googleContacts", "typeVersion": 1, "position": [1250, 300], "credentials": {"googleContactsOAuth2Api": {"id": "25", "name": "Google Contacts creds"}}, "id": "0d3f3684-07a8-4465-95dc-59dc3e11bdbb"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(3000);\n\n// Output data\nreturn items;"}, "name": "Sleep 3 seconds", "type": "n8n-nodes-base.function", "position": [610, 300], "typeVersion": 1, "id": "e1d77a33-d0c8-4920-a533-bc6fb413ea05"}], "connections": {"Google Contacts": {"main": [[{"node": "Sleep 3 seconds", "type": "main", "index": 0}]]}, "Google Contacts1": {"main": [[{"node": "Google Contacts2", "type": "main", "index": 0}]]}, "Google Contacts2": {"main": [[{"node": "Google Contacts3", "type": "main", "index": 0}]]}, "Google Contacts3": {"main": [[{"node": "Google Contacts4", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Google Contacts", "type": "main", "index": 0}]]}, "Sleep 3 seconds": {"main": [[{"node": "Google Contacts1", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}