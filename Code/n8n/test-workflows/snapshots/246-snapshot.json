{"data": {"startData": {}, "resultData": {"runData": {"When clicking \"Test workflow\"": [{"startTime": 1747343998710, "executionIndex": 0, "source": [], "hints": [], "executionTime": 1, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "OpenAI Chat Model": [{"startTime": 1747343998712, "executionTime": 381, "executionIndex": 2, "executionStatus": "success", "source": [{"previousNode": "Open AI Chat", "previousNodeRun": 0}], "data": {"ai_languageModel": [[{"json": {"response": {"object": true}, "tokenUsage": {"object": true}}}]]}, "inputOverride": {"ai_languageModel": [[{"json": {"messages": ["Human: How much is 1+1? Only provide the numerical answer without any other text.\n"], "estimatedTokens": 20, "options": {"openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "model": "gpt-3.5-turbo-0125", "temperature": 0, "timeout": 60000, "max_retries": 2, "configuration": {"baseURL": "https://api.openai.com/v1"}, "model_kwargs": {}}}}]]}, "metadata": {"subRun": [{"node": "OpenAI Chat Model", "runIndex": 0}]}}], "Open AI Chat": [{"startTime": 1747343998711, "executionIndex": 1, "source": [{"previousNode": "When clicking \"Test workflow\""}], "hints": [], "executionTime": 382, "executionStatus": "success", "data": {"main": [[{"json": {"text": "2"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Open AI Chat"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {"OpenAI Chat Model": [{"subRun": [{"node": "OpenAI Chat Model", "runIndex": 0}]}]}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:58.710Z", "stoppedAt": "2025-05-15T21:19:59.093Z", "status": "running", "finished": true}