{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1710331931146, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Clockify": [{"startTime": 1710331931146, "executionTime": 393, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "65f1981b8d52242d1a6a5092", "name": "TestProject created Wed, 13 Mar 2024 12:12:11 GMT", "hourlyRate": {"object": true}, "clientId": "", "workspaceId": "60335ad3f24e660123d7fdee", "billable": true, "memberships": ["json array"], "color": "#8BC34A", "estimate": {"object": true}, "archived": false, "duration": "PT0S", "clientName": "", "note": "", "costRate": {"object": true}, "timeEstimate": {"object": true}, "budgetEstimate": {"object": true}, "template": false, "public": false}, "pairedItem": {"item": 0}}]]}}], "Clockify5": [{"startTime": 1710331931540, "executionTime": 190, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "65f1981be8273c7a4c5e6647", "name": "TestTag0.9326327500236014", "workspaceId": "60335ad3f24e660123d7fdee", "archived": false}, "pairedItem": {"item": 0}}]]}}], "Clockify9": [{"startTime": 1710331931730, "executionTime": 157, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "65f1981ba75a0777c651975b", "description": {"object": true}, "tagIds": {"object": true}, "userId": "60335ad2f24e660123d7fdeb", "billable": false, "taskId": {"object": true}, "projectId": {"object": true}, "timeInterval": {"object": true}, "workspaceId": "60335ad3f24e660123d7fdee", "isLocked": false, "customFieldValues": ["json array"], "type": "REGULAR", "kioskId": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Clockify1": [{"startTime": 1710331931887, "executionTime": 307, "source": [{"previousNode": "Clockify"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "65f1981b8d52242d1a6a5092", "name": "Updated TestProject created Wed, 13 Mar 2024 12:12:11 GMT", "hourlyRate": {"object": true}, "clientId": "", "workspaceId": "60335ad3f24e660123d7fdee", "billable": false, "memberships": ["json array"], "color": "#8BC34A", "estimate": {"object": true}, "archived": false, "duration": "PT0S", "clientName": "", "note": "", "costRate": {"object": true}, "timeEstimate": {"object": true}, "budgetEstimate": {"object": true}, "template": false, "public": false}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.5 second": [{"startTime": 1710331932194, "executionTime": 503, "source": [{"previousNode": "Clockify5"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "65f1981be8273c7a4c5e6647", "name": "TestTag0.9326327500236014", "workspaceId": "60335ad3f24e660123d7fdee", "archived": false}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Clockify10": [{"startTime": 1710331932697, "executionTime": 339, "source": [{"previousNode": "Clockify9"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "65f1981ba75a0777c651975b", "description": "Updated time entry", "tagIds": {"object": true}, "userId": "60335ad2f24e660123d7fdeb", "billable": false, "taskId": {"object": true}, "projectId": {"object": true}, "timeInterval": {"object": true}, "workspaceId": "60335ad3f24e660123d7fdee", "isLocked": false, "customFieldValues": ["json array"], "type": "REGULAR", "kioskId": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Clockify2": [{"startTime": 1710331933037, "executionTime": 227, "source": [{"previousNode": "Clockify1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "65f1981b8d52242d1a6a5092", "name": "Updated TestProject created Wed, 13 Mar 2024 12:12:11 GMT", "hourlyRate": {"object": true}, "clientId": "", "workspaceId": "60335ad3f24e660123d7fdee", "billable": false, "memberships": ["json array"], "color": "#8BC34A", "estimate": {"object": true}, "archived": false, "duration": "PT0S", "clientName": "", "note": "", "costRate": {"object": true}, "timeEstimate": {"object": true}, "budgetEstimate": {"object": true}, "template": false, "public": false}, "pairedItem": {"item": 0}}]]}}], "Clockify6": [{"startTime": 1710331933264, "executionTime": 341, "source": [{"previousNode": "Sleep 0.5 second"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "65f1981be8273c7a4c5e6647", "name": "UpdatedTag0.6688306691795847", "workspaceId": "60335ad3f24e660123d7fdee", "archived": false}, "pairedItem": {"item": 0}}]]}}], "Clockify11": [{"startTime": 1710331933605, "executionTime": 241, "source": [{"previousNode": "Clockify10"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "65f1981ba75a0777c651975b", "description": "Updated time entry", "tagIds": {"object": true}, "userId": "60335ad2f24e660123d7fdeb", "billable": false, "taskId": {"object": true}, "projectId": {"object": true}, "workspaceId": "60335ad3f24e660123d7fdee", "timeInterval": {"object": true}, "customFieldValues": ["json array"], "type": "REGULAR", "kioskId": {"object": true}, "hourlyRate": {"object": true}, "costRate": {"object": true}, "isLocked": false}, "pairedItem": {"item": 0}}]]}}], "Clockify3": [{"startTime": 1710331933846, "executionTime": 308, "source": [{"previousNode": "Clockify2"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "60bdd9a66c24294f3e5f77b3", "name": "TestProject created Mon, 07 Jun 2021 08:32:37 GMT", "hourlyRate": {"object": true}, "clientId": "", "workspaceId": "60335ad3f24e660123d7fdee", "billable": true, "memberships": ["json array"], "color": "#00BCD4", "estimate": {"object": true}, "archived": false, "duration": "PT0S", "clientName": "", "note": "", "costRate": {"object": true}, "timeEstimate": {"object": true}, "budgetEstimate": {"object": true}, "template": false, "public": false}, "pairedItem": {"item": 0}}]]}}], "Clockify7": [{"startTime": 1710331934154, "executionTime": 170, "source": [{"previousNode": "Clockify6"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "60bdd90f8b25e0206c07a554", "name": "TestTag", "workspaceId": "60335ad3f24e660123d7fdee", "archived": false}, "pairedItem": {"item": 0}}]]}}], "Clockify12": [{"startTime": 1710331934324, "executionTime": 148, "source": [{"previousNode": "Clockify11"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Clockify4": [{"startTime": 1710331934472, "executionTime": 0, "source": [{"previousNode": "Clockify3"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "60bdd9a66c24294f3e5f77b3", "name": "TestProject created Mon, 07 Jun 2021 08:32:37 GMT", "hourlyRate": {"object": true}, "clientId": "", "workspaceId": "60335ad3f24e660123d7fdee", "billable": true, "memberships": ["json array"], "color": "#00BCD4", "estimate": {"object": true}, "archived": false, "duration": "PT0S", "clientName": "", "note": "", "costRate": {"object": true}, "timeEstimate": {"object": true}, "budgetEstimate": {"object": true}, "template": false, "public": false}, "pairedItem": {"item": 0}}]]}}], "Clockify8": [{"startTime": 1710331934472, "executionTime": 157, "source": [{"previousNode": "Clockify7"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Clockify8"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2024-03-13T12:12:11.145Z", "stoppedAt": "2024-03-13T12:12:14.629Z", "status": "running", "finished": true}