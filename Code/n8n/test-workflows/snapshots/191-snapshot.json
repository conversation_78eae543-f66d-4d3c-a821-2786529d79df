{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1676891419759, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "MailerLite": [{"startTime": 1676891419760, "executionTime": 303, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 1493465993027103000, "name": {"object": true}, "email": "<EMAIL>", "sent": 0, "opened": 0, "opened_rate": 0, "clicked": 0, "clicked_rate": 0, "type": "active", "country_id": {"object": true}, "signup_ip": "", "signup_timestamp": "", "confirmation_ip": "", "confirmation_timestamp": "", "fields": ["json array"], "webform_subscribe_date": {"object": true}, "date_subscribe": {"object": true}, "date_unsubscribe": {"object": true}, "date_created": "2023-02-20 11:10:19", "date_updated": "2023-02-20 11:10:20", "user_agent": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "MailerLite1": [{"startTime": 1676891420063, "executionTime": 182, "source": [{"previousNode": "MailerLite"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 1493465993027103000, "name": "", "email": "<EMAIL>", "sent": 0, "opened": 0, "opened_rate": 0, "clicked": 0, "clicked_rate": 0, "type": "active", "country_id": {"object": true}, "signup_ip": "", "signup_timestamp": "", "confirmation_ip": "", "confirmation_timestamp": "", "fields": ["json array"], "webform_subscribe_date": {"object": true}, "date_subscribe": {"object": true}, "date_unsubscribe": {"object": true}, "date_created": "2023-02-20 11:10:19", "date_updated": "2023-02-20 11:10:20", "user_agent": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "MailerLite2": [{"startTime": 1676891420246, "executionTime": 219, "source": [{"previousNode": "MailerLite1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 1493465993027103000, "name": "UpdatedName1676891420247", "email": "<EMAIL>", "sent": 0, "opened": 0, "opened_rate": 0, "clicked": 0, "clicked_rate": 0, "type": "active", "country_id": {"object": true}, "signup_ip": "", "signup_timestamp": "", "confirmation_ip": "", "confirmation_timestamp": "", "fields": ["json array"], "webform_subscribe_date": {"object": true}, "date_subscribe": {"object": true}, "date_unsubscribe": {"object": true}, "date_created": "2023-02-20 11:10:19", "date_updated": "2023-02-20 11:10:20", "user_agent": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "MailerLite3": [{"startTime": 1676891420466, "executionTime": 160, "source": [{"previousNode": "MailerLite2"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 968892253701516000, "name": "", "email": "<EMAIL>", "sent": 0, "opened": 0, "opened_rate": 0, "clicked": 0, "clicked_rate": 0, "type": "active", "country_id": {"object": true}, "signup_ip": {"object": true}, "signup_timestamp": {"object": true}, "confirmation_ip": {"object": true}, "confirmation_timestamp": {"object": true}, "fields": ["json array"], "webform_subscribe_date": {"object": true}, "date_subscribe": {"object": true}, "date_unsubscribe": {"object": true}, "date_created": "2021-02-26 16:35:57", "date_updated": "2021-02-26 16:35:57", "user_agent": {"object": true}}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "MailerLite3"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2023-02-20T11:10:19.756Z", "stoppedAt": "2023-02-20T11:10:20.627Z", "status": "running", "finished": true}