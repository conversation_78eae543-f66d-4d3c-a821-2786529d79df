{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343994499, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Monday.com": [{"startTime": 1747343994499, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 1493, "executionStatus": "success", "data": {"main": [[{"json": {"id": "9163279560"}, "pairedItem": {"item": 0}}]]}}], "Monday.com1": [{"startTime": 1747343995992, "executionIndex": 2, "source": [{"previousNode": "Monday.com"}], "hints": [], "executionTime": 705, "executionStatus": "success", "data": {"main": [[{"json": {"id": "9163279560", "name": "Board1747343994501", "description": {"object": true}, "state": "active", "board_folder_id": {"object": true}, "board_kind": "private", "owners": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Monday.com2": [{"startTime": 1747343996697, "executionIndex": 3, "source": [{"previousNode": "Monday.com1"}], "hints": [], "executionTime": 363, "executionStatus": "success", "data": {"main": [[{"json": {"id": "9163279560", "name": "Board1747343994501", "description": {"object": true}, "state": "active", "board_folder_id": {"object": true}, "board_kind": "private", "owners": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Monday.com4": [{"startTime": 1747343997060, "executionIndex": 4, "source": [{"previousNode": "Monday.com2"}], "hints": [], "executionTime": 820, "executionStatus": "success", "data": {"main": [[{"json": {"id": "boolean_mkqzts11"}, "pairedItem": {"item": 0}}]]}}], "Monday.com6": [{"startTime": 1747343997880, "executionIndex": 5, "source": [{"previousNode": "Monday.com4"}], "hints": [], "executionTime": 707, "executionStatus": "success", "data": {"main": [[{"json": {"id": "group_mkqzjrxd"}, "pairedItem": {"item": 0}}]]}}], "Monday.com9": [{"startTime": 1747343998587, "executionIndex": 6, "source": [{"previousNode": "Monday.com6"}], "hints": [], "executionTime": 699, "executionStatus": "success", "data": {"main": [[{"json": {"id": "9163280038"}, "pairedItem": {"item": 0}}]]}}], "Monday.com10": [{"startTime": 1747343999286, "executionIndex": 7, "source": [{"previousNode": "Monday.com9"}], "hints": [], "executionTime": 1154, "executionStatus": "success", "data": {"main": [[{"json": {"id": "4124607906"}, "pairedItem": {"item": 0}}]]}}], "Monday.com11": [{"startTime": 1747344000440, "executionIndex": 8, "source": [{"previousNode": "Monday.com10"}], "hints": [], "executionTime": 627, "executionStatus": "success", "data": {"main": [[{"json": {"id": "9163280038"}, "pairedItem": {"item": 0}}]]}}], "Monday.com12": [{"startTime": 1747344001067, "executionIndex": 9, "source": [{"previousNode": "Monday.com11"}], "hints": [], "executionTime": 334, "executionStatus": "success", "data": {"main": [[{"json": {"id": "9163280038", "name": "Item1747343998592", "created_at": "2025-05-15T21:19:59Z", "state": "active", "column_values": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Monday.com13": [{"startTime": 1747344001401, "executionIndex": 10, "source": [{"previousNode": "Monday.com12"}], "hints": [], "executionTime": 568, "executionStatus": "success", "data": {"main": [[{"json": {"id": "9163280038", "name": "Item1747343998592", "created_at": "2025-05-15T21:19:59Z", "state": "active", "column_values": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Monday.com14": [{"startTime": 1747344001969, "executionIndex": 11, "source": [{"previousNode": "Monday.com13"}], "hints": [], "executionTime": 562, "executionStatus": "success", "data": {"main": [[{"json": {"id": "9163279581", "name": "Task 1", "created_at": "2025-05-15T21:19:55Z", "state": "active", "board": {"object": true}, "column_values": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Monday.com15": [{"startTime": 1747344002531, "executionIndex": 12, "source": [{"previousNode": "Monday.com14"}], "hints": [], "executionTime": 819, "executionStatus": "success", "data": {"main": [[{"json": {"id": "9163280038"}, "pairedItem": {"item": 0}}]]}}], "Monday.com16": [{"startTime": 1747344003350, "executionIndex": 13, "source": [{"previousNode": "Monday.com15"}], "hints": [], "executionTime": 809, "executionStatus": "success", "data": {"main": [[{"json": {"id": "9163280038"}, "pairedItem": {"item": 0}}]]}}], "Monday.com7": [{"startTime": 1747344004159, "executionIndex": 14, "source": [{"previousNode": "Monday.com16"}], "hints": [], "executionTime": 1162, "executionStatus": "success", "data": {"main": [[{"json": {"id": "group_mkqzjrxd"}, "pairedItem": {"item": 0}}]]}}], "Monday.com8": [{"startTime": 1747344005321, "executionIndex": 15, "source": [{"previousNode": "Monday.com7"}], "hints": [], "executionTime": 373, "executionStatus": "success", "data": {"main": [[{"json": {"id": "topics", "title": "Group Title", "color": "#037f4c", "position": "65536", "archived": false}, "pairedItem": {"item": 0}}]]}}], "Monday.com5": [{"startTime": 1747344005695, "executionIndex": 16, "source": [{"previousNode": "Monday.com8"}], "hints": [], "executionTime": 340, "executionStatus": "success", "data": {"main": [[{"json": {"id": "name", "title": "Name", "type": "name", "settings_str": "{}", "archived": false}, "pairedItem": {"item": 0}}, {"json": {"id": "boolean_mkqzts11", "title": "Column1747343997063", "type": "checkbox", "settings_str": "{}", "archived": false}, "pairedItem": {"item": 0}}]]}}], "Merge": [{"startTime": 1747344006035, "executionIndex": 17, "source": [{"previousNode": "Monday.com5"}, {"previousNode": "Monday.com5"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": [{"item": 0}, {"item": 1}]}]]}}], "Monday.com3": [{"startTime": 1747344006036, "executionIndex": 18, "source": [{"previousNode": "<PERSON><PERSON>"}], "hints": [], "executionTime": 660, "executionStatus": "success", "data": {"main": [[{"json": {"id": "9163279560"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Monday.com3"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:54.498Z", "stoppedAt": "2025-05-15T21:20:06.696Z", "status": "running", "finished": true}