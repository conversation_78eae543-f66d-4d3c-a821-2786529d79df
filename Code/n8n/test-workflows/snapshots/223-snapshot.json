{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"hints": [], "startTime": 1738078174417, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "LingvaNex": [{"hints": [], "startTime": 1738078174417, "executionTime": 374, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"err": {"object": true}, "result": "Automatisierung"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "LingvaNex"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-01-28T15:29:34.416Z", "stoppedAt": "2025-01-28T15:29:34.791Z", "status": "running", "finished": true}