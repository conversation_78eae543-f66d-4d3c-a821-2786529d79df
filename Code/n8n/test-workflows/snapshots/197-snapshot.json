{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1710331895822, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "MQTT qos:1": [{"startTime": 1710331895822, "executionTime": 99, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "MQTT1 qos:0": [{"startTime": 1710331895921, "executionTime": 113, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Function": [{"startTime": 1710331896034, "executionTime": 3, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"message": "MQTT-item-message1710331896036"}, "pairedItem": {"item": 0}}]]}}], "MQTT2 qos:2": [{"startTime": 1710331896037, "executionTime": 128, "source": [{"previousNode": "Function"}], "executionStatus": "success", "data": {"main": [[{"json": {"message": "MQTT-item-message1710331896036"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "MQTT2 qos:2"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2024-03-13T12:11:35.822Z", "stoppedAt": "2024-03-13T12:11:36.165Z", "status": "running", "finished": true}