{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343994528, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "AWS Comprehend": [{"startTime": 1747343994528, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 535, "executionStatus": "success", "data": {"main": [[{"json": {"en": 0.995190441608429}, "pairedItem": {"item": 0}}]]}}], "AWS Comprehend1": [{"startTime": 1747343995063, "executionIndex": 2, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 441, "executionStatus": "success", "data": {"main": [[{"json": {"Sentiment": "POSITIVE", "SentimentScore": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "AWS Comprehend2": [{"startTime": 1747343995504, "executionIndex": 3, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 533, "executionStatus": "success", "data": {"main": [[{"json": {"BeginOffset": 0, "EndOffset": 3, "Score": 0.4956623315811157, "Text": "n8n", "Type": "OTHER"}, "pairedItem": {"item": 0}}, {"json": {"BeginOffset": 18, "EndOffset": 23, "Score": 0.4929276704788208, "Text": "eight", "Type": "OTHER"}, "pairedItem": {"item": 0}}, {"json": {"BeginOffset": 53, "EndOffset": 62, "Score": 0.7383686304092407, "Text": "every app", "Type": "QUANTITY"}, "pairedItem": {"item": 0}}, {"json": {"BeginOffset": 147, "EndOffset": 158, "Score": 0.9422106146812439, "Text": "single line", "Type": "QUANTITY"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "AWS Comprehend2"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:54.528Z", "stoppedAt": "2025-05-15T21:19:56.037Z", "status": "running", "finished": true}