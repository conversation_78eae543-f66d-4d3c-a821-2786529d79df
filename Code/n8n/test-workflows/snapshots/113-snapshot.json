{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1710331887198, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Set": [{"startTime": 1710331887198, "executionTime": 3, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"message": "AMQPMessage1710331887201"}, "pairedItem": {"item": 0}}]]}}], "AMQP Sender": [{"startTime": 1710331887201, "executionTime": 449, "source": [{"previousNode": "Set"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 0}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "AMQP Sender"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2024-03-13T12:11:27.197Z", "stoppedAt": "2024-03-13T12:11:27.650Z", "status": "running", "finished": true}