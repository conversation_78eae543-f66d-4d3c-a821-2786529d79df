{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343994524, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Mandrill": [{"startTime": 1747343994524, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 578, "executionStatus": "success", "data": {"main": [[{"json": {"email": "<EMAIL>", "status": "rejected", "_id": "8111baeb0ff141db9aa0f2f7cc96a08d", "reject_reason": "unsigned", "queued_reason": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Mandrill1": [{"startTime": 1747343995102, "executionIndex": 2, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 512, "executionStatus": "success", "data": {"main": [[{"json": {"email": "<EMAIL>", "status": "rejected", "_id": "769293eaca9843c2926c7ec1e5df052a", "reject_reason": "unsigned", "queued_reason": {"object": true}}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Mandrill1"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:54.524Z", "stoppedAt": "2025-05-15T21:19:55.614Z", "status": "running", "finished": true}