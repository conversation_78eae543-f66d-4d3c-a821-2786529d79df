{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1710331919258, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "ClickUp": [{"startTime": 1710331919258, "executionTime": 1567, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "90121105894", "name": "Test21710331919261", "orderindex": 102, "override_statuses": false, "hidden": false, "space": {"object": true}, "task_count": "0", "archived": false, "statuses": ["json array"], "lists": ["json array"], "permission_level": "create"}, "pairedItem": {"item": 0}}]]}}], "Sleep 8 Seconds": [{"startTime": 1710331920826, "executionTime": 802, "source": [{"previousNode": "ClickUp"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "90121105894", "name": "Test21710331919261", "orderindex": 102, "override_statuses": false, "hidden": false, "space": {"object": true}, "task_count": "0", "archived": false, "statuses": ["json array"], "lists": ["json array"], "permission_level": "create"}, "pairedItem": {"item": 0}, "index": 0}]]}}], "ClickUp1": [{"startTime": 1710331921629, "executionTime": 473, "source": [{"previousNode": "Sleep 8 Seconds"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "901201909617", "name": "testingList21710331921631", "deleted": false, "orderindex": 0, "content": "", "priority": {"object": true}, "assignee": {"object": true}, "due_date": {"object": true}, "start_date": {"object": true}, "folder": {"object": true}, "space": {"object": true}, "inbound_address": "<EMAIL>", "archived": false, "override_statuses": false, "statuses": ["json array"], "permission_level": "create"}, "pairedItem": {"item": 0}}]]}}], "Sleep 8 Seconds1": [{"startTime": 1710331922103, "executionTime": 802, "source": [{"previousNode": "ClickUp1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "901201909617", "name": "testingList21710331921631", "deleted": false, "orderindex": 0, "content": "", "priority": {"object": true}, "assignee": {"object": true}, "due_date": {"object": true}, "start_date": {"object": true}, "folder": {"object": true}, "space": {"object": true}, "inbound_address": "<EMAIL>", "archived": false, "override_statuses": false, "statuses": ["json array"], "permission_level": "create"}, "pairedItem": {"item": 0}, "index": 0}]]}}], "ClickUp2": [{"startTime": 1710331922905, "executionTime": 323, "source": [{"previousNode": "Sleep 8 Seconds1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 90120030547385, "hist_id": "bf5d7961-3473-4ba7-9ba2-3ccd75f11f4d", "date": 1710331923158, "version": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Sleep 8 Seconds2": [{"startTime": 1710331923228, "executionTime": 803, "source": [{"previousNode": "ClickUp2"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 90120030547385, "hist_id": "bf5d7961-3473-4ba7-9ba2-3ccd75f11f4d", "date": 1710331923158, "version": {"object": true}}, "pairedItem": {"item": 0}, "index": 0}]]}}], "ClickUp3": [{"startTime": 1710331924031, "executionTime": 408, "source": [{"previousNode": "Sleep 8 Seconds2"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Sleep 8 Seconds3": [{"startTime": 1710331924439, "executionTime": 802, "source": [{"previousNode": "ClickUp3"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}, "index": 0}]]}}], "ClickUp4": [{"startTime": 1710331925241, "executionTime": 325, "source": [{"previousNode": "Sleep 8 Seconds3"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "90120030547385", "comment": ["json array"], "comment_text": "commentUpdated1710331924035", "user": {"object": true}, "reactions": ["json array"], "date": "1710331923158"}, "pairedItem": {"item": 0}}]]}}], "Sleep 8 Seconds4": [{"startTime": 1710331925566, "executionTime": 802, "source": [{"previousNode": "ClickUp4"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "90120030547385", "comment": ["json array"], "comment_text": "commentUpdated1710331924035", "user": {"object": true}, "reactions": ["json array"], "date": "1710331923158"}, "pairedItem": {"item": 0}, "index": 0}]]}}], "ClickUp5": [{"startTime": 1710331926369, "executionTime": 407, "source": [{"previousNode": "Sleep 8 Seconds4"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Sleep 8 Seconds5": [{"startTime": 1710331926777, "executionTime": 802, "source": [{"previousNode": "ClickUp5"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}, "index": 0}]]}}], "ClickUp6": [{"startTime": 1710331927579, "executionTime": 2324, "source": [{"previousNode": "Sleep 8 Seconds5"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Sleep 8 Seconds6": [{"startTime": 1710331929903, "executionTime": 803, "source": [{"previousNode": "ClickUp6"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}, "index": 0}]]}}], "ClickUp7": [{"startTime": 1710331930706, "executionTime": 426, "source": [{"previousNode": "Sleep 8 Seconds6"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "ClickUp7"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2024-03-13T12:11:59.256Z", "stoppedAt": "2024-03-13T12:12:11.132Z", "status": "running", "finished": true}