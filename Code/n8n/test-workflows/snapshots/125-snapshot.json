{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1676891408444, "executionTime": 1, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Set": [{"startTime": 1676891408445, "executionTime": 42, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 555, "name": "Name1676891408487"}, "pairedItem": {"item": 0}}]]}}], "Postgres": [{"startTime": 1676891408487, "executionTime": 187, "source": [{"previousNode": "Set"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 555, "name": "Name1676891408487"}, "pairedItem": {"item": 0}}]]}}], "Set1": [{"startTime": 1676891408675, "executionTime": 3, "source": [{"previousNode": "Postgres"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 555, "name": "UpdatedName1676891408677"}, "pairedItem": {"item": 0}}]]}}], "Postgres1": [{"startTime": 1676891408678, "executionTime": 194, "source": [{"previousNode": "Set1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 555, "name": "UpdatedName1676891408677"}, "pairedItem": {"item": 0}}]]}}], "Postgres2": [{"startTime": 1676891408873, "executionTime": 202, "source": [{"previousNode": "Postgres1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": {"object": true}, "name": "omar456"}, "pairedItem": {"item": 0}}, {"json": {"id": {"object": true}, "name": "omar123"}, "pairedItem": {"item": 0}}, {"json": {"id": {"object": true}, "name": "omar456"}, "pairedItem": {"item": 0}}, {"json": {"id": {"object": true}, "name": "omar123"}, "pairedItem": {"item": 0}}, {"json": {"id": {"object": true}, "name": "omar456"}, "pairedItem": {"item": 0}}, {"json": {"id": {"object": true}, "name": "omar123"}, "pairedItem": {"item": 0}}, {"json": {"id": {"object": true}, "name": "omar456"}, "pairedItem": {"item": 0}}, {"json": {"id": {"object": true}, "name": "omar123"}, "pairedItem": {"item": 0}}, {"json": {"id": {"object": true}, "name": "omar456"}, "pairedItem": {"item": 0}}, {"json": {"id": {"object": true}, "name": "omar123"}, "pairedItem": {"item": 0}}, {"json": {"id": {"object": true}, "name": "omar456"}, "pairedItem": {"item": 0}}, {"json": {"id": {"object": true}, "name": "omar123"}, "pairedItem": {"item": 0}}, {"json": {"id": 555, "name": "UpdatedName1676891408677"}, "pairedItem": {"item": 0}}, {"json": {"id": {"object": true}, "name": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Postgres3": [{"startTime": 1676891409075, "executionTime": 126, "source": [{"previousNode": "Postgres2"}], "executionStatus": "success", "data": {"main": [[]]}}]}, "lastNodeExecuted": "Postgres3"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2023-02-20T11:10:08.443Z", "stoppedAt": "2023-02-20T11:10:09.202Z", "status": "running", "finished": true}