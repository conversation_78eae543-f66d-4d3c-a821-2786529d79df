{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1678116858303, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Google Tasks": [{"startTime": 1678116858304, "executionTime": 713, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"kind": "tasks#task", "id": "eVRMM3dMeGw5Qjd1a05yTg", "etag": "\"LTEyMTUzNTM1OTI\"", "title": "Do Testing", "updated": "2023-03-06T15:34:19.000Z", "selfLink": "https://www.googleapis.com/tasks/v1/lists/MDI3ODQzODIxOTE4MjA1ODY3NzM6MDow/tasks/eVRMM3dMeGw5Qjd1a05yTg", "position": "00000000000000000000", "status": "needsAction", "links": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Google Tasks1": [{"startTime": 1678116859017, "executionTime": 306, "source": [{"previousNode": "Google Tasks"}], "executionStatus": "success", "data": {"main": [[{"json": {"kind": "tasks#task", "id": "eVRMM3dMeGw5Qjd1a05yTg", "etag": "\"LTEyMTUzNTM4MDA\"", "title": "Do Testing", "updated": "2023-03-06T15:34:19.000Z", "selfLink": "https://www.googleapis.com/tasks/v1/lists/MDI3ODQzODIxOTE4MjA1ODY3NzM6MDow/tasks/eVRMM3dMeGw5Qjd1a05yTg", "position": "00000000000000000000", "status": "completed", "completed": "2023-03-06T15:34:19.000Z", "links": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Google Tasks4": [{"startTime": 1678116859324, "executionTime": 193, "source": [{"previousNode": "Google Tasks1"}], "executionStatus": "success", "data": {"main": [[{"json": {"kind": "tasks#task", "id": "eVRMM3dMeGw5Qjd1a05yTg", "etag": "\"LTEyMTUzNTM4MDA\"", "title": "Do Testing", "updated": "2023-03-06T15:34:19.000Z", "selfLink": "https://www.googleapis.com/tasks/v1/lists/MDI3ODQzODIxOTE4MjA1ODY3NzM6MDow/tasks/eVRMM3dMeGw5Qjd1a05yTg", "position": "00000000000000000000", "status": "completed", "completed": "2023-03-06T15:34:19.000Z", "links": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Google Tasks2": [{"startTime": 1678116859518, "executionTime": 168, "source": [{"previousNode": "Google Tasks4"}], "executionStatus": "success", "data": {"main": [[{"json": {"kind": "tasks#task", "id": "eVRMM3dMeGw5Qjd1a05yTg", "etag": "\"LTEyMTUzNTM4MDA\"", "title": "Do Testing", "updated": "2023-03-06T15:34:19.000Z", "selfLink": "https://www.googleapis.com/tasks/v1/lists/MDI3ODQzODIxOTE4MjA1ODY3NzM6MDow/tasks/eVRMM3dMeGw5Qjd1a05yTg", "position": "00000000000000000000", "status": "completed", "completed": "2023-03-06T15:34:19.000Z", "links": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Google Tasks3": [{"startTime": 1678116859686, "executionTime": 213, "source": [{"previousNode": "Google Tasks2"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Google Tasks3"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2023-03-06T15:34:18.302Z", "stoppedAt": "2023-03-06T15:34:19.899Z", "status": "running", "finished": true}