{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343994468, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Set": [{"startTime": 1747343994468, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"name": "test", "toBeRenamed": "name"}, "pairedItem": {"item": 0}}]]}}], "Rename Keys": [{"startTime": 1747343994468, "executionIndex": 2, "source": [{"previousNode": "Set"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"name": "test", "Renamed": "name"}, "pairedItem": {"item": 0}}]]}}], "Function": [{"startTime": 1747343994468, "executionIndex": 3, "source": [{"previousNode": "<PERSON><PERSON>"}], "hints": [], "executionTime": 2, "executionStatus": "success", "data": {"main": [[{"json": {"name": "test", "Renamed": "name"}, "pairedItem": {"item": 0}, "index": 0}]]}}]}, "lastNodeExecuted": "Function"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:54.467Z", "stoppedAt": "2025-05-15T21:19:54.470Z", "status": "running", "finished": true}