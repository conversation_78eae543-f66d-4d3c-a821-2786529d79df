{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1710331887342, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Set": [{"startTime": 1710331887342, "executionTime": 3, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"key": "<PERSON><PERSON><PERSON>", "value": "Value1710331887344"}, "pairedItem": {"item": 0}}]]}}], "Redis": [{"startTime": 1710331887345, "executionTime": 230, "source": [{"previousNode": "Set"}], "executionStatus": "success", "data": {"main": [[{"json": {"redis_version": 6.2, "redis_git_sha1": 0, "redis_git_dirty": 0, "redis_build_id": "a26c8b6d79010f4f", "redis_mode": "standalone", "os": "Linux 5.4.0-65-generic x86_64", "arch_bits": 64, "multiplexing_api": "epoll", "atomicvar_api": "c11-builtin", "gcc_version": 8.3, "process_id": 1, "process_supervised": "no", "run_id": "335ae737a685624354ebf06e053072c8dde37824", "tcp_port": 6379, "server_time_usec": 1710331887537701, "uptime_in_seconds": 34307370, "uptime_in_days": 397, "hz": 10, "configured_hz": 10, "lru_clock": 15833071, "executable": "/data/redis-server", "config_file": "/usr/local/etc/redis.conf", "io_threads_active": 0, "connected_clients": 1, "cluster_connections": 0, "maxclients": 10000, "client_recent_max_input_buffer": 40, "client_recent_max_output_buffer": 0, "blocked_clients": 0, "tracking_clients": 0, "clients_in_timeout_table": 0, "used_memory": 988872, "used_memory_human": "965.70K", "used_memory_rss": 4448256, "used_memory_rss_human": "4.24M", "used_memory_peak": 3094400, "used_memory_peak_human": "2.95M", "used_memory_peak_perc": "31.96%", "used_memory_overhead": 831184, "used_memory_startup": 810184, "used_memory_dataset": 157688, "used_memory_dataset_perc": "88.25%", "allocator_allocated": 1020376, "allocator_active": 1339392, "allocator_resident": 3866624, "total_system_memory": 16395214848, "total_system_memory_human": "15.27G", "used_memory_lua": 37888, "used_memory_lua_human": "37.00K", "used_memory_scripts": 0, "used_memory_scripts_human": "0B", "number_of_cached_scripts": 0, "maxmemory": 0, "maxmemory_human": "0B", "maxmemory_policy": "noeviction", "allocator_frag_ratio": 1.31, "allocator_frag_bytes": 319016, "allocator_rss_ratio": 2.89, "allocator_rss_bytes": 2527232, "rss_overhead_ratio": 1.15, "rss_overhead_bytes": 581632, "mem_fragmentation_ratio": 4.51, "mem_fragmentation_bytes": 3461176, "mem_not_counted_for_evict": 0, "mem_replication_backlog": 0, "mem_clients_slaves": 0, "mem_clients_normal": 20520, "mem_aof_buffer": 0, "mem_allocator": "jemalloc-5.1.0", "active_defrag_running": 0, "lazyfree_pending_objects": 0, "lazyfreed_objects": 0, "loading": 0, "current_cow_size": 0, "current_fork_perc": "0.00%", "current_save_keys_processed": 0, "current_save_keys_total": 0, "rdb_changes_since_last_save": 0, "rdb_bgsave_in_progress": 0, "rdb_last_save_time": 1710299433, "rdb_last_bgsave_status": "ok", "rdb_last_bgsave_time_sec": 0, "rdb_current_bgsave_time_sec": "-1", "rdb_last_cow_size": 483328, "aof_enabled": 0, "aof_rewrite_in_progress": 0, "aof_rewrite_scheduled": 0, "aof_last_rewrite_time_sec": "-1", "aof_current_rewrite_time_sec": "-1", "aof_last_bgrewrite_status": "ok", "aof_last_write_status": "ok", "aof_last_cow_size": 0, "module_fork_in_progress": 0, "module_fork_last_cow_size": 0, "total_connections_received": 43137, "total_commands_processed": 389780, "instantaneous_ops_per_sec": 1, "total_net_input_bytes": 7316126, "total_net_output_bytes": 47053879, "instantaneous_input_kbps": 0.09, "instantaneous_output_kbps": 0.11, "rejected_connections": 0, "sync_full": 0, "sync_partial_ok": 0, "sync_partial_err": 0, "expired_keys": 0, "expired_stale_perc": 0, "expired_time_cap_reached_count": 0, "expire_cycle_cpu_milliseconds": 1228670, "evicted_keys": 0, "keyspace_hits": 3820, "keyspace_misses": 0, "pubsub_channels": 0, "pubsub_patterns": 0, "latest_fork_usec": 983, "total_forks": 108873, "migrate_cached_sockets": 0, "slave_expires_tracked_keys": 0, "active_defrag_hits": 0, "active_defrag_misses": 0, "active_defrag_key_hits": 0, "active_defrag_key_misses": 0, "tracking_total_keys": 0, "tracking_total_items": 0, "tracking_total_prefixes": 0, "unexpected_error_replies": 0, "total_error_replies": 400712, "dump_payload_sanitizations": 0, "total_reads_processed": 266496, "total_writes_processed": 227508, "io_threaded_reads_processed": 0, "io_threaded_writes_processed": 0, "role": "master", "connected_slaves": 0, "master_failover_state": "no-failover", "master_replid": "5bcfb8d0e50aae43141a6bc4de0e07b90f780906", "master_replid2": 0, "master_repl_offset": 0, "second_repl_offset": "-1", "repl_backlog_active": 0, "repl_backlog_size": 1048576, "repl_backlog_first_byte_offset": 0, "repl_backlog_histlen": 0, "used_cpu_sys": 81544.939292, "used_cpu_user": 114072.000995, "used_cpu_sys_children": 259.550501, "used_cpu_user_children": 136.476364, "used_cpu_sys_main_thread": 81531.244451, "used_cpu_user_main_thread": 114059.326093, "errorstat_ERR": {"object": true}, "errorstat_MISCONF": {"object": true}, "errorstat_NOAUTH": {"object": true}, "errorstat_WRONGPASS": {"object": true}, "cluster_enabled": 0, "db0": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Redis1": [{"startTime": 1710331887575, "executionTime": 143, "source": [{"previousNode": "Redis"}], "executionStatus": "success", "data": {"main": [[{"json": {"redis_version": 6.2, "redis_git_sha1": 0, "redis_git_dirty": 0, "redis_build_id": "a26c8b6d79010f4f", "redis_mode": "standalone", "os": "Linux 5.4.0-65-generic x86_64", "arch_bits": 64, "multiplexing_api": "epoll", "atomicvar_api": "c11-builtin", "gcc_version": 8.3, "process_id": 1, "process_supervised": "no", "run_id": "335ae737a685624354ebf06e053072c8dde37824", "tcp_port": 6379, "server_time_usec": 1710331887537701, "uptime_in_seconds": 34307370, "uptime_in_days": 397, "hz": 10, "configured_hz": 10, "lru_clock": 15833071, "executable": "/data/redis-server", "config_file": "/usr/local/etc/redis.conf", "io_threads_active": 0, "connected_clients": 1, "cluster_connections": 0, "maxclients": 10000, "client_recent_max_input_buffer": 40, "client_recent_max_output_buffer": 0, "blocked_clients": 0, "tracking_clients": 0, "clients_in_timeout_table": 0, "used_memory": 988872, "used_memory_human": "965.70K", "used_memory_rss": 4448256, "used_memory_rss_human": "4.24M", "used_memory_peak": 3094400, "used_memory_peak_human": "2.95M", "used_memory_peak_perc": "31.96%", "used_memory_overhead": 831184, "used_memory_startup": 810184, "used_memory_dataset": 157688, "used_memory_dataset_perc": "88.25%", "allocator_allocated": 1020376, "allocator_active": 1339392, "allocator_resident": 3866624, "total_system_memory": 16395214848, "total_system_memory_human": "15.27G", "used_memory_lua": 37888, "used_memory_lua_human": "37.00K", "used_memory_scripts": 0, "used_memory_scripts_human": "0B", "number_of_cached_scripts": 0, "maxmemory": 0, "maxmemory_human": "0B", "maxmemory_policy": "noeviction", "allocator_frag_ratio": 1.31, "allocator_frag_bytes": 319016, "allocator_rss_ratio": 2.89, "allocator_rss_bytes": 2527232, "rss_overhead_ratio": 1.15, "rss_overhead_bytes": 581632, "mem_fragmentation_ratio": 4.51, "mem_fragmentation_bytes": 3461176, "mem_not_counted_for_evict": 0, "mem_replication_backlog": 0, "mem_clients_slaves": 0, "mem_clients_normal": 20520, "mem_aof_buffer": 0, "mem_allocator": "jemalloc-5.1.0", "active_defrag_running": 0, "lazyfree_pending_objects": 0, "lazyfreed_objects": 0, "loading": 0, "current_cow_size": 0, "current_fork_perc": "0.00%", "current_save_keys_processed": 0, "current_save_keys_total": 0, "rdb_changes_since_last_save": 0, "rdb_bgsave_in_progress": 0, "rdb_last_save_time": 1710299433, "rdb_last_bgsave_status": "ok", "rdb_last_bgsave_time_sec": 0, "rdb_current_bgsave_time_sec": "-1", "rdb_last_cow_size": 483328, "aof_enabled": 0, "aof_rewrite_in_progress": 0, "aof_rewrite_scheduled": 0, "aof_last_rewrite_time_sec": "-1", "aof_current_rewrite_time_sec": "-1", "aof_last_bgrewrite_status": "ok", "aof_last_write_status": "ok", "aof_last_cow_size": 0, "module_fork_in_progress": 0, "module_fork_last_cow_size": 0, "total_connections_received": 43137, "total_commands_processed": 389780, "instantaneous_ops_per_sec": 1, "total_net_input_bytes": 7316126, "total_net_output_bytes": 47053879, "instantaneous_input_kbps": 0.09, "instantaneous_output_kbps": 0.11, "rejected_connections": 0, "sync_full": 0, "sync_partial_ok": 0, "sync_partial_err": 0, "expired_keys": 0, "expired_stale_perc": 0, "expired_time_cap_reached_count": 0, "expire_cycle_cpu_milliseconds": 1228670, "evicted_keys": 0, "keyspace_hits": 3820, "keyspace_misses": 0, "pubsub_channels": 0, "pubsub_patterns": 0, "latest_fork_usec": 983, "total_forks": 108873, "migrate_cached_sockets": 0, "slave_expires_tracked_keys": 0, "active_defrag_hits": 0, "active_defrag_misses": 0, "active_defrag_key_hits": 0, "active_defrag_key_misses": 0, "tracking_total_keys": 0, "tracking_total_items": 0, "tracking_total_prefixes": 0, "unexpected_error_replies": 0, "total_error_replies": 400712, "dump_payload_sanitizations": 0, "total_reads_processed": 266496, "total_writes_processed": 227508, "io_threaded_reads_processed": 0, "io_threaded_writes_processed": 0, "role": "master", "connected_slaves": 0, "master_failover_state": "no-failover", "master_replid": "5bcfb8d0e50aae43141a6bc4de0e07b90f780906", "master_replid2": 0, "master_repl_offset": 0, "second_repl_offset": "-1", "repl_backlog_active": 0, "repl_backlog_size": 1048576, "repl_backlog_first_byte_offset": 0, "repl_backlog_histlen": 0, "used_cpu_sys": 81544.939292, "used_cpu_user": 114072.000995, "used_cpu_sys_children": 259.550501, "used_cpu_user_children": 136.476364, "used_cpu_sys_main_thread": 81531.244451, "used_cpu_user_main_thread": 114059.326093, "errorstat_ERR": {"object": true}, "errorstat_MISCONF": {"object": true}, "errorstat_NOAUTH": {"object": true}, "errorstat_WRONGPASS": {"object": true}, "cluster_enabled": 0, "db0": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Redis2": [{"startTime": 1710331887718, "executionTime": 166, "source": [{"previousNode": "Redis1"}], "executionStatus": "success", "data": {"main": [[{"json": {"TestKey": "Value1710331887344"}, "pairedItem": {"item": 0}}]]}}], "Redis3": [{"startTime": 1710331887885, "executionTime": 124, "source": [{"previousNode": "Redis2"}], "executionStatus": "success", "data": {"main": [[{"json": {"value": "Value1710331887344"}, "pairedItem": {"item": 0}}]]}}], "Redis4": [{"startTime": 1710331888009, "executionTime": 126, "source": [{"previousNode": "Redis3"}], "executionStatus": "success", "data": {"main": [[{"json": {"value": "Value1710331887344"}, "pairedItem": {"item": 0}}]]}}], "Function": [{"startTime": 1710331888135, "executionTime": 3, "source": [{"previousNode": "Redis3"}], "executionStatus": "success", "data": {"main": [[{"json": {"value": "Value1710331887344"}, "pairedItem": {"item": 0}, "index": 0}]]}}]}, "lastNodeExecuted": "Function"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2024-03-13T12:11:27.342Z", "stoppedAt": "2024-03-13T12:11:28.138Z", "status": "running", "finished": true}