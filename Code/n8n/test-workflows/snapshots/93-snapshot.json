{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747344011783, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "FunctionItem": [{"startTime": 1747344011783, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 1, "executionStatus": "success", "data": {"main": [[{"json": {"name": "test", "indexes": ["json array"], "subobj": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Move Binary Data": [{"startTime": 1747344011784, "executionIndex": 2, "source": [{"previousNode": "FunctionItem"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}, "binary": {"data": {"mimeType": "application/json", "fileType": "json", "fileExtension": "json", "data": "eyJuYW1lIjoidGVzdCIsImluZGV4ZXMiOlsxLDIsM10sInN1Ym9iaiI6eyJuYW1lIjoic3VidGVzdCJ9fQ==", "fileSize": "61 B"}}}]]}}], "Move Binary Data1": [{"startTime": 1747344011784, "executionIndex": 3, "source": [{"previousNode": "Move Binary Data"}], "hints": [], "executionTime": 1, "executionStatus": "success", "data": {"main": [[{"json": {"name": "test", "indexes": ["json array"], "subobj": {"object": true}}, "pairedItem": {"item": 0}, "binary": {}}]]}}], "Function": [{"startTime": 1747344011785, "executionIndex": 4, "source": [{"previousNode": "Move Binary Data1"}], "hints": [], "executionTime": 1, "executionStatus": "success", "data": {"main": [[{"json": {"name": "test", "indexes": ["json array"], "subobj": {"object": true}}, "pairedItem": {"item": 0}, "binary": {}, "index": 0}]]}}]}, "lastNodeExecuted": "Function"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:20:11.783Z", "stoppedAt": "2025-05-15T21:20:11.786Z", "status": "running", "finished": true}