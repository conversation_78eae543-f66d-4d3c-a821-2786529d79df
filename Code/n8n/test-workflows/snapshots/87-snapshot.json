{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"hints": [], "startTime": 1738078283500, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign": [{"hints": [], "startTime": 1738078283500, "executionTime": 1276, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"connection": {"object": true}, "meta": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign5": [{"hints": [], "startTime": 1738078284777, "executionTime": 947, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"email": "<EMAIL>", "cdate": "2025-01-28T09:31:25-06:00", "udate": "2025-01-28T09:31:25-06:00", "phone": "", "orgid": "0", "orgname": "", "links": {"object": true}, "id": "6714", "organization": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign8": [{"hints": [], "startTime": 1738078285724, "executionTime": 657, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"stringid": "testlist", "userid": "1", "name": "TestList", "cdate": "2021-03-02T10:30:17-06:00", "p_use_tracking": "1", "p_use_analytics_read": "0", "p_use_analytics_link": "0", "p_use_twitter": "0", "p_use_facebook": "0", "p_embed_image": "1", "p_use_captcha": "1", "send_last_broadcast": "0", "private": "0", "analytics_domains": {"object": true}, "analytics_source": "", "analytics_ua": "", "twitter_token": "", "twitter_token_secret": "", "facebook_session": {"object": true}, "carboncopy": {"object": true}, "subscription_notify": {"object": true}, "unsubscription_notify": {"object": true}, "require_name": "0", "get_unsubscribe_reason": "0", "to_name": "Subscriber", "optinoptout": "1", "sender_name": "", "sender_addr1": "", "sender_addr2": "", "sender_city": "", "sender_state": "", "sender_zip": "", "sender_country": "", "sender_phone": "", "sender_url": "https://n8n1612344344.activehosted.com", "sender_reminder": "You are receiving these emails because you are subscribed to our updates.", "fulladdress": "", "optinmessageid": "0", "optoutconf": "0", "deletestamp": {"object": true}, "udate": {"object": true}, "created_timestamp": "2021-03-02 10:30:17", "updated_timestamp": "2021-03-02 10:30:17", "created_by": {"object": true}, "updated_by": {"object": true}, "channel": "email", "description": {"object": true}, "non_deleted_subscribers": "4", "active_subscribers": "2", "links": {"object": true}, "id": "1", "user": "1"}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign9": [{"hints": [], "startTime": 1738078286381, "executionTime": 717, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"tag": "Tag1738078286384", "tagType": "contact", "cdate": "2025-01-28T09:31:27-06:00", "links": {"object": true}, "id": "1991"}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign1": [{"hints": [], "startTime": 1738078287099, "executionTime": 930, "source": [{"previousNode": "ActiveCampaign"}], "executionStatus": "success", "data": {"main": [[{"json": {"connection": {"object": true}, "meta": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign7": [{"hints": [], "startTime": 1738078288029, "executionTime": 777, "source": [{"previousNode": "ActiveCampaign5"}], "executionStatus": "success", "data": {"main": [[{"json": {"contacts": ["json array"], "dealGroups": ["json array"], "dealStages": ["json array"], "deal": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign10": [{"hints": [], "startTime": 1738078288806, "executionTime": 585, "source": [{"previousNode": "ActiveCampaign9"}], "executionStatus": "success", "data": {"main": [[{"json": {"tagType": "contact", "tag": "Tag1738078286384", "description": "", "subscriber_count": "0", "cdate": "2025-01-28T09:31:27-06:00", "created_timestamp": "2025-01-28 09:31:27", "updated_timestamp": "2025-01-28 09:31:27", "created_by": {"object": true}, "updated_by": {"object": true}, "deleted": "0", "links": {"object": true}, "id": "1991"}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign2": [{"hints": [], "startTime": 1738078289392, "executionTime": 529, "source": [{"previousNode": "ActiveCampaign1"}], "executionStatus": "success", "data": {"main": [[{"json": {"connection": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign14": [{"hints": [], "startTime": 1738078289922, "executionTime": 686, "source": [{"previousNode": "ActiveCampaign7"}], "executionStatus": "success", "data": {"main": [[{"json": {"dealStages": ["json array"], "deal": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign11": [{"hints": [], "startTime": 1738078290609, "executionTime": 602, "source": [{"previousNode": "ActiveCampaign10"}], "executionStatus": "success", "data": {"main": [[{"json": {"tag": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign3": [{"hints": [], "startTime": 1738078291212, "executionTime": 476, "source": [{"previousNode": "ActiveCampaign2"}], "executionStatus": "success", "data": {"main": [[{"json": {"service": "n8n", "externalid": "1621002648800", "name": "Name1621002648800", "isInternal": "0", "connectionType": "ecommerce", "status": "1", "syncStatus": "0", "sync_request_time": {"object": true}, "sync_start_time": {"object": true}, "lastSync": {"object": true}, "logoUrl": "https://n8n.io/_nuxt/img/df5be1c.png", "linkUrl": "https://n8n.io/", "credentialExpiration": {"object": true}, "cdate": "2021-05-14T09:31:09-05:00", "udate": "2021-05-14T09:31:09-05:00", "disconnectDate": {"object": true}, "listId": {"object": true}, "planTier": {"object": true}, "links": {"object": true}, "id": "92", "serviceName": "n8n"}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign15": [{"hints": [], "startTime": 1738078291689, "executionTime": 704, "source": [{"previousNode": "ActiveCampaign14"}], "executionStatus": "success", "data": {"main": [[{"json": {"deal": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign12": [{"hints": [], "startTime": 1738078292393, "executionTime": 464, "source": [{"previousNode": "ActiveCampaign11"}], "executionStatus": "success", "data": {"main": [[{"json": {"tagType": "contact", "tag": "TestTag", "description": "", "subscriber_count": "2", "cdate": "2021-03-02T10:29:14-06:00", "created_timestamp": "2021-03-02 10:29:14", "updated_timestamp": "2025-01-28 09:31:31", "created_by": {"object": true}, "updated_by": {"object": true}, "deleted": "0", "links": {"object": true}, "id": "1"}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign4": [{"hints": [], "startTime": 1738078292858, "executionTime": 966, "source": [{"previousNode": "ActiveCampaign3"}], "executionStatus": "success", "data": {"main": [[{"json": {"meta": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign16": [{"hints": [], "startTime": 1738078293824, "executionTime": 681, "source": [{"previousNode": "ActiveCampaign15"}], "executionStatus": "success", "data": {"main": [[{"json": {"deals": ["json array"], "note": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign13": [{"hints": [], "startTime": 1738078294505, "executionTime": 631, "source": [{"previousNode": "ActiveCampaign12"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign17": [{"hints": [], "startTime": 1738078295137, "executionTime": 616, "source": [{"previousNode": "ActiveCampaign16"}], "executionStatus": "success", "data": {"main": [[{"json": {"users": ["json array"], "deals": ["json array"], "note": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign18": [{"hints": [], "startTime": 1738078295754, "executionTime": 613, "source": [{"previousNode": "ActiveCampaign17"}], "executionStatus": "success", "data": {"main": [[{"json": {"hash": "069b44df", "owner": "1", "contact": "6714", "organization": {"object": true}, "group": "1", "stage": "1", "title": "Title1738078288034", "description": "", "percent": "0", "cdate": "2025-01-28T09:31:28-06:00", "mdate": "2025-01-28T09:31:34-06:00", "nextdate": {"object": true}, "nexttaskid": "0", "value": "201", "currency": "usd", "winProbability": {"object": true}, "winProbabilityMdate": {"object": true}, "status": "0", "activitycount": "4", "nextdealid": "1780", "edate": {"object": true}, "links": {"object": true}, "id": "1780", "isDisabled": false, "nextTask": {"object": true}, "account": {"object": true}, "customerAccount": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign19": [{"hints": [], "startTime": *************, "executionTime": 732, "source": [{"previousNode": "ActiveCampaign18"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign6": [{"hints": [], "startTime": *************, "executionTime": 713, "source": [{"previousNode": "ActiveCampaign19"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "ActiveCampaign6"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-01-28T15:31:23.497Z", "stoppedAt": "2025-01-28T15:31:37.813Z", "status": "running", "finished": true}