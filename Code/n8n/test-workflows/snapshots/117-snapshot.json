{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1710331887222, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Gotify": [{"startTime": 1710331887223, "executionTime": 287, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 1306, "appid": 1, "message": "Message content 1710331887225", "title": "Title1710331887228", "priority": 0, "date": "2024-03-13T12:11:27.484571026Z"}, "pairedItem": {"item": 0}}]]}}], "Gotify1": [{"startTime": 1710331887510, "executionTime": 58, "source": [{"previousNode": "Gotify"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 1306, "appid": 1, "message": "Message content 1710331887225", "title": "Title1710331887228", "priority": 0, "date": "2024-03-13T12:11:27.484571026Z"}, "pairedItem": {"item": 0}}]]}}], "Gotify2": [{"startTime": 1710331887568, "executionTime": 62, "source": [{"previousNode": "Gotify1"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Gotify2"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2024-03-13T12:11:27.222Z", "stoppedAt": "2024-03-13T12:11:27.630Z", "status": "running", "finished": true}