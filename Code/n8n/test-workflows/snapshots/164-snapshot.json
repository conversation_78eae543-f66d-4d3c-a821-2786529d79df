{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1676891416118, "executionTime": 1, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Set": [{"startTime": 1676891416119, "executionTime": 22, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"name": "SpaceTag1676891416141"}, "pairedItem": {"item": 0}}]]}}], "ClickUp1": [{"startTime": 1676891416142, "executionTime": 735, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "901000766205", "name": "TestList1676891416143", "deleted": false, "orderindex": 0, "content": "", "priority": {"object": true}, "assignee": {"object": true}, "due_date": {"object": true}, "start_date": {"object": true}, "folder": {"object": true}, "space": {"object": true}, "inbound_address": "<EMAIL>", "archived": false, "override_statuses": false, "statuses": ["json array"], "permission_level": "create"}, "pairedItem": {"item": 0}}]]}}], "ClickUp": [{"startTime": 1676891416877, "executionTime": 345, "source": [{"previousNode": "Set"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.8 second": [{"startTime": 1676891417222, "executionTime": 826, "source": [{"previousNode": "ClickUp1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "901000766205", "name": "TestList1676891416143", "deleted": false, "orderindex": 0, "content": "", "priority": {"object": true}, "assignee": {"object": true}, "due_date": {"object": true}, "start_date": {"object": true}, "folder": {"object": true}, "space": {"object": true}, "inbound_address": "<EMAIL>", "archived": false, "override_statuses": false, "statuses": ["json array"], "permission_level": "create"}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Sleep 0.8 second1": [{"startTime": 1676891418048, "executionTime": 804, "source": [{"previousNode": "ClickUp"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}, "index": 0}]]}}], "ClickUp7": [{"startTime": 1676891418852, "executionTime": 477, "source": [{"previousNode": "Sleep 0.8 second"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "901000766208", "name": "TestList1676891418852", "deleted": false, "orderindex": 0, "content": "", "priority": {"object": true}, "assignee": {"object": true}, "due_date": {"object": true}, "start_date": {"object": true}, "folder": {"object": true}, "space": {"object": true}, "inbound_address": "<EMAIL>", "archived": false, "override_statuses": false, "statuses": ["json array"], "permission_level": "create"}, "pairedItem": {"item": 0}}]]}}], "ClickUp2": [{"startTime": 1676891419329, "executionTime": 411, "source": [{"previousNode": "Sleep 0.8 second1"}], "executionStatus": "success", "data": {"main": [[{"json": {"name": "spacetag1626268329609", "tag_fg": "#fff", "tag_bg": "#FF6D5A", "creator": 8779387}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.8 second5": [{"startTime": 1676891419740, "executionTime": 804, "source": [{"previousNode": "ClickUp7"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "901000766208", "name": "TestList1676891418852", "deleted": false, "orderindex": 0, "content": "", "priority": {"object": true}, "assignee": {"object": true}, "due_date": {"object": true}, "start_date": {"object": true}, "folder": {"object": true}, "space": {"object": true}, "inbound_address": "<EMAIL>", "archived": false, "override_statuses": false, "statuses": ["json array"], "permission_level": "create"}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Set1": [{"startTime": 1676891420544, "executionTime": 1, "source": [{"previousNode": "ClickUp2"}], "executionStatus": "success", "data": {"main": [[{"json": {"name": "spacetag1626268329609", "tag_fg": "#fff", "tag_bg": "#FF6D5A", "creator": 8779387, "updatedname": "UpdatedSpaceTag1676891420545"}, "pairedItem": {"item": 0}}]]}}], "ClickUp5": [{"startTime": 1676891420545, "executionTime": 780, "source": [{"previousNode": "Sleep 0.8 second5"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "8677f32j9", "custom_id": {"object": true}, "name": "testTask1676891420545", "text_content": "", "description": "", "status": {"object": true}, "orderindex": "2978.00000000000000000000000000000000", "date_created": "1676891420964", "date_updated": "1676891420964", "date_closed": {"object": true}, "date_done": {"object": true}, "archived": false, "creator": {"object": true}, "assignees": ["json array"], "watchers": ["json array"], "checklists": ["json array"], "tags": ["json array"], "parent": {"object": true}, "priority": {"object": true}, "due_date": {"object": true}, "start_date": {"object": true}, "points": {"object": true}, "time_estimate": {"object": true}, "time_spent": 0, "custom_fields": ["json array"], "dependencies": ["json array"], "linked_tasks": ["json array"], "team_id": "4651110", "url": "https://app.clickup.com/t/8677f32j9", "permission_level": "create", "list": {"object": true}, "project": {"object": true}, "folder": {"object": true}, "space": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.8 second2": [{"startTime": 1676891421325, "executionTime": 804, "source": [{"previousNode": "Set1"}], "executionStatus": "success", "data": {"main": [[{"json": {"name": "spacetag1626268329609", "tag_fg": "#fff", "tag_bg": "#FF6D5A", "creator": 8779387, "updatedname": "UpdatedSpaceTag1676891420545"}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Sleep 0.8 second4": [{"startTime": 1676891422130, "executionTime": 804, "source": [{"previousNode": "ClickUp5"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "8677f32j9", "custom_id": {"object": true}, "name": "testTask1676891420545", "text_content": "", "description": "", "status": {"object": true}, "orderindex": "2978.00000000000000000000000000000000", "date_created": "1676891420964", "date_updated": "1676891420964", "date_closed": {"object": true}, "date_done": {"object": true}, "archived": false, "creator": {"object": true}, "assignees": ["json array"], "watchers": ["json array"], "checklists": ["json array"], "tags": ["json array"], "parent": {"object": true}, "priority": {"object": true}, "due_date": {"object": true}, "start_date": {"object": true}, "points": {"object": true}, "time_estimate": {"object": true}, "time_spent": 0, "custom_fields": ["json array"], "dependencies": ["json array"], "linked_tasks": ["json array"], "team_id": "4651110", "url": "https://app.clickup.com/t/8677f32j9", "permission_level": "create", "list": {"object": true}, "project": {"object": true}, "folder": {"object": true}, "space": {"object": true}}, "pairedItem": {"item": 0}, "index": 0}]]}}], "ClickUp3": [{"startTime": 1676891422935, "executionTime": 563, "source": [{"previousNode": "Sleep 0.8 second2"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Set2": [{"startTime": 1676891423498, "executionTime": 1, "source": [{"previousNode": "Sleep 0.8 second4"}], "executionStatus": "success", "data": {"main": [[{"json": {"name": "TaskTag1676891423498"}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.8 second3": [{"startTime": 1676891423499, "executionTime": 814, "source": [{"previousNode": "ClickUp3"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}, "index": 0}]]}}], "ClickUp8": [{"startTime": 1676891424316, "executionTime": 420, "source": [{"previousNode": "Set2"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "ClickUp4": [{"startTime": 1676891424736, "executionTime": 407, "source": [{"previousNode": "Sleep 0.8 second3"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.8 second6": [{"startTime": 1676891425143, "executionTime": 803, "source": [{"previousNode": "ClickUp8"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}, "index": 0}]]}}], "ClickUp9": [{"startTime": 1676891425946, "executionTime": 374, "source": [{"previousNode": "Sleep 0.8 second6"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.8 second7": [{"startTime": 1676891426321, "executionTime": 804, "source": [{"previousNode": "ClickUp9"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}, "index": 0}]]}}], "ClickUp6": [{"startTime": 1676891427125, "executionTime": 476, "source": [{"previousNode": "Sleep 0.8 second7"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.8 second8": [{"startTime": 1676891427601, "executionTime": 809, "source": [{"previousNode": "ClickUp6"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}, "index": 0}]]}}], "ClickUp10": [{"startTime": 1676891428410, "executionTime": 391, "source": [{"previousNode": "Sleep 0.8 second8"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "ClickUp11": [{"startTime": 1676891428802, "executionTime": 427, "source": [{"previousNode": "ClickUp10"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "ClickUp12": [{"startTime": 1676891429230, "executionTime": 430, "source": [{"previousNode": "ClickUp11"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "ClickUp13": [{"startTime": 1676891429661, "executionTime": 1616, "source": [{"previousNode": "ClickUp12"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "ClickUp13"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2023-02-20T11:10:16.115Z", "stoppedAt": "2023-02-20T11:10:31.277Z", "status": "running", "finished": true}