{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343994529, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "PostHog": [{"startTime": 1747343994529, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 175, "executionStatus": "success", "data": {"main": [[{"json": {"status": 1}, "pairedItem": {"item": 0}}]]}}], "PostHog1": [{"startTime": 1747343994704, "executionIndex": 2, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 79, "executionStatus": "success", "data": {"main": [[{"json": {"status": 1}, "pairedItem": {"item": 0}}]]}}], "PostHog2": [{"startTime": 1747343994783, "executionIndex": 3, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 75, "executionStatus": "success", "data": {"main": [[{"json": {"status": 1}, "pairedItem": {"item": 0}}]]}}], "PostHog3": [{"startTime": 1747343994858, "executionIndex": 4, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 82, "executionStatus": "success", "data": {"main": [[{"json": {"status": 1}, "pairedItem": {"item": 0}}]]}}], "PostHog4": [{"startTime": 1747343994940, "executionIndex": 5, "source": [{"previousNode": "PostHog3"}], "hints": [], "executionTime": 67, "executionStatus": "success", "data": {"main": [[{"json": {"status": 1}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "PostHog4"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:54.529Z", "stoppedAt": "2025-05-15T21:19:55.007Z", "status": "running", "finished": true}