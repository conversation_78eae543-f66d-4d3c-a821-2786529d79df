{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747344001850, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Drift ": [{"startTime": 1747344001850, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 632, "executionStatus": "success", "data": {"main": [[{"json": {"id": 25164082913, "createdAt": 1747344002321, "attributes": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Drift 1": [{"startTime": 1747344002482, "executionIndex": 2, "source": [{"previousNode": "Drift "}], "hints": [], "executionTime": 527, "executionStatus": "success", "data": {"main": [[{"json": {"id": 25164082913, "createdAt": 1747344002321, "attributes": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Drift 2": [{"startTime": 1747344003010, "executionIndex": 3, "source": [{"previousNode": "Drift 1"}], "hints": [], "executionTime": 520, "executionStatus": "success", "data": {"main": [[{"json": {"id": 25164082913, "createdAt": 1747344002321, "attributes": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Drift 3": [{"startTime": 1747344003530, "executionIndex": 4, "source": [{"previousNode": "Drift 2"}], "hints": [], "executionTime": 466, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Drift 3"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:20:01.850Z", "stoppedAt": "2025-05-15T21:20:03.996Z", "status": "running", "finished": true}