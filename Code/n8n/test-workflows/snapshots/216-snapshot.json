{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343995559, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Handle JSON data": [{"startTime": 1747343995559, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 2, "executionStatus": "success", "data": {"main": [[{"json": {"arr": ["json array"], "str": "Testing Function Item node", "num": 1337, "obj": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Handle Binary data": [{"startTime": 1747343995561, "executionIndex": 2, "source": [{"previousNode": "Handle JSON data"}], "hints": [], "executionTime": 1, "executionStatus": "success", "data": {"main": [[{"json": {"arr": ["json array"], "str": "Testing Function Item node", "num": 1337, "obj": {"object": true}, "binaryData": "NIL"}, "pairedItem": {"item": 0}, "binary": {"data": {"data": "SGVsbG8gZnJvbSBuOG4gRnVuY3Rpb24gaXRlbSB0ZXN0aW5nIHdvcmtmbG93", "mimeType": "text", "fileExtension": "text", "fileName": "testfile"}}}]]}}], "Handle Static data": [{"startTime": 1747343995562, "executionIndex": 3, "source": [{"previousNode": "Handle Binary data"}], "hints": [], "executionTime": 3, "executionStatus": "success", "data": {"main": [[{"json": {"arr": ["json array"], "str": "Testing Function Item node", "num": 1337, "obj": {"object": true}, "binaryData": "NIL", "globalStaticMessage": "Hello, Global Static Data", "nodeStaticMessage": "Hello, Node Static Data"}, "pairedItem": {"item": 0}, "binary": {"data": {"data": "SGVsbG8gZnJvbSBuOG4gRnVuY3Rpb24gaXRlbSB0ZXN0aW5nIHdvcmtmbG93", "mimeType": "text", "fileExtension": "text", "fileName": "testfile"}}}]]}}]}, "lastNodeExecuted": "Handle Static data"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:55.559Z", "stoppedAt": "2025-05-15T21:19:55.565Z", "status": "running", "finished": true}