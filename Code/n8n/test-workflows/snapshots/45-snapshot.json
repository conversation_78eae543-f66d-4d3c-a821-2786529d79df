{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1678116858649, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Google Sheets": [{"startTime": 1678116858650, "executionTime": 2218, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"spreadsheetId": "13KroPsxIAWmV8Q73df1-5OhI0aAG64Ch74waTZqUYhU", "properties": {"object": true}, "sheets": ["json array"], "spreadsheetUrl": "https://docs.google.com/spreadsheets/d/13KroPsxIAWmV8Q73df1-5OhI0aAG64Ch74waTZqUYhU/edit"}, "pairedItem": {"item": 0}}]]}}], "Function": [{"startTime": 1678116860868, "executionTime": 13, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 861, "name": "test1"}, "pairedItem": {"item": 0}}, {"json": {"id": 61, "name": "test2"}, "pairedItem": {"item": 0}}]]}}], "Google Sheets7": [{"startTime": 1678116860881, "executionTime": 1007, "source": [{"previousNode": "Google Sheets"}], "executionStatus": "success", "data": {"main": [[{"json": {"spreadsheetId": "13KroPsxIAWmV8Q73df1-5OhI0aAG64Ch74waTZqUYhU", "sheetId": 826802864, "title": "Sheet2", "index": 1, "sheetType": "GRID", "gridProperties": {"object": true}, "tabColor": {"object": true}, "tabColorStyle": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Google Sheets1": [{"startTime": 1678116861889, "executionTime": 1273, "source": [{"previousNode": "Function"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 861, "name": "test1"}, "pairedItem": {"item": 0}}, {"json": {"id": 61, "name": "test2"}, "pairedItem": {"item": 1}}]]}}], "Google Sheets8": [{"startTime": 1678116863162, "executionTime": 577, "source": [{"previousNode": "Google Sheets7"}], "executionStatus": "success", "data": {"main": [[{"json": {"spreadsheetId": "13KroPsxIAWmV8Q73df1-5OhI0aAG64Ch74waTZqUYhU"}, "pairedItem": {"item": 0}}]]}}], "Google Sheets3": [{"startTime": 1678116863740, "executionTime": 336, "source": [{"previousNode": "Google Sheets1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 861, "name": "test1"}, "pairedItem": {"item": 0}}, {"json": {"id": 61, "name": "test2"}, "pairedItem": {"item": 1}}]]}}], "Google Sheets4": [{"startTime": 1678116864076, "executionTime": 246, "source": [{"previousNode": "Google Sheets3"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 861, "name": "test1"}, "pairedItem": {"item": 0}}, {"json": {"id": 861, "name": "test1"}, "pairedItem": {"item": 1}}]]}}], "Set1": [{"startTime": 1678116864322, "executionTime": 2, "source": [{"previousNode": "Google Sheets4"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 861, "name": "Updated test1"}, "pairedItem": {"item": 0}}, {"json": {"id": 61, "name": "Updated test2"}, "pairedItem": {"item": 1}}]]}}], "Google Sheets2": [{"startTime": 1678116864325, "executionTime": 1044, "source": [{"previousNode": "Set1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 861, "name": "Updated test1"}, "pairedItem": {"item": 0}}, {"json": {"id": 61, "name": "Updated test2"}, "pairedItem": {"item": 1}}]]}}], "Google Sheets6": [{"startTime": 1678116865369, "executionTime": 510, "source": [{"previousNode": "Google Sheets2"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 861, "name": "Updated test1"}, "pairedItem": {"item": 0}}, {"json": {"id": 61, "name": "Updated test2"}, "pairedItem": {"item": 1}}]]}}], "Google Sheets5": [{"startTime": 1678116865879, "executionTime": 413, "source": [{"previousNode": "Google Sheets6"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 861, "name": "Updated test1"}, "pairedItem": {"item": 0}}, {"json": {"id": 61, "name": "Updated test2"}, "pairedItem": {"item": 1}}]]}}]}, "lastNodeExecuted": "Google Sheets5"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2023-03-06T15:34:18.647Z", "stoppedAt": "2023-03-06T15:34:26.292Z", "status": "running", "finished": true}