{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343994645, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "OpenThesaurus": [{"startTime": 1747343994645, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 185, "executionStatus": "success", "data": {"main": [[{"json": {"id": 3913, "categories": ["json array"], "terms": ["json array"]}, "pairedItem": {"item": 0}}, {"json": {"id": 11705, "categories": ["json array"], "terms": ["json array"]}, "pairedItem": {"item": 0}}, {"json": {"id": 8559, "categories": ["json array"], "terms": ["json array"]}, "pairedItem": {"item": 0}}, {"json": {"id": 15485, "categories": ["json array"], "terms": ["json array"]}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "OpenThesaurus"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:54.645Z", "stoppedAt": "2025-05-15T21:19:54.830Z", "status": "running", "finished": true}