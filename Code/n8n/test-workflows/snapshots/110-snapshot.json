{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343907477, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "GraphQL": [{"startTime": 1747343907477, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 177, "executionStatus": "success", "data": {"main": [[{"json": {"data": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Function": [{"startTime": 1747343907655, "executionIndex": 2, "source": [{"previousNode": "GraphQL"}], "hints": [], "executionTime": 4, "executionStatus": "success", "data": {"main": [[{"json": {"data": {"object": true}}, "pairedItem": {"item": 0}, "index": 0}]]}}]}, "lastNodeExecuted": "Function"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:18:27.477Z", "stoppedAt": "2025-05-15T21:18:27.659Z", "status": "running", "finished": true}