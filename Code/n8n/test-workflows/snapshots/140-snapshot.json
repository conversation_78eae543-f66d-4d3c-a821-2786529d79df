{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343994520, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Mailgun": [{"startTime": 1747343994520, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 689, "executionStatus": "success", "data": {"main": [[{"json": {"id": "<<EMAIL>>", "message": "Queued. Thank you."}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Mailgun"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:54.520Z", "stoppedAt": "2025-05-15T21:19:55.209Z", "status": "running", "finished": true}