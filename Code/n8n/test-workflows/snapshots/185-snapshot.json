{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1705409098608, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Salesforce": [{"startTime": 1705409098608, "executionTime": 437, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "0030900002C8F6SAAV", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Salesforce8": [{"startTime": 1705409099045, "executionTime": 172, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "a000900000jSn74AAC", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Salesforce13": [{"startTime": 1705409099218, "executionTime": 178, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"label": "FixedFlow", "name": "FixedFlow", "type": "FLOW", "url": "/services/data/v59.0/actions/custom/flow/FixedFlow"}, "pairedItem": {"item": 0}}]]}}], "Salesforce15": [{"startTime": 1705409099396, "executionTime": 280, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "00Q0900000Q92tNEAR", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Salesforce1": [{"startTime": *************, "executionTime": 179, "source": [{"previousNode": "Salesforce"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "0030900002C8F6SAAV", "IsDeleted": false, "MasterRecordId": {"object": true}, "AccountId": {"object": true}, "LastName": "Contact1705409098611", "FirstName": {"object": true}, "Salutation": {"object": true}, "Name": "Contact1705409098611", "OtherStreet": {"object": true}, "OtherCity": {"object": true}, "OtherState": {"object": true}, "OtherPostalCode": {"object": true}, "OtherCountry": {"object": true}, "OtherLatitude": {"object": true}, "OtherLongitude": {"object": true}, "OtherGeocodeAccuracy": {"object": true}, "OtherAddress": {"object": true}, "MailingStreet": {"object": true}, "MailingCity": {"object": true}, "MailingState": {"object": true}, "MailingPostalCode": {"object": true}, "MailingCountry": {"object": true}, "MailingLatitude": {"object": true}, "MailingLongitude": {"object": true}, "MailingGeocodeAccuracy": {"object": true}, "MailingAddress": {"object": true}, "Phone": {"object": true}, "Fax": {"object": true}, "MobilePhone": {"object": true}, "HomePhone": {"object": true}, "OtherPhone": {"object": true}, "AssistantPhone": {"object": true}, "ReportsToId": {"object": true}, "Email": {"object": true}, "Title": {"object": true}, "Department": {"object": true}, "AssistantName": {"object": true}, "LeadSource": {"object": true}, "Birthdate": {"object": true}, "Description": {"object": true}, "OwnerId": "00509000005ntkGAAQ", "CreatedDate": "2024-01-16T12:44:59.000+0000", "CreatedById": "00509000005ntkGAAQ", "LastModifiedDate": "2024-01-16T12:44:59.000+0000", "LastModifiedById": "00509000005ntkGAAQ", "SystemModstamp": "2024-01-16T12:44:59.000+0000", "LastActivityDate": {"object": true}, "LastCURequestDate": {"object": true}, "LastCUUpdateDate": {"object": true}, "LastViewedDate": "2024-01-16T12:44:59.000+0000", "LastReferencedDate": "2024-01-16T12:44:59.000+0000", "EmailBouncedReason": {"object": true}, "EmailBouncedDate": {"object": true}, "IsEmailBounced": false, "PhotoUrl": "/services/images/photo/0030900002C8F6SAAV", "Jigsaw": {"object": true}, "JigsawContactId": {"object": true}, "CleanStatus": "Pending", "IndividualId": {"object": true}, "IsPriorityRecord": false, "Level__c": {"object": true}, "Languages__c": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.5 second7": [{"startTime": 1705409099857, "executionTime": 506, "source": [{"previousNode": "Salesforce8"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "a000900000jSn74AAC", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Salesforce14": [{"startTime": 1705409100366, "executionTime": 0, "source": [{"previousNode": "Salesforce13"}], "executionStatus": "success", "data": {"main": [[{"json": {"label": "FixedFlow", "name": "FixedFlow", "type": "FLOW", "url": "/services/data/v59.0/actions/custom/flow/FixedFlow"}, "pairedItem": {"item": 0}}]]}}], "Salesforce16": [{"startTime": 1705409100366, "executionTime": 175, "source": [{"previousNode": "Salesforce15"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "00Q0900000Q92tNEAR", "IsDeleted": false, "MasterRecordId": {"object": true}, "LastName": "LastName1705409099400", "FirstName": {"object": true}, "Salutation": {"object": true}, "Name": "LastName1705409099400", "Title": {"object": true}, "Company": "n8n", "Street": {"object": true}, "City": {"object": true}, "State": {"object": true}, "PostalCode": {"object": true}, "Country": {"object": true}, "Latitude": {"object": true}, "Longitude": {"object": true}, "GeocodeAccuracy": {"object": true}, "Address": {"object": true}, "Phone": {"object": true}, "MobilePhone": {"object": true}, "Fax": {"object": true}, "Email": {"object": true}, "Website": {"object": true}, "PhotoUrl": "/services/images/photo/00Q0900000Q92tNEAR", "Description": {"object": true}, "LeadSource": {"object": true}, "Status": "Open - Not Contacted", "Industry": {"object": true}, "Rating": {"object": true}, "AnnualRevenue": {"object": true}, "NumberOfEmployees": {"object": true}, "OwnerId": "00509000005ntkGAAQ", "IsConverted": false, "ConvertedDate": {"object": true}, "ConvertedAccountId": {"object": true}, "ConvertedContactId": {"object": true}, "ConvertedOpportunityId": {"object": true}, "IsUnreadByOwner": true, "CreatedDate": "2024-01-16T12:44:59.000+0000", "CreatedById": "00509000005ntkGAAQ", "LastModifiedDate": "2024-01-16T12:44:59.000+0000", "LastModifiedById": "00509000005ntkGAAQ", "SystemModstamp": "2024-01-16T12:44:59.000+0000", "LastActivityDate": {"object": true}, "LastViewedDate": "2024-01-16T12:44:59.000+0000", "LastReferencedDate": "2024-01-16T12:44:59.000+0000", "Jigsaw": {"object": true}, "JigsawContactId": {"object": true}, "CleanStatus": "Pending", "CompanyDunsNumber": {"object": true}, "DandbCompanyId": {"object": true}, "EmailBouncedReason": {"object": true}, "EmailBouncedDate": {"object": true}, "IndividualId": {"object": true}, "IsPriorityRecord": false, "SICCode__c": {"object": true}, "ProductInterest__c": {"object": true}, "Primary__c": {"object": true}, "CurrentGenerators__c": {"object": true}, "NumberofLocations__c": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.5 second1": [{"startTime": *************, "executionTime": 505, "source": [{"previousNode": "Salesforce1"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "0030900002C8F6SAAV", "IsDeleted": false, "MasterRecordId": {"object": true}, "AccountId": {"object": true}, "LastName": "Contact1705409098611", "FirstName": {"object": true}, "Salutation": {"object": true}, "Name": "Contact1705409098611", "OtherStreet": {"object": true}, "OtherCity": {"object": true}, "OtherState": {"object": true}, "OtherPostalCode": {"object": true}, "OtherCountry": {"object": true}, "OtherLatitude": {"object": true}, "OtherLongitude": {"object": true}, "OtherGeocodeAccuracy": {"object": true}, "OtherAddress": {"object": true}, "MailingStreet": {"object": true}, "MailingCity": {"object": true}, "MailingState": {"object": true}, "MailingPostalCode": {"object": true}, "MailingCountry": {"object": true}, "MailingLatitude": {"object": true}, "MailingLongitude": {"object": true}, "MailingGeocodeAccuracy": {"object": true}, "MailingAddress": {"object": true}, "Phone": {"object": true}, "Fax": {"object": true}, "MobilePhone": {"object": true}, "HomePhone": {"object": true}, "OtherPhone": {"object": true}, "AssistantPhone": {"object": true}, "ReportsToId": {"object": true}, "Email": {"object": true}, "Title": {"object": true}, "Department": {"object": true}, "AssistantName": {"object": true}, "LeadSource": {"object": true}, "Birthdate": {"object": true}, "Description": {"object": true}, "OwnerId": "00509000005ntkGAAQ", "CreatedDate": "2024-01-16T12:44:59.000+0000", "CreatedById": "00509000005ntkGAAQ", "LastModifiedDate": "2024-01-16T12:44:59.000+0000", "LastModifiedById": "00509000005ntkGAAQ", "SystemModstamp": "2024-01-16T12:44:59.000+0000", "LastActivityDate": {"object": true}, "LastCURequestDate": {"object": true}, "LastCUUpdateDate": {"object": true}, "LastViewedDate": "2024-01-16T12:44:59.000+0000", "LastReferencedDate": "2024-01-16T12:44:59.000+0000", "EmailBouncedReason": {"object": true}, "EmailBouncedDate": {"object": true}, "IsEmailBounced": false, "PhotoUrl": "/services/images/photo/0030900002C8F6SAAV", "Jigsaw": {"object": true}, "JigsawContactId": {"object": true}, "CleanStatus": "Pending", "IndividualId": {"object": true}, "IsPriorityRecord": false, "Level__c": {"object": true}, "Languages__c": {"object": true}}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Salesforce9": [{"startTime": 1705409101048, "executionTime": 237, "source": [{"previousNode": "Sleep 0.5 second7"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "a000900000jSn74AAC", "OwnerId": "00509000005ntkGAAQ", "IsDeleted": false, "Name": "TestCustomObjectFixed", "CreatedDate": "2024-01-16T12:44:59.000+0000", "CreatedById": "00509000005ntkGAAQ", "LastModifiedDate": "2024-01-16T12:44:59.000+0000", "LastModifiedById": "00509000005ntkGAAQ", "SystemModstamp": "2024-01-16T12:44:59.000+0000"}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.5 second": [{"startTime": 1705409101285, "executionTime": 507, "source": [{"previousNode": "Salesforce16"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "00Q0900000Q92tNEAR", "IsDeleted": false, "MasterRecordId": {"object": true}, "LastName": "LastName1705409099400", "FirstName": {"object": true}, "Salutation": {"object": true}, "Name": "LastName1705409099400", "Title": {"object": true}, "Company": "n8n", "Street": {"object": true}, "City": {"object": true}, "State": {"object": true}, "PostalCode": {"object": true}, "Country": {"object": true}, "Latitude": {"object": true}, "Longitude": {"object": true}, "GeocodeAccuracy": {"object": true}, "Address": {"object": true}, "Phone": {"object": true}, "MobilePhone": {"object": true}, "Fax": {"object": true}, "Email": {"object": true}, "Website": {"object": true}, "PhotoUrl": "/services/images/photo/00Q0900000Q92tNEAR", "Description": {"object": true}, "LeadSource": {"object": true}, "Status": "Open - Not Contacted", "Industry": {"object": true}, "Rating": {"object": true}, "AnnualRevenue": {"object": true}, "NumberOfEmployees": {"object": true}, "OwnerId": "00509000005ntkGAAQ", "IsConverted": false, "ConvertedDate": {"object": true}, "ConvertedAccountId": {"object": true}, "ConvertedContactId": {"object": true}, "ConvertedOpportunityId": {"object": true}, "IsUnreadByOwner": true, "CreatedDate": "2024-01-16T12:44:59.000+0000", "CreatedById": "00509000005ntkGAAQ", "LastModifiedDate": "2024-01-16T12:44:59.000+0000", "LastModifiedById": "00509000005ntkGAAQ", "SystemModstamp": "2024-01-16T12:44:59.000+0000", "LastActivityDate": {"object": true}, "LastViewedDate": "2024-01-16T12:44:59.000+0000", "LastReferencedDate": "2024-01-16T12:44:59.000+0000", "Jigsaw": {"object": true}, "JigsawContactId": {"object": true}, "CleanStatus": "Pending", "CompanyDunsNumber": {"object": true}, "DandbCompanyId": {"object": true}, "EmailBouncedReason": {"object": true}, "EmailBouncedDate": {"object": true}, "IndividualId": {"object": true}, "IsPriorityRecord": false, "SICCode__c": {"object": true}, "ProductInterest__c": {"object": true}, "Primary__c": {"object": true}, "CurrentGenerators__c": {"object": true}, "NumberofLocations__c": {"object": true}}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Salesforce2": [{"startTime": 1705409101792, "executionTime": 173, "source": [{"previousNode": "Sleep 0.5 second1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "002090000011nDlAAI", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Salesforce10": [{"startTime": 1705409101966, "executionTime": 131, "source": [{"previousNode": "Salesforce9"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "a0009000008QNMNAA4"}, "pairedItem": {"item": 0}}]]}}], "Salesforce17": [{"startTime": 1705409102098, "executionTime": 203, "source": [{"previousNode": "Sleep 0.5 second"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "002090000011nDvAAI", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Salesforce3": [{"startTime": 1705409102302, "executionTime": 166, "source": [{"previousNode": "Salesforce2"}], "executionStatus": "success", "data": {"main": [[{"json": {"objectDescribe": {"object": true}, "recentItems": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.5 second6": [{"startTime": 1705409102468, "executionTime": 506, "source": [{"previousNode": "Salesforce10"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "a0009000008QNMNAA4"}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Salesforce18": [{"startTime": 1705409102975, "executionTime": 160, "source": [{"previousNode": "Salesforce17"}], "executionStatus": "success", "data": {"main": [[{"json": {"objectDescribe": {"object": true}, "recentItems": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.5 second3": [{"startTime": 1705409103135, "executionTime": 505, "source": [{"previousNode": "Salesforce3"}], "executionStatus": "success", "data": {"main": [[{"json": {"objectDescribe": {"object": true}, "recentItems": ["json array"]}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Salesforce11": [{"startTime": 1705409103641, "executionTime": 196, "source": [{"previousNode": "Sleep 0.5 second6"}], "executionStatus": "success", "data": {"main": [[{"json": {"errors": ["json array"], "success": true}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.5 second2": [{"startTime": 1705409103837, "executionTime": 505, "source": [{"previousNode": "Salesforce18"}], "executionStatus": "success", "data": {"main": [[{"json": {"objectDescribe": {"object": true}, "recentItems": ["json array"]}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Salesforce4": [{"startTime": 1705409104343, "executionTime": 145, "source": [{"previousNode": "Sleep 0.5 second3"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "0030900002C8EiUAAV", "FirstName": {"object": true}, "LastName": "Contact1705407559274", "Email": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Salesforce12": [{"startTime": 1705409104489, "executionTime": 315, "source": [{"previousNode": "Salesforce11"}], "executionStatus": "success", "data": {"main": [[{"json": {"errors": ["json array"], "success": true}, "pairedItem": {"item": 0}}]]}}], "Salesforce19": [{"startTime": 1705409104804, "executionTime": 151, "source": [{"previousNode": "Sleep 0.5 second2"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "00Q09000006rtukEAA", "Company": "n8n", "FirstName": {"object": true}, "LastName": "LastName1621428976317", "Street": {"object": true}, "PostalCode": {"object": true}, "City": {"object": true}, "Email": {"object": true}, "Status": "Open - Not Contacted"}, "pairedItem": {"item": 0}}]]}}], "Salesforce5": [{"startTime": 1705409104955, "executionTime": 206, "source": [{"previousNode": "Salesforce4"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "00v0900000ZyfD3AAJ", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Salesforce20": [{"startTime": 1705409105162, "executionTime": 259, "source": [{"previousNode": "Salesforce19"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "00v0900000ZyfD8AAJ", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.5 second5": [{"startTime": 1705409105421, "executionTime": 504, "source": [{"previousNode": "Salesforce5"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "00v0900000ZyfD3AAJ", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Sleep 0.5 second4": [{"startTime": 1705409105925, "executionTime": 504, "source": [{"previousNode": "Salesforce20"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "00v0900000ZyfD8AAJ", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Salesforce6": [{"startTime": 1705409106431, "executionTime": 257, "source": [{"previousNode": "Sleep 0.5 second5"}], "executionStatus": "success", "data": {"main": [[{"json": {"errors": ["json array"], "success": true}, "pairedItem": {"item": 0}}]]}}], "Salesforce21": [{"startTime": 1705409106688, "executionTime": 205, "source": [{"previousNode": "Sleep 0.5 second4"}], "executionStatus": "success", "data": {"main": [[{"json": {"errors": ["json array"], "success": true}, "pairedItem": {"item": 0}}]]}}], "Salesforce7": [{"startTime": 1705409106893, "executionTime": 466, "source": [{"previousNode": "Salesforce6"}], "executionStatus": "success", "data": {"main": [[{"json": {"errors": ["json array"], "success": true}, "pairedItem": {"item": 0}}]]}}], "Salesforce22": [{"startTime": 1705409107359, "executionTime": 509, "source": [{"previousNode": "Salesforce21"}], "executionStatus": "success", "data": {"main": [[{"json": {"errors": ["json array"], "success": true}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Salesforce22"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2024-01-16T12:44:58.604Z", "stoppedAt": "2024-01-16T12:45:07.868Z", "status": "running", "finished": true}