{"data": {"startData": {}, "resultData": {"runData": {"When clicking \"Test workflow\"": [{"startTime": 1747343996141, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Mistral Cloud Chat Model": [{"startTime": 1747343996676, "executionTime": 200, "executionIndex": 2, "executionStatus": "success", "source": [{"previousNode": "Mistral Cloud Chat", "previousNodeRun": 0}], "data": {"ai_languageModel": [[{"json": {"response": {"object": true}, "tokenUsage": {"object": true}}}]]}, "inputOverride": {"ai_languageModel": [[{"json": {"messages": ["Human: How much is 1+1? Only provide the numerical answer without any other text.\n"], "estimatedTokens": 20, "options": {"mistral_api_key": {"lc": 1, "type": "secret", "id": ["MISTRAL_API_KEY"]}, "model_name": "mistral-tiny", "temperature": 0}}}]]}, "metadata": {"subRun": [{"node": "Mistral Cloud Chat Model", "runIndex": 0}]}}], "Mistral Cloud Chat": [{"startTime": 1747343996141, "executionIndex": 1, "source": [{"previousNode": "When clicking \"Test workflow\""}], "hints": [], "executionTime": 735, "executionStatus": "success", "data": {"main": [[{"json": {"text": "2"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Mistral Cloud Chat"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {"Mistral Cloud Chat Model": [{"subRun": [{"node": "Mistral Cloud Chat Model", "runIndex": 0}]}]}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:56.141Z", "stoppedAt": "2025-05-15T21:19:56.876Z", "status": "running", "finished": true}