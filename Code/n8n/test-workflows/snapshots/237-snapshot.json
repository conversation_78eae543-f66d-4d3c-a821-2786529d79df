{"data": {"startData": {}, "resultData": {"runData": {"When clicking \"Test workflow\"": [{"startTime": 1747343996926, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Azure OpenAI Chat Model": [{"startTime": 1747343996936, "executionTime": 776, "executionIndex": 2, "executionStatus": "success", "source": [{"previousNode": "Azure OpenAI Chat", "previousNodeRun": 0}], "data": {"ai_languageModel": [[{"json": {"response": {"object": true}, "tokenUsage": {"object": true}}}]]}, "inputOverride": {"ai_languageModel": [[{"json": {"messages": ["Human: How much is 1+1? Only provide the numerical answer without any other text.\n"], "estimatedTokens": 20, "options": {"azure_endpoint": "https://n8n-ai-us.openai.azure.com/", "temperature": 0, "timeout": 60000, "max_retries": 2, "model_kwargs": {}, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "deployment_name": "gpt4"}}}]]}, "metadata": {"subRun": [{"node": "Azure OpenAI Chat Model", "runIndex": 0}]}}], "Azure OpenAI Chat": [{"startTime": 1747343996926, "executionIndex": 1, "source": [{"previousNode": "When clicking \"Test workflow\""}], "hints": [], "executionTime": 787, "executionStatus": "success", "data": {"main": [[{"json": {"text": "2"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Azure OpenAI Chat"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {"Azure OpenAI Chat Model": [{"subRun": [{"node": "Azure OpenAI Chat Model", "runIndex": 0}]}]}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:56.926Z", "stoppedAt": "2025-05-15T21:19:57.713Z", "status": "running", "finished": true}