{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343994471, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Set": [{"startTime": 1747343994471, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 4, "executionStatus": "success", "data": {"main": [[{"json": {"filename": "filename1747343994475"}, "pairedItem": {"item": 0}}]]}}], "Execute Command": [{"startTime": 1747343994475, "executionIndex": 2, "source": [{"previousNode": "Set"}], "hints": [], "executionTime": 61, "executionStatus": "success", "data": {"main": [[{"json": {"exitCode": 0, "stderr": "", "stdout": ""}, "pairedItem": {"item": 0}}]]}}], "Read Binary File": [{"startTime": 1747343994536, "executionIndex": 3, "source": [{"previousNode": "Execute Command"}], "hints": [], "executionTime": 14, "executionStatus": "success", "data": {"main": [[{"json": {"exitCode": 0, "stderr": "", "stdout": ""}, "binary": {"data": {"mimeType": "text/plain", "fileType": "text", "data": "dGVzdAo=", "directory": "/tmp", "fileName": "filename1747343994475", "fileSize": "5 B"}}, "pairedItem": {"item": 0}}]]}}], "Function": [{"startTime": 1747343994550, "executionIndex": 4, "source": [{"previousNode": "Read Binary File"}], "hints": [], "executionTime": 1, "executionStatus": "success", "data": {"main": [[{"json": {"exitCode": 0, "stderr": "", "stdout": ""}, "binary": {"data": {"mimeType": "text/plain", "fileType": "text", "data": "dGVzdAo=", "directory": "/tmp", "fileName": "filename1747343994475", "fileSize": "5 B"}}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Execute Command1": [{"startTime": 1747343994551, "executionIndex": 5, "source": [{"previousNode": "Function"}], "hints": [], "executionTime": 10, "executionStatus": "success", "data": {"main": [[{"json": {"exitCode": 0, "stderr": "", "stdout": ""}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Execute Command1"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:54.471Z", "stoppedAt": "2025-05-15T21:19:54.561Z", "status": "running", "finished": true}