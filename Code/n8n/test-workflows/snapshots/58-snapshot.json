{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747344007863, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Todoist": [{"startTime": 1747344007863, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 841, "executionStatus": "success", "data": {"main": [[{"json": {"id": "9167050615", "assigner_id": {"object": true}, "assignee_id": {"object": true}, "project_id": "2259097055", "section_id": {"object": true}, "parent_id": {"object": true}, "order": 21, "content": "Test Task", "description": "", "is_completed": false, "labels": ["json array"], "priority": 1, "comment_count": 0, "creator_id": "32694069", "created_at": "2025-05-15T21:20:08.213266Z", "due": {"object": true}, "url": "https://app.todoist.com/app/task/9167050615", "duration": {"object": true}, "deadline": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Todoist1": [{"startTime": 1747344008704, "executionIndex": 2, "source": [{"previousNode": "Todoist"}], "hints": [], "executionTime": 248, "executionStatus": "success", "data": {"main": [[{"json": {"id": "9167050615", "assigner_id": {"object": true}, "assignee_id": {"object": true}, "project_id": "2259097055", "section_id": {"object": true}, "parent_id": {"object": true}, "order": 21, "content": "Test Task", "description": "", "is_completed": false, "labels": ["json array"], "priority": 1, "comment_count": 0, "creator_id": "32694069", "created_at": "2025-05-15T21:20:08.213266Z", "due": {"object": true}, "url": "https://app.todoist.com/app/task/9167050615", "duration": {"object": true}, "deadline": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Todoist2": [{"startTime": 1747344008952, "executionIndex": 3, "source": [{"previousNode": "Todoist1"}], "hints": [], "executionTime": 521, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Todoist3": [{"startTime": 1747344009473, "executionIndex": 4, "source": [{"previousNode": "Todoist2"}], "hints": [], "executionTime": 654, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Todoist5": [{"startTime": 1747344010127, "executionIndex": 5, "source": [{"previousNode": "Todoist3"}], "hints": [], "executionTime": 471, "executionStatus": "success", "data": {"main": [[{"json": {"id": "4610234385", "assigner_id": {"object": true}, "assignee_id": {"object": true}, "project_id": "2259097055", "section_id": {"object": true}, "parent_id": {"object": true}, "order": 1, "content": "Test Task", "description": "", "is_completed": false, "labels": ["json array"], "priority": 1, "comment_count": 0, "creator_id": "32694069", "created_at": "2021-02-25T12:23:24.274629Z", "due": {"object": true}, "url": "https://app.todoist.com/app/task/4610234385", "duration": {"object": true}, "deadline": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Todoist4": [{"startTime": 1747344010598, "executionIndex": 6, "source": [{"previousNode": "Todoist5"}], "hints": [], "executionTime": 487, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Todoist4"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:20:07.863Z", "stoppedAt": "2025-05-15T21:20:11.085Z", "status": "running", "finished": true}