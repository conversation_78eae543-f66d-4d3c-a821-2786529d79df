import { DEFAULT_USER_PASSWORD } from './constants';

export interface UserCredentials {
	email: string;
	password: string;
	firstName: string;
	lastName: string;
}

// Simple name generators
const FIRST_NAMES = [
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON><PERSON>',
	'<PERSON>',
];

const LAST_NAMES = [
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
];

const getRandomName = (names: string[]): string => {
	return names[Math.floor(Math.random() * names.length)];
};

const randFirstName = (): string => getRandomName(FIRST_NAMES);
const randLastName = (): string => getRandomName(LAST_NAMES);

export const INSTANCE_OWNER_CREDENTIALS: UserCredentials = {
	email: '<EMAIL>',
	password: DEFAULT_USER_PASSWORD,
	firstName: randFirstName(),
	lastName: randLastName(),
};

export const INSTANCE_ADMIN_CREDENTIALS: UserCredentials = {
	email: '<EMAIL>',
	password: DEFAULT_USER_PASSWORD,
	firstName: randFirstName(),
	lastName: randLastName(),
};

export const INSTANCE_MEMBER_CREDENTIALS: UserCredentials[] = [
	{
		email: '<EMAIL>',
		password: DEFAULT_USER_PASSWORD,
		firstName: randFirstName(),
		lastName: randLastName(),
	},
];
