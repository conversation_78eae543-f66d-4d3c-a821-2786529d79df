{"name": "n8n-workflow", "version": "1.101.0", "description": "Workflow base code of n8n", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "main": "dist/cjs/index.js", "exports": {".": {"types": "./dist/esm/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "./common": {"types": "./dist/esm/common/index.d.ts", "import": "./dist/esm/common/index.js", "require": "./dist/cjs/common/index.js"}, "./*": "./*"}, "scripts": {"clean": "rimraf dist .turbo", "dev": "pnpm watch", "typecheck": "tsc --noEmit", "build:vite": "vite build", "build": "tsc --build tsconfig.build.esm.json tsconfig.build.cjs.json", "format": "biome format --write .", "format:check": "biome ci .", "lint": "eslint src --quiet", "lintfix": "eslint src --fix", "watch": "tsc --build tsconfig.build.esm.json tsconfig.build.cjs.json --watch", "test": "vitest run", "test:dev": "vitest --watch"}, "files": ["dist/**/*"], "devDependencies": {"@langchain/core": "catalog:", "@n8n/config": "workspace:*", "@n8n/vitest-config": "workspace:*", "@n8n/typescript-config": "workspace:*", "@types/express": "catalog:", "@types/jmespath": "^0.15.0", "@types/lodash": "catalog:", "@types/luxon": "3.2.0", "@types/md5": "^2.3.5", "@types/xml2js": "catalog:", "vitest": "catalog:", "vitest-mock-extended": "catalog:"}, "dependencies": {"@n8n/tournament": "1.0.6", "ast-types": "0.15.2", "callsites": "catalog:", "esprima-next": "5.8.4", "form-data": "catalog:", "jmespath": "0.16.0", "js-base64": "catalog:", "jssha": "3.3.1", "lodash": "catalog:", "luxon": "catalog:", "md5": "2.3.0", "recast": "0.22.0", "title-case": "3.0.3", "transliteration": "2.3.5", "xml2js": "catalog:", "zod": "catalog:"}}