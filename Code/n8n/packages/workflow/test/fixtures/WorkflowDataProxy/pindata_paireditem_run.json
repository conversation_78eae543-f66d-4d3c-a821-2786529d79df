{"data": {"startData": {}, "resultData": {"runData": {"When clicking ‘Execute workflow’": [{"hints": [], "startTime": 1737031584297, "executionTime": 1, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "DebugHelper": [{"hints": [], "startTime": 1737031584299, "executionTime": 1, "source": [{"previousNode": "When clicking ‘Execute workflow’"}], "executionStatus": "success", "data": {"main": [[{"json": {"uid": "3c54ac5d-5c75-409d-9975-76ee151e5fc9", "email": "<EMAIL>", "firstname": "<PERSON>", "lastname": "<PERSON>", "password": "c~837Nv"}, "pairedItem": {"item": 0}}, {"json": {"uid": "5b0d9bd7-2ecf-47fb-b484-3eb1e76fa901", "email": "<PERSON><PERSON>@hotmail.com", "firstname": "<PERSON>", "lastname": "<PERSON><PERSON><PERSON>", "password": "48FF,6dnx"}, "pairedItem": {"item": 0}}, {"json": {"uid": "76437ebe-d406-447a-ab89-3b10f5183480", "email": "<EMAIL>", "firstname": "Alma", "lastname": "<PERSON>n", "password": "6$G2R3nT"}, "pairedItem": {"item": 0}}]]}}], "Edit Fields": [{"hints": [], "startTime": 1737031584301, "executionTime": 0, "source": [{"previousNode": "DebugHelper"}], "executionStatus": "success", "data": {"main": [[{"json": {"uid": "d42c6385-12f2-4486-92b5-eebd2e95d161", "email": "<EMAIL>", "firstname": "<PERSON>", "lastname": "Krajcik", "password": "k%Y2I9oq", "test": "1"}}, {"json": {"uid": "53fc09df-5463-4f48-9fda-6500b1b77c82", "email": "<PERSON><PERSON>@gmail.com", "firstname": "<PERSON>", "lastname": "<PERSON><PERSON>", "password": "t48s3-r", "test": "1"}}]]}}], "Set": [{"hints": [], "startTime": 1737031584301, "executionTime": 0, "source": [{"previousNode": "<PERSON>"}], "data": {"main": [[{"json": {"uid": "d42c6385-12f2-4486-92b5-eebd2e95d161", "email": "<EMAIL>", "firstname": "<PERSON>", "lastname": "Krajcik", "password": "k%Y2I9oq", "test": "1"}, "pairedItem": {"item": 0}}, {"json": {"uid": "53fc09df-5463-4f48-9fda-6500b1b77c82", "email": "<PERSON><PERSON>@gmail.com", "firstname": "<PERSON>", "lastname": "<PERSON><PERSON>", "password": "t48s3-r", "test": "1"}, "pairedItem": {"item": 1}}]]}}]}, "pinData": {"Edit Fields": [{"json": {"uid": "d42c6385-12f2-4486-92b5-eebd2e95d161", "email": "<EMAIL>", "firstname": "<PERSON>", "lastname": "Krajcik", "password": "k%Y2I9oq", "test": "1"}}, {"json": {"uid": "53fc09df-5463-4f48-9fda-6500b1b77c82", "email": "<PERSON><PERSON>@gmail.com", "firstname": "<PERSON>", "lastname": "<PERSON><PERSON>", "password": "t48s3-r", "test": "1"}}]}, "lastNodeExecuted": "Set"}, "executionData": {"contextData": {}, "nodeExecutionStack": [{"node": {"parameters": {"mode": "runOnceForAllItems", "language": "javaScript", "jsCode": "for (let i = 0; i < $input.all().length; i++) {   $(\"DebugHelper\").itemMatching(i) }  return []", "notice": ""}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [720, 0], "id": "d4e8b6a2-cd73-452d-b5f0-986753f5dc4a", "name": "Set"}, "data": {"main": [[{"json": {"uid": "d42c6385-12f2-4486-92b5-eebd2e95d161", "email": "<EMAIL>", "firstname": "<PERSON>", "lastname": "Krajcik", "password": "k%Y2I9oq", "test": "1"}, "pairedItem": {"item": 0}}, {"json": {"uid": "53fc09df-5463-4f48-9fda-6500b1b77c82", "email": "<PERSON><PERSON>@gmail.com", "firstname": "<PERSON>", "lastname": "<PERSON><PERSON>", "password": "t48s3-r", "test": "1"}, "pairedItem": {"item": 1}}]]}, "source": {"main": [{"previousNode": "<PERSON>"}]}}], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}}