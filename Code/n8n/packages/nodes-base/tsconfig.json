{
	"extends": ["@n8n/typescript-config/modern/tsconfig.json"],
	"compilerOptions": {
		"paths": {
			"@credentials/*": ["./credentials/*"],
			"@test/*": ["./test/*"],
			"@utils/*": ["./utils/*"],
			"@nodes-testing/*": ["../core/nodes-testing/*"]
		},
		"tsBuildInfoFile": "dist/typecheck.tsbuildinfo",

		"emitDecoratorMetadata": true,
		"experimentalDecorators": true,

		// TODO: remove all options below this line to align with the modern config
		// TODO: "module" to be removed when we migrate to vitest
		"module": "commonjs",
		// TODO: "moduleResolution" to be removed when we migrate to vitest
		"moduleResolution": "node",
		"noImplicitReturns": false,
		"useUnknownInCatchVariables": false,
		"isolatedModules": false,
		"verbatimModuleSyntax": false,
		"noUncheckedIndexedAccess": false,
		"noImplicitOverride": false,

		"noImplicitAny": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"strictNullChecks": true,
		"preserveConstEnums": true
	},
	"include": [
		"shims.d.ts",
		"credentials/**/*.ts",
		"nodes/**/*.ts",
		"nodes/**/*.json",
		"test/**/*.ts",
		"types/**/*.ts",
		"utils/**/*.ts",
		"types/**/*.ts",
		"../core/nodes-testing/**/*.ts",
		"../../node_modules/jest-expect-message/types/index.d.ts"
	],
	"references": [
		{ "path": "../@n8n/imap/tsconfig.build.json" },
		{ "path": "../workflow/tsconfig.build.esm.json" },
		{ "path": "../core/tsconfig.build.json" }
	]
}
