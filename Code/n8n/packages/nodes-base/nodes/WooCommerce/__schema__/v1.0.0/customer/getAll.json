{"type": "object", "properties": {"_links": {"type": "object", "properties": {"collection": {"type": "array", "items": {"type": "object", "properties": {"href": {"type": "string"}}}}, "self": {"type": "array", "items": {"type": "object", "properties": {"href": {"type": "string"}}}}}}, "avatar_url": {"type": "string"}, "billing": {"type": "object", "properties": {"address_1": {"type": "string"}, "address_2": {"type": "string"}, "city": {"type": "string"}, "company": {"type": "string"}, "country": {"type": "string"}, "email": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "phone": {"type": "string"}, "postcode": {"type": "string"}, "state": {"type": "string"}}}, "date_created": {"type": "string"}, "date_created_gmt": {"type": "string"}, "email": {"type": "string"}, "first_name": {"type": "string"}, "id": {"type": "integer"}, "is_paying_customer": {"type": "boolean"}, "last_name": {"type": "string"}, "meta_data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "key": {"type": "string"}}}}, "role": {"type": "string"}, "shipping": {"type": "object", "properties": {"address_1": {"type": "string"}, "address_2": {"type": "string"}, "city": {"type": "string"}, "company": {"type": "string"}, "country": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "phone": {"type": "string"}, "postcode": {"type": "string"}, "state": {"type": "string"}}}, "username": {"type": "string"}}, "version": 1}