{"node": "n8n-nodes-base.uproc", "nodeVersion": "1.0", "codexVersion": "1.0", "categories": ["Data & Storage"], "resources": {"credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/uProc/"}], "primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.uproc/"}], "generic": [{"label": "How uProc scraped a multi-page website with a low-code workflow", "icon": " 🕸️", "url": "https://n8n.io/blog/how-uproc-scraped-a-multi-page-website-with-a-low-code-workflow/"}, {"label": "7 no-code workflow automations for Amazon Web Services", "url": "https://n8n.io/blog/aws-workflow-automation/"}]}}