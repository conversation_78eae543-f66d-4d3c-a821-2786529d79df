{"node": "n8n-nodes-base.twilio", "nodeVersion": "1.0", "codexVersion": "1.0", "categories": ["Communication", "Development"], "resources": {"credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/twilio/"}], "primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.twilio/"}], "generic": [{"label": "Love at first sight: <PERSON>’s n8n journey", "icon": "❤️", "url": "https://n8n.io/blog/love-at-first-sight-ricardos-n8n-journey/"}, {"label": "Database Monitoring and Alerting with n8n", "icon": "📡", "url": "https://n8n.io/blog/database-monitoring-and-alerting-with-n8n/"}, {"label": "Automatically Adding Expense Receipts to Google Sheets with Telegram, Mindee, Twilio, and n8n", "icon": "🧾", "url": "https://n8n.io/blog/automatically-adding-expense-receipts-to-google-sheets-with-telegram-mindee-twilio-and-n8n/"}, {"label": "Tracking Time Spent in Meetings With Google Calendar, Twilio, and n8n", "icon": "🗓", "url": "https://n8n.io/blog/tracking-time-spent-in-meetings-with-google-calendar-twilio-and-n8n/"}, {"label": "Creating Error Workflows in n8n", "icon": "🌪", "url": "https://n8n.io/blog/creating-error-workflows-in-n8n/"}, {"label": "Sending Automated Congratulations with Google Sheets, Twilio, and n8n ", "icon": "🙌", "url": "https://n8n.io/blog/sending-automated-congratulations-with-google-sheets-twilio-and-n8n/"}, {"label": "Learn to Build Powerful API Endpoints Using Webhooks", "icon": "🧰", "url": "https://n8n.io/blog/learn-to-build-powerful-api-endpoints-using-webhooks/"}, {"label": "Sending SMS the Low-Code Way with Airtable, Twilio Programmable SMS, and n8n", "icon": "📱", "url": "https://n8n.io/blog/sending-sms-the-low-code-way-with-airtable-twilio-programmable-sms-and-n8n/"}]}, "alias": ["SMS", "Phone", "Voice"]}