{"node": "n8n-nodes-base.totp", "nodeVersion": "1.0", "codexVersion": "1.0", "categories": ["Core <PERSON>"], "subcategories": ["Helpers"], "details": "Generate a time-based one-time password", "alias": ["2FA", "MFA", "authentication", "Security", "OTP", "password", "multi", "factor"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.totp/"}]}}