{"node": "n8n-nodes-base.splitOut", "nodeVersion": "1.0", "codexVersion": "1.0", "details": "", "categories": ["Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.splitout/"}], "generic": []}, "alias": ["Split", "Nested", "Transform", "Array", "List", "<PERSON><PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}}