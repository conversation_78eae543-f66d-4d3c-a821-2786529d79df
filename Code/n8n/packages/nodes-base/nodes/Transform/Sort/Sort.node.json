{"node": "n8n-nodes-base.sort", "nodeVersion": "1.0", "codexVersion": "1.0", "details": "", "categories": ["Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.sort/"}], "generic": []}, "alias": ["Sort", "Order", "Transform", "Array", "List", "<PERSON><PERSON>", "Random"], "subcategories": {"Core Nodes": ["Data Transformation"]}}