{"type": "object", "properties": {"$approval": {"type": "object", "properties": {"approve": {"type": "boolean"}, "delegate": {"type": "boolean"}, "reject": {"type": "boolean"}, "resubmit": {"type": "boolean"}, "takeover": {"type": "boolean"}}}, "$approval_state": {"type": "string"}, "$approved": {"type": "boolean"}, "$currency_symbol": {"type": "string"}, "$editable": {"type": "boolean"}, "$field_states": {"type": "null"}, "$in_merge": {"type": "boolean"}, "$is_duplicate": {"type": "boolean"}, "$layout_id": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "$locked_for_me": {"type": "boolean"}, "$process_flow": {"type": "boolean"}, "$review": {"type": "null"}, "$review_process": {"type": "object", "properties": {"approve": {"type": "boolean"}, "reject": {"type": "boolean"}, "resubmit": {"type": "boolean"}}}, "$state": {"type": "string"}, "Account_Name": {"type": "string"}, "Account_Number": {"type": "string"}, "Created_By": {"type": "object", "properties": {"email": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}}}, "Created_Time": {"type": "string"}, "id": {"type": "string"}, "Locked__s": {"type": "boolean"}, "Modified_By": {"type": "object", "properties": {"email": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}}}, "Modified_Time": {"type": "string"}, "Owner": {"type": "object", "properties": {"email": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}}}, "Parent_Account": {"type": "null"}}, "version": 1}