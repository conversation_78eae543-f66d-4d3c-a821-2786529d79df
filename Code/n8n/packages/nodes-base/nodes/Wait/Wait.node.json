{"node": "n8n-nodes-base.wait", "nodeVersion": "1.0", "codexVersion": "1.0", "categories": ["Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.wait/"}], "generic": [{"label": "How to get started with CRM automation (with 3 no-code workflow ideas", "icon": "👥", "url": "https://n8n.io/blog/how-to-get-started-with-crm-automation-and-no-code-workflow-ideas/"}, {"label": "7 no-code workflow automations for Amazon Web Services", "url": "https://n8n.io/blog/aws-workflow-automation/"}]}, "alias": ["pause", "sleep", "delay", "timeout"], "subcategories": {"Core Nodes": ["Helpers", "Flow"]}}