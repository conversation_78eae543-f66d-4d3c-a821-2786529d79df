{"extends": ["./tsconfig.json", "@n8n/typescript-config/modern/tsconfig.cjs.json"], "compilerOptions": {"outDir": "dist", "module": "commonjs", "tsBuildInfoFile": "dist/typecheck.tsbuildinfo"}, "include": ["credentials/**/*.ts", "credentials/translations/**/*.json", "nodes/**/*.ts", "nodes/**/*.json", "types/**/*.ts", "utils/**/*.ts"], "exclude": ["node_modules", "nodes/**/*.test.ts", "credentials/**/*.test.ts", "utils/**/*.test.ts", "test/**", "../core/nodes-testing"]}