# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Building the Project
```bash
./build.sh                    # Build all packages in Debug mode
./build.sh --release          # Build in Release mode  
./build.sh package_name       # Build specific package
./build.sh --clean            # Clean and rebuild
./build.sh --no-clangd        # Build without generating compile_commands.json
```

### Running the System
```bash
./run_network_manager.sh      # Start network manager (auto-detects architecture)
```

### Testing
```bash
./tests/run-all-tests.sh      # Run complete test suite
./tests/debug-config-test.sh  # Validate debug configuration
./tests/config-validation-test.sh  # Validate network configurations
```

### Architecture Management
```bash
./scripts/get_arch.sh         # Get current architecture
./scripts/update_launch_config.sh  # Update VSCode debug configs for current arch
```

## Architecture and Code Structure

### Multi-Architecture Support
- Architecture-aware build system supporting x86_64, aarch64, arm64
- Build outputs go to `install/{arch}/Debug/` or `install/{arch}/Release/`
- VSCode debug configurations automatically adapt to current architecture

### Core Components

#### Network Manager (NetworkManager class)
- Main orchestrator in `src/gen3_network_manager_core/src/network_manager.cpp:36`
- Manages network switching, quality monitoring, NAT rules
- Handles ROS2 services: SwitchNetwork, RefreshNetwork
- Publishes network status and quality metrics

#### Specialized Managers
- **CellularManager**: 5G/cellular network management 
- **WiFiManager**: WiFi network operations
- **DNSManager**: DNS configuration and optimization
- **BindingManager**: Network binding operations
- **NetworkMonitor**: Network quality monitoring
- **NetworkSwitch**: Intelligent network switching logic

#### Message Interface System
Located in `src/gen3_network_interfaces/`:
- **Messages**: NetworkStatus, NetworkQuality, BindingStatus, WiFiNetwork
- **Services**: SwitchNetwork, ConnectWiFi, GetNetworkList, StartBinding, GetFivegInfo
- **Actions**: NetworkBinding

### Configuration System
- YAML-based configuration in `src/gen3_network_manager_core/config/`
- Environment-specific configs: `development_config.yaml`, `production_config.yaml`
- Parameters include timing intervals, network preferences, NAT settings

### Key Features
- **Intelligent Auto-switching**: Scientific scoring system for network quality assessment
- **NAT Rule Management**: Dynamic iptables rule configuration for network changes
- **Multi-interface Support**: WiFi, Ethernet, 5G with priority management
- **Quality Monitoring**: Real-time network performance tracking with DNS resolution timing
- **Deadlock Prevention**: Async processing to prevent blocking operations

## Development Workflow

1. **Code Modifications**: Edit source files
2. **Build**: `./build.sh` (includes ROS2 environment auto-setup)
3. **Test**: `./tests/run-tests.sh` for targeted tests
4. **Integration Test**: `./tests/run-all-tests.sh` for full validation
5. **Debug**: Use VSCode F5 with auto-architecture detection

## Important Implementation Notes

### Network Switching Logic
- Switch decisions use multi-factor scoring including signal strength, connectivity, and stability
- Implements frequency limiting to prevent rapid switching
- Supports rollback on failed switches

### Threading and Async Operations  
- Uses async quality processing to prevent UI blocking
- Mutex protection for shared state (status_mutex_, fiveg_quality_mutex_)
- Future-based async operations for network quality checks

### NAT and Routing Management
- Dynamic iptables rule updates on network changes
- Route priority management for multi-interface scenarios
- Static route preservation during network switches

### Build System Features
- Automatic ROS2 environment detection and setup
- clangd integration with compile_commands.json generation
- VSCode debug configuration auto-updates for current architecture
- Parallel build support with configurable job count

## Testing and Validation

The project includes comprehensive testing:
- **Unit Tests**: C++ test files for core functionality
- **Integration Tests**: Shell scripts for end-to-end validation  
- **Config Tests**: Validation of YAML configurations and debug setups
- **Performance Tests**: Latency measurement and network quality validation

## Dependencies and Requirements
- **ROS2**: Humble (auto-detected during build)
- **Build System**: colcon with ament_cmake
- **C++ Standard**: C++17
- **Key ROS2 Packages**: rclcpp, rclcpp_action, std_msgs, builtin_interfaces