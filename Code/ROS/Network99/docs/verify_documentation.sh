#!/bin/bash

# 文档整合验证脚本
# 验证所有调试相关文档是否完整和一致

echo "=== 文档整合验证 ==="
echo

# 检查主要文档文件
echo "📚 检查主要文档文件:"
MAIN_DOCS=(
    "README.md"
    "DEBUG_CONFIG_SUMMARY.md"
    "docs/README.md"
    "docs/调试配置指南.md"
    "docs/架构配置说明.md"
    "docs/Markdown配置说明.md"
    "docs/launch配置备份管理.md"
)

for doc in "${MAIN_DOCS[@]}"; do
    if [ -f "$doc" ]; then
        echo "✅ $doc"
    else
        echo "❌ $doc (缺失)"
    fi
done

echo
echo "📁 检查分类文档目录:"
CATEGORY_DIRS=(
    "docs/features"
    "docs/troubleshooting"
    "docs/api"
    "docs/requirements"
    "docs/development"
    "tests"
)

for dir in "${CATEGORY_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        file_count=$(find "$dir" -name "*.md" -type f | wc -l)
        echo "✅ $dir ($file_count 个文件)"
    else
        echo "❌ $dir (目录不存在)"
    fi
done

echo
echo "📋 检查分类文档文件:"
CATEGORY_DOCS=(
    "docs/features/自动切换优化.md"
    "docs/features/配置参数使用说明.md"
    "docs/features/死锁预防机制.md"
    "docs/troubleshooting/调试日志说明.md"
    "docs/troubleshooting/日志级别优化.md"
    "docs/api/ROS接口文档.md"
    "docs/requirements/网络需求分析.md"
    "docs/development/Claude开发笔记.md"
)

for doc in "${CATEGORY_DOCS[@]}"; do
    if [ -f "$doc" ]; then
        echo "✅ $doc"
    else
        echo "❌ $doc (缺失)"
    fi
done

echo
echo "🔧 检查配置文件:"
CONFIG_FILES=(
    ".vscode/launch.json"
    ".vscode/tasks.json"
)

for config in "${CONFIG_FILES[@]}"; do
    if [ -f "$config" ]; then
        echo "✅ $config"
    else
        echo "❌ $config (缺失)"
    fi
done

echo
echo "🛠️ 检查工具脚本:"
SCRIPTS=(
    "build.sh"
    "run_network_manager.sh"
    "scripts/get_arch.sh"
    "scripts/update_launch_config.sh"
    "scripts/backup_launch_json.sh"
    "scripts/restore_launch_json.sh"
    "scripts/manage_launch_backups.sh"
    "tests/run-all-tests.sh"
    "tests/config-validation-test.sh"
    "tests/debug-config-test.sh"
)

for script in "${SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        if [ -x "$script" ]; then
            echo "✅ $script (可执行)"
        else
            echo "⚠️ $script (不可执行)"
        fi
    else
        echo "❌ $script (缺失)"
    fi
done

echo
echo "🔍 检查文档内容一致性:"

# 检查 launch.json 中的配置数量
if [ -f ".vscode/launch.json" ]; then
    CONFIG_COUNT=$(grep -c '"name":.*Debug' .vscode/launch.json)
    echo "✅ launch.json 包含 $CONFIG_COUNT 个调试配置"
else
    echo "❌ launch.json 文件不存在"
fi

# 检查是否包含 ROS 库路径
if [ -f ".vscode/launch.json" ]; then
    if grep -q "/opt/ros/humble/lib" .vscode/launch.json; then
        echo "✅ launch.json 包含 ROS 库路径"
    else
        echo "❌ launch.json 缺少 ROS 库路径"
    fi
fi

# 检查架构支持
CURRENT_ARCH=$(uname -m)
if [ -f ".vscode/launch.json" ]; then
    if grep -q "install/$CURRENT_ARCH" .vscode/launch.json; then
        echo "✅ launch.json 支持当前架构 ($CURRENT_ARCH)"
    else
        echo "⚠️ launch.json 可能需要更新架构路径"
    fi
fi

echo
echo "📖 文档链接检查:"

# 检查主 README 中的链接
if [ -f "README.md" ]; then
    echo "检查 README.md 中的文档链接:"

    # 检查引用的文档是否存在
    REFERENCED_DOCS=(
        "docs/README.md"
        "docs/调试配置指南.md"
        "docs/架构配置说明.md"
        "DEBUG_CONFIG_SUMMARY.md"
        "docs/features/"
        "docs/troubleshooting/"
        "docs/api/"
        "docs/requirements/"
        "docs/development/"
    )

    for ref_doc in "${REFERENCED_DOCS[@]}"; do
        if grep -q "$ref_doc" README.md; then
            if [[ "$ref_doc" == */ ]]; then
                # 检查目录
                if [ -d "$ref_doc" ]; then
                    echo "  ✅ $ref_doc (目录链接有效)"
                else
                    echo "  ❌ $ref_doc (目录链接无效)"
                fi
            else
                # 检查文件
                if [ -f "$ref_doc" ]; then
                    echo "  ✅ $ref_doc (文件链接有效)"
                else
                    echo "  ❌ $ref_doc (文件链接无效)"
                fi
            fi
        fi
    done
fi

# 检查分类目录的 README 文件
echo
echo "检查分类目录 README 文件:"
for dir in "${CATEGORY_DIRS[@]}"; do
    readme_file="$dir/README.md"
    if [ -f "$readme_file" ]; then
        echo "  ✅ $readme_file"
    else
        echo "  ❌ $readme_file (缺失)"
    fi
done

echo
echo "🧪 运行配置测试:"
if [ -f "tests/debug-config-test.sh" ] && [ -x "tests/debug-config-test.sh" ]; then
    echo "运行 tests/debug-config-test.sh..."
    if ./tests/debug-config-test.sh > /dev/null 2>&1; then
        echo "✅ 调试配置测试通过"
    else
        echo "❌ 调试配置测试失败"
    fi
else
    echo "⚠️ 无法运行配置测试"
fi

echo
echo "🧪 测试套件验证:"
if [ -f "tests/run-tests.sh" ] && [ -x "tests/run-tests.sh" ]; then
    echo "运行 tests/run-tests.sh..."
    if ./test-organization-summary.sh > /dev/null 2>&1; then
        echo "✅ 测试脚本整理验证通过"
    else
        echo "❌ 测试脚本整理验证失败"
    fi
else
    echo "⚠️ 无法运行测试套件验证"
fi

echo
echo "� 备份文件检查:"
if [ -d ".vscode" ]; then
    BACKUP_COUNT=$(find .vscode -name "launch.json.backup.*" -type f | wc -l)
    echo "launch.json 备份文件数量: $BACKUP_COUNT"

    if [ $BACKUP_COUNT -le 3 ]; then
        echo "✅ 备份文件数量正常 (≤3)"
    else
        echo "⚠️ 备份文件过多，建议运行 ./scripts/manage_launch_backups.sh 清理"
    fi

    if [ -f "scripts/backup_launch_json.sh" ] && [ -x "scripts/backup_launch_json.sh" ]; then
        echo "✅ 备份管理脚本可用"
    else
        echo "❌ 备份管理脚本不可用"
    fi
else
    echo "⚠️ .vscode 目录不存在"
fi

echo
echo "�📊 文档统计:"
echo "- 主要文档: ${#MAIN_DOCS[@]} 个"
echo "- 分类目录: ${#CATEGORY_DIRS[@]} 个"
echo "- 分类文档: ${#CATEGORY_DOCS[@]} 个"
echo "- 配置文件: ${#CONFIG_FILES[@]} 个"
echo "- 工具脚本: ${#SCRIPTS[@]} 个"
echo "- 调试配置: $CONFIG_COUNT 个"

echo
echo "=== 验证完成 ==="
echo
echo "📋 文档结构:"
echo "├── README.md                     # 项目主文档"
echo "├── DEBUG_CONFIG_SUMMARY.md       # 配置更新总结"
echo "├── docs/"
echo "│   ├── README.md                 # 文档索引"
echo "│   ├── 调试配置指南.md           # 完整调试指南"
echo "│   ├── 架构配置说明.md           # 架构配置说明"
echo "│   ├── features/                 # 功能特性文档"
echo "│   │   ├── README.md"
echo "│   │   ├── 自动切换优化.md"
echo "│   │   ├── 配置参数使用说明.md"
echo "│   │   └── 死锁预防机制.md"
echo "│   ├── troubleshooting/          # 故障排除文档"
echo "│   │   ├── README.md"
echo "│   │   ├── 调试日志说明.md"
echo "│   │   └── 日志级别优化.md"
echo "│   ├── api/                      # API 接口文档"
echo "│   │   ├── README.md"
echo "│   │   └── ROS接口文档.md"
echo "│   ├── requirements/             # 需求分析文档"
echo "│   │   ├── README.md"
echo "│   │   └── 网络需求分析.md"
echo "│   └── development/              # 开发文档"
echo "│       ├── README.md"
echo "│       └── Claude开发笔记.md"
echo "├── .vscode/"
echo "│   ├── launch.json              # VSCode 调试配置"
echo "│   └── tasks.json               # VSCode 任务配置"
echo "└── scripts/                     # 工具脚本"
echo
echo "🚀 使用建议:"
echo "1. 新用户: 先阅读 README.md 和 docs/README.md"
echo "2. 功能了解: 查看 docs/features/ 目录"
echo "3. 调试问题: 查看 docs/troubleshooting/ 目录"
echo "4. API 开发: 查看 docs/api/ 目录"
echo "5. 需求分析: 查看 docs/requirements/ 目录"
echo "6. 开发环境: 查看 docs/development/ 目录"
echo "7. 快速参考: 查看 DEBUG_CONFIG_SUMMARY.md"
