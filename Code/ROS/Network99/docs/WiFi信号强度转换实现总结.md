# WiFi 信号强度转换实现总结

## 🎯 实现目标

将 `nmcli` 命令获取的 WiFi 信号百分比转换为标准的 dBm 单位，提供更准确和专业的信号强度表示。

## 📊 转换公式

### 数学公式
```
dBm = -100 + (percentage × 0.7)
```

### 转换范围
- **输入范围**: 0% - 100%
- **输出范围**: -100 dBm 到 -30 dBm
- **边界处理**: 超出范围的值会被限制在有效范围内

### 关键转换点
| 百分比 | dBm 值 | 信号质量 |
|--------|--------|----------|
| 100% | -30 dBm | 优秀 |
| 75% | -48 dBm | 良好 |
| 50% | -65 dBm | 一般 |
| 25% | -83 dBm | 较弱 |
| 0% | -100 dBm | 很弱 |

## 🔧 代码实现

### 1. 头文件声明 (network_utils.hpp)
```cpp
// 信号强度转换函数
static int32_t convert_signal_percentage_to_dbm(int percentage);
```

### 2. 转换函数实现 (network_utils.cpp)
```cpp
int32_t NetworkUtils::convert_signal_percentage_to_dbm(int percentage) {
  // 限制百分比范围在 0-100 之间
  if (percentage < 0) percentage = 0;
  if (percentage > 100) percentage = 100;
  
  // WiFi 信号强度转换公式
  // 基于经验值：100% ≈ -30dBm, 0% ≈ -100dBm
  // 使用线性插值：dBm = -100 + (percentage * 0.7)
  int32_t dbm = -100 + static_cast<int32_t>(percentage * 0.7);
  
  // 确保结果在合理范围内 (-100 到 -30 dBm)
  if (dbm > -30) dbm = -30;
  if (dbm < -100) dbm = -100;
  
  return dbm;
}
```

### 3. 应用到 WiFi 信息获取
**修改位置**: `get_wifi_info()` 函数
```cpp
// 解析信号强度 - nmcli返回的是百分比，需要转换为dBm
try {
  int percentage = std::stoi(match[3].str());
  info.signal_strength = convert_signal_percentage_to_dbm(percentage);
  RCLCPP_DEBUG(logger, "[UTILS] 信号强度转换: %d%% -> %ddBm", percentage, info.signal_strength);
} catch (...) {
  info.signal_strength = -100;
}
```

### 4. 应用到 WiFi 网络扫描
**修改位置**: `scan_wifi_networks()` 函数
```cpp
// 解析信号强度 - nmcli返回的是百分比，需要转换为dBm
try {
  int percentage = std::stoi(match[3].str());
  network.signal_strength = convert_signal_percentage_to_dbm(percentage);
  RCLCPP_DEBUG(logger, "[UTILS] 信号强度转换: %d%% -> %ddBm", percentage, network.signal_strength);
} catch (...) {
  network.signal_strength = -100;
}
```

## 📋 修改记录

### 文件修改列表
| 文件 | 修改内容 | 行数 |
|------|----------|------|
| `network_utils.hpp` | 添加转换函数声明 | +2 |
| `network_utils.hpp` | 更新 WiFiInfo 结构体注释 | 修改 |
| `network_utils.cpp` | 实现转换函数 | +18 |
| `network_utils.cpp` | 修改 get_wifi_info() 中的信号处理 | 修改 |
| `network_utils.cpp` | 修改 scan_wifi_networks() 中的信号处理 | 修改 |

### 具体修改位置
1. **第 111 行** (network_utils.hpp): 添加转换函数声明
2. **第 48 行** (network_utils.hpp): 添加信号强度注释
3. **第 745-762 行** (network_utils.cpp): 实现转换函数
4. **第 294-298 行** (network_utils.cpp): 修改 get_wifi_info() 信号处理
5. **第 524-528 行** (network_utils.cpp): 修改 scan_wifi_networks() 信号处理

## 🧪 测试验证

### 测试脚本
创建了专门的测试脚本 `tests/signal-strength-conversion-test.sh`：
- ✅ 验证转换公式的正确性
- ✅ 测试边界值处理
- ✅ 显示实际 WiFi 网络的转换结果
- ✅ 提供信号质量评估

### 测试结果示例
```
当前 WiFi 信号强度:
SSID: K3_5g, 原始: 52%, 转换: -64 dBm

转换验证:
  0% -> -100 dBm (很弱)
  25% -> -83 dBm (较弱)
  50% -> -65 dBm (一般)
  75% -> -48 dBm (良好)
  100% -> -30 dBm (优秀)
```

## 📈 改进效果

### 1. 数据准确性
- **标准化单位**: 使用行业标准的 dBm 单位
- **精确表示**: 提供更精确的信号强度数值
- **专业性**: 符合网络工程师的使用习惯

### 2. 用户体验
- **直观理解**: dBm 值能更好地反映信号质量
- **比较性**: 便于不同网络间的信号强度比较
- **调试友好**: 便于网络问题的诊断和调试

### 3. 系统兼容性
- **向后兼容**: 保持原有的数据结构不变
- **透明转换**: 对上层应用透明
- **日志增强**: 添加了详细的转换日志

## 🔍 技术细节

### dBm 单位说明
- **dBm**: 相对于 1 毫瓦的分贝值
- **对数单位**: 每 3dBm 的差异表示功率翻倍或减半
- **负值**: WiFi 信号强度通常为负值，越接近 0 越强

### 信号质量评级
```cpp
if (dbm >= -50)      // 优秀 - 信号很强
else if (dbm >= -60) // 良好 - 信号较强  
else if (dbm >= -70) // 一般 - 信号中等
else if (dbm >= -80) // 较弱 - 可能影响性能
else                 // 很弱 - 连接不稳定
```

### 转换公式选择
- **线性插值**: 简单可靠的转换方法
- **经验值**: 基于实际 WiFi 设备的信号范围
- **边界保护**: 确保输出值在合理范围内

## 💡 使用建议

### 开发者
1. **监控信号**: 在网络切换时监控信号强度变化
2. **阈值设置**: 根据 dBm 值设置网络切换阈值
3. **质量评估**: 使用 dBm 值进行网络质量评估

### 运维人员
1. **信号诊断**: 使用 dBm 值诊断网络问题
2. **位置优化**: 根据信号强度优化设备位置
3. **性能调优**: 监控信号强度变化趋势

### 最佳实践
1. **阈值建议**: -70dBm 以上通常能提供良好连接
2. **频段差异**: 2.4GHz 和 5GHz 信号传播特性不同
3. **环境因素**: 考虑障碍物对信号强度的影响

## 🔗 相关文档

- [网络工具文档](../docs/api/ROS接口文档.md)
- [WiFi 管理功能](../docs/features/自动切换优化.md)
- [故障排除指南](../docs/troubleshooting/调试日志说明.md)

---

**WiFi 信号强度转换功能已完成！** 现在系统能够提供标准的 dBm 信号强度值，提升了数据的专业性和准确性。🎊
