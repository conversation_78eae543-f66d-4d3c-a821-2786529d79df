# 故障排除文档

本目录包含 ROS2 网络管理器的故障诊断和问题解决文档。

## 📋 文档列表

### [调试日志说明.md](调试日志说明.md)
**调试日志说明**
- ROS2 DEBUG 日志来源分析
- 日志级别控制方法
- 常见日志问题解释
- 适用场景：调试人员、开发者

### [日志级别优化.md](日志级别优化.md)
**日志级别优化**
- 日志级别优化策略
- 性能影响分析
- 生产环境配置建议
- 适用场景：运维人员、系统管理员

## 🔧 快速故障排除

### 常见问题
1. **共享库加载错误**: 参考 [调试配置文档](../DEBUG_CONFIGURATION.md#故障排除)
2. **日志过多**: 查看 [日志级别优化](日志级别优化.md)
3. **DEBUG 日志混乱**: 阅读 [调试日志说明](调试日志说明.md)

### 诊断工具
```bash
# 验证配置
./test_debug_config.sh

# 检查日志级别
ros2 param get /gen3_network_manager log_level

# 监控系统状态
ros2 topic echo /network_status --once
```

## 🔗 相关文档

- [调试配置](../DEBUG_CONFIGURATION.md) - 完整调试环境设置
- [功能特性](../features/) - 系统功能说明
- [API 接口](../api/) - ROS2 接口文档

## 📝 使用建议

1. **遇到问题**: 先查看对应的故障排除文档
2. **日志分析**: 参考日志说明文档理解输出内容
3. **性能优化**: 根据日志级别优化文档调整配置

---

**返回**: [文档索引](../README.md) | [项目主页](../../README.md)
