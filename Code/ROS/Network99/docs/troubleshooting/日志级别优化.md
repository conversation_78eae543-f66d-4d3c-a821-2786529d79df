# 日志级别优化说明

## 优化目标

将部分频繁输出或过于详细的 `RCLCPP_INFO` 日志改为 `RCLCPP_DEBUG`，以减少正常运行时的日志噪音，同时保留重要的状态信息。

## 优化原则

### 保留为 INFO 级别的日志：
- **初始化和销毁**：组件的创建、初始化完成、销毁等重要生命周期事件
- **错误和警告**：所有错误和警告信息
- **重要状态变更**：网络切换、连接状态变化等关键事件
- **服务调用**：ROS服务的调用和响应
- **配置加载**：参数和配置文件的加载结果

### 改为 DEBUG 级别的日志：
- **频繁的状态更新**：定时器触发的状态检查和发布
- **详细的网络信息**：接口详情、DNS服务器列表等
- **工具函数调用**：网络扫描、延迟测试等工具函数的开始/结束
- **数据接收**：订阅者收到消息的详细内容
- **内部处理过程**：算法的中间步骤和计算结果

## 已优化的文件和内容

### 1. NetworkManager (network_manager.cpp)

#### 改为 DEBUG 的日志：
- **网络接口检测**：`[STATUS] 检测到 X 个网络接口` 和接口详情
- **网络质量信息**：`[SUB] 收到网络质量信息` 的详细内容
- **状态发布**：`[PUB] 发布网络状态` 的详细信息
- **WiFi详情**：WiFi SSID、信号强度、安全类型等
- **DNS服务器**：DNS服务器列表信息

#### 保留为 INFO 的日志：
- 初始化和销毁消息
- 服务创建和配置加载
- 网络切换操作
- 错误和警告信息

### 2. NetworkUtils (network_utils.cpp)

#### 改为 DEBUG 的日志：
- **WiFi信息获取**：`[UTILS] 开始获取当前WiFi信息`
- **nmcli调用**：`[UTILS] 尝试使用nmcli获取WiFi信息`
- **网络扫描**：`[UTILS] 开始扫描WiFi网络`
- **延迟测试**：`[UTILS] 测试网络延迟` 的开始信息

#### 保留为 INFO 的日志：
- 重要的配置加载结果
- 网络操作的成功/失败状态
- 错误和异常信息

### 3. NetworkMonitor (network_monitor.cpp)

#### 改为 DEBUG 的日志：
- **状态发布**：`[PUB] 发布连接状态` 和 `[PUB] 发布板间通信状态`
- **状态广播**：`[PUB] 广播网络状态` 的详细信息

#### 保留为 INFO 的日志：
- 监控器的初始化和销毁
- 重要的连接状态变化
- 错误和异常情况

### 4. DNSManager (dns_manager.cpp)

#### 改为 DEBUG 的日志：
- **DNS服务器详情**：每个DNS服务器的具体地址
- **首次检查**：`[TIMER] 立即执行首次DNS检查`
- **配置检查**：`检查DNS配置` 的开始提示
- **服务器获取**：`获取当前DNS服务器` 的开始提示
- **服务器数量**：`获取到X个DNS服务器` 的统计信息

#### 保留为 INFO 的日志：
- DNS管理器的初始化和销毁
- DNS服务器设置成功/失败
- 重要的配置变更操作
- 错误和警告信息

### 5. WiFiManager (wifi_manager.cpp)

#### 改为 DEBUG 的日志：
- **初始化检查**：`[INIT] 立即获取当前WiFi连接状态`
- **首次扫描**：`[TIMER] 立即执行首次WiFi扫描`
- **后台扫描**：`[ASYNC] 开始后台WiFi扫描` 和扫描完成信息
- **缓存更新**：`[CACHE] 网络列表缓存更新完成` 的详细信息

#### 保留为 INFO 的日志：
- WiFi管理器的初始化和销毁
- 服务创建和配置
- 当前WiFi连接状态（重要信息）
- WiFi连接成功/失败
- 网络配置的保存和加载

### 6. BindingManager (binding_manager.cpp)

#### 改为 DEBUG 的日志：
- **首次发布**：`[TIMER] 立即执行首次绑定状态发布`
- **流程开始**：`开始执行绑定流程` 的开始提示

#### 保留为 INFO 的日志：
- 绑定管理器的初始化和销毁
- 绑定请求的接收和处理
- 绑定流程的重要状态变化
- 绑定成功/失败的结果

## 使用建议

### 开发环境
```yaml
# development_config.yaml
log_level: "debug"  # 查看所有详细信息，便于调试
```

### 测试环境
```yaml
# network_config.yaml  
log_level: "info"   # 查看重要信息，减少日志噪音
```

### 生产环境
```yaml
# production_config.yaml
log_level: "warn"   # 只显示警告和错误，最小化日志输出
```

## 日志级别对比

### 优化前（全部使用 INFO）
```
[INFO] [timestamp] [gen3_network_manager]: [STATUS] 检测到 3 个网络接口
[INFO] [timestamp] [gen3_network_manager]: [STATUS] 接口: wlan0, 状态: UP, IP: ***********00
[INFO] [timestamp] [gen3_network_manager]: [STATUS] 接口: eth0, 状态: DOWN, IP: 
[INFO] [timestamp] [gen3_network_manager]: [SUB] 收到网络质量信息 - 接口: wlan0, 类型: 1, 信号强度: -45dBm, 延迟: 12.5ms, 综合评分: 85.2
[INFO] [timestamp] [gen3_network_manager]: [PUB] 发布网络状态 - 类型: WiFi, 接口: wlan0, 状态: 已连接, IP: ***********00, 网关: ***********
[INFO] [timestamp] [gen3_network_manager]: [PUB] WiFi详情 - SSID: MyNetwork, 信号强度: -45dBm, 安全类型: WPA2
[INFO] [timestamp] [gen3_network_manager]: [PUB] DNS服务器: *******, *******
```

### 优化后（INFO级别）
```
[INFO] [timestamp] [gen3_network_manager]: 初始化网络管理器
[INFO] [timestamp] [gen3_network_manager]: [PUB] 创建网络状态发布者: network_status
[INFO] [timestamp] [gen3_network_manager]: [TIMER] 创建状态更新定时器，间隔: 15.0秒
[INFO] [timestamp] [gen3_network_manager]: 网络管理器初始化完成
```

### 优化后（DEBUG级别）
```
[INFO] [timestamp] [gen3_network_manager]: 网络管理器初始化完成
[DEBUG] [timestamp] [gen3_network_manager]: [STATUS] 检测到 3 个网络接口
[DEBUG] [timestamp] [gen3_network_manager]: [STATUS] 接口: wlan0, 状态: UP, IP: ***********00
[DEBUG] [timestamp] [gen3_network_manager]: [SUB] 收到网络质量信息 - 接口: wlan0, 类型: 1, 信号强度: -45dBm, 延迟: 12.5ms, 综合评分: 85.2
[DEBUG] [timestamp] [gen3_network_manager]: [PUB] 发布网络状态 - 类型: WiFi, 接口: wlan0, 状态: 已连接, IP: ***********00, 网关: ***********
```

## 效果

1. **INFO级别**：只显示重要的状态信息，日志清晰简洁
2. **DEBUG级别**：显示所有详细信息，便于深度调试
3. **WARN级别**：只显示警告和错误，适合生产环境

这样的优化让日志输出更加合理，既保证了重要信息的可见性，又避免了过多的日志噪音。
