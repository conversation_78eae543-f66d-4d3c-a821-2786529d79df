# ROS2 DEBUG 日志说明

## 您看到的DEBUG日志来源

您在终端中看到的这些DEBUG日志：

```
[DEBUG] [1751990188.818511542] [rcl]: Initializing wait set with '0' subscriptions, '2' guard conditions, '0' timers, '0' clients, '0' services
[DEBUG] [1751990188.818822451] [rcl]: Waiting with timeout: 9s + 999207542ns
[DEBUG] [1751990188.818929490] [rcl]: Timeout calculated based on next scheduled timer: true
[DEBUG] [1751990188.819053738] [rcl]: Guard condition in wait set is ready
[DEBUG] [1751990198.818311888] [rcl]: Timer in wait set is ready
[DEBUG] [1751990198.818454218] [rcl]: Calling timer
```

**这些日志来自ROS2的底层组件，不是您的应用程序代码产生的。**

## 日志来源分析

### 1. RCL (ROS Client Library)
- `[rcl]: Initializing wait set` - 初始化事件等待集合
- `[rcl]: Waiting with timeout` - 等待事件，设置超时时间
- `[rcl]: Timer in wait set is ready` - 定时器准备就绪
- `[rcl]: Calling timer` - 调用定时器回调函数

### 2. 为什么会出现这些日志？
- 您的配置文件中设置了 `log_level: "debug"`
- ROS2的全局日志级别被设置为DEBUG
- 这导致所有ROS2组件（包括底层的RCL）都输出DEBUG级别的日志

## 如何控制这些DEBUG日志

### 方法1：修改配置文件

#### 默认配置 (network_config.yaml)
```yaml
/**:
  ros__parameters:
    log_level: "info"  # 改为info，减少日志输出
```

#### 开发配置 (development_config.yaml)
```yaml
/**:
  ros__parameters:
    log_level: "debug"  # 开发时保持debug，但要知道会有很多底层日志
```

#### 生产配置 (production_config.yaml)
```yaml
/**:
  ros__parameters:
    log_level: "warn"  # 生产环境只显示警告和错误
```

### 方法2：运行时覆盖日志级别

```bash
# 使用info级别运行
ros2 run gen3_network_manager_core network_manager_node \
  --ros-args -p log_level:=info

# 使用warn级别运行
ros2 run gen3_network_manager_core network_manager_node \
  --ros-args -p log_level:=warn
```

### 方法3：环境变量控制

```bash
# 设置RCL日志级别为INFO，减少底层日志
export RCUTILS_LOGGING_SEVERITY_THRESHOLD=INFO

# 然后运行程序
ros2 run gen3_network_manager_core network_manager_node
```

### 方法4：使用launch文件时指定日志级别

```bash
# 使用开发配置但覆盖日志级别
ros2 launch gen3_network_manager_core network_manager.launch.py \
  config_file:=development_config.yaml \
  log_level:=info
```

## 日志级别说明

| 级别 | 数值 | 说明 | 适用场景 |
|------|------|------|----------|
| debug | 10 | 最详细的日志，包括RCL底层信息 | 深度调试 |
| info | 20 | 一般信息日志 | 日常开发 |
| warn | 30 | 警告和错误信息 | 生产环境 |
| error | 40 | 只显示错误信息 | 问题排查 |
| fatal | 50 | 只显示致命错误 | 最小日志 |

## 推荐设置

### 开发环境
- 如果需要调试网络管理器本身：使用 `debug`
- 如果只是测试功能：使用 `info`

### 生产环境
- 正常运行：使用 `warn` 或 `info`
- 问题排查：临时切换到 `debug`

## 示例：减少DEBUG日志的配置

修改 `development_config.yaml`：

```yaml
/**:
  ros__parameters:
    # 日志配置 - 开发环境使用info而不是debug
    log_level: "info"  # 改为info，避免过多RCL日志
    
    # 其他配置保持不变...
    network_check_interval: 0.5
    quality_check_interval: 2.0
    # ...
```

这样您就可以看到网络管理器的重要信息，而不会被RCL的底层DEBUG日志淹没。

## 总结

- **DEBUG日志来自ROS2底层，不是您的代码问题**
- **通过调整配置文件中的 `log_level` 可以控制日志输出**
- **推荐开发时使用 `info`，生产时使用 `warn`**
- **只有在需要深度调试时才使用 `debug` 级别**
