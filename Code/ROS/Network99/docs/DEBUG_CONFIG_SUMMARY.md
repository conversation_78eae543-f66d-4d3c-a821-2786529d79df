# ROS2 网络管理器调试配置文档

## 配置概述 ✅

本项目的 VSCode 调试配置已完全优化，解决了共享库加载问题，并支持多种调试场景。

## 可用的调试配置

### 1. 基础调试配置
- **ROS2 C++ Debug (Auto-Arch)** - 基础调试，自动架构检测
- **ROS2 C++ Debug (x86_64 Fixed)** - 固定 x86_64 架构基础调试

### 2. 带配置文件的调试
- **ROS2 C++ Debug with Config (Auto-Arch)** - 使用网络配置文件
- **ROS2 C++ Debug with Config (x86_64 Fixed)** - 固定架构带配置文件

### 3. 开发环境调试
- **ROS2 C++ Debug with Dev Config (Auto-Arch)** - 开发环境配置，详细日志

### 4. Release 版本调试
- **ROS2 C++ Release Debug (Auto-Arch)** - Release 版本调试

### 5. 进程调试
- **ROS2 C++ Attach to Process (Auto-Arch)** - 附加到运行中的进程

### 6. Launch 文件调试
- **ROS2 Launch File Debug** - 使用 Launch 文件启动调试
- **ROS2 Launch with Custom Config** - 自定义配置的 Launch 调试

## 主要改进

### 1. 解决共享库加载问题
- ✅ 添加 `/opt/ros/humble/lib` 到 `LD_LIBRARY_PATH`
- ✅ 设置完整的 ROS 环境变量
- ✅ 解决 `librclcpp_action.so: cannot open shared object file` 错误

### 2. 架构自适应支持
- ✅ 提供架构选择输入 `${input:architecture}`
- ✅ 自动检测脚本 `scripts/update_launch_config.sh`
- ✅ 架构自适应启动脚本 `run_network_manager.sh`
- ✅ 混合配置：Auto-Arch（灵活）+ Fixed（快速）

### 3. 完整环境变量设置
所有配置现在包含：
- `LD_LIBRARY_PATH`: ROS + 工作空间库路径
- `AMENT_PREFIX_PATH`: ROS + 工作空间包路径
- `ROS_DOMAIN_ID`: 0
- `ROS_DISTRO`: humble
- `ROS_VERSION`: 2

## 使用方法

### 快速开始
1. **构建项目**：`./build.sh`
2. **更新配置**：`./scripts/update_launch_config.sh`
3. **开始调试**：在 VSCode 中按 `F5`

### 命令行运行
```bash
./run_network_manager.sh  # 自动架构检测和环境设置
```

### VSCode 调试
1. 按 `F5` 或点击 "Run and Debug"
2. 选择调试配置：
   - 日常开发：`ROS2 C++ Debug (Auto-Arch)`
   - 带配置：`ROS2 C++ Debug with Config (Auto-Arch)`
   - 开发调试：`ROS2 C++ Debug with Dev Config (Auto-Arch)`

## 测试验证

运行测试脚本验证配置：
```bash
./test_debug_config.sh
```

测试结果：
- ✅ 当前架构：x86_64
- ✅ ROS Humble 已安装
- ✅ 构建目录存在
- ✅ 可执行文件存在
- ✅ launch.json 配置正确
- ✅ ROS 共享库可用
- ✅ 环境变量设置正确
- ✅ 程序可以正常启动
- ✅ 找到 7 个调试配置

## 支持的架构

- **x86_64**: Intel/AMD 64位处理器
- **aarch64**: ARM 64位处理器（如树莓派、Jetson）
- **arm64**: ARM 64位处理器（macOS Apple Silicon）

## 使用方法

### VSCode 调试
1. 在 VSCode 中按 `F5`
2. 选择合适的配置：
   - `ROS2 C++ Debug (Auto-Arch)` - 可选择架构，灵活性高
   - `ROS2 C++ Debug (x86_64 Fixed)` - 固定当前架构，启动快
   - `ROS2 C++ Debug with Config (Auto-Arch)` - 带配置文件
3. 如选择 Auto-Arch，选择目标架构（默认 x86_64）
4. 开始调试

### 命令行运行
```bash
./run_network_manager.sh  # 自动架构检测和环境设置
```

## 切换架构时的操作

当在不同架构的机器上工作时：
1. 重新构建：`./build.sh`
2. 更新配置：`./scripts/update_launch_config.sh`
3. 验证配置：`./test_debug_config.sh`

## 相关文件

- `.vscode/launch.json` - VSCode 调试配置
- `.vscode/tasks.json` - VSCode 任务配置
- `scripts/update_launch_config.sh` - 配置更新脚本
- `scripts/get_arch.sh` - 架构检测脚本
- `run_network_manager.sh` - 启动脚本
- `test_debug_config.sh` - 配置测试脚本
- `docs/ARCHITECTURE_CONFIG.md` - 详细文档

## 故障排除

### 共享库错误
如果遇到 `librclcpp_action.so` 错误：
```bash
./scripts/update_launch_config.sh  # 更新配置
./run_network_manager.sh           # 使用启动脚本
```

### 程序不存在错误
如果调试时找不到程序：
```bash
./build.sh                         # 重新构建
./scripts/update_launch_config.sh  # 更新配置
```

### 架构不匹配
在新架构机器上：
```bash
./build.sh                         # 构建对应架构
./scripts/update_launch_config.sh  # 更新配置
./test_debug_config.sh             # 验证配置
```

## 最佳实践

1. **优先使用 Auto-Arch 配置**：支持多架构，更灵活
2. **定期更新配置**：在新机器或架构变更后运行更新脚本
3. **使用启动脚本**：`run_network_manager.sh` 自动处理环境设置
4. **验证配置**：使用 `test_debug_config.sh` 确保配置正确

---

**配置更新完成！现在可以在任何支持的架构上无缝进行 ROS2 项目的开发和调试。**
