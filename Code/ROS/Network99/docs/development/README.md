# 开发文档

本目录包含 ROS2 网络管理器的开发相关文档和工具。

## 📋 文档列表

### [<PERSON>开发笔记.md](<PERSON>开发笔记.md)
**Claude 开发笔记**
- AI 辅助开发记录
- 开发过程中的问题和解决方案
- 代码优化建议
- 适用场景：开发者、技术研究

## 🛠️ 开发工具

### 构建和测试
```bash
# 构建项目
./build.sh

# 运行测试
./test_debug_config.sh

# 验证文档
./verify_documentation.sh
```

### 调试工具
```bash
# 启动调试
./run_network_manager.sh

# VSCode 调试
# 按 F5，选择调试配置
```

### 架构工具
```bash
# 检测架构
./scripts/get_arch.sh

# 更新配置
./scripts/update_launch_config.sh
```

## 🔧 开发环境

### 必需工具
- **ROS2 Humble**: 基础运行环境
- **VSCode**: 推荐开发IDE
- **GDB**: 调试工具
- **Git**: 版本控制

### 推荐扩展
- C/C++ Extension Pack
- ROS Extension
- Markdown All in One

## 🔗 相关文档

- [调试配置](../调试配置指南.md) - 完整开发环境设置
- [架构配置](../架构配置说明.md) - 多架构支持
- [故障排除](../troubleshooting/) - 开发问题解决

## 📝 开发流程

1. **环境准备**: 安装 ROS2 Humble 和开发工具
2. **项目构建**: 运行 `./build.sh` 构建项目
3. **配置调试**: 使用 VSCode 调试配置
4. **代码开发**: 编写和测试代码
5. **文档更新**: 同步更新相关文档

## 🧪 测试策略

### 单元测试
- 核心功能模块测试
- 网络接口测试
- 配置参数验证

### 集成测试
- 多组件协同测试
- ROS2 接口测试
- 端到端功能测试

### 系统测试
- 多架构兼容性测试
- 性能压力测试
- 故障恢复测试

---

**返回**: [文档索引](../README.md) | [项目主页](../../README.md)
