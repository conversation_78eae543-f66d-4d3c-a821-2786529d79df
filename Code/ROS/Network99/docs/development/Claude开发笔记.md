# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Building the Project

This is a ROS2 workspace with a custom build script that uses colcon:

```bash
# Build all packages
./build.sh

# Build specific packages
./build.sh gen3_network_interfaces gen3_network_manager_core

# Build with different options
./build.sh --release                    # Release build (default: Debug)
./build.sh --clean                      # Clean build
./build.sh --verbose                    # Verbose output
./build.sh --no-install                 # Build only, don't install
./build.sh --update-deps                # Update rosdep dependencies
./build.sh -j 8                         # Use 8 parallel jobs
```

The build script creates architecture-specific build/install directories (build/{arch}/{BuildType} and install/{arch}/{BuildType}).

### NR90 Serial Tool (Development)

For the NR90 5G serial communication tool in `other/nr90_serial_tool/`:

```bash
cd other/nr90_serial_tool
make                    # Build the tool
make clean             # Clean build files
make test              # Run device test
make check             # Code quality check (requires cppcheck)
make format            # Format code (requires clang-format)
```

### Setting up ROS2 Environment

```bash
# Source the environment after building
source install/setup.bash

# Or use architecture-specific paths
source install/{arch}/Debug/setup.bash
```

### Running Tests

The codebase uses standard ROS2 testing with ament:

```bash
# Run all tests (after building)
colcon test --build-base build/{arch}/Debug --install-base install/{arch}/Debug

# Run tests for specific packages
colcon test --packages-select gen3_network_manager_core
```

## Code Architecture

This is a ROS2 network management system with the following structure:

### Main ROS2 Packages

- **gen3_network_interfaces**: Custom ROS2 message/service/action definitions
- **gen3_network_manager_core**: Main network management node implementation

### System Architecture

The system uses a **single-node multi-component architecture**:
- One ROS2 node: `gen3_network_manager`
- Six internal C++ components within the node:
  - NetworkManager: Overall network state management and external interfaces
  - WiFiManager: WiFi connection and management
  - NetworkSwitch: Network switching between WiFi/5G/Ethernet
  - BindingManager: Device binding processes (BLE/QR code)
  - NetworkMonitor: Network status and quality monitoring
  - DNSManager: DNS server management

### Key Features

- Real-time network status monitoring and quality assessment
- Automatic switching between WiFi, 5G, and Ethernet networks
- Device binding support (BLE and QR code methods)
- WiFi network scanning, connection, and priority management
- Network connectivity testing and diagnostics

### File Structure

```
src/
   gen3_network_interfaces/          # ROS2 interface definitions
      msg/                         # Message types
      srv/                         # Service types
      action/                      # Action types
   gen3_network_manager_core/       # Main implementation
       include/gen3_network_manager_core/  # Header files
       src/                         # Source implementations
       config/                      # Configuration files
       launch/                      # Launch files
```

### Dependencies

- ROS2 Foxy or later
- C++14 standard
- Standard ROS2 packages: rclcpp, rclcpp_action, rclcpp_components
- System dependencies: uuid library
- Network tools: nmcli, iwlist, ping

### Configuration

The system uses YAML configuration files in `src/gen3_network_manager_core/config/`:
- `network_config.yaml`: Production configuration
- `development_config.yaml`: Development configuration
- `production_config.yaml`: Production deployment configuration

### Running the System

```bash
# Launch with launch file (recommended)
ros2 launch gen3_network_manager_core network_manager.launch.py

# Or run node directly
ros2 run gen3_network_manager_core network_manager_node
```

### Key ROS2 Interfaces

**Main Topics:**
- `/network_status`: Current network status (1Hz)
- `/network_quality`: Network quality metrics (0.2Hz)
- `/binding_status`: Device binding status (0.5Hz)

**Main Services:**
- `/switch_network`: Manual network switching
- `/connect_wifi`: WiFi connection
- `/get_network_list`: Get available/saved networks
- `/start_binding`: Start device binding

**Actions:**
- `/network_binding`: Long-running binding process with progress feedback

### Platform Specifics

- Targets Linux systems (tested on Ubuntu 20.04)
- Uses NetworkManager (nmcli) for WiFi operations
- Supports 5G modems via USB interfaces
- Requires appropriate permissions for network operations

## Development Notes

- The codebase includes Chinese documentation and comments
- Build output uses colored terminal output for better visibility
- The build script supports cross-compilation for different architectures
- Network interface names are configurable (default: wlan0 for WiFi, usb0 for 5G)
- The system can operate in different priority modes (5G-first, WiFi-first, or adaptive)

## Recent Updates (2025-07-09)

### Smart Route Management System

Enhanced the network switching logic with intelligent route management:

#### Key Features
1. **Smart Route Detection**: Automatically detects if only target interface routes exist
2. **Route Metric Management**: Sets WiFi routes with highest priority (lowest metric)
3. **IP Address Waiting**: Waits for interface to acquire IP before proceeding
4. **Duplicate Route Cleanup**: Removes duplicate WiFi routes automatically
5. **Intelligent Fallback**: Restores previous routes if switching fails

#### Enhanced Functions
- `manage_route_intelligently()`: Main smart routing logic
- `wait_for_ip_address()`: Waits for interface to get IP with timeout
- `set_route_metric()`: Sets route priority using metric values
- `set_wifi_route_priority()`: Ensures WiFi gets highest priority
- `clean_duplicate_wifi_routes()`: Removes redundant WiFi routes

#### Network Switching Logic
From 5G to WiFi:
1. Connects to WiFi and waits for IP acquisition
2. Checks existing route table composition
3. If only WiFi routes exist: no changes needed
4. If multiple routes exist: sets WiFi metric to 100 (highest priority)
5. Cleans up any duplicate WiFi routes
6. Maintains route table integrity throughout process

This update fixes the previous issue where default routes were deleted unconditionally during network switching.