# Gen3 网络管理系统 ROS2 接口文档

## 📋 概述

本文档详细描述了Gen3网络管理系统中所有ROS2话题(Topics)、服务(Services)和动作(Actions)的接口定义和使用方法。

## 🏗️ 系统架构

系统采用**单节点多组件架构**，包含：

### ROS2节点

- **gen3_network_manager** - 唯一的ROS2节点，集成所有网络管理功能

### 内部组件 (C++类)

该节点内部包含7个核心管理器组件：

- **NetworkManager** - 网络管理器，负责整体网络状态管理和对外接口
- **WiFiManager** - WiFi管理器，负责WiFi网络的连接和管理
- **NetworkSwitch** - 网络切换器，负责在不同网络之间进行切换
- **BindingManager** - 设备绑定管理器，负责设备绑定流程
- **NetworkMonitor** - 网络监控器，负责监控网络状态和质量
- **DNSManager** - DNS管理器，负责DNS服务器管理
- **IptablesUtils** - NAT规则管理工具，负责iptables规则的自动配置和管理

### 架构特点

- **单进程设计**: 所有组件运行在同一个进程中，减少进程间通信开销
- **模块化组织**: 各组件职责明确，便于维护和扩展
- **统一接口**: 通过单一节点对外提供所有ROS2接口
- **内部协作**: 组件间通过共享内存和直接函数调用进行高效通信

## 📨 消息类型 (Messages)

### 1. NetworkStatus - 网络状态消息

**话题**: `network_status`, `network_status_broadcast`
**发布者**: gen3_network_manager节点 (NetworkManager组件, NetworkMonitor组件)
**订阅者**: gen3_network_manager节点 (NetworkMonitor组件内部订阅)

```msg
# 网络类型常量
uint8 NETWORK_TYPE_UNKNOWN = 0
uint8 NETWORK_TYPE_WIFI = 1
uint8 NETWORK_TYPE_5G = 2
uint8 NETWORK_TYPE_ETHERNET = 3

# 连接状态常量
uint8 CONNECTION_STATUS_DISCONNECTED = 0
uint8 CONNECTION_STATUS_CONNECTING = 1
uint8 CONNECTION_STATUS_CONNECTED = 2
uint8 CONNECTION_STATUS_SWITCHING = 3
uint8 CONNECTION_STATUS_ERROR = 4

# 基本信息
std_msgs/Header header
uint8 network_type              # 当前网络类型
uint8 connection_status         # 连接状态
string interface_name           # 网络接口名称
string ip_address              # IP地址
string gateway                 # 网关地址
string[] dns_servers           # DNS服务器列表

# WiFi特定信息
string wifi_ssid               # WiFi网络名称
int32 wifi_signal_strength     # WiFi信号强度 (dBm)
string wifi_security_type      # 加密类型
```

### 2. NetworkQuality - 网络质量消息

**话题**: `network_quality`
**发布者**: gen3_network_manager节点 (NetworkSwitch组件)
**订阅者**: gen3_network_manager节点 (NetworkManager组件, NetworkMonitor组件)

```msg
std_msgs/Header header

# 网络标识
uint8 network_type             # 网络类型 (WiFi/5G)
string interface_name          # 接口名称

# 信号质量
int32 signal_strength          # 信号强度 (dBm)
float64 signal_quality         # 信号质量百分比 (0-100)

# 连通性测试
bool gateway_reachable         # 网关可达性
bool dns_working               # DNS解析正常
bool internet_accessible       # 互联网可访问

# 性能指标
float64 latency_ms             # 延迟测试结果
float64 jitter_ms              # 抖动
float64 download_speed_mbps    # 下载速度
float64 upload_speed_mbps      # 上传速度
float64 packet_loss_rate       # 丢包率

# DNS性能
float64 dns_resolution_time_ms # DNS解析时间
string[] working_dns_servers   # 可用DNS服务器
string[] failed_dns_servers    # 失效DNS服务器

# 综合评分
float64 overall_score          # 综合质量评分 (0-100)
bool is_suitable_for_switching # 是否适合作为切换目标

# 检测时间
builtin_interfaces/Time test_start_time
builtin_interfaces/Time test_end_time
```

### 3. WiFiNetwork - WiFi网络信息消息

**用途**: 网络列表管理、连接历史记录

```msg
# 网络基本信息
string ssid                    # 网络名称
string bssid                   # MAC地址
int32 signal_strength          # 信号强度 (dBm)
uint32 frequency               # 频率 (MHz)
string security_type           # 加密类型 (OPEN/WEP/WPA/WPA2/WPA3)
bool is_hidden                 # 是否隐藏网络

# 连接配置
string password                # 网络密码 (加密存储)
bool auto_connect              # 是否自动连接
uint32 priority                # 优先级 (数值越大优先级越高)

# 绑定信息
bool is_bound_network          # 是否通过绑定流程添加
builtin_interfaces/Time binding_time    # 绑定时间
string binding_source          # 绑定来源 (BLE/QR_CODE/MANUAL)

# 连接历史
uint32 connection_count        # 连接次数
builtin_interfaces/Time last_connected_time  # 最后连接时间
float64 success_rate           # 连接成功率 (0-1)
int32 min_signal_threshold     # 最低信号阈值

# 网络质量历史
float64 avg_latency_ms         # 平均延迟
float64 avg_download_speed     # 平均下载速度
float64 avg_upload_speed       # 平均上传速度

# 状态标记
bool is_available              # 当前是否可用
bool is_in_range               # 是否在信号范围内
builtin_interfaces/Time last_seen_time   # 最后发现时间
```

### 4. BindingStatus - 设备绑定状态消息(暂定)

**话题**: `binding_status`
**发布者**: gen3_network_manager节点 (BindingManager组件)
**订阅者**: gen3_network_manager节点 (NetworkMonitor组件)

```msg
# 绑定状态常量
uint8 BINDING_STATUS_IDLE = 0
uint8 BINDING_STATUS_WAITING = 1
uint8 BINDING_STATUS_CONNECTING = 2
uint8 BINDING_STATUS_CONFIGURING = 3
uint8 BINDING_STATUS_REGISTERING = 4
uint8 BINDING_STATUS_SUCCESS = 5
uint8 BINDING_STATUS_FAILED = 6
uint8 BINDING_STATUS_TIMEOUT = 7

# 绑定方式常量
uint8 BINDING_METHOD_UNKNOWN = 0
uint8 BINDING_METHOD_BLE = 1
uint8 BINDING_METHOD_QR_CODE = 2
uint8 BINDING_METHOD_MANUAL = 3

std_msgs/Header header

# 绑定基本信息
uint8 binding_status           # 当前绑定状态
uint8 binding_method           # 绑定方式
string device_id               # 设备ID
string user_device_id          # 用户设备ID

# 绑定进度
uint8 progress_percentage      # 进度百分比 (0-100)
string current_step            # 当前步骤描述
string status_message          # 状态消息
```

## 🔧 服务类型 (Services)

### 1. SwitchNetwork - 网络切换服务

**服务名**: `switch_network`
**服务器**: gen3_network_manager节点 (NetworkManager组件)
**客户端**: 外部控制程序

```srv
# 网络类型常量
uint8 NETWORK_TYPE_AUTO = 0    # 自动选择最优网络
uint8 NETWORK_TYPE_WIFI = 1    # 强制使用WiFi
uint8 NETWORK_TYPE_5G = 2      # 强制使用5G
uint8 NETWORK_TYPE_ETHERNET = 3 # 强制使用以太网

# 请求
uint8 target_network_type      # 目标网络类型
string target_wifi_ssid        # 目标WiFi (仅当类型为WiFi时)
bool force_switch              # 是否强制切换 (忽略质量检测)
uint32 timeout_seconds         # 切换超时时间

---

# 响应
bool success                   # 切换是否成功
string message                 # 结果消息
string error_code              # 错误代码

# 切换前网络信息
uint8 previous_network_type    # 切换前网络类型
string previous_interface      # 切换前接口名称
string previous_ip_address     # 切换前IP地址
```

### 2. ConnectWiFi - WiFi连接服务

**服务名**: `connect_wifi`
**服务器**: gen3_network_manager节点 (WiFiManager组件)
**客户端**: 外部控制程序

```srv
# 请求
string ssid                    # WiFi网络名称
string password                # 网络密码
string security_type           # 加密类型 (可选，自动检测)
bool save_to_list              # 是否保存到已知网络列表
uint32 priority                # 优先级 (可选)
bool is_bound_network          # 是否标记为绑定网络
uint32 timeout_seconds         # 连接超时时间 (默认30秒)

---

# 响应
bool success                   # 连接是否成功
string message                 # 结果消息
string error_code              # 错误代码 (失败时)

# 连接结果信息
string ip_address              # 获得的IP地址
string gateway                 # 网关地址
string[] dns_servers           # DNS服务器
int32 signal_strength          # 信号强度
float64 connection_time_seconds # 连接耗时
```

### 3. GetNetworkList - 获取网络列表服务

**服务名**: `get_network_list`
**服务器**: gen3_network_manager节点 (WiFiManager组件)
**客户端**: 外部控制程序

```srv
# 列表类型常量
uint8 LIST_TYPE_AVAILABLE = 1  # 可用网络列表
uint8 LIST_TYPE_SAVED = 2      # 已保存网络列表
uint8 LIST_TYPE_ALL = 3        # 所有网络列表

# 请求
uint8 list_type                # 列表类型
bool include_signal_strength   # 是否包含信号强度
bool include_connection_history # 是否包含连接历史
bool force_scan                # 是否强制重新扫描

---

# 响应
bool success                   # 查询是否成功
string message                 # 结果消息

# 网络列表
WiFiNetwork[] available_networks  # 可用网络列表
WiFiNetwork[] saved_networks      # 已保存网络列表

# 扫描信息
builtin_interfaces/Time scan_time # 扫描时间
uint32 total_networks_found       # 发现的网络总数
uint32 scan_duration_ms           # 扫描耗时
```

### 4. SetNetworkPriority - 设置网络优先级服务

**服务名**: `set_network_priority`
**服务器**: gen3_network_manager节点 (WiFiManager组件)
**客户端**: 外部控制程序

```srv
# 请求
string ssid                    # 网络名称
uint32 priority                # 新的优先级 (数值越大优先级越高)
bool is_bound_network          # 是否标记为绑定网络
bool auto_connect              # 是否自动连接

---

# 响应
bool success                   # 设置是否成功
string message                 # 结果消息
string error_code              # 错误代码

# 更新后的网络信息
uint32 old_priority            # 原优先级
uint32 new_priority            # 新优先级
uint32 current_rank            # 当前排名 (在所有网络中的位置)
```

### 5. StartBinding - 启动设备绑定服务

**服务名**: `start_binding`
**服务器**: gen3_network_manager节点 (BindingManager组件)
**客户端**: 外部控制程序

```srv
# 绑定方式常量
uint8 BINDING_METHOD_BLE = 1
uint8 BINDING_METHOD_QR_CODE = 2
uint8 BINDING_METHOD_BOTH = 3

# 请求
uint8 binding_method           # 绑定方式
uint32 timeout_seconds         # 超时时间
string device_name             # 设备名称
bool reset_existing_binding    # 是否重置现有绑定

---

# 响应
bool success                   # 启动是否成功
string message                 # 结果消息
string error_code              # 错误代码

# 绑定会话信息
string session_id              # 绑定会话ID
builtin_interfaces/Time session_start_time # 会话开始时间
uint32 estimated_duration_seconds # 预计绑定时长
```

## 🎯 动作类型 (Actions)

### 1. NetworkBinding - 网络绑定动作

**动作名**: `network_binding`
**动作服务器**: gen3_network_manager节点 (BindingManager组件)
**动作客户端**: 外部控制程序

```action
# 绑定方式常量
uint8 BINDING_METHOD_BLE = 1
uint8 BINDING_METHOD_QR_CODE = 2
uint8 BINDING_METHOD_BOTH = 3

# Goal - 绑定目标
uint8 binding_method           # 绑定方式
uint32 timeout_seconds         # 超时时间
string device_name             # 设备名称
bool reset_existing_binding    # 是否重置现有绑定

---

# Result - 绑定结果
bool success                   # 绑定是否成功
string message                 # 结果消息
string error_code              # 错误代码

# 绑定信息
string device_id               # 设备ID
string user_device_id          # 用户设备ID
builtin_interfaces/Time binding_completion_time

# WiFi配置结果
bool wifi_configured           # WiFi是否配置成功
string wifi_ssid               # 配置的WiFi网络
string wifi_ip_address         # 获得的IP地址

# 平台注册结果
bool platform_registered       # 是否成功注册到平台
string registration_token      # 注册令牌

---

# Feedback - 绑定进度反馈
uint8 current_step             # 当前步骤
string step_description        # 步骤描述
uint8 progress_percentage      # 进度百分比 (0-100)
string status_message          # 状态消息

# 步骤常量
uint8 STEP_INITIALIZING = 1    # 初始化
uint8 STEP_WAITING_CONNECTION = 2  # 等待连接
uint8 STEP_RECEIVING_CONFIG = 3    # 接收配置
uint8 STEP_CONNECTING_WIFI = 4     # 连接WiFi
uint8 STEP_REGISTERING_PLATFORM = 5  # 注册平台
uint8 STEP_FINALIZING = 6      # 完成绑定

# 实时状态
bool ble_advertising           # BLE是否在广播
bool qr_scanner_active         # 二维码扫描是否激活
uint32 connection_attempts     # 连接尝试次数
builtin_interfaces/Time step_start_time
```

## 📡 话题列表 (Topics)

### 发布话题

| 话题名称                     | 消息类型       | 发布者组件     | 频率     | 描述             |
| ---------------------------- | -------------- | -------------- | -------- | ---------------- |
| `network_status`           | NetworkStatus  | NetworkManager | 1Hz      | 当前网络状态     |
| `network_status_broadcast` | NetworkStatus  | NetworkMonitor | 事件触发 | 网络状态广播     |
| `network_quality`          | NetworkQuality | NetworkSwitch  | 0.2Hz    | 网络质量检测结果 |
| `binding_status`           | BindingStatus  | BindingManager | 0.5Hz    | 设备绑定状态     |
| `wifi_status`              | String         | WiFiManager    | 事件触发 | WiFi连接状态     |
| `switch_status`            | String         | NetworkSwitch  | 事件触发 | 网络切换状态     |
| `connection_status`        | String         | NetworkMonitor | 事件触发 | 连接状态变化     |
| `board_status`             | String         | NetworkMonitor | 事件触发 | 板间通信状态     |
| `dns_status`               | String         | DNSManager     | 事件触发 | DNS服务状态      |

**注意**: 所有话题都由单一的 `gen3_network_manager` 节点发布，表中的"发布者组件"指节点内部的具体组件。

### 订阅话题

| 订阅组件       | 订阅话题            | 消息类型       | 回调函数                     | 描述             |
| -------------- | ------------------- | -------------- | ---------------------------- | ---------------- |
| NetworkManager | `network_quality` | NetworkQuality | `network_quality_callback` | 处理网络质量信息 |
| NetworkMonitor | `network_status`  | NetworkStatus  | `network_status_callback`  | 监控网络状态     |
| NetworkMonitor | `network_quality` | NetworkQuality | `network_quality_callback` | 监控网络质量     |
| NetworkMonitor | `binding_status`  | BindingStatus  | `binding_status_callback`  | 监控绑定状态     |

**注意**: 所有订阅都在单一的 `gen3_network_manager` 节点内部进行，表中的"订阅组件"指节点内部的具体组件。组件间通过内部回调函数进行通信。

## 🔧 服务列表 (Services)

| 服务名称                 | 服务类型           | 服务器组件     | 描述           |
| ------------------------ | ------------------ | -------------- | -------------- |
| `switch_network`       | SwitchNetwork      | NetworkManager | 手动网络切换   |
| `connect_wifi`         | ConnectWiFi        | WiFiManager    | WiFi连接       |
| `get_network_list`     | GetNetworkList     | WiFiManager    | 获取网络列表   |
| `set_network_priority` | SetNetworkPriority | WiFiManager    | 设置网络优先级 |
| `start_binding`        | StartBinding       | BindingManager | 启动设备绑定   |

**注意**: 所有服务都由单一的 `gen3_network_manager` 节点提供，表中的"服务器组件"指节点内部的具体组件。

## 🎯 动作列表 (Actions)

| 动作名称            | 动作类型       | 动作服务器组件 | 描述               |
| ------------------- | -------------- | -------------- | ------------------ |
| `network_binding` | NetworkBinding | BindingManager | 长时间设备绑定流程 |

**注意**: 所有动作都由单一的 `gen3_network_manager` 节点提供，表中的"动作服务器组件"指节点内部的具体组件。

## � 系统启动

### 启动网络管理系统

```bash
# 设置环境
source ~/ros2_ws/install/setup.bash

# 启动网络管理器（使用launch文件）
ros2 launch gen3_network_manager_core network_manager.launch.py

# 或者直接运行节点
ros2 run gen3_network_manager_core network_manager_node
```

### 启动参数

```bash
# 指定日志级别
ros2 launch gen3_network_manager_core network_manager.launch.py log_level:=debug

# 指定配置文件
ros2 launch gen3_network_manager_core network_manager.launch.py config_file:=/path/to/config.yaml
```

### 验证系统运行

```bash
# 检查节点是否运行
ros2 node list | grep gen3_network_manager

# 检查话题是否发布
ros2 topic list | grep network

# 查看网络状态
ros2 topic echo /gen3/network/status --once
```

## �📝 使用示例

### 1. 查看当前网络状态

```bash
# 查看网络状态话题
ros2 topic echo /network_status

# 查看网络质量
ros2 topic echo /network_quality
```

### 2. 手动切换网络

```bash
# 切换到WiFi
ros2 service call /switch_network gen3_network_interfaces/srv/SwitchNetwork "{target_network_type: 1, target_wifi_ssid: 'K3_5g', force_switch: false, timeout_seconds: 30}"

# 切换到5G网络
ros2 service call /switch_network gen3_network_interfaces/srv/SwitchNetwork "{target_network_type: 2, force_switch: false, timeout_seconds: 30}"

# 切换到以太网
ros2 service call /switch_network gen3_network_interfaces/srv/SwitchNetwork "{target_network_type: 3, force_switch: false, timeout_seconds: 30}"

# 自动选择最优网络
ros2 service call /switch_network gen3_network_interfaces/srv/SwitchNetwork "{target_network_type: 0, force_switch: false, timeout_seconds: 30}"

# 强制切换（忽略质量检测）
ros2 service call /switch_network gen3_network_interfaces/srv/SwitchNetwork "{target_network_type: 1, target_wifi_ssid: 'MyWiFi', force_switch: true, timeout_seconds: 30}"
```

### 3. 连接WiFi网络

```bash
# 连接新的WiFi网络
ros2 service call /connect_wifi gen3_network_interfaces/srv/ConnectWiFi "{ssid: 'robot66', password: '66666666', save_to_list: true, priority: 100, timeout_seconds: 30}"
```

### 4. 获取网络列表

```bash
# 获取所有可用网络
ros2 service call /get_network_list gen3_network_interfaces/srv/GetNetworkList "{list_type: 1, include_signal_strength: true, force_scan: true}"

# 获取已保存网络
ros2 service call /get_network_list gen3_network_interfaces/srv/GetNetworkList "{list_type: 2, include_connection_history: true}"
```

### 5. 启动设备绑定

```bash
# 启动BLE绑定
ros2 service call /start_binding gen3_network_interfaces/srv/StartBinding "{binding_method: 1, timeout_seconds: 300, device_name: 'Gen3Robot'}"

# 使用动作进行绑定（支持进度反馈）
ros2 action send_goal /network_binding gen3_network_interfaces/action/NetworkBinding "{binding_method: 3, timeout_seconds: 300, device_name: 'Gen3Robot'}"
```

## � 网络优先级配置示例

### 5G优先配置

当需要优先使用5G网络时，可以通过以下方式配置：

#### 方法1: 直接切换到5G网络

```bash
# 立即切换到5G网络
ros2 service call /switch_network gen3_network_interfaces/srv/SwitchNetwork "{target_network_type: 2, force_switch: false, timeout_seconds: 30}"

# 强制切换到5G（忽略网络质量检测）
ros2 service call /switch_network gen3_network_interfaces/srv/SwitchNetwork "{target_network_type: 2, force_switch: true, timeout_seconds: 30}"
```

#### 方法2: 配置文件设置5G优先

在配置文件 `network_config.yaml` 中设置：

```yaml
# 5G优先配置
network_priority:
  preferred_network_type: 5G
  auto_switch_enabled: true
  quality_threshold:
    min_signal_strength: -85  # dBm
    min_download_speed: 10.0  # Mbps
    max_latency: 100.0        # ms

# 5G网络配置
5g_config:
  interface_name: "usb5g0"
  connection_timeout: 30
  retry_attempts: 3
  quality_check_interval: 10

# 备用网络配置（5G不可用时）
fallback_networks:
  - type: "WiFi"
    priority: 2
    auto_connect: true
  - type: "Ethernet"
    priority: 1
    auto_connect: true
```

#### 方法3: 运行时参数设置

```bash
# 启动时指定5G优先
ros2 launch gen3_network_manager_core network_manager.launch.py \
  preferred_network:=5G \
  auto_switch:=true \
  quality_check_interval:=10

# 运行时动态设置参数
ros2 param set /gen3_network_manager preferred_network_type 5G
ros2 param set /gen3_network_manager auto_switch_enabled true
```

### WiFi优先配置

当需要优先使用WiFi网络时，可以通过以下方式配置：

#### 方法1: 直接切换到WiFi网络

```bash
# 切换到指定WiFi网络
ros2 service call /switch_network gen3_network_interfaces/srv/SwitchNetwork "{target_network_type: 1, target_wifi_ssid: 'Office_WiFi', force_switch: false, timeout_seconds: 30}"

# 切换到最强信号的WiFi网络（自动选择）
ros2 service call /switch_network gen3_network_interfaces/srv/SwitchNetwork "{target_network_type: 1, target_wifi_ssid: '', force_switch: false, timeout_seconds: 30}"
```

#### 方法2: 配置文件设置WiFi优先

在配置文件 `network_config.yaml` 中设置：

```yaml
# WiFi优先配置
network_priority:
  preferred_network_type: WiFi
  auto_switch_enabled: true
  quality_threshold:
    min_signal_strength: -70  # dBm
    min_download_speed: 5.0   # Mbps
    max_latency: 50.0         # ms

# WiFi网络配置
wifi_config:
  interface_name: "wlan0"
  scan_interval: 30
  connection_timeout: 30
  retry_attempts: 3

  # 优先WiFi网络列表（按优先级排序）
  preferred_networks:
    - ssid: "Office_WiFi_5G"
      priority: 100
      auto_connect: true
      min_signal_threshold: -65
    - ssid: "Office_WiFi_2.4G"
      priority: 90
      auto_connect: true
      min_signal_threshold: -70
    - ssid: "Guest_WiFi"
      priority: 50
      auto_connect: false
      min_signal_threshold: -75

# 备用网络配置（WiFi不可用时）
fallback_networks:
  - type: "5G"
    priority: 2
    auto_connect: true
  - type: "Ethernet"
    priority: 1
    auto_connect: true
```

#### 方法3: 设置WiFi网络优先级

```bash
# 设置特定WiFi网络的高优先级
ros2 service call /set_network_priority gen3_network_interfaces/srv/SetNetworkPriority "{ssid: 'Office_WiFi_5G', priority: 100, auto_connect: true}"

# 设置多个WiFi网络的优先级
ros2 service call /set_network_priority gen3_network_interfaces/srv/SetNetworkPriority "{ssid: 'Office_WiFi_2.4G', priority: 90, auto_connect: true}"

ros2 service call /set_network_priority gen3_network_interfaces/srv/SetNetworkPriority "{ssid: 'Home_WiFi', priority: 80, auto_connect: true}"
```

### 混合优先级配置

根据不同场景自动选择最优网络：

#### 配置文件示例

```yaml
# 智能网络选择配置
network_priority:
  mode: "adaptive"  # auto, 5g_first, wifi_first, adaptive
  auto_switch_enabled: true
  switch_conditions:
    signal_degradation_threshold: -80  # dBm
    speed_degradation_threshold: 2.0   # Mbps
    latency_increase_threshold: 200.0  # ms
    connection_failure_threshold: 3    # 次数

# 网络类型优先级权重
network_weights:
  5G:
    base_priority: 100
    signal_weight: 0.3
    speed_weight: 0.4
    latency_weight: 0.3
  WiFi:
    base_priority: 90
    signal_weight: 0.4
    speed_weight: 0.3
    latency_weight: 0.3
  Ethernet:
    base_priority: 80
    reliability_weight: 1.0

# 场景化配置
scenarios:
  indoor:
    preferred_order: ["WiFi", "5G", "Ethernet"]
    wifi_boost: 10  # 室内WiFi优先级提升
  outdoor:
    preferred_order: ["5G", "WiFi", "Ethernet"]
    5g_boost: 15    # 室外5G优先级提升
  stationary:
    preferred_order: ["Ethernet", "WiFi", "5G"]
    ethernet_boost: 20  # 固定位置以太网优先
```

#### 动态切换示例

```bash
# 设置室内模式（WiFi优先）
ros2 param set /gen3_network_manager scenario "indoor"

# 设置室外模式（5G优先）
ros2 param set /gen3_network_manager scenario "outdoor"

# 设置固定模式（以太网优先）
ros2 param set /gen3_network_manager scenario "stationary"

# 恢复自适应模式
ros2 param set /gen3_network_manager scenario "adaptive"
```

## 🔧 NAT网络转发配置

### NAT功能概述

网络管理器支持自动NAT（网络地址转换）功能，使机器人能够作为网关设备，为内网设备提供外网访问能力。当WiFi和5G网络切换时，NAT规则会自动更新，确保内网设备能够无缝访问互联网。

### 配置参数

#### 基本NAT配置

```yaml
# NAT和内网卡配置
enable_nat: true                    # 启用NAT功能
lan_interfaces: ["eth0", "eth1"]    # 内网卡列表

# 网络接口配置
wifi_interface: "wlan0"             # WiFi外网接口
5g_interface: "eth2"                # 5G外网接口
ethernet_interface: "eth0"          # 以太网接口（通常作为内网卡）
```

#### 配置参数说明

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enable_nat` | boolean | `true` | 是否启用NAT转发功能 |
| `lan_interfaces` | array | `["eth0", "eth1"]` | 内网卡接口名称列表 |
| `wifi_interface` | string | `"wlan0"` | WiFi网络接口名称 |
| `5g_interface` | string | `"eth2"` | 5G网络接口名称 |
| `ethernet_interface` | string | `"eth0"` | 以太网接口名称 |

### NAT配置场景

#### 场景1：单内网卡环境

```yaml
# 单内网卡NAT配置
enable_nat: true
lan_interfaces: ["eth0"]  # 只有一个内网卡
```

#### 场景2：多内网卡环境

```yaml
# 多内网卡NAT配置
enable_nat: true
lan_interfaces: ["eth0", "eth1", "eth3", "eth4"]  # 多个内网卡
```

#### 场景3：禁用NAT功能

```yaml
# 禁用NAT配置
enable_nat: false
lan_interfaces: []  # 空列表
```

#### 场景4：虚拟机测试环境

```yaml
# 虚拟机NAT配置
enable_nat: true
lan_interfaces: ["enp0s3", "enp0s8"]  # 虚拟机网卡名称
wifi_interface: "enp0s9"
5g_interface: "enp0s10"
```

### 运行时NAT管理

#### 动态配置NAT参数

```bash
# 启用NAT功能
ros2 param set /gen3_network_manager enable_nat true

# 禁用NAT功能
ros2 param set /gen3_network_manager enable_nat false

# 修改内网卡列表
ros2 param set /gen3_network_manager lan_interfaces "['eth0', 'eth1']"

# 查看当前NAT配置
ros2 param get /gen3_network_manager enable_nat
ros2 param get /gen3_network_manager lan_interfaces
```

#### 验证NAT规则

```bash
# 检查NAT规则是否正确配置
sudo iptables -t nat -L POSTROUTING -n --line-numbers

# 检查FORWARD规则
sudo iptables -L FORWARD -n --line-numbers

# 检查IP转发状态
cat /proc/sys/net/ipv4/ip_forward

# 测试内网设备连通性
ping -I eth0 *******
ping -I eth1 *******
```

### NAT功能特性

#### 自动化管理
- **启动时配置**：系统启动时根据当前网络状态自动配置NAT规则
- **切换时更新**：WiFi和5G切换时自动更新NAT转发规则
- **状态变化响应**：网络接口变化时自动调整NAT配置
- **规则清理**：切换到非外网卡时自动清理NAT规则

#### iptables规则管理
- **MASQUERADE规则**：为外网接口配置源NAT，隐藏内网设备IP
- **FORWARD规则**：允许内网到外网的流量转发和返回流量
- **IP转发启用**：自动启用系统级IP转发功能
- **规则持久化**：确保NAT规则在系统重启后保持有效

#### 企业级特性
- **多内网卡支持**：支持配置多个内网接口
- **权限管理**：需要root权限执行iptables操作
- **日志记录**：详细记录NAT规则配置过程和结果
- **容错处理**：NAT配置失败不影响网络切换主流程

## 🔧 网络质量检查优化功能

### DNS解析时间真实测量

#### 功能概述
系统现在支持真实的DNS解析时间测量，替代了原有的固定时间值，提供更准确的网络质量评估。

#### 技术实现
```cpp
// DNS解析时间测量接口
std::tuple<bool, double> NetworkUtils::test_dns_resolution_with_time(
    const std::vector<std::string>& domains, int timeout_ms);
```

#### 测试域名列表
```cpp
std::vector<std::string> dns_test_domains = {
    "www.baidu.com",
    "www.qq.com",
    "www.163.com",
    "www.sina.com.cn"
};
```

#### 使用示例
```bash
# 查看DNS解析时间（通过日志）
ros2 topic echo /rosout | grep "DNS解析时间"

# 示例输出：DNS解析时间: 45.2ms
```

### 5G信号强度异常值修复

#### 功能概述
系统现在具备多层验证机制，自动修复5G信号强度异常值，确保显示合理的信号强度数据。

#### 验证机制
1. **数据源验证**：检查RSRP、RSCP、RSSI的有效性
2. **边界检查**：信号强度必须在-150dBm到-30dBm范围内
3. **默认值设置**：异常时使用-100dBm作为默认值

#### 信号强度选择优先级
```cpp
// 优先级：RSRP > RSCP > RSSI
if (cell_info.rsrp != -999 && is_valid_signal(cell_info.rsrp)) {
    primary_signal = cell_info.rsrp;
} else if (cell_info.rscp != -999 && is_valid_signal(cell_info.rscp)) {
    primary_signal = cell_info.rscp;
} else if (cell_info.rssi != -999 && is_valid_signal(cell_info.rssi)) {
    primary_signal = cell_info.rssi;
}
```

#### 调试信息
```bash
# 查看5G信号强度修复日志
ros2 topic echo /rosout | grep "5G信号强度"

# 示例输出：
# [DEBUG] 5G信号强度选择: RSRP=-85dBm (有效)
# [DEBUG] 5G信号强度最终值: -85dBm
```

### 网络质量检查简化

#### 功能概述
系统简化了网络质量检查流程，删除了冗余的测试项目，提升了检查速度和系统性能。

#### 简化内容
- **删除项目**：网关可达性测试、互联网可访问性测试
- **保留项目**：DNS解析测试、信号强度检查、延迟测试
- **权重调整**：DNS解析权重从30%提升到60%

#### 评分逻辑
```cpp
// 简化后的评分逻辑
float score = 0.0;
if (quality.dns_working) {
    score += 60.0;  // DNS解析权重提升
}
if (quality.signal_strength > -70) {
    score += 20.0;  // 信号强度评分
}
// 其他评分项目...
```

#### 性能提升
- **检查速度**：提升约30%
- **系统负载**：降低并发网络连接数量
- **响应时间**：早期失败检测机制

### 早期失败检测

#### 功能概述
当检测到无效的网络状态（如信号强度为0）时，系统会立即返回失败结果，避免进行无意义的后续测试。

#### 实现机制
```cpp
// WiFi信号强度零值检查
if (quality.signal_strength == 0) {
    RCLCPP_WARN(logger_, "WiFi信号强度为0，直接返回失败结果");
    return create_failed_quality_result();
}

// 5G信号强度零值检查
if (quality.signal_strength == 0) {
    RCLCPP_WARN(logger_, "5G信号强度为0，直接返回失败结果");
    return create_failed_quality_result();
}
```

#### 效果
- 快速识别不可用网络
- 减少系统资源消耗
- 提高网络质量检查效率

### 监控和调试

```bash
# 查看当前网络优先级配置
ros2 param get /gen3_network_manager preferred_network_type
ros2 param get /gen3_network_manager auto_switch_enabled

# 监控网络切换日志
ros2 topic echo /switch_status

# 查看网络质量评分
ros2 topic echo /network_quality

# 查看所有已保存的WiFi网络及其优先级
ros2 service call /get_network_list gen3_network_interfaces/srv/GetNetworkList "{list_type: 2, include_connection_history: true}"
```

## �🔍 调试命令

### 查看系统状态

```bash
# 查看网络管理节点
ros2 node list | grep gen3_network_manager

# 查看节点信息
ros2 node info /gen3_network_manager

# 查看所有话题
ros2 topic list | grep -E "(network|wifi|binding|dns)"

# 查看所有服务
ros2 service list | grep -E "(network|wifi|binding)"

# 查看所有动作
ros2 action list | grep binding
```

### 监控系统运行

```bash
# 监控网络状态变化
ros2 topic echo /network_status --once

# 监控网络质量
ros2 topic echo /network_quality --once

# 监控绑定状态
ros2 topic echo /binding_status --once
```

## � 网络监控器详细功能

### 核心监控功能

NetworkMonitor是系统的核心监控组件，提供以下功能：

#### 1. 网络状态监控

- **实时状态跟踪**: 监控网络接口状态变化
- **连接统计**: 记录连接/断开次数、在线时间百分比
- **状态广播**: 定期广播当前网络状态给其他组件

#### 2. 连通性检查 (每10秒执行)

- **网关可达性**: 使用ping和路由检查验证网关连通性
- **DNS解析测试**: 测试DNS服务器是否正常工作
- **互联网访问**: 验证是否能访问外部服务器(*******)
- **综合判断**: 基于多项检查结果判断网络连通性

#### 3. WiFi路由完整性检查

- **路由异常检测**: 检测WiFi已连接但缺少默认路由的异常情况
- **自动修复**: 通过重新连接WiFi来修复路由问题
- **状态恢复**: 保存和恢复静态路由配置

#### 4. 板间通信监控（待定）

- **通信状态检查**: 监控与其他板卡的通信状态
- **状态发布**: 发布板间通信状态信息

### 发布的状态消息类型

- `CONNECTED` / `DISCONNECTED` - 基本连接状态
- `INTERNET_UNREACHABLE` - 互联网不可访问
- `CONNECTIVITY_LOST` - 网络连通性丢失
- `BOARD_COMMUNICATION_OK` / `BOARD_COMMUNICATION_LOST` - 板间通信状态

## 🛠️ 网络工具类功能

### NetworkUtils核心功能

NetworkUtils提供了底层网络操作的封装，包括：

#### 1. 网络接口管理

- **接口信息获取**: 获取网络接口的IP、MAC、网关等信息
- **接口状态控制**: 启用/禁用网络接口
- **路由管理**: 设置默认路由、获取路由信息

#### 2. WiFi网络管理

- **WiFi扫描**: 扫描可用WiFi网络，支持信号强度和安全类型检测
- **WiFi连接**: 连接到指定WiFi网络，支持多种加密类型
- **WiFi状态**: 获取当前WiFi连接状态和详细信息
- **BSSID处理**: 正确处理nmcli输出中转义的MAC地址格式

#### 3. 网络质量测试

- **连通性测试**: ping测试网关和外部服务器
- **DNS解析测试**: 验证DNS服务器功能
- **延迟测试**: 测量网络延迟和抖动
- **速度测试**: 测量下载和上传速度

#### 4. 系统命令执行

- **安全命令执行**: 封装系统命令调用，处理错误和超时
- **输出解析**: 解析各种网络工具的输出格式


##  性能指标

### 话题发布频率

- **网络状态**: 1Hz (实时状态监控)
- **网络状态广播**: 事件触发 (状态变化时发布)
- **网络质量**: 0.2Hz (5秒检测一次)
- **绑定状态**: 0.5Hz (2秒更新一次)
- **连接状态**: 事件触发 (连接状态变化时发布)
- **板间通信状态**: 事件触发 (通信状态变化时发布)

### 监控检查间隔

- **连通性检查**: 10秒 (可配置参数 `connectivity_check_interval`)
- **WiFi路由检查**: 立即执行 + 事件触发
- **网络状态更新**: 实时响应网络变化

### 服务响应时间

- **网络切换**: 5-30秒 (取决于网络类型)
- **WiFi连接**: 10-30秒 (取决于信号强度)
- **网络列表**: 1-5秒 (取决于是否需要扫描)
- **优先级设置**: <1秒 (配置更新)
- **绑定启动**: <1秒 (会话初始化)

### 动作执行时间

- **设备绑定**: 30-300秒 (取决于绑定方式和网络条件)
- **WiFi路由修复**: 10-20秒 (重新连接WiFi)

---

**文档版本**: v1.4
**最后更新**: 2025年1月
**更新内容**: 新增NAT网络转发功能配置和接口说明
**适用系统**: Gen3 Network Manager v1.3+

## 📝 更新日志

### v1.3 (2025-07-08)
- **新增网络优先级配置示例**: 添加5G优先和WiFi优先的详细配置方法
- 提供三种配置方式：直接切换、配置文件设置、运行时参数设置
- 添加混合优先级配置和场景化网络选择示例
- 完善网络切换命令，包含5G、WiFi、以太网的切换示例
- 添加监控和调试命令，便于网络优先级配置的验证和调试

### v1.2 (2025-07-08)
- **重大架构更新**: 修正系统架构描述，从多节点架构更新为单节点多组件架构
- 更新所有ROS2接口的发布者/订阅者/服务器信息，反映真实的单节点实现
- 添加系统启动章节，包含launch文件和直接运行方式
- 更新调试命令，使用正确的节点名称 `gen3_network_manager`
- 完善架构特点说明：单进程设计、模块化组织、统一接口

### v1.1 (2025-07-08)
- 更新网络监控器详细功能描述
- 添加WiFi路由完整性检查功能说明
- 修复BSSID处理逻辑的文档说明
- 更新话题发布频率和监控间隔信息
- 完善连通性检查机制描述

### v1.4 (2025-07-27)
- 新增NAT网络转发功能配置和接口说明
- 新增DNS解析时间真实测量功能
- 新增5G信号强度异常值修复功能
- 新增网络质量检查简化功能
- 新增早期失败检测机制
- 更新IptablesUtils工具类接口
- 完善配置参数说明和使用示例

### v1.3 (2025-07-08)
- 更新网络切换逻辑
- 完善监控和调试功能

### v1.0 (2025-07-06)
- 初始版本发布
- 完整的ROS2接口定义
- 基础功能文档
