# API 接口文档

本目录包含 ROS2 网络管理器的 API 接口和通信协议文档。

## 📋 文档列表

### [ROS接口文档.md](ROS接口文档.md)
**ROS2 接口完整文档**
- 系统架构说明
- 消息类型定义 (Messages)
- 服务类型定义 (Services)
- 动作类型定义 (Actions)
- 话题列表和使用方法
- 完整的使用示例
- 适用场景：开发者、集成人员

## 🔧 接口概览

### 核心接口类型
- **话题 (Topics)**: 网络状态、质量信息、绑定状态
- **服务 (Services)**: 网络切换、WiFi连接、配置管理
- **动作 (Actions)**: 设备绑定、长时间操作

### 主要功能
- 网络状态监控和广播
- 自动网络切换控制
- WiFi 网络管理
- 设备绑定和配网
- 网络质量检测

## 🚀 快速开始

### 查看系统状态
```bash
# 检查网络状态
ros2 topic echo /network_status --once

# 查看可用服务
ros2 service list | grep network

# 监控网络质量
ros2 topic echo /network_quality
```

### 基本操作
```bash
# 切换到 WiFi
ros2 service call /switch_network gen3_network_interfaces/srv/SwitchNetwork \
  "{target_network_type: 1, target_wifi_ssid: 'MyWiFi', force_switch: false, timeout_seconds: 30}"

# 获取网络列表
ros2 service call /get_network_list gen3_network_interfaces/srv/GetNetworkList \
  "{list_type: 1, include_signal_strength: true, force_scan: true}"
```

## 🔗 相关文档

- [功能特性](../features/) - 系统功能详细说明
- [故障排除](../troubleshooting/) - 接口调用问题解决
- [需求分析](../requirements/) - 系统设计背景

## 📝 使用建议

1. **接口开发**: 详细阅读 ROS2 接口文档了解所有可用接口
2. **系统集成**: 参考使用示例进行接口调用
3. **问题调试**: 结合故障排除文档解决接口问题

---

**返回**: [文档索引](../README.md) | [项目主页](../../README.md)
