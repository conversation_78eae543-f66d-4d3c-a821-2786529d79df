# launch.json 配置备份管理

## 📋 概述

为了确保 VSCode 调试配置的安全性和可恢复性，项目提供了完整的 launch.json 备份管理系统。系统自动维护最多 3 个备份文件，防止配置丢失。

## 🛠️ 备份管理工具

### 1. 自动备份脚本
**文件**: `scripts/backup_launch_json.sh`
**功能**: 创建带时间戳的备份文件，自动清理多余备份

```bash
# 创建备份
./scripts/backup_launch_json.sh
```

**特性**:
- ✅ 自动生成时间戳备份文件
- ✅ 自动清理超过 3 个的旧备份
- ✅ 显示备份文件信息和统计
- ✅ 提供恢复命令提示

### 2. 备份清理脚本
**文件**: `scripts/manage_launch_backups.sh`
**功能**: 管理现有备份文件，确保数量不超过限制

```bash
# 清理多余备份
./scripts/manage_launch_backups.sh
```

**特性**:
- ✅ 显示所有备份文件列表
- ✅ 交互式删除确认
- ✅ 保留最新的 3 个备份
- ✅ 提供使用建议

### 3. 备份恢复脚本
**文件**: `scripts/restore_launch_json.sh`
**功能**: 从备份文件恢复 launch.json 配置

```bash
# 交互式恢复
./scripts/restore_launch_json.sh

# 恢复指定备份
./scripts/restore_launch_json.sh 20250719_214551
```

**特性**:
- ✅ 交互式备份文件选择
- ✅ 显示文件差异预览
- ✅ 恢复前自动备份当前文件
- ✅ 支持命令行参数指定备份

### 4. 架构配置更新脚本
**文件**: `scripts/update_launch_config.sh`
**功能**: 更新架构路径时自动创建备份

```bash
# 更新架构配置
./scripts/update_launch_config.sh
```

**特性**:
- ✅ 更新前自动备份
- ✅ 自动检测系统架构
- ✅ 批量更新配置路径
- ✅ 验证更新结果

## 📁 备份文件命名规则

### 文件名格式
```
launch.json.backup.YYYYMMDD_HHMMSS
```

### 示例
```
launch.json.backup.20250719_214551  # 2025年7月19日 21:45:51
launch.json.backup.20250719_213849  # 2025年7月19日 21:38:49
launch.json.backup.20250719_212724  # 2025年7月19日 21:27:24
```

### 特殊备份
```
launch.json.backup.before_restore_YYYYMMDD_HHMMSS  # 恢复前备份
```

## 🔄 使用工作流

### 日常开发流程
1. **修改前备份**:
   ```bash
   ./scripts/backup_launch_json.sh
   ```

2. **修改配置**: 编辑 `.vscode/launch.json`

3. **测试配置**: 验证调试配置是否正常

4. **如需恢复**:
   ```bash
   ./scripts/restore_launch_json.sh
   ```

### 架构更新流程
1. **自动备份和更新**:
   ```bash
   ./scripts/update_launch_config.sh
   ```

2. **验证配置**: 测试新架构下的调试功能

3. **如有问题恢复**:
   ```bash
   ./scripts/restore_launch_json.sh
   ```

### 定期维护
```bash
# 清理多余备份（建议每周运行）
./scripts/manage_launch_backups.sh
```

## 📊 备份策略

### 自动备份触发条件
- ✅ 运行 `backup_launch_json.sh` 脚本
- ✅ 运行 `update_launch_config.sh` 脚本
- ✅ 运行 `restore_launch_json.sh` 恢复前

### 备份保留策略
- **最大数量**: 3 个备份文件
- **清理规则**: 保留最新的 3 个，删除更旧的备份
- **命名规则**: 时间戳确保唯一性和排序

### 备份内容
- **完整配置**: 整个 launch.json 文件
- **元数据**: 文件大小、创建时间
- **差异检测**: 恢复时显示与当前文件的差异

## 🔍 故障排除

### 常见问题

#### 1. 备份文件过多
**现象**: `.vscode` 目录中有大量备份文件
**解决**: 运行清理脚本
```bash
./scripts/manage_launch_backups.sh
```

#### 2. 恢复后配置无效
**现象**: 恢复备份后调试功能不正常
**解决**: 
1. 检查架构路径是否正确
2. 重新运行架构更新脚本
3. 重新加载 VSCode

#### 3. 备份脚本权限错误
**现象**: 脚本无法执行
**解决**: 添加执行权限
```bash
chmod +x scripts/*.sh
```

#### 4. 找不到备份文件
**现象**: 恢复时提示没有备份文件
**解决**: 
1. 检查 `.vscode` 目录
2. 手动创建备份
3. 确认文件命名格式

### 手动备份命令
```bash
# 创建手动备份
cp .vscode/launch.json .vscode/launch.json.backup.$(date +%Y%m%d_%H%M%S)

# 查看所有备份
ls -la .vscode/launch.json.backup.*

# 手动恢复
cp .vscode/launch.json.backup.YYYYMMDD_HHMMSS .vscode/launch.json
```

## 💡 最佳实践

### 开发建议
1. **修改前备份**: 重要修改前总是先备份
2. **测试验证**: 修改后及时测试调试功能
3. **定期清理**: 定期清理多余的备份文件
4. **文档记录**: 重要配置变更记录原因

### 团队协作
1. **统一工具**: 团队成员使用相同的备份脚本
2. **配置同步**: 重要配置变更及时同步
3. **备份共享**: 关键备份可以提交到版本控制
4. **培训指导**: 新成员了解备份管理流程

### 安全考虑
1. **敏感信息**: 备份文件不包含敏感信息
2. **权限控制**: 备份文件权限设置合理
3. **定期检查**: 定期检查备份文件完整性
4. **版本控制**: 重要备份纳入版本控制管理

## 🔗 相关文档

- [调试配置指南](调试配置指南.md) - 完整的调试环境设置
- [架构配置说明](架构配置说明.md) - 多架构支持说明
- [故障排除](troubleshooting/) - 调试问题解决方案

---

**返回**: [文档索引](README.md) | [项目主页](../README.md)
