#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ROS2 环境设置
function setup_ros2_environment() {
    echo -e "${BLUE}=== 设置 ROS2 环境 ===${NC}"

    # 检查是否已经设置了 ROS2 环境
    if [ -n "$ROS_DISTRO" ] && command -v colcon >/dev/null 2>&1; then
        echo -e "${GREEN}ROS2 环境已设置: $ROS_DISTRO${NC}"
        # 验证 rosidl_adapter 是否可用
        if python3 -c "import rosidl_adapter" 2>/dev/null; then
            echo -e "${GREEN}✓ rosidl_adapter 模块可用${NC}"
            return 0
        else
            echo -e "${YELLOW}警告: rosidl_adapter 模块不可用，尝试重新设置环境${NC}"
        fi
    fi

    # 常见的 ROS2 安装路径
    ROS2_PATHS=(
        "/opt/ros/humble"
        "/opt/ros/galactic"
        "/opt/ros/foxy"
        "/opt/ros/iron"
        "/opt/ros/jazzy"
        "/opt/ros/rolling"
    )

    # 尝试自动检测并设置 ROS2 环境
    for ros_dir in "${ROS2_PATHS[@]}"; do
        if [ -d "$ros_dir" ] && [ -f "$ros_dir/setup.bash" ]; then
            echo -e "${YELLOW}找到 ROS2 安装: $ros_dir${NC}"

            # 使用 source 命令设置环境（更可靠的方式）
            if source "$ros_dir/setup.bash" 2>/dev/null; then
                echo -e "${GREEN}成功 source ROS2 环境: $(basename "$ros_dir")${NC}"

                # 验证关键组件
                if command -v colcon >/dev/null 2>&1; then
                    echo -e "${GREEN}✓ colcon 命令可用${NC}"
                else
                    echo -e "${RED}✗ colcon 命令不可用${NC}"
                    continue
                fi

                if python3 -c "import rosidl_adapter" 2>/dev/null; then
                    echo -e "${GREEN}✓ rosidl_adapter 模块可用${NC}"
                    export ROS2_SETUP_PATH="$ros_dir/setup.bash"
                    return 0
                else
                    echo -e "${RED}✗ rosidl_adapter 模块不可用${NC}"
                    continue
                fi
            else
                echo -e "${RED}无法 source $ros_dir/setup.bash${NC}"
                continue
            fi
        fi
    done

    # 如果没有找到，提示用户
    echo -e "${RED}错误: 未找到可用的 ROS2 环境或环境设置失败${NC}"
    echo -e "${YELLOW}请确保已安装 ROS2 并手动 source 环境:${NC}"
    echo -e "  ${GREEN}source /opt/ros/<distro>/setup.bash${NC}"
    echo -e "${YELLOW}或者使用 --skip-ros-setup 选项跳过自动设置${NC}"
    echo -e "${YELLOW}尝试手动运行: source /opt/ros/humble/setup.bash${NC}"
    exit 1
}

# 默认参数
BUILD_TYPE="Debug"  # 默认Debug模式
PACKAGES=""
ARCH=$(uname -m)
CLEAN=false
CLANGD=true  # 默认启用clangd
VERBOSE=false
INSTALL=true
UPDATE_DEPS=false
JOBS=$(nproc)
SKIP_ROS_SETUP=false  # 是否跳过 ROS2 环境设置

# 帮助信息
function show_help {
    echo -e "${BLUE}ROS2 编译脚本${NC}"
    echo "用法: $0 [选项] [包名1 包名2 ...]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -r, --release       使用Release模式编译 (默认: Debug)"
    echo "  -c, --clean         清理构建目录后重新编译"
    echo "  --no-clangd         不生成compile_commands.json"
    echo "  -v, --verbose       显示详细编译信息"
    echo "  --no-install        仅编译不安装"
    echo "  -u, --update-deps   更新rosdep依赖"
    echo "  -j, --jobs N        使用N个并行任务编译 (默认: $(nproc))"
    echo "  --skip-ros-setup    跳过 ROS2 环境自动设置"
    echo ""
    echo "如果不指定包名，将编译所有包"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -h|--help)
            show_help
            exit 0
            ;;
        -r|--release)
            BUILD_TYPE="Release"
            shift
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        --no-clangd)
            CLANGD=false
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --no-install)
            INSTALL=false
            shift
            ;;
        -u|--update-deps)
            UPDATE_DEPS=true
            shift
            ;;
        -j|--jobs)
            JOBS="$2"
            shift 2
            ;;
        --skip-ros-setup)
            SKIP_ROS_SETUP=true
            shift
            ;;
        *)
            # 剩余参数视为包名
            PACKAGES="$PACKAGES $1"
            shift
            ;;
    esac
done

# 设置 ROS2 环境（如果未跳过）
if [ "$SKIP_ROS_SETUP" = false ]; then
    setup_ros2_environment
fi

# 设置构建和安装目录
WORKSPACE_DIR=$(pwd)
BUILD_DIR="${WORKSPACE_DIR}/build/${ARCH}/${BUILD_TYPE}"
INSTALL_DIR="${WORKSPACE_DIR}/install/${ARCH}/${BUILD_TYPE}"

# 显示配置信息
echo -e "${BLUE}=== 编译配置 ===${NC}"
echo -e "ROS2 发行版: ${GREEN}${ROS_DISTRO:-未设置}${NC}"
echo -e "工作目录: ${GREEN}${WORKSPACE_DIR}${NC}"
echo -e "构建类型: ${GREEN}${BUILD_TYPE}${NC}"
echo -e "架构: ${GREEN}${ARCH}${NC}"
echo -e "构建目录: ${GREEN}${BUILD_DIR}${NC}"
echo -e "安装目录: ${GREEN}${INSTALL_DIR}${NC}"
echo -e "clangd支持: ${GREEN}$([ "$CLANGD" = true ] && echo "启用" || echo "禁用")${NC}"
if [ -n "$PACKAGES" ]; then
    echo -e "编译包: ${GREEN}${PACKAGES}${NC}"
else
    echo -e "编译包: ${GREEN}所有包${NC}"
fi
echo -e "并行任务: ${GREEN}${JOBS}${NC}"
echo ""

# 更新rosdep依赖
if [ "$UPDATE_DEPS" = true ]; then
    echo -e "${BLUE}=== 更新依赖 ===${NC}"
    rosdep update
    rosdep install --from-paths src --ignore-src -y
    if [ $? -ne 0 ]; then
        echo -e "${RED}依赖更新失败${NC}"
        exit 1
    fi
fi

# 清理构建目录
if [ "$CLEAN" = true ]; then
    echo -e "${BLUE}=== 清理构建目录 ===${NC}"
    rm -rf ${BUILD_DIR}
    rm -rf ${INSTALL_DIR}
fi

# 创建构建目录
mkdir -p ${BUILD_DIR}
mkdir -p ${INSTALL_DIR}

# 构建命令
BUILD_CMD="colcon build"
BUILD_CMD+=" --build-base ${BUILD_DIR}"
BUILD_CMD+=" --install-base ${INSTALL_DIR}"
BUILD_CMD+=" --cmake-args -DCMAKE_BUILD_TYPE=${BUILD_TYPE}"

# 如果指定了包名，则只构建指定的包
if [ -n "$PACKAGES" ]; then
    BUILD_CMD+=" --packages-select ${PACKAGES}"
fi

# 是否生成compile_commands.json
if [ "$CLANGD" = true ]; then
    BUILD_CMD+=" -DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
    echo -e "${YELLOW}将生成compile_commands.json供clangd使用${NC}"
fi

# 是否显示详细信息
if [ "$VERBOSE" = true ]; then
    BUILD_CMD+=" --event-handlers console_direct+"
fi

# 是否安装
if [ "$INSTALL" = false ]; then
    BUILD_CMD+=" --cmake-target build"
fi

# 设置并行任务数
BUILD_CMD+=" --parallel-workers ${JOBS}"

# 执行构建
echo -e "${BLUE}=== 开始构建 ===${NC}"
echo -e "执行命令: ${GREEN}${BUILD_CMD}${NC}"

# 确保在构建前 ROS2 环境正确设置
if [ -n "$ROS2_SETUP_PATH" ]; then
    echo -e "${BLUE}重新确认 ROS2 环境设置...${NC}"
    source "$ROS2_SETUP_PATH"
    echo -e "${GREEN}ROS2 环境已重新加载: $ROS_DISTRO${NC}"
elif [ -n "$ROS_DISTRO" ]; then
    echo -e "${GREEN}使用当前 ROS2 环境: $ROS_DISTRO${NC}"
else
    echo -e "${RED}警告: ROS2 环境未设置，构建可能失败${NC}"
fi

# 显示关键环境变量用于调试
echo -e "${BLUE}环境变量检查:${NC}"
echo -e "  ROS_DISTRO: ${GREEN}${ROS_DISTRO:-未设置}${NC}"
echo -e "  PYTHONPATH: ${GREEN}${PYTHONPATH:-未设置}${NC}"
echo -e "  AMENT_PREFIX_PATH: ${GREEN}${AMENT_PREFIX_PATH:-未设置}${NC}"

eval ${BUILD_CMD}

# 检查构建结果并更新VSCode配置
if [ $? -eq 0 ]; then
    echo -e "${GREEN}构建成功!${NC}"

    # 如果启用了clangd，创建compile_commands.json的软链接到工作空间根目录
    if [ "$CLANGD" = true ]; then
        echo -e "${BLUE}=== 设置clangd ===${NC}"
        COMPILE_COMMANDS_PATH="${BUILD_DIR}/compile_commands.json"
        if [ -f "$COMPILE_COMMANDS_PATH" ]; then
            ln -sf "./build/${ARCH}/${BUILD_TYPE}/compile_commands.json" "${WORKSPACE_DIR}/compile_commands.json"
            echo -e "${GREEN}已创建compile_commands.json软链接供clangd使用${NC}"
        else
            echo -e "${YELLOW}警告: 未找到compile_commands.json${NC}"
        fi
    fi

    # 更新VSCode launch.json中的架构路径
    if [ -f ".vscode/launch.json" ]; then
        echo -e "${BLUE}=== 更新VSCode配置 ===${NC}"
        # 备份原始文件
        cp .vscode/launch.json .vscode/launch.json.backup.$(date +%Y%m%d_%H%M%S)

        # 更新架构路径
        sed -i "s|/install/[^/]*/Debug|/install/$ARCH/Debug|g" .vscode/launch.json
        sed -i "s|/install/[^/]*/Release|/install/$ARCH/Release|g" .vscode/launch.json

        echo -e "${GREEN}已更新launch.json中的架构路径为: $ARCH${NC}"
    fi
else
    echo -e "${RED}构建失败!${NC}"
    exit 1
fi

# # 检查构建结果
# if [ $? -eq 0 ]; then
#     echo -e "${GREEN}构建成功!${NC}"
    
#     # 如果启用了clangd，创建compile_commands.json的软链接到工作空间根目录
#     if [ "$CLANGD" = true ]; then
#         echo -e "${BLUE}=== 设置clangd ===${NC}"
#         COMPILE_COMMANDS_PATH=$(find ${BUILD_DIR} -name "compile_commands.json" | head -n 1)
#         if [ -n "$COMPILE_COMMANDS_PATH" ]; then
#             ln -sf ${COMPILE_COMMANDS_PATH} ${WORKSPACE_DIR}/compile_commands.json
#             echo -e "${GREEN}已创建compile_commands.json软链接${NC}"
#         else
#             echo -e "${YELLOW}警告: 未找到compile_commands.json${NC}"
#         fi
#     fi
    
#     # 创建setup脚本的软链接到工作空间根目录，方便使用
#     echo -e "${BLUE}=== 创建环境设置脚本 ===${NC}"
#     for setup_file in setup.bash setup.sh setup.zsh local_setup.bash local_setup.sh local_setup.zsh; do
#         if [ -f "${INSTALL_DIR}/${setup_file}" ]; then
#             ln -sf ${INSTALL_DIR}/${setup_file} ${WORKSPACE_DIR}/${setup_file}
#         fi
#     done
#     echo -e "${GREEN}已创建环境设置脚本软链接${NC}"
    
#     # 创建或更新VSCode配置
#     VSCODE_DIR="${WORKSPACE_DIR}/.vscode"
#     mkdir -p ${VSCODE_DIR}
    
#     # 创建launch.json文件
#     echo -e "${BLUE}=== 创建/更新VSCode调试配置 ===${NC}"
#     cat > ${VSCODE_DIR}/launch.json << EOF
# {
#     "version": "0.2.0",
#     "configurations": [
#         {
#             "name": "ROS2 C++ Debug",
#             "type": "cppdbg",
#             "request": "launch",
#             "program": "\${command:cmake.launchTargetPath}",
#             "args": [],
#             "stopAtEntry": false,
#             "cwd": "\${workspaceFolder}",
#             "environment": [
#                 {
#                     "name": "LD_LIBRARY_PATH",
#                     "value": "\${workspaceFolder}/install/${ARCH}/${BUILD_TYPE}/lib:\${env:LD_LIBRARY_PATH}"
#                 },
#                 {
#                     "name": "AMENT_PREFIX_PATH",
#                     "value": "\${workspaceFolder}/install/${ARCH}/${BUILD_TYPE}:\${env:AMENT_PREFIX_PATH}"
#                 }
#             ],
#             "externalConsole": false,
#             "MIMode": "gdb",
#             "setupCommands": [
#                 {
#                     "description": "为 gdb 启用整齐打印",
#                     "text": "-enable-pretty-printing",
#                     "ignoreFailures": true
#                 },
#                 {
#                     "description": "设置反汇编风格为 Intel",
#                     "text": "-gdb-set disassembly-flavor intel",
#                     "ignoreFailures": true
#                 }
#             ],
#             "preLaunchTask": "colcon build",
#             "miDebuggerPath": "/usr/bin/gdb"
#         }
#     ]
# }
# EOF
    
#     # 创建tasks.json文件
#     cat > ${VSCODE_DIR}/tasks.json << EOF
# {
#     "version": "2.0.0",
#     "tasks": [
#         {
#             "label": "colcon build",
#             "type": "shell",
#             "command": "./build.sh",
#             "args": [
#                 "\${input:package}"
#             ],
#             "group": {
#                 "kind": "build",
#                 "isDefault": true
#             },
#             "presentation": {
#                 "reveal": "always",
#                 "panel": "new"
#             },
#             "problemMatcher": [
#                 "\$gcc"
#             ]
#         }
#     ],
#     "inputs": [
#         {
#             "id": "package",
#             "type": "promptString",
#             "description": "要编译的包名 (留空编译所有包)",
#             "default": ""
#         }
#     ]
# }
# EOF

#     # 创建c_cpp_properties.json文件
#     if [ "$CLANGD" = true ]; then
#         cat > ${VSCODE_DIR}/c_cpp_properties.json << EOF
# {
#     "configurations": [
#         {
#             "name": "Linux",
#             "includePath": [
#                 "\${workspaceFolder}/**",
#                 "/opt/ros/humble/include/**"
#             ],
#             "defines": [],
#             "compilerPath": "/usr/bin/gcc",
#             "cStandard": "gnu17",
#             "cppStandard": "gnu++17",
#             "intelliSenseMode": "linux-gcc-x64",
#             "compileCommands": "\${workspaceFolder}/compile_commands.json"
#         }
#     ],
#     "version": 4
# }
# EOF
#     fi
    
#     echo -e "${GREEN}VSCode配置文件已创建/更新${NC}"
    
#     # 显示如何使用编译结果
#     echo -e "${BLUE}=== 使用说明 ===${NC}"
#     echo -e "要使用编译结果，请运行: ${GREEN}source ${WORKSPACE_DIR}/setup.bash${NC}"
#     if [ "$CLANGD" = true ]; then
#         echo -e "clangd配置已就绪，可在IDE中使用代码补全和导航功能"
#     fi
    
#     exit 0
# else
#     echo -e "${RED}构建失败!${NC}"
#     exit 1
# fi 