# 网络管理器变更日志

## [v2.0.1] - 2025-07-30

### 🚀 功能新增

#### systemd服务支持
- **完整的systemd服务集成** (8f4e9d1)
  - 新增systemd服务配置文件，支持系统级自动启动
  - 实现服务安装、卸载、启动、停止和重启的管理脚本
  - 支持将网络管理器部署到/opt目录，符合Linux系统规范

#### WiFi网络管理增强
- **WiFi自动切换连接优化** (2b7c4d5)
  - 改进WiFi优先级评估算法，更准确地选择最佳网络
  - 优化WiFi信号强度阈值判断，避免频繁切换
  - 新增连接失败自动重试机制，提高连接成功率
  - 实现WiFi连接状态实时监控，快速响应网络变化

### 🔧 系统优化改进

#### 日志配置优化
- **日志级别动态配置** (7e8a9f2)
  - 优化日志级别配置机制，支持通过配置文件动态调整
  - 修复systemd服务中日志级别设置不生效的问题
  - 确保debug级别日志正确显示，便于问题排查

#### 启动脚本优化
- **服务启动机制改进** (3d4c5e6)
  - 优化systemd服务启动脚本，直接运行可执行文件而非通过launch文件
  - 添加进程检查和清理功能，确保服务重启时不存在冲突进程
  - 改进文件复制方法，解决"Text file busy"错误

#### 部署流程优化
- **安装部署流程简化** (5e2f7a8)
  - 优化文件部署逻辑，支持增量更新
  - 添加配置文件和launch文件的自动部署
  - 实现可执行文件和库文件的智能复制，避免文件占用错误

#### 网络切换逻辑优化
- **网络切换稳定性提升** (4f6e7d8)
  - 优化网络切换决策逻辑，减少不必要的切换操作
  - 增加网络切换冷却时间，避免频繁切换导致的连接不稳定
  - 改进网络质量评分算法，更加平滑地处理网络波动
  - 新增网络切换前的连接性预检测，减少切换失败率

### 🛠️ 工具改进

#### 日志管理工具
- **日志级别更新工具** (9c4d2e3)
  - 新增日志级别快速更新脚本，支持无需重启服务更新日志级别
  - 自动检测开发环境和部署环境配置文件位置
  - 提供友好的命令行界面，便于操作和查询

## [v2.0.0] - 2025-07-27

### 🎉 重大功能新增

#### NAT网络转发功能
- **新增完整的NAT规则自动管理系统** (da33d3f, 18ea5ce, be3da0e)
  - 新增IptablesUtils工具类，支持iptables规则自动配置
  - 支持启动时根据当前网络状态自动配置NAT规则
  - 支持网络切换时动态更新NAT规则
  - 支持网络接口变化时自动调整NAT配置
  - 新增enable_nat和lan_interfaces配置参数

#### 网络质量检查优化
- **DNS解析时间真实测量** (0c81a07)
  - 新增多域名DNS解析时间测量接口
  - 替代固定DNS解析时间值，提供更准确的网络质量评估

- **5G信号强度异常值修复** (d237342)
  - 优化蜂窝网络信号强度监测逻辑
  - 改进不同制式(RSSI/RSRP/RSCP)信号选择策略
  - 增加有效性校验和异常信号值默认处理机制

- **网络质量检查简化** (9a5d40d)
  - 重构网络连通性检测逻辑，精简网络质量消息结构
  - 移除冗余的网关可达性与互联网可访问性字段
  - 聚焦DNS解析状态作为核心连通性指标

- **早期失败检测机制** (9056f2e)
  - 增加WiFi与5G信号强度为0时的失败快速返回机制
  - 提升异常信号场景处理效率

### 🔧 系统优化改进

#### 网络信息获取优化
- **WiFi信息获取逻辑优化** (8ce934d, a3ab204)
  - 改用iw命令替代nmcli作为主要获取方式
  - 增强WiFi连接信息解析能力
  - 改用iw link直接解析信号强度并支持dBm单位输出

- **5G网络功能增强** (06ee431, 4ab20ea)
  - 新增蜂窝网络(5G)模块支持
  - 实现CellularManager初始化与异常容错处理
  - 新增5G网络专项质量监控与信息展示

#### 通信模块优化
- **串口通信增强** (cee596a, 41e1884)
  - 新增USB AT通信备用方案
  - 串口通信失败时自动切换至USB设备通信
  - 实现双通道冗余保障

- **AT指令功能扩展** (aee8f1d, 1d21db9, 6733153)
  - 新增网络搜索偏好配置功能
  - 新增USB拨号模式监控与自动切换逻辑
  - 升级信号强度监控逻辑，改用服务小区信息接口

### 📊 系统改进

#### 网络环境检测优化
- **国内网络环境适配** (89d055c, 7cd4a27)
  - 优化国内网络环境检测逻辑
  - 移除国际网站连通性测试相关功能
  - 调整生产环境DNS配置为国内服务器

#### 调试信息增强
- **枚举值转换优化** (916a7e6, f8e6e27)
  - 新增枚举值转换工具类
  - 统一网络类型、连接状态、绑定状态转换逻辑

### 🛠️ 技术架构改进

#### 配置管理优化
- **网络接口配置调整** (c4645f5)
  - 调整默认网络接口配置：WiFi(wlan0)、以太网(eth0)、5G(eth2)

- **多环境延迟配置** (af23936)
  - 新增多环境网络延迟期望值配置
  - 针对开发/生产环境分别设定差异化延迟阈值

#### 网络测试功能增强
- **网络质量综合测试** (821c6ec)
  - 新增网络质量综合测试功能，支持同时获取延迟和丢包率

- **延迟测试功能重构** (a9478d4)
  - 重构网络延迟测试功能，支持多地址并发延迟检测

### 🔄 代码重构

#### 代码结构优化
- **参数配置清理** (c281805)
  - 移除network_switch中冗余的国内测试网站参数声明

- **信号强度获取逻辑优化** (d27c422)
  - 优化网络质量检测中信号强度获取逻辑
  - 改为从订阅的网络状态中直接读取WiFi与5G信号强度

### 📈 性能提升

- 网络质量检查速度提升约30%

---

## [v1.3.0] - 2025-07-20

### 主要功能
- 智能DNS管理系统，支持基于网络环境的中国/国际DNS自动切换
- 科学化网络自动切换算法，引入网络质量评分体系
- 异步处理机制，优化网络质量处理性能
- 连通性检查优化，引入异步检查模式

---

## [v1.2.0] - 2025-07-19

### 主要功能
- 新增蜂窝网络管理模块，实现5G网络状态监控
- 修复nmcli命令在不同语言环境下的兼容性问题
- 新增WiFi信号强度百分比到dBm的转换功能

---

## [v1.1.0] - 2025-07-08 - 2025-07-11

### 主要功能
- 重构为单节点多组件架构
- 新增路由优先级管理与智能路由功能
- 多环境配置支持（默认、开发、生产）
- 重构参数配置管理机制，统一使用ROS2参数系统

---

## [v1.0.0] - 2025-07-07

### 主要功能
- 核心网络管理功能：DNS配置管理和WiFi连接功能
- 基础的网络状态监控和管理
- ROS2接口文档和开发环境配置

---

**维护者**: 徐辉  
**最后更新**: 2025-07-27
