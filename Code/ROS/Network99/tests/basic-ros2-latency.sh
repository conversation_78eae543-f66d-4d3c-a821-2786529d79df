#!/bin/bash

# 基础 ROS2 延迟测试

echo "=== ROS2 基础延迟测试 ==="
echo

# 设置环境
source /opt/ros/humble/setup.bash

# 创建日志目录
LOG_DIR="/tmp/basic_ros2_latency_$(date +%s)"
mkdir -p "$LOG_DIR"

echo "日志目录: $LOG_DIR"
echo

# 测试1: 1Hz 基础测试
echo "测试1: 1Hz 基础延迟测试"
echo "启动发布者..."

# 启动发布者
ros2 topic pub --rate 1 /test_topic std_msgs/msg/String "data: 'test'" > "$LOG_DIR/pub_1hz.log" 2>&1 &
PUB_PID=$!

# 等待发布者启动
sleep 2

echo "启动订阅者..."
# 启动订阅者，运行10秒
timeout 10s ros2 topic echo /test_topic > "$LOG_DIR/sub_1hz.log" 2>&1 &
SUB_PID=$!

echo "等待测试完成（12秒）..."
sleep 12

# 停止发布者
kill $PUB_PID 2>/dev/null
wait $PUB_PID 2>/dev/null

echo "分析结果..."
if [ -f "$LOG_DIR/sub_1hz.log" ]; then
    received=$(grep -c "^data:" "$LOG_DIR/sub_1hz.log" 2>/dev/null || echo "0")
    echo "接收到消息数: $received"
    echo "预期消息数: 约10条"
    
    if [ $received -gt 5 ]; then
        echo "✓ 1Hz测试成功"
    else
        echo "✗ 1Hz测试失败"
    fi
else
    echo "✗ 没有找到订阅者日志"
fi

echo
echo "测试2: 10Hz 中频测试"

# 测试2: 10Hz
ros2 topic pub --rate 10 /test_10hz std_msgs/msg/String "data: 'test10'" > "$LOG_DIR/pub_10hz.log" 2>&1 &
PUB_10_PID=$!

sleep 1

timeout 5s ros2 topic echo /test_10hz > "$LOG_DIR/sub_10hz.log" 2>&1 &
SUB_10_PID=$!

sleep 6

kill $PUB_10_PID 2>/dev/null
wait $PUB_10_PID 2>/dev/null

if [ -f "$LOG_DIR/sub_10hz.log" ]; then
    received_10=$(grep -c "^data:" "$LOG_DIR/sub_10hz.log" 2>/dev/null || echo "0")
    echo "10Hz 接收到消息数: $received_10"
    echo "10Hz 预期消息数: 约50条"
    
    if [ $received_10 -gt 25 ]; then
        echo "✓ 10Hz测试成功"
    else
        echo "✗ 10Hz测试失败"
    fi
fi

echo
echo "测试3: 单次消息测试"

# 测试3: 单次消息
ros2 topic echo /single_test > "$LOG_DIR/single_sub.log" 2>&1 &
SINGLE_SUB_PID=$!

sleep 2

echo "发送单次消息..."
for i in {1..3}; do
    echo "发送消息 $i"
    ros2 topic pub --once /single_test std_msgs/msg/String "data: 'single_$i'" > /dev/null 2>&1
    sleep 1
done

sleep 2
kill $SINGLE_SUB_PID 2>/dev/null
wait $SINGLE_SUB_PID 2>/dev/null

if [ -f "$LOG_DIR/single_sub.log" ]; then
    single_received=$(grep -c "^data:" "$LOG_DIR/single_sub.log" 2>/dev/null || echo "0")
    echo "单次测试接收消息数: $single_received"
    echo "单次测试预期消息数: 3条"
    
    if [ $single_received -gt 0 ]; then
        echo "✓ 单次消息测试成功"
    else
        echo "✗ 单次消息测试失败"
    fi
fi

echo
echo "=== 延迟分析 ==="

# 计算总体性能
total_tests=3
passed_tests=0

if [ $received -gt 5 ]; then
    ((passed_tests++))
fi

if [ $received_10 -gt 25 ]; then
    ((passed_tests++))
fi

if [ $single_received -gt 0 ]; then
    ((passed_tests++))
fi

echo "测试通过率: $passed_tests/$total_tests"

if [ $passed_tests -eq 3 ]; then
    echo "🎉 所有测试通过！ROS2 延迟性能正常"
elif [ $passed_tests -eq 2 ]; then
    echo "✅ 大部分测试通过，性能良好"
elif [ $passed_tests -eq 1 ]; then
    echo "⚠️ 部分测试通过，性能一般"
else
    echo "❌ 测试失败，需要检查配置"
fi

echo
echo "=== ROS2 正常环境延迟基准 ==="
echo "根据 ROS2 官方文档和社区测试："
echo
echo "本地通信延迟（同一台机器）："
echo "• 最佳情况: 10-100 微秒"
echo "• 典型情况: 100-1000 微秒 (1毫秒内)"
echo "• 高负载时: 1-10 毫秒"
echo
echo "网络通信延迟（不同机器）："
echo "• 局域网: 1-10 毫秒"
echo "• 广域网: 10-100 毫秒"
echo "• 互联网: 50-500 毫秒"
echo
echo "频率与延迟关系："
echo "• 1Hz: 延迟不敏感，适合状态监控"
echo "• 10Hz: 适合一般控制应用"
echo "• 100Hz: 适合实时控制"
echo "• 1000Hz: 需要特殊优化"
echo
echo "影响延迟的因素："
echo "• QoS 配置（Reliable vs BestEffort）"
echo "• 消息大小"
echo "• 系统负载"
echo "• 网络配置"
echo "• 序列化开销"
echo

echo "详细日志:"
echo "1Hz测试: cat $LOG_DIR/sub_1hz.log"
echo "10Hz测试: cat $LOG_DIR/sub_10hz.log"
echo "单次测试: cat $LOG_DIR/single_sub.log"

echo
echo "🎊 ROS2 基础延迟测试完成！"

# 清理
pkill -f "ros2 topic" 2>/dev/null
