#!/bin/bash

# 精确延迟测试脚本 - 测量 ROS2 发布订阅的精确延迟时间

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== ROS2 精确延迟测试 ===${NC}"
echo

# 设置 ROS2 环境
source /opt/ros/humble/setup.bash

# 创建日志目录
LOG_DIR="/tmp/precise_latency_test_$(date +%s)"
mkdir -p "$LOG_DIR"

echo -e "${BLUE}1. 连接建立延迟测试${NC}"
echo

# 测试话题
TOPIC="/latency_test"

echo -e "  ${YELLOW}启动发布者...${NC}"
# 启动发布者，每秒发布一次，包含时间戳
ros2 topic pub --rate 1 $TOPIC std_msgs/msg/String "data: 'timestamp_$(date +%s.%N)'" > "$LOG_DIR/publisher.log" 2>&1 &
PUB_PID=$!
echo -e "  ${GREEN}✓${NC} 发布者已启动 (PID: $PUB_PID)"

# 记录发布者启动时间
PUB_START_TIME=$(date +%s.%N)
echo "发布者启动时间: $PUB_START_TIME" > "$LOG_DIR/timing_analysis.log"

# 等待1秒让发布者建立话题
sleep 1

echo -e "  ${YELLOW}启动订阅者...${NC}"
# 记录订阅者启动时间
SUB_START_TIME=$(date +%s.%N)
echo "订阅者启动时间: $SUB_START_TIME" >> "$LOG_DIR/timing_analysis.log"

# 启动订阅者，运行10秒
timeout 10s ros2 topic echo $TOPIC > "$LOG_DIR/subscriber.log" 2>&1 &
SUB_PID=$!
echo -e "  ${GREEN}✓${NC} 订阅者已启动 (PID: $SUB_PID)"

echo -e "  ${YELLOW}等待测试完成（12秒）...${NC}"
sleep 12

# 停止发布者
kill $PUB_PID 2>/dev/null
wait $PUB_PID 2>/dev/null

# 记录测试结束时间
TEST_END_TIME=$(date +%s.%N)
echo "测试结束时间: $TEST_END_TIME" >> "$LOG_DIR/timing_analysis.log"

echo -e "  ${GREEN}✓${NC} 测试完成"

echo
echo -e "${BLUE}2. 延迟分析${NC}"

# 计算连接建立延迟
if command -v bc >/dev/null 2>&1; then
    CONNECTION_DELAY=$(echo "$SUB_START_TIME - $PUB_START_TIME" | bc -l)
    TOTAL_TEST_TIME=$(echo "$TEST_END_TIME - $PUB_START_TIME" | bc -l)
    
    echo -e "  ${YELLOW}时间分析:${NC}"
    echo -e "    发布者启动到订阅者启动: ${GREEN}${CONNECTION_DELAY}${NC} 秒"
    echo -e "    总测试时间: ${GREEN}${TOTAL_TEST_TIME}${NC} 秒"
fi

# 分析发布者输出
if [ -f "$LOG_DIR/publisher.log" ]; then
    pub_lines=$(grep -c "publishing" "$LOG_DIR/publisher.log" 2>/dev/null || echo "0")
    echo -e "  ${YELLOW}发布者统计:${NC}"
    echo -e "    发布消息数量: ${GREEN}$pub_lines${NC}"
fi

# 分析订阅者输出
if [ -f "$LOG_DIR/subscriber.log" ]; then
    # 计算接收到的消息数量（每条消息后面跟着 "---"）
    sub_messages=$(grep -c "^data:" "$LOG_DIR/subscriber.log" 2>/dev/null || echo "0")
    echo -e "  ${YELLOW}订阅者统计:${NC}"
    echo -e "    接收消息数量: ${GREEN}$sub_messages${NC}"
    
    if [ $sub_messages -gt 0 ]; then
        # 获取第一条消息的时间
        first_msg_line=$(grep -n "^data:" "$LOG_DIR/subscriber.log" | head -1 | cut -d: -f1)
        if [ ! -z "$first_msg_line" ]; then
            # 估算首次接收延迟（订阅者启动后多久收到第一条消息）
            echo -e "    ${GREEN}✓${NC} 成功接收消息"
            
            # 显示接收到的消息样本
            echo -e "  ${YELLOW}接收到的消息样本:${NC}"
            grep "^data:" "$LOG_DIR/subscriber.log" | head -3 | sed 's/^/    /'
        fi
        
        # 计算消息接收率
        if [ $pub_lines -gt 0 ]; then
            if command -v bc >/dev/null 2>&1; then
                success_rate=$(echo "scale=1; $sub_messages * 100 / $pub_lines" | bc -l)
                echo -e "    消息接收成功率: ${GREEN}${success_rate}%${NC}"
            fi
        fi
    else
        echo -e "    ${RED}✗${NC} 没有接收到消息"
    fi
fi

echo
echo -e "${BLUE}3. 实时延迟测试${NC}"

# 进行实时延迟测试
REALTIME_TOPIC="/realtime_test"

echo -e "  ${YELLOW}启动实时测试...${NC}"

# 启动订阅者（先启动）
ros2 topic echo $REALTIME_TOPIC > "$LOG_DIR/realtime_sub.log" 2>&1 &
RT_SUB_PID=$!

# 等待订阅者准备
sleep 2

echo -e "  ${YELLOW}发送带时间戳的消息...${NC}"

# 发送5条带精确时间戳的消息
for i in {1..5}; do
    SEND_TIME=$(date +%s.%N)
    MSG_DATA="msg_${i}_sent_at_${SEND_TIME}"
    
    echo "发送消息 $i 时间: $SEND_TIME" >> "$LOG_DIR/send_times.log"
    
    # 发送消息
    ros2 topic pub --once $REALTIME_TOPIC std_msgs/msg/String "data: '$MSG_DATA'" > /dev/null 2>&1
    
    echo -e "    发送消息 $i: ${GREEN}$SEND_TIME${NC}"
    
    # 等待1秒
    sleep 1
done

# 等待接收完成
sleep 3

# 停止订阅者
kill $RT_SUB_PID 2>/dev/null
wait $RT_SUB_PID 2>/dev/null

echo -e "  ${GREEN}✓${NC} 实时测试完成"

# 分析实时延迟
echo
echo -e "${BLUE}4. 实时延迟分析${NC}"

if [ -f "$LOG_DIR/realtime_sub.log" ] && [ -f "$LOG_DIR/send_times.log" ]; then
    rt_received=$(grep -c "^data:" "$LOG_DIR/realtime_sub.log" 2>/dev/null || echo "0")
    rt_sent=5
    
    echo -e "  ${YELLOW}实时测试统计:${NC}"
    echo -e "    发送消息数: ${GREEN}$rt_sent${NC}"
    echo -e "    接收消息数: ${GREEN}$rt_received${NC}"
    
    if [ $rt_received -gt 0 ]; then
        echo -e "    ${GREEN}✓${NC} 实时消息传递成功"
        
        # 显示接收到的消息
        echo -e "  ${YELLOW}接收到的实时消息:${NC}"
        grep "^data:" "$LOG_DIR/realtime_sub.log" | sed 's/^/    /'
        
        # 尝试计算延迟（简单估算）
        if command -v bc >/dev/null 2>&1; then
            if [ $rt_received -eq $rt_sent ]; then
                echo -e "    ${GREEN}✓${NC} 所有消息都成功接收"
            else
                loss_rate=$(echo "scale=1; ($rt_sent - $rt_received) * 100 / $rt_sent" | bc -l)
                echo -e "    ${YELLOW}○${NC} 消息丢失率: ${loss_rate}%"
            fi
        fi
    else
        echo -e "    ${RED}✗${NC} 实时消息传递失败"
    fi
fi

echo
echo -e "${BLUE}5. 连接速度测试${NC}"

# 测试连接建立速度
SPEED_TOPIC="/speed_test"

echo -e "  ${YELLOW}测试连接建立速度...${NC}"

# 同时启动发布者和订阅者，测量首次消息传递时间
SPEED_START=$(date +%s.%N)

# 启动高频发布者
ros2 topic pub --rate 5 $SPEED_TOPIC std_msgs/msg/String "data: 'speed_test'" > "$LOG_DIR/speed_pub.log" 2>&1 &
SPEED_PUB_PID=$!

# 立即启动订阅者
ros2 topic echo $SPEED_TOPIC > "$LOG_DIR/speed_sub.log" 2>&1 &
SPEED_SUB_PID=$!

# 运行5秒
sleep 5

# 停止进程
kill $SPEED_PUB_PID $SPEED_SUB_PID 2>/dev/null
wait $SPEED_PUB_PID $SPEED_SUB_PID 2>/dev/null

SPEED_END=$(date +%s.%N)

if command -v bc >/dev/null 2>&1; then
    SPEED_TOTAL_TIME=$(echo "$SPEED_END - $SPEED_START" | bc -l)
    echo -e "  ${GREEN}✓${NC} 连接速度测试完成，总时间: ${YELLOW}${SPEED_TOTAL_TIME}${NC} 秒"
fi

# 分析连接速度
if [ -f "$LOG_DIR/speed_sub.log" ]; then
    speed_messages=$(grep -c "^data:" "$LOG_DIR/speed_sub.log" 2>/dev/null || echo "0")
    echo -e "  连接速度测试接收消息数: ${GREEN}$speed_messages${NC}"
    
    if [ $speed_messages -gt 15 ]; then
        echo -e "  ${GREEN}✓${NC} 连接建立非常快"
    elif [ $speed_messages -gt 10 ]; then
        echo -e "  ${YELLOW}○${NC} 连接建立较快"
    elif [ $speed_messages -gt 5 ]; then
        echo -e "  ${YELLOW}○${NC} 连接建立正常"
    else
        echo -e "  ${RED}✗${NC} 连接建立较慢"
    fi
fi

echo
echo -e "${BLUE}6. 延迟总结${NC}"

# 计算总体性能评分
total_score=0
max_score=4

# 基本连接测试
if [ -f "$LOG_DIR/subscriber.log" ] && [ $(grep -c "^data:" "$LOG_DIR/subscriber.log" 2>/dev/null || echo "0") -gt 0 ]; then
    ((total_score++))
    echo -e "  ${GREEN}✓${NC} 基本消息传递: 正常"
else
    echo -e "  ${RED}✗${NC} 基本消息传递: 失败"
fi

# 实时测试
if [ -f "$LOG_DIR/realtime_sub.log" ] && [ $(grep -c "^data:" "$LOG_DIR/realtime_sub.log" 2>/dev/null || echo "0") -gt 0 ]; then
    ((total_score++))
    echo -e "  ${GREEN}✓${NC} 实时消息传递: 正常"
else
    echo -e "  ${RED}✗${NC} 实时消息传递: 失败"
fi

# 连接速度
if [ -f "$LOG_DIR/speed_sub.log" ] && [ $(grep -c "^data:" "$LOG_DIR/speed_sub.log" 2>/dev/null || echo "0") -gt 5 ]; then
    ((total_score++))
    echo -e "  ${GREEN}✓${NC} 连接建立速度: 正常"
else
    echo -e "  ${RED}✗${NC} 连接建立速度: 较慢"
fi

# 整体稳定性
if [ $total_score -ge 2 ]; then
    ((total_score++))
    echo -e "  ${GREEN}✓${NC} 整体稳定性: 良好"
else
    echo -e "  ${RED}✗${NC} 整体稳定性: 需要改进"
fi

echo
echo -e "  ${BLUE}延迟性能评分: ${GREEN}$total_score/$max_score${NC}"

if [ $total_score -eq $max_score ]; then
    echo -e "  ${GREEN}🎉 优秀！ROS2 延迟性能非常好${NC}"
    echo -e "    • 消息传递快速可靠"
    echo -e "    • 连接建立迅速"
    echo -e "    • 系统响应良好"
elif [ $total_score -ge 3 ]; then
    echo -e "  ${GREEN}✅ 良好！ROS2 延迟性能正常${NC}"
    echo -e "    • 大部分功能正常"
    echo -e "    • 可能有轻微延迟"
elif [ $total_score -ge 2 ]; then
    echo -e "  ${YELLOW}⚠️ 一般！ROS2 延迟性能需要优化${NC}"
    echo -e "    • 基本功能可用"
    echo -e "    • 建议检查网络配置"
else
    echo -e "  ${RED}❌ 较差！ROS2 延迟性能有问题${NC}"
    echo -e "    • 多项功能异常"
    echo -e "    • 需要检查系统配置"
fi

echo
echo -e "${BLUE}7. 详细信息${NC}"
echo -e "  日志目录: ${YELLOW}$LOG_DIR${NC}"
echo -e "  时间分析: ${YELLOW}cat $LOG_DIR/timing_analysis.log${NC}"
echo -e "  发送时间: ${YELLOW}cat $LOG_DIR/send_times.log${NC}"
echo -e "  基本测试: ${YELLOW}cat $LOG_DIR/subscriber.log${NC}"
echo -e "  实时测试: ${YELLOW}cat $LOG_DIR/realtime_sub.log${NC}"
echo -e "  速度测试: ${YELLOW}cat $LOG_DIR/speed_sub.log${NC}"

echo
echo -e "${GREEN}🎊 精确延迟测试完成！${NC}"

# 清理进程
pkill -f "ros2 topic" 2>/dev/null
