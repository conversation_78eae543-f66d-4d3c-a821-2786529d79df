#include <iostream>
#include <string>

// 模拟配置文件加载测试
class NetworkConfigTest {
public:
    void test_interface_mapping() {
        std::cout << "=== 测试网络接口配置映射 ===" << std::endl;
        
        // 模拟配置文件中的接口名称
        std::string wifi_interface = "wlan0";      // 默认配置
        std::string ethernet_interface = "wlan1";  // 配置文件中的设置
        std::string fiveg_interface = "eth0";      // 配置文件中的设置
        
        std::cout << "配置的接口名称:" << std::endl;
        std::cout << "  WiFi接口: " << wifi_interface << std::endl;
        std::cout << "  以太网接口: " << ethernet_interface << std::endl;
        std::cout << "  5G接口: " << fiveg_interface << std::endl;
        
        // 测试接口类型识别
        test_network_type_detection("wlan0", wifi_interface, ethernet_interface, fiveg_interface);
        test_network_type_detection("wlan1", wifi_interface, ethernet_interface, fiveg_interface);
        test_network_type_detection("eth0", wifi_interface, ethernet_interface, fiveg_interface);
        test_network_type_detection("usb0", wifi_interface, ethernet_interface, fiveg_interface);
        test_network_type_detection("unknown_interface", wifi_interface, ethernet_interface, fiveg_interface);
    }
    
private:
    enum NetworkType {
        NETWORK_TYPE_UNKNOWN = 0,
        NETWORK_TYPE_WIFI = 1,
        NETWORK_TYPE_ETHERNET = 2,
        NETWORK_TYPE_5G = 3
    };
    
    NetworkType get_network_type_with_config(
        const std::string& interface_name,
        const std::string& wifi_interface,
        const std::string& ethernet_interface,
        const std::string& fiveg_interface) {
        
        std::cout << "  检测接口: " << interface_name << std::endl;
        
        // 精确匹配配置的接口名称
        if (interface_name == wifi_interface) {
            std::cout << "    -> 精确匹配WiFi接口" << std::endl;
            return NETWORK_TYPE_WIFI;
        } else if (interface_name == ethernet_interface) {
            std::cout << "    -> 精确匹配以太网接口" << std::endl;
            return NETWORK_TYPE_ETHERNET;
        } else if (interface_name == fiveg_interface) {
            std::cout << "    -> 精确匹配5G接口" << std::endl;
            return NETWORK_TYPE_5G;
        } else {
            // 如果精确匹配失败，尝试模式匹配作为后备
            std::cout << "    -> 精确匹配失败，尝试模式匹配" << std::endl;
            
            // 检查是否包含WiFi接口的基础名称
            std::string wifi_base = wifi_interface;
            if (wifi_base.length() > 1 && std::isdigit(wifi_base.back())) {
                wifi_base = wifi_base.substr(0, wifi_base.length() - 1);
            }
            
            // 检查是否包含以太网接口的基础名称
            std::string eth_base = ethernet_interface;
            if (eth_base.length() > 1 && std::isdigit(eth_base.back())) {
                eth_base = eth_base.substr(0, eth_base.length() - 1);
            }
            
            // 检查是否包含5G接口的基础名称
            std::string fiveg_base = fiveg_interface;
            if (fiveg_base.length() > 1 && std::isdigit(fiveg_base.back())) {
                fiveg_base = fiveg_base.substr(0, fiveg_base.length() - 1);
            }
            
            if (interface_name.find(wifi_base) != std::string::npos) {
                std::cout << "    -> 模式匹配WiFi接口 (基于 " << wifi_base << ")" << std::endl;
                return NETWORK_TYPE_WIFI;
            } else if (interface_name.find(eth_base) != std::string::npos) {
                std::cout << "    -> 模式匹配以太网接口 (基于 " << eth_base << ")" << std::endl;
                return NETWORK_TYPE_ETHERNET;
            } else if (interface_name.find(fiveg_base) != std::string::npos) {
                std::cout << "    -> 模式匹配5G接口 (基于 " << fiveg_base << ")" << std::endl;
                return NETWORK_TYPE_5G;
            } else {
                // 最后的后备方案：使用传统的硬编码模式
                if (interface_name.find("wlan") != std::string::npos) {
                    std::cout << "    -> 后备匹配WiFi接口" << std::endl;
                    return NETWORK_TYPE_WIFI;
                } else if (interface_name.find("eth") != std::string::npos) {
                    std::cout << "    -> 后备匹配以太网接口" << std::endl;
                    return NETWORK_TYPE_ETHERNET;
                } else if (interface_name.find("usb") != std::string::npos) {
                    std::cout << "    -> 后备匹配5G接口" << std::endl;
                    return NETWORK_TYPE_5G;
                } else {
                    std::cout << "    -> 无法识别接口类型" << std::endl;
                    return NETWORK_TYPE_UNKNOWN;
                }
            }
        }
    }
    
    void test_network_type_detection(
        const std::string& interface_name,
        const std::string& wifi_interface,
        const std::string& ethernet_interface,
        const std::string& fiveg_interface) {
        
        NetworkType type = get_network_type_with_config(
            interface_name, wifi_interface, ethernet_interface, fiveg_interface);
        
        std::string type_name;
        switch (type) {
            case NETWORK_TYPE_WIFI:
                type_name = "WiFi";
                break;
            case NETWORK_TYPE_ETHERNET:
                type_name = "以太网";
                break;
            case NETWORK_TYPE_5G:
                type_name = "5G";
                break;
            default:
                type_name = "未知";
                break;
        }
        
        std::cout << "    结果: " << type_name << " (类型码: " << type << ")" << std::endl;
        std::cout << std::endl;
    }
};

int main() {
    std::cout << "网络配置测试程序" << std::endl;
    std::cout << "==================" << std::endl;
    
    NetworkConfigTest test;
    test.test_interface_mapping();
    
    std::cout << "测试完成！" << std::endl;
    return 0;
}
