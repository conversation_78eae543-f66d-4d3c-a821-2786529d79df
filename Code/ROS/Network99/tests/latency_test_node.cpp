#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/string.hpp>
#include <chrono>
#include <vector>
#include <numeric>
#include <iomanip>
#include <sstream>

using namespace std::chrono_literals;

class LatencyTestNode : public rclcpp::Node
{
public:
    LatencyTestNode() : Node("latency_test_node")
    {
        RCLCPP_INFO(this->get_logger(), "=== ROS2 C++ 延迟测试节点启动 ===");
        
        // 创建发布者和订阅者
        publisher_ = this->create_publisher<std_msgs::msg::String>("latency_test_topic", 10);
        
        subscription_ = this->create_subscription<std_msgs::msg::String>(
            "latency_test_topic", 10,
            std::bind(&LatencyTestNode::message_callback, this, std::placeholders::_1));
        
        // 等待连接建立
        RCLCPP_INFO(this->get_logger(), "等待发布者和订阅者连接建立...");
        std::this_thread::sleep_for(2s);
        
        // 开始测试
        run_latency_tests();
    }

private:
    void message_callback(const std_msgs::msg::String::SharedPtr msg)
    {
        auto receive_time = std::chrono::high_resolution_clock::now();
        
        // 解析消息中的发送时间戳
        std::string data = msg->data;
        size_t pos = data.find("timestamp:");
        if (pos != std::string::npos) {
            std::string timestamp_str = data.substr(pos + 10);
            try {
                auto send_time_ns = std::stoull(timestamp_str);
                auto send_time = std::chrono::time_point<std::chrono::high_resolution_clock>(
                    std::chrono::nanoseconds(send_time_ns));
                
                // 计算延迟
                auto latency = std::chrono::duration_cast<std::chrono::microseconds>(
                    receive_time - send_time);
                
                latency_measurements_.push_back(latency.count());
                
                RCLCPP_DEBUG(this->get_logger(), "消息延迟: %ld 微秒", latency.count());
            } catch (const std::exception& e) {
                RCLCPP_WARN(this->get_logger(), "解析时间戳失败: %s", e.what());
            }
        }
        
        messages_received_++;
    }
    
    void run_latency_tests()
    {
        RCLCPP_INFO(this->get_logger(), "开始延迟测试...");
        
        // 测试1: 低频率测试 (1Hz)
        test_frequency(1.0, 10, "低频率测试 (1Hz)");
        
        // 测试2: 中频率测试 (10Hz)
        test_frequency(10.0, 50, "中频率测试 (10Hz)");
        
        // 测试3: 高频率测试 (100Hz)
        test_frequency(100.0, 200, "高频率测试 (100Hz)");
        
        // 测试4: 超高频率测试 (1000Hz)
        test_frequency(1000.0, 500, "超高频率测试 (1000Hz)");
        
        // 输出总结
        print_summary();
    }
    
    void test_frequency(double frequency, int message_count, const std::string& test_name)
    {
        RCLCPP_INFO(this->get_logger(), "\n--- %s ---", test_name.c_str());
        RCLCPP_INFO(this->get_logger(), "频率: %.1f Hz, 消息数量: %d", frequency, message_count);
        
        // 重置计数器
        latency_measurements_.clear();
        messages_received_ = 0;
        messages_sent_ = 0;
        
        // 计算发送间隔
        auto interval = std::chrono::duration<double>(1.0 / frequency);
        auto interval_us = std::chrono::duration_cast<std::chrono::microseconds>(interval);
        
        RCLCPP_INFO(this->get_logger(), "发送间隔: %ld 微秒", interval_us.count());
        
        // 记录测试开始时间
        auto test_start = std::chrono::high_resolution_clock::now();
        
        // 发送消息
        for (int i = 0; i < message_count; ++i) {
            auto send_time = std::chrono::high_resolution_clock::now();
            auto send_time_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                send_time.time_since_epoch()).count();
            
            std_msgs::msg::String msg;
            msg.data = "msg_" + std::to_string(i) + "_timestamp:" + std::to_string(send_time_ns);
            
            publisher_->publish(msg);
            messages_sent_++;
            
            // 等待下一次发送
            if (i < message_count - 1) {
                std::this_thread::sleep_for(interval);
            }
        }
        
        // 等待接收完成
        RCLCPP_INFO(this->get_logger(), "等待消息接收完成...");
        auto wait_start = std::chrono::high_resolution_clock::now();
        
        // 简单等待，让回调有时间处理
        std::this_thread::sleep_for(2s);
        
        auto test_end = std::chrono::high_resolution_clock::now();
        auto test_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            test_end - test_start);
        
        // 分析结果
        analyze_results(test_name, frequency, test_duration.count());
    }
    
    void analyze_results(const std::string& test_name, double frequency, long test_duration_ms)
    {
        RCLCPP_INFO(this->get_logger(), "\n=== %s 结果分析 ===", test_name.c_str());
        RCLCPP_INFO(this->get_logger(), "发送消息数: %d", messages_sent_);
        RCLCPP_INFO(this->get_logger(), "接收消息数: %d", messages_received_);
        RCLCPP_INFO(this->get_logger(), "测试持续时间: %ld 毫秒", test_duration_ms);
        
        if (messages_sent_ > 0) {
            double success_rate = (double)messages_received_ / messages_sent_ * 100.0;
            RCLCPP_INFO(this->get_logger(), "消息接收成功率: %.1f%%", success_rate);
        }
        
        if (!latency_measurements_.empty()) {
            // 计算延迟统计
            auto min_latency = *std::min_element(latency_measurements_.begin(), latency_measurements_.end());
            auto max_latency = *std::max_element(latency_measurements_.begin(), latency_measurements_.end());
            auto avg_latency = std::accumulate(latency_measurements_.begin(), latency_measurements_.end(), 0.0) / latency_measurements_.size();
            
            // 计算中位数
            std::vector<long> sorted_latencies = latency_measurements_;
            std::sort(sorted_latencies.begin(), sorted_latencies.end());
            auto median_latency = sorted_latencies[sorted_latencies.size() / 2];
            
            // 计算99百分位
            auto p99_index = static_cast<size_t>(sorted_latencies.size() * 0.99);
            auto p99_latency = sorted_latencies[std::min(p99_index, sorted_latencies.size() - 1)];
            
            RCLCPP_INFO(this->get_logger(), "延迟统计 (微秒):");
            RCLCPP_INFO(this->get_logger(), "  最小延迟: %ld μs", min_latency);
            RCLCPP_INFO(this->get_logger(), "  最大延迟: %ld μs", max_latency);
            RCLCPP_INFO(this->get_logger(), "  平均延迟: %.1f μs", avg_latency);
            RCLCPP_INFO(this->get_logger(), "  中位延迟: %ld μs", median_latency);
            RCLCPP_INFO(this->get_logger(), "  99%延迟: %ld μs", p99_latency);
            
            // 延迟评价
            if (avg_latency < 100) {
                RCLCPP_INFO(this->get_logger(), "  延迟评价: 🎉 优秀 (< 100μs)");
            } else if (avg_latency < 1000) {
                RCLCPP_INFO(this->get_logger(), "  延迟评价: ✅ 良好 (< 1ms)");
            } else if (avg_latency < 10000) {
                RCLCPP_INFO(this->get_logger(), "  延迟评价: ⚠️ 一般 (< 10ms)");
            } else {
                RCLCPP_INFO(this->get_logger(), "  延迟评价: ❌ 较差 (> 10ms)");
            }
            
            // 计算实际频率
            if (test_duration_ms > 0) {
                double actual_frequency = (double)messages_received_ / (test_duration_ms / 1000.0);
                RCLCPP_INFO(this->get_logger(), "实际接收频率: %.1f Hz (目标: %.1f Hz)", 
                           actual_frequency, frequency);
            }
        } else {
            RCLCPP_WARN(this->get_logger(), "没有收到任何消息，无法计算延迟");
        }
        
        // 保存测试结果
        TestResult result;
        result.test_name = test_name;
        result.target_frequency = frequency;
        result.messages_sent = messages_sent_;
        result.messages_received = messages_received_;
        result.test_duration_ms = test_duration_ms;
        result.latency_measurements = latency_measurements_;
        
        test_results_.push_back(result);
    }
    
    void print_summary()
    {
        RCLCPP_INFO(this->get_logger(), "\n==================================================");
        RCLCPP_INFO(this->get_logger(), "=== ROS2 延迟测试总结 ===");
        RCLCPP_INFO(this->get_logger(), "==================================================");
        
        for (const auto& result : test_results_) {
            RCLCPP_INFO(this->get_logger(), "\n%s:", result.test_name.c_str());
            RCLCPP_INFO(this->get_logger(), "  目标频率: %.1f Hz", result.target_frequency);
            RCLCPP_INFO(this->get_logger(), "  消息成功率: %d/%d (%.1f%%)", 
                       result.messages_received, result.messages_sent,
                       result.messages_sent > 0 ? (double)result.messages_received / result.messages_sent * 100.0 : 0.0);
            
            if (!result.latency_measurements.empty()) {
                auto avg_latency = std::accumulate(result.latency_measurements.begin(), 
                                                 result.latency_measurements.end(), 0.0) / result.latency_measurements.size();
                RCLCPP_INFO(this->get_logger(), "  平均延迟: %.1f μs", avg_latency);
            }
        }
        
        RCLCPP_INFO(this->get_logger(), "\n=== ROS2 延迟基准参考 ===");
        RCLCPP_INFO(this->get_logger(), "本地通信延迟基准:");
        RCLCPP_INFO(this->get_logger(), "  优秀: < 100 微秒");
        RCLCPP_INFO(this->get_logger(), "  良好: 100-1000 微秒 (1毫秒内)");
        RCLCPP_INFO(this->get_logger(), "  一般: 1-10 毫秒");
        RCLCPP_INFO(this->get_logger(), "  较差: > 10 毫秒");
        
        RCLCPP_INFO(this->get_logger(), "\n频率建议:");
        RCLCPP_INFO(this->get_logger(), "  状态监控: 1-10 Hz");
        RCLCPP_INFO(this->get_logger(), "  控制应用: 10-100 Hz");
        RCLCPP_INFO(this->get_logger(), "  实时控制: 100-1000 Hz");
        
        RCLCPP_INFO(this->get_logger(), "\n🎊 延迟测试完成！");
    }

private:
    struct TestResult {
        std::string test_name;
        double target_frequency;
        int messages_sent;
        int messages_received;
        long test_duration_ms;
        std::vector<long> latency_measurements;
    };
    
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr publisher_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr subscription_;
    
    std::vector<long> latency_measurements_;
    int messages_received_ = 0;
    int messages_sent_ = 0;
    
    std::vector<TestResult> test_results_;
};

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    
    auto node = std::make_shared<LatencyTestNode>();
    
    // 运行节点直到测试完成
    rclcpp::spin(node);
    
    rclcpp::shutdown();
    return 0;
}
