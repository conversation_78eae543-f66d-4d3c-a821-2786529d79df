#!/bin/bash

# 快速 ROS2 延迟测试脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== 快速 ROS2 延迟测试 ===${NC}"
echo

# 设置 ROS2 环境
source /opt/ros/humble/setup.bash

# 测试话题
TOPIC="/quick_test"

echo -e "${BLUE}1. 测试基本发布订阅延迟${NC}"
echo

# 创建日志目录
LOG_DIR="/tmp/quick_test_$(date +%s)"
mkdir -p "$LOG_DIR"

echo -e "  ${YELLOW}步骤1: 启动发布者（1Hz，持续10秒）${NC}"
# 启动发布者
ros2 topic pub --rate 1 $TOPIC std_msgs/msg/String "data: 'test_message'" > "$LOG_DIR/publisher.log" 2>&1 &
PUB_PID=$!
echo -e "  ${GREEN}✓${NC} 发布者已启动 (PID: $PUB_PID)"

# 等待1秒让发布者建立话题
sleep 1

echo -e "  ${YELLOW}步骤2: 启动订阅者（运行8秒）${NC}"
# 启动订阅者
timeout 8s ros2 topic echo $TOPIC > "$LOG_DIR/subscriber.log" 2>&1 &
SUB_PID=$!
echo -e "  ${GREEN}✓${NC} 订阅者已启动 (PID: $SUB_PID)"

echo -e "  ${YELLOW}步骤3: 等待测试完成...${NC}"
# 等待测试完成
sleep 10

# 停止发布者
kill $PUB_PID 2>/dev/null
wait $PUB_PID 2>/dev/null

echo -e "  ${GREEN}✓${NC} 测试完成"

echo
echo -e "${BLUE}2. 分析结果${NC}"

# 分析发布者
if [ -f "$LOG_DIR/publisher.log" ]; then
    pub_lines=$(wc -l < "$LOG_DIR/publisher.log")
    echo -e "  发布者日志行数: ${GREEN}$pub_lines${NC}"
else
    echo -e "  ${RED}✗${NC} 发布者日志不存在"
fi

# 分析订阅者
if [ -f "$LOG_DIR/subscriber.log" ]; then
    sub_lines=$(wc -l < "$LOG_DIR/subscriber.log")
    echo -e "  订阅者接收消息数: ${GREEN}$sub_lines${NC}"
    
    if [ $sub_lines -gt 0 ]; then
        echo -e "  ${GREEN}✓${NC} 消息传递成功"
        echo -e "  ${BLUE}接收到的消息:${NC}"
        head -5 "$LOG_DIR/subscriber.log" | sed 's/^/    /'
        if [ $sub_lines -gt 5 ]; then
            echo "    ... (还有 $((sub_lines - 5)) 条消息)"
        fi
    else
        echo -e "  ${RED}✗${NC} 没有接收到消息"
    fi
else
    echo -e "  ${RED}✗${NC} 订阅者日志不存在"
fi

echo
echo -e "${BLUE}3. 连接建立时间测试${NC}"

# 测试连接建立时间
TOPIC2="/connection_test"

echo -e "  ${YELLOW}同时启动发布者和订阅者...${NC}"

# 记录开始时间
START_TIME=$(date +%s.%N)

# 同时启动
ros2 topic pub --rate 2 $TOPIC2 std_msgs/msg/String "data: 'connection_test'" > "$LOG_DIR/conn_pub.log" 2>&1 &
CONN_PUB_PID=$!

ros2 topic echo $TOPIC2 > "$LOG_DIR/conn_sub.log" 2>&1 &
CONN_SUB_PID=$!

# 等待5秒
sleep 5

# 停止进程
kill $CONN_PUB_PID $CONN_SUB_PID 2>/dev/null
wait $CONN_PUB_PID $CONN_SUB_PID 2>/dev/null

# 记录结束时间
END_TIME=$(date +%s.%N)

# 计算连接时间
if command -v bc >/dev/null 2>&1; then
    CONN_TIME=$(echo "$END_TIME - $START_TIME" | bc -l)
    echo -e "  ${GREEN}✓${NC} 连接测试完成，总时间: ${YELLOW}${CONN_TIME}${NC} 秒"
else
    echo -e "  ${GREEN}✓${NC} 连接测试完成"
fi

# 分析连接测试结果
if [ -f "$LOG_DIR/conn_sub.log" ]; then
    conn_messages=$(wc -l < "$LOG_DIR/conn_sub.log")
    echo -e "  连接测试接收消息数: ${GREEN}$conn_messages${NC}"
    
    if [ $conn_messages -gt 5 ]; then
        echo -e "  ${GREEN}✓${NC} 连接建立快速"
    elif [ $conn_messages -gt 2 ]; then
        echo -e "  ${YELLOW}○${NC} 连接建立正常"
    else
        echo -e "  ${RED}✗${NC} 连接建立较慢"
    fi
fi

echo
echo -e "${BLUE}4. 测试总结${NC}"

# 计算总体结果
total_score=0
max_score=3

# 检查基本发布订阅
if [ -f "$LOG_DIR/subscriber.log" ] && [ $(wc -l < "$LOG_DIR/subscriber.log") -gt 0 ]; then
    ((total_score++))
    echo -e "  ${GREEN}✓${NC} 基本发布订阅: 正常"
else
    echo -e "  ${RED}✗${NC} 基本发布订阅: 失败"
fi

# 检查发布者
if [ -f "$LOG_DIR/publisher.log" ]; then
    ((total_score++))
    echo -e "  ${GREEN}✓${NC} 发布者运行: 正常"
else
    echo -e "  ${RED}✗${NC} 发布者运行: 失败"
fi

# 检查连接测试
if [ -f "$LOG_DIR/conn_sub.log" ] && [ $(wc -l < "$LOG_DIR/conn_sub.log") -gt 0 ]; then
    ((total_score++))
    echo -e "  ${GREEN}✓${NC} 连接建立: 正常"
else
    echo -e "  ${RED}✗${NC} 连接建立: 失败"
fi

echo
echo -e "  ${BLUE}总体评分: ${GREEN}$total_score/$max_score${NC}"

if [ $total_score -eq $max_score ]; then
    echo -e "  ${GREEN}🎉 所有测试通过！ROS2 延迟性能良好${NC}"
elif [ $total_score -gt 1 ]; then
    echo -e "  ${YELLOW}⚠️ 大部分测试通过，性能基本正常${NC}"
else
    echo -e "  ${RED}❌ 多项测试失败，需要检查配置${NC}"
fi

echo
echo -e "${BLUE}5. 详细信息${NC}"
echo -e "  日志目录: ${YELLOW}$LOG_DIR${NC}"
echo -e "  查看发布者日志: ${YELLOW}cat $LOG_DIR/publisher.log${NC}"
echo -e "  查看订阅者日志: ${YELLOW}cat $LOG_DIR/subscriber.log${NC}"
echo -e "  查看连接测试: ${YELLOW}cat $LOG_DIR/conn_sub.log${NC}"

echo
echo -e "${GREEN}🎊 快速延迟测试完成！${NC}"

# 清理进程
pkill -f "ros2 topic" 2>/dev/null
