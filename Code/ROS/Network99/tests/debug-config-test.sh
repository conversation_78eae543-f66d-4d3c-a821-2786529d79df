#!/bin/bash

# 测试调试配置脚本
# 验证所有调试配置是否正确设置

echo "=== 调试配置测试 ==="
echo

# 检查当前架构
CURRENT_ARCH=$(uname -m)
echo "当前系统架构: $CURRENT_ARCH"

# 检查 ROS 环境
echo "ROS 环境检查:"
if [ -f "/opt/ros/humble/setup.bash" ]; then
    echo "✓ ROS Humble 已安装"
else
    echo "✗ ROS Humble 未找到"
    exit 1
fi

# 检查构建输出
echo
echo "构建输出检查:"
BUILD_DIR="install/$CURRENT_ARCH/Debug"
if [ -d "$BUILD_DIR" ]; then
    echo "✓ 构建目录存在: $BUILD_DIR"
else
    echo "✗ 构建目录不存在: $BUILD_DIR"
    echo "请先运行: ./build.sh"
    exit 1
fi

# 检查可执行文件
EXECUTABLE="$BUILD_DIR/gen3_network_manager_core/lib/gen3_network_manager_core/network_manager_node"
if [ -f "$EXECUTABLE" ]; then
    echo "✓ 可执行文件存在: $EXECUTABLE"
else
    echo "✗ 可执行文件不存在: $EXECUTABLE"
    exit 1
fi

# 检查 launch.json 配置
echo
echo "launch.json 配置检查:"
LAUNCH_JSON=".vscode/launch.json"
if [ -f "$LAUNCH_JSON" ]; then
    echo "✓ launch.json 文件存在"
    
    # 检查是否包含当前架构路径
    if grep -q "install/$CURRENT_ARCH" "$LAUNCH_JSON"; then
        echo "✓ launch.json 包含当前架构路径 ($CURRENT_ARCH)"
    else
        echo "⚠ launch.json 可能需要更新架构路径"
        echo "运行: ./scripts/update_launch_config.sh"
    fi
    
    # 检查是否包含 ROS 环境路径
    if grep -q "/opt/ros/humble/lib" "$LAUNCH_JSON"; then
        echo "✓ launch.json 包含 ROS 库路径"
    else
        echo "✗ launch.json 缺少 ROS 库路径"
    fi
    
    # 统计调试配置数量
    CONFIG_COUNT=$(grep -c '"name":.*Debug' "$LAUNCH_JSON")
    echo "✓ 找到 $CONFIG_COUNT 个调试配置"
    
else
    echo "✗ launch.json 文件不存在"
    exit 1
fi

# 检查共享库
echo
echo "共享库检查:"
if [ -f "/opt/ros/humble/lib/librclcpp_action.so" ]; then
    echo "✓ ROS 共享库可用: librclcpp_action.so"
else
    echo "✗ ROS 共享库不可用: librclcpp_action.so"
fi

# 测试环境设置
echo
echo "环境设置测试:"
source /opt/ros/humble/setup.bash 2>/dev/null
source "$BUILD_DIR/setup.bash" 2>/dev/null

if [ -n "$ROS_DISTRO" ]; then
    echo "✓ ROS_DISTRO: $ROS_DISTRO"
else
    echo "✗ ROS_DISTRO 未设置"
fi

if [ -n "$AMENT_PREFIX_PATH" ]; then
    echo "✓ AMENT_PREFIX_PATH 已设置"
else
    echo "✗ AMENT_PREFIX_PATH 未设置"
fi

# 快速启动测试
echo
echo "快速启动测试:"
echo "测试可执行文件是否能正常启动（3秒后自动停止）..."

timeout 3s "$EXECUTABLE" >/dev/null 2>&1 &
PID=$!
sleep 3
if kill -0 $PID 2>/dev/null; then
    kill $PID 2>/dev/null
    echo "✓ 程序可以正常启动"
else
    echo "✗ 程序启动失败"
fi

echo
echo "=== 测试完成 ==="
echo
echo "调试配置摘要:"
echo "- 架构: $CURRENT_ARCH"
echo "- 构建目录: $BUILD_DIR"
echo "- 可执行文件: $EXECUTABLE"
echo "- 调试配置: $CONFIG_COUNT 个"
echo
echo "使用方法:"
echo "1. 在 VSCode 中按 F5"
echo "2. 选择合适的调试配置"
echo "3. 开始调试"
echo
echo "推荐配置:"
echo "- 基础调试: ROS2 C++ Debug (Auto-Arch)"
echo "- 带配置调试: ROS2 C++ Debug with Config (Auto-Arch)"
echo "- 开发调试: ROS2 C++ Debug with Dev Config (Auto-Arch)"
