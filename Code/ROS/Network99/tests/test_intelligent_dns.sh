#!/bin/bash

# 测试智能DNS管理系统

echo "=== 测试智能DNS管理系统 ==="

# 设置环境
export ROS_DISTRO=humble
source /opt/ros/humble/setup.bash

# Source 工作空间
cd /mine/note/Code/ROS/Network99
source install/x86_64/Debug/setup.bash

# 创建测试日志目录
LOG_DIR="/tmp/intelligent_dns_test_$(date +%s)"
mkdir -p "$LOG_DIR"

echo "日志目录: $LOG_DIR"
echo

# 创建智能DNS测试参数文件
cat > "$LOG_DIR/intelligent_dns_params.yaml" << EOF
network_manager_node:
  ros__parameters:
    # DNS管理配置
    dns_check_interval: 10.0
    dns_test_timeout_ms: 3000
    dns_max_consecutive_failures: 3
    dns_priority_update_threshold: 0.8
    
    # DNS服务器配置（按优先级分组）
    dns_servers:
      primary: ["8.8.8.8", "1.1.1.1"]           # 主要DNS服务器
      secondary: ["114.114.114.114", "223.5.5.5"] # 备用DNS服务器
      backup: ["208.67.222.222", "9.9.9.9"]      # 备份DNS服务器
    
    # 其他网络管理配置
    network_check_interval: 5.0
    connectivity_check_interval: 15.0
    
    # 网络接口配置
    wifi_interface: "wlan0"
    ethernet_interface: "eth0"
    5g_interface: "usb0"
EOF

echo "1. 启动智能DNS管理系统..."
# 启动网络管理器节点，使用智能DNS配置
ros2 run gen3_network_manager_core network_manager_node --ros-args --params-file "$LOG_DIR/intelligent_dns_params.yaml" > "$LOG_DIR/dns_system.log" 2>&1 &
SYSTEM_PID=$!

echo "2. 等待系统运行（45秒）..."
echo "   - 观察智能DNS管理器初始化"
echo "   - 监控DNS服务器配置加载"
echo "   - 验证智能DNS检查逻辑"
echo "   - 测试DNS服务器优先级管理"
sleep 45

# 停止进程
echo "3. 停止测试进程..."
kill $SYSTEM_PID 2>/dev/null
wait $SYSTEM_PID 2>/dev/null

echo
echo "4. 分析智能DNS管理测试结果..."

# 分析系统日志
echo "=== 智能DNS管理分析 ==="
if [ -f "$LOG_DIR/dns_system.log" ]; then
    echo "系统日志文件存在，开始分析..."
    
    echo
    echo "--- DNS管理器初始化分析 ---"
    
    # 检查智能DNS管理器初始化
    smart_dns_init=$(grep -c "创建智能DNS管理器\|初始化智能DNS管理器" "$LOG_DIR/dns_system.log" 2>/dev/null || echo "0")
    dns_config_load=$(grep -c "加载DNS服务器配置" "$LOG_DIR/dns_system.log" 2>/dev/null || echo "0")
    dns_server_count=$(grep "已加载.*个DNS服务器配置" "$LOG_DIR/dns_system.log" | tail -1 | grep -o '[0-9]\+' | head -1 || echo "0")
    
    echo "智能DNS管理器初始化: $smart_dns_init 次"
    echo "DNS服务器配置加载: $dns_config_load 次"
    echo "配置的DNS服务器数量: $dns_server_count 个"
    
    if [ $smart_dns_init -gt 0 ]; then
        echo "✅ 智能DNS管理器初始化成功"
    else
        echo "❌ 智能DNS管理器初始化失败"
    fi
    
    if [ $dns_config_load -gt 0 ]; then
        echo "✅ DNS服务器配置加载成功"
    else
        echo "❌ DNS服务器配置加载失败"
    fi
    
    echo
    echo "--- 智能DNS检查分析 ---"
    
    # 检查智能DNS检查
    intelligent_check_count=$(grep -c "开始智能DNS检查\|智能DNS检查完成" "$LOG_DIR/dns_system.log" 2>/dev/null || echo "0")
    dns_test_count=$(grep -c "测试DNS服务器" "$LOG_DIR/dns_system.log" 2>/dev/null || echo "0")
    dns_resolution_check=$(grep -c "检查DNS解析是否正常工作" "$LOG_DIR/dns_system.log" 2>/dev/null || echo "0")
    
    echo "智能DNS检查执行次数: $intelligent_check_count"
    echo "DNS服务器测试次数: $dns_test_count"
    echo "DNS解析状态检查次数: $dns_resolution_check"
    
    if [ $intelligent_check_count -gt 0 ]; then
        echo "✅ 智能DNS检查正常执行"
    else
        echo "❌ 智能DNS检查未执行"
    fi
    
    if [ $dns_test_count -gt 0 ]; then
        echo "✅ DNS服务器测试正常工作"
    else
        echo "❌ DNS服务器测试未工作"
    fi
    
    echo
    echo "--- DNS服务器管理分析 ---"
    
    # 检查DNS服务器统计更新
    stats_update_count=$(grep -c "更新DNS服务器.*统计" "$LOG_DIR/dns_system.log" 2>/dev/null || echo "0")
    priority_update_count=$(grep -c "更新DNS服务器优先级\|优先级调整" "$LOG_DIR/dns_system.log" 2>/dev/null || echo "0")
    dns_switch_count=$(grep -c "切换到最佳DNS服务器\|成功切换到DNS服务器" "$LOG_DIR/dns_system.log" 2>/dev/null || echo "0")
    
    echo "DNS服务器统计更新次数: $stats_update_count"
    echo "DNS优先级更新次数: $priority_update_count"
    echo "DNS服务器切换次数: $dns_switch_count"
    
    if [ $stats_update_count -gt 0 ]; then
        echo "✅ DNS服务器统计更新正常"
    else
        echo "ℹ️ DNS服务器统计更新可能在DEBUG级别"
    fi
    
    if [ $dns_switch_count -gt 0 ]; then
        echo "✅ DNS服务器智能切换功能正常"
    else
        echo "ℹ️ 未触发DNS服务器切换（可能当前DNS正常）"
    fi
    
    echo
    echo "--- 关键功能验证 ---"
    
    # 检查定时器功能
    dns_timer_count=$(grep -c "智能DNS检查定时器触发" "$LOG_DIR/dns_system.log" 2>/dev/null || echo "0")
    
    echo "智能DNS检查定时器触发次数: $dns_timer_count"
    
    if [ $dns_timer_count -gt 0 ]; then
        echo "✅ 智能DNS检查定时器正常工作"
    else
        echo "❌ 智能DNS检查定时器未工作"
    fi
    
    # 显示一些关键日志示例
    echo
    echo "--- 关键日志示例 ---"
    
    echo "DNS管理器初始化日志:"
    grep "智能DNS管理器\|DNS服务器配置" "$LOG_DIR/dns_system.log" | head -3
    
    echo
    echo "DNS服务器配置日志:"
    grep "DNS服务器:.*优先级" "$LOG_DIR/dns_system.log" | head -5
    
    echo
    echo "智能DNS检查日志:"
    grep "智能DNS检查\|DNS解析.*工作" "$LOG_DIR/dns_system.log" | head -5
    
    echo
    echo "DNS服务器测试日志:"
    grep "测试DNS服务器.*结果" "$LOG_DIR/dns_system.log" | head -3
    
else
    echo "❌ 系统日志文件不存在"
fi

echo

# 总体评估
echo "5. 智能DNS管理系统总体评估..."

score=0
max_score=6

# 评分标准
if [ $smart_dns_init -gt 0 ]; then ((score++)); fi
if [ $dns_config_load -gt 0 ]; then ((score++)); fi
if [ $intelligent_check_count -gt 0 ]; then ((score++)); fi
if [ $dns_test_count -gt 0 ]; then ((score++)); fi
if [ $dns_timer_count -gt 0 ]; then ((score++)); fi
if [ "$dns_server_count" -gt 0 ]; then ((score++)); fi

echo "智能DNS管理系统评分: $score/$max_score"

if [ $score -eq $max_score ]; then
    echo "🎉 智能DNS管理系统完全成功！"
    echo "  ✅ 智能DNS管理器初始化成功"
    echo "  ✅ DNS服务器配置加载完整"
    echo "  ✅ 智能DNS检查正常执行"
    echo "  ✅ DNS服务器测试功能正常"
    echo "  ✅ 定时检查机制工作正常"
    echo "  ✅ DNS服务器优先级管理有效"
elif [ $score -ge 4 ]; then
    echo "✅ 智能DNS管理系统基本成功"
    echo "  • 大部分功能正常"
    echo "  • 可能需要微调"
else
    echo "❌ 智能DNS管理系统需要改进"
    echo "  • 请检查日志和配置"
fi

echo

# 显示智能DNS管理优势
echo "6. 智能DNS管理系统优势..."
echo "✅ 定时DNS状态检查:"
echo "   • 定期检查DNS解析是否正常工作"
echo "   • 自动检测DNS异常并触发切换"
echo "   • 防止DNS故障影响网络连接"

echo
echo "✅ 配置化DNS服务器管理:"
echo "   • 从配置文件加载DNS服务器列表"
echo "   • 支持主要、备用、备份三级DNS服务器"
echo "   • 每个DNS服务器都有独立的优先级"

echo
echo "✅ 智能DNS服务器选择:"
echo "   • 按优先级逐个测试DNS服务器"
echo "   • 自动切换到最佳可用DNS服务器"
echo "   • 支持DNS服务器可用性检测"

echo
echo "✅ 加权优先级更新:"
echo "   • 根据成功率动态调整DNS服务器优先级"
echo "   • 高成功率的DNS服务器优先级提升"
echo "   • 低成功率的DNS服务器优先级降低"

echo
echo "✅ 统计信息管理:"
echo "   • 记录每个DNS服务器的成功率"
echo "   • 跟踪连续失败次数"
echo "   • 监控平均响应时间"

echo
echo "详细日志文件:"
echo "  DNS管理系统: $LOG_DIR/dns_system.log"
echo "  测试参数文件: $LOG_DIR/intelligent_dns_params.yaml"

echo
echo "🎊 智能DNS管理系统测试完成！"

# 清理进程
pkill -f "network_manager_node" 2>/dev/null
