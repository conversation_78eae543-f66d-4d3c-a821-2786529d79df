#!/bin/bash

# 运行 C++ 延迟测试程序

echo "=== 运行 ROS2 C++ 延迟测试 ==="

# 设置环境
export ROS_DISTRO=humble
export LD_LIBRARY_PATH=/opt/ros/humble/lib:$LD_LIBRARY_PATH

# 启动 ROS2 daemon
echo "启动 ROS2 daemon..."
ros2 daemon start

# 等待 daemon 启动
sleep 2

echo "运行延迟测试程序..."
echo "注意：程序会自动测试不同频率的延迟"
echo

# 运行测试程序，限制运行时间
timeout 60s ./latency_test_node

if [ $? -eq 124 ]; then
    echo "测试超时，程序运行超过60秒"
elif [ $? -eq 0 ]; then
    echo "✅ 测试完成"
else
    echo "❌ 测试出现错误"
fi

echo
echo "=== C++ 延迟测试完成 ==="
