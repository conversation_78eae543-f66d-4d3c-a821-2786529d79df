#!/bin/bash

# 简化的测试套件 - 专门测试整理后的测试脚本
# 运行 tests/ 目录中的所有测试

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_TESTS++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_TESTS++))
}

log_header() {
    echo -e "${PURPLE}=== $1 ===${NC}"
}

# 运行单个测试的函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local test_description="$3"
    
    ((TOTAL_TESTS++))
    
    log_header "$test_name"
    log_info "$test_description"
    
    if eval "$test_command"; then
        log_success "$test_name 通过"
        return 0
    else
        log_error "$test_name 失败"
        return 1
    fi
}

# 主测试流程
main() {
    log_header "测试脚本整理验证"
    log_info "验证整理后的测试脚本..."
    echo
    
    # 检查是否在正确的目录
    if [ ! -f "README.md" ] || [ ! -d "src" ]; then
        log_error "请在项目根目录中运行此脚本"
        exit 1
    fi
    
    # 检查测试目录
    if [ ! -d "tests" ]; then
        log_error "tests/ 目录不存在"
        exit 1
    fi
    
    echo
    
    # 1. 配置验证测试
    if [ -f "tests/config-validation-test.sh" ]; then
        run_test "配置验证测试" \
                 "./tests/config-validation-test.sh > /dev/null 2>&1" \
                 "验证配置文件存在性和语法正确性"
    else
        log_error "配置验证测试脚本不存在"
        ((TOTAL_TESTS++))
        ((FAILED_TESTS++))
    fi
    
    echo
    
    # 2. 调试配置测试
    if [ -f "tests/debug-config-test.sh" ]; then
        run_test "调试配置测试" \
                 "./tests/debug-config-test.sh > /dev/null 2>&1" \
                 "验证 VSCode 调试配置和环境设置"
    else
        log_error "调试配置测试脚本不存在"
        ((TOTAL_TESTS++))
        ((FAILED_TESTS++))
    fi
    
    echo
    
    # 3. 网络接口测试
    if [ -f "tests/network-interface-test.cpp" ]; then
        run_test "网络接口测试" \
                 "cd tests && g++ -o network-interface-test network-interface-test.cpp && ./network-interface-test > /dev/null 2>&1 && rm -f network-interface-test" \
                 "测试网络接口类型识别和配置映射"
    else
        log_error "网络接口测试文件不存在"
        ((TOTAL_TESTS++))
        ((FAILED_TESTS++))
    fi
    
    echo
    
    # 4. 测试目录结构验证
    ((TOTAL_TESTS++))
    log_header "测试目录结构验证"
    log_info "验证测试目录结构完整性"
    if [ -f 'tests/README.md' ] && [ -f 'tests/run-all-tests.sh' ] && [ -f 'tests/run-tests.sh' ]; then
        log_success "测试目录结构验证 通过"
    else
        log_error "测试目录结构验证 失败"
        ((FAILED_TESTS++))
    fi

    echo

    # 5. 测试脚本权限验证
    ((TOTAL_TESTS++))
    log_header "测试脚本权限验证"
    log_info "验证测试脚本具有执行权限"
    if [ -x 'tests/config-validation-test.sh' ] && [ -x 'tests/debug-config-test.sh' ] && [ -x 'tests/run-all-tests.sh' ] && [ -x 'tests/run-tests.sh' ]; then
        log_success "测试脚本权限验证 通过"
    else
        log_error "测试脚本权限验证 失败"
        ((FAILED_TESTS++))
    fi
    
    echo
    
    # 测试结果汇总
    log_header "测试结果汇总"
    echo -e "${BLUE}总测试数:${NC} $TOTAL_TESTS"
    echo -e "${GREEN}通过测试:${NC} $PASSED_TESTS"
    echo -e "${RED}失败测试:${NC} $FAILED_TESTS"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}🎉 所有测试通过！测试脚本整理成功！${NC}"
        echo
        echo "测试脚本已成功整理到 tests/ 目录："
        echo "  ✅ tests/config-validation-test.sh - 配置验证测试"
        echo "  ✅ tests/debug-config-test.sh - 调试配置测试"
        echo "  ✅ tests/network-interface-test.cpp - 网络接口测试"
        echo "  ✅ tests/run-all-tests.sh - 完整测试套件"
        echo "  ✅ tests/README.md - 测试文档"
        echo
        echo "使用方法："
        echo "  ./tests/run-tests.sh          # 运行简化测试套件"
        echo "  ./tests/run-all-tests.sh      # 运行完整测试套件"
        echo "  ./tests/config-validation-test.sh  # 单独运行配置测试"
        echo "  ./tests/debug-config-test.sh       # 单独运行调试测试"
        exit 0
    else
        echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败${NC}"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "测试脚本整理验证工具"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -v, --verbose  详细输出模式"
    echo
    echo "功能:"
    echo "  验证测试脚本是否已正确整理到 tests/ 目录"
    echo "  运行基本的测试验证"
    echo "  检查测试目录结构和权限"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            set -x
            shift
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 运行主程序
main
