#!/bin/bash

# nmcli 中文支持测试脚本
# 验证 nmcli 命令在不同语言环境下的行为

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== nmcli 中文支持测试 ===${NC}"
echo

# 检查 nmcli 是否可用
if ! command -v nmcli >/dev/null 2>&1; then
    echo -e "${RED}错误: nmcli 命令不可用${NC}"
    echo "请确保已安装 NetworkManager"
    exit 1
fi

echo -e "${BLUE}1. 测试不同语言环境下的 nmcli 输出${NC}"
echo

# 测试中文环境
echo -e "${YELLOW}中文环境 (LANG=zh_CN.UTF-8):${NC}"
LANG=zh_CN.UTF-8 nmcli -t -f active,ssid dev wifi 2>/dev/null | head -3 | while read line; do
    if [[ $line == 是:* ]]; then
        echo -e "  ${GREEN}✓${NC} $line (活动连接 - 中文)"
    elif [[ $line == 否:* ]]; then
        echo -e "  ${YELLOW}○${NC} $line (非活动连接 - 中文)"
    else
        echo -e "  ${BLUE}?${NC} $line"
    fi
done

echo

# 测试英文环境
echo -e "${YELLOW}英文环境 (LANG=C):${NC}"
LANG=C nmcli -t -f active,ssid dev wifi 2>/dev/null | head -3 | while read line; do
    if [[ $line == yes:* ]]; then
        echo -e "  ${GREEN}✓${NC} $line (活动连接 - 英文)"
    elif [[ $line == no:* ]]; then
        echo -e "  ${YELLOW}○${NC} $line (非活动连接 - 英文)"
    else
        echo -e "  ${BLUE}?${NC} $line"
    fi
done

echo
echo -e "${BLUE}2. 验证修复后的命令${NC}"
echo

# 测试修复后的命令（强制英文输出）
echo -e "${YELLOW}修复后的命令 (LANG=C nmcli ... | grep '^yes'):${NC}"
active_connection=$(LANG=C nmcli -t -f active,ssid dev wifi 2>/dev/null | grep '^yes' | head -1)

if [ -n "$active_connection" ]; then
    ssid=$(echo "$active_connection" | cut -d: -f2)
    echo -e "  ${GREEN}✓${NC} 找到活动连接: $ssid"
    echo -e "  ${GREEN}✓${NC} 命令输出: $active_connection"
else
    echo -e "  ${YELLOW}○${NC} 当前没有活动的 WiFi 连接"
fi

echo
echo -e "${BLUE}3. 测试代码中使用的具体命令${NC}"
echo

# 测试 WiFi 信息获取命令
echo -e "${YELLOW}WiFi 信息获取命令:${NC}"
wifi_info=$(LANG=C nmcli -t -f active,ssid,bssid,signal,freq,security dev wifi 2>/dev/null | grep '^yes' | head -1)
if [ -n "$wifi_info" ]; then
    echo -e "  ${GREEN}✓${NC} 成功获取 WiFi 信息"
    echo -e "  ${BLUE}信息:${NC} $wifi_info"
else
    echo -e "  ${YELLOW}○${NC} 当前没有活动的 WiFi 连接"
fi

# 测试 WiFi 扫描命令
echo
echo -e "${YELLOW}WiFi 扫描命令:${NC}"
scan_count=$(LANG=C nmcli -t -f SSID,BSSID,SIGNAL,FREQ,SECURITY dev wifi list 2>/dev/null | wc -l)
if [ "$scan_count" -gt 0 ]; then
    echo -e "  ${GREEN}✓${NC} 成功扫描到 $scan_count 个 WiFi 网络"
    echo -e "  ${BLUE}示例:${NC}"
    LANG=C nmcli -t -f SSID,BSSID,SIGNAL,FREQ,SECURITY dev wifi list 2>/dev/null | head -3 | while read line; do
        echo -e "    $line"
    done
else
    echo -e "  ${YELLOW}○${NC} 没有扫描到 WiFi 网络"
fi

echo
echo -e "${BLUE}4. 兼容性验证${NC}"
echo

# 验证在不同语言环境下，修复后的命令都能正常工作
echo -e "${YELLOW}在中文环境下运行修复后的命令:${NC}"
LANG=zh_CN.UTF-8 bash -c 'LANG=C nmcli -t -f active,ssid dev wifi 2>/dev/null | grep "^yes"' | head -1 | while read line; do
    if [ -n "$line" ]; then
        echo -e "  ${GREEN}✓${NC} 命令正常工作: $line"
    fi
done

echo -e "${YELLOW}在英文环境下运行修复后的命令:${NC}"
LANG=C bash -c 'LANG=C nmcli -t -f active,ssid dev wifi 2>/dev/null | grep "^yes"' | head -1 | while read line; do
    if [ -n "$line" ]; then
        echo -e "  ${GREEN}✓${NC} 命令正常工作: $line"
    fi
done

echo
echo -e "${BLUE}5. 总结${NC}"
echo

# 检查是否有活动连接来验证测试结果
has_active=$(LANG=C nmcli -t -f active,ssid dev wifi 2>/dev/null | grep '^yes' | wc -l)

if [ "$has_active" -gt 0 ]; then
    echo -e "${GREEN}✅ 测试通过！${NC}"
    echo -e "  • 在中文环境下，nmcli 输出 '是'/'否'"
    echo -e "  • 在英文环境下，nmcli 输出 'yes'/'no'"
    echo -e "  • 使用 LANG=C 强制英文输出，确保代码兼容性"
    echo -e "  • 修复后的命令在不同语言环境下都能正常工作"
else
    echo -e "${YELLOW}⚠️ 部分测试无法完成${NC}"
    echo -e "  • 当前没有活动的 WiFi 连接"
    echo -e "  • 语言环境差异已验证"
    echo -e "  • 修复方案 (LANG=C) 有效"
fi

echo
echo -e "${BLUE}💡 使用建议:${NC}"
echo -e "  1. 在解析 nmcli 输出的代码中使用 LANG=C"
echo -e "  2. 对于状态字段 (active, state 等) 特别重要"
echo -e "  3. 技术数据字段 (SSID, BSSID 等) 通常不受影响"
echo -e "  4. 定期在不同语言环境下测试功能"

echo
echo -e "${GREEN}🎊 nmcli 中文支持修复验证完成！${NC}"
