cmake_minimum_required(VERSION 3.8)
project(latency_test)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)

# 创建延迟测试可执行文件
add_executable(latency_test_node latency_test_node.cpp)
add_executable(simple_latency_test simple_latency_test.cpp)

ament_target_dependencies(latency_test_node
  rclcpp
  std_msgs
)

ament_target_dependencies(simple_latency_test
  rclcpp
  std_msgs
)

# 安装可执行文件
install(TARGETS
  latency_test_node
  simple_latency_test
  DESTINATION lib/${PROJECT_NAME}
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
