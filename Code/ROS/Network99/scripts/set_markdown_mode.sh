#!/bin/bash

# Markdown默认打开方式设置脚本

SETTINGS_FILE=".vscode/settings.json"

show_help() {
    echo "用法: $0 [模式]"
    echo ""
    echo "模式选项:"
    echo "  preview  - 默认预览模式 (推荐用于阅读文档)"
    echo "  editor   - 默认编辑模式 (推荐用于编写文档)"
    echo "  remove   - 移除设置，使用VSCode默认行为"
    echo ""
    echo "示例:"
    echo "  $0 preview   # 设置为预览模式"
    echo "  $0 editor    # 设置为编辑模式"
    echo "  $0 remove    # 移除自定义设置"
}

set_preview_mode() {
    echo "设置Markdown默认为预览模式..."
    
    # 备份原文件
    cp "$SETTINGS_FILE" "$SETTINGS_FILE.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 更新设置
    if grep -q '"workbench.editorAssociations"' "$SETTINGS_FILE"; then
        # 如果已存在，则更新
        sed -i 's/"workbench.editorAssociations": {[^}]*}/"workbench.editorAssociations": {\n\t\t"*.md": "vscode.markdown.preview.editor"\n\t}/' "$SETTINGS_FILE"
    else
        # 如果不存在，则添加
        sed -i '/},$/i\\t"workbench.editorAssociations": {\n\t\t"*.md": "vscode.markdown.preview.editor"\n\t},' "$SETTINGS_FILE"
    fi
    
    echo "✓ 已设置为预览模式"
}

set_editor_mode() {
    echo "设置Markdown默认为编辑模式..."
    
    # 备份原文件
    cp "$SETTINGS_FILE" "$SETTINGS_FILE.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 移除workbench.editorAssociations中的md设置
    sed -i '/"workbench.editorAssociations": {/,/}/d' "$SETTINGS_FILE"
    
    echo "✓ 已设置为编辑模式"
}

remove_setting() {
    echo "移除Markdown默认打开设置..."
    
    # 备份原文件
    cp "$SETTINGS_FILE" "$SETTINGS_FILE.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 移除相关设置
    sed -i '/"workbench.editorAssociations": {/,/}/d' "$SETTINGS_FILE"
    
    echo "✓ 已移除自定义设置，使用VSCode默认行为"
}

# 主逻辑
case "$1" in
    "preview")
        set_preview_mode
        ;;
    "editor")
        set_editor_mode
        ;;
    "remove")
        remove_setting
        ;;
    "-h"|"--help"|"")
        show_help
        ;;
    *)
        echo "错误: 未知模式 '$1'"
        echo ""
        show_help
        exit 1
        ;;
esac
