#!/bin/bash

# 设置日志输出
exec > >(tee /var/log/network_manager.log) 2>&1

echo "[$(date)] 网络管理器服务启动..."

# 获取当前系统架构
CURRENT_ARCH=$(uname -m)
echo "[$(date)] 检测到当前架构: $CURRENT_ARCH"

# 工作目录
WORK_DIR="/opt/network99"
cd "$WORK_DIR" || {
    echo "[$(date)] 错误：无法进入工作目录 $WORK_DIR"
    exit 1
}

# Source ROS Humble 环境
if [ -f "/opt/ros/humble/setup.bash" ]; then
    source /opt/ros/humble/setup.bash
    echo "[$(date)] ✓ ROS2 环境已加载: $ROS_DISTRO"
else
    echo "[$(date)] ✗ 错误：未找到 ROS Humble 安装"
    exit 1
fi

# 检查环境变量
echo "[$(date)] 环境变量检查:"
echo "  ROS_DISTRO: $ROS_DISTRO"
echo "  PYTHONPATH: $PYTHONPATH"
echo "  AMENT_PREFIX_PATH: $AMENT_PREFIX_PATH"

# Source 工作空间环境
WORKSPACE_SETUP="$WORK_DIR/install/setup.bash"
if [ -f "$WORKSPACE_SETUP" ]; then
    source "$WORKSPACE_SETUP"
    echo "[$(date)] ✓ 工作空间环境已加载"
else
    echo "[$(date)] ✗ 错误：未找到工作空间安装文件: $WORKSPACE_SETUP"
    exit 1
fi

# 从配置文件中读取日志级别
CONFIG_FILE="$WORK_DIR/share/gen3_network_manager_core/config/network_config.yaml"
LOG_LEVEL="debug"  # 默认值

if [ -f "$CONFIG_FILE" ]; then
    echo "[$(date)] 尝试从配置文件读取日志级别: $CONFIG_FILE"
    # 使用grep和sed提取日志级别
    EXTRACTED_LOG_LEVEL=$(grep "log_level:" "$CONFIG_FILE" | head -n 1 | sed 's/.*log_level:[[:space:]]*"\([^"]*\)".*/\1/')
    if [ -n "$EXTRACTED_LOG_LEVEL" ]; then
        LOG_LEVEL="$EXTRACTED_LOG_LEVEL"
        echo "[$(date)] ✓ 从配置文件读取到日志级别: $LOG_LEVEL"
    else
        echo "[$(date)] ⚠ 无法从配置文件读取日志级别，使用默认值: $LOG_LEVEL"
    fi
else
    echo "[$(date)] ⚠ 配置文件不存在，使用默认日志级别: $LOG_LEVEL"
fi

# 设置ROS2日志级别环境变量
export RCUTILS_CONSOLE_OUTPUT_FORMAT='[{severity}] [{time}] [{name}]: {message}'
export RCUTILS_COLORIZED_OUTPUT=1
export RCUTILS_LOGGING_USE_STDOUT=1
export RCUTILS_LOGGING_BUFFERED_STREAM=1

# 明确设置日志级别环境变量
echo "[$(date)] 设置日志级别环境变量为: $LOG_LEVEL"
export RCUTILS_LOGGING_LEVEL=$LOG_LEVEL

# 检查可执行文件是否存在
EXECUTABLE="$WORK_DIR/bin/network_manager_node"
if [ -f "$EXECUTABLE" ]; then
    echo "[$(date)] ✓ 找到网络管理器节点可执行文件"
    echo "[$(date)] 正在启动网络管理器节点..."
    echo "----------------------------------------"

    # 显示环境信息
    echo "[$(date)] 架构: $CURRENT_ARCH"
    echo "[$(date)] ROS版本: $ROS_DISTRO"
    echo "[$(date)] 可执行文件: $EXECUTABLE"
    echo "[$(date)] 配置文件: $CONFIG_FILE"
    echo "[$(date)] 日志级别: $LOG_LEVEL"
    echo "----------------------------------------"

    # 直接运行可执行文件，并明确指定日志级别和配置文件
    exec "$EXECUTABLE" --ros-args --log-level "$LOG_LEVEL" --params-file "$CONFIG_FILE"
else
    # 尝试查找其他可能的位置
    echo "[$(date)] ✗ 错误：未找到网络管理器节点可执行文件: $EXECUTABLE"
    echo "[$(date)] 尝试查找其他可能的位置..."
    
    # 检查安装目录下的可执行文件
    ALTERNATIVE_EXECUTABLE="$WORK_DIR/install/gen3_network_manager_core/lib/gen3_network_manager_core/network_manager_node"
    if [ -f "$ALTERNATIVE_EXECUTABLE" ]; then
        echo "[$(date)] ✓ 找到替代可执行文件位置: $ALTERNATIVE_EXECUTABLE"
        echo "[$(date)] 正在启动网络管理器节点..."
        echo "----------------------------------------"
        
        # 显示环境信息
        echo "[$(date)] 架构: $CURRENT_ARCH"
        echo "[$(date)] ROS版本: $ROS_DISTRO"
        echo "[$(date)] 可执行文件: $ALTERNATIVE_EXECUTABLE"
        echo "[$(date)] 配置文件: $CONFIG_FILE"
        echo "[$(date)] 日志级别: $LOG_LEVEL"
        echo "----------------------------------------"
        
        # 运行网络管理器节点，并明确指定日志级别和配置文件
        exec "$ALTERNATIVE_EXECUTABLE" --ros-args --log-level "$LOG_LEVEL" --params-file "$CONFIG_FILE"
    else
        echo "[$(date)] ✗ 错误：未能找到任何可用的可执行文件"
        exit 1
    fi
fi 