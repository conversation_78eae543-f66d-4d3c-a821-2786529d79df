#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认安装路径
INSTALL_DIR="/opt/network99"
CONFIG_FILE="$INSTALL_DIR/share/gen3_network_manager_core/config/network_config.yaml"
DEV_CONFIG_FILE="$(dirname "$(dirname "$(realpath "$0")")")/src/gen3_network_manager_core/config/network_config.yaml"

# 有效的日志级别
VALID_LEVELS=("debug" "info" "warn" "error" "fatal")

# 显示帮助信息
function show_help() {
    echo -e "${BLUE}网络管理器日志级别更新工具${NC}"
    echo ""
    echo "使用方法: $0 [日志级别]"
    echo ""
    echo "可用的日志级别:"
    echo "  debug  - 调试级别，显示所有日志"
    echo "  info   - 信息级别，显示一般信息（默认）"
    echo "  warn   - 警告级别，只显示警告和错误"
    echo "  error  - 错误级别，只显示错误"
    echo "  fatal  - 严重错误级别，只显示严重错误"
    echo ""
    echo "示例:"
    echo "  $0 debug  # 设置为调试级别"
    echo "  $0 info   # 设置为信息级别"
    echo ""
    echo "当前日志级别:"
    
    # 尝试读取当前日志级别
    read_current_level
}

# 读取当前日志级别
function read_current_level() {
    local config_file=""
    
    # 先检查部署版本
    if [ -f "$CONFIG_FILE" ]; then
        config_file="$CONFIG_FILE"
        echo -e "  ${YELLOW}[部署版本]${NC}"
    # 再检查开发版本
    elif [ -f "$DEV_CONFIG_FILE" ]; then
        config_file="$DEV_CONFIG_FILE"
        echo -e "  ${YELLOW}[开发版本]${NC}"
    else
        echo -e "  ${RED}未找到配置文件${NC}"
        return 1
    fi
    
    # 读取当前级别
    local current_level=$(grep "log_level:" "$config_file" | head -n 1 | sed 's/.*log_level:[[:space:]]*"\([^"]*\)".*/\1/')
    if [ -n "$current_level" ]; then
        echo -e "  当前日志级别: ${GREEN}$current_level${NC}"
    else
        echo -e "  ${RED}无法读取当前日志级别${NC}"
        return 1
    fi
    
    return 0
}

# 验证日志级别是否有效
function is_valid_level() {
    local level="$1"
    for valid_level in "${VALID_LEVELS[@]}"; do
        if [ "$level" == "$valid_level" ]; then
            return 0
        fi
    done
    return 1
}

# 更新日志级别
function update_log_level() {
    local new_level="$1"
    local config_files=()
    
    # 添加找到的配置文件
    if [ -f "$CONFIG_FILE" ]; then
        config_files+=("$CONFIG_FILE")
    fi
    
    if [ -f "$DEV_CONFIG_FILE" ]; then
        config_files+=("$DEV_CONFIG_FILE")
    fi
    
    if [ ${#config_files[@]} -eq 0 ]; then
        echo -e "${RED}错误: 未找到任何配置文件${NC}"
        echo -e "请确认网络管理器已正确安装或处于开发环境中"
        return 1
    fi
    
    # 更新所有找到的配置文件
    for config_file in "${config_files[@]}"; do
        echo -e "${BLUE}更新配置文件: $config_file${NC}"
        
        # 备份原文件
        cp "$config_file" "${config_file}.bak"
        
        # 更新日志级别（只更新第一个匹配项，即顶部的日志级别）
        sed -i '0,/log_level:[[:space:]]*"[^"]*"/s//log_level: "'$new_level'"/' "$config_file"
        
        echo -e "${GREEN}✓ 已更新日志级别为: $new_level${NC}"
    done
    
    # 如果服务正在运行，提示重启
    if systemctl is-active --quiet network_manager; then
        echo -e "${YELLOW}注意: 网络管理器服务正在运行${NC}"
        echo -e "要应用新的日志级别，请重启服务:"
        echo -e "  ${GREEN}sudo systemctl restart network_manager${NC}"
    fi
    
    return 0
}

# 主函数
if [ $# -eq 0 ]; then
    show_help
    exit 0
fi

# 获取用户指定的日志级别
new_level="$1"

# 检查日志级别是否有效
if ! is_valid_level "$new_level"; then
    echo -e "${RED}错误: 无效的日志级别 '$new_level'${NC}"
    echo -e "有效的日志级别: ${VALID_LEVELS[*]}"
    exit 1
fi

# 更新日志级别
update_log_level "$new_level"
exit $? 