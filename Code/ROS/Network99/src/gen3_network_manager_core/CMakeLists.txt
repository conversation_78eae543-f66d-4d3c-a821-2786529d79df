cmake_minimum_required(VERSION 3.5)
project(gen3_network_manager_core)

# 默认编译为C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# 查找依赖包
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_action REQUIRED)
find_package(rclcpp_components REQUIRED)
find_package(std_msgs REQUIRED)
find_package(std_srvs REQUIRED)
find_package(gen3_network_interfaces REQUIRED)

# 包含目录
include_directories(
  include
)

# 添加可执行文件
add_executable(network_manager_node
  src/network_manager_node.cpp
  src/network_manager.cpp
  src/wifi_manager.cpp
  src/network_switch.cpp
  src/binding_manager.cpp
  src/network_monitor.cpp
  src/dns_manager.cpp
  src/utils/network_utils.cpp
  src/utils/enum_utils.cpp
  src/utils/iptables_utils.cpp
  src/cellular_manager.cpp
  src/nr90_serial_comm.cpp
  src/usb_at_comm.cpp
)

# 添加依赖项
ament_target_dependencies(network_manager_node
  rclcpp
  rclcpp_action
  rclcpp_components
  std_msgs
  std_srvs
  gen3_network_interfaces
)

# 添加uuid库链接
target_link_libraries(network_manager_node uuid)

# 安装目标
install(TARGETS
  network_manager_node
  DESTINATION lib/${PROJECT_NAME}
)

# 安装启动文件
install(DIRECTORY
  launch
  DESTINATION share/${PROJECT_NAME}/
)

# 安装配置文件
install(DIRECTORY
  config
  DESTINATION share/${PROJECT_NAME}/
)

# 导出依赖项
ament_export_dependencies(rclcpp)
ament_export_dependencies(rclcpp_action)
ament_export_dependencies(rclcpp_components)
ament_export_dependencies(gen3_network_interfaces)
ament_export_include_directories(include)

ament_package() 