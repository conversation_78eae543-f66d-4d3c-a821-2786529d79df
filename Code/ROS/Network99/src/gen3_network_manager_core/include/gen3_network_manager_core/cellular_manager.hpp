// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef GEN3_NETWORK_MANAGER_CORE__CELLULAR_MANAGER_HPP_
#define GEN3_NETWORK_MANAGER_CORE__CELLULAR_MANAGER_HPP_

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <mutex>
#include <atomic>
#include <thread>
#include <chrono>

#include "rclcpp/rclcpp.hpp"
#include "std_msgs/msg/string.hpp"

#include "gen3_network_interfaces/msg/network_status.hpp"
#include "gen3_network_interfaces/msg/network_quality.hpp"
#include "gen3_network_interfaces/srv/get_fiveg_info.hpp"

namespace gen3_network_manager_core
{

// 蜂窝网络信息结构体
struct CellularNetworkInfo {
  std::string operator_name;      // 运营商名称
  std::string network_type;       // 网络类型 (5G, 4G, 3G等)
  std::string registration_status; // 注册状态
  int32_t signal_strength;        // 信号强度 (dBm)
  int32_t signal_quality;         // 信号质量 (0-100%)
  uint32_t frequency;             // 频率 (MHz)
  std::string cell_id;            // 小区ID
  std::string imsi;               // IMSI
  std::string imei;               // IMEI
  bool is_connected;              // 是否已连接
  std::string ip_address;         // IP地址
  std::string device_path;        // 设备路径
};

// 蜂窝模块状态枚举
enum class CellularModuleStatus {
  UNKNOWN = 0,
  INITIALIZING = 1,
  READY = 2,
  CONNECTING = 3,
  CONNECTED = 4,
  DISCONNECTED = 5,
  ERROR = 6
};

class CellularManager
{
public:
  explicit CellularManager(const rclcpp::Node::SharedPtr & node);
  ~CellularManager();

  // 初始化函数
  bool init();

  // 5G模块控制
  bool start_module();
  bool stop_module();
  bool restart_module();

  // 网络连接控制
  bool connect_network();
  bool disconnect_network();

  // 信息获取
  CellularNetworkInfo get_network_info();
  CellularModuleStatus get_module_status();
  
  // 信号强度获取
  int32_t get_signal_strength();
  int32_t get_signal_quality();

  // 网络状态检查
  bool is_module_ready();
  bool is_network_connected();
  bool test_connectivity();

private:
  // ROS节点
  rclcpp::Node::SharedPtr node_;
  
  // 日志记录器
  rclcpp::Logger logger_;

  // 发布者
  rclcpp::Publisher<gen3_network_interfaces::msg::NetworkStatus>::SharedPtr status_pub_;
  rclcpp::Publisher<gen3_network_interfaces::msg::NetworkQuality>::SharedPtr quality_pub_;
  rclcpp::Publisher<std_msgs::msg::String>::SharedPtr debug_pub_;

  // 服务
  rclcpp::Service<gen3_network_interfaces::srv::GetFivegInfo>::SharedPtr get_info_srv_;

  // 定时器
  rclcpp::TimerBase::SharedPtr status_timer_;
  rclcpp::TimerBase::SharedPtr quality_timer_;

  // 内部状态
  std::mutex status_mutex_;
  std::atomic<CellularModuleStatus> module_status_;
  CellularNetworkInfo current_info_;
  
  // 配置参数
  std::string device_path_;
  int baud_rate_;
  double status_update_interval_;
  double quality_update_interval_;
  
  // 串口通信相关
  int serial_fd_;
  bool is_serial_open_;
  
  // 后台监控线程
  std::thread monitor_thread_;
  std::atomic<bool> monitor_running_;

  // 回调函数
  void status_timer_callback();
  void quality_timer_callback();
  void handle_get_info(
    const std::shared_ptr<gen3_network_interfaces::srv::GetFivegInfo::Request> request,
    std::shared_ptr<gen3_network_interfaces::srv::GetFivegInfo::Response> response);

  // 串口通信函数
  bool open_serial_port();
  void close_serial_port();
  bool send_at_command(const std::string & command, std::string & response, int timeout_ms = 3000);
  
  // AT命令处理函数
  bool check_module_ready();
  bool get_device_info();
  bool get_network_registration_status();
  bool get_signal_info();
  bool get_network_operator();
  
  // 状态更新函数
  void update_module_status();
  void update_network_info();
  void publish_status();
  void publish_quality();
  
  // 监控线程函数
  void monitor_thread_function();
  
  // 工具函数
  std::string module_status_to_string(CellularModuleStatus status);
  bool parse_signal_strength(const std::string & response, int32_t & strength);
  bool parse_network_registration(const std::string & response, std::string & status);
  bool parse_operator_info(const std::string & response, std::string & operator_name);
};

}  // namespace gen3_network_manager_core

#endif  // GEN3_NETWORK_MANAGER_CORE__FIVEG_MANAGER_HPP_
