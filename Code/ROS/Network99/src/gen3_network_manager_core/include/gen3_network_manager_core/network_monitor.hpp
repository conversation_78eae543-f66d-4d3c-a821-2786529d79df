// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef GEN3_NETWORK_MANAGER_CORE__NETWORK_MONITOR_HPP_
#define GEN3_NETWORK_MANAGER_CORE__NETWORK_MONITOR_HPP_

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <mutex>
#include <chrono>
#include <atomic>
#include <future>
#include <thread>

#include "rclcpp/rclcpp.hpp"
#include "std_msgs/msg/string.hpp"

#include "gen3_network_interfaces/msg/network_status.hpp"
#include "gen3_network_interfaces/msg/network_quality.hpp"
#include "gen3_network_interfaces/msg/binding_status.hpp"

namespace gen3_network_manager_core
{

class NetworkMonitor
{
public:
  explicit NetworkMonitor(const rclcpp::Node::SharedPtr & node);
  ~NetworkMonitor();

  // 初始化函数
  bool init();

private:
  // ROS节点
  rclcpp::Node::SharedPtr node_;
  
  // 日志记录器
  rclcpp::Logger logger_;

  // 发布者
  rclcpp::Publisher<gen3_network_interfaces::msg::NetworkStatus>::SharedPtr network_status_broadcast_pub_;
  rclcpp::Publisher<std_msgs::msg::String>::SharedPtr connection_status_pub_;
  rclcpp::Publisher<std_msgs::msg::String>::SharedPtr board_status_pub_;

  // 订阅者
  rclcpp::Subscription<gen3_network_interfaces::msg::NetworkStatus>::SharedPtr network_status_sub_;
  rclcpp::Subscription<gen3_network_interfaces::msg::NetworkQuality>::SharedPtr network_quality_sub_;
  rclcpp::Subscription<gen3_network_interfaces::msg::BindingStatus>::SharedPtr binding_status_sub_;

  // 定时器
  rclcpp::TimerBase::SharedPtr monitor_timer_;
  rclcpp::TimerBase::SharedPtr connectivity_check_timer_;
  
  // 内部状态
  std::mutex monitor_mutex_;
  gen3_network_interfaces::msg::NetworkStatus current_status_;
  std::map<std::string, gen3_network_interfaces::msg::NetworkQuality> network_quality_map_;

  struct ConnectionStats {
    bool is_connected;
    std::chrono::steady_clock::time_point last_connected_time;
    std::chrono::steady_clock::time_point last_disconnected_time;
    int disconnect_count;
    int connect_count;
    double uptime_percentage;
  };

  std::map<std::string, ConnectionStats> connection_stats_;

  // 异步连通性检查状态
  std::atomic<bool> last_connectivity_result_{false};
  std::atomic<bool> last_board_connectivity_result_{true};
  std::atomic<bool> connectivity_check_in_progress_{false};
  std::atomic<bool> board_connectivity_check_in_progress_{false};

  // 异步任务相关
  std::future<bool> connectivity_future_;
  std::future<bool> board_connectivity_future_;

  // 连通性检查参数缓存（在init中获取，避免重复获取）
  std::string dns_server_;
  std::string internet_server_;
  int dns_timeout_ms_;
  int ping_timeout_ms_;
  int gateway_timeout_ms_;
  int external_timeout_ms_;
  
  // 回调函数
  void network_status_callback(const gen3_network_interfaces::msg::NetworkStatus::SharedPtr msg);
  void network_quality_callback(const gen3_network_interfaces::msg::NetworkQuality::SharedPtr msg);
  void binding_status_callback(const gen3_network_interfaces::msg::BindingStatus::SharedPtr msg);
  void monitor_timer_callback();
  void connectivity_check_timer_callback();
  
  // 辅助函数
  bool check_connectivity();
  bool check_board_connectivity();
  void update_connection_stats(const std::string & interface, bool is_connected);
  void publish_connection_status(const std::string & status);
  void publish_board_status(const std::string & status);
  void broadcast_network_status(bool mutex_already_locked = false);

  // 异步检查辅助函数
  void start_async_connectivity_check();
  void start_async_board_connectivity_check();
  void check_async_results();

  // WiFi路由完整性检查
  void check_wifi_route_integrity();
  bool reconnect_wifi_to_fix_route(const std::string & ssid);
};

}  // namespace gen3_network_manager_core

#endif  // GEN3_NETWORK_MANAGER_CORE__NETWORK_MONITOR_HPP_ 