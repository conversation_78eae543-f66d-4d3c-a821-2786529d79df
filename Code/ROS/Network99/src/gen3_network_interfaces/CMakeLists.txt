cmake_minimum_required(VERSION 3.5)
project(gen3_network_interfaces)

# 默认编译为C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# 查找依赖包
find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(builtin_interfaces REQUIRED)
find_package(std_msgs REQUIRED)

# 定义接口文件
set(msg_files
  "msg/NetworkStatus.msg"
  "msg/NetworkQuality.msg"
  "msg/WiFiNetwork.msg"
  "msg/BindingStatus.msg"
)

set(srv_files
  "srv/SwitchNetwork.srv"
  "srv/ConnectWiFi.srv"
  "srv/GetNetworkList.srv"
  "srv/SetNetworkPriority.srv"
  "srv/StartBinding.srv"
  "srv/GetFivegInfo.srv"
)

set(action_files
  "action/NetworkBinding.action"
)

# 生成接口
rosidl_generate_interfaces(${PROJECT_NAME}
  ${msg_files}
  ${srv_files}
  ${action_files}
  DEPENDENCIES builtin_interfaces std_msgs
)

ament_export_dependencies(rosidl_default_runtime)
ament_package() 