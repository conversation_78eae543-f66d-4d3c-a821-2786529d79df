# 网络类型常量
uint8 NETWORK_TYPE_AUTO = 0    # 自动选择最优网络
uint8 NETWORK_TYPE_WIFI = 1    # 强制使用WiFi
uint8 NETWORK_TYPE_5G = 2      # 强制使用5G
uint8 NETWORK_TYPE_ETHERNET = 3 # 强制使用以太网

# 请求
uint8 target_network_type      # 目标网络类型
string target_wifi_ssid        # 目标WiFi (仅当类型为WiFi时)
bool force_switch              # 是否强制切换 (忽略质量检测)
uint32 timeout_seconds         # 切换超时时间

---

# 响应
bool success                   # 切换是否成功
string message                 # 结果消息
string error_code              # 错误代码

# 切换前网络信息
uint8 previous_network_type    # 切换前网络类型
string previous_interface      # 切换前接口名称
string previous_ip_address     # 切换前IP地址 