# 列表类型常量
uint8 LIST_TYPE_AVAILABLE = 1  # 可用网络列表
uint8 LIST_TYPE_SAVED = 2      # 已保存网络列表
uint8 LIST_TYPE_ALL = 3        # 所有网络列表

# 请求
uint8 list_type                # 列表类型
bool include_signal_strength   # 是否包含信号强度
bool include_connection_history # 是否包含连接历史
bool force_scan                # 是否强制重新扫描

---

# 响应
bool success                   # 查询是否成功
string message                 # 结果消息

# 网络列表
WiFiNetwork[] available_networks  # 可用网络列表
WiFiNetwork[] saved_networks      # 已保存网络列表

# 扫描信息
builtin_interfaces/Time scan_time # 扫描时间
uint32 total_networks_found       # 发现的网络总数
uint32 scan_duration_ms           # 扫描耗时 