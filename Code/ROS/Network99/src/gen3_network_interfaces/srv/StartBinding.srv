# 绑定方式常量
uint8 BINDING_METHOD_BLE = 1
uint8 BINDING_METHOD_QR_CODE = 2
uint8 BINDING_METHOD_BOTH = 3

# 请求
uint8 binding_method           # 绑定方式
uint32 timeout_seconds         # 超时时间
string device_name             # 设备名称
bool reset_existing_binding    # 是否重置现有绑定

---

# 响应
bool success                   # 启动是否成功
string message                 # 结果消息
string error_code              # 错误代码

# 绑定会话信息
string session_id              # 绑定会话ID
builtin_interfaces/Time session_start_time # 会话开始时间
uint32 estimated_duration_seconds # 预计绑定时长 