# 网络类型常量
uint8 NETWORK_TYPE_UNKNOWN = 0
uint8 NETWORK_TYPE_WIFI = 1
uint8 NETWORK_TYPE_5G = 2
uint8 NETWORK_TYPE_ETHERNET = 3

# 连接状态常量
uint8 CONNECTION_STATUS_DISCONNECTED = 0
uint8 CONNECTION_STATUS_CONNECTING = 1
uint8 CONNECTION_STATUS_CONNECTED = 2
uint8 CONNECTION_STATUS_SWITCHING = 3
uint8 CONNECTION_STATUS_ERROR = 4

# 基本信息
std_msgs/Header header
uint8 network_type              # 当前网络类型
uint8 connection_status         # 连接状态
string interface_name           # 网络接口名称
string ip_address              # IP地址
string gateway                 # 网关地址
string[] dns_servers           # DNS服务器列表

# WiFi特定信息
string wifi_ssid               # WiFi网络名称
int32 wifi_signal_strength     # WiFi信号强度 (dBm)
string wifi_security_type      # 加密类型

# 5G特定信息
string fiveg_operator_name     # 5G运营商名称
int32 fiveg_signal_strength    # 5G信号强度 (dBm)
string fiveg_network_type      # 5G网络制式 (LTE/SA/NR)