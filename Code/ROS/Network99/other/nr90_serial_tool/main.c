#include "nr90_serial_comm.h"
#include <getopt.h>

// 显示帮助信息
void show_help(const char* program_name) {
    printf("NR90 5G设备串口通信测试工具\n");
    printf("\n");
    printf("用法: %s [选项]\n", program_name);
    printf("\n");
    printf("选项:\n");
    printf("  -d, --device PATH     指定ttyUSB设备路径 (默认: %s)\n", DEFAULT_DEVICE);
    printf("  -b, --baud RATE       指定波特率 (默认: 115200)\n");
    printf("  -t, --timeout SEC     指定命令超时时间 (默认: %d秒)\n", DEFAULT_TIMEOUT);
    printf("  -v, --verbose         启用详细输出 (调试模式)\n");
    printf("  -q, --quiet           静默模式 (仅输出错误)\n");
    printf("  -c, --command CMD     执行单个AT命令\n");
    printf("  -l, --list            列出所有可用的ttyUSB设备\n");
    printf("  -h, --help            显示此帮助信息\n");
    printf("\n");
    printf("示例:\n");
    printf("  %s                              # 使用默认设备进行完整测试\n", program_name);
    printf("  %s -d /dev/ttyUSB1             # 使用指定设备\n", program_name);
    printf("  %s -c \"AT+COPS?\"               # 执行单个AT命令\n", program_name);
    printf("  %s -v                          # 启用详细输出\n", program_name);
    printf("  %s -l                          # 列出可用设备\n", program_name);
    printf("\n");
    printf("返回值:\n");
    printf("  0   - 测试成功\n");
    printf("  1   - 测试失败或设备错误\n");
    printf("  2   - 参数错误\n");
    printf("  130 - 被信号中断\n");
    printf("\n");
    printf("特性:\n");
    printf("  - 支持NR90 5G模组的AT命令通信\n");
    printf("  - 自动检测和配置串口参数\n");
    printf("  - 完整的5G网络状态检测\n");
    printf("  - 支持信号中断和安全退出\n");
    printf("  - 详细的错误处理和日志记录\n");
}

// 列出可用的ttyUSB设备
void list_ttyusb_devices(void) {
    printf("搜索可用的ttyUSB设备...\n");
    
    // 使用系统命令列出设备
    int result = system("ls -la /dev/ttyUSB* 2>/dev/null | while read line; do echo \"  $line\"; done || echo \"  未找到ttyUSB设备\"");
    (void)result; // 忽略返回值警告
    
    printf("\n");
    printf("提示: 请确保设备已连接并且您有足够的权限访问设备\n");
    printf("如果设备不可访问，可能需要将用户添加到dialout组:\n");
    printf("  sudo usermod -a -G dialout $USER\n");
    printf("然后重新登录或使用 newgrp dialout\n");
}

// 执行单个AT命令
int execute_single_command(nr90_device_t* device, const char* command) {
    nr90_log(LOG_INFO, "执行单个AT命令: %s", command);
    
    nr90_response_t response;
    nr90_error_t result = nr90_send_at_command(device, command, &response, DEFAULT_TIMEOUT);
    
    if (result != NR90_SUCCESS) {
        nr90_log(LOG_ERROR, "命令执行失败: %s", nr90_error_string(result));
        return 1;
    }
    
    printf("\n");
    nr90_print_response(&response);
    
    if (response.is_ok) {
        nr90_log(LOG_INFO, "命令执行成功");
        return 0;
    } else if (response.is_error) {
        nr90_log(LOG_WARNING, "设备返回错误响应");
        return 1;
    } else {
        nr90_log(LOG_INFO, "收到响应");
        return 0;
    }
}

// 主函数
int main(int argc, char* argv[]) {
    // 默认参数
    char device_path[MAX_DEVICE_PATH] = DEFAULT_DEVICE;
    int baud_rate = 115200;
    int timeout = DEFAULT_TIMEOUT;
    char* single_command = NULL;
    int list_devices = 0;
    int verbose = 0;
    int quiet = 0;
    
    // 命令行选项定义
    static struct option long_options[] = {
        {"device",   required_argument, 0, 'd'},
        {"baud",     required_argument, 0, 'b'},
        {"timeout",  required_argument, 0, 't'},
        {"verbose",  no_argument,       0, 'v'},
        {"quiet",    no_argument,       0, 'q'},
        {"command",  required_argument, 0, 'c'},
        {"list",     no_argument,       0, 'l'},
        {"help",     no_argument,       0, 'h'},
        {0, 0, 0, 0}
    };
    
    // 解析命令行参数
    int option_index = 0;
    int c;
    
    while ((c = getopt_long(argc, argv, "d:b:t:vqc:lh", long_options, &option_index)) != -1) {
        switch (c) {
            case 'd':
                strncpy(device_path, optarg, MAX_DEVICE_PATH - 1);
                device_path[MAX_DEVICE_PATH - 1] = '\0';
                break;
            case 'b':
                baud_rate = atoi(optarg);
                if (baud_rate <= 0) {
                    fprintf(stderr, "错误: 无效的波特率: %s\n", optarg);
                    return 2;
                }
                break;
            case 't':
                timeout = atoi(optarg);
                if (timeout <= 0) {
                    fprintf(stderr, "错误: 无效的超时时间: %s\n", optarg);
                    return 2;
                }
                break;
            case 'v':
                verbose = 1;
                break;
            case 'q':
                quiet = 1;
                break;
            case 'c':
                single_command = optarg;
                break;
            case 'l':
                list_devices = 1;
                break;
            case 'h':
                show_help(argv[0]);
                return 0;
            case '?':
                fprintf(stderr, "使用 %s -h 查看帮助信息\n", argv[0]);
                return 2;
            default:
                abort();
        }
    }
    
    // 设置日志级别
    if (verbose) {
        nr90_set_log_level(LOG_DEBUG);
    } else if (quiet) {
        nr90_set_log_level(LOG_ERROR);
    } else {
        nr90_set_log_level(LOG_INFO);
    }
    
    // 如果只是列出设备，直接执行并退出
    if (list_devices) {
        list_ttyusb_devices();
        return 0;
    }
    
    // 设置信号处理
    nr90_setup_signal_handlers();
    
    // 初始化设备
    nr90_device_t device;
    nr90_error_t result = nr90_device_init(&device, device_path);
    if (result != NR90_SUCCESS) {
        nr90_log(LOG_ERROR, "设备初始化失败: %s", nr90_error_string(result));
        return 1;
    }
    
    // 设置波特率（如果指定了非默认值）
    if (baud_rate != 115200) {
        switch (baud_rate) {
            case 9600:   device.baud_rate = B9600; break;
            case 19200:  device.baud_rate = B19200; break;
            case 38400:  device.baud_rate = B38400; break;
            case 57600:  device.baud_rate = B57600; break;
            case 115200: device.baud_rate = B115200; break;
            case 230400: device.baud_rate = B230400; break;
            case 460800: device.baud_rate = B460800; break;
            case 921600: device.baud_rate = B921600; break;
            default:
                nr90_log(LOG_ERROR, "不支持的波特率: %d", baud_rate);
                return 2;
        }
        nr90_log(LOG_INFO, "使用波特率: %d", baud_rate);
    }
    
    int exit_code = 0;
    
    // 打开设备
    result = nr90_device_open(&device);
    if (result != NR90_SUCCESS) {
        nr90_log(LOG_ERROR, "设备打开失败: %s", nr90_error_string(result));
        exit_code = 1;
        goto cleanup;
    }
    
    // 配置设备
    result = nr90_device_configure(&device);
    if (result != NR90_SUCCESS) {
        nr90_log(LOG_ERROR, "设备配置失败: %s", nr90_error_string(result));
        exit_code = 1;
        goto cleanup;
    }
    
    // 执行测试或命令
    if (single_command) {
        // 执行单个命令
        exit_code = execute_single_command(&device, single_command);
    } else {
        // 执行完整测试
        result = nr90_run_full_test(&device);
        if (result == NR90_SUCCESS) {
            exit_code = 0;
        } else if (result == NR90_ERROR_RESPONSE_TIMEOUT && g_interrupted) {
            exit_code = 130; // 被信号中断
        } else {
            exit_code = 1;
        }
    }
    
cleanup:
    // 清理资源
    nr90_device_cleanup(&device);
    
    if (g_interrupted) {
        nr90_log(LOG_INFO, "程序被用户中断");
        exit_code = 130;
    }
    
    return exit_code;
}
