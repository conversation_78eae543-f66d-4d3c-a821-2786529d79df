{
    "recommendations": [
        // C/C++ 开发
        "ms-vscode.cpptools",
        "ms-vscode.cpptools-extension-pack",
        "ms-vscode.cmake-tools",
        "twxs.cmake",
        
        // Python 开发
        "ms-python.python",
        "ms-python.pylint",
        "ms-python.black-formatter",
        
        // ROS 开发
        "ms-iot.vscode-ros",
        "smilerobotics.urdf",
        
        // Git 和版本控制
        "eamodio.gitlens",
        "mhutchie.git-graph",
        
        // 调试和测试
        "vadimcn.vscode-lldb",
        "ms-vscode.test-adapter-converter",
        
        // 代码质量
        "ms-vscode.vscode-clangd",
        "llvm-vs-code-extensions.vscode-clangd",
        "cpptools-extension-pack",
        
        // 文档和标记
        "yzhang.markdown-all-in-one",
        "shd101wyy.markdown-preview-enhanced",
        "redhat.vscode-xml",
        "redhat.vscode-yaml",
        
        // 实用工具
        "ms-vscode.hexeditor",
        "streetsidesoftware.code-spell-checker",
        "gruntfuggly.todo-tree",
        "alefragnani.bookmarks",
        "ms-vscode.vscode-json",
        
        // 主题和界面
        "pkief.material-icon-theme",
        "zhuangtongfa.material-theme",
        
        // 终端和 Shell
        "ms-vscode.powershell",
        "timonwong.shellcheck"
    ],
    "unwantedRecommendations": [
        "ms-vscode.vscode-typescript-next"
    ]
}
