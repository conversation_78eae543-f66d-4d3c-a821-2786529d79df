{
	// Python 路径设置
	"python.autoComplete.extraPaths": [
		"${workspaceFolder}/install/gen3_network_interfaces/lib/python3.8/site-packages",
		"${workspaceFolder}/install/gen3_network_interfaces/lib/python3.10/site-packages",
		"/opt/ros/humble/lib/python3.10/site-packages",
		"/opt/ros/humble/local/lib/python3.10/dist-packages"
	],
	"python.analysis.extraPaths": [
		"${workspaceFolder}/install/gen3_network_interfaces/lib/python3.8/site-packages",
		"${workspaceFolder}/install/gen3_network_interfaces/lib/python3.10/site-packages",
		"/opt/ros/humble/lib/python3.10/site-packages",
		"/opt/ros/humble/local/lib/python3.10/dist-packages"
	],
	"python.defaultInterpreterPath": "/usr/bin/python3",
	"python.linting.enabled": true,
	"python.linting.pylintEnabled": true,
	"python.formatting.provider": "black",

	// C/C++ 设置
	"C_Cpp.default.intelliSenseMode": "linux-gcc-x64",
	"C_Cpp.default.compilerPath": "/usr/bin/gcc",
	"C_Cpp.default.cppStandard": "gnu++17",
	"C_Cpp.default.cStandard": "gnu17",
	"C_Cpp.intelliSenseEngine": "default",
	"C_Cpp.errorSquiggles": "enabled",
	"C_Cpp.autocomplete": "default",
	"C_Cpp.suggestSnippets": true,

	// CMake 设置
	"cmake.buildDirectory": "${workspaceFolder}/build",
	"cmake.installPrefix": "${workspaceFolder}/install",
	"cmake.configureOnOpen": false,
	"cmake.buildBeforeRun": true,

	// 文件关联
	"files.associations": {
		"*.cmake": "cmake",
		"*.vim": "lua",
		"*.launch": "xml",
		"*.launch.py": "python",
		"*.urdf": "xml",
		"*.xacro": "xml",
		"*.sdf": "xml",
		"*.world": "xml",
		"*.msg": "plaintext",
		"*.srv": "plaintext",
		"*.action": "plaintext",
		"*.cfg": "python",
		"CMakeLists.txt": "cmake",
		"cctype": "cpp",
		"clocale": "cpp",
		"cmath": "cpp",
		"cstdarg": "cpp",
		"cstddef": "cpp",
		"cstdio": "cpp",
		"cstdlib": "cpp",
		"cstring": "cpp",
		"ctime": "cpp",
		"cwchar": "cpp",
		"cwctype": "cpp",
		"array": "cpp",
		"atomic": "cpp",
		"strstream": "cpp",
		"bit": "cpp",
		"bitset": "cpp",
		"chrono": "cpp",
		"compare": "cpp",
		"complex": "cpp",
		"concepts": "cpp",
		"condition_variable": "cpp",
		"cstdint": "cpp",
		"deque": "cpp",
		"list": "cpp",
		"map": "cpp",
		"set": "cpp",
		"unordered_map": "cpp",
		"vector": "cpp",
		"exception": "cpp",
		"algorithm": "cpp",
		"functional": "cpp",
		"iterator": "cpp",
		"memory": "cpp",
		"memory_resource": "cpp",
		"numeric": "cpp",
		"optional": "cpp",
		"random": "cpp",
		"ratio": "cpp",
		"regex": "cpp",
		"string": "cpp",
		"string_view": "cpp",
		"system_error": "cpp",
		"tuple": "cpp",
		"type_traits": "cpp",
		"utility": "cpp",
		"fstream": "cpp",
		"initializer_list": "cpp",
		"iomanip": "cpp",
		"iosfwd": "cpp",
		"iostream": "cpp",
		"istream": "cpp",
		"limits": "cpp",
		"mutex": "cpp",
		"new": "cpp",
		"ostream": "cpp",
		"ranges": "cpp",
		"shared_mutex": "cpp",
		"sstream": "cpp",
		"stdexcept": "cpp",
		"stop_token": "cpp",
		"streambuf": "cpp",
		"thread": "cpp",
		"cfenv": "cpp",
		"cinttypes": "cpp",
		"typeindex": "cpp",
		"typeinfo": "cpp",
		"variant": "cpp"
	},

	// 编辑器设置
	"editor.tabSize": 4,
	"editor.insertSpaces": true,
	"editor.detectIndentation": true,
	"editor.rulers": [80, 120],
	"editor.wordWrap": "on",
	"editor.minimap.enabled": true,

	// 终端设置
	"terminal.integrated.shell.linux": "/bin/bash",
	"terminal.integrated.env.linux": {
		"ROS_DOMAIN_ID": "0"
	},

	// 搜索设置
	"search.exclude": {
		"**/build/**": true,
		"**/install/**": true,
		"**/log/**": true,
		"**/.git/**": true,
		"**/node_modules/**": true
	},

	// 文件监视设置
	"files.watcherExclude": {
		"**/build/**": true,
		"**/install/**": true,
		"**/log/**": true,
		"**/.git/**": true
	},

	// Git 设置
	"git.ignoreLimitWarning": true,

	// ROS 相关设置
	"ros.distro": "humble",
	"colcon.provideTasks": true,

	// 调试设置
	"debug.allowBreakpointsEverywhere": true,
	"debug.showBreakpointsInOverviewRuler": true,

	// Markdown 设置
	"markdown.preview.doubleClickToSwitchToEditor": false,
	"markdown.preview.markEditorSelection": true,
	"markdown.preview.scrollPreviewWithEditor": true,
	"markdown.preview.scrollEditorWithPreview": true,
	"markdown.preview.fontSize": 14,
	"markdown.preview.lineHeight": 1.6,
	"markdown.preview.fontFamily": "system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
	// 选择Markdown默认打开方式：
	// 选项1: 默认预览模式 - "vscode.markdown.preview.editor"
	// 选项2: 默认编辑模式 - "default"
	// 选项3: 并排模式 - 不设置workbench.editorAssociations，使用命令面板
	"workbench.editorAssociations": {
		"*.md": "vscode.markdown.preview.editor"
	},

	// 用户自定义颜色主题
	"workbench.colorCustomizations": {
		"statusBar.noFolderBackground": "#005f5f",
		"statusBar.debuggingBackground": "#005f5f",
		"statusBar.debuggingForeground": "#ffffff",
		"editor.selectionBackground": "#f54813",
		"editor.selectionHighlightBackground": "#139bf5",
		"editorCursor.foreground": "#ff0015",
		"terminalCursor.foreground": "#FF0000"
	}
}