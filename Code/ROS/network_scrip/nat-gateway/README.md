# 智能NAT网关系统

一个功能完整的智能NAT网关解决方案，为局域网设备提供网络地址转换(NAT)服务，实现共享上网功能。

## 🎯 功能特性

- 🔍 **智能检测**: 自动检测网络接口和内网网段
- 🔧 **灵活配置**: 支持手动配置和自动检测模式
- 🛡️ **安全可靠**: 完善的错误处理和日志记录
- 📊 **实时监控**: 提供详细的状态监控和性能统计
- 💾 **备份恢复**: 完整的配置备份和恢复功能
- 🎨 **友好界面**: 彩色输出和清晰的状态显示
- ⚙️ **系统集成**: 支持systemd服务管理
- 🧪 **全面测试**: 内置完整的功能测试套件
- 🔄 **外网切换**: 支持外网接口的手动和自动切换 🆕
- 📈 **智能评分**: 基于连通性、类型、速度的接口评分系统 🆕
- 🚨 **故障转移**: 自动故障检测和接口切换 🆕

## 📁 文件结构

```
nat-gateway/
├── nat-gateway.sh          # 主控制脚本
├── nat-service.sh          # NAT服务核心脚本 (支持外网切换)
├── wan-manager.sh          # 外网接口管理器 🆕
├── install.sh              # 系统安装脚本
├── config.sh               # 配置管理脚本
├── monitor.sh              # 实时监控脚本
├── test.sh                 # 功能测试脚本
├── backup.sh               # 备份恢复脚本
├── quick-install.sh        # 快速安装脚本
├── demo-wan-switch.sh      # 外网切换功能演示脚本 🆕
└── README.md               # 说明文档
```

## 🚀 快速开始

### 方式一：在源码目录使用（推荐）

```bash
# 进入NAT网关目录
cd nat-gateway

# 查看帮助
./nat-gateway.sh help

# 启动NAT服务
sudo ./nat-gateway.sh start

# 查看服务状态
./nat-gateway.sh status

# 实时监控
./nat-gateway.sh monitor

# 测试功能
./nat-gateway.sh test
```

### 方式二：系统安装版本

```bash
# 安装系统服务
sudo ./install.sh install

# 查看安装状态
sudo ./install.sh status

# 使用系统命令
sudo nat-gateway start
nat-gateway status
nat-gateway monitor
nat-gateway test
```

### 快速一键安装

```bash
# 一键安装和配置
sudo ./quick-install.sh
```

## 📋 详细功能

### 1. NAT服务管理

```bash
# 启动NAT服务
nat-gateway start

# 停止NAT服务
nat-gateway stop

# 重启NAT服务
nat-gateway restart

# 查看服务状态
nat-gateway status
```

### 2. 配置管理

```bash
# 在源码目录中使用
./nat-gateway.sh config show           # 显示当前配置
sudo ./nat-gateway.sh config edit      # 编辑配置文件
./nat-gateway.sh config validate       # 验证配置
sudo ./nat-gateway.sh config set AUTO_DETECT 1  # 设置配置项

# 系统安装版本使用
nat-gateway config show
sudo nat-gateway config edit
nat-gateway config validate
sudo nat-gateway config set AUTO_DETECT 1

# 直接使用配置脚本
./config.sh show                       # 显示配置
sudo ./config.sh edit                  # 编辑配置
./config.sh validate                   # 验证配置
sudo ./config.sh set AUTO_DETECT 1     # 设置配置项
```

### 3. 实时监控

```bash
# 在源码目录中使用
./nat-gateway.sh monitor               # 启动实时监控
./monitor.sh once                      # 显示一次状态
./monitor.sh interval 10               # 自定义监控间隔

# 系统安装版本使用
nat-gateway monitor                    # 启动实时监控

# 注意：如果系统版本监控脚本找不到，请在源码目录中运行
```

### 4. 功能测试

```bash
# 在源码目录中使用
./nat-gateway.sh test                  # 运行所有测试
./test.sh connectivity                 # 测试网络连通性
./test.sh system                       # 测试系统环境
./test.sh iptables                     # 测试iptables规则

# 系统安装版本使用
nat-gateway test                       # 运行所有测试

# 注意：如果系统版本测试脚本找不到，请在源码目录中运行
```

### 5. 外网接口管理 🆕

```bash
# 在源码目录中使用
sudo ./nat-gateway.sh list-wan        # 列出可用外网接口
sudo ./nat-gateway.sh auto-wan        # 自动选择最佳外网接口
sudo ./nat-gateway.sh switch-wan      # 交互式切换外网接口
sudo ./nat-gateway.sh switch-wan eth0 # 切换到指定接口
sudo ./nat-gateway.sh force-wan wlan0 # 强制切换接口

# 系统安装版本使用
sudo nat-gateway list-wan
sudo nat-gateway auto-wan
sudo nat-gateway switch-wan
sudo nat-gateway switch-wan eth0
sudo nat-gateway force-wan wlan0
```

### 6. 高级外网接口管理

```bash
# 使用专门的外网接口管理器（在源码目录中）
./wan-manager.sh info          # 显示详细接口信息
./wan-manager.sh monitor       # 实时监控接口状态
./wan-manager.sh monitor 10    # 10秒间隔监控
./wan-manager.sh test          # 测试所有接口连通性
./wan-manager.sh compare       # 比较接口性能
sudo ./wan-manager.sh failover # 自动故障转移
./wan-manager.sh current       # 显示当前活动接口

# 演示外网切换功能
./demo-wan-switch.sh           # 功能演示脚本
```

### 7. 备份恢复

```bash
# 在源码目录中使用
sudo ./nat-gateway.sh backup          # 创建备份
sudo ./nat-gateway.sh restore         # 恢复备份
./backup.sh list                      # 列出备份
./backup.sh verify                    # 验证备份

# 系统安装版本使用
sudo nat-gateway backup
sudo nat-gateway restore

# 注意：如果系统版本备份脚本找不到，请在源码目录中运行
```

## ⚙️ 配置说明

### 主配置文件

配置文件位置: `/etc/network/nat-gateway.conf`

```bash
# 自动检测网络接口 (1=启用, 0=禁用)
AUTO_DETECT=1

# 手动指定外网接口 (当AUTO_DETECT=0时使用)
WAN_INTERFACES="eth0 wlan0"

# 手动指定内网网段 (当AUTO_DETECT=0时使用)
LAN_NETWORKS="***********/24 ***********/24"

# 启用IP转发
IP_FORWARD_ENABLED=1

# 日志级别 (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL=INFO

# 启动时测试连通性
TEST_CONNECTIVITY=1

# 备份保留天数
BACKUP_RETENTION_DAYS=30

# 监控间隔(秒)
MONITOR_INTERVAL=60

# 自动重启失败的服务
AUTO_RESTART=1
```

### 网络架构示例

```
Internet
    |
    | (外网接口: eth0/wlan0)
    |
┌───▼───┐
│ NAT   │ <- 当前设备 (网关)
│ 网关  │
└───┬───┘
    |
    | (内网接口: eth1)
    |
┌───▼───────────────────┐
│ 局域网设备             │
│ ***********/24       │ <- 局域网设备
│ ***********/24       │
└───────────────────────┘
```

## 🛠️ 高级功能

### 智能外网接口管理 🆕

#### 自动评分机制
系统根据以下标准为外网接口评分：

| 评分项目 | 权重 | 说明 |
|---------|------|------|
| 默认路由 | 40分 | 接口是否有默认路由 |
| 连通性测试 | 30分 | 能否访问外网测试主机 |
| 接口类型 | 20分 | 有线网络优先级最高 |
| 接口速度 | 10分 | 千兆网络加分最多 |

#### 接口类型优先级
- **有线网络 (eth\*)**: 20分 - 最高优先级
- **新式有线 (enp\*)**: 18分 - 高优先级
- **另一种有线 (ens\*)**: 16分 - 较高优先级
- **无线网络 (wlan\*)**: 10分 - 中等优先级
- **移动网络 (wwan\*)**: 5分 - 较低优先级

#### 智能切换策略
- **自动模式**: 根据评分自动选择最佳接口
- **手动模式**: 用户交互式选择或直接指定
- **强制模式**: 跳过连通性检查，强制切换
- **故障转移**: 检测到故障时自动切换到备用接口

### 自动检测模式

系统会自动检测:
- 有默认路由的接口作为外网接口
- 私有IP网段作为内网网段
- 自动配置相应的NAT规则

### 手动配置模式

当自动检测不满足需求时，可以手动指定:
- 外网接口列表
- 内网网段列表
- 自定义NAT规则

### 系统服务集成

```bash
# 查看服务状态
systemctl status nat-gateway

# 启动/停止服务
systemctl start nat-gateway
systemctl stop nat-gateway

# 启用/禁用开机自启
systemctl enable nat-gateway
systemctl disable nat-gateway

# 查看服务日志
journalctl -u nat-gateway -f
```

## 📊 监控指标

### 系统信息
- 系统运行时间和负载
- 内存使用情况
- 网络接口状态

### NAT状态
- IP转发状态
- iptables规则数量
- 连接跟踪统计

### 网络统计
- 接口流量统计
- NAT处理包数和字节数
- 活动连接数

### 连通性测试
- 本地回环测试
- 网关连通性测试
- 外网连接测试
- DNS解析测试

## 🧪 测试套件

### 测试类型

1. **系统环境测试**: 检查权限、工具、依赖
2. **网络接口测试**: 验证接口状态和配置
3. **IP转发测试**: 检查内核转发功能
4. **iptables规则测试**: 验证NAT规则配置
5. **连通性测试**: 测试网络连接
6. **DNS解析测试**: 验证域名解析
7. **服务状态测试**: 检查systemd服务
8. **性能测试**: 评估规则查询性能

### 测试结果

- ✓ PASS: 测试通过
- ✗ FAIL: 测试失败
- ⚠ WARN: 警告信息
- ℹ INFO: 信息提示

## 💾 备份策略

### 备份内容

- iptables规则 (所有表)
- NAT网关配置文件
- 网络接口信息
- 系统信息
- 备份清单和校验和

### 备份管理

- 自动生成时间戳
- 支持备份验证
- 自动清理旧备份
- 交互式恢复

## ⚠️ 注意事项

1. **权限要求**: 需要root权限运行
2. **网络中断**: 修改规则可能导致短暂网络中断
3. **防火墙冲突**: 可能与其他防火墙软件冲突
4. **内核支持**: 需要内核支持iptables和连接跟踪
5. **备份重要**: 修改前建议先备份当前配置

## 💡 使用建议

### 推荐使用方式
1. **源码目录运行**: 推荐在源码目录中直接运行脚本，功能最完整
2. **系统安装**: 适合生产环境，但某些高级功能可能需要在源码目录中运行
3. **权限管理**: 只有需要修改系统配置的操作才需要sudo权限

### 脚本路径说明
- **源码目录**: `./nat-gateway.sh`, `./wan-manager.sh` 等
- **系统安装**: `nat-gateway`, `natgw` (快捷命令)
- **如果系统版本找不到某些脚本**: 请回到源码目录运行

## 🔧 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查日志
   journalctl -u nat-gateway -n 50
   
   # 手动测试
   nat-gateway test
   ```

2. **网络不通**
   ```bash
   # 检查IP转发
   cat /proc/sys/net/ipv4/ip_forward
   
   # 检查iptables规则
   iptables -t nat -L -n -v
   ```

3. **配置错误**
   ```bash
   # 验证配置
   nat-gateway config validate
   
   # 重置配置
   nat-gateway config reset
   ```

### 恢复操作

```bash
# 从备份恢复
nat-gateway restore

# 重置为默认配置
nat-gateway config reset

# 重新安装
sudo ./install.sh uninstall
sudo ./install.sh install
```

## 📈 更新日志

- v1.0: 初始版本
  - 智能网络检测
  - 自动NAT配置
  - 实时监控
  - 完整测试套件
  - 备份恢复功能
  - systemd集成

## 🤝 贡献

欢迎提交问题和改进建议！

## 📄 许可证

MIT License
