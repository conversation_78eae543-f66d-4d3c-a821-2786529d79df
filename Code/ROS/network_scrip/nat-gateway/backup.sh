#!/bin/bash

# =============================================================================
# NAT网关备份和恢复脚本
# 功能: 备份和恢复NAT网关的配置和规则
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 备份目录
BACKUP_DIR="/etc/network/backups"
CONFIG_FILE="/etc/network/nat-gateway.conf"
LOG_FILE="/var/log/iptables-nat.log"

# 日志函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}"
}

error_exit() {
    log_message "ERROR" "$1"
    echo -e "${RED}错误: $1${NC}" >&2
    exit 1
}

success_message() {
    log_message "INFO" "$1"
    echo -e "${GREEN}✓ $1${NC}"
}

info_message() {
    log_message "INFO" "$1"
    echo -e "${BLUE}ℹ $1${NC}"
}

warning_message() {
    log_message "WARN" "$1"
    echo -e "${YELLOW}⚠ $1${NC}"
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error_exit "此脚本需要root权限运行，请使用 sudo"
    fi
}

# 创建备份目录
create_backup_dir() {
    mkdir -p "$BACKUP_DIR"
    if [[ ! -d "$BACKUP_DIR" ]]; then
        error_exit "无法创建备份目录: $BACKUP_DIR"
    fi
}

# 生成备份文件名
generate_backup_name() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    echo "nat-gateway_backup_$timestamp"
}

# 备份iptables规则
backup_iptables() {
    local backup_name=$1
    local iptables_file="$BACKUP_DIR/${backup_name}_iptables.rules"
    
    info_message "备份iptables规则..."
    
    # 备份所有表的规则
    {
        echo "# iptables规则备份"
        echo "# 备份时间: $(date)"
        echo "# 系统: $(uname -a)"
        echo ""
        
        echo "# === FILTER表 ==="
        iptables-save -t filter
        echo ""
        
        echo "# === NAT表 ==="
        iptables-save -t nat
        echo ""
        
        echo "# === MANGLE表 ==="
        iptables-save -t mangle
        echo ""
        
        echo "# === RAW表 ==="
        iptables-save -t raw 2>/dev/null || echo "# RAW表不可用"
        
    } > "$iptables_file"
    
    if [[ -f "$iptables_file" ]]; then
        success_message "iptables规则已备份到: $iptables_file"
        return 0
    else
        error_exit "iptables规则备份失败"
    fi
}

# 备份配置文件
backup_config() {
    local backup_name=$1
    local config_backup="$BACKUP_DIR/${backup_name}_config.conf"
    
    info_message "备份配置文件..."
    
    if [[ -f "$CONFIG_FILE" ]]; then
        cp "$CONFIG_FILE" "$config_backup"
        success_message "配置文件已备份到: $config_backup"
    else
        warning_message "配置文件不存在: $CONFIG_FILE"
        echo "# 配置文件不存在" > "$config_backup"
    fi
}

# 备份网络信息
backup_network_info() {
    local backup_name=$1
    local network_file="$BACKUP_DIR/${backup_name}_network.info"
    
    info_message "备份网络信息..."
    
    {
        echo "# 网络信息备份"
        echo "# 备份时间: $(date)"
        echo ""
        
        echo "# === 网络接口 ==="
        ip addr show
        echo ""
        
        echo "# === 路由表 ==="
        ip route show
        echo ""
        
        echo "# === 路由表详细 ==="
        ip route show table all
        echo ""
        
        echo "# === ARP表 ==="
        ip neigh show
        echo ""
        
        echo "# === 网络统计 ==="
        cat /proc/net/dev
        echo ""
        
        echo "# === 连接跟踪 ==="
        if [[ -f /proc/net/nf_conntrack ]]; then
            wc -l /proc/net/nf_conntrack
            head -10 /proc/net/nf_conntrack
        else
            echo "连接跟踪不可用"
        fi
        
    } > "$network_file"
    
    success_message "网络信息已备份到: $network_file"
}

# 备份系统信息
backup_system_info() {
    local backup_name=$1
    local system_file="$BACKUP_DIR/${backup_name}_system.info"
    
    info_message "备份系统信息..."
    
    {
        echo "# 系统信息备份"
        echo "# 备份时间: $(date)"
        echo ""
        
        echo "# === 系统信息 ==="
        uname -a
        echo ""
        
        echo "# === 发行版信息 ==="
        if [[ -f /etc/os-release ]]; then
            cat /etc/os-release
        fi
        echo ""
        
        echo "# === 内核参数 ==="
        sysctl net.ipv4.ip_forward
        sysctl net.ipv4.conf.all.forwarding
        echo ""
        
        echo "# === 已安装的网络工具 ==="
        which iptables ip ss netstat 2>/dev/null || echo "某些工具未安装"
        echo ""
        
        echo "# === 服务状态 ==="
        systemctl status nat-gateway 2>/dev/null || echo "NAT网关服务未安装"
        
    } > "$system_file"
    
    success_message "系统信息已备份到: $system_file"
}

# 创建备份清单
create_backup_manifest() {
    local backup_name=$1
    local manifest_file="$BACKUP_DIR/${backup_name}_manifest.txt"
    
    {
        echo "# NAT网关备份清单"
        echo "# 备份名称: $backup_name"
        echo "# 备份时间: $(date)"
        echo "# 备份目录: $BACKUP_DIR"
        echo ""
        
        echo "# === 备份文件列表 ==="
        ls -la "$BACKUP_DIR"/${backup_name}_*
        echo ""
        
        echo "# === 文件校验和 ==="
        for file in "$BACKUP_DIR"/${backup_name}_*; do
            if [[ -f "$file" ]]; then
                md5sum "$file"
            fi
        done
        
    } > "$manifest_file"
    
    success_message "备份清单已创建: $manifest_file"
}

# 执行完整备份
perform_backup() {
    check_root
    create_backup_dir
    
    local backup_name=$(generate_backup_name)
    
    echo -e "${BLUE}=== 开始备份NAT网关配置 ===${NC}"
    echo -e "备份名称: ${CYAN}$backup_name${NC}"
    echo -e "备份目录: ${CYAN}$BACKUP_DIR${NC}"
    echo
    
    # 执行各项备份
    backup_iptables "$backup_name"
    backup_config "$backup_name"
    backup_network_info "$backup_name"
    backup_system_info "$backup_name"
    create_backup_manifest "$backup_name"
    
    echo
    success_message "备份完成！备份名称: $backup_name"
    
    # 清理旧备份
    cleanup_old_backups
}

# 列出可用备份
list_backups() {
    echo -e "${BLUE}=== 可用的备份 ===${NC}"
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        warning_message "备份目录不存在: $BACKUP_DIR"
        return 1
    fi
    
    local backups=($(ls -1 "$BACKUP_DIR"/*_manifest.txt 2>/dev/null | sed 's/_manifest.txt$//' | xargs -n1 basename | sort -r))
    
    if [[ ${#backups[@]} -eq 0 ]]; then
        warning_message "没有找到备份文件"
        return 1
    fi
    
    echo -e "${CYAN}找到 ${#backups[@]} 个备份:${NC}"
    echo
    
    for i in "${!backups[@]}"; do
        local backup_name="${backups[$i]}"
        local manifest_file="$BACKUP_DIR/${backup_name}_manifest.txt"
        
        if [[ -f "$manifest_file" ]]; then
            local backup_time=$(grep "备份时间:" "$manifest_file" | cut -d: -f2- | xargs)
            echo -e "${YELLOW}$((i+1)).${NC} $backup_name"
            echo -e "   时间: ${CYAN}$backup_time${NC}"
            
            # 显示备份文件大小
            local total_size=0
            for file in "$BACKUP_DIR"/${backup_name}_*; do
                if [[ -f "$file" ]]; then
                    local size=$(stat -c%s "$file" 2>/dev/null || echo "0")
                    total_size=$((total_size + size))
                fi
            done
            local size_human=$(numfmt --to=iec-i --suffix=B "$total_size" 2>/dev/null || echo "${total_size}B")
            echo -e "   大小: ${CYAN}$size_human${NC}"
            echo
        fi
    done
}

# 恢复备份
restore_backup() {
    local backup_name=$1
    
    if [[ -z "$backup_name" ]]; then
        list_backups
        echo -e "${YELLOW}请选择要恢复的备份编号: ${NC}"
        read -r choice
        
        local backups=($(ls -1 "$BACKUP_DIR"/*_manifest.txt 2>/dev/null | sed 's/_manifest.txt$//' | xargs -n1 basename | sort -r))
        
        if [[ $choice =~ ^[0-9]+$ ]] && [[ $choice -ge 1 ]] && [[ $choice -le ${#backups[@]} ]]; then
            backup_name="${backups[$((choice-1))]}"
        else
            error_exit "无效的选择"
        fi
    fi
    
    check_root
    
    local iptables_file="$BACKUP_DIR/${backup_name}_iptables.rules"
    local config_file="$BACKUP_DIR/${backup_name}_config.conf"
    local manifest_file="$BACKUP_DIR/${backup_name}_manifest.txt"
    
    # 检查备份文件是否存在
    if [[ ! -f "$manifest_file" ]]; then
        error_exit "备份不存在: $backup_name"
    fi
    
    echo -e "${BLUE}=== 恢复备份: $backup_name ===${NC}"
    echo
    
    # 确认恢复操作
    echo -e "${YELLOW}警告: 这将覆盖当前的NAT网关配置！${NC}"
    echo -e "${YELLOW}确认要继续吗? [y/N]: ${NC}"
    read -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        info_message "取消恢复操作"
        return 0
    fi
    
    # 创建当前配置的备份
    info_message "创建当前配置的备份..."
    perform_backup
    
    # 恢复iptables规则
    if [[ -f "$iptables_file" ]]; then
        info_message "恢复iptables规则..."
        
        # 清空当前规则
        iptables -F
        iptables -t nat -F
        iptables -t mangle -F
        iptables -X
        iptables -t nat -X
        iptables -t mangle -X
        
        # 恢复规则
        if iptables-restore < "$iptables_file"; then
            success_message "iptables规则恢复成功"
        else
            error_exit "iptables规则恢复失败"
        fi
    else
        warning_message "iptables备份文件不存在"
    fi
    
    # 恢复配置文件
    if [[ -f "$config_file" ]]; then
        info_message "恢复配置文件..."
        cp "$config_file" "$CONFIG_FILE"
        success_message "配置文件恢复成功"
    else
        warning_message "配置备份文件不存在"
    fi
    
    echo
    success_message "备份恢复完成！"
    info_message "建议重启NAT网关服务: systemctl restart nat-gateway"
}

# 清理旧备份
cleanup_old_backups() {
    local retention_days=30
    
    # 从配置文件读取保留天数
    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE" 2>/dev/null
        retention_days=${BACKUP_RETENTION_DAYS:-30}
    fi
    
    info_message "清理 $retention_days 天前的备份..."
    
    local deleted_count=0
    
    # 查找并删除旧备份
    find "$BACKUP_DIR" -name "nat-gateway_backup_*" -type f -mtime +$retention_days | while read -r file; do
        rm -f "$file"
        deleted_count=$((deleted_count + 1))
    done
    
    if [[ $deleted_count -gt 0 ]]; then
        success_message "已清理 $deleted_count 个旧备份文件"
    else
        info_message "没有需要清理的旧备份"
    fi
}

# 验证备份
verify_backup() {
    local backup_name=$1
    
    if [[ -z "$backup_name" ]]; then
        list_backups
        echo -e "${YELLOW}请选择要验证的备份编号: ${NC}"
        read -r choice
        
        local backups=($(ls -1 "$BACKUP_DIR"/*_manifest.txt 2>/dev/null | sed 's/_manifest.txt$//' | xargs -n1 basename | sort -r))
        
        if [[ $choice =~ ^[0-9]+$ ]] && [[ $choice -ge 1 ]] && [[ $choice -le ${#backups[@]} ]]; then
            backup_name="${backups[$((choice-1))]}"
        else
            error_exit "无效的选择"
        fi
    fi
    
    local manifest_file="$BACKUP_DIR/${backup_name}_manifest.txt"
    
    if [[ ! -f "$manifest_file" ]]; then
        error_exit "备份不存在: $backup_name"
    fi
    
    echo -e "${BLUE}=== 验证备份: $backup_name ===${NC}"
    
    # 检查备份文件完整性
    local files_ok=0
    local files_missing=0
    
    for file_type in "iptables.rules" "config.conf" "network.info" "system.info" "manifest.txt"; do
        local file_path="$BACKUP_DIR/${backup_name}_${file_type}"
        
        if [[ -f "$file_path" ]]; then
            echo -e "${GREEN}✓${NC} $file_type 存在"
            files_ok=$((files_ok + 1))
        else
            echo -e "${RED}✗${NC} $file_type 缺失"
            files_missing=$((files_missing + 1))
        fi
    done
    
    echo
    echo -e "完整文件: ${GREEN}$files_ok${NC}"
    echo -e "缺失文件: ${RED}$files_missing${NC}"
    
    if [[ $files_missing -eq 0 ]]; then
        success_message "备份验证通过"
    else
        warning_message "备份不完整"
    fi
}

# 显示帮助
show_help() {
    cat << EOF
${BLUE}NAT网关备份和恢复脚本${NC}

${YELLOW}用法:${NC}
    $0 [命令] [参数]

${YELLOW}命令:${NC}
    backup              创建新备份
    list                列出所有备份
    restore [名称]      恢复指定备份
    verify [名称]       验证备份完整性
    cleanup             清理旧备份
    help                显示此帮助信息

${YELLOW}示例:${NC}
    $0 backup           # 创建新备份
    $0 list             # 列出所有备份
    $0 restore          # 交互式恢复备份
    $0 verify           # 交互式验证备份

${YELLOW}备份内容:${NC}
    - iptables规则 (所有表)
    - NAT网关配置文件
    - 网络接口信息
    - 系统信息
    - 备份清单和校验和

${YELLOW}备份位置:${NC}
    $BACKUP_DIR

EOF
}

# 主函数
main() {
    local command=${1:-backup}
    
    case "$command" in
        "backup")
            perform_backup
            ;;
        "list")
            list_backups
            ;;
        "restore")
            restore_backup "$2"
            ;;
        "verify")
            verify_backup "$2"
            ;;
        "cleanup")
            check_root
            cleanup_old_backups
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            error_exit "未知命令: $command。使用 '$0 help' 查看帮助信息"
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
