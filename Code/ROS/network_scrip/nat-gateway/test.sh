#!/bin/bash

# =============================================================================
# NAT网关测试脚本
# 功能: 测试NAT网关的各项功能和网络连通性
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_test() {
    local test_name=$1
    local result=$2
    local message=$3
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [[ $result == "PASS" ]]; then
        echo -e "${GREEN}✓ PASS${NC} - $test_name: $message"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    elif [[ $result == "FAIL" ]]; then
        echo -e "${RED}✗ FAIL${NC} - $test_name: $message"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    elif [[ $result == "WARN" ]]; then
        echo -e "${YELLOW}⚠ WARN${NC} - $test_name: $message"
    else
        echo -e "${BLUE}ℹ INFO${NC} - $test_name: $message"
    fi
}

# 显示测试横幅
show_banner() {
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                    NAT网关功能测试                            ║
║                  NAT Gateway Testing                        ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 测试系统环境
test_system_environment() {
    echo -e "${BLUE}=== 系统环境测试 ===${NC}"
    
    # 测试root权限
    if [[ $EUID -eq 0 ]]; then
        log_test "Root权限" "PASS" "具有管理员权限"
    else
        log_test "Root权限" "WARN" "当前非root用户，某些测试可能失败"
    fi
    
    # 测试iptables
    if command -v iptables &> /dev/null; then
        log_test "iptables工具" "PASS" "已安装"
        
        # 测试iptables权限
        if iptables -L &> /dev/null; then
            log_test "iptables权限" "PASS" "可以执行iptables命令"
        else
            log_test "iptables权限" "FAIL" "无法执行iptables命令"
        fi
    else
        log_test "iptables工具" "FAIL" "未安装"
    fi
    
    # 测试ip命令
    if command -v ip &> /dev/null; then
        log_test "iproute2工具" "PASS" "已安装"
    else
        log_test "iproute2工具" "FAIL" "未安装"
    fi
    
    # 测试systemctl
    if command -v systemctl &> /dev/null; then
        log_test "systemd" "PASS" "已安装"
    else
        log_test "systemd" "WARN" "未安装或不可用"
    fi
    
    echo
}

# 测试网络接口
test_network_interfaces() {
    echo -e "${BLUE}=== 网络接口测试 ===${NC}"
    
    # 获取所有网络接口
    local interfaces=($(ip link show | grep -E '^[0-9]+:' | cut -d: -f2 | tr -d ' '))
    
    if [[ ${#interfaces[@]} -eq 0 ]]; then
        log_test "网络接口" "FAIL" "未找到任何网络接口"
        return 1
    fi
    
    log_test "网络接口数量" "INFO" "找到 ${#interfaces[@]} 个接口"
    
    local active_interfaces=0
    local wan_interfaces=0
    
    for interface in "${interfaces[@]}"; do
        if [[ $interface == "lo" ]]; then
            continue
        fi
        
        # 检查接口状态
        if ip link show "$interface" | grep -q "state UP"; then
            active_interfaces=$((active_interfaces + 1))
            log_test "接口状态" "PASS" "$interface 已启用"
            
            # 检查IP地址
            local ip_addr=$(ip addr show "$interface" | grep "inet " | head -1 | awk '{print $2}')
            if [[ -n $ip_addr ]]; then
                log_test "IP地址" "PASS" "$interface: $ip_addr"
                
                # 检查是否为外网接口
                if ip route show default | grep -q "$interface"; then
                    wan_interfaces=$((wan_interfaces + 1))
                    log_test "外网接口" "PASS" "$interface 有默认路由"
                fi
            else
                log_test "IP地址" "WARN" "$interface 无IP地址"
            fi
        else
            log_test "接口状态" "WARN" "$interface 未启用"
        fi
    done
    
    if [[ $active_interfaces -eq 0 ]]; then
        log_test "活动接口" "FAIL" "没有活动的网络接口"
    else
        log_test "活动接口" "PASS" "$active_interfaces 个活动接口"
    fi
    
    if [[ $wan_interfaces -eq 0 ]]; then
        log_test "外网连接" "WARN" "未检测到外网接口"
    else
        log_test "外网连接" "PASS" "$wan_interfaces 个外网接口"
    fi
    
    echo
}

# 测试IP转发
test_ip_forwarding() {
    echo -e "${BLUE}=== IP转发测试 ===${NC}"
    
    local ip_forward=$(cat /proc/sys/net/ipv4/ip_forward 2>/dev/null || echo "0")
    
    if [[ $ip_forward -eq 1 ]]; then
        log_test "IP转发" "PASS" "已启用"
    else
        log_test "IP转发" "FAIL" "未启用"
    fi
    
    # 检查sysctl配置
    if grep -q "^net.ipv4.ip_forward=1" /etc/sysctl.conf 2>/dev/null; then
        log_test "IP转发持久化" "PASS" "已配置永久启用"
    else
        log_test "IP转发持久化" "WARN" "未配置永久启用"
    fi
    
    echo
}

# 测试iptables规则
test_iptables_rules() {
    echo -e "${BLUE}=== iptables规则测试 ===${NC}"
    
    # 测试NAT表
    if iptables -t nat -L &> /dev/null; then
        log_test "NAT表访问" "PASS" "可以访问NAT表"
        
        # 检查MASQUERADE规则
        local masq_rules=$(iptables -t nat -L POSTROUTING -n 2>/dev/null | grep -c MASQUERADE || echo "0")
        if [[ $masq_rules -gt 0 ]]; then
            log_test "MASQUERADE规则" "PASS" "找到 $masq_rules 条规则"
        else
            log_test "MASQUERADE规则" "WARN" "未找到MASQUERADE规则"
        fi
    else
        log_test "NAT表访问" "FAIL" "无法访问NAT表"
    fi
    
    # 测试FORWARD链
    if iptables -L FORWARD &> /dev/null; then
        log_test "FORWARD链访问" "PASS" "可以访问FORWARD链"
        
        # 检查FORWARD规则
        local forward_rules=$(iptables -L FORWARD -n 2>/dev/null | grep -c ACCEPT || echo "0")
        if [[ $forward_rules -gt 0 ]]; then
            log_test "FORWARD规则" "PASS" "找到 $forward_rules 条ACCEPT规则"
        else
            log_test "FORWARD规则" "WARN" "未找到FORWARD ACCEPT规则"
        fi
    else
        log_test "FORWARD链访问" "FAIL" "无法访问FORWARD链"
    fi
    
    echo
}

# 测试网络连通性
test_connectivity() {
    echo -e "${BLUE}=== 网络连通性测试 ===${NC}"
    
    # 测试本地回环
    if ping -c 1 -W 2 127.0.0.1 &> /dev/null; then
        log_test "本地回环" "PASS" "127.0.0.1 可达"
    else
        log_test "本地回环" "FAIL" "127.0.0.1 不可达"
    fi
    
    # 测试网关连通性
    local gateway=$(ip route show default | head -1 | awk '{print $3}')
    if [[ -n $gateway ]]; then
        if ping -c 1 -W 3 "$gateway" &> /dev/null; then
            log_test "网关连通性" "PASS" "$gateway 可达"
        else
            log_test "网关连通性" "FAIL" "$gateway 不可达"
        fi
    else
        log_test "网关连通性" "WARN" "未找到默认网关"
    fi
    
    # 测试外网连通性
    local test_hosts=("*******" "*******" "***************")
    local reachable_hosts=0
    
    for host in "${test_hosts[@]}"; do
        if ping -c 1 -W 3 "$host" &> /dev/null; then
            log_test "外网连通性" "PASS" "$host 可达"
            reachable_hosts=$((reachable_hosts + 1))
        else
            log_test "外网连通性" "FAIL" "$host 不可达"
        fi
    done
    
    if [[ $reachable_hosts -gt 0 ]]; then
        log_test "外网连接" "PASS" "$reachable_hosts/${#test_hosts[@]} 个测试主机可达"
    else
        log_test "外网连接" "FAIL" "所有测试主机都不可达"
    fi
    
    echo
}

# 测试DNS解析
test_dns_resolution() {
    echo -e "${BLUE}=== DNS解析测试 ===${NC}"
    
    # 测试域名解析
    local test_domains=("google.com" "baidu.com" "github.com")
    local resolved_domains=0
    
    for domain in "${test_domains[@]}"; do
        if nslookup "$domain" &> /dev/null; then
            log_test "DNS解析" "PASS" "$domain 解析成功"
            resolved_domains=$((resolved_domains + 1))
        else
            log_test "DNS解析" "FAIL" "$domain 解析失败"
        fi
    done
    
    if [[ $resolved_domains -gt 0 ]]; then
        log_test "DNS功能" "PASS" "$resolved_domains/${#test_domains[@]} 个域名解析成功"
    else
        log_test "DNS功能" "FAIL" "所有域名解析都失败"
    fi
    
    # 检查DNS服务器配置
    if [[ -f /etc/resolv.conf ]]; then
        local dns_servers=$(grep -c "^nameserver" /etc/resolv.conf 2>/dev/null || echo "0")
        if [[ $dns_servers -gt 0 ]]; then
            log_test "DNS配置" "PASS" "配置了 $dns_servers 个DNS服务器"
        else
            log_test "DNS配置" "WARN" "未配置DNS服务器"
        fi
    else
        log_test "DNS配置" "WARN" "/etc/resolv.conf 不存在"
    fi
    
    echo
}

# 测试NAT服务
test_nat_service() {
    echo -e "${BLUE}=== NAT服务测试 ===${NC}"
    
    # 检查systemd服务
    if systemctl is-active --quiet nat-gateway 2>/dev/null; then
        log_test "NAT服务状态" "PASS" "服务正在运行"
    else
        log_test "NAT服务状态" "WARN" "服务未运行"
    fi
    
    if systemctl is-enabled --quiet nat-gateway 2>/dev/null; then
        log_test "NAT服务自启" "PASS" "已启用开机自启"
    else
        log_test "NAT服务自启" "WARN" "未启用开机自启"
    fi
    
    # 检查配置文件
    local config_file="/etc/network/nat-gateway.conf"
    if [[ -f "$config_file" ]]; then
        log_test "配置文件" "PASS" "配置文件存在"
        
        # 验证配置文件语法
        if source "$config_file" 2>/dev/null; then
            log_test "配置语法" "PASS" "配置文件语法正确"
        else
            log_test "配置语法" "FAIL" "配置文件语法错误"
        fi
    else
        log_test "配置文件" "WARN" "配置文件不存在"
    fi
    
    echo
}

# 测试连接跟踪
test_connection_tracking() {
    echo -e "${BLUE}=== 连接跟踪测试 ===${NC}"
    
    # 检查连接跟踪模块
    if [[ -f /proc/net/nf_conntrack ]]; then
        log_test "连接跟踪" "PASS" "连接跟踪功能可用"
        
        local conn_count=$(wc -l < /proc/net/nf_conntrack)
        log_test "连接数量" "INFO" "当前 $conn_count 个连接"
        
        # 检查连接跟踪表大小
        local max_conns=$(cat /proc/sys/net/netfilter/nf_conntrack_max 2>/dev/null || echo "未知")
        log_test "连接表大小" "INFO" "最大 $max_conns 个连接"
        
    else
        log_test "连接跟踪" "WARN" "连接跟踪功能不可用"
    fi
    
    echo
}

# 性能测试
test_performance() {
    echo -e "${BLUE}=== 性能测试 ===${NC}"
    
    # 测试iptables规则数量
    local nat_rules=$(iptables -t nat -L -n 2>/dev/null | wc -l)
    local filter_rules=$(iptables -L -n 2>/dev/null | wc -l)
    
    log_test "NAT规则数量" "INFO" "$nat_rules 条"
    log_test "过滤规则数量" "INFO" "$filter_rules 条"
    
    # 测试规则查询性能
    local start_time=$(date +%s%N)
    iptables -t nat -L POSTROUTING -n &> /dev/null
    local end_time=$(date +%s%N)
    local query_time=$(( (end_time - start_time) / 1000000 ))
    
    if [[ $query_time -lt 100 ]]; then
        log_test "规则查询性能" "PASS" "${query_time}ms (良好)"
    elif [[ $query_time -lt 500 ]]; then
        log_test "规则查询性能" "WARN" "${query_time}ms (一般)"
    else
        log_test "规则查询性能" "FAIL" "${query_time}ms (较慢)"
    fi
    
    echo
}

# 显示测试总结
show_summary() {
    echo -e "${BLUE}=== 测试总结 ===${NC}"
    echo -e "总测试数: ${CYAN}$TOTAL_TESTS${NC}"
    echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败: ${RED}$FAILED_TESTS${NC}"
    echo -e "跳过: ${YELLOW}$((TOTAL_TESTS - PASSED_TESTS - FAILED_TESTS))${NC}"
    
    local success_rate=0
    if [[ $TOTAL_TESTS -gt 0 ]]; then
        success_rate=$(( PASSED_TESTS * 100 / TOTAL_TESTS ))
    fi
    
    echo -e "成功率: ${CYAN}${success_rate}%${NC}"
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        echo -e "\n${GREEN}✓ 所有关键测试都通过了！${NC}"
    elif [[ $FAILED_TESTS -le 2 ]]; then
        echo -e "\n${YELLOW}⚠ 有少量测试失败，请检查相关配置${NC}"
    else
        echo -e "\n${RED}✗ 多个测试失败，NAT网关可能无法正常工作${NC}"
    fi
    
    echo
}

# 显示帮助
show_help() {
    cat << EOF
${BLUE}NAT网关测试脚本${NC}

${YELLOW}用法:${NC}
    $0 [选项]

${YELLOW}选项:${NC}
    all             运行所有测试 (默认)
    system          系统环境测试
    network         网络接口测试
    iptables        iptables规则测试
    connectivity    网络连通性测试
    dns             DNS解析测试
    service         NAT服务测试
    performance     性能测试
    help            显示此帮助信息

${YELLOW}示例:${NC}
    $0              # 运行所有测试
    $0 connectivity # 只测试网络连通性
    $0 system       # 只测试系统环境

EOF
}

# 主函数
main() {
    local test_type=${1:-all}
    
    show_banner
    
    case "$test_type" in
        "all")
            test_system_environment
            test_network_interfaces
            test_ip_forwarding
            test_iptables_rules
            test_connectivity
            test_dns_resolution
            test_nat_service
            test_connection_tracking
            test_performance
            ;;
        "system")
            test_system_environment
            ;;
        "network")
            test_network_interfaces
            ;;
        "iptables")
            test_iptables_rules
            ;;
        "connectivity")
            test_connectivity
            ;;
        "dns")
            test_dns_resolution
            ;;
        "service")
            test_nat_service
            ;;
        "performance")
            test_performance
            ;;
        "help"|"-h"|"--help")
            show_help
            return 0
            ;;
        *)
            echo -e "${RED}错误: 未知测试类型 '$test_type'${NC}"
            echo -e "使用 '${YELLOW}$0 help${NC}' 查看帮助信息"
            exit 1
            ;;
    esac
    
    show_summary
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
