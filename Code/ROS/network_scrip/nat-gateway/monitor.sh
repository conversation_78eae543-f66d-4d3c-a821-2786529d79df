#!/bin/bash

# =============================================================================
# NAT网关监控脚本
# 功能: 实时监控NAT网关的状态和性能
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m'

# 配置文件
CONFIG_FILE="/etc/network/nat-gateway.conf"
LOG_FILE="/var/log/iptables-nat.log"

# 默认监控间隔
MONITOR_INTERVAL=5

# 加载配置
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE"
        MONITOR_INTERVAL=${MONITOR_INTERVAL:-5}
    fi
}

# 清屏函数
clear_screen() {
    clear
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                    NAT网关实时监控                            ║
║                  Real-time NAT Monitor                      ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 获取系统信息
get_system_info() {
    local uptime=$(uptime | awk '{print $3,$4}' | sed 's/,//')
    local load=$(uptime | awk -F'load average:' '{print $2}')
    local memory=$(free -h | awk '/^Mem:/ {printf "%.1f/%.1f GB (%.1f%%)", $3/1024/1024/1024, $2/1024/1024/1024, $3/$2*100}')
    
    echo -e "${BLUE}=== 系统信息 ===${NC}"
    echo -e "运行时间: ${GREEN}$uptime${NC}"
    echo -e "系统负载: ${YELLOW}$load${NC}"
    echo -e "内存使用: ${CYAN}$memory${NC}"
    echo
}

# 获取网络接口状态
get_interface_status() {
    echo -e "${BLUE}=== 网络接口状态 ===${NC}"
    
    # 获取所有活动接口
    local interfaces=($(ip link show | grep -E '^[0-9]+:' | grep 'state UP' | cut -d: -f2 | tr -d ' '))
    
    for interface in "${interfaces[@]}"; do
        if [[ $interface == "lo" ]]; then
            continue
        fi
        
        local ip_info=$(ip addr show "$interface" | grep "inet " | head -1 | awk '{print $2}')
        local rx_bytes=$(cat "/sys/class/net/$interface/statistics/rx_bytes" 2>/dev/null || echo "0")
        local tx_bytes=$(cat "/sys/class/net/$interface/statistics/tx_bytes" 2>/dev/null || echo "0")
        
        # 转换字节为可读格式
        local rx_human=$(numfmt --to=iec-i --suffix=B "$rx_bytes" 2>/dev/null || echo "$rx_bytes B")
        local tx_human=$(numfmt --to=iec-i --suffix=B "$tx_bytes" 2>/dev/null || echo "$tx_bytes B")
        
        # 检查是否有默认路由
        local is_wan=""
        if ip route show default | grep -q "$interface"; then
            is_wan=" ${YELLOW}[WAN]${NC}"
        fi
        
        echo -e "${GREEN}$interface${NC}$is_wan: ${CYAN}$ip_info${NC} | RX: ${MAGENTA}$rx_human${NC} | TX: ${MAGENTA}$tx_human${NC}"
    done
    echo
}

# 获取NAT规则状态
get_nat_status() {
    echo -e "${BLUE}=== NAT规则状态 ===${NC}"
    
    # 检查IP转发
    local ip_forward=$(cat /proc/sys/net/ipv4/ip_forward 2>/dev/null || echo "0")
    if [[ $ip_forward -eq 1 ]]; then
        echo -e "IP转发: ${GREEN}✓ 已启用${NC}"
    else
        echo -e "IP转发: ${RED}✗ 未启用${NC}"
    fi
    
    # 统计NAT规则
    local nat_rules=$(iptables -t nat -L POSTROUTING -n 2>/dev/null | grep -c MASQUERADE 2>/dev/null || echo "0")
    nat_rules=$(echo "$nat_rules" | tr -d '\n\r' | awk '{print $1}')  # 清理输出
    echo -e "MASQUERADE规则: ${CYAN}$nat_rules 条${NC}"

    # 统计FORWARD规则
    local forward_rules=$(iptables -L FORWARD -n 2>/dev/null | grep -c ACCEPT 2>/dev/null || echo "0")
    forward_rules=$(echo "$forward_rules" | tr -d '\n\r' | awk '{print $1}')  # 清理输出
    echo -e "FORWARD规则: ${CYAN}$forward_rules 条${NC}"
    
    # 显示活动的NAT规则
    if [[ $nat_rules -gt 0 ]]; then
        echo -e "\n${YELLOW}活动的NAT规则:${NC}"
        iptables -t nat -L POSTROUTING -n --line-numbers 2>/dev/null | grep MASQUERADE | while read -r line; do
            echo -e "  ${CYAN}$line${NC}"
        done
    fi
    echo
}

# 获取连接统计
get_connection_stats() {
    echo -e "${BLUE}=== 连接统计 ===${NC}"
    
    # 统计连接跟踪表
    if [[ -f /proc/net/nf_conntrack ]]; then
        local total_conns=$(wc -l < /proc/net/nf_conntrack)
        local tcp_conns=$(grep -c "tcp" /proc/net/nf_conntrack 2>/dev/null || echo "0")
        local udp_conns=$(grep -c "udp" /proc/net/nf_conntrack 2>/dev/null || echo "0")
        local established=$(grep -c "ESTABLISHED" /proc/net/nf_conntrack 2>/dev/null || echo "0")
        
        echo -e "总连接数: ${CYAN}$total_conns${NC}"
        echo -e "TCP连接: ${GREEN}$tcp_conns${NC} | UDP连接: ${GREEN}$udp_conns${NC}"
        echo -e "已建立连接: ${YELLOW}$established${NC}"
    else
        echo -e "${YELLOW}连接跟踪信息不可用${NC}"
    fi
    
    # 网络统计
    if command -v ss &> /dev/null; then
        local listening=$(ss -ln | grep -c LISTEN)
        echo -e "监听端口: ${MAGENTA}$listening${NC}"
    fi
    echo
}

# 获取流量统计
get_traffic_stats() {
    echo -e "${BLUE}=== 流量统计 ===${NC}"
    
    # 获取iptables计数器
    local nat_packets=0
    local nat_bytes=0
    
    while read -r line; do
        if [[ $line =~ MASQUERADE ]]; then
            local packets=$(echo "$line" | awk '{print $1}')
            local bytes=$(echo "$line" | awk '{print $2}')
            nat_packets=$((nat_packets + packets))
            nat_bytes=$((nat_bytes + bytes))
        fi
    done < <(iptables -t nat -L POSTROUTING -n -v 2>/dev/null | tail -n +3)
    
    local nat_bytes_human=$(numfmt --to=iec-i --suffix=B "$nat_bytes" 2>/dev/null || echo "$nat_bytes B")
    
    echo -e "NAT处理包数: ${CYAN}$nat_packets${NC}"
    echo -e "NAT处理字节: ${CYAN}$nat_bytes_human${NC}"
    echo
}

# 检查服务状态
check_service_status() {
    echo -e "${BLUE}=== 服务状态 ===${NC}"
    
    # 检查systemd服务
    if systemctl is-active --quiet nat-gateway 2>/dev/null; then
        echo -e "NAT网关服务: ${GREEN}✓ 运行中${NC}"
    else
        echo -e "NAT网关服务: ${RED}✗ 未运行${NC}"
    fi
    
    # 检查关键进程
    if pgrep -f "iptables" > /dev/null; then
        echo -e "iptables进程: ${GREEN}✓ 活动${NC}"
    else
        echo -e "iptables进程: ${YELLOW}⚠ 无活动${NC}"
    fi
    echo
}

# 测试网络连通性
test_connectivity() {
    echo -e "${BLUE}=== 连通性测试 ===${NC}"
    
    # 测试本地回环
    if ping -c 1 127.0.0.1 &> /dev/null; then
        echo -e "本地回环: ${GREEN}✓ 正常${NC}"
    else
        echo -e "本地回环: ${RED}✗ 异常${NC}"
    fi
    
    # 测试外网连接
    if ping -c 1 -W 2 ******* &> /dev/null; then
        echo -e "外网连接: ${GREEN}✓ 正常${NC}"
    else
        echo -e "外网连接: ${RED}✗ 异常${NC}"
    fi
    
    # 测试DNS解析
    if nslookup google.com &> /dev/null; then
        echo -e "DNS解析: ${GREEN}✓ 正常${NC}"
    else
        echo -e "DNS解析: ${RED}✗ 异常${NC}"
    fi
    echo
}

# 显示最近日志
show_recent_logs() {
    echo -e "${BLUE}=== 最近日志 (最新5条) ===${NC}"
    
    if [[ -f "$LOG_FILE" ]]; then
        tail -5 "$LOG_FILE" | while read -r line; do
            if [[ $line =~ ERROR ]]; then
                echo -e "${RED}$line${NC}"
            elif [[ $line =~ WARN ]]; then
                echo -e "${YELLOW}$line${NC}"
            else
                echo -e "${CYAN}$line${NC}"
            fi
        done
    else
        echo -e "${YELLOW}日志文件不存在${NC}"
    fi
    echo
}

# 显示帮助信息
show_help() {
    cat << EOF
${BLUE}NAT网关监控脚本${NC}

${YELLOW}用法:${NC}
    $0 [选项]

${YELLOW}选项:${NC}
    monitor         启动实时监控 (默认)
    once            显示一次状态信息
    interval <秒>   设置监控间隔
    help            显示此帮助信息

${YELLOW}快捷键 (监控模式):${NC}
    q               退出监控
    r               刷新显示
    Ctrl+C          强制退出

${YELLOW}示例:${NC}
    $0              # 启动实时监控
    $0 once         # 显示一次状态
    $0 interval 10  # 10秒间隔监控

EOF
}

# 单次显示状态
show_once() {
    clear_screen
    get_system_info
    get_interface_status
    get_nat_status
    get_connection_stats
    get_traffic_stats
    check_service_status
    test_connectivity
    show_recent_logs
    echo -e "${CYAN}监控时间: $(date)${NC}"
}

# 实时监控
start_monitor() {
    local interval=${1:-$MONITOR_INTERVAL}
    
    # 设置信号处理
    trap 'echo -e "\n${YELLOW}监控已停止${NC}"; exit 0' INT TERM
    
    echo -e "${GREEN}启动实时监控 (间隔: ${interval}秒)${NC}"
    echo -e "${YELLOW}按 Ctrl+C 退出监控${NC}"
    sleep 2
    
    while true; do
        show_once
        echo -e "\n${YELLOW}下次刷新: ${interval}秒后 | 按 Ctrl+C 退出${NC}"
        sleep "$interval"
    done
}

# 主函数
main() {
    local command=${1:-monitor}
    
    # 加载配置
    load_config
    
    case "$command" in
        "monitor")
            start_monitor
            ;;
        "once")
            show_once
            ;;
        "interval")
            local interval=$2
            if [[ $interval =~ ^[0-9]+$ ]] && [[ $interval -gt 0 ]]; then
                start_monitor "$interval"
            else
                echo -e "${RED}错误: 间隔必须是正整数${NC}"
                exit 1
            fi
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}错误: 未知选项 '$command'${NC}"
            echo -e "使用 '${YELLOW}$0 help${NC}' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
