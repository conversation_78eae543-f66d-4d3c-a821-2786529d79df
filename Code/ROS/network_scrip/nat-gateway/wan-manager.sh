#!/bin/bash

# =============================================================================
# 外网接口管理器
# 功能: 专门管理NAT网关的外网接口切换和监控
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m'

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
NAT_SCRIPT="$SCRIPT_DIR/nat-service.sh"

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                    外网接口管理器                             ║
║                  WAN Interface Manager                      ║
╠══════════════════════════════════════════════════════════════╣
║  功能: 智能管理和切换NAT网关的外网接口                        ║
║  特性: 自动评分、连通性测试、实时监控                         ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 日志函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}"
}

error_exit() {
    log_message "ERROR" "$1"
    echo -e "${RED}错误: $1${NC}" >&2
    exit 1
}

success_message() {
    log_message "INFO" "$1"
    echo -e "${GREEN}✓ $1${NC}"
}

info_message() {
    log_message "INFO" "$1"
    echo -e "${BLUE}ℹ $1${NC}"
}

warning_message() {
    log_message "WARN" "$1"
    echo -e "${YELLOW}⚠ $1${NC}"
}

# 检查NAT脚本是否存在
check_nat_script() {
    if [[ ! -f "$NAT_SCRIPT" ]]; then
        error_exit "NAT脚本不存在: $NAT_SCRIPT"
    fi
}

# 获取当前活动的外网接口
get_current_wan_interface() {
    # 从iptables规则中获取当前使用的外网接口
    local current_interfaces=($(iptables -t nat -L POSTROUTING -n 2>/dev/null | grep MASQUERADE | awk '{print $NF}' | sort -u))
    
    if [[ ${#current_interfaces[@]} -gt 0 ]]; then
        echo "${current_interfaces[0]}"  # 返回第一个
    else
        # 如果没有NAT规则，尝试从默认路由获取
        ip route show default | head -1 | awk '{print $5}'
    fi
}

# 详细显示外网接口信息
show_detailed_wan_info() {
    echo -e "${BLUE}=== 详细外网接口信息 ===${NC}"
    
    # 获取当前活动接口
    local current_wan=$(get_current_wan_interface)
    
    # 执行NAT脚本获取接口列表
    if ! sudo "$NAT_SCRIPT" list-wan 2>/dev/null; then
        warning_message "无法获取外网接口信息"
        return 1
    fi
    
    echo
    echo -e "${BLUE}当前活动的外网接口: ${CYAN}${current_wan:-"未知"}${NC}"
    
    # 显示NAT规则统计
    local nat_rules=$(iptables -t nat -L POSTROUTING -n 2>/dev/null | grep -c MASQUERADE || echo "0")
    echo -e "${BLUE}活动的NAT规则数量: ${CYAN}$nat_rules${NC}"
    
    echo
}

# 监控外网接口状态
monitor_wan_interfaces() {
    local interval=${1:-5}
    
    echo -e "${GREEN}启动外网接口监控 (间隔: ${interval}秒)${NC}"
    echo -e "${YELLOW}按 Ctrl+C 退出监控${NC}"
    
    # 设置信号处理
    trap 'echo -e "\n${YELLOW}监控已停止${NC}"; exit 0' INT TERM
    
    while true; do
        clear
        show_banner
        show_detailed_wan_info
        
        echo -e "${BLUE}=== 实时连通性测试 ===${NC}"
        local available_interfaces=($(ip link show | grep -E '^[0-9]+:' | grep 'state UP' | cut -d: -f2 | tr -d ' ' | grep -v lo))
        
        for interface in "${available_interfaces[@]}"; do
            if ip addr show "$interface" | grep -q "inet "; then
                local ip=$(ip addr show "$interface" | grep "inet " | head -1 | awk '{print $2}' | cut -d'/' -f1)
                
                # 测试连通性
                local status="${RED}✗ 不可达${NC}"
                if ping -c 1 -W 2 -I "$interface" ******* &> /dev/null; then
                    status="${GREEN}✓ 可达${NC}"
                fi
                
                # 检查是否为当前外网接口
                local is_current=""
                if [[ $interface == $(get_current_wan_interface) ]]; then
                    is_current=" ${CYAN}[当前]${NC}"
                fi
                
                echo -e "${CYAN}$interface${NC}: $ip - $status$is_current"
            fi
        done
        
        echo -e "\n${YELLOW}下次刷新: ${interval}秒后 | 按 Ctrl+C 退出${NC}"
        sleep "$interval"
    done
}

# 测试所有外网接口
test_all_wan_interfaces() {
    echo -e "${BLUE}=== 测试所有外网接口 ===${NC}"
    
    local test_hosts=("*******" "*******" "***************")
    local available_interfaces=($(ip link show | grep -E '^[0-9]+:' | grep 'state UP' | cut -d: -f2 | tr -d ' ' | grep -v lo))
    
    echo -e "${CYAN}测试主机: ${test_hosts[*]}${NC}"
    echo
    
    for interface in "${available_interfaces[@]}"; do
        if ip addr show "$interface" | grep -q "inet "; then
            local ip=$(ip addr show "$interface" | grep "inet " | head -1 | awk '{print $2}' | cut -d'/' -f1)
            echo -e "${YELLOW}测试接口: $interface ($ip)${NC}"
            
            local success_count=0
            local total_tests=${#test_hosts[@]}
            
            for host in "${test_hosts[@]}"; do
                if ping -c 1 -W 3 -I "$interface" "$host" &> /dev/null; then
                    echo -e "  ${GREEN}✓${NC} $host 可达"
                    success_count=$((success_count + 1))
                else
                    echo -e "  ${RED}✗${NC} $host 不可达"
                fi
            done
            
            local success_rate=$(( success_count * 100 / total_tests ))
            echo -e "  ${BLUE}成功率: ${CYAN}${success_rate}%${NC} (${success_count}/${total_tests})"
            echo
        fi
    done
}

# 比较外网接口性能
compare_wan_performance() {
    echo -e "${BLUE}=== 外网接口性能比较 ===${NC}"
    
    local available_interfaces=($(ip link show | grep -E '^[0-9]+:' | grep 'state UP' | cut -d: -f2 | tr -d ' ' | grep -v lo))
    local test_host="*******"
    
    echo -e "${CYAN}测试主机: $test_host${NC}"
    echo -e "${CYAN}测试项目: 延迟、丢包率${NC}"
    echo
    
    printf "%-12s %-15s %-10s %-10s %-10s\n" "接口" "IP地址" "延迟(ms)" "丢包率" "状态"
    echo "================================================================"
    
    for interface in "${available_interfaces[@]}"; do
        if ip addr show "$interface" | grep -q "inet "; then
            local ip=$(ip addr show "$interface" | grep "inet " | head -1 | awk '{print $2}' | cut -d'/' -f1)
            
            # 测试延迟和丢包率
            local ping_result=$(ping -c 5 -W 2 -I "$interface" "$test_host" 2>/dev/null)
            
            if [[ $? -eq 0 ]]; then
                local avg_time=$(echo "$ping_result" | tail -1 | awk -F'/' '{print $5}' | cut -d' ' -f1)
                local packet_loss=$(echo "$ping_result" | grep "packet loss" | awk '{print $6}')
                local status="${GREEN}正常${NC}"
                
                printf "%-12s %-15s %-10s %-10s %-10s\n" "$interface" "$ip" "${avg_time}ms" "$packet_loss" "$status"
            else
                printf "%-12s %-15s %-10s %-10s %-10s\n" "$interface" "$ip" "超时" "100%" "${RED}异常${NC}"
            fi
        fi
    done
    
    echo
}

# 自动故障转移
auto_failover() {
    local current_wan=$(get_current_wan_interface)
    
    if [[ -z $current_wan ]]; then
        warning_message "未检测到当前外网接口"
        return 1
    fi
    
    echo -e "${BLUE}=== 自动故障转移 ===${NC}"
    echo -e "当前外网接口: ${CYAN}$current_wan${NC}"
    
    # 测试当前接口连通性
    if ping -c 3 -W 2 -I "$current_wan" ******* &> /dev/null; then
        success_message "当前外网接口工作正常，无需故障转移"
        return 0
    fi
    
    warning_message "当前外网接口连通性异常，开始故障转移..."
    
    # 自动选择最佳接口
    if sudo "$NAT_SCRIPT" auto-wan; then
        success_message "故障转移完成"
    else
        error_exit "故障转移失败"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
${BLUE}外网接口管理器${NC}

${YELLOW}用法:${NC}
    $0 [命令] [参数]

${YELLOW}命令:${NC}
    list            列出所有可用的外网接口
    info            显示详细的外网接口信息
    monitor [间隔]  实时监控外网接口状态
    test            测试所有外网接口连通性
    compare         比较外网接口性能
    auto            自动选择最佳外网接口
    switch [接口]   切换到指定外网接口
    force <接口>    强制切换到指定接口
    failover        自动故障转移
    current         显示当前活动的外网接口
    help            显示此帮助信息

${YELLOW}示例:${NC}
    $0 list                 # 列出外网接口
    $0 info                 # 显示详细信息
    $0 monitor 10           # 10秒间隔监控
    $0 test                 # 测试连通性
    $0 compare              # 性能比较
    $0 auto                 # 自动选择最佳接口
    $0 switch eth0          # 切换到eth0
    $0 force wlan0          # 强制切换到wlan0
    $0 failover             # 故障转移

${YELLOW}说明:${NC}
    - 需要root权限执行切换操作
    - 监控模式按Ctrl+C退出
    - 故障转移会自动选择最佳可用接口

EOF
}

# 主函数
main() {
    local command=${1:-info}
    local param=$2
    
    # 检查NAT脚本
    check_nat_script
    
    case "$command" in
        "list")
            sudo "$NAT_SCRIPT" list-wan
            ;;
        "info")
            show_detailed_wan_info
            ;;
        "monitor")
            local interval=${param:-5}
            monitor_wan_interfaces "$interval"
            ;;
        "test")
            test_all_wan_interfaces
            ;;
        "compare")
            compare_wan_performance
            ;;
        "auto")
            sudo "$NAT_SCRIPT" auto-wan
            ;;
        "switch")
            if [[ -n $param ]]; then
                sudo "$NAT_SCRIPT" switch-wan "$param"
            else
                sudo "$NAT_SCRIPT" switch-wan
            fi
            ;;
        "force")
            if [[ -z $param ]]; then
                error_exit "请指定要强制切换的外网接口"
            fi
            sudo "$NAT_SCRIPT" force-wan "$param"
            ;;
        "failover")
            auto_failover
            ;;
        "current")
            local current=$(get_current_wan_interface)
            if [[ -n $current ]]; then
                echo -e "当前外网接口: ${CYAN}$current${NC}"
            else
                echo -e "${YELLOW}未检测到活动的外网接口${NC}"
            fi
            ;;
        "help"|"-h"|"--help")
            show_banner
            show_help
            ;;
        *)
            error_exit "未知命令: $command。使用 '$0 help' 查看帮助信息"
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
