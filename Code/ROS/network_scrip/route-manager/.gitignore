# 路由管理器项目 .gitignore

# 系统生成的备份文件
*.backup
*.backup.*
route-backup-*
*.bak

# 日志文件
*.log
logs/
/var/log/route-manager/

# 临时文件
*.tmp
*.temp
.tmp/
temp/

# 配置文件（可能包含敏感信息）
*.conf.local
*.conf.user
config.local
local.conf

# 系统服务文件（安装后生成）
/etc/systemd/system/route-priority-manager.service
/etc/systemd/system/route-priority-manager.timer
/etc/route-manager/

# 运行时文件
*.pid
*.lock
.running

# 测试文件
test-*
*-test.sh
test_*.sh

# 编辑器临时文件
*~
.*.swp
.*.swo
*.orig
.vscode/
.idea/

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# 压缩文件
*.zip
*.tar.gz
*.tar.bz2
*.rar
*.7z

# 安装脚本生成的文件
install.log
uninstall.log
setup.log

# 网络调试文件
network-debug-*
route-debug-*
interface-debug-*

# 性能测试文件
benchmark-*
performance-*

# 文档生成文件
*.pdf
*.html
docs/build/

# 本地开发文件
dev-*
local-*
my-*

# 错误报告
error-report-*
crash-*
