#!/bin/bash

# =============================================================================
# NAT网关安装脚本
# 功能: 安装和配置智能NAT网关服务
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INSTALL_DIR="/usr/local/bin"
SERVICE_DIR="/etc/systemd/system"
CONFIG_DIR="/etc/network"
LOG_DIR="/var/log"

# 服务名称
SERVICE_NAME="nat-gateway"
SCRIPT_NAME="iptables_wifi.sh"

# 日志函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}"
}

error_exit() {
    log_message "ERROR" "$1"
    echo -e "${RED}错误: $1${NC}" >&2
    exit 1
}

success_message() {
    log_message "INFO" "$1"
    echo -e "${GREEN}✓ $1${NC}"
}

info_message() {
    log_message "INFO" "$1"
    echo -e "${BLUE}ℹ $1${NC}"
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error_exit "此脚本需要root权限运行，请使用 sudo"
    fi
}

# 安装依赖
install_dependencies() {
    info_message "检查并安装依赖..."
    
    # 检查包管理器
    if command -v apt-get &> /dev/null; then
        apt-get update
        apt-get install -y iptables iproute2 systemd
    elif command -v yum &> /dev/null; then
        yum install -y iptables iproute systemd
    elif command -v dnf &> /dev/null; then
        dnf install -y iptables iproute2 systemd
    else
        warning_message "未识别的包管理器，请手动安装: iptables, iproute2, systemd"
    fi
    
    success_message "依赖检查完成"
}

# 复制脚本文件
install_script() {
    info_message "安装NAT网关脚本..."
    
    # 创建目录
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$LOG_DIR"
    
    # 复制主脚本
    cp "$SCRIPT_DIR/$SCRIPT_NAME" "$INSTALL_DIR/nat-gateway"
    chmod +x "$INSTALL_DIR/nat-gateway"
    
    success_message "脚本安装完成: $INSTALL_DIR/nat-gateway"
}

# 创建systemd服务
create_service() {
    info_message "创建systemd服务..."
    
    cat > "$SERVICE_DIR/$SERVICE_NAME.service" << EOF
[Unit]
Description=NAT Gateway Service
After=network.target
Wants=network.target

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=$INSTALL_DIR/nat-gateway start
ExecStop=$INSTALL_DIR/nat-gateway stop
ExecReload=$INSTALL_DIR/nat-gateway restart
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd
    systemctl daemon-reload
    
    success_message "systemd服务创建完成"
}

# 启用服务
enable_service() {
    info_message "启用NAT网关服务..."
    
    systemctl enable "$SERVICE_NAME"
    systemctl start "$SERVICE_NAME"
    
    # 检查服务状态
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        success_message "NAT网关服务启动成功"
    else
        error_exit "NAT网关服务启动失败"
    fi
}

# 创建配置文件
create_config() {
    info_message "创建配置文件..."
    
    cat > "$CONFIG_DIR/nat-gateway.conf" << EOF
# NAT网关配置文件
# 此文件由安装脚本自动生成

# 自动检测网络接口 (1=启用, 0=禁用)
AUTO_DETECT=1

# 手动指定外网接口 (当AUTO_DETECT=0时使用)
# WAN_INTERFACES="eth0 wlan0"

# 手动指定内网网段 (当AUTO_DETECT=0时使用)
# LAN_NETWORKS="***********/24 ***********/24"

# 启用IP转发
IP_FORWARD_ENABLED=1

# 日志级别 (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL=INFO

# 启动时测试连通性
TEST_CONNECTIVITY=1
EOF
    
    success_message "配置文件创建完成: $CONFIG_DIR/nat-gateway.conf"
}

# 卸载服务
uninstall_service() {
    info_message "卸载NAT网关服务..."
    
    # 停止并禁用服务
    systemctl stop "$SERVICE_NAME" 2>/dev/null || true
    systemctl disable "$SERVICE_NAME" 2>/dev/null || true
    
    # 删除服务文件
    rm -f "$SERVICE_DIR/$SERVICE_NAME.service"
    
    # 删除脚本文件
    rm -f "$INSTALL_DIR/nat-gateway"
    
    # 删除配置文件（询问用户）
    if [[ -f "$CONFIG_DIR/nat-gateway.conf" ]]; then
        read -p "是否删除配置文件? [y/N]: " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -f "$CONFIG_DIR/nat-gateway.conf"
            success_message "配置文件已删除"
        fi
    fi
    
    # 重新加载systemd
    systemctl daemon-reload
    
    success_message "NAT网关服务卸载完成"
}

# 显示状态
show_status() {
    echo -e "\n${BLUE}=== NAT网关服务状态 ===${NC}"
    
    # 服务状态
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        echo -e "${GREEN}✓ 服务状态: 运行中${NC}"
    else
        echo -e "${RED}✗ 服务状态: 未运行${NC}"
    fi
    
    # 服务是否启用
    if systemctl is-enabled --quiet "$SERVICE_NAME"; then
        echo -e "${GREEN}✓ 开机自启: 已启用${NC}"
    else
        echo -e "${YELLOW}⚠ 开机自启: 未启用${NC}"
    fi
    
    # 配置文件
    if [[ -f "$CONFIG_DIR/nat-gateway.conf" ]]; then
        echo -e "${GREEN}✓ 配置文件: 存在${NC}"
    else
        echo -e "${YELLOW}⚠ 配置文件: 不存在${NC}"
    fi
    
    # IP转发状态
    local ip_forward=$(cat /proc/sys/net/ipv4/ip_forward 2>/dev/null || echo "0")
    if [[ $ip_forward -eq 1 ]]; then
        echo -e "${GREEN}✓ IP转发: 已启用${NC}"
    else
        echo -e "${RED}✗ IP转发: 未启用${NC}"
    fi
    
    echo
}

# 显示帮助
show_help() {
    cat << EOF
${BLUE}NAT网关安装脚本${NC}

${YELLOW}用法:${NC}
    $0 [选项]

${YELLOW}选项:${NC}
    install         安装NAT网关服务
    uninstall       卸载NAT网关服务
    status          显示服务状态
    start           启动服务
    stop            停止服务
    restart         重启服务
    enable          启用开机自启
    disable         禁用开机自启
    help            显示此帮助信息

${YELLOW}示例:${NC}
    $0 install      # 安装服务
    $0 status       # 查看状态
    $0 uninstall    # 卸载服务

EOF
}

# 主函数
main() {
    local action=${1:-install}
    
    case "$action" in
        "install")
            check_root
            install_dependencies
            install_script
            create_service
            create_config
            enable_service
            show_status
            echo -e "\n${GREEN}NAT网关安装完成！${NC}"
            echo -e "使用 'systemctl status $SERVICE_NAME' 查看服务状态"
            echo -e "使用 'nat-gateway help' 查看使用帮助"
            ;;
        "uninstall")
            check_root
            uninstall_service
            ;;
        "status")
            show_status
            ;;
        "start")
            check_root
            systemctl start "$SERVICE_NAME"
            ;;
        "stop")
            check_root
            systemctl stop "$SERVICE_NAME"
            ;;
        "restart")
            check_root
            systemctl restart "$SERVICE_NAME"
            ;;
        "enable")
            check_root
            systemctl enable "$SERVICE_NAME"
            ;;
        "disable")
            check_root
            systemctl disable "$SERVICE_NAME"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            error_exit "未知选项: $action。使用 '$0 help' 查看帮助信息"
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
