#!/bin/bash

# 路由优先级管理器快速安装脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}==================== 路由优先级管理器快速安装 ====================${NC}"

# 检查root权限
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}✗ 此脚本需要root权限运行${NC}"
    echo -e "${YELLOW}请使用: sudo $0${NC}"
    exit 1
fi

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo -e "${BLUE}📍 工作目录: $SCRIPT_DIR${NC}"

# 检查必要文件
echo -e "${BLUE}🔍 检查必要文件...${NC}"

required_files=(
    "route-priority-manager.sh"
    "install-route-manager.sh"
)

for file in "${required_files[@]}"; do
    if [ -f "$SCRIPT_DIR/$file" ]; then
        echo -e "${GREEN}✓ $file${NC}"
    else
        echo -e "${RED}✗ 缺少文件: $file${NC}"
        exit 1
    fi
done

# 显示当前网络状态
echo -e "\n${BLUE}🌐 当前网络状态:${NC}"
echo -e "${CYAN}可用网络接口:${NC}"
ip link show | grep -E '^[0-9]+:' | while read -r line; do
    iface=$(echo "$line" | awk -F': ' '{print $2}' | cut -d'@' -f1)
    # 检查接口是否真正可用（有UP标志）
    if [ "$iface" != "lo" ]; then
        if echo "$line" | grep -q "UP"; then
            echo -e "  ${YELLOW}$iface${NC}: UP"
        else
            echo -e "  ${YELLOW}$iface${NC}: DOWN"
        fi
    fi
done

echo -e "\n${CYAN}当前默认路由:${NC}"
ip route show default

# 询问安装方式
echo -e "\n${YELLOW}请选择安装方式:${NC}"
echo -e "1) 快速安装 (自动选择最佳网卡)"
echo -e "2) 指定网卡安装 (手动指定优先级网卡)"
echo -e "3) 自定义安装 (完整配置向导)"
echo -e "4) 仅安装不启用"
echo -e "5) 查看帮助"

read -p "请选择 (1-5): " install_choice

case $install_choice in
    1)
        echo -e "\n${BLUE}🚀 执行快速安装 (自动选择最佳网卡)...${NC}"
        echo -e "${YELLOW}将自动选择最佳网卡并设置为最高优先级${NC}"
        echo -e "${YELLOW}系统启动时将自动配置路由优先级${NC}"

        echo -e "\n${CYAN}确认继续? (Y/n)${NC}"
        read -r confirm
        confirm=${confirm:-Y}

        if [[ $confirm =~ ^[Yy]$ ]]; then
            echo -e "${BLUE}配置自动模式...${NC}"
            "$SCRIPT_DIR/install-route-manager.sh" --install-auto --metric 10 --auto-start
        else
            echo -e "${YELLOW}安装已取消${NC}"
            exit 0
        fi
        ;;
    2)
        echo -e "\n${BLUE}🎯 指定网卡安装...${NC}"

        # 显示可用网卡供用户选择
        echo -e "${CYAN}可用的网络接口:${NC}"
        available_interfaces=()
        while IFS= read -r line; do
            iface=$(echo "$line" | awk -F': ' '{print $2}' | cut -d'@' -f1)
            # 检查接口是否真正可用（有UP标志）
            if [ "$iface" != "lo" ]; then
                available_interfaces+=("$iface")
                if echo "$line" | grep -q "UP"; then
                    echo -e "  ${YELLOW}$iface${NC}: UP"
                else
                    echo -e "  ${YELLOW}$iface${NC}: DOWN"
                fi
            fi
        done < <(ip link show | grep -E '^[0-9]+:')

        echo -e "\n${CYAN}请输入要设置为最高优先级的网卡 (用逗号分隔多个网卡):${NC}"
        echo -e "${YELLOW}示例: eth0 或 eth0,wlan0${NC}"
        read -p "网卡名称: " priority_interfaces

        if [ -z "$priority_interfaces" ]; then
            echo -e "${RED}✗ 未指定网卡，安装已取消${NC}"
            exit 1
        fi

        echo -e "\n${CYAN}基础优先级值 (默认10):${NC}"
        read -p "优先级: " base_metric
        base_metric=${base_metric:-10}

        echo -e "\n${CYAN}确认安装配置:${NC}"
        echo -e "  优先级网卡: ${YELLOW}$priority_interfaces${NC}"
        echo -e "  基础优先级: ${YELLOW}$base_metric${NC}"
        echo -e "\n${CYAN}确认继续? (Y/n)${NC}"
        read -r confirm
        confirm=${confirm:-Y}

        if [[ $confirm =~ ^[Yy]$ ]]; then
            echo -e "${BLUE}配置指定网卡模式...${NC}"
            "$SCRIPT_DIR/install-route-manager.sh" --install-manual "$priority_interfaces" --metric "$base_metric" --auto-start
        else
            echo -e "${YELLOW}安装已取消${NC}"
            exit 0
        fi
        ;;
    3)
        echo -e "\n${BLUE}🔧 执行自定义安装 (完整配置向导)...${NC}"
        "$SCRIPT_DIR/install-route-manager.sh" --install
        ;;
    4)
        echo -e "\n${BLUE}📦 仅安装系统...${NC}"
        echo -e "${YELLOW}安装完成后需要手动启用服务${NC}"
        "$SCRIPT_DIR/install-route-manager.sh" --install-auto --metric 10 --no-auto-start
        ;;
    5)
        echo -e "\n${BLUE}📖 帮助信息:${NC}"
        "$SCRIPT_DIR/install-route-manager.sh" --help
        exit 0
        ;;
    *)
        echo -e "${RED}✗ 无效选择${NC}"
        exit 1
        ;;
esac

# 检查安装结果
if [ $? -eq 0 ]; then
    echo -e "\n${GREEN}==================== 安装成功 ====================${NC}"
    
    # 显示服务状态
    echo -e "${BLUE}📊 服务状态:${NC}"
    systemctl status route-priority-manager.timer --no-pager -l || true
    
    # 显示当前路由状态
    echo -e "\n${BLUE}🌐 当前路由状态:${NC}"
    /usr/local/bin/route-priority-manager.sh ranking 2>/dev/null || ip route show default
    
    echo -e "\n${BLUE}🎯 快速命令:${NC}"
    echo -e "  查看状态: ${CYAN}systemctl status route-priority-manager.timer${NC}"
    echo -e "  查看日志: ${CYAN}journalctl -u route-priority-manager.service -f${NC}"
    echo -e "  手动执行: ${CYAN}sudo /usr/local/bin/route-priority-manager.sh auto${NC}"
    echo -e "  编辑配置: ${CYAN}sudo nano /etc/route-manager/route-manager.conf${NC}"
    echo -e "  重启服务: ${CYAN}sudo systemctl restart route-priority-manager.timer${NC}"
    
    echo -e "\n${YELLOW}💡 提示:${NC}"
    echo -e "  - 服务将在系统启动后30秒自动运行"
    echo -e "  - 可通过配置文件自定义网卡优先级"
    echo -e "  - 支持多网卡环境的智能管理"
    
    # 询问是否立即测试
    echo -e "\n${YELLOW}是否立即测试路由配置? (y/N)${NC}"
    read -r test_now
    
    if [[ $test_now =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}🧪 执行测试...${NC}"
        /usr/local/bin/route-priority-manager.sh --systemd-mode
        
        echo -e "\n${BLUE}测试后的路由状态:${NC}"
        /usr/local/bin/route-priority-manager.sh ranking
    fi
    
else
    echo -e "\n${RED}==================== 安装失败 ====================${NC}"
    echo -e "${YELLOW}请检查错误信息并重试${NC}"
    exit 1
fi

echo -e "\n${GREEN}==================== 完成 ====================${NC}"
