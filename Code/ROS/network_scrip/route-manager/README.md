# 统一路由优先级管理器

一个功能完整的Linux多网卡路由优先级管理工具，整合了所有路由管理功能到单一脚本中。

## 🚀 功能特性

- ✅ **智能路由优先级管理** - 自动设置和调整网卡优先级
- ✅ **重复路由清理** - 智能清理同一网卡的多条默认路由
- ✅ **多网卡冗余配置** - 保持网络连接的高可用性
- ✅ **自动故障切换** - 自动选择最佳可用网络接口
- ✅ **路由备份与恢复** - 安全的路由表备份和恢复功能
- ✅ **批量配置支持** - 支持配置文件批量设置多个接口
- ✅ **网络诊断工具** - 内置连通性测试和系统信息显示
- ✅ **兼容性修复** - 正确识别所有类型网络接口状态

## 📦 文件结构

```
route-manager/
├── unified-route-manager.sh    # 🎯 统一管理脚本 (主要工具)
├── README.md                   # 📖 完整文档
├── INSTALLATION_GUIDE.md       # 🔧 安装指南
├── route-priority-manager.sh   # 🔄 原始脚本 (systemd服务用)
├── install-route-manager.sh    # 📥 systemd服务安装脚本
├── quick-install.sh            # ⚡ 快速安装脚本
├── manage-route-service.sh     # 🛠️ 服务管理脚本
├── install-nat-gateway.sh      # 🌐 NAT网关安装脚本
└── route-config-example.txt    # 📄 配置文件示例
```

## 🎯 推荐使用方式

### 日常管理 (推荐)
使用 **`unified-route-manager.sh`** - 功能最完整，操作最简单：

```bash
# 显示当前路由状态
./unified-route-manager.sh show

# 显示接口优先级排名
./unified-route-manager.sh ranking

# 设置WiFi为最高优先级
sudo ./unified-route-manager.sh set wlp1s0 10

# 设置移动网络为第二优先级  
sudo ./unified-route-manager.sh set wwan0 50

# 清理重复路由
sudo ./unified-route-manager.sh cleanup

# 自动选择最佳接口
sudo ./unified-route-manager.sh auto

# 检查网络连通性
./unified-route-manager.sh check
```

### systemd服务安装
如需要定时自动管理，使用安装脚本：

```bash
# 快速安装systemd服务
sudo ./quick-install.sh

# 或完整安装向导
sudo ./install-route-manager.sh --install
```

## 📋 快速开始

### 1. 查看当前状态
```bash
./unified-route-manager.sh show
./unified-route-manager.sh ranking
```

### 2. 清理重复路由
```bash
sudo ./unified-route-manager.sh cleanup
```

### 3. 设置网卡优先级
```bash
# WiFi最高优先级
sudo ./unified-route-manager.sh set wlp1s0 10

# 移动网络第二优先级
sudo ./unified-route-manager.sh set wwan0 50
```

### 4. 验证结果
```bash
./unified-route-manager.sh ranking
./unified-route-manager.sh check
```

## 📖 完整命令参考

### 路由管理命令
```bash
./unified-route-manager.sh show [detail]           # 显示当前路由状态
./unified-route-manager.sh ranking                 # 显示接口优先级排名
./unified-route-manager.sh set <接口> <优先级>     # 设置接口优先级
./unified-route-manager.sh auto [类型] [基础优先级] # 自动选择最佳接口
./unified-route-manager.sh cleanup [接口]          # 清理重复的默认路由
./unified-route-manager.sh batch <配置文件>        # 批量设置接口优先级
```

### 备份恢复命令
```bash
./unified-route-manager.sh backup                  # 备份当前路由表
./unified-route-manager.sh restore [备份文件]      # 恢复路由备份
```

### 诊断命令
```bash
./unified-route-manager.sh check                   # 检查网络连通性
./unified-route-manager.sh info                    # 显示系统网络信息
./unified-route-manager.sh status                  # 显示服务状态
```

### 配置命令
```bash
./unified-route-manager.sh sample [文件名]         # 创建示例配置文件
./unified-route-manager.sh help                    # 显示帮助信息
```

## 🔧 配置文件格式

批量配置文件格式 (用于 `batch` 命令):
```
# 接口:优先级
wlp1s0:10
wwan0:50
eth0:100
```

## 💡 使用建议

### 优先级设置建议
- **WiFi**: 10 (最高优先级)
- **移动网络**: 50 (第二优先级)
- **有线网络**: 100 (第三优先级)
- **其他接口**: 200+ (备用)

### 最佳实践
1. **定期清理**: 使用 `cleanup` 命令清理重复路由
2. **备份习惯**: 重要修改前使用 `backup` 命令备份
3. **连通性测试**: 修改后使用 `check` 命令验证网络
4. **状态监控**: 使用 `ranking` 命令查看当前优先级

## 🔍 故障排除

### 常见问题

**Q: 接口显示为 UNKNOWN 状态？**
A: 这是正常的，特别是对于点对点接口(如移动网络)。脚本已修复此显示问题。

**Q: 设置优先级后其他路由被删除？**
A: 已修复此问题。现在脚本只调整指定接口的优先级，不会删除其他路由。

**Q: 网络连接中断？**
A: 使用 `restore` 命令恢复备份，或手动检查路由表。

### 日志查看
```bash
# 查看操作日志
tail -f ~/.unified-route-manager.log

# 如果是root用户
tail -f /var/log/unified-route-manager.log
```

## 🔄 版本历史

### v2.0 (当前版本)
- ✅ 整合所有功能到统一脚本
- ✅ 修复接口状态显示问题
- ✅ 修复路由删除安全问题
- ✅ 添加重复路由清理功能
- ✅ 增强网络诊断功能
- ✅ 改进用户体验和错误处理

### v1.x (历史版本)
- 基础路由优先级管理
- systemd服务集成
- 多脚本分散管理

## 📄 许可证

MIT License - 自由使用和修改

## 🤝 贡献

欢迎提交问题报告和功能建议！

---

**推荐**: 日常使用 `unified-route-manager.sh`，需要自动化服务时安装 systemd 服务。
