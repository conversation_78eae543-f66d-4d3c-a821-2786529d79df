# 文件说明

## 📁 目录结构

```
route-manager/
├── unified-route-manager.sh    # 🎯 统一管理脚本 (推荐使用)
├── README.md                   # 📖 完整使用文档
├── INSTALLATION_GUIDE.md       # 🔧 安装指南
├── FILES.md                    # 📄 本文件说明
├── route-priority-manager.sh   # 🔄 原始脚本 (systemd服务用)
├── install-route-manager.sh    # 📥 systemd服务安装脚本
├── quick-install.sh            # ⚡ 快速安装脚本
├── manage-route-service.sh     # 🛠️ 服务管理脚本
├── install-nat-gateway.sh      # 🌐 NAT网关安装脚本
└── route-config-example.txt    # 📄 配置文件示例
```

## 🎯 主要文件

### unified-route-manager.sh ⭐
**推荐使用** - 统一的路由管理脚本，包含所有功能：
- 路由状态显示和优先级排名
- 智能设置接口优先级
- 重复路由清理
- 自动选择最佳接口
- 批量配置支持
- 网络诊断工具
- 路由备份和恢复

**使用示例**:
```bash
./unified-route-manager.sh show
./unified-route-manager.sh ranking
sudo ./unified-route-manager.sh set wlp1s0 10
sudo ./unified-route-manager.sh cleanup
```

### route-priority-manager.sh
原始的路由管理脚本，主要用于：
- systemd 服务后端
- 兼容旧版本使用
- 高级用户自定义

## 📥 安装脚本

### quick-install.sh ⚡
快速安装 systemd 服务的脚本：
- 自动检测网络环境
- 一键安装和配置
- 适合大多数用户

### install-route-manager.sh 📥
完整的安装向导：
- 详细配置选项
- 自定义设置
- 高级功能配置

### manage-route-service.sh 🛠️
服务管理脚本：
- 启动/停止服务
- 查看服务状态
- 卸载服务

## 🌐 扩展功能

### install-nat-gateway.sh
NAT网关安装脚本，用于：
- 配置网络地址转换
- 设置网关功能
- 网络共享配置

## 📄 配置文件

### route-config-example.txt
配置文件示例，包含：
- 接口优先级配置格式
- 常用配置示例
- 配置说明注释

## 🚀 使用建议

### 日常使用
```bash
# 推荐：使用统一脚本
./unified-route-manager.sh help
```

### 自动化服务
```bash
# 安装systemd服务
sudo ./quick-install.sh
```

### 高级配置
```bash
# 完整安装向导
sudo ./install-route-manager.sh --install
```

## 📖 文档

- **README.md**: 完整的使用文档和功能说明
- **INSTALLATION_GUIDE.md**: 详细的安装指南
- **FILES.md**: 本文件说明

## 🔄 版本历史

### v2.0 (当前)
- ✅ 整合所有功能到 `unified-route-manager.sh`
- ✅ 修复接口状态显示问题
- ✅ 修复路由删除安全问题
- ✅ 简化文件结构

### v1.x (历史)
- 多脚本分散管理
- 基础功能实现

---

**推荐**: 日常使用 `unified-route-manager.sh`，需要自动化时安装 systemd 服务。
