AMENT_PREFIX_PATH=/opt/ros/humble
AUTOJUMP_ERROR_PATH=/home/<USER>/.local/share/autojump/errors.log
AUTOJUMP_SOURCED=1
BUNDLED_DEBUGPY_PATH=/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.11.2025070101-linux-x64/bundled/libs/debugpy
CHROME_DESKTOP=code.desktop
CLAUDE_CODE_SSE_PORT=57743
COLCON=1
COLORTERM=truecolor
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
DEFAULTS_PATH=/usr/share/gconf/ubuntu.default.path
DESKTOP_SESSION=ubuntu
DISPLAY=:1
ENABLE_IDE_INTEGRATION=true
FZF_DEFAULT_COMMAND=fd --exclude={.git,.idea,.sass-cache,node_modules,build} --type f
FZF_DEFAULT_OPTS=--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'
GDK_BACKEND=x11
GDMSESSION=ubuntu
GEMINI_API_KEY=AIzaSyBHGmgSCnj9yEITCK_cpm62hfOf7kqNQik
GIO_LAUNCHED_DESKTOP_FILE=/usr/share/applications/code.desktop
GIO_LAUNCHED_DESKTOP_FILE_PID=83140
GIT_ASKPASS=/usr/share/code/resources/app/extensions/git/dist/askpass.sh
GJS_DEBUG_OUTPUT=stderr
GJS_DEBUG_TOPICS=JS ERROR;JS LOG
GNOME_DESKTOP_SESSION_ID=this-is-deprecated
GNOME_SHELL_SESSION_MODE=ubuntu
GOOGLE_CLOUD_PROJECT=dotted-chariot-464922-r5
GPG_AGENT_INFO=/run/user/1000/gnupg/S.gpg-agent:0:1
GPG_TTY=/dev/pts/14
GTK_IM_MODULE=fcitx
GTK_MODULES=gail:atk-bridge
HOME=/home/<USER>
INPUT_METHOD=fcitx
INVOCATION_ID=6637f72b0cd340819e24d9934224274e
JOURNAL_STREAM=8:309237
LANG=zh_CN.UTF-8
LANGUAGE=zh_CN:en
LC_ADDRESS=zh_CN.UTF-8
LC_IDENTIFICATION=zh_CN.UTF-8
LC_MEASUREMENT=zh_CN.UTF-8
LC_MONETARY=zh_CN.UTF-8
LC_NAME=zh_CN.UTF-8
LC_NUMERIC=zh_CN.UTF-8
LC_PAPER=zh_CN.UTF-8
LC_TELEPHONE=zh_CN.UTF-8
LC_TIME=zh_CN.UTF-8
LD_LIBRARY_PATH=/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/home/<USER>/.x-cmd.root/local/data/pkg/sphere/X/l/j/h/lib
LESS=-R
LOGNAME=xuhui
LSCOLORS=Gxfxcxdxbxegedabagacad
LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
MANAGERPID=80109
MANDATORY_PATH=/usr/share/gconf/ubuntu.mandatory.path
MANPATH=/home/<USER>/.nvm/versions/node/v22.17.1/share/man:/home/<USER>/.x-cmd.root/local/data/pkg/sphere/X/l/j/h/man:
NVM_BIN=/home/<USER>/.nvm/versions/node/v22.17.1/bin
NVM_CD_FLAGS=-q
NVM_DIR=/home/<USER>/.nvm
NVM_INC=/home/<USER>/.nvm/versions/node/v22.17.1/include/node
OLDPWD=/mine/note/Code/ROS/python_debug
ORIGINAL_XDG_CURRENT_DESKTOP=ubuntu:GNOME
PAGER=less
PAPERSIZE=a4
PATH=/opt/ros/humble/bin:/home/<USER>/.nvm/versions/node/v22.17.1/bin:/home/<USER>/.x-cmd.root/local/data/pkg/sphere/X/l/j/h/bin:/home/<USER>/.x-cmd.root/bin:/home/<USER>/anaconda3/bin:/home/<USER>/.cargo/bin:/home/<USER>/.local/bin:/home/<USER>/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.11.2025070101-linux-x64/bundled/scripts/noConfigScripts:/usr/local/go/bin:/home/<USER>/.pixi/bin:/home/<USER>/.x-cmd.root/local/data/triarii/bin
PWD=/mine/note/Code/ROS/python_debug/build/python_debug_node
PYDEVD_DISABLE_FILE_VALIDATION=1
PYTHONPATH=/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages
PYTHON_BASIC_REPL=1
QT_ACCESSIBILITY=1
QT_IM_MODULE=fcitx
ROS_DISTRO=humble
ROS_LOCALHOST_ONLY=0
ROS_PYTHON_VERSION=3
ROS_VERSION=2
SDL_IM_MODULE=fcitx
SESSION_MANAGER=local/xuhui-G1617-01:@/tmp/.ICE-unix/80332,unix/xuhui-G1617-01:/tmp/.ICE-unix/80332
SHELL=/usr/bin/zsh
SHLVL=1
SSH_AGENT_LAUNCHER=gnome-keyring
SSH_AUTH_SOCK=/run/user/1000/keyring/ssh
SYSTEMD_EXEC_PID=80361
TERM=xterm-256color
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.102.1
USER=xuhui
USERNAME=xuhui
USER_ZDOTDIR=/home/<USER>
VSCODE_DEBUGPY_ADAPTER_ENDPOINTS=/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.11.2025070101-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-4bede0c1fd7417be.txt
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=/usr/share/code/resources/app/extensions/git/dist/askpass-main.js
VSCODE_GIT_ASKPASS_NODE=/usr/share/code/code
VSCODE_GIT_IPC_HANDLE=/run/user/1000/vscode-git-0a53abc180.sock
VSCODE_INJECTION=1
WINDOWPATH=2
XAUTHORITY=/run/user/1000/gdm/Xauthority
XDG_CONFIG_DIRS=/etc/xdg/xdg-ubuntu:/etc/xdg
XDG_CURRENT_DESKTOP=Unity
XDG_DATA_DIRS=/usr/share/ubuntu:/usr/share/gnome:/home/<USER>/.local/share/flatpak/exports/share:/var/lib/flatpak/exports/share:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop:/opt/apps/amber-ce-bookworm/files/ace-env/amber-ce-tools/data-dir/
XDG_MENU_PREFIX=gnome-
XDG_RUNTIME_DIR=/run/user/1000
XDG_SESSION_CLASS=user
XDG_SESSION_DESKTOP=ubuntu
XDG_SESSION_TYPE=x11
XMODIFIERS=@im=fcitx
ZDOTDIR=/home/<USER>
ZSH=/home/<USER>/.oh-my-zsh
_=/usr/bin/env
___X_CMD_LOG_C_DEBUG=\033[2;35m
___X_CMD_LOG_C_ERROR=\033[1;31m
___X_CMD_LOG_C_INFO=\033[32m
___X_CMD_LOG_C_TF=
___X_CMD_LOG_C_WARN=\033[1;33m
