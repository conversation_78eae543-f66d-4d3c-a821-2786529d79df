{"version": "0.2.0", "configurations": [{"name": "ROS2 Debug Node (Direct)", "type": "python", "request": "launch", "program": "${workspaceFolder}/python_debug_node/debug_node.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}:${env:PYTHONPATH}", "ROS_DOMAIN_ID": "0", "AMENT_PREFIX_PATH": "/opt/ros/humble", "PATH": "/opt/ros/humble/bin:${env:PATH}", "LD_LIBRARY_PATH": "/opt/ros/humble/lib:${env:LD_LIBRARY_PATH}", "HOME": "${env:HOME}", "RCL_LOGGING_USE_STDOUT": "1", "RCUTILS_LOGGING_USE_STDOUT": "1"}, "args": [], "justMyCode": false, "stopOnEntry": false}, {"name": "ROS2 Debug Node (Module)", "type": "python", "request": "launch", "module": "python_debug_node.debug_node", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}:${env:PYTHONPATH}", "ROS_DOMAIN_ID": "0", "AMENT_PREFIX_PATH": "/opt/ros/humble", "PATH": "/opt/ros/humble/bin:${env:PATH}", "LD_LIBRARY_PATH": "/opt/ros/humble/lib:${env:LD_LIBRARY_PATH}", "HOME": "${env:HOME}", "RCL_LOGGING_USE_STDOUT": "1", "RCUTILS_LOGGING_USE_STDOUT": "1"}, "args": [], "justMyCode": false, "stopOnEntry": false}, {"name": "ROS2 Launch File Debug (Fixed)", "type": "python", "request": "launch", "program": "/opt/ros/humble/bin/ros2", "args": ["launch", "${workspaceFolder}/launch/debug_node_launch.py", "--ros-args", "--log-level", "DEBUG"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}:${env:PYTHONPATH}", "ROS_DOMAIN_ID": "0", "AMENT_PREFIX_PATH": "/opt/ros/humble:${workspaceFolder}/install", "PATH": "/opt/ros/humble/bin:${env:PATH}", "CMAKE_PREFIX_PATH": "/opt/ros/humble:${workspaceFolder}/install", "LD_LIBRARY_PATH": "/opt/ros/humble/lib:${env:LD_LIBRARY_PATH}", "HOME": "${env:HOME}", "RCL_LOGGING_USE_STDOUT": "1", "RCUTILS_LOGGING_USE_STDOUT": "1", "RCUTILS_CONSOLE_OUTPUT_FORMAT": "[{severity}] [{time}] [{name}]: {message}"}, "justMyCode": false, "stopOnEntry": false}, {"name": "ROS2 Launch with Verbose Logs", "type": "python", "request": "launch", "program": "/bin/bash", "args": ["-c", "export ROS_DOMAIN_ID=0 && export LD_LIBRARY_PATH=/opt/ros/humble/lib:$LD_LIBRARY_PATH && export PATH=/opt/ros/humble/bin:$PATH && export PYTHONPATH=${workspaceFolder}:$PYTHONPATH && export RCL_LOGGING_USE_STDOUT=1 && export RCUTILS_LOGGING_USE_STDOUT=1 && export RCUTILS_CONSOLE_OUTPUT_FORMAT='[{severity}] [{time}] [{name}]: {message}' && python3 /opt/ros/humble/bin/ros2 launch ${workspaceFolder}/launch/debug_node_launch.py --ros-args --log-level DEBUG"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"HOME": "${env:HOME}"}, "justMyCode": false, "stopOnEntry": false}, {"name": "Attach to ROS2 Node", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}"}], "justMyCode": false}]}