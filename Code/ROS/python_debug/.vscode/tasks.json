{"version": "2.0.0", "tasks": [{"label": "colcon build", "type": "shell", "command": "colcon", "args": ["build", "--packages-select", "python_debug_node", "--symlink-install"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "source and run node", "type": "shell", "command": "bash", "args": ["-c", "source install/setup.bash && ros2 run python_debug_node debug_node"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "dependsOn": "colcon build"}, {"label": "source and launch", "type": "shell", "command": "bash", "args": ["-c", "source install/setup.bash && ros2 launch python_debug_node debug_node_launch.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "dependsOn": "colcon build"}]}