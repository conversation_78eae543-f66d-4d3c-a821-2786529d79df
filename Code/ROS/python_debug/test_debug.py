#!/usr/bin/env python3
"""
测试脚本：用于验证ROS2调试节点的功能
"""

import os
import sys
import time
import subprocess
import threading

def setup_environment():
    """设置ROS2环境变量"""
    os.environ['ROS_DOMAIN_ID'] = '0'
    os.environ['LD_LIBRARY_PATH'] = '/opt/ros/humble/lib:' + os.environ.get('LD_LIBRARY_PATH', '')
    os.environ['PATH'] = '/opt/ros/humble/bin:' + os.environ.get('PATH', '')
    os.environ['PYTHONPATH'] = '/mine/note/Code/ROS/python_debug:' + os.environ.get('PYTHONPATH', '')
    os.environ['RCL_LOGGING_USE_STDOUT'] = '1'
    os.environ['RCUTILS_LOGGING_USE_STDOUT'] = '1'

def test_direct_run():
    """测试直接运行节点"""
    print("=== 测试1: 直接运行节点 ===")
    setup_environment()
    
    try:
        # 启动节点
        process = subprocess.Popen([
            'python3', 
            '/mine/note/Code/ROS/python_debug/python_debug_node/debug_node.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待5秒
        time.sleep(5)
        
        # 终止进程
        process.terminate()
        stdout, stderr = process.communicate(timeout=5)
        
        print("节点输出:")
        print(stdout)
        if stderr:
            print("错误输出:")
            print(stderr)
            
        return "Debug Node 已启动" in stdout
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_launch_file():
    """测试Launch文件"""
    print("\n=== 测试2: Launch文件运行 ===")
    setup_environment()
    
    try:
        # 启动launch文件
        process = subprocess.Popen([
            'python3', 
            '/opt/ros/humble/bin/ros2',
            'launch',
            '/mine/note/Code/ROS/python_debug/launch/debug_node_launch.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待5秒
        time.sleep(5)
        
        # 终止进程
        process.terminate()
        stdout, stderr = process.communicate(timeout=5)
        
        print("Launch输出:")
        print(stdout)
        if stderr:
            print("错误输出:")
            print(stderr)
            
        return "process started" in stdout
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_topic_communication():
    """测试话题通信"""
    print("\n=== 测试3: 话题通信 ===")
    setup_environment()
    
    try:
        # 启动节点
        node_process = subprocess.Popen([
            'python3', 
            '/mine/note/Code/ROS/python_debug/python_debug_node/debug_node.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待节点启动
        time.sleep(2)
        
        # 发布测试消息
        pub_process = subprocess.Popen([
            'python3', '/opt/ros/humble/bin/ros2',
            'topic', 'pub', '--once',
            '/input_topic', 'std_msgs/String',
            'data: "Test Message"'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待消息处理
        time.sleep(3)
        
        # 终止进程
        node_process.terminate()
        pub_process.terminate()
        
        stdout, stderr = node_process.communicate(timeout=5)
        
        print("节点输出:")
        print(stdout)
        
        return "收到消息" in stdout or "Test Message" in stdout
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始ROS2调试节点测试...")
    
    results = []
    
    # 运行测试
    results.append(("直接运行节点", test_direct_run()))
    results.append(("Launch文件运行", test_launch_file()))
    results.append(("话题通信", test_topic_communication()))
    
    # 显示结果
    print("\n=== 测试结果 ===")
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    # 总结
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！调试环境配置正确。")
        return 0
    else:
        print("❌ 部分测试失败，请检查配置。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
