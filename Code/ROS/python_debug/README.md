# ROS2 Python Debug Node

这是一个用于调试的ROS2 Python节点项目，包含完整的VSCode调试配置。

## 项目结构

```
python_debug/
├── .vscode/
│   ├── launch.json          # VSCode调试配置
│   └── tasks.json           # VSCode任务配置
├── python_debug_node/
│   ├── __init__.py
│   └── debug_node.py        # 主要的节点代码
├── launch/
│   └── debug_node_launch.py # Launch文件
├── resource/
│   └── python_debug_node
├── package.xml              # ROS2包配置
├── setup.py                 # Python包配置
└── setup.cfg               # 安装配置
```

## 功能特性

- 发布消息到 `debug_topic` 话题
- 订阅 `input_topic` 话题
- 定时器每2秒发布一次消息
- 包含多个调试点和变量
- 完整的VSCode调试配置

## 构建和运行

### 1. 直接运行节点（推荐用于调试）

```bash
export ROS_DOMAIN_ID=0
export LD_LIBRARY_PATH=/opt/ros/humble/lib:$LD_LIBRARY_PATH
export PYTHONPATH=/mine/note/Code/ROS/python_debug:$PYTHONPATH
python3 python_debug_node/debug_node.py
```

### 2. 使用Launch文件运行

```bash
export ROS_DOMAIN_ID=0
export LD_LIBRARY_PATH=/opt/ros/humble/lib:$LD_LIBRARY_PATH
export PATH=/opt/ros/humble/bin:$PATH
python3 /opt/ros/humble/bin/ros2 launch /mine/note/Code/ROS/python_debug/launch/debug_node_launch.py
```

### 3. 构建包（可选）

```bash
colcon build --packages-select python_debug_node --symlink-install
```

## VSCode调试配置

项目包含4种调试配置：

### 1. ROS2 Debug Node
直接调试Python文件，适合快速调试。

### 2. ROS2 Debug Node (with ROS2 run)
使用模块方式运行，更接近实际运行环境。

### 3. ROS2 Launch File Debug
调试Launch文件启动的节点。

### 4. Attach to ROS2 Node
附加到正在运行的节点进行调试。

## 调试步骤

1. 在VSCode中打开项目
2. 在 `debug_node.py` 中设置断点
3. 按 `F5` 或选择调试配置启动调试
4. 使用调试控制台查看变量值

## 测试调试功能

### 发送测试消息

```bash
# 在另一个终端中
source install/setup.bash
ros2 topic pub /input_topic std_msgs/String "data: 'Hello Debug'"
```

### 查看发布的消息

```bash
# 在另一个终端中
source install/setup.bash
ros2 topic echo /debug_topic
```

## 调试技巧

1. 在 `timer_callback()` 函数中设置断点
2. 在 `process_debug_info()` 函数中查看调试信息
3. 在 `listener_callback()` 函数中调试消息处理
4. 使用VSCode的变量监视器查看节点状态

## 环境要求

- ROS2 Humble (或其他版本)
- Python 3.8+
- VSCode with Python extension
- colcon build tool
