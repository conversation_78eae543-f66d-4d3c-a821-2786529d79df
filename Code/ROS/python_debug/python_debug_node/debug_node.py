#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
import time


class DebugNode(Node):
    """
    一个用于调试的ROS2 Python节点
    """
    
    def __init__(self):
        super().__init__('debug_node')
        
        # 创建发布者
        self.publisher_ = self.create_publisher(String, 'debug_topic', 10)
        
        # 创建订阅者
        self.subscription = self.create_subscription(
            String,
            'input_topic',
            self.listener_callback,
            10
        )
        self.subscription  # 防止未使用变量警告
        
        # 创建定时器，每2秒发布一次消息
        self.timer = self.create_timer(2.0, self.timer_callback)
        
        # 计数器
        self.counter = 0
        
        self.get_logger().info('Debug Node 已启动')
    
    def timer_callback(self):
        """定时器回调函数"""
        msg = String()
        msg.data = f'Hello World: {self.counter}'
        
        # 这里可以设置断点进行调试
        self.publisher_.publish(msg)
        self.get_logger().info(f'发布消息: "{msg.data}"')
        
        self.counter += 1
        
        # 添加一些调试用的变量
        debug_info = {
            'counter': self.counter,
            'timestamp': time.time(),
            'node_name': self.get_name()
        }
        
        # 可以在这里设置断点查看debug_info
        self.process_debug_info(debug_info)
    
    def process_debug_info(self, debug_info):
        """处理调试信息的函数"""
        # 这个函数专门用于调试
        if debug_info['counter'] % 5 == 0:
            self.get_logger().warn(f'计数器达到5的倍数: {debug_info["counter"]}')
    
    def listener_callback(self, msg):
        """订阅者回调函数"""
        self.get_logger().info(f'收到消息: "{msg.data}"')
        
        # 处理接收到的消息
        response_msg = String()
        response_msg.data = f'回复: {msg.data}'
        
        # 延迟发布回复
        time.sleep(0.1)
        self.publisher_.publish(response_msg)


def main(args=None):
    """主函数"""
    rclpy.init(args=args)
    
    debug_node = DebugNode()
    
    try:
        rclpy.spin(debug_node)
    except KeyboardInterrupt:
        debug_node.get_logger().info('节点被用户中断')
    finally:
        debug_node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
