import os
from launch import LaunchDescription
from launch.actions import ExecuteProcess
from launch_ros.actions import Node


def generate_launch_description():
    # 获取当前文件的目录
    current_dir = os.path.dirname(os.path.realpath(__file__))
    workspace_dir = os.path.dirname(current_dir)
    node_script = os.path.join(workspace_dir, 'python_debug_node', 'debug_node.py')

    return LaunchDescription([
        # 方法1: 使用ExecuteProcess直接运行Python脚本
        ExecuteProcess(
            cmd=['python3', node_script],
            output='both',  # 同时输出到屏幕和日志
            name='debug_node_process',
            env={
                'ROS_DOMAIN_ID': '0',
                'PYTHONPATH': workspace_dir + ':' + os.environ.get('PYTHONPATH', ''),
                'AMENT_PREFIX_PATH': '/opt/ros/humble',
                'PATH': '/opt/ros/humble/bin:' + os.environ.get('PATH', ''),
                'LD_LIBRARY_PATH': '/opt/ros/humble/lib:' + os.environ.get('LD_LIBRARY_PATH', ''),
                'HOME': os.environ.get('HOME', '/tmp'),
                'RCL_LOGGING_USE_STDOUT': '1',
                'RCUTILS_LOGGING_USE_STDOUT': '1',
                'RCUTILS_CONSOLE_OUTPUT_FORMAT': '[{severity}] [{time}] [{name}]: {message}',
                'RCUTILS_COLORIZED_OUTPUT': '1'  # 启用彩色输出
            }
        ),

        # 方法2: 如果包已正确安装，可以使用Node
        # Node(
        #     package='python_debug_node',
        #     executable='debug_node',
        #     name='debug_node',
        #     output='screen',
        #     parameters=[
        #         {'use_sim_time': False}
        #     ],
        #     remappings=[
        #         # 可以在这里添加话题重映射
        #     ]
        # )
    ])
