# Gen3 网络管理系统

## 概述

Gen3网络管理系统是一个基于ROS2 Foxy的网络监控和管理系统，用于管理机器人的网络连接，包括WiFi、5G和以太网。系统提供了网络状态监控、自动网络切换、WiFi管理、设备绑定等功能。

## 系统架构

系统包含以下6个核心模块：
- **网络管理器 (NetworkManager)** - 负责整体网络状态管理和对外接口
- **WiFi管理器 (WiFiManager)** - 负责WiFi网络的连接和管理
- **网络切换器 (NetworkSwitch)** - 负责在不同网络之间进行切换
- **绑定管理器 (BindingManager)** - 负责设备绑定流程
- **网络监控器 (NetworkMonitor)** - 负责监控网络状态和质量
- **DNS管理器 (DNSManager)** - 负责DNS服务器管理

## 功能特性

- 实时监控网络状态和质量
- 自动在WiFi、5G和以太网之间切换
- WiFi网络扫描、连接和管理
- 设备绑定（支持BLE和二维码方式）
- DNS服务器配置和管理
- 网络连通性测试和诊断

## 安装和编译

### 依赖项

- ROS2 Foxy
- C++14或更高版本
- Linux操作系统（已在Ubuntu 20.04上测试）

### 编译

```bash
# 创建工作空间
mkdir -p ~/ros2_ws/src
cd ~/ros2_ws/src

# 克隆代码
git clone https://github.com/example/gen3_network_manager.git

# 编译
cd ~/ros2_ws
colcon build --symlink-install
```

## 使用方法

### 启动网络管理系统

```bash
# 设置环境
source ~/ros2_ws/install/setup.bash

# 启动网络管理器
ros2 launch gen3_network_manager_core network_manager.launch.py
```

### 查看网络状态

```bash
# 查看当前网络状态
ros2 topic echo /gen3/network/status

# 查看网络质量
ros2 topic echo /gen3/network/quality
```

### 手动切换网络

```bash
# 切换到WiFi
ros2 service call /gen3/network/switch gen3_network_interfaces/srv/SwitchNetwork "{target_network_type: 1, target_wifi_ssid: 'MyWiFi', force_switch: false, timeout_seconds: 30}"

# 自动选择最优网络
ros2 service call /gen3/network/switch gen3_network_interfaces/srv/SwitchNetwork "{target_network_type: 0, force_switch: false, timeout_seconds: 30}"
```

### 连接WiFi网络

```bash
# 连接新的WiFi网络
ros2 service call /gen3/network/connect_wifi gen3_network_interfaces/srv/ConnectWiFi "{ssid: 'MyWiFi', password: 'mypassword', save_to_list: true, priority: 100, timeout_seconds: 30}"
```

### 启动设备绑定

```bash
# 启动BLE绑定
ros2 service call /gen3/network/start_binding gen3_network_interfaces/srv/StartBinding "{binding_method: 1, timeout_seconds: 300, device_name: 'Gen3Robot'}"

# 使用动作进行绑定（支持进度反馈）
ros2 action send_goal /gen3/network/binding gen3_network_interfaces/action/NetworkBinding "{binding_method: 3, timeout_seconds: 300, device_name: 'Gen3Robot'}"
```

## 配置

配置文件位于`config/network_config.yaml`，可以根据需要修改网络参数：

- 网络检查间隔
- 自动切换设置
- WiFi配置
- 5G配置
- DNS服务器设置
- 绑定设置

## 许可证

Apache License 2.0 