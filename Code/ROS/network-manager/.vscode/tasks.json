{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "colcon build",
            "type": "shell",
            "command": "./build.sh",
            // "args": [
            //     "${input:package}"
            // ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "reveal": "always",
                "panel": "new",
                "close": true,
                "focus": false,
                "showReuseMessage": false,
                "clear": true
            },
            "problemMatcher": [
                "$gcc"
            ],
            "runOptions": {
                "runOn": "folderOpen"
            }
        },
        {
            "label": "colcon build release",
            "type": "shell",
            "command": "./build.sh",
            "args": [
                "--release",
                "${input:package}"
            ],
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "new",
                "close": true,
                "focus": false,
                "showReuseMessage": false,
                "clear": true
            },
            "problemMatcher": [
                "$gcc"
            ]
        },
        {
            "label": "colcon build clean",
            "type": "shell",
            "command": "./build.sh",
            "args": [
                "--clean",
                "${input:package}"
            ],
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "problemMatcher": [
                "$gcc"
            ]
        },
        {
            "label": "colcon build verbose",
            "type": "shell",
            "command": "./build.sh",
            "args": [
                "--verbose",
                "${input:package}"
            ],
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "problemMatcher": [
                "$gcc"
            ]
        },
        {
            "label": "source setup",
            "type": "shell",
            "command": "source",
            "args": [
                "./install/setup.bash"
            ],
            "group": "build",
            "presentation": {
                "reveal": "silent",
                "panel": "shared"
            }
        },
        {
            "label": "run network_manager_node",
            "type": "shell",
            "command": "bash",
            "args": [
                "-c",
                "source ./install/setup.bash && ros2 run gen3_network_manager_core network_manager_node"
            ],
            "group": "test",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "dependsOn": "colcon build"
        },
        {
            "label": "run network_manager_node with config",
            "type": "shell",
            "command": "bash",
            "args": [
                "-c",
                "source ./install/setup.bash && ros2 run gen3_network_manager_core network_manager_node --ros-args --params-file ./install/$(uname -m)/Debug/gen3_network_manager_core/share/gen3_network_manager_core/config/network_config.yaml --log-level debug"
            ],
            "group": "test",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "dependsOn": "colcon build"
        },
        {
            "label": "run network_manager_node with dev config",
            "type": "shell",
            "command": "bash",
            "args": [
                "-c",
                "source ./install/setup.bash && ros2 run gen3_network_manager_core network_manager_node --ros-args --params-file ./install/$(uname -m)/Debug/gen3_network_manager_core/share/gen3_network_manager_core/config/development_config.yaml --log-level debug"
            ],
            "group": "test",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "dependsOn": "colcon build"
        },
        {
            "label": "launch network_manager",
            "type": "shell",
            "command": "bash",
            "args": [
                "-c",
                "source ./install/setup.bash && ros2 launch gen3_network_manager_core network_manager.launch.py"
            ],
            "group": "test",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "dependsOn": "colcon build"
        },
        {
            "label": "launch network_manager with dev config",
            "type": "shell",
            "command": "bash",
            "args": [
                "-c",
                "source ./install/setup.bash && ros2 launch gen3_network_manager_core network_manager.launch.py config_file:=./install/$(uname -m)/Debug/gen3_network_manager_core/share/gen3_network_manager_core/config/development_config.yaml log_level:=debug"
            ],
            "group": "test",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "dependsOn": "colcon build"
        },
        {
            "label": "launch network_manager with production config",
            "type": "shell",
            "command": "bash",
            "args": [
                "-c",
                "source ./install/setup.bash && ros2 launch gen3_network_manager_core network_manager.launch.py config_file:=./install/$(uname -m)/Debug/gen3_network_manager_core/share/gen3_network_manager_core/config/production_config.yaml log_level:=info"
            ],
            "group": "test",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "dependsOn": "colcon build"
        },
        {
            "label": "ros2 topic list",
            "type": "shell",
            "command": "bash",
            "args": [
                "-c",
                "source ./install/setup.bash && ros2 topic list"
            ],
            "group": "test",
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "ros2 node list",
            "type": "shell",
            "command": "bash",
            "args": [
                "-c",
                "source ./install/setup.bash && ros2 node list"
            ],
            "group": "test",
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "update launch config",
            "type": "shell",
            "command": "./scripts/update_launch_config.sh",
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "set markdown preview mode",
            "type": "shell",
            "command": "./scripts/set_markdown_mode.sh",
            "args": ["preview"],
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "set markdown editor mode",
            "type": "shell",
            "command": "./scripts/set_markdown_mode.sh",
            "args": ["editor"],
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        }
    ],
    "inputs": [
        {
            "id": "package",
            "type": "promptString",
            "description": "要编译的包名 (留空编译所有包)",
            "default": ""
        },
        {
            "id": "architecture",
            "type": "command",
            "command": "shellCommand.execute",
            "args": {
                "command": "uname -m"
            }
        }
    ]
}
