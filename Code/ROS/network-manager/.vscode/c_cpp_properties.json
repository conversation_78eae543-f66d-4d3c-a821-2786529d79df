{"configurations": [{"name": "Linux", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/src/**", "${workspaceFolder}/install/*/include/**", "/opt/ros/humble/include/**", "/usr/include/**"], "defines": ["_GNU_SOURCE", "ROS_PACKAGE_NAME=\"gen3_network_manager_core\""], "compilerPath": "/usr/bin/gcc", "cStandard": "gnu17", "cppStandard": "gnu++17", "intelliSenseMode": "linux-gcc-x64", "compileCommands": "${workspaceFolder}/compile_commands.json", "configurationProvider": "ms-vscode.cmake-tools"}], "version": 4}