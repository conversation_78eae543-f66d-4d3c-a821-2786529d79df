{"version": "0.2.0", "configurations": [{"name": "ROS2 C++ Debug with Config (Auto-Arch)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/lib/gen3_network_manager_core/network_manager_node", "args": ["--ros-args", "--params-file", "${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/share/gen3_network_manager_core/config/network_config.yaml", "--log-level", "debug"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "LD_LIBRARY_PATH", "value": "/opt/ros/humble/lib:${workspaceFolder}/install/x86_64/Debug/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_interfaces/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/lib:${env:LD_LIBRARY_PATH}"}, {"name": "AMENT_PREFIX_PATH", "value": "/opt/ros/humble:${workspaceFolder}/install/x86_64/Debug:${env:AMENT_PREFIX_PATH}"}, {"name": "ROS_DOMAIN_ID", "value": "0"}, {"name": "ROS_DISTRO", "value": "humble"}, {"name": "ROS_VERSION", "value": "2"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "设置反汇编风格为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}, {"description": "忽略SIGPIPE信号", "text": "handle SIGPIPE nostop noprint pass", "ignoreFailures": true}], "preLaunchTask": "colcon build", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "ROS2 C++ Debug with Dev Config (Auto-Arch)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/lib/gen3_network_manager_core/network_manager_node", "args": ["--ros-args", "--params-file", "${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/share/gen3_network_manager_core/config/development_config.yaml", "--log-level", "debug", "--remap", "__node:=gen3_network_manager_debug"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "LD_LIBRARY_PATH", "value": "/opt/ros/humble/lib:${workspaceFolder}/install/x86_64/Debug/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_interfaces/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/lib:${env:LD_LIBRARY_PATH}"}, {"name": "AMENT_PREFIX_PATH", "value": "/opt/ros/humble:${workspaceFolder}/install/x86_64/Debug:${env:AMENT_PREFIX_PATH}"}, {"name": "ROS_DOMAIN_ID", "value": "0"}, {"name": "ROS_DISTRO", "value": "humble"}, {"name": "ROS_VERSION", "value": "2"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "设置反汇编风格为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}, {"description": "忽略SIGPIPE信号", "text": "handle SIGPIPE nostop noprint pass", "ignoreFailures": true}], "preLaunchTask": "colcon build for debug", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "ROS2 Launch with Custom Config", "type": "cppdbg", "request": "launch", "program": "/opt/ros/humble/bin/ros2", "args": ["launch", "${workspaceFolder}/src/gen3_network_manager_core/launch/network_manager.launch.py", "config_file:=${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/share/gen3_network_manager_core/config/development_config.yaml", "log_level:=debug"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "LD_LIBRARY_PATH", "value": "/opt/ros/humble/lib:${workspaceFolder}/install/x86_64/Debug/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_interfaces/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/lib:${env:LD_LIBRARY_PATH}"}, {"name": "AMENT_PREFIX_PATH", "value": "/opt/ros/humble:${workspaceFolder}/install/x86_64/Debug:${env:AMENT_PREFIX_PATH}"}, {"name": "ROS_DOMAIN_ID", "value": "0"}, {"name": "ROS_DISTRO", "value": "humble"}, {"name": "ROS_VERSION", "value": "2"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "colcon build", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "ROS2 C++ Debug with Config (x86_64 Fixed)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/lib/gen3_network_manager_core/network_manager_node", "args": ["--ros-args", "--params-file", "${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/share/gen3_network_manager_core/config/network_config.yaml", "--log-level", "debug"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "LD_LIBRARY_PATH", "value": "/opt/ros/humble/lib:${workspaceFolder}/install/x86_64/Debug/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_interfaces/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/lib:${env:LD_LIBRARY_PATH}"}, {"name": "AMENT_PREFIX_PATH", "value": "/opt/ros/humble:${workspaceFolder}/install/x86_64/Debug:${env:AMENT_PREFIX_PATH}"}, {"name": "ROS_DOMAIN_ID", "value": "0"}, {"name": "ROS_DISTRO", "value": "humble"}, {"name": "ROS_VERSION", "value": "2"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "设置反汇编风格为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}, {"description": "忽略SIGPIPE信号", "text": "handle SIGPIPE nostop noprint pass", "ignoreFailures": true}], "preLaunchTask": "colcon build", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "ROS2 Launch File Debug", "type": "cppdbg", "request": "launch", "program": "/opt/ros/humble/bin/ros2", "args": ["launch", "${workspaceFolder}/src/gen3_network_manager_core/launch/network_manager.launch.py"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "LD_LIBRARY_PATH", "value": "/opt/ros/humble/lib:${workspaceFolder}/install/x86_64/Debug/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_interfaces/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/lib:${env:LD_LIBRARY_PATH}"}, {"name": "AMENT_PREFIX_PATH", "value": "/opt/ros/humble:${workspaceFolder}/install/x86_64/Debug:${env:AMENT_PREFIX_PATH}"}, {"name": "ROS_DOMAIN_ID", "value": "0"}, {"name": "ROS_DISTRO", "value": "humble"}, {"name": "ROS_VERSION", "value": "2"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "colcon build", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "ROS2 C++ Debug (Auto-Arch)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/lib/gen3_network_manager_core/network_manager_node", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "LD_LIBRARY_PATH", "value": "/opt/ros/humble/lib:${workspaceFolder}/install/x86_64/Debug/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_interfaces/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/lib:${env:LD_LIBRARY_PATH}"}, {"name": "AMENT_PREFIX_PATH", "value": "/opt/ros/humble:${workspaceFolder}/install/x86_64/Debug:${env:AMENT_PREFIX_PATH}"}, {"name": "ROS_DOMAIN_ID", "value": "0"}, {"name": "ROS_DISTRO", "value": "humble"}, {"name": "ROS_VERSION", "value": "2"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "设置反汇编风格为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}, {"description": "忽略SIGPIPE信号", "text": "handle SIGPIPE nostop noprint pass", "ignoreFailures": true}], "preLaunchTask": "colcon build", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "ROS2 C++ Debug (x86_64 Fixed)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/lib/gen3_network_manager_core/network_manager_node", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "LD_LIBRARY_PATH", "value": "/opt/ros/humble/lib:${workspaceFolder}/install/x86_64/Debug/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_interfaces/lib:${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/lib:${env:LD_LIBRARY_PATH}"}, {"name": "AMENT_PREFIX_PATH", "value": "/opt/ros/humble:${workspaceFolder}/install/x86_64/Debug:${env:AMENT_PREFIX_PATH}"}, {"name": "ROS_DOMAIN_ID", "value": "0"}, {"name": "ROS_DISTRO", "value": "humble"}, {"name": "ROS_VERSION", "value": "2"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "设置反汇编风格为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}, {"description": "忽略SIGPIPE信号", "text": "handle SIGPIPE nostop noprint pass", "ignoreFailures": true}], "preLaunchTask": "colcon build", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "ROS2 C++ Release Debug (Auto-Arch)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/install/x86_64/Release/gen3_network_manager_core/lib/gen3_network_manager_core/network_manager_node", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "LD_LIBRARY_PATH", "value": "/opt/ros/humble/lib:${workspaceFolder}/install/x86_64/Release/lib:${workspaceFolder}/install/x86_64/Release/gen3_network_interfaces/lib:${workspaceFolder}/install/x86_64/Release/gen3_network_manager_core/lib:${env:LD_LIBRARY_PATH}"}, {"name": "AMENT_PREFIX_PATH", "value": "/opt/ros/humble:${workspaceFolder}/install/x86_64/Release:${env:AMENT_PREFIX_PATH}"}, {"name": "ROS_DOMAIN_ID", "value": "0"}, {"name": "ROS_DISTRO", "value": "humble"}, {"name": "ROS_VERSION", "value": "2"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "设置反汇编风格为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}, {"description": "忽略SIGPIPE信号", "text": "handle SIGPIPE nostop noprint pass", "ignoreFailures": true}], "preLaunchTask": "colcon build release", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "ROS2 C++ Attach to Process (Auto-Arch)", "type": "cppdbg", "request": "attach", "program": "${workspaceFolder}/install/x86_64/Debug/gen3_network_manager_core/lib/gen3_network_manager_core/network_manager_node", "processId": "${command:pickProcess}", "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "设置反汇编风格为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "miDebuggerPath": "/usr/bin/gdb"}], "inputs": [{"id": "architecture", "type": "pickString", "description": "选择目标架构", "options": ["x86_64", "aarch64", "arm64"], "default": "x86_64"}]}