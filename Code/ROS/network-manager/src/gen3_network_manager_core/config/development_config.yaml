/**:
  ros__parameters:
    # 开发环境配置 - 高频监控便于调试

    # 日志配置
    log_level: "debug"  # 开发环境使用debug级别

    # 定时器间隔配置（秒）- 对应代码中的参数声明
    network_check_interval: 0.5      # 网络监控 0.5秒 (network_monitor.cpp)
    quality_check_interval: 2.0      # 质量检查 2秒 (network_switch.cpp)
    connectivity_check_interval: 5.0 # 连通性检查 5秒 (network_monitor.cpp)
    wifi_scan_interval: 30.0         # WiFi扫描 30秒
    switch_monitor_interval: 0.5     # 切换监控 0.5秒 (network_switch.cpp)
    dns_check_interval: 15.0         # DNS检查 15秒
    status_update_interval: 3.0      # 状态更新 3秒
    binding_status_interval: 1.0     # 绑定状态 1秒
    
    # 网络功能配置
    enable_auto_switch: "true"
    preferred_network_type: 1        # WiFi优先
    min_signal_strength: -80         # 更宽松的信号要求
    min_connectivity_score: 30.0    # 更宽松的质量要求

    # NAT和内网卡配置
    enable_nat: true
    lan_interfaces: ["eth0", "eth1"]  # 内网卡列表
    
    # WiFi设置
    wifi:
      min_signal_strength: -80
      reconnect_attempts: 5          # 更多重连尝试
      scan_interval: 30.0
      known_networks:
        - ssid: "Dev_WiFi"
          priority: 100
          auto_connect: "true"
        - ssid: "Test_Network"
          priority: 90
          auto_connect: "true"
    
    # 5G设置
    5g:
      apn: "cmnet"
      pin: ""
      connect_timeout: 60.0          # 更长的连接超时
      auto_reconnect: "true"
    
    # === 智能网络切换设置 ===
    network_switch:
      auto_switch_enabled: true
      preferred_network_type: 1
      min_signal_strength: -80
      min_connectivity_score: 30.0
      cooldown_period: 30.0          # 更短的冷却时间

      # 科学化质量评分权重（开发环境优化）
      quality_weights:
        latency: 0.35              # 开发环境更重视延迟
        packet_loss: 0.25          # 丢包率权重
        signal_strength: 0.15      # 信号强度权重
        connectivity: 0.25         # 连通性权重
        # 带宽评分已暂时去掉
        # download_speed: 0.0
        # upload_speed: 0.0

      # 开发环境延迟期望值配置（毫秒）- 相对宽松的期望值
      expected_latency:
        wifi: 25.0        # WiFi网络期望延迟（开发环境稍宽松）
        fiveg: 20.0       # 5G网络期望延迟（开发环境稍宽松）
        ethernet: 8.0     # 以太网期望延迟（开发环境稍宽松）
        default: 60.0     # 默认期望延迟（开发环境稍宽松）

    # === 智能DNS管理设置 ===
    # DNS检测和切换参数（开发环境优化）
    dns_test_timeout_ms: 5000        # 开发环境使用更长超时
    dns_max_consecutive_failures: 2  # 开发环境更快触发切换
    dns_priority_update_threshold: 0.7 # 开发环境更敏感的优先级更新

    # DNS服务器配置
    dns_servers:
      # 国际DNS服务器
      primary: ["8.8.8.8", "1.1.1.1"]
      secondary: ["114.114.114.114", "223.5.5.5"]
      backup: ["208.67.222.222", "9.9.9.9"]

      # 中国优化DNS服务器配置
      china_primary: ["114.114.114.114", "223.5.5.5"]    # 114DNS, 阿里DNS
      china_secondary: ["119.29.29.29", "180.76.76.76"]  # 腾讯DNS, 百度DNS
      china_backup: ["1.2.4.8", "210.2.4.8"]            # 中国电信DNS
      international: ["8.8.8.8", "1.1.1.1", "208.67.222.222"]

    # === 中国网络环境检测配置 ===
    china_network:
      # 开发环境启用中国网络检测
      enable_detection: true

      # 开发环境更频繁的检测间隔
      detection_interval: 180.0  # 3分钟检测一次

      # 优先使用国内DNS
      prefer_domestic_dns: true

      # 国内网站测试列表（开发环境精简）
      domestic_test_sites:
        - "www.baidu.com"
        - "www.qq.com"
        - "www.163.com"

      # 国际网站测试列表（开发环境精简）
      international_test_sites:
        - "www.google.com"
        - "github.com"
        - "www.cloudflare.com"

    # === 连通性检查配置 (network_monitor.cpp 参数) ===
    connectivity:
      dns_server: "www.baidu.com"      # 开发环境使用国内DNS测试 (network_monitor.cpp)
      internet_server: "114.114.114.114" # 使用国内服务器测试 (network_monitor.cpp)

      # 开发环境超时配置 (network_monitor.cpp 参数)
      dns_timeout_ms: 5000             # (network_monitor.cpp: connectivity.dns_timeout_ms)
      ping_timeout_ms: 3000            # (network_monitor.cpp: connectivity.ping_timeout_ms)
      gateway_timeout_ms: 2000         # (network_monitor.cpp: connectivity.gateway_timeout_ms)
      external_timeout_ms: 5000        # (network_monitor.cpp: connectivity.external_timeout_ms)

    # === 异步处理配置 ===
    async_quality_check:
      enable: true
      max_concurrent_checks: 2         # 开发环境限制并发数
      check_timeout_ms: 15000          # 开发环境更长超时

    async_quality_callback:
      enable: true
      processing_timeout_ms: 8000      # 开发环境更长处理超时
    
    # 绑定设置
    binding:
      binding_timeout: 600           # 更长的绑定超时
      binding_method: 3
      ble_name: "Gen3_Dev_Robot"
