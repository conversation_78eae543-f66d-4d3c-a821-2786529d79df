// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "gen3_network_manager_core/cellular_manager.hpp"
#include "gen3_network_manager_core/nr90_serial_comm.hpp"
#include "gen3_network_manager_core/utils/network_utils.hpp"

#include <functional>
#include <memory>
#include <string>
#include <chrono>
#include <thread>

using namespace std::chrono_literals;
using std::placeholders::_1;
using std::placeholders::_2;

namespace gen3_network_manager_core
{

CellularManager::CellularManager(const rclcpp::Node::SharedPtr & node)
: node_(node), module_status_(CellularModuleStatus::UNKNOWN), serial_fd_(-1),
  is_serial_open_(false), monitor_running_(false), logger_(rclcpp::get_logger("cellular_manager"))
{
  RCLCPP_INFO(logger_, "创建5G模块管理器");
}

CellularManager::~CellularManager()
{
  // 停止监控线程
  monitor_running_.store(false);
  if (monitor_thread_.joinable()) {
    monitor_thread_.join();
  }
  
  RCLCPP_INFO(logger_, "销毁5G模块管理器");
}

bool CellularManager::init()
{
  RCLCPP_INFO(logger_, "初始化5G模块管理器");
  
  try {
    // 获取配置参数
    auto network_config = utils::NetworkUtils::get_network_config();
    device_path_ = node_->declare_parameter("fiveg_device_path", "/dev/ttyUSB0");
    baud_rate_ = node_->declare_parameter("fiveg_baud_rate", 115200);
    status_update_interval_ = node_->declare_parameter("fiveg_status_interval", 10.0);
    quality_update_interval_ = node_->declare_parameter("fiveg_quality_interval", 5.0);
    
    RCLCPP_INFO(logger_, "[CONFIG] 5G设备配置: 路径=%s, 波特率=%d", 
                device_path_.c_str(), baud_rate_);
    
    // 创建发布者
    status_pub_ = node_->create_publisher<gen3_network_interfaces::msg::NetworkStatus>(
      "fiveg_status", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建5G状态发布者: fiveg_status");

    quality_pub_ = node_->create_publisher<gen3_network_interfaces::msg::NetworkQuality>(
      "fiveg_quality", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建5G质量发布者: fiveg_quality");

    debug_pub_ = node_->create_publisher<std_msgs::msg::String>(
      "fiveg_debug", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建5G调试发布者: fiveg_debug");

    // 创建服务
    get_info_srv_ = node_->create_service<gen3_network_interfaces::srv::GetFivegInfo>(
      "get_fiveg_info", std::bind(&CellularManager::handle_get_info, this, _1, _2));
    RCLCPP_INFO(logger_, "[SRV] 创建5G信息服务: get_fiveg_info");

    // 创建定时器
    status_timer_ = node_->create_wall_timer(
      std::chrono::duration<double>(status_update_interval_),
      std::bind(&CellularManager::status_timer_callback, this));
    RCLCPP_INFO(logger_, "[TIMER] 创建状态更新定时器，间隔: %.1f秒", 
                status_update_interval_);

    quality_timer_ = node_->create_wall_timer(
      std::chrono::duration<double>(quality_update_interval_),
      std::bind(&CellularManager::quality_timer_callback, this));
    RCLCPP_INFO(logger_, "[TIMER] 创建质量更新定时器，间隔: %.1f秒", 
                quality_update_interval_);

    // 初始化5G模块状态
    current_info_.device_path = device_path_;
    current_info_.is_connected = false;
    module_status_.store(CellularModuleStatus::INITIALIZING);
    
    // 启动监控线程
    monitor_running_.store(true);
    monitor_thread_ = std::thread(&CellularManager::monitor_thread_function, this);
    
    RCLCPP_INFO(logger_, "5G模块管理器初始化完成");
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "5G模块管理器初始化失败: %s", e.what());
    return false;
  }
}

bool CellularManager::start_module()
{
  RCLCPP_INFO(logger_, "启动5G模块");
  
  module_status_.store(CellularModuleStatus::INITIALIZING);
  
  // 这里可以添加5G模块的启动逻辑
  // 例如：通过GPIO控制模块电源、复位等
  
  return true;
}

bool CellularManager::stop_module()
{
  RCLCPP_INFO(logger_, "停止5G模块");
  
  module_status_.store(CellularModuleStatus::DISCONNECTED);
  
  // 这里可以添加5G模块的停止逻辑
  
  return true;
}

bool CellularManager::restart_module()
{
  RCLCPP_INFO(logger_, "重启5G模块");
  
  stop_module();
  std::this_thread::sleep_for(2s);
  return start_module();
}

CellularNetworkInfo CellularManager::get_network_info()
{
  std::lock_guard<std::mutex> lock(status_mutex_);
  return current_info_;
}

CellularModuleStatus CellularManager::get_module_status()
{
  return module_status_.load();
}

int32_t CellularManager::get_signal_strength()
{
  std::lock_guard<std::mutex> lock(status_mutex_);
  return current_info_.signal_strength;
}

int32_t CellularManager::get_signal_quality()
{
  std::lock_guard<std::mutex> lock(status_mutex_);
  return current_info_.signal_quality;
}

bool CellularManager::is_module_ready()
{
  CellularModuleStatus status = module_status_.load();
  return (status == CellularModuleStatus::READY || 
          status == CellularModuleStatus::CONNECTED);
}

bool CellularManager::is_network_connected()
{
  std::lock_guard<std::mutex> lock(status_mutex_);
  return current_info_.is_connected;
}

bool CellularManager::test_connectivity()
{
  // 使用网络工具测试连通性
  return utils::NetworkUtils::test_connectivity("8.8.8.8", 3000);
}

void CellularManager::status_timer_callback()
{
  RCLCPP_DEBUG(logger_, "[TIMER] 5G状态更新定时器触发");
  
  update_module_status();
  publish_status();
}

void CellularManager::quality_timer_callback()
{
  RCLCPP_DEBUG(logger_, "[TIMER] 5G质量更新定时器触发");
  
  update_network_info();
  publish_quality();
}

void CellularManager::handle_get_info(
  const std::shared_ptr<gen3_network_interfaces::srv::GetFivegInfo::Request> request,
  std::shared_ptr<gen3_network_interfaces::srv::GetFivegInfo::Response> response)
{
  (void)request; // 避免未使用参数警告
  
  RCLCPP_INFO(logger_, "[SRV] 处理5G信息获取请求");
  
  std::lock_guard<std::mutex> lock(status_mutex_);
  
  response->success = true;
  response->message = "5G信息获取成功";
  response->operator_name = current_info_.operator_name;
  response->network_type = current_info_.network_type;
  response->signal_strength = current_info_.signal_strength;
  response->signal_quality = current_info_.signal_quality;
  response->is_connected = current_info_.is_connected;
  response->ip_address = current_info_.ip_address;
  response->cell_id = current_info_.cell_id;
  response->frequency = current_info_.frequency;
  response->imsi = current_info_.imsi;
  response->imei = current_info_.imei;
}

std::string CellularManager::module_status_to_string(CellularModuleStatus status)
{
  switch (status) {
    case CellularModuleStatus::UNKNOWN: return "UNKNOWN";
    case CellularModuleStatus::INITIALIZING: return "INITIALIZING";
    case CellularModuleStatus::READY: return "READY";
    case CellularModuleStatus::CONNECTING: return "CONNECTING";
    case CellularModuleStatus::CONNECTED: return "CONNECTED";
    case CellularModuleStatus::DISCONNECTED: return "DISCONNECTED";
    case CellularModuleStatus::ERROR: return "ERROR";
    default: return "UNKNOWN";
  }
}

void CellularManager::update_module_status()
{
  // 这里会在监控线程中实现具体的状态更新逻辑
  RCLCPP_DEBUG(logger_, "[UPDATE] 更新5G模块状态");
}

void CellularManager::update_network_info()
{
  // 这里会在监控线程中实现具体的网络信息更新逻辑
  RCLCPP_DEBUG(logger_, "[UPDATE] 更新5G网络信息");
}

void CellularManager::publish_status()
{
  auto msg = gen3_network_interfaces::msg::NetworkStatus();
  msg.header.stamp = node_->now();
  msg.network_type = gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G;

  {
    std::lock_guard<std::mutex> lock(status_mutex_);
    auto network_config = utils::NetworkUtils::get_network_config();
    msg.interface_name = network_config.g5_interface;
    msg.ip_address = current_info_.ip_address;

    // 获取网关信息
    auto interfaces = utils::NetworkUtils::get_network_interfaces();
    for (const auto & iface : interfaces) {
      if (iface.name == network_config.g5_interface) {
        msg.gateway = iface.gateway;
        break;
      }
    }
  }

  CellularModuleStatus status = module_status_.load();
  msg.connection_status = (status == CellularModuleStatus::CONNECTED) ?
    gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_CONNECTED :
    gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_DISCONNECTED;

  status_pub_->publish(msg);
}

void CellularManager::publish_quality()
{
  auto msg = gen3_network_interfaces::msg::NetworkQuality();
  msg.header.stamp = node_->now();
  msg.network_type = gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G;
  
  {
    std::lock_guard<std::mutex> lock(status_mutex_);
    auto network_config = utils::NetworkUtils::get_network_config();
    msg.interface_name = network_config.g5_interface;
    msg.signal_strength = current_info_.signal_strength;
    msg.signal_quality = current_info_.signal_quality;
  }
  
  msg.dns_working = test_connectivity();

  // 填充5G特定信息
  msg.operator_name = current_info_.operator_name;
  msg.network_mode = current_info_.network_type;

  quality_pub_->publish(msg);
}

void CellularManager::monitor_thread_function()
{
  RCLCPP_INFO(logger_, "[MONITOR] 5G监控线程启动");

  // 创建串口通信对象
  auto serial_comm = std::make_unique<Nr90SerialComm>(node_);

  // 初始化通信（自动选择串口或USB方式）
  Nr90Error result = serial_comm->init(device_path_, baud_rate_);
  if (result != Nr90Error::SUCCESS) {
    RCLCPP_ERROR(logger_, "[MONITOR] 通信初始化失败: %s",
                 Nr90SerialComm::error_to_string(result).c_str());
    module_status_.store(CellularModuleStatus::ERROR);
    return;
  }

  RCLCPP_INFO(logger_, "[MONITOR] ✓ 5G模块通信初始化成功");

  // 等待模块就绪
  RCLCPP_INFO(logger_, "[MONITOR] 等待5G模块就绪...");
  int retry_count = 0;
  const int max_retries = 10;

  while (monitor_running_.load() && retry_count < max_retries) {
    result = serial_comm->test_basic_communication();
    if (result == Nr90Error::SUCCESS) {
      RCLCPP_INFO(logger_, "[MONITOR] 5G模块通信正常");
      module_status_.store(CellularModuleStatus::READY);
      break;
    }

    retry_count++;
    RCLCPP_WARN(logger_, "[MONITOR] 5G模块通信测试失败，重试 %d/%d",
                retry_count, max_retries);
    std::this_thread::sleep_for(2s);
  }

  if (retry_count >= max_retries) {
    RCLCPP_ERROR(logger_, "[MONITOR] 5G模块通信失败，超过最大重试次数");
    module_status_.store(CellularModuleStatus::ERROR);
    return;
  }

  // 检查和设置USB拨号模式
  RCLCPP_INFO(logger_, "[MONITOR] 检查USB拨号模式配置");
  int current_mode = -1;
  std::string dial_status;

  result = serial_comm->get_usb_dial_status(current_mode, dial_status);
  if (result == Nr90Error::SUCCESS) {
    RCLCPP_INFO(logger_, "[MONITOR] 当前USB拨号模式: %d (%s)", current_mode, dial_status.c_str());

    if (current_mode != 0) {
      RCLCPP_WARN(logger_, "[MONITOR] 当前模式为 %d，需要切换到网卡模式(0)", current_mode);

      // 切换到网卡模式
      result = serial_comm->set_usb_dial_mode(0);
      if (result == Nr90Error::SUCCESS) {
        RCLCPP_INFO(logger_, "[MONITOR] 成功切换到网卡模式");

        // 验证切换结果
        std::this_thread::sleep_for(2s);  // 等待设置生效
        result = serial_comm->get_usb_dial_status(current_mode, dial_status);
        if (result == Nr90Error::SUCCESS && current_mode == 0) {
          RCLCPP_INFO(logger_, "[MONITOR] ✓ 网卡模式设置验证成功: %d (%s)",
                     current_mode, dial_status.c_str());
        } else {
          RCLCPP_WARN(logger_, "[MONITOR] ⚠ 网卡模式设置验证失败，当前模式: %d", current_mode);
        }
      } else {
        RCLCPP_ERROR(logger_, "[MONITOR] 切换到网卡模式失败: %s",
                    Nr90SerialComm::error_to_string(result).c_str());
      }
    } else {
      RCLCPP_INFO(logger_, "[MONITOR] ✓ 已经是网卡模式，无需切换");
    }
  } else {
    RCLCPP_WARN(logger_, "[MONITOR] 查询USB拨号模式失败: %s，继续运行",
               Nr90SerialComm::error_to_string(result).c_str());
  }

  // 主监控循环
  RCLCPP_INFO(logger_, "[MONITOR] 开始5G模块监控循环");

  while (monitor_running_.load()) {
    try {
      // 更新服务小区信息（包含信号强度）
      Nr90ServingCellInfo cell_info;
      result = serial_comm->get_serving_cell_info(cell_info);

      if (result == Nr90Error::SUCCESS) {
        std::lock_guard<std::mutex> lock(status_mutex_);

        // 根据网络制式选择合适的信号强度指标
        int32_t primary_signal = -999;  // 初始化为无效值

        if (cell_info.act_type == "LTE" || cell_info.act_type == "NR") {
          // LTE/5G优先使用RSRP，如果不可用则使用RSSI
          if (cell_info.rsrp != -999 && cell_info.rsrp >= -150 && cell_info.rsrp <= -30) {
            primary_signal = cell_info.rsrp;
            RCLCPP_DEBUG(logger_, "[MONITOR] 使用RSRP作为主信号强度: %ddBm", primary_signal);
          } else if (cell_info.rssi != -999 && cell_info.rssi >= -150 && cell_info.rssi <= -30) {
            primary_signal = cell_info.rssi;
            RCLCPP_DEBUG(logger_, "[MONITOR] 使用RSSI作为主信号强度: %ddBm", primary_signal);
          }
        } else if (cell_info.act_type == "WCDMA") {
          // WCDMA优先使用RSCP，如果不可用则使用RSSI
          if (cell_info.rscp != -999 && cell_info.rscp >= -150 && cell_info.rscp <= -30) {
            primary_signal = cell_info.rscp;
            RCLCPP_DEBUG(logger_, "[MONITOR] 使用RSCP作为主信号强度: %ddBm", primary_signal);
          } else if (cell_info.rssi != -999 && cell_info.rssi >= -150 && cell_info.rssi <= -30) {
            primary_signal = cell_info.rssi;
            RCLCPP_DEBUG(logger_, "[MONITOR] 使用RSSI作为主信号强度: %ddBm", primary_signal);
          }
        } else {
          // 其他制式使用RSSI
          if (cell_info.rssi != -999 && cell_info.rssi >= -150 && cell_info.rssi <= -30) {
            primary_signal = cell_info.rssi;
            RCLCPP_DEBUG(logger_, "[MONITOR] 使用RSSI作为主信号强度: %ddBm", primary_signal);
          }
        }

        // 检查信号强度有效性并设置合理的默认值
        if (primary_signal == -999 || primary_signal < -150 || primary_signal > -30) {
          RCLCPP_WARN(logger_, "[MONITOR] 信号强度无效或超出范围: %ddBm，设置为默认值-100dBm", primary_signal);
          primary_signal = -100;  // 设置为合理的默认值
        }

        // 更新网络信息
        current_info_.signal_strength = primary_signal;
        current_info_.network_type = cell_info.act_type;
        current_info_.registration_status = cell_info.state;

        // 构建运营商名称 (MCC-MNC格式)
        if (cell_info.mcc != -1 && cell_info.mnc != -1) {
          current_info_.operator_name = std::to_string(cell_info.mcc) + "-" +
                                       std::to_string(cell_info.mnc);
        }

        // 构建小区ID信息
        if (cell_info.pcid != -1) {
          current_info_.cell_id = std::to_string(cell_info.pcid);
        } else if (cell_info.lac != -1) {
          current_info_.cell_id = "LAC:" + std::to_string(cell_info.lac);
        }

        // 设置频率信息 (从ARFCN转换，这里简化处理)
        if (cell_info.arfcn != -1) {
          current_info_.frequency = static_cast<uint32_t>(cell_info.arfcn);
        }

        // 计算信号质量百分比 (假设-113dBm为0%, -51dBm为100%)
        // 确保信号强度在合理范围内再计算质量
        if (primary_signal >= -113 && primary_signal <= -51) {
          current_info_.signal_quality = static_cast<int32_t>((primary_signal + 113) * 100 / 62);
        } else if (primary_signal > -51) {
          current_info_.signal_quality = 100;  // 信号很强，设为100%
        } else {
          current_info_.signal_quality = 0;    // 信号很弱，设为0%
        }

        // 确保质量值在0-100范围内
        current_info_.signal_quality = std::max(0, std::min(100, current_info_.signal_quality));

        RCLCPP_DEBUG(logger_, "[MONITOR] 原始信号数据 - RSSI: %ddBm, RSRP: %ddBm, RSCP: %ddBm",
                     cell_info.rssi, cell_info.rsrp, cell_info.rscp);

        RCLCPP_DEBUG(logger_, "[MONITOR] 服务小区信息 - 制式: %s, 状态: %s, 运营商: %s, 小区: %s, 信号强度: %ddBm, 质量: %d%%",
                     cell_info.act_type.c_str(), cell_info.state.c_str(),
                     current_info_.operator_name.c_str(), current_info_.cell_id.c_str(),
                     primary_signal, current_info_.signal_quality);
      } else {
        RCLCPP_DEBUG(logger_, "[MONITOR] 获取服务小区信息失败: %s",
                     Nr90SerialComm::error_to_string(result).c_str());

        // 串口通信失败时，检查并修复异常的信号强度值
        std::lock_guard<std::mutex> lock(status_mutex_);
        if (current_info_.signal_strength < -150 || current_info_.signal_strength > -30) {
          RCLCPP_WARN(logger_, "[MONITOR] 通信失败且当前信号强度异常: %ddBm，重置为默认值",
                      current_info_.signal_strength);
          current_info_.signal_strength = -100;  // 设置合理的默认值
          current_info_.signal_quality = 20;     // 对应的质量值
        }
      }

      // 检查网络连接状态
      auto network_config = utils::NetworkUtils::get_network_config();
      auto interfaces = utils::NetworkUtils::get_network_interfaces();

      bool is_connected = false;
      std::string ip_address;

      for (const auto & iface : interfaces) {
        if (iface.name == network_config.g5_interface && !iface.ip_address.empty()) {
          is_connected = true;
          ip_address = iface.ip_address;
          break;
        }
      }

      {
        std::lock_guard<std::mutex> lock(status_mutex_);
        current_info_.is_connected = is_connected;
        current_info_.ip_address = ip_address;
      }

      // 更新模块状态
      if (is_connected) {
        module_status_.store(CellularModuleStatus::CONNECTED);
      } else if (module_status_.load() == CellularModuleStatus::CONNECTED) {
        module_status_.store(CellularModuleStatus::READY);
      }

      // 发布调试信息
      auto debug_msg = std_msgs::msg::String();
      debug_msg.data = "5G Status: " + module_status_to_string(module_status_.load()) +
                      ", Signal: " + std::to_string(current_info_.signal_strength) + "dBm" +
                      ", Connected: " + (is_connected ? "Yes" : "No");
      debug_pub_->publish(debug_msg);

    } catch (const std::exception & e) {
      RCLCPP_ERROR(logger_, "[MONITOR] 监控异常: %s", e.what());
      module_status_.store(CellularModuleStatus::ERROR);
    }

    // 监控间隔
    std::this_thread::sleep_for(5s);
  }

  RCLCPP_INFO(logger_, "[MONITOR] 5G监控线程结束");
}

}  // namespace gen3_network_manager_core
