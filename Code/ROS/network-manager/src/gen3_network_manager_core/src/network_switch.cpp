#include "gen3_network_manager_core/network_switch.hpp"
#include "gen3_network_manager_core/utils/network_utils.hpp"
#include "gen3_network_manager_core/utils/enum_utils.hpp"

#include <functional>
#include <memory>
#include <string>
#include <chrono>
#include <vector>
#include <algorithm>
#include <iostream>

using namespace std::chrono_literals;
using std::placeholders::_1;

// 定义NETWORK_TYPE_AUTO常量，这种方式不会与现有类型冲突
const uint8_t NETWORK_TYPE_AUTO = 99;

namespace gen3_network_manager_core
{

NetworkSwitch::NetworkSwitch(const rclcpp::Node::SharedPtr & node)
: node_(node), is_switching_(false), logger_(rclcpp::get_logger("network_switch"))
{
  RCLCPP_INFO(logger_, "创建网络切换器");
  
  // 初始化切换参数
  current_switch_params_.in_progress = false;
  
  // 声明参数
  node_->declare_parameter("quality_check_interval", 5.0);
  node_->declare_parameter("switch_monitor_interval", 1.0);
  node_->declare_parameter("network_switch.min_connectivity_score", 50.0);
  node_->declare_parameter("network_switch.quality_weights.latency", 0.3);
  node_->declare_parameter("network_switch.quality_weights.packet_loss", 0.25);
  node_->declare_parameter("network_switch.quality_weights.signal_strength", 0.2);
  node_->declare_parameter("network_switch.quality_weights.connectivity", 0.25);

  // 声明延迟期望值参数
  node_->declare_parameter("network_switch.expected_latency.wifi", 20.0);
  node_->declare_parameter("network_switch.expected_latency.fiveg", 15.0);
  node_->declare_parameter("network_switch.expected_latency.ethernet", 5.0);
  node_->declare_parameter("network_switch.expected_latency.default", 50.0);

  // 注意：china_network.domestic_test_sites 参数已在 DNSManager 中声明，这里不需要重复声明

  // 暂时去掉带宽权重参数
  // node_->declare_parameter("network_switch.quality_weights.download_speed", 0.1);
  // node_->declare_parameter("network_switch.quality_weights.upload_speed", 0.1);
}

NetworkSwitch::~NetworkSwitch()
{
  RCLCPP_INFO(logger_, "销毁网络切换器");
}

bool NetworkSwitch::init()
{
  RCLCPP_INFO(logger_, "初始化网络切换器");
  
  try {
    // 创建发布者
    switch_status_pub_ = node_->create_publisher<std_msgs::msg::String>(
      "switch_status", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建切换状态发布者: switch_status");

    network_quality_pub_ = node_->create_publisher<gen3_network_interfaces::msg::NetworkQuality>(
      "network_quality", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建网络质量发布者: network_quality");

    // 创建订阅者
    network_status_sub_ = node_->create_subscription<gen3_network_interfaces::msg::NetworkStatus>(
      "network_status", 10, std::bind(&NetworkSwitch::network_status_callback, this, _1));
    RCLCPP_INFO(logger_, "[SUB] 创建网络状态订阅者: network_status");

    // 创建定时器
    double quality_check_interval = node_->get_parameter("quality_check_interval").as_double();

    quality_check_timer_ = node_->create_wall_timer(
      std::chrono::duration<double>(quality_check_interval),
      std::bind(&NetworkSwitch::quality_check_timer_callback, this));
    RCLCPP_INFO(logger_, "[TIMER] 创建质量检查定时器，间隔: %.1f秒", quality_check_interval);

    // 注意：不在初始化阶段立即执行质量检查定时器回调
    // 质量检查逻辑将在一次性初始化定时器中执行
    RCLCPP_INFO(logger_, "[TIMER] 首次质量检查将在一次性初始化定时器中执行");

    double switch_monitor_interval = node_->get_parameter("switch_monitor_interval").as_double();

    switch_monitor_timer_ = node_->create_wall_timer(
      std::chrono::duration<double>(switch_monitor_interval),
      std::bind(&NetworkSwitch::switch_monitor_timer_callback, this));
    RCLCPP_INFO(logger_, "[TIMER] 创建切换监控定时器，间隔: %.1f秒", switch_monitor_interval);

    // 创建一次性初始化定时器，1秒后执行初始化逻辑
    init_timer_ = node_->create_wall_timer(
      std::chrono::seconds(1),
      std::bind(&NetworkSwitch::init_timer_callback, this));
    RCLCPP_INFO(logger_, "[TIMER] 创建一次性初始化定时器，1秒后执行初始化逻辑");

    // 注意：不在初始化阶段立即执行定时器回调
    // 所有需要立即执行的逻辑都移到一次性初始化定时器中
    RCLCPP_INFO(logger_, "[TIMER] 首次切换监控将在一次性初始化定时器中执行");
    
    RCLCPP_INFO(logger_, "网络切换器初始化完成");
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "网络切换器初始化失败: %s", e.what());
    return false;
  }
}

void NetworkSwitch::network_status_callback(
  const gen3_network_interfaces::msg::NetworkStatus::SharedPtr msg)
{
  RCLCPP_INFO(logger_,
    "[SUB] 收到网络状态更新 - 类型: %d, 接口: %s, IP: %s, 连接状态: %d",
    msg->network_type, msg->interface_name.c_str(), msg->ip_address.c_str(), msg->connection_status);

  // 根据网络类型输出详细信息
  if (msg->network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
    RCLCPP_INFO(logger_,
      "[SUB] WiFi详情 - SSID: %s, 信号强度: %ddBm, 安全类型: %s",
      msg->wifi_ssid.c_str(), msg->wifi_signal_strength,
      msg->wifi_security_type.c_str());
  } else if (msg->network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G) {
    RCLCPP_INFO(logger_,
      "[SUB] 5G详情 - 运营商: %s, 信号强度: %ddBm, 网络制式: %s",
      msg->fiveg_operator_name.c_str(), msg->fiveg_signal_strength,
      msg->fiveg_network_type.c_str());
  } else if (msg->network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET) {
    RCLCPP_INFO(logger_,
      "[SUB] 以太网详情 - 接口: %s",
      msg->interface_name.c_str());
  }

  // 输出网络配置信息
  RCLCPP_DEBUG(logger_,
    "[SUB] 网络配置 - 网关: %s, DNS: %s",
    msg->gateway.c_str(), msg->dns_servers.empty() ? "无" : msg->dns_servers[0].c_str());

  std::lock_guard<std::mutex> lock(switch_mutex_);
  current_status_ = *msg;

  RCLCPP_DEBUG(logger_, "[SUB] 网络状态已更新到本地缓存");
}

void NetworkSwitch::init_timer_callback()
{
  RCLCPP_INFO(logger_, "[INIT_TIMER] 一次性初始化定时器触发");

  // 取消定时器，确保只执行一次
  if (init_timer_) {
    init_timer_->cancel();
    init_timer_.reset();
    RCLCPP_INFO(logger_, "[INIT_TIMER] 一次性初始化定时器已取消");
  }

  // 执行原本在初始化后立即执行的逻辑

  RCLCPP_INFO(logger_, "[INIT_TIMER] 执行首次质量检查定时器回调");

  // 调用质量检查定时器回调，执行完整的质量检查逻辑
  quality_check_timer_callback();

  RCLCPP_INFO(logger_, "[INIT_TIMER] 执行首次切换监控（原本在初始化后立即执行）");

  // 调用切换监控定时器回调，执行完整的切换监控逻辑
  switch_monitor_timer_callback();

  RCLCPP_INFO(logger_, "[INIT_TIMER] 一次性初始化逻辑执行完成");
}

void NetworkSwitch::quality_check_timer_callback()
{
  RCLCPP_DEBUG(logger_, "[TIMER] 质量检查定时器触发（异步模式）");

  // 首先检查上一次异步质量检查的结果
  check_async_quality_result();

  // 如果正在切换网络，跳过新的质量检查
  if (is_switching_.load()) {
    RCLCPP_DEBUG(logger_, "[TIMER] 正在切换网络，跳过质量检查");
    return;
  }

  std::string interface_name;
  uint8_t network_type;

  {
    std::lock_guard<std::mutex> lock(switch_mutex_);
    interface_name = current_status_.interface_name;
    network_type = current_status_.network_type;
  }

  if (!interface_name.empty()) {
    RCLCPP_DEBUG(logger_, "[TIMER] 启动异步网络质量检查 - 接口: %s, 类型: %d",
      interface_name.c_str(), network_type);
    check_network_quality(network_type, interface_name);
  } else {
    RCLCPP_DEBUG(logger_, "[TIMER] 接口名为空，跳过质量检查");
  }

  RCLCPP_DEBUG(logger_, "[TIMER] 质量检查定时器处理完成（异步模式）");
}

void NetworkSwitch::switch_monitor_timer_callback()
{
  RCLCPP_DEBUG(logger_, "[TIMER] 切换监控定时器触发");

  std::lock_guard<std::mutex> lock(switch_mutex_);

  // 如果没有切换进行中，直接返回
  if (!current_switch_params_.in_progress) {
    RCLCPP_DEBUG(logger_, "[TIMER] 没有正在进行的切换");
    return;
  }
  
  // 检查是否超时
  auto now = node_->now();
  auto elapsed = now - current_switch_params_.start_time;
  auto timeout = std::chrono::seconds(current_switch_params_.timeout_seconds);

  RCLCPP_DEBUG(logger_, "[TIMER] 切换进行中，已耗时: %.1f秒，超时阈值: %u秒",
    elapsed.seconds(), current_switch_params_.timeout_seconds);

  if (elapsed > timeout) {
    RCLCPP_ERROR(logger_, "[TIMER] 网络切换超时");

    // 发布状态
    std_msgs::msg::String msg;
    msg.data = "SWITCH_TIMEOUT";
    switch_status_pub_->publish(msg);

    RCLCPP_INFO(logger_, "[PUB] 发布切换超时状态: SWITCH_TIMEOUT");

    // 重置切换状态
    current_switch_params_.in_progress = false;
    is_switching_.store(false);
    return;
  }
  
  // 检查当前网络状态是否符合目标
  if (current_status_.network_type == current_switch_params_.target_network_type) {
    RCLCPP_DEBUG(logger_, "[TIMER] 网络类型匹配目标类型: %d", current_status_.network_type);

    // 如果是WiFi，还需要检查SSID
    if (current_status_.network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI &&
        !current_switch_params_.target_wifi_ssid.empty()) {
      if (current_status_.wifi_ssid == current_switch_params_.target_wifi_ssid) {
        RCLCPP_INFO(logger_, "[TIMER] WiFi网络切换成功完成 - SSID: %s",
          current_status_.wifi_ssid.c_str());

        // 发布状态
        std_msgs::msg::String msg;
        msg.data = "SWITCH_COMPLETED";
        switch_status_pub_->publish(msg);

        RCLCPP_INFO(logger_, "[PUB] 发布切换完成状态: SWITCH_COMPLETED");

        // 重置切换状态
        current_switch_params_.in_progress = false;
        is_switching_.store(false);
      } else {
        RCLCPP_DEBUG(logger_, "[TIMER] WiFi SSID不匹配，当前: %s, 目标: %s",
          current_status_.wifi_ssid.c_str(), current_switch_params_.target_wifi_ssid.c_str());
      }
    } else {
      RCLCPP_INFO(logger_, "[TIMER] 网络切换成功完成 - 类型: %d", current_status_.network_type);

      // 发布状态
      std_msgs::msg::String msg;
      msg.data = "SWITCH_COMPLETED";
      switch_status_pub_->publish(msg);

      RCLCPP_INFO(logger_, "[PUB] 发布切换完成状态: SWITCH_COMPLETED");

      // 重置切换状态
      current_switch_params_.in_progress = false;
      is_switching_.store(false);
    }
  } else {
    RCLCPP_DEBUG(logger_, "[TIMER] 网络类型不匹配，当前: %d, 目标: %d",
      current_status_.network_type, current_switch_params_.target_network_type);
  }

  RCLCPP_DEBUG(logger_, "[TIMER] 切换监控定时器处理完成");
}

bool NetworkSwitch::check_network_quality(uint8_t network_type, const std::string & interface_name)
{
  RCLCPP_DEBUG(
    logger_, "[QUALITY] 启动异步网络质量检查 - 类型: %d, 接口: %s",
    network_type, interface_name.c_str());

  // 检查上一次异步质量检查的结果
  check_async_quality_result();

  // 启动新的异步质量检查
  start_async_quality_check(network_type, interface_name);

  return true;  // 异步模式下立即返回，实际结果通过回调处理
}

// 原来的同步检查逻辑移到异步函数中
gen3_network_interfaces::msg::NetworkQuality NetworkSwitch::perform_quality_check_async(
  uint8_t network_type, const std::string & interface_name)
{
  RCLCPP_DEBUG(
    logger_, "[ASYNC_QUALITY] 执行异步网络质量检查 - 类型: %d, 接口: %s",
    network_type, interface_name.c_str());

  gen3_network_interfaces::msg::NetworkQuality quality;

  try {
    // 基本信息
    quality.interface_name = interface_name;
    quality.network_type = network_type;
    quality.test_start_time = node_->now();

    // 获取信号强度（从订阅的网络状态中获取）
    if (network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
      // 从当前网络状态中获取WiFi信号强度
      quality.signal_strength = current_status_.wifi_signal_strength;
      // signal_quality 字段保留但不进行复杂转换，设为固定值或简单映射
      quality.signal_quality = 50.0;  // 默认值，实际评分使用 signal_strength
      RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] WiFi信号强度: %ddBm", quality.signal_strength);

      // WiFi信号强度为0时直接返回失败结果
      if (quality.signal_strength == 0) {
        RCLCPP_WARN(logger_, "[ASYNC_QUALITY] WiFi信号强度为0，直接返回失败结果");
        gen3_network_interfaces::msg::NetworkQuality failed_quality;
        failed_quality.interface_name = interface_name;
        failed_quality.network_type = network_type;
        failed_quality.test_start_time = node_->now();
        failed_quality.test_end_time = node_->now();
        failed_quality.overall_score = 0.0;
        failed_quality.is_suitable_for_switching = false;
        failed_quality.dns_working = false;
        failed_quality.signal_strength = 0;
        failed_quality.signal_quality = 0.0;
        return failed_quality;
      }
    } else if (network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G) {
      // 从当前网络状态中获取5G信号强度
      quality.signal_strength = current_status_.fiveg_signal_strength;
      // signal_quality 字段保留但不进行复杂转换，设为固定值或简单映射
      quality.signal_quality = 50.0;  // 默认值，实际评分使用 signal_strength
      RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] 5G信号强度: %ddBm", quality.signal_strength);

      // 5G信号强度为0时直接返回失败结果
      if (quality.signal_strength == 0) {
        RCLCPP_WARN(logger_, "[ASYNC_QUALITY] 5G信号强度为0，直接返回失败结果");
        gen3_network_interfaces::msg::NetworkQuality failed_quality;
        failed_quality.interface_name = interface_name;
        failed_quality.network_type = network_type;
        failed_quality.test_start_time = node_->now();
        failed_quality.test_end_time = node_->now();
        failed_quality.overall_score = 0.0;
        failed_quality.is_suitable_for_switching = false;
        failed_quality.dns_working = false;
        failed_quality.signal_strength = 0;
        failed_quality.signal_quality = 0.0;
        return failed_quality;
      }
    } else {
      quality.signal_strength = 0;
      quality.signal_quality = 0.0;  // 其他网络类型
      RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] 其他网络类型，信号强度设为0");
    }

    // 测试DNS解析（获取真实解析时间）
    std::vector<std::string> dns_test_domains = {
      "www.baidu.com", "www.qq.com", "www.163.com"
    };
    auto dns_result = utils::NetworkUtils::test_dns_resolution_with_time(dns_test_domains, 2000);
    quality.dns_working = std::get<0>(dns_result);
    double dns_resolution_time = std::get<1>(dns_result);

    RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] DNS解析测试 - 成功: %s, 平均解析时间: %.2fms",
                 quality.dns_working ? "是" : "否", dns_resolution_time);

    // 设置消息头
    quality.header.stamp = node_->now();

    // 测试网络质量（延迟和丢包率）- 使用配置中的国内网站地址
    std::vector<std::string> quality_test_hosts =
      node_->get_parameter("china_network.domestic_test_sites").as_string_array();

    RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] 使用国内网站测试网络质量: %zu个网站", quality_test_hosts.size());
    auto quality_result = utils::NetworkUtils::test_network_quality(quality_test_hosts, 5);
    bool quality_success = std::get<0>(quality_result);
    double latency_ms = std::get<1>(quality_result);
    double packet_loss_rate = std::get<2>(quality_result);

    if (quality_success) {
      quality.latency_ms = latency_ms;
      quality.jitter_ms = latency_ms * 0.1;  // 简化处理，假设抖动是延迟的10%
      quality.packet_loss_rate = packet_loss_rate;
      RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] 网络质量测试成功 - 延迟: %.2fms, 丢包率: %.2f%%",
                   latency_ms, packet_loss_rate);
    } else {
      quality.latency_ms = 1000.0;  // 默认高延迟
      quality.jitter_ms = 100.0;
      quality.packet_loss_rate = 100.0;  // 默认高丢包率
      RCLCPP_WARN(logger_, "[ASYNC_QUALITY] 网络质量测试失败，使用默认值");
    }
    
    // 测试网络速度
    auto speed_result = utils::NetworkUtils::test_network_speed();
    bool speed_success = std::get<0>(speed_result);
    double download_speed = std::get<1>(speed_result);
    double upload_speed = std::get<2>(speed_result);
    if (speed_success) {
      quality.download_speed_mbps = download_speed;
      quality.upload_speed_mbps = upload_speed;
    } else {
      quality.download_speed_mbps = 0.1;  // 默认低速度
      quality.upload_speed_mbps = 0.05;
    }
    
    // 丢包率已在上面的网络质量测试中获取，这里不需要重复计算
    
    // 获取DNS信息
    auto dns_servers = utils::NetworkUtils::get_dns_servers();
    for (const auto & dns : dns_servers) {
      bool dns_working = utils::NetworkUtils::test_connectivity(dns, 1000);
      if (dns_working) {
        quality.working_dns_servers.push_back(dns);
      } else {
        quality.failed_dns_servers.push_back(dns);
      }
    }
    
    // DNS解析时间（使用真实测量值）
    quality.dns_resolution_time_ms = quality.dns_working ? dns_resolution_time : 1000.0;
    
    // 计算综合评分
    quality.overall_score = calculate_network_score(quality);
    
    // 判断是否适合作为切换目标
    double min_score = node_->get_parameter("network_switch.min_connectivity_score").as_double();
    quality.is_suitable_for_switching = quality.overall_score >= min_score;
    
    // 记录测试时间
    quality.test_end_time = node_->now();

    RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] 异步网络质量检查完成 - 接口: %s, 评分: %.1f",
                 interface_name.c_str(), quality.overall_score);

    return quality;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[ASYNC_QUALITY] 检查网络质量异常: %s", e.what());

    // 返回默认的失败质量结果
    gen3_network_interfaces::msg::NetworkQuality failed_quality;
    failed_quality.interface_name = interface_name;
    failed_quality.network_type = network_type;
    failed_quality.test_start_time = node_->now();
    failed_quality.test_end_time = node_->now();
    failed_quality.overall_score = 0.0;
    failed_quality.is_suitable_for_switching = false;
    failed_quality.dns_working = false;

    return failed_quality;
  }
}

bool NetworkSwitch::switch_to_network(
  uint8_t network_type, const std::string & wifi_ssid, bool force_switch)
{
  RCLCPP_INFO(
    logger_, "切换网络: 类型=%d, WiFi=%s, 强制=%s",
    network_type, wifi_ssid.c_str(), force_switch ? "是" : "否");
  
  // 如果已经在切换中，返回失败
  if (is_switching_.load()) {
    RCLCPP_WARN(logger_, "已有网络切换在进行中，请等待完成");
    return false;
  }
  
  // 检查当前网络类型
  {
    std::lock_guard<std::mutex> lock(switch_mutex_);
    
    // 如果已经是目标网络类型，并且是WiFi的话SSID也匹配，则无需切换
    if (current_status_.network_type == network_type) {
      if (network_type != gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI || 
          wifi_ssid.empty() || 
          current_status_.wifi_ssid == wifi_ssid) {
        RCLCPP_INFO(logger_, "已经是目标网络类型，无需切换");
        return true;
      }
    }
    
    // 如果不是强制切换，检查目标网络质量
    if (!force_switch && network_type != NETWORK_TYPE_AUTO) {
      // 获取网络配置
      auto network_config = utils::NetworkUtils::get_network_config();

      // 获取对应接口名称
      std::string target_interface;

      switch (network_type) {
        case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI:
          target_interface = network_config.wifi_interface;
          break;
        case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G:
          target_interface = network_config.g5_interface;
          break;
        case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET:
          target_interface = network_config.ethernet_interface;
          break;
        default:
          RCLCPP_WARN(logger_, "未知的网络类型: %d", network_type);
          return false;
      }
      
      // 检查网络质量
      auto it = network_quality_map_.find(target_interface);
      if (it != network_quality_map_.end()) {
        if (!it->second.is_suitable_for_switching) {
          RCLCPP_WARN(
            logger_, "目标网络质量不佳，不适合切换. 评分: %.1f",
            it->second.overall_score);
          return false;
        }
      }
    }
  }
  
  // 设置切换状态
  is_switching_.store(true);
  
  // 发布状态
  std_msgs::msg::String msg;
  msg.data = "SWITCH_STARTED";
  switch_status_pub_->publish(msg);
  
  // 执行网络切换
  bool result = execute_network_switch(network_type, wifi_ssid);
  
  if (result) {
    // 更新切换参数
    std::lock_guard<std::mutex> lock(switch_mutex_);
    current_switch_params_.target_network_type = network_type;
    current_switch_params_.target_wifi_ssid = wifi_ssid;
    current_switch_params_.force_switch = force_switch;
    current_switch_params_.in_progress = true;
    current_switch_params_.start_time = node_->now();
    current_switch_params_.timeout_seconds = 30;  // 默认30秒超时
    
    RCLCPP_INFO(logger_, "网络切换开始");
    return true;
  } else {
    // 切换失败，重置状态
    is_switching_.store(false);
    
    // 发布状态
    std_msgs::msg::String status_msg;
    status_msg.data = "SWITCH_FAILED";
    switch_status_pub_->publish(status_msg);
    
    RCLCPP_ERROR(logger_, "网络切换失败");
    return false;
  }
}

bool NetworkSwitch::get_best_network(uint8_t & network_type, std::string & wifi_ssid)
{
  std::lock_guard<std::mutex> lock(switch_mutex_);
  
  // 如果网络质量信息为空，无法确定最佳网络
  if (network_quality_map_.empty()) {
    RCLCPP_WARN(logger_, "没有网络质量信息，无法确定最佳网络");
    return false;
  }
  
  // 查找评分最高的网络
  auto best_it = std::max_element(
    network_quality_map_.begin(), network_quality_map_.end(),
    [](const auto & a, const auto & b) {
      return a.second.overall_score < b.second.overall_score;
    });
  
  if (best_it != network_quality_map_.end()) {
    network_type = best_it->second.network_type;
    
    // 如果是WiFi，获取SSID
    if (network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
      // 简化处理，实际应该从WiFi管理器获取当前SSID
      wifi_ssid = current_status_.wifi_ssid;
    } else {
      wifi_ssid = "";
    }
    
    RCLCPP_INFO(
      logger_, "最佳网络: 类型=%d, 接口=%s, 评分=%.1f",
      network_type, best_it->first.c_str(), best_it->second.overall_score);
    
    return true;
  }
  
  return false;
}

bool NetworkSwitch::is_switching() const
{
  return is_switching_.load();
}

bool NetworkSwitch::execute_network_switch(uint8_t network_type, const std::string & wifi_ssid)
{
  try {
    // 获取网络配置
    auto network_config = utils::NetworkUtils::get_network_config();

    // 使用局部变量存储SSID
    std::string local_wifi_ssid = wifi_ssid;

    // 如果是自动选择最优网络
    if (network_type == NETWORK_TYPE_AUTO) {
      uint8_t best_type;
      std::string best_ssid;

      if (get_best_network(best_type, best_ssid)) {
        network_type = best_type;
        if (!best_ssid.empty()) {
          local_wifi_ssid = best_ssid;
        }
      } else {
        RCLCPP_WARN(logger_, "无法确定最佳网络，使用默认网络类型");
        network_type = gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI;
      }
    }

    // 根据网络类型执行切换
    switch (network_type) {
      case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI: {
        RCLCPP_INFO(logger_, "执行WiFi网络切换，目标SSID: %s", local_wifi_ssid.c_str());
        
        // 启用WiFi接口
        utils::NetworkUtils::set_interface_state(network_config.wifi_interface, true);
        
        // 如果指定了SSID，连接到指定WiFi
        if (!local_wifi_ssid.empty()) {
          RCLCPP_INFO(logger_, "开始连接WiFi: %s", local_wifi_ssid.c_str());
          bool connect_result = utils::NetworkUtils::connect_to_wifi(local_wifi_ssid, "", "");
          if (!connect_result) {
            RCLCPP_ERROR(logger_, "连接WiFi失败: %s", local_wifi_ssid.c_str());
            return false;
          }
          RCLCPP_INFO(logger_, "WiFi连接成功: %s", local_wifi_ssid.c_str());
        }

        // 使用智能路由管理
        RCLCPP_INFO(logger_, "开始智能路由管理 - WiFi网络");
        return utils::NetworkUtils::manage_route_intelligently(
          network_config.wifi_interface, 
          gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI);
      }

      case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G: {
        RCLCPP_INFO(logger_, "执行5G网络切换");
        
        // 启用5G接口
        utils::NetworkUtils::set_interface_state(network_config.g5_interface, true);

        // 使用智能路由管理
        RCLCPP_INFO(logger_, "开始智能路由管理 - 5G网络");
        return utils::NetworkUtils::manage_route_intelligently(
          network_config.g5_interface, 
          gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G);
      }

      case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET: {
        RCLCPP_INFO(logger_, "执行以太网网络切换");
        
        // 启用以太网接口
        utils::NetworkUtils::set_interface_state(network_config.ethernet_interface, true);

        // 使用智能路由管理
        RCLCPP_INFO(logger_, "开始智能路由管理 - 以太网");
        return utils::NetworkUtils::manage_route_intelligently(
          network_config.ethernet_interface, 
          gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET);
      }
      
      default:
        RCLCPP_ERROR(logger_, "不支持的网络类型: %s",
                     utils::EnumUtils::network_type_to_string(network_type).c_str());
        return false;
    }
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "执行网络切换异常: %s", e.what());
    return false;
  }
}

void NetworkSwitch::publish_switch_status(const std::string & status)
{
  std_msgs::msg::String msg;
  msg.data = status;
  switch_status_pub_->publish(msg);
}

void NetworkSwitch::publish_network_quality(const gen3_network_interfaces::msg::NetworkQuality & quality)
{
  network_quality_pub_->publish(quality);
}

float NetworkSwitch::calculate_network_score(const gen3_network_interfaces::msg::NetworkQuality & quality)
{
  RCLCPP_DEBUG(logger_, "[SCORE] 开始计算网络评分 - 接口: %s, 类型: %d",
               quality.interface_name.c_str(), quality.network_type);

  // 获取权重参数（考虑缓存优化）
  double latency_weight = node_->get_parameter("network_switch.quality_weights.latency").as_double();
  double packet_loss_weight = node_->get_parameter("network_switch.quality_weights.packet_loss").as_double();
  double signal_strength_weight = node_->get_parameter("network_switch.quality_weights.signal_strength").as_double();
  double connectivity_weight = node_->get_parameter("network_switch.quality_weights.connectivity").as_double();

  // 暂时去掉带宽权重
  // double download_speed_weight = node_->get_parameter("network_switch.quality_weights.download_speed").as_double();
  // double upload_speed_weight = node_->get_parameter("network_switch.quality_weights.upload_speed").as_double();

  // === 1. 延迟评分（使用对数函数，更符合人类感知） ===
  double latency_score = calculate_latency_score(quality.latency_ms, quality.network_type);

  // === 2. 丢包率评分（指数衰减） ===
  double packet_loss_score = calculate_packet_loss_score(quality.packet_loss_rate);

  // === 3. 信号强度评分（根据网络类型差异化处理） ===
  double signal_strength_score = calculate_signal_strength_score(quality.signal_strength, quality.network_type);

  // === 4. 带宽评分（暂时去掉，简化评分算法） ===
  // double download_speed_score = calculate_bandwidth_score(quality.download_speed_mbps, true);
  // double upload_speed_score = calculate_bandwidth_score(quality.upload_speed_mbps, false);
  double download_speed_score = 0.0;  // 暂时不计入评分
  double upload_speed_score = 0.0;    // 暂时不计入评分

  // === 5. 连通性评分（基础连接能力） ===
  double connectivity_score = calculate_connectivity_score(quality);

  // === 6. 计算加权总分（科学的权重分配，暂时去掉带宽评分） ===
  double total_score =
    latency_score * latency_weight +
    packet_loss_score * packet_loss_weight +
    signal_strength_score * signal_strength_weight +
    connectivity_score * connectivity_weight;
    // 暂时去掉带宽评分
    // + download_speed_score * download_speed_weight +
    // + upload_speed_score * upload_speed_weight;

  // === 7. 网络类型修正因子 ===
  double network_type_factor = get_network_type_factor(quality.network_type);
  total_score *= network_type_factor;

  // === 8. 稳定性修正（基于历史数据） ===
  double stability_factor = calculate_stability_factor(quality);
  total_score *= stability_factor;

  // 确保评分在合理范围内
  total_score = std::max(0.0, std::min(100.0, total_score));

  RCLCPP_DEBUG(logger_, "[SCORE] 网络评分计算完成 - 总分: %.2f", total_score);
  RCLCPP_DEBUG(logger_, "[SCORE] 各项得分 - 延迟: %.1f, 丢包: %.1f, 信号: %.1f, 连通性: %.1f",
               latency_score, packet_loss_score, signal_strength_score, connectivity_score);
  // 暂时去掉带宽评分显示: 下载: %.1f, 上传: %.1f

  return static_cast<float>(total_score);
}

// 异步网络质量检查实现
void NetworkSwitch::start_async_quality_check(uint8_t network_type, const std::string & interface_name)
{
  // 如果上一个异步任务还在进行中，跳过这次检查
  if (quality_check_in_progress_.load()) {
    RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] 网络质量检查仍在进行中，跳过本次检查");
    return;
  }

  // 标记检查开始
  quality_check_in_progress_.store(true);
  current_quality_check_interface_ = interface_name;

  // 启动异步任务
  quality_check_future_ = std::async(std::launch::async, [this, network_type, interface_name]() -> gen3_network_interfaces::msg::NetworkQuality {
    RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] 开始异步网络质量检查");

    gen3_network_interfaces::msg::NetworkQuality result;
    try {
      // 执行实际的质量检查（原来的同步逻辑）
      result = perform_quality_check_async(network_type, interface_name);
      RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] 异步网络质量检查完成，评分: %.1f", result.overall_score);
    } catch (const std::exception& e) {
      RCLCPP_ERROR(logger_, "[ASYNC_QUALITY] 网络质量检查异常: %s", e.what());
      // 创建默认的失败结果
      result.interface_name = interface_name;
      result.network_type = network_type;
      result.overall_score = 0.0;
      result.is_suitable_for_switching = false;
    }

    // 标记检查完成
    quality_check_in_progress_.store(false);
    return result;
  });
}

void NetworkSwitch::check_async_quality_result()
{
  // 检查异步质量检查结果
  if (quality_check_future_.valid()) {
    auto status = quality_check_future_.wait_for(std::chrono::milliseconds(0));
    if (status == std::future_status::ready) {
      try {
        auto quality_result = quality_check_future_.get();

        // 更新质量信息到缓存
        {
          std::lock_guard<std::mutex> lock(switch_mutex_);
          network_quality_map_[quality_result.interface_name] = quality_result;
        }

        // 发布质量信息
        publish_network_quality(quality_result);

        RCLCPP_DEBUG(logger_, "[ASYNC_QUALITY] 异步质量检查结果已处理 - 接口: %s, 评分: %.1f",
                     quality_result.interface_name.c_str(), quality_result.overall_score);
      } catch (const std::exception& e) {
        RCLCPP_ERROR(logger_, "[ASYNC_QUALITY] 获取异步质量检查结果异常: %s", e.what());
      }
    }
  }
}

// === 科学评分算法实现 ===

double NetworkSwitch::calculate_latency_score(double latency_ms, uint8_t network_type)
{
  // 从配置参数中获取基于网络类型的延迟期望值（毫秒）
  double expected_latency = node_->get_parameter("network_switch.expected_latency.default").as_double();

  switch (network_type) {
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI:
      expected_latency = node_->get_parameter("network_switch.expected_latency.wifi").as_double();
      break;
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G:
      expected_latency = node_->get_parameter("network_switch.expected_latency.fiveg").as_double();
      break;
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET:
      expected_latency = node_->get_parameter("network_switch.expected_latency.ethernet").as_double();
      break;
  }

  // 使用对数函数计算延迟评分，更符合人类感知
  // 公式: 100 * exp(-latency / expected_latency)
  double score = 100.0 * std::exp(-latency_ms / expected_latency);

  // 对于极低延迟给予奖励
  if (latency_ms < expected_latency * 0.5) {
    score = std::min(100.0, score * 1.1);
  }

  RCLCPP_DEBUG(logger_, "[SCORE] 延迟评分 - 延迟: %.1fms, 期望: %.1fms, 得分: %.1f",
               latency_ms, expected_latency, score);

  return std::max(0.0, std::min(100.0, score));
}

double NetworkSwitch::calculate_packet_loss_score(double packet_loss_rate)
{
  // 丢包率评分使用指数衰减函数
  // 即使很小的丢包率也会显著影响用户体验
  double score = 100.0 * std::exp(-packet_loss_rate * 0.5);

  // 零丢包给予满分
  if (packet_loss_rate < 0.01) {
    score = 100.0;
  }

  RCLCPP_DEBUG(logger_, "[SCORE] 丢包率评分 - 丢包率: %.2f%%, 得分: %.1f",
               packet_loss_rate, score);

  return std::max(0.0, std::min(100.0, score));
}

double NetworkSwitch::calculate_signal_strength_score(int32_t signal_strength, uint8_t network_type)
{
  double score = 100.0;  // 非无线网络默认满分

  if (network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
    // WiFi信号强度评分（dBm）
    // 优秀: -30 to -50 dBm
    // 良好: -50 to -60 dBm
    // 一般: -60 to -70 dBm
    // 较差: -70 to -80 dBm
    // 很差: < -80 dBm

    if (signal_strength >= -50) {
      score = 100.0;  // 优秀
    } else if (signal_strength >= -60) {
      score = 90.0 - (signal_strength + 50) * 2.0;  // 90-70分
    } else if (signal_strength >= -70) {
      score = 70.0 - (signal_strength + 60) * 3.0;  // 70-40分
    } else if (signal_strength >= -80) {
      score = 40.0 - (signal_strength + 70) * 2.0;  // 40-20分
    } else {
      score = std::max(0.0, 20.0 + (signal_strength + 80) * 0.5);  // <20分
    }
  } else if (network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G) {
    // 5G信号强度评分（简化处理，实际应该考虑RSRP、RSRQ等）
    if (signal_strength >= -70) {
      score = 100.0;
    } else if (signal_strength >= -85) {
      score = 100.0 - (signal_strength + 70) * 2.0;
    } else {
      score = std::max(0.0, 70.0 + (signal_strength + 85) * 1.5);
    }
  }

  RCLCPP_DEBUG(logger_, "[SCORE] 信号强度评分 - 强度: %ddBm, 类型: %d, 得分: %.1f",
               signal_strength, network_type, score);

  return std::max(0.0, std::min(100.0, score));
}

// 暂时去掉带宽评分函数
/*
double NetworkSwitch::calculate_bandwidth_score(double bandwidth_mbps, bool is_download)
{
  // 基于实际使用需求的带宽评分
  // 使用对数函数，因为带宽的感知不是线性的

  double reference_bandwidth = is_download ? 10.0 : 5.0;  // 参考带宽（Mbps）

  if (bandwidth_mbps <= 0.1) {
    return 0.0;  // 极低带宽
  }

  // 使用对数函数: score = 100 * log(1 + bandwidth/reference) / log(1 + max_expected/reference)
  double max_expected = is_download ? 100.0 : 50.0;  // 最大期望带宽
  double score = 100.0 * std::log(1.0 + bandwidth_mbps / reference_bandwidth) /
                         std::log(1.0 + max_expected / reference_bandwidth);

  // 对于超高带宽给予适当奖励，但不超过100分
  if (bandwidth_mbps > max_expected) {
    score = std::min(100.0, score * 1.05);
  }

  RCLCPP_DEBUG(logger_, "[SCORE] 带宽评分 - %s: %.2fMbps, 得分: %.1f",
               is_download ? "下载" : "上传", bandwidth_mbps, score);

  return std::max(0.0, std::min(100.0, score));
}
*/

double NetworkSwitch::calculate_connectivity_score(const gen3_network_interfaces::msg::NetworkQuality & quality)
{
  double score = 0.0;

  // 基础连通性检查（权重分配）
  if (quality.dns_working) {
    score += 60.0;  // DNS工作占60%（原来30%，现在承担更多权重）
  }

  // DNS解析时间修正
  if (quality.dns_working && quality.dns_resolution_time_ms > 0) {
    if (quality.dns_resolution_time_ms < 50.0) {
      score += 5.0;  // 快速DNS解析奖励
    } else if (quality.dns_resolution_time_ms > 500.0) {
      score -= 5.0;  // 慢速DNS解析惩罚
    }
  }

  // 工作DNS服务器数量修正
  if (quality.working_dns_servers.size() > 1) {
    score += 5.0;  // 多个DNS服务器可用奖励
  }

  RCLCPP_DEBUG(logger_, "[SCORE] 连通性评分 - DNS: %s, 得分: %.1f",
               quality.dns_working ? "正常" : "异常", score);

  return std::max(0.0, std::min(100.0, score));
}

double NetworkSwitch::get_network_type_factor(uint8_t network_type)
{
  // 不同网络类型的基础修正因子
  switch (network_type) {
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET:
      return 1.1;   // 以太网稳定性奖励
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G:
      return 1.05;  // 5G技术先进性奖励
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI:
      return 1.0;   // WiFi基准
    default:
      return 0.9;   // 未知网络类型惩罚
  }
}

double NetworkSwitch::calculate_stability_factor(const gen3_network_interfaces::msg::NetworkQuality & quality)
{
  // 基于当前质量数据的稳定性评估
  double factor = 1.0;

  // 抖动影响稳定性
  if (quality.jitter_ms > 0) {
    if (quality.jitter_ms < 5.0) {
      factor += 0.02;  // 低抖动奖励
    } else if (quality.jitter_ms > 20.0) {
      factor -= 0.05;  // 高抖动惩罚
    }
  }

  // 测试时间长度影响可信度
  auto test_duration = quality.test_end_time.sec - quality.test_start_time.sec;
  if (test_duration > 10) {
    factor += 0.03;  // 长时间测试更可信
  } else if (test_duration < 3) {
    factor -= 0.02;  // 短时间测试可信度低
  }

  RCLCPP_DEBUG(logger_, "[SCORE] 稳定性因子 - 抖动: %.1fms, 测试时长: %lds, 因子: %.3f",
               quality.jitter_ms, test_duration, factor);

  return std::max(0.8, std::min(1.2, factor));  // 限制在合理范围内
}

}  // namespace gen3_network_manager_core