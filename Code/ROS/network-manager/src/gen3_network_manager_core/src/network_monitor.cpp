// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "gen3_network_manager_core/network_monitor.hpp"
#include "gen3_network_manager_core/utils/network_utils.hpp"
#include "gen3_network_manager_core/utils/enum_utils.hpp"

#include <functional>
#include <memory>
#include <string>
#include <chrono>
#include <thread>

using namespace std::chrono_literals;
using std::placeholders::_1;

namespace gen3_network_manager_core
{

NetworkMonitor::NetworkMonitor(const rclcpp::Node::SharedPtr & node)
: node_(node), logger_(rclcpp::get_logger("network_monitor"))
{
  RCLCPP_INFO(logger_, "创建网络监控器");
  
  // 声明参数
  node_->declare_parameter("network_check_interval", 1.0);
  node_->declare_parameter("connectivity_check_interval", 10.0);

  // 连通性检查相关参数
  node_->declare_parameter("connectivity.dns_server", "www.baidu.com");
  node_->declare_parameter("connectivity.internet_server", "8.8.8.8");
  node_->declare_parameter("connectivity.ping_timeout_ms", 2000);
  node_->declare_parameter("connectivity.dns_timeout_ms", 2000);
  node_->declare_parameter("connectivity.gateway_timeout_ms", 1000);
  node_->declare_parameter("connectivity.external_timeout_ms", 2000);
}

NetworkMonitor::~NetworkMonitor()
{
  RCLCPP_INFO(logger_, "销毁网络监控器");
}

bool NetworkMonitor::init()
{
  RCLCPP_INFO(logger_, "初始化网络监控器");
  
  try {
    // 创建发布者
    network_status_broadcast_pub_ = node_->create_publisher<gen3_network_interfaces::msg::NetworkStatus>(
      "network_status_broadcast", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建网络状态广播发布者: network_status_broadcast");

    connection_status_pub_ = node_->create_publisher<std_msgs::msg::String>(
      "connection_status", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建连接状态发布者: connection_status");

    board_status_pub_ = node_->create_publisher<std_msgs::msg::String>(
      "board_status", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建板间通信状态发布者: board_status");
    
    // 创建订阅者
    network_status_sub_ = node_->create_subscription<gen3_network_interfaces::msg::NetworkStatus>(
      "network_status", 10, std::bind(&NetworkMonitor::network_status_callback, this, _1));
    RCLCPP_INFO(logger_, "[SUB] 创建网络状态订阅者: network_status");

    //TODO 暂时未使用
    network_quality_sub_ = node_->create_subscription<gen3_network_interfaces::msg::NetworkQuality>(
      "network_quality", 10, std::bind(&NetworkMonitor::network_quality_callback, this, _1));
    RCLCPP_INFO(logger_, "[SUB] 创建网络质量订阅者: network_quality");

    binding_status_sub_ = node_->create_subscription<gen3_network_interfaces::msg::BindingStatus>(
      "binding_status", 10, std::bind(&NetworkMonitor::binding_status_callback, this, _1));
    RCLCPP_INFO(logger_, "[SUB] 创建绑定状态订阅者: binding_status");
    
    // 创建定时器
    // double monitor_interval = node_->get_parameter("network_check_interval").as_double();

    // monitor_timer_ = node_->create_wall_timer(
    //   std::chrono::duration<double>(monitor_interval),
    //   std::bind(&NetworkMonitor::monitor_timer_callback, this));
    // RCLCPP_INFO(logger_, "[TIMER] 创建网络监控定时器，间隔: %.1f秒", monitor_interval);

    // // 立即获取并广播当前网络状态
    // RCLCPP_INFO(logger_, "[INIT] 立即获取并广播当前网络状态");
    // broadcast_network_status(false);  // 立即广播真实状态

    // 立即执行一次网络监控定时器
    RCLCPP_INFO(logger_, "[TIMER] 立即执行首次网络监控");
    monitor_timer_callback();

    // 获取并缓存连通性检查参数（避免在每次检查时重复获取）
    RCLCPP_INFO(logger_, "[INIT] 获取连通性检查参数");
    dns_server_ = node_->get_parameter("connectivity.dns_server").as_string();
    internet_server_ = node_->get_parameter("connectivity.internet_server").as_string();
    dns_timeout_ms_ = node_->get_parameter("connectivity.dns_timeout_ms").as_int();
    ping_timeout_ms_ = node_->get_parameter("connectivity.ping_timeout_ms").as_int();
    gateway_timeout_ms_ = node_->get_parameter("connectivity.gateway_timeout_ms").as_int();
    external_timeout_ms_ = node_->get_parameter("connectivity.external_timeout_ms").as_int();

    RCLCPP_INFO(logger_, "[INIT] 连通性检查参数 - DNS服务器: %s, 互联网服务器: %s",
                dns_server_.c_str(), internet_server_.c_str());
    RCLCPP_INFO(logger_, "[INIT] 超时配置 - DNS: %dms, Ping: %dms, 网关: %dms, 外部: %dms",
                dns_timeout_ms_, ping_timeout_ms_, gateway_timeout_ms_, external_timeout_ms_);

    double connectivity_check_interval = node_->get_parameter("connectivity_check_interval").as_double();

    connectivity_check_timer_ = node_->create_wall_timer(
      std::chrono::duration<double>(connectivity_check_interval),
      std::bind(&NetworkMonitor::connectivity_check_timer_callback, this));
    RCLCPP_INFO(logger_, "[TIMER] 创建连通性检查定时器，间隔: %.1f秒", connectivity_check_interval);

    // 启动第一次异步连通性检查
    RCLCPP_INFO(logger_, "[TIMER] 启动首次异步连通性检查");
    start_async_connectivity_check();
    start_async_board_connectivity_check();
    
    RCLCPP_INFO(logger_, "网络监控器初始化完成");
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "网络监控器初始化失败: %s", e.what());
    return false;
  }
}

void NetworkMonitor::network_status_callback(
  const gen3_network_interfaces::msg::NetworkStatus::SharedPtr msg)
{
  // 使用统一的枚举转换函数
  std::string connection_status_str = utils::EnumUtils::connection_status_to_string(msg->connection_status);

  RCLCPP_INFO(logger_,
    "[SUB] 收到网络状态消息 - 接口: %s, 连接状态: %s, IP: %s, 网关: %s",
    msg->interface_name.c_str(), connection_status_str.c_str(),
    msg->ip_address.c_str(), msg->gateway.c_str());

  std::lock_guard<std::mutex> lock(monitor_mutex_);

  // 检查网络状态变化
  bool status_changed = false;
  bool interface_changed = false;

  if (current_status_.connection_status != msg->connection_status) {
    status_changed = true;
    // 使用统一的枚举转换函数
    std::string old_status_str = utils::EnumUtils::connection_status_to_string(current_status_.connection_status);

    RCLCPP_INFO(logger_,
      "[SUB] 检测到连接状态变化: %s -> %s",
      old_status_str.c_str(), connection_status_str.c_str());
  }

  if (current_status_.interface_name != msg->interface_name) {
    interface_changed = true;
    RCLCPP_INFO(logger_,
      "[SUB] 检测到接口变化: %s -> %s",
      current_status_.interface_name.c_str(), msg->interface_name.c_str());
  }

  // 更新当前状态
  current_status_ = *msg;

  // 如果状态变化，更新连接统计
  if (status_changed) {
    bool is_connected =
      (msg->connection_status == gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_CONNECTED);
    RCLCPP_INFO(logger_,
      "[SUB] 处理连接状态变化 - 接口: %s, 已连接: %s",
      msg->interface_name.c_str(), is_connected ? "是" : "否");

    update_connection_stats(msg->interface_name, is_connected);

    // 发布连接状态
    std::string status_msg = is_connected ? "CONNECTED" : "DISCONNECTED";
    publish_connection_status(status_msg);
  }

  // 如果接口变化，广播网络状态
  if (interface_changed) {
    RCLCPP_INFO(logger_, "[SUB] 接口变化，触发网络状态广播");
    broadcast_network_status(true); // 已经持有锁
  }

  RCLCPP_DEBUG(logger_, "[SUB] 网络状态回调处理完成");
}

void NetworkMonitor::network_quality_callback(
  const gen3_network_interfaces::msg::NetworkQuality::SharedPtr msg)
{
  RCLCPP_INFO(logger_,
    "[SUB] 收到网络质量消息 - 接口: %s, 延迟: %.2fms, 丢包率: %.2f%%, 下载速度: %.2fMbps",
    msg->interface_name.c_str(), msg->latency_ms, msg->packet_loss_rate,
    msg->download_speed_mbps);

  std::lock_guard<std::mutex> lock(monitor_mutex_);
  // 更新网络质量信息
  //TODO 相关参数暂时未使用
  network_quality_map_[msg->interface_name] = *msg;
  RCLCPP_DEBUG(logger_, "[SUB] 已更新接口 %s 的网络质量信息", msg->interface_name.c_str());

  // 检查DNS连通性
  if (!msg->dns_working &&
      msg->interface_name == current_status_.interface_name) {
    RCLCPP_WARN(logger_,
      "[SUB] 检测到当前接口 %s DNS解析异常，发布连接状态", msg->interface_name.c_str());
    publish_connection_status("DNS_UNREACHABLE");
  }

  RCLCPP_DEBUG(logger_, "[SUB] 网络质量回调处理完成");
}

void NetworkMonitor::binding_status_callback(
  const gen3_network_interfaces::msg::BindingStatus::SharedPtr msg)
{
  // 使用统一的枚举转换函数
  std::string binding_status_str = utils::EnumUtils::binding_status_to_string(msg->binding_status);

  RCLCPP_INFO(logger_,
    "[SUB] 收到绑定状态消息 - 设备ID: %s, 绑定状态: %s",
    msg->device_id.c_str(), binding_status_str.c_str());

  // 在绑定成功时广播网络状态
  if (msg->binding_status == gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_SUCCESS) {
    RCLCPP_INFO(logger_, "[SUB] 绑定成功，触发网络状态广播");
    broadcast_network_status(false); // 需要获取锁
  } else {
    RCLCPP_WARN(logger_, "[SUB] 绑定失败，状态: %d", msg->binding_status);
  }

  RCLCPP_DEBUG(logger_, "[SUB] 绑定状态回调处理完成");
}

void NetworkMonitor::monitor_timer_callback()
{
  RCLCPP_DEBUG(logger_, "[TIMER] 网络监控定时器触发");

  // 检查WiFi连接状态和路由完整性
  check_wifi_route_integrity();

  // 周期性广播网络状态
  broadcast_network_status(false); // 需要获取锁
  RCLCPP_DEBUG(logger_, "[TIMER] 网络监控定时器处理完成");
}

void NetworkMonitor::connectivity_check_timer_callback()
{
  RCLCPP_DEBUG(logger_, "[TIMER] 连通性检查定时器触发（异步模式）");

  // 检查异步任务结果并启动新的异步检查
  check_async_results();

  // 发送上次的检测值（异步方式，避免阻塞定时器）
  bool connectivity = last_connectivity_result_.load();
  bool board_connectivity = last_board_connectivity_result_.load();

  RCLCPP_DEBUG(logger_, "[TIMER] 使用上次检测结果 - 网络连通性: %s, 板间通信: %s",
               connectivity ? "正常" : "异常", board_connectivity ? "正常" : "异常");

  // 发布状态（基于上次的检测值）
  if (!connectivity) {
    RCLCPP_WARN(logger_, "[TIMER] 网络连通性丢失，发布状态");
    publish_connection_status("CONNECTIVITY_LOST");
  } else {
    publish_connection_status("CONNECTIVITY_OK");
  }

  if (!board_connectivity) {
    RCLCPP_WARN(logger_, "[TIMER] 板间通信丢失，发布状态");
    publish_board_status("BOARD_COMMUNICATION_LOST");
  } else {
    publish_board_status("BOARD_COMMUNICATION_OK");
  }

  // 启动新的异步检查（为下次定时器回调准备）
  start_async_connectivity_check();
  start_async_board_connectivity_check();

  RCLCPP_DEBUG(logger_, "[TIMER] 连通性检查定时器处理完成（异步模式）");
}

bool NetworkMonitor::check_connectivity()
{
  try {
    // 检查网关可达性 - 使用多种方法确保准确性
    bool gateway_reachable = false;

    // 网关连通性检查已移除，简化连通性判断

    // 检查DNS解析（使用缓存的参数）
    bool dns_working = utils::NetworkUtils::test_dns_resolution(dns_server_, dns_timeout_ms_);

    // 更新连接统计
    bool is_connected = dns_working;
    update_connection_stats(current_status_.interface_name, is_connected);
    
    return is_connected;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "检查连通性异常: %s", e.what());
    return false;
  }
}

bool NetworkMonitor::check_board_connectivity()
{
  try {
    // TODO 简化处理，实际应该检查板间通信
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "检查板间通信异常: %s", e.what());
    return false;
  }
}

void NetworkMonitor::update_connection_stats(const std::string & interface, bool is_connected)
{
  auto now = std::chrono::steady_clock::now();
  
  // 获取或创建接口统计信息
  auto & stats = connection_stats_[interface];
  
  // 更新连接状态
  if (is_connected != stats.is_connected) {
    if (is_connected) {
      // 连接状态变化：断开->连接
      stats.last_connected_time = now;
      stats.connect_count++;
      
      RCLCPP_INFO(
        logger_, "网络连接恢复: 接口=%s, 连接次数=%d",
        interface.c_str(), stats.connect_count);
    } else {
      // 连接状态变化：连接->断开
      stats.last_disconnected_time = now;
      stats.disconnect_count++;
      
      RCLCPP_WARN(
        logger_, "网络连接断开: 接口=%s, 断开次数=%d",
        interface.c_str(), stats.disconnect_count);
    }
    
    stats.is_connected = is_connected;
  }
  
  // 计算在线时间百分比
  if (stats.connect_count > 0) {
    // 简化处理，实际应该累计连接时间
    stats.uptime_percentage = stats.is_connected ? 100.0 : 
      (100.0 * (stats.connect_count - 1) / (stats.connect_count + stats.disconnect_count));
  } else {
    stats.uptime_percentage = 0.0;
  }
}

void NetworkMonitor::publish_connection_status(const std::string & status)
{
  std_msgs::msg::String msg;
  msg.data = status;
  connection_status_pub_->publish(msg);

  RCLCPP_DEBUG(logger_, "[PUB] 发布连接状态: %s", status.c_str());
}

void NetworkMonitor::publish_board_status(const std::string & status)
{
  std_msgs::msg::String msg;
  msg.data = status;
  board_status_pub_->publish(msg);

  RCLCPP_DEBUG(logger_, "[PUB] 发布板间通信状态: %s", status.c_str());
}

void NetworkMonitor::broadcast_network_status(bool mutex_already_locked)
{
  // 如果互斥锁没有被锁定，则获取锁
  std::unique_lock<std::mutex> lock(monitor_mutex_, std::defer_lock);
  if (!mutex_already_locked) {
    lock.lock();
  }

  // 更新时间戳
  current_status_.header.stamp = node_->now();

  // 广播网络状态
  network_status_broadcast_pub_->publish(current_status_);

  // 使用统一的枚举转换函数
  std::string broadcast_status_str = utils::EnumUtils::connection_status_to_string(current_status_.connection_status);

  RCLCPP_DEBUG(logger_,
    "[PUB] 广播网络状态 - 接口: %s, 连接状态: %s, IP: %s",
    current_status_.interface_name.c_str(),
    broadcast_status_str.c_str(),
    current_status_.ip_address.c_str());
}

// WiFi路由完整性检查
void NetworkMonitor::check_wifi_route_integrity()
{
  try {
    // 1. 检查WiFi连接状态
    auto current_wifi = utils::NetworkUtils::get_current_wifi_info();

    if (!current_wifi.is_connected) {
      // WiFi未连接，无需检查路由
      return;
    }

    RCLCPP_DEBUG(logger_, "[ROUTE_CHECK] 检查WiFi路由完整性: %s",
      current_wifi.ssid.c_str());

    // 2. 检查WiFi接口是否有默认路由
    auto network_config = utils::NetworkUtils::get_network_config();
    std::string route_cmd = "ip route show default dev " + network_config.wifi_interface;
    std::string route_output = utils::NetworkUtils::exec_command(route_cmd);

    bool has_default_route = !route_output.empty() &&
                            route_output.find("default") != std::string::npos;

    if (has_default_route) {
      RCLCPP_DEBUG(logger_, "[ROUTE_CHECK] WiFi默认路由正常: %s",
        route_output.c_str());
      return;
    }

    // 2.1 记录当前所有默认路由（用于调试）
    std::string all_routes_cmd = "ip route show default";
    std::string all_routes = utils::NetworkUtils::exec_command(all_routes_cmd);

    if (!all_routes.empty()) {
      RCLCPP_DEBUG(logger_, "[ROUTE_CHECK] 当前所有默认路由: %s", all_routes.c_str());
    }

    // 3. WiFi已连接但没有默认路由，检查是否有IP地址
    auto interfaces = utils::NetworkUtils::get_network_interfaces();
    bool wifi_has_ip = false;
    std::string wifi_ip;

    for (const auto & iface : interfaces) {
      if (iface.name == network_config.wifi_interface && !iface.ip_address.empty()) {
        wifi_has_ip = true;
        wifi_ip = iface.ip_address;
        break;
      }
    }

    if (!wifi_has_ip) {
      RCLCPP_DEBUG(logger_, "[ROUTE_CHECK] WiFi接口无IP地址，跳过路由修复");
      return;
    }

    // 4. WiFi有连接有IP但无默认路由，这是异常状态，需要修复
    RCLCPP_WARN(logger_,
      "[ROUTE_CHECK] 检测到WiFi异常状态 - 已连接(%s, IP: %s)但缺少默认路由，开始修复",
      current_wifi.ssid.c_str(), wifi_ip.c_str());

    // 4.1 检查WiFi接口的网关信息
    std::string gateway_cmd = "ip route show | grep 'dev " + network_config.wifi_interface + "' | grep -v 'scope link'";
    std::string gateway_info = utils::NetworkUtils::exec_command(gateway_cmd);

    if (!gateway_info.empty()) {
      RCLCPP_INFO(logger_, "[ROUTE_CHECK] WiFi接口路由信息: %s", gateway_info.c_str());
    } else {
      RCLCPP_WARN(logger_, "[ROUTE_CHECK] WiFi接口没有任何路由信息");
    }

    // 5. 重新连接WiFi以恢复路由
    bool reconnect_success = reconnect_wifi_to_fix_route(current_wifi.ssid);

    if (reconnect_success) {
      RCLCPP_INFO(logger_,
        "[ROUTE_CHECK] WiFi路由修复成功: %s", current_wifi.ssid.c_str());
    } else {
      RCLCPP_ERROR(logger_,
        "[ROUTE_CHECK] WiFi路由修复失败: %s", current_wifi.ssid.c_str());
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[ROUTE_CHECK] WiFi路由检查异常: %s", e.what());
  }
}

// 重新连接WiFi以修复路由
bool NetworkMonitor::reconnect_wifi_to_fix_route(const std::string & ssid)
{
  RCLCPP_INFO(logger_, "[ROUTE_FIX] 开始重新连接WiFi修复路由: %s", ssid.c_str());

  try {
    // 1. 保存当前静态路由（如果有的话）
    std::string save_routes_cmd = "ip route show | grep -v default | grep -v 'scope link' | grep -v '127.0.0.1'";
    std::string saved_routes = utils::NetworkUtils::exec_command(save_routes_cmd);

    // 2. 断开当前WiFi连接
    RCLCPP_INFO(logger_, "[ROUTE_FIX] 断开当前WiFi连接");
    auto network_config_fix = utils::NetworkUtils::get_network_config();
    std::string disconnect_cmd = "nmcli device disconnect " + network_config_fix.wifi_interface;
    utils::NetworkUtils::exec_command(disconnect_cmd);

    // 等待断开完成
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // 3. 重新连接到WiFi
    RCLCPP_INFO(logger_, "[ROUTE_FIX] 重新连接到WiFi: %s", ssid.c_str());
    bool connect_result = utils::NetworkUtils::connect_to_wifi(ssid, "", "WPA2");

    if (!connect_result) {
      RCLCPP_ERROR(logger_, "[ROUTE_FIX] WiFi重连失败: %s", ssid.c_str());
      return false;
    }

    // 4. 等待连接稳定
    std::this_thread::sleep_for(std::chrono::seconds(5));

    // 5. 验证路由是否恢复
    std::string verify_cmd = "ip route show default dev " + network_config_fix.wifi_interface;
    std::string verify_output = utils::NetworkUtils::exec_command(verify_cmd);

    bool route_restored = !verify_output.empty() &&
                         verify_output.find("default") != std::string::npos;

    if (route_restored) {
      RCLCPP_INFO(logger_, "[ROUTE_FIX] WiFi默认路由已恢复: %s",
        verify_output.c_str());

      // 6. 恢复之前保存的静态路由（如果有）
      if (!saved_routes.empty()) {
        RCLCPP_INFO(logger_, "[ROUTE_FIX] 恢复静态路由");
        // 这里可以添加静态路由恢复逻辑
      }

      return true;
    } else {
      RCLCPP_ERROR(logger_, "[ROUTE_FIX] WiFi重连后仍无默认路由");
      return false;
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[ROUTE_FIX] WiFi路由修复异常: %s", e.what());
    return false;
  }
}

// 异步连通性检查实现
void NetworkMonitor::start_async_connectivity_check()
{
  // 如果上一个异步任务还在进行中，跳过这次检查
  if (connectivity_check_in_progress_.load()) {
    RCLCPP_DEBUG(logger_, "[ASYNC] 网络连通性检查仍在进行中，跳过本次检查");
    return;
  }

  // 标记检查开始
  connectivity_check_in_progress_.store(true);

  // 启动异步任务
  connectivity_future_ = std::async(std::launch::async, [this]() -> bool {
    RCLCPP_DEBUG(logger_, "[ASYNC] 开始异步网络连通性检查");

    bool result = false;
    try {
      // 执行实际的连通性检查（原来的同步逻辑）
      result = check_connectivity();
      RCLCPP_DEBUG(logger_, "[ASYNC] 异步网络连通性检查完成: %s", result ? "正常" : "异常");
    } catch (const std::exception& e) {
      RCLCPP_ERROR(logger_, "[ASYNC] 网络连通性检查异常: %s", e.what());
      result = false;
    }

    // 标记检查完成
    connectivity_check_in_progress_.store(false);
    return result;
  });
}

void NetworkMonitor::start_async_board_connectivity_check()
{
  // 如果上一个异步任务还在进行中，跳过这次检查
  if (board_connectivity_check_in_progress_.load()) {
    RCLCPP_DEBUG(logger_, "[ASYNC] 板间通信检查仍在进行中，跳过本次检查");
    return;
  }

  // 标记检查开始
  board_connectivity_check_in_progress_.store(true);

  // 启动异步任务
  board_connectivity_future_ = std::async(std::launch::async, [this]() -> bool {
    RCLCPP_DEBUG(logger_, "[ASYNC] 开始异步板间通信检查");

    bool result = true;
    try {
      // 执行实际的板间通信检查（原来的同步逻辑）
      result = check_board_connectivity();
      RCLCPP_DEBUG(logger_, "[ASYNC] 异步板间通信检查完成: %s", result ? "正常" : "异常");
    } catch (const std::exception& e) {
      RCLCPP_ERROR(logger_, "[ASYNC] 板间通信检查异常: %s", e.what());
      result = false;
    }

    // 标记检查完成
    board_connectivity_check_in_progress_.store(false);
    return result;
  });
}

void NetworkMonitor::check_async_results()
{
  // 检查网络连通性异步任务结果
  if (connectivity_future_.valid()) {
    auto status = connectivity_future_.wait_for(std::chrono::milliseconds(0));
    if (status == std::future_status::ready) {
      try {
        bool result = connectivity_future_.get();
        last_connectivity_result_.store(result);
        RCLCPP_DEBUG(logger_, "[ASYNC] 更新网络连通性结果: %s", result ? "正常" : "异常");
      } catch (const std::exception& e) {
        RCLCPP_ERROR(logger_, "[ASYNC] 获取网络连通性结果异常: %s", e.what());
        last_connectivity_result_.store(false);
      }
    }
  }

  // 检查板间通信异步任务结果
  if (board_connectivity_future_.valid()) {
    auto status = board_connectivity_future_.wait_for(std::chrono::milliseconds(0));
    if (status == std::future_status::ready) {
      try {
        bool result = board_connectivity_future_.get();
        last_board_connectivity_result_.store(result);
        RCLCPP_DEBUG(logger_, "[ASYNC] 更新板间通信结果: %s", result ? "正常" : "异常");
      } catch (const std::exception& e) {
        RCLCPP_ERROR(logger_, "[ASYNC] 获取板间通信结果异常: %s", e.what());
        last_board_connectivity_result_.store(false);
      }
    }
  }
}

}  // namespace gen3_network_manager_core