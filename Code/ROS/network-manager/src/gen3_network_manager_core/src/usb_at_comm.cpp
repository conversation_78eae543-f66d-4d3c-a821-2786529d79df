// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "gen3_network_manager_core/usb_at_comm.hpp"

#include <dirent.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <linux/usbdevice_fs.h>
#include <linux/usb/ch9.h>
#include <cstring>
#include <cctype>
#include <errno.h>

namespace gen3_network_manager_core
{

// 支持的设备 ID 列表（基于 111.c）
const std::vector<UsbDeviceId> UsbAtComm::supported_devices_ = {
  {
    USB_DEVICE_ID_MATCH_VENDOR | USB_DEVICE_ID_MATCH_PRODUCT | USB_DEVICE_ID_MATCH_INT_NUMBER,
    0x3505, 0x1001, 4, 0, 0, 0
  },
  {
    USB_DEVICE_ID_MATCH_VENDOR | USB_DEVICE_ID_MATCH_PRODUCT | USB_DEVICE_ID_MATCH_INT_NUMBER,
    0x3505, 0x1002, 4, 0, 0, 0
  },
  {
    USB_DEVICE_ID_MATCH_VENDOR | USB_DEVICE_ID_MATCH_INT_CLASS | 
    USB_DEVICE_ID_MATCH_INT_SUBCLASS | USB_DEVICE_ID_MATCH_INT_PROTOCOL,
    0x3505, 0, 0, 0xff, 0x03, 0x12
  }
};

UsbAtComm::UsbAtComm(const rclcpp::Node::SharedPtr & node)
: node_(node),
  logger_(rclcpp::get_logger("usb_at_comm")),
  device_fd_(-1),
  ep_in_(0),
  ep_out_(0),
  ep_len_(0),
  interface_num_(0)
{
  RCLCPP_INFO(logger_, "[USB_AT] USB AT 通信模块初始化");
}

UsbAtComm::~UsbAtComm()
{
  cleanup();
}

bool UsbAtComm::init()
{
  RCLCPP_INFO(logger_, "[USB_AT] 初始化 USB AT 通信");
  
  if (!find_device()) {
    RCLCPP_ERROR(logger_, "[USB_AT] 未找到支持的 USB 设备");
    return false;
  }
  
  if (!open_device()) {
    RCLCPP_ERROR(logger_, "[USB_AT] 打开 USB 设备失败");
    return false;
  }
  
  RCLCPP_INFO(logger_, "[USB_AT] ✓ USB AT 通信初始化成功");
  return true;
}

void UsbAtComm::cleanup()
{
  if (device_fd_ >= 0) {
    close_device();
  }
}

bool UsbAtComm::find_device()
{
  RCLCPP_INFO(logger_, "[USB_AT] 开始搜索 USB 设备...");
  return find_device_recursive(USB_DEV_DIR);
}

bool UsbAtComm::find_device_recursive(const std::string & dir_path)
{
  DIR * dir = opendir(dir_path.c_str());
  if (dir == nullptr) {
    RCLCPP_WARN(logger_, "[USB_AT] 无法打开目录: %s", dir_path.c_str());
    return false;
  }

  struct dirent * entry;
  while ((entry = readdir(dir)) != nullptr) {
    // 跳过非数字开头的目录/文件
    if (!std::isdigit(entry->d_name[0])) {
      continue;
    }

    std::string full_path = dir_path + "/" + entry->d_name;
    
    if (entry->d_type == DT_DIR) {
      // 递归搜索子目录
      if (find_device_recursive(full_path)) {
        closedir(dir);
        return true;
      }
    } else {
      // 检查设备文件
      if (get_device_descriptor(full_path)) {
        device_path_ = full_path;
        RCLCPP_INFO(logger_, "[USB_AT] ✓ 找到匹配的 USB 设备: %s", device_path_.c_str());
        closedir(dir);
        return true;
      }
    }
  }
  
  closedir(dir);
  return false;
}

bool UsbAtComm::match_device_id(const UsbDeviceId & device_id)
{
  for (const auto & supported : supported_devices_) {
    bool match = true;
    
    if (supported.match_flags & USB_DEVICE_ID_MATCH_VENDOR) {
      if (device_id.id_vendor != supported.id_vendor) {
        match = false;
      }
    }
    
    if (supported.match_flags & USB_DEVICE_ID_MATCH_PRODUCT) {
      if (device_id.id_product != supported.id_product) {
        match = false;
      }
    }
    
    if (supported.match_flags & USB_DEVICE_ID_MATCH_INT_NUMBER) {
      if (device_id.b_interface_number != supported.b_interface_number) {
        match = false;
      }
    }
    
    if (supported.match_flags & USB_DEVICE_ID_MATCH_INT_CLASS) {
      if (device_id.b_interface_class != supported.b_interface_class) {
        match = false;
      }
    }
    
    if (supported.match_flags & USB_DEVICE_ID_MATCH_INT_SUBCLASS) {
      if (device_id.b_interface_sub_class != supported.b_interface_sub_class) {
        match = false;
      }
    }
    
    if (supported.match_flags & USB_DEVICE_ID_MATCH_INT_PROTOCOL) {
      if (device_id.b_interface_protocol != supported.b_interface_protocol) {
        match = false;
      }
    }
    
    if (match) {
      RCLCPP_DEBUG(logger_, "[USB_AT] 设备匹配: VID=0x%04x, PID=0x%04x, Interface=%d",
                  device_id.id_vendor, device_id.id_product, device_id.b_interface_number);
      return true;
    }
  }
  
  return false;
}

bool UsbAtComm::get_device_descriptor(const std::string & dev_path)
{
  int fd = open(dev_path.c_str(), O_RDWR | O_NOCTTY);
  if (fd < 0) {
    return false;
  }

  uint8_t * desc = static_cast<uint8_t *>(malloc(2048));
  if (!desc) {
    close(fd);
    return false;
  }

  memset(desc, 0, 2048);
  int desc_len = read(fd, desc, 2048);
  int len = 0;
  UsbDeviceId device_id = {};

  while (len < desc_len) {
    struct usb_descriptor_header * desc_header =
      reinterpret_cast<struct usb_descriptor_header *>(desc + len);

    if (desc_header->bLength == 0) {
      break;
    }

    if ((desc_header->bLength == USB_DT_DEVICE_SIZE) &&
        (desc_header->bDescriptorType == USB_DT_DEVICE)) {
      struct usb_device_descriptor * dev_desc =
        reinterpret_cast<struct usb_device_descriptor *>(desc_header);
      device_id.id_vendor = dev_desc->idVendor;
      device_id.id_product = dev_desc->idProduct;
    } else if ((desc_header->bLength == USB_DT_INTERFACE_SIZE) &&
               (desc_header->bDescriptorType == USB_DT_INTERFACE)) {
      struct usb_interface_descriptor * interface_desc =
        reinterpret_cast<struct usb_interface_descriptor *>(desc_header);
      device_id.b_interface_number = interface_desc->bInterfaceNumber;
      device_id.b_interface_class = interface_desc->bInterfaceClass;
      device_id.b_interface_sub_class = interface_desc->bInterfaceSubClass;
      device_id.b_interface_protocol = interface_desc->bInterfaceProtocol;
    } else if ((desc_header->bLength == USB_DT_ENDPOINT_SIZE) &&
               (desc_header->bDescriptorType == USB_DT_ENDPOINT)) {
      struct usb_endpoint_descriptor * ep_desc =
        reinterpret_cast<struct usb_endpoint_descriptor *>(desc_header);

      if (match_device_id(device_id)) {
        RCLCPP_INFO(logger_, "[USB_AT] 找到匹配设备!");

        if ((ep_desc->bmAttributes & USB_ENDPOINT_XFERTYPE_MASK) == USB_ENDPOINT_XFER_BULK) {
          if (ep_desc->bEndpointAddress & USB_ENDPOINT_DIR_MASK) {
            ep_in_ = ep_desc->bEndpointAddress;
          } else {
            ep_out_ = ep_desc->bEndpointAddress;
            ep_len_ = ep_desc->wMaxPacketSize;
            interface_num_ = device_id.b_interface_number;

            RCLCPP_INFO(logger_, "[USB_AT] 端点信息: IN=0x%02x, OUT=0x%02x, 长度=%d, 接口=%d",
                       ep_in_, ep_out_, ep_len_, interface_num_);

            free(desc);
            close(fd);
            return true;
          }
        }
      }
    }

    len += desc_header->bLength;
  }

  free(desc);
  close(fd);
  return false;
}

bool UsbAtComm::open_device()
{
  RCLCPP_INFO(logger_, "[USB_AT] 打开设备: %s", device_path_.c_str());

  device_fd_ = open(device_path_.c_str(), O_RDWR | O_NOCTTY);
  if (device_fd_ < 0) {
    RCLCPP_ERROR(logger_, "[USB_AT] 设备打开失败: %s", strerror(errno));
    return false;
  }

  // 获取当前驱动
  struct usbdevfs_getdriver usb_dev;
  usb_dev.interface = interface_num_;

  int ret = ioctl(device_fd_, USBDEVFS_GETDRIVER, &usb_dev);
  if (ret && errno != ENODATA) {
    RCLCPP_ERROR(logger_, "[USB_AT] USBDEVFS_GETDRIVER 失败: %s", strerror(errno));
    close(device_fd_);
    device_fd_ = -1;
    return false;
  } else if (ret == 0) {
    // 断开现有驱动
    struct usbdevfs_ioctl operate;
    operate.data = nullptr;
    operate.ifno = interface_num_;
    operate.ioctl_code = USBDEVFS_DISCONNECT;

    ret = ioctl(device_fd_, USBDEVFS_IOCTL, &operate);
    if (ret) {
      RCLCPP_ERROR(logger_, "[USB_AT] USBDEVFS_DISCONNECT 失败: %s", strerror(errno));
      close(device_fd_);
      device_fd_ = -1;
      return false;
    }
  }

  // 声明接口
  ret = ioctl(device_fd_, USBDEVFS_CLAIMINTERFACE, &interface_num_);
  if (ret) {
    RCLCPP_ERROR(logger_, "[USB_AT] USBDEVFS_CLAIMINTERFACE 失败: %s", strerror(errno));
    close(device_fd_);
    device_fd_ = -1;
    return false;
  }

  // 控制设置
  control_setup();

  RCLCPP_INFO(logger_, "[USB_AT] ✓ 设备打开成功");
  return true;
}

bool UsbAtComm::close_device()
{
  if (device_fd_ >= 0) {
    RCLCPP_INFO(logger_, "[USB_AT] 关闭设备");
    close(device_fd_);
    device_fd_ = -1;
  }
  return true;
}

void UsbAtComm::control_setup()
{
  struct usbdevfs_ctrltransfer control;

  // 基于 111.c 中的控制消息设置
  control.bRequestType = 0x21;
  control.bRequest = 0x22;
  control.wValue = 0x0003;
  control.wIndex = 2;
  control.wLength = 0;
  control.data = nullptr;

  ioctl(device_fd_, USBDEVFS_CONTROL, &control);
}

int UsbAtComm::bulk_out(const uint8_t * data, int len)
{
  struct usbdevfs_bulktransfer bulk;
  uint8_t last_data;
  bool zlp = false;

  bulk.ep = ep_out_;
  bulk.len = len;
  bulk.data = const_cast<void *>(static_cast<const void *>(data));
  bulk.timeout = 15000;

  // 检查是否需要零长度包
  if ((len % ep_len_) == 0) {
    zlp = true;
    last_data = data[len - 1];
    len--;
  }

  int ret = ioctl(device_fd_, USBDEVFS_BULK, &bulk);
  if (ret != len) {
    RCLCPP_ERROR(logger_, "[USB_AT] bulk out 错误: %d", ret);
    return -1;
  }

  if (zlp) {
    bulk.len = 1;
    bulk.data = &last_data;
    ioctl(device_fd_, USBDEVFS_BULK, &bulk);
  }

  return 0;
}

int UsbAtComm::bulk_in(uint8_t * data, int len, int timeout_ms)
{
  struct usbdevfs_bulktransfer bulk;

  bulk.ep = ep_in_ | 0x80;
  bulk.len = len;
  bulk.data = data;
  bulk.timeout = timeout_ms;

  int ret = ioctl(device_fd_, USBDEVFS_BULK, &bulk);
  if (ret == -1) {
    return -1;
  }

  return ret;
}

bool UsbAtComm::send_at_command(const std::string & command, std::string & response, int timeout_ms)
{
  if (device_fd_ < 0) {
    RCLCPP_ERROR(logger_, "[USB_AT] 设备未连接");
    return false;
  }

  if (command.empty()) {
    RCLCPP_ERROR(logger_, "[USB_AT] AT 命令为空");
    return false;
  }

  RCLCPP_DEBUG(logger_, "[USB_AT] 发送 AT 命令: %s", command.c_str());

  // 发送命令
  std::string cmd_with_cr = command + "\r";
  if (bulk_out(reinterpret_cast<const uint8_t *>(cmd_with_cr.c_str()), cmd_with_cr.length()) != 0) {
    RCLCPP_ERROR(logger_, "[USB_AT] 发送命令失败");
    return false;
  }

  // 接收响应
  response.clear();
  uint8_t buffer[1024];

  while (true) {
    memset(buffer, 0, sizeof(buffer));
    int ret = bulk_in(buffer, sizeof(buffer) - 1, timeout_ms);

    if (ret > 0) {
      buffer[ret] = '\0';
      std::string chunk(reinterpret_cast<char *>(buffer));
      response += chunk;

      RCLCPP_DEBUG(logger_, "[USB_AT] 接收数据: %s", chunk.c_str());

      // 检查是否收到完整响应
      if (response.find("OK") != std::string::npos ||
          response.find("ERROR") != std::string::npos) {
        RCLCPP_DEBUG(logger_, "[USB_AT] 完整响应: %s", response.c_str());
        return true;
      }
    } else {
      RCLCPP_WARN(logger_, "[USB_AT] 接收超时或错误");
      return false;
    }
  }

  return false;
}

bool UsbAtComm::send_at_command_simple(const std::string & command, int timeout_ms)
{
  std::string response;
  return send_at_command(command, response, timeout_ms);
}

}  // namespace gen3_network_manager_core
