// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "gen3_network_manager_core/utils/network_utils.hpp"

#include <iostream>
#include <fstream>
#include <sstream>
#include <string>
#include <vector>
#include <map>
#include <regex>
#include <cstdio>
#include <cstdlib>
#include <memory>
#include <array>
#include <stdexcept>
#include <thread>
#include <chrono>
#include <netdb.h>
#include <sys/socket.h>
#include <sys/types.h>

#include "rclcpp/rclcpp.hpp"
#include "gen3_network_interfaces/msg/network_status.hpp"

namespace gen3_network_manager_core
{
namespace utils
{

// 静态成员变量定义
rclcpp::Node::SharedPtr NetworkUtils::node_ = nullptr;
NetworkUtils::NetworkConfig NetworkUtils::cached_config_;
bool NetworkUtils::config_loaded_ = false;

// 设置ROS节点
void NetworkUtils::set_node(const rclcpp::Node::SharedPtr & node) {
  node_ = node;
  config_loaded_ = false; // 重置配置加载状态
}

// 获取网络配置
NetworkUtils::NetworkConfig NetworkUtils::get_network_config() {
  if (!config_loaded_ && node_) {
    // 从ROS参数读取配置
    cached_config_.wifi_interface = node_->get_parameter("wifi_interface").as_string();
    cached_config_.ethernet_interface = node_->get_parameter("ethernet_interface").as_string();
    cached_config_.g5_interface = node_->get_parameter("5g_interface").as_string();

    config_loaded_ = true;

    RCLCPP_INFO(node_->get_logger(), "[CONFIG] 网络接口配置: WiFi=%s, 以太网=%s, 5G=%s",
      cached_config_.wifi_interface.c_str(),
      cached_config_.ethernet_interface.c_str(),
      cached_config_.g5_interface.c_str());
  } else if (!node_) {
    // 如果没有设置节点，使用默认配置
    RCLCPP_WARN(rclcpp::get_logger("network_utils"), "[CONFIG] 未设置ROS节点，使用默认网络接口配置");
    cached_config_.wifi_interface = "wlan0";
    cached_config_.ethernet_interface = "eth0";
    cached_config_.g5_interface = "usb0";
  }

  return cached_config_;
}

// 获取网络类型
uint8_t NetworkUtils::get_network_type(
  const std::string & interface_name,
  const std::string & wifi_interface,
  const std::string & ethernet_interface,
  const std::string & fiveg_interface)
{
  // 简单比对接口名称
  if (interface_name == wifi_interface) {
    return gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI;
  } else if (interface_name == ethernet_interface) {
    return gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET;
  } else if (interface_name == fiveg_interface) {
    return gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G;
  } else {
    return gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_UNKNOWN;
  }
}

// 执行系统命令并返回输出
std::string NetworkUtils::exec_command(const std::string & cmd) {
  std::array<char, 128> buffer;
  std::string result;
  std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(cmd.c_str(), "r"), pclose);
  
  if (!pipe) {
    throw std::runtime_error("popen() failed!");
  }
  
  while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
    result += buffer.data();
  }
  
  return result;
}

std::vector<NetworkUtils::InterfaceInfo> NetworkUtils::get_network_interfaces() {
  static auto logger = rclcpp::get_logger("network_utils");
  RCLCPP_DEBUG(logger, "[UTILS] 开始获取网络接口列表");

  std::vector<InterfaceInfo> interfaces;

  try {
    // 获取网络接口列表
    RCLCPP_DEBUG(logger, "[UTILS] 执行命令: ip -o addr show");
    std::string output = exec_command("ip -o addr show");
    std::istringstream iss(output);
    std::string line;
    
    // 解析输出
    std::regex interface_regex(R"(^\d+:\s+(\S+)\s+inet\s+(\S+)\s+.*)");
    std::map<std::string, InterfaceInfo> interface_map;
    
    while (std::getline(iss, line)) {
      std::smatch match;
      if (std::regex_search(line, match, interface_regex)) {
        std::string name = match[1];
        std::string ip = match[2];
        
        // 过滤回环接口
        if (name == "lo") {
          continue;
        }
        
        // 创建或更新接口信息
        InterfaceInfo info;
        info.name = name;
        info.ip_address = ip.substr(0, ip.find('/'));
        
        // 获取MAC地址
        std::string mac_output = exec_command("ip link show " + name);
        std::regex mac_regex(R"(link/\S+\s+(\S+)\s+.*)");
        std::smatch mac_match;
        if (std::regex_search(mac_output, mac_match, mac_regex)) {
          info.mac_address = mac_match[1];
        }
        
        // 获取接口状态
        std::string state_output = exec_command("ip link show " + name);
        info.is_up = state_output.find("state UP") != std::string::npos;
        info.is_running = state_output.find("LOWER_UP") != std::string::npos;
        
        // 获取网关
        std::string route_output = exec_command("ip route show dev " + name);
        std::regex gateway_regex(R"(default via (\S+))");
        std::smatch gateway_match;
        if (std::regex_search(route_output, gateway_match, gateway_regex)) {
          info.gateway = gateway_match[1];
        }
        
        // 获取子网掩码
        std::size_t pos = ip.find('/');
        if (pos != std::string::npos) {
          int prefix = std::stoi(ip.substr(pos + 1));
          // 将前缀长度转换为子网掩码
          uint32_t mask = 0xFFFFFFFF << (32 - prefix);
          std::ostringstream netmask;
          netmask << ((mask >> 24) & 0xFF) << "."
                  << ((mask >> 16) & 0xFF) << "."
                  << ((mask >> 8) & 0xFF) << "."
                  << (mask & 0xFF);
          info.netmask = netmask.str();
        }
        
        interface_map[name] = info;
      }
    }
    
    // 将map转换为vector
    for (const auto & pair : interface_map) {
      interfaces.push_back(pair.second);
    }
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[UTILS] 获取网络接口失败: %s", e.what());
  }

  RCLCPP_DEBUG(logger, "[UTILS] 获取网络接口完成，共找到 %zu 个接口", interfaces.size());
  return interfaces;
}

NetworkUtils::InterfaceInfo NetworkUtils::get_interface_info(const std::string & interface_name) {
  InterfaceInfo info;
  info.name = interface_name;
  
  try {
    // 获取IP地址
    std::string ip_output = exec_command("ip -o addr show " + interface_name);
    std::regex ip_regex(R"(inet\s+(\S+))");
    std::smatch ip_match;
    if (std::regex_search(ip_output, ip_match, ip_regex)) {
      std::string ip = ip_match[1];
      info.ip_address = ip.substr(0, ip.find('/'));
      
      // 获取子网掩码
      std::size_t pos = ip.find('/');
      if (pos != std::string::npos) {
        int prefix = std::stoi(ip.substr(pos + 1));
        // 将前缀长度转换为子网掩码
        uint32_t mask = 0xFFFFFFFF << (32 - prefix);
        std::ostringstream netmask;
        netmask << ((mask >> 24) & 0xFF) << "."
                << ((mask >> 16) & 0xFF) << "."
                << ((mask >> 8) & 0xFF) << "."
                << (mask & 0xFF);
        info.netmask = netmask.str();
      }
    }
    
    // 获取MAC地址
    std::string mac_output = exec_command("ip link show " + interface_name);
    std::regex mac_regex(R"(link/\S+\s+(\S+)\s+.*)");
    std::smatch mac_match;
    if (std::regex_search(mac_output, mac_match, mac_regex)) {
      info.mac_address = mac_match[1];
    }
    
    // 获取接口状态
    info.is_up = mac_output.find("state UP") != std::string::npos;
    info.is_running = mac_output.find("LOWER_UP") != std::string::npos;
    
    // 获取网关
    std::string route_output = exec_command("ip route show dev " + interface_name);
    std::regex gateway_regex(R"(default via (\S+))");
    std::smatch gateway_match;
    if (std::regex_search(route_output, gateway_match, gateway_regex)) {
      info.gateway = gateway_match[1];
    }
    
  } catch (const std::exception & e) {
    static auto logger = rclcpp::get_logger("network_utils");
    RCLCPP_ERROR(logger, "[UTILS] 获取接口信息失败: %s", e.what());
  }
  
  return info;
}

NetworkUtils::WiFiInfo NetworkUtils::get_current_wifi_info() {
  static auto logger = rclcpp::get_logger("network_utils");
  RCLCPP_DEBUG(logger, "[UTILS] 使用iw命令获取当前WiFi信息");

  WiFiInfo info;

  try {
    // 获取网络配置
    NetworkConfig config = get_network_config();

    // 使用iw命令获取当前连接信息
    std::string iw_output = exec_command("iw dev " + config.wifi_interface + " link 2>/dev/null");
    RCLCPP_DEBUG(logger, "[UTILS] iw link输出: %s", iw_output.c_str());

    // 检查是否已连接
    if (!iw_output.empty() && iw_output.find("Not connected") == std::string::npos) {
      RCLCPP_DEBUG(logger, "[UTILS] 检测到WiFi连接，开始解析信息");

      // 解析SSID
      std::regex ssid_regex("SSID:\\s*(.+)");
      std::smatch ssid_match;
      if (std::regex_search(iw_output, ssid_match, ssid_regex)) {
        info.ssid = ssid_match[1];
        // 去除可能的换行符和空格
        info.ssid.erase(info.ssid.find_last_not_of(" \n\r\t") + 1);
        RCLCPP_DEBUG(logger, "[UTILS] 解析SSID: %s", info.ssid.c_str());
      }

      // 解析BSSID
      std::regex bssid_regex("Connected to\\s+([0-9a-fA-F:]{17})");
      std::smatch bssid_match;
      if (std::regex_search(iw_output, bssid_match, bssid_regex)) {
        info.bssid = bssid_match[1];
        RCLCPP_DEBUG(logger, "[UTILS] 解析BSSID: %s", info.bssid.c_str());
      }

      // 解析频率
      std::regex freq_regex("freq:\\s*(\\d+)");
      std::smatch freq_match;
      if (std::regex_search(iw_output, freq_match, freq_regex)) {
        info.frequency = std::stoi(freq_match[1]);
        RCLCPP_DEBUG(logger, "[UTILS] 解析频率: %dMHz", info.frequency);
      }

      // 解析信号强度
      std::regex signal_regex("signal:\\s*(-?\\d+)\\s*dBm");
      std::smatch signal_match;
      if (std::regex_search(iw_output, signal_match, signal_regex)) {
        info.signal_strength = std::stoi(signal_match[1]);
        RCLCPP_DEBUG(logger, "[UTILS] 解析信号强度: %ddBm", info.signal_strength);
      } else {
        // 备用方案：使用station dump获取信号强度
        RCLCPP_DEBUG(logger, "[UTILS] iw link中未找到信号强度，尝试station dump");
        std::string signal_output = exec_command("iw dev " + config.wifi_interface + " station dump 2>/dev/null | grep 'signal:'");
        std::regex station_signal_regex("signal:\\s*(-?\\d+)");
        std::smatch station_signal_match;
        if (std::regex_search(signal_output, station_signal_match, station_signal_regex)) {
          info.signal_strength = std::stoi(station_signal_match[1]);
          RCLCPP_DEBUG(logger, "[UTILS] 从station dump解析信号强度: %ddBm", info.signal_strength);
        }
      }

      // 获取安全类型 - 从wpa_supplicant状态获取
      std::string wpa_output = exec_command("wpa_cli status 2>/dev/null | grep key_mgmt");
      if (wpa_output.find("WPA2") != std::string::npos) {
        info.security_type = "WPA2";
      } else if (wpa_output.find("WPA3") != std::string::npos) {
        info.security_type = "WPA3";
      } else if (wpa_output.find("WPA") != std::string::npos) {
        info.security_type = "WPA";
      } else if (wpa_output.find("NONE") != std::string::npos) {
        info.security_type = "OPEN";
      } else {
        info.security_type = "WPA2"; // 默认假设
      }
      RCLCPP_DEBUG(logger, "[UTILS] 解析安全类型: %s", info.security_type.c_str());

      // 设置连接状态
      info.is_connected = !info.ssid.empty();

      if (info.is_connected) {
        RCLCPP_INFO(logger, "[UTILS] ✓ iw获取WiFi成功 - SSID: %s, BSSID: %s, 信号强度: %ddBm, 频率: %dMHz, 安全类型: %s",
          info.ssid.c_str(), info.bssid.c_str(), info.signal_strength, info.frequency, info.security_type.c_str());
        return info;
      }
    } else {
      RCLCPP_DEBUG(logger, "[UTILS] WiFi未连接或iw命令输出为空");
    }

    // 未连接或获取失败
    RCLCPP_DEBUG(logger, "[UTILS] 当前未连接WiFi或获取信息失败");
    info.is_connected = false;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[UTILS] 获取WiFi信息异常: %s", e.what());
    info.is_connected = false;
  }

  return info;
}

std::vector<NetworkUtils::WiFiInfo> NetworkUtils::scan_wifi_networks() {
  static auto logger = rclcpp::get_logger("network_utils");
  RCLCPP_DEBUG(logger, "[UTILS] 开始扫描WiFi网络");

  std::vector<WiFiInfo> networks;

  try {
    // 使用nmcli扫描WiFi网络
    RCLCPP_INFO(logger, "[UTILS] 使用nmcli执行WiFi扫描");

    // 1. 刷新WiFi扫描
    exec_command("nmcli device wifi rescan 2>/dev/null || true");

    // 等待扫描完成
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // 2. 获取扫描结果
    RCLCPP_INFO(logger, "[UTILS] 获取nmcli WiFi扫描结果");
    // 使用 LANG=C 确保输出格式一致
    std::string output = exec_command("LANG=C nmcli -t -f SSID,BSSID,SIGNAL,FREQ,SECURITY dev wifi list 2>/dev/null");

    if (!output.empty()) {
        // 解析nmcli结果
        std::istringstream iss(output);
        std::string line;

        while (std::getline(iss, line)) {
          if (line.empty()) continue;

          // nmcli输出格式: SSID:BSSID:SIGNAL:FREQ:SECURITY
          // 注意：BSSID中的冒号被转义为\:，需要特殊处理
          RCLCPP_DEBUG(logger, "[UTILS] 解析nmcli行: %s", line.c_str());

          // 使用正则表达式解析，处理BSSID中的转义冒号
          // 修改正则表达式以正确匹配完整的BSSID
          std::regex nmcli_line_regex(R"(([^:]+):([a-fA-F0-9]{2}(?:\\:[a-fA-F0-9]{2}){5}):(\d+):([^:]+):(.+))");
          std::smatch match;

          if (std::regex_search(line, match, nmcli_line_regex)) {
            WiFiInfo network;
            network.ssid = match[1].str();

            // 处理BSSID中的转义冒号
            std::string raw_bssid = match[2].str();
            network.bssid = raw_bssid;
            // 将转义的冒号 \: 替换为正常的冒号 :
            size_t pos = 0;
            while ((pos = network.bssid.find("\\:", pos)) != std::string::npos) {
              network.bssid.replace(pos, 2, ":");
              pos += 1; // 移动到替换后的冒号之后，避免重复处理
            }

            // 解析信号强度 - nmcli返回的是百分比，需要转换为dBm
            try {
              int percentage = std::stoi(match[3].str());
              network.signal_strength = convert_signal_percentage_to_dbm(percentage);
              RCLCPP_DEBUG(logger, "[UTILS] 信号强度转换: %d%% -> %ddBm", percentage, network.signal_strength);
            } catch (...) {
              network.signal_strength = -100;
            }

            // 解析频率 - 需要从 "5745 MHz" 格式中提取数字
            try {
              std::string freq_str = match[4].str();
              std::regex freq_regex(R"((\d+))");
              std::smatch freq_match;
              if (std::regex_search(freq_str, freq_match, freq_regex)) {
                network.frequency = std::stoi(freq_match[1].str());
              } else {
                network.frequency = 2400; // 默认值
              }
            } catch (...) {
              network.frequency = 2400;
            }

            // 解析安全类型
            network.security_type = match[5].str();
            if (network.security_type.empty()) {
              network.security_type = "OPEN";
            }

            networks.push_back(network);
            RCLCPP_DEBUG(logger, "[UTILS] nmcli添加网络: %s (%s) 信号: %ddBm, 频率: %dMHz",
              network.ssid.c_str(), network.bssid.c_str(), network.signal_strength, network.frequency);
          } else {
            RCLCPP_DEBUG(logger, "[UTILS] nmcli行解析失败: %s", line.c_str());
          }
        }

        RCLCPP_INFO(logger, "[UTILS] nmcli扫描完成，发现 %zu 个网络", networks.size());
    } else {
      RCLCPP_WARN(logger, "[UTILS] nmcli WiFi扫描结果为空");
    }

    // 获取当前连接的WiFi信息
    WiFiInfo current_wifi = get_current_wifi_info();

    // 标记当前连接的网络
    if (!current_wifi.ssid.empty()) {
      bool found_connected = false;
      for (auto & network : networks) {
        if (network.ssid == current_wifi.ssid) {
          network.is_connected = true;
          found_connected = true;
          RCLCPP_INFO(logger, "[UTILS] 标记已连接网络: %s", network.ssid.c_str());
          break; // 找到匹配的网络后立即退出循环
        }
      }

      // 如果没有找到匹配的网络，说明当前连接的网络不在扫描列表中
      if (!found_connected) {
        RCLCPP_DEBUG(logger, "[UTILS] 当前连接的网络 %s 不在扫描列表中", current_wifi.ssid.c_str());
      }
    } else {
      RCLCPP_DEBUG(logger, "[UTILS] 当前未连接任何WiFi网络");
    }

    RCLCPP_INFO(logger, "[UTILS] WiFi扫描完成，发现 %zu 个网络", networks.size());

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[UTILS] WiFi扫描失败: %s", e.what());
  }

  return networks;
}



bool NetworkUtils::connect_to_wifi(
  const std::string & ssid, const std::string & password, const std::string & security_type)
{
  static auto logger = rclcpp::get_logger("network_utils");
  try {
    RCLCPP_INFO(logger, "[WIFI] 使用nmcli连接到WiFi: %s", ssid.c_str());

    NetworkConfig config = get_network_config();

    // 1. 确保WiFi接口处于活跃状态
    std::string enable_cmd = "nmcli radio wifi on";
    exec_command(enable_cmd);

    // 2. 启用WiFi设备
    std::string device_up_cmd = "nmcli device set " + config.wifi_interface + " managed yes";
    exec_command(device_up_cmd);

    // 3. 检查是否已经连接到目标网络
    // 使用 LANG=C 强制英文输出，确保 active 字段显示为 yes/no
    std::string current_connection = exec_command("LANG=C nmcli -t -f active,ssid dev wifi | grep '^yes' | cut -d: -f2");
    if (!current_connection.empty() && current_connection.find(ssid) != std::string::npos) {
      RCLCPP_INFO(logger, "[WIFI] 已连接到目标网络: %s", ssid.c_str());
      return true;
    }

    // 4. 断开当前连接（如果有）
    std::string disconnect_cmd = "nmcli device disconnect " + config.wifi_interface + " 2>/dev/null || true";
    exec_command(disconnect_cmd);

    // 等待断开完成
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // 5. 构建连接命令
    std::string connect_cmd;

    if (security_type == "OPEN" || password.empty()) {
      // 开放网络
      connect_cmd = "nmcli device wifi connect \"" + ssid + "\"";
    } else {
      // 加密网络
      connect_cmd = "nmcli device wifi connect \"" + ssid + "\" password \"" + password + "\"";
    }

    RCLCPP_INFO(logger, "[WIFI] 执行连接命令...");

    // 6. 执行连接
    std::string result = exec_command(connect_cmd);

    // 7. 等待连接建立
    std::this_thread::sleep_for(std::chrono::seconds(5));

    // 8. 验证连接状态
    // 使用 LANG=C 强制英文输出，确保 active 字段显示为 yes/no
    std::string status_cmd = "LANG=C nmcli -t -f active,ssid dev wifi | grep '^yes'";
    std::string connection_status = exec_command(status_cmd);

    bool connected = !connection_status.empty() &&
                    connection_status.find(ssid) != std::string::npos;

    if (connected) {
      RCLCPP_INFO(logger, "[WIFI] 成功连接到WiFi: %s", ssid.c_str());

      // 9. 验证IP地址获取
      std::this_thread::sleep_for(std::chrono::seconds(3));
      std::string ip_cmd = "ip addr show " + config.wifi_interface + " | grep 'inet ' | awk '{print $2}' | cut -d/ -f1";
      std::string ip_address = exec_command(ip_cmd);

      if (!ip_address.empty()) {
        RCLCPP_INFO(logger, "[WIFI] 获得IP地址: %s", ip_address.c_str());
      } else {
        RCLCPP_WARN(logger, "[WIFI] 警告: 连接成功但未获得IP地址");
      }

      return true;
    } else {
      RCLCPP_ERROR(logger, "[WIFI] 连接失败: %s, 结果: %s", ssid.c_str(), result.c_str());
      return false;
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[WIFI] 连接WiFi异常: %s", e.what());
    return false;
  }
}

bool NetworkUtils::disconnect_wifi() {
  static auto logger = rclcpp::get_logger("network_utils");
  try {
    RCLCPP_INFO(logger, "[WIFI] 使用nmcli断开WiFi连接");

    NetworkConfig config = get_network_config();

    // 1. 断开WiFi设备连接
    std::string disconnect_cmd = "nmcli device disconnect " + config.wifi_interface;
    std::string result = exec_command(disconnect_cmd);

    // 2. 等待断开完成
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // 3. 验证断开状态
    // 使用 LANG=C 强制英文输出，确保 active 字段显示为 yes/no
    std::string status_cmd = "LANG=C nmcli -t -f active,ssid dev wifi | grep '^yes'";
    std::string connection_status = exec_command(status_cmd);

    bool disconnected = connection_status.empty();

    if (disconnected) {
      RCLCPP_INFO(logger, "[WIFI] WiFi连接已断开");
    } else {
      RCLCPP_WARN(logger, "[WIFI] 警告: WiFi可能未完全断开，当前状态: %s", connection_status.c_str());
    }

    return disconnected;

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[WIFI] 断开WiFi异常: %s", e.what());
    return false;
  }
}

std::vector<std::string> NetworkUtils::get_dns_servers() {
  std::vector<std::string> dns_servers;
  
  try {
    // 读取/etc/resolv.conf
    std::ifstream resolv_conf("/etc/resolv.conf");
    std::string line;
    std::regex dns_regex(R"(nameserver\s+(\S+))");
    
    while (std::getline(resolv_conf, line)) {
      std::smatch match;
      if (std::regex_search(line, match, dns_regex)) {
        dns_servers.push_back(match[1]);
      }
    }
    
  } catch (const std::exception & e) {
    static auto logger = rclcpp::get_logger("network_utils");
    RCLCPP_ERROR(logger, "[UTILS] 获取DNS服务器失败: %s", e.what());
  }
  
  return dns_servers;
}

// 信号强度转换函数：将百分比转换为dBm
int32_t NetworkUtils::convert_signal_percentage_to_dbm(int percentage) {
  // 限制百分比范围在 0-100 之间
  if (percentage < 0) percentage = 0;
  if (percentage > 100) percentage = 100;

  // WiFi 信号强度转换公式
  // 基于经验值：100% ≈ -30dBm, 0% ≈ -100dBm
  // 使用线性插值：dBm = -100 + (percentage * 0.7)
  // 这样：100% = -30dBm, 50% = -65dBm, 0% = -100dBm
  int32_t dbm = -100 + static_cast<int32_t>(percentage * 0.7);

  // 确保结果在合理范围内 (-100 到 -30 dBm)
  if (dbm > -30) dbm = -30;
  if (dbm < -100) dbm = -100;

  return dbm;
}

bool NetworkUtils::set_dns_servers(const std::vector<std::string> & dns_servers) {
  try {
    // 备份原始文件
    exec_command("cp /etc/resolv.conf /etc/resolv.conf.bak");
    
    // 写入新的DNS配置
    std::ofstream resolv_conf("/etc/resolv.conf");
    resolv_conf << "# Generated by Gen3 Network Manager\n";
    
    for (const auto & dns : dns_servers) {
      resolv_conf << "nameserver " << dns << "\n";
    }
    
    resolv_conf.close();
    return true;
    
  } catch (const std::exception & e) {
    static auto logger = rclcpp::get_logger("network_utils");
    RCLCPP_ERROR(logger, "[UTILS] 设置DNS服务器失败: %s", e.what());
    return false;
  }
}

bool NetworkUtils::test_connectivity(const std::string & host, int timeout_ms)
{
  // 构建ping命令
  std::string ping_cmd = "ping -c 1 -W " + std::to_string(timeout_ms / 1000) + " " + host + 
    " > /dev/null 2>&1";
  
  // 执行ping命令
  int result = system(ping_cmd.c_str());
  
  // 返回ping结果
  return (result == 0);
}

// 带时间测量的DNS解析测试（单域名）
std::tuple<bool, double> NetworkUtils::test_dns_resolution_with_time(const std::string & hostname, int timeout_ms)
{
  static auto logger = rclcpp::get_logger("network_utils");
  RCLCPP_DEBUG(logger, "[UTILS] 测试DNS解析时间: %s, 超时: %dms", hostname.c_str(), timeout_ms);

  auto start_time = std::chrono::steady_clock::now();

  try {
    // 获取地址信息
    struct addrinfo hints, * res;
    memset(&hints, 0, sizeof(hints));
    hints.ai_family = AF_UNSPEC;
    hints.ai_socktype = SOCK_STREAM;

    // 设置超时环境变量
    std::string timeout_str = std::to_string(timeout_ms / 1000);
    setenv("RESOLV_TIMEOUT", timeout_str.c_str(), 1);

    // 解析主机名
    int status = getaddrinfo(hostname.c_str(), nullptr, &hints, &res);

    // 恢复环境变量
    unsetenv("RESOLV_TIMEOUT");

    auto end_time = std::chrono::steady_clock::now();
    double resolution_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();

    if (status != 0) {
      RCLCPP_DEBUG(logger, "[UTILS] DNS解析失败: %s, 耗时: %.2fms", hostname.c_str(), resolution_time);
      return std::make_tuple(false, resolution_time);
    }

    // 释放资源
    freeaddrinfo(res);

    RCLCPP_DEBUG(logger, "[UTILS] DNS解析成功: %s, 耗时: %.2fms", hostname.c_str(), resolution_time);
    return std::make_tuple(true, resolution_time);

  } catch (const std::exception & e) {
    auto end_time = std::chrono::steady_clock::now();
    double resolution_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    RCLCPP_ERROR(logger, "[UTILS] DNS解析异常: %s, 耗时: %.2fms, 错误: %s",
                 hostname.c_str(), resolution_time, e.what());
    return std::make_tuple(false, resolution_time);
  }
}

// 带时间测量的DNS解析测试（多域名）
std::tuple<bool, double> NetworkUtils::test_dns_resolution_with_time(const std::vector<std::string> & domains, int timeout_ms)
{
  static auto logger = rclcpp::get_logger("network_utils");
  RCLCPP_DEBUG(logger, "[UTILS] 测试多域名DNS解析时间: %zu个域名, 超时: %dms", domains.size(), timeout_ms);

  if (domains.empty()) {
    RCLCPP_WARN(logger, "[UTILS] DNS解析测试失败: 域名列表为空");
    return std::make_tuple(false, 0.0);
  }

  std::vector<double> successful_resolution_times;

  for (const auto & domain : domains) {
    RCLCPP_DEBUG(logger, "[UTILS] 测试域名DNS解析: %s", domain.c_str());

    auto result = test_dns_resolution_with_time(domain, timeout_ms);
    bool success = std::get<0>(result);
    double resolution_time = std::get<1>(result);

    if (success) {
      successful_resolution_times.push_back(resolution_time);
      RCLCPP_DEBUG(logger, "[UTILS] 域名 %s DNS解析成功: %.2fms", domain.c_str(), resolution_time);
    } else {
      RCLCPP_DEBUG(logger, "[UTILS] 域名 %s DNS解析失败: %.2fms", domain.c_str(), resolution_time);
    }
  }

  // 计算成功解析的平均时间
  if (successful_resolution_times.empty()) {
    RCLCPP_WARN(logger, "[UTILS] 所有域名DNS解析都失败");
    return std::make_tuple(false, 0.0);
  }

  double total_time = 0.0;
  for (double time : successful_resolution_times) {
    total_time += time;
  }
  double avg_resolution_time = total_time / successful_resolution_times.size();

  RCLCPP_INFO(logger, "[UTILS] 多域名DNS解析测试完成: %zu/%zu个域名成功, 平均解析时间: %.2fms",
              successful_resolution_times.size(), domains.size(), avg_resolution_time);

  return std::make_tuple(true, avg_resolution_time);
}

// DNS解析测试（保留兼容性）
bool NetworkUtils::test_dns_resolution(const std::string & hostname, int timeout_ms)
{
  auto result = test_dns_resolution_with_time(hostname, timeout_ms);
  return std::get<0>(result);
}

// 多地址延迟测试（主要函数）
std::tuple<bool, double> NetworkUtils::test_latency(const std::vector<std::string> & hosts, int count) {
  static auto logger = rclcpp::get_logger("network_utils");
  RCLCPP_DEBUG(logger, "[UTILS] 测试多地址网络延迟: %zu个地址, 每个地址%d次ping", hosts.size(), count);

  if (hosts.empty()) {
    RCLCPP_WARN(logger, "[UTILS] 延迟测试失败: 地址列表为空");
    return std::make_tuple(false, 0.0);
  }

  std::vector<double> successful_latencies;

  for (const auto & host : hosts) {
    RCLCPP_DEBUG(logger, "[UTILS] 测试地址: %s", host.c_str());

    try {
      // 每个地址的超时时间：count*2+3秒（减少单个地址的等待时间）
      int host_timeout = count * 2 + 3;
      std::string cmd = "timeout " + std::to_string(host_timeout) + "s ping -c " + std::to_string(count) + " -W 2 " + host + " | grep 'avg'";
      RCLCPP_DEBUG(logger, "[UTILS] 执行ping命令: %s", cmd.c_str());

      std::string output = exec_command(cmd);

      // 解析ping结果中的平均延迟
      std::regex latency_regex(R"(= \d+\.\d+/(\d+\.\d+)/\d+\.\d+/\d+\.\d+)");
      std::smatch match;

      if (std::regex_search(output, match, latency_regex)) {
        double host_latency = std::stod(match[1]);
        successful_latencies.push_back(host_latency);
        RCLCPP_DEBUG(logger, "[UTILS] 地址 %s 延迟测试成功: %.2fms", host.c_str(), host_latency);
      } else {
        RCLCPP_DEBUG(logger, "[UTILS] 地址 %s 延迟测试失败: 无法解析结果", host.c_str());
      }

    } catch (const std::exception & e) {
      RCLCPP_WARN(logger, "[UTILS] 地址 %s 延迟测试异常: %s", host.c_str(), e.what());
    }
  }

  // 计算成功测试的平均延迟
  if (successful_latencies.empty()) {
    RCLCPP_WARN(logger, "[UTILS] 所有地址延迟测试都失败");
    return std::make_tuple(false, 0.0);
  }

  double total_latency = 0.0;
  for (double latency : successful_latencies) {
    total_latency += latency;
  }
  double avg_latency = total_latency / successful_latencies.size();

  RCLCPP_INFO(logger, "[UTILS] 多地址延迟测试完成: %zu/%zu个地址成功, 平均延迟: %.2fms",
              successful_latencies.size(), hosts.size(), avg_latency);

  return std::make_tuple(true, avg_latency);
}

// 单地址延迟测试（兼容性函数）
std::tuple<bool, double> NetworkUtils::test_latency(const std::string & host, int count) {
  return test_latency(std::vector<std::string>{host}, count);
}

// 整合的网络质量测试（主要函数）- 同时获取延迟和丢包率
std::tuple<bool, double, double> NetworkUtils::test_network_quality(const std::vector<std::string> & hosts, int count) {
  static auto logger = rclcpp::get_logger("network_utils");
  RCLCPP_DEBUG(logger, "[UTILS] 测试多地址网络质量: %zu个地址, 每个地址%d次ping", hosts.size(), count);

  if (hosts.empty()) {
    RCLCPP_WARN(logger, "[UTILS] 网络质量测试失败: 地址列表为空");
    return std::make_tuple(false, 0.0, 0.0);
  }

  std::vector<double> successful_latencies;
  std::vector<double> successful_packet_loss_rates;

  for (const auto & host : hosts) {
    RCLCPP_DEBUG(logger, "[UTILS] 测试地址网络质量: %s", host.c_str());

    try {
      // 每个地址的超时时间：count*2+5秒
      int host_timeout = count * 2 + 5;
      std::string cmd = "timeout " + std::to_string(host_timeout) + "s ping -c " + std::to_string(count) + " -W 2 " + host;
      RCLCPP_DEBUG(logger, "[UTILS] 执行ping命令: %s", cmd.c_str());

      std::string output = exec_command(cmd);

      bool latency_parsed = false;
      bool packet_loss_parsed = false;
      double host_latency = 0.0;
      double host_packet_loss = 0.0;

      // 解析延迟信息
      // ping输出格式: "rtt min/avg/max/mdev = 25.123/28.456/32.789/2.345 ms"
      std::regex latency_regex(R"(= \d+\.\d+/(\d+\.\d+)/\d+\.\d+/\d+\.\d+)");
      std::smatch latency_match;
      if (std::regex_search(output, latency_match, latency_regex)) {
        host_latency = std::stod(latency_match[1]);
        latency_parsed = true;
        RCLCPP_DEBUG(logger, "[UTILS] 地址 %s 延迟解析成功: %.2fms", host.c_str(), host_latency);
      }

      // 解析丢包率信息
      // ping输出格式: "5 packets transmitted, 4 received, 20% packet loss"
      std::regex packet_loss_regex(R"((\d+)% packet loss)");
      std::smatch packet_loss_match;
      if (std::regex_search(output, packet_loss_match, packet_loss_regex)) {
        host_packet_loss = std::stod(packet_loss_match[1]);
        packet_loss_parsed = true;
        RCLCPP_DEBUG(logger, "[UTILS] 地址 %s 丢包率解析成功: %.1f%%", host.c_str(), host_packet_loss);
      } else {
        // 备用解析方法：通过发送和接收包数量计算丢包率
        std::regex transmitted_regex(R"((\d+) packets transmitted)");
        std::regex received_regex(R"((\d+) received)");
        std::smatch transmitted_match, received_match;

        if (std::regex_search(output, transmitted_match, transmitted_regex) &&
            std::regex_search(output, received_match, received_regex)) {
          int transmitted = std::stoi(transmitted_match[1]);
          int received = std::stoi(received_match[1]);

          if (transmitted > 0) {
            host_packet_loss = ((double)(transmitted - received) / transmitted) * 100.0;
            packet_loss_parsed = true;
            RCLCPP_DEBUG(logger, "[UTILS] 地址 %s 丢包率计算成功: %d发送/%d接收 = %.1f%%",
                         host.c_str(), transmitted, received, host_packet_loss);
          }
        }
      }

      // 只有当两个指标都成功解析时才记录结果
      if (latency_parsed && packet_loss_parsed) {
        successful_latencies.push_back(host_latency);
        successful_packet_loss_rates.push_back(host_packet_loss);
        RCLCPP_DEBUG(logger, "[UTILS] 地址 %s 网络质量测试成功: 延迟=%.2fms, 丢包率=%.1f%%",
                     host.c_str(), host_latency, host_packet_loss);
      } else {
        RCLCPP_DEBUG(logger, "[UTILS] 地址 %s 网络质量测试失败: 延迟解析=%s, 丢包率解析=%s",
                     host.c_str(), latency_parsed ? "成功" : "失败", packet_loss_parsed ? "成功" : "失败");
      }

    } catch (const std::exception & e) {
      RCLCPP_WARN(logger, "[UTILS] 地址 %s 网络质量测试异常: %s", host.c_str(), e.what());
    }
  }

  // 计算成功测试的平均值
  if (successful_latencies.empty() || successful_packet_loss_rates.empty()) {
    RCLCPP_WARN(logger, "[UTILS] 所有地址网络质量测试都失败");
    return std::make_tuple(false, 0.0, 0.0);
  }

  // 计算平均延迟
  double total_latency = 0.0;
  for (double latency : successful_latencies) {
    total_latency += latency;
  }
  double avg_latency = total_latency / successful_latencies.size();

  // 计算平均丢包率
  double total_packet_loss = 0.0;
  for (double loss_rate : successful_packet_loss_rates) {
    total_packet_loss += loss_rate;
  }
  double avg_packet_loss = total_packet_loss / successful_packet_loss_rates.size();

  RCLCPP_INFO(logger, "[UTILS] 多地址网络质量测试完成: %zu/%zu个地址成功, 平均延迟: %.2fms, 平均丢包率: %.2f%%",
              successful_latencies.size(), hosts.size(), avg_latency, avg_packet_loss);

  return std::make_tuple(true, avg_latency, avg_packet_loss);
}

// 单地址网络质量测试（兼容性函数）
std::tuple<bool, double, double> NetworkUtils::test_network_quality(const std::string & host, int count) {
  return test_network_quality(std::vector<std::string>{host}, count);
}

// 注意：丢包率测试功能已整合到 test_network_quality 函数中

std::tuple<bool, double, double> NetworkUtils::test_network_speed(const std::string & url) {
  static auto logger = rclcpp::get_logger("network_utils");
  RCLCPP_DEBUG(logger, "[UTILS] 开始网络速度测试");
  return std::make_tuple(false, 0.0, 0.0);

  try {
    // 使用默认URL或指定URL
    std::string test_url = url.empty() ? "http://speedtest.tele2.net/10MB.zip" : url;
    RCLCPP_DEBUG(logger, "[UTILS] 测试URL: %s", test_url.c_str());

    // 测试下载速度，设置30秒超时和最大下载时间
    RCLCPP_DEBUG(logger, "[UTILS] 开始下载速度测试，超时设置: 连接10秒，总计30秒");
    std::string cmd = "timeout 30s curl -s -w '%{speed_download}\\n' -o /dev/null --connect-timeout 10 --max-time 30 --fail " + test_url;
    RCLCPP_DEBUG(logger, "[UTILS] 执行命令: %s", cmd.c_str());

    std::string output = exec_command(cmd);

    // 检查输出是否为空或无效
    if (output.empty()) {
      RCLCPP_WARN(logger, "[UTILS] curl命令返回空结果，可能是网络连接失败或超时");
      return std::make_tuple(false, 0.0, 0.0);
    }

    // 去除可能的换行符和空格
    output.erase(output.find_last_not_of(" \n\r\t") + 1);

    double download_speed_bytes = 0.0;
    try {
      download_speed_bytes = std::stod(output);
    } catch (const std::exception & e) {
      RCLCPP_ERROR(logger, "[UTILS] 解析下载速度失败，输出: '%s', 错误: %s", output.c_str(), e.what());
      return std::make_tuple(false, 0.0, 0.0);
    }

    // 转换为Mbps (bytes/s -> Mbps)
    double download_speed = download_speed_bytes / 1024.0 / 1024.0 * 8.0;

    // 检查速度是否合理 (0.1 Mbps 到 1000 Mbps)
    if (download_speed < 0.1 || download_speed > 1000.0) {
      RCLCPP_WARN(logger, "[UTILS] 下载速度异常: %.2fMbps，原始值: %.2f bytes/s",
        download_speed, download_speed_bytes);
      return std::make_tuple(false, 0.0, 0.0);
    }

    // 测试上传速度 (简化处理，实际应使用专门的测速服务)
    double upload_speed = download_speed * 0.2;  // 假设上传速度是下载速度的20%

    RCLCPP_DEBUG(logger, "[UTILS] 网络速度测试完成 - 下载: %.2fMbps, 上传: %.2fMbps",
      download_speed, upload_speed);
    return std::make_tuple(true, download_speed, upload_speed);

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[UTILS] 测试网络速度失败: %s", e.what());
    return std::make_tuple(false, 0.0, 0.0);
  }
}

bool NetworkUtils::set_interface_state(const std::string & interface_name, bool enable) {
  static auto logger = rclcpp::get_logger("network_utils");
  RCLCPP_DEBUG(logger, "[UTILS] 设置接口状态: %s -> %s",
    interface_name.c_str(), enable ? "启用" : "禁用");

  try {
    std::string cmd = "ip link set " + interface_name + (enable ? " up" : " down");
    RCLCPP_DEBUG(logger, "[UTILS] 执行命令: %s", cmd.c_str());
    int result = system(cmd.c_str());

    if (result == 0) {
      RCLCPP_DEBUG(logger, "[UTILS] 接口状态设置成功: %s", interface_name.c_str());
      return true;
    } else {
      RCLCPP_ERROR(logger, "[UTILS] 接口状态设置失败: %s, 返回码: %d",
        interface_name.c_str(), result);
      return false;
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[UTILS] 设置接口状态异常: %s", e.what());
    return false;
  }
}

bool NetworkUtils::set_default_route(const std::string & interface_name) {
  static auto logger = rclcpp::get_logger("network_utils");
  RCLCPP_DEBUG(logger, "[UTILS] 设置默认路由: %s", interface_name.c_str());

  try {
    // 获取接口信息
    InterfaceInfo info = get_interface_info(interface_name);

    if (info.gateway.empty()) {
      RCLCPP_ERROR(logger, "[UTILS] 接口 %s 没有网关", interface_name.c_str());
      return false;
    }

    RCLCPP_DEBUG(logger, "[UTILS] 接口网关: %s", info.gateway.c_str());

    // 检查接口是否已有默认路由
    if (has_default_route_for_interface(interface_name)) {
      RCLCPP_DEBUG(logger, "[UTILS] 接口 %s 已有默认路由，无需更改", interface_name.c_str());
      return true;
    }

    // 备份当前路由表
    std::vector<std::string> route_backup;
    if (!backup_routes(route_backup)) {
      RCLCPP_WARN(logger, "[UTILS] 路由备份失败，继续执行但无法回滚");
    }

    // 构建新的默认路由命令
    std::string new_route = "default via " + info.gateway + " dev " + interface_name;
    std::string add_cmd = "ip route add " + new_route;
    
    RCLCPP_DEBUG(logger, "[UTILS] 尝试添加新路由: %s", add_cmd.c_str());
    int add_result = system(add_cmd.c_str());

    if (add_result == 0) {
      // 新路由添加成功
      RCLCPP_DEBUG(logger, "[UTILS] 新默认路由添加成功: %s", new_route.c_str());
      
      // 删除其他的默认路由，保留刚添加的
      std::vector<std::string> current_routes = get_default_routes();
      for (const auto & route : current_routes) {
        if (route != new_route && route.find(new_route) == std::string::npos) {
          std::string del_cmd = "ip route del " + route + " 2>/dev/null";
          RCLCPP_DEBUG(logger, "[UTILS] 删除旧路由: %s", del_cmd.c_str());
          exec_command(del_cmd);
        }
      }
      
      RCLCPP_DEBUG(logger, "[UTILS] 默认路由设置成功: %s via %s",
        interface_name.c_str(), info.gateway.c_str());
      return true;
      
    } else {
      // 添加失败，可能是因为有冲突的默认路由
      RCLCPP_DEBUG(logger, "[UTILS] 直接添加失败，需要先清理现有路由");
      
      // 删除所有默认路由
      exec_command("ip route del default 2>/dev/null");
      
      // 立即添加新路由
      add_result = system(add_cmd.c_str());
      
      if (add_result == 0) {
        RCLCPP_DEBUG(logger, "[UTILS] 默认路由替换成功: %s via %s",
          interface_name.c_str(), info.gateway.c_str());
        return true;
      } else {
        RCLCPP_ERROR(logger, "[UTILS] 默认路由设置失败，尝试恢复原路由");
        
        // 恢复备份的路由
        if (!route_backup.empty()) {
          restore_routes(route_backup);
        }
        
        RCLCPP_ERROR(logger, "[UTILS] 默认路由设置失败，返回码: %d", add_result);
        return false;
      }
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[UTILS] 设置默认路由异常: %s", e.what());
    return false;
  }
}

std::string NetworkUtils::get_default_route_interface() {
  static auto logger = rclcpp::get_logger("network_utils");
  RCLCPP_DEBUG(logger, "[UTILS] 获取默认路由接口");

  try {
    std::string output = exec_command("ip route | grep default");
    std::regex interface_regex(R"(dev\s+(\S+))");
    std::smatch match;

    if (std::regex_search(output, match, interface_regex)) {
      std::string interface = match[1];
      RCLCPP_DEBUG(logger, "[UTILS] 默认路由接口: %s", interface.c_str());
      return interface;
    }

    RCLCPP_DEBUG(logger, "[UTILS] 未找到默认路由接口");
    return "";

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[UTILS] 获取默认路由接口失败: %s", e.what());
    return "";
  }
}

// 获取默认路由列表
std::vector<std::string> NetworkUtils::get_default_routes() {
  std::vector<std::string> routes;
  
  try {
    std::string output = exec_command("ip route show | grep default");
    std::istringstream iss(output);
    std::string line;
    
    while (std::getline(iss, line)) {
      routes.push_back(line);
    }
  } catch (const std::exception & e) {
    static auto logger = rclcpp::get_logger("network_utils");
    RCLCPP_ERROR(logger, "[UTILS] 获取默认路由失败: %s", e.what());
  }
  
  return routes;
}

// 检查接口是否有默认路由
bool NetworkUtils::has_default_route_for_interface(const std::string & interface_name) {
  try {
    std::string output = exec_command("ip route show | grep default | grep " + interface_name);
    return !output.empty();
  } catch (const std::exception & e) {
    static auto logger = rclcpp::get_logger("network_utils");
    RCLCPP_ERROR(logger, "[UTILS] 检查接口默认路由失败: %s", e.what());
    return false;
  }
}

// 备份路由表
bool NetworkUtils::backup_routes(std::vector<std::string> & backup) {
  try {
    backup = get_default_routes();
    return true;
  } catch (const std::exception & e) {
    static auto logger = rclcpp::get_logger("network_utils");
    RCLCPP_ERROR(logger, "[UTILS] 备份路由表失败: %s", e.what());
    return false;
  }
}

// 恢复路由表
bool NetworkUtils::restore_routes(const std::vector<std::string> & backup) {
  try {
    // 先删除所有默认路由
    exec_command("ip route del default");
    
    // 恢复备份的路由
    for (const auto & route : backup) {
      exec_command("ip route add " + route);
    }
    
    return true;
  } catch (const std::exception & e) {
    static auto logger = rclcpp::get_logger("network_utils");
    RCLCPP_ERROR(logger, "[UTILS] 恢复路由表失败: %s", e.what());
    return false;
  }
}

// 获取5G网络信息
NetworkUtils::FiveGInfo NetworkUtils::get_5g_network_info(const std::string & device_path) {
  FiveGInfo info;
  info.device_path = device_path;
  info.is_connected = false;
  
  try {
    // 这里是简化的实现，实际应该调用AT命令获取5G模块信息
    std::string output = exec_command("echo 'AT+COPS?' > " + device_path + " && cat " + device_path);
    
    // 解析运营商信息
    if (output.find("CHINA MOBILE") != std::string::npos) {
      info.operator_name = "CHINA MOBILE";
    } else if (output.find("CHINA UNICOM") != std::string::npos) {
      info.operator_name = "CHINA UNICOM";
    } else if (output.find("CHINA TELECOM") != std::string::npos) {
      info.operator_name = "CHINA TELECOM";
    } else {
      info.operator_name = "UNKNOWN";
    }
    
    // 获取网络类型
    output = exec_command("echo 'AT+QNWINFO' > " + device_path + " && cat " + device_path);
    if (output.find("5G") != std::string::npos) {
      info.network_type = "5G";
    } else if (output.find("LTE") != std::string::npos) {
      info.network_type = "4G";
    } else if (output.find("WCDMA") != std::string::npos) {
      info.network_type = "3G";
    } else {
      info.network_type = "UNKNOWN";
    }
    
    // 获取信号强度
    output = exec_command("echo 'AT+CSQ' > " + device_path + " && cat " + device_path);
    std::regex csq_regex(R"(\+CSQ:\s+(\d+),\s*(\d+))");
    std::smatch csq_match;
    if (std::regex_search(output, csq_match, csq_regex) && csq_match.size() > 1) {
      int csq = std::stoi(csq_match[1]);
      if (csq < 99) {
        info.signal_strength = -113 + 2 * csq;  // 转换为dBm
        info.signal_quality = csq * 100 / 31;   // 转换为百分比
      }
    }
    
    // 检查是否已连接
    info.is_connected = test_5g_connectivity(device_path);
    
  } catch (const std::exception & e) {
    static auto logger = rclcpp::get_logger("network_utils");
    RCLCPP_ERROR(logger, "[UTILS] 获取5G网络信息失败: %s", e.what());
  }
  
  return info;
}

// 测试5G连接
bool NetworkUtils::test_5g_connectivity(const std::string & device_path) {
  try {
    // 简化实现，实际应该检查5G模块的连接状态
    std::string output = exec_command("ip addr show | grep usb");
    if (output.find("inet ") != std::string::npos) {
      return true;
    }
    
    // 使用device_path参数避免未使用警告
    if (!device_path.empty()) {
      // 检查设备路径是否存在
      output = exec_command("test -e " + device_path + " && echo 'exists'");
      if (output.find("exists") != std::string::npos) {
        // 如果设备存在，可以进一步检查
        output = exec_command("echo 'AT+CGATT?' > " + device_path + " 2>/dev/null && cat " + device_path + " 2>/dev/null");
        if (output.find("+CGATT: 1") != std::string::npos) {
          return true;
        }
      }
    }
  } catch (const std::exception & e) {
    static auto logger = rclcpp::get_logger("network_utils");
    RCLCPP_ERROR(logger, "[UTILS] 测试5G连接失败: %s", e.what());
  }
  
  return false;
}

// 获取5G信号强度
int32_t NetworkUtils::get_5g_signal_strength(const std::string & device_path) {
  try {
    // 简化实现，直接调用get_5g_network_info获取信息
    auto info = get_5g_network_info(device_path);
    return info.signal_strength;
  } catch (const std::exception & e) {
    static auto logger = rclcpp::get_logger("network_utils");
    RCLCPP_ERROR(logger, "[UTILS] 获取5G信号强度失败: %s", e.what());
    return -999;  // 无效值
  }
}

// 检查是否为5G接口
bool NetworkUtils::is_5g_interface(const std::string & interface_name) {
  // 检查接口名称是否符合5G接口的特征
  return interface_name.find("usb") != std::string::npos || 
         interface_name.find("wwan") != std::string::npos ||
         interface_name.find("rmnet") != std::string::npos;
}

// 设置路由跃点值
bool NetworkUtils::set_route_metric(const std::string & interface_name, int metric) {
  try {
    // 获取当前接口的网关
    InterfaceInfo info = get_interface_info(interface_name);
    static auto logger = rclcpp::get_logger("network_utils");
    if (info.gateway.empty()) {
      RCLCPP_ERROR(logger, "[UTILS] 接口 %s 没有网关", interface_name.c_str());
      return false;
    }

    // 删除现有的默认路由
    std::string del_cmd = "ip route del default dev " + interface_name + " 2>/dev/null";
    exec_command(del_cmd);

    // 添加带跃点值的默认路由
    std::string add_cmd = "ip route add default via " + info.gateway + " dev " + interface_name + " metric " + std::to_string(metric);
    int result = system(add_cmd.c_str());

    if (result == 0) {
      RCLCPP_DEBUG(logger, "[UTILS] 设置路由跃点值成功: %s metric=%d", interface_name.c_str(), metric);
      return true;
    } else {
      RCLCPP_ERROR(logger, "[UTILS] 设置路由跃点值失败: %s", interface_name.c_str());
      return false;
    }

  } catch (const std::exception & e) {
    static auto logger = rclcpp::get_logger("network_utils");
    RCLCPP_ERROR(logger, "[UTILS] 设置路由跃点值异常: %s", e.what());
    return false;
  }
}

// 更新路由优先级
bool NetworkUtils::update_route_priority(const std::string & interface_name, int priority)
{
  static auto logger = rclcpp::get_logger("network_utils");
  try {
    RCLCPP_DEBUG(logger, "[UTILS] 更新路由优先级: %s, 优先级: %d", interface_name.c_str(), priority);
    
    // 获取所有默认路由
    std::vector<std::string> routes = get_default_routes();
    
    // 检查是否有目标接口的路由
    std::vector<std::string> target_routes;
    std::vector<std::string> other_routes;
    
    for (const auto & route : routes) {
      if (route.find(interface_name) != std::string::npos) {
        target_routes.push_back(route);
      } else {
        other_routes.push_back(route);
      }
    }
    
    // 情况4: 如果需要设置的网卡没有默认路由表且是无线网卡，就重新连接直到获取IP地址
    bool is_wireless = interface_name.find("wlan") != std::string::npos || 
                       interface_name.find("wlp") != std::string::npos;
    
    if (target_routes.empty() && is_wireless) {
      RCLCPP_DEBUG(logger, "[UTILS] 无线网卡 %s 没有默认路由，尝试重新连接", interface_name.c_str());
      
      // 使用 nmcli 获取当前连接的 WiFi 信息
      std::string nmcli_output = exec_command("nmcli -t -f NAME,DEVICE con show --active | grep " + interface_name);
      std::string connection_name;
      
      if (!nmcli_output.empty()) {
        // 解析连接名称，格式通常是 "连接名称:设备名"
        size_t colon_pos = nmcli_output.find(':');
        if (colon_pos != std::string::npos) {
          connection_name = nmcli_output.substr(0, colon_pos);
          // 去除可能的换行符
          connection_name.erase(connection_name.find_last_not_of("\r\n") + 1);
          RCLCPP_DEBUG(logger, "[UTILS] 当前WiFi连接名称: %s", connection_name.c_str());
        }
      }
      
      if (connection_name.empty()) {
        RCLCPP_DEBUG(logger, "[UTILS] 未找到活动的WiFi连接，尝试获取可用连接");
        // 尝试获取该接口的任何可用连接
        nmcli_output = exec_command("nmcli -t -f NAME,DEVICE con show | grep " + interface_name + " | head -1");
        if (!nmcli_output.empty()) {
          size_t colon_pos = nmcli_output.find(':');
          if (colon_pos != std::string::npos) {
            connection_name = nmcli_output.substr(0, colon_pos);
            connection_name.erase(connection_name.find_last_not_of("\r\n") + 1);
            RCLCPP_DEBUG(logger, "[UTILS] 找到可用WiFi连接: %s", connection_name.c_str());
          }
        }
      }
      
      if (!connection_name.empty()) {
        // 1. 先关闭接口
        RCLCPP_DEBUG(logger, "[UTILS] 关闭WiFi接口 %s", interface_name.c_str());
        exec_command("ip link set " + interface_name + " down");
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 2. 重新启用接口
        RCLCPP_DEBUG(logger, "[UTILS] 重新启用WiFi接口 %s", interface_name.c_str());
        exec_command("ip link set " + interface_name + " up");
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 3. 使用 nmcli 重新连接 WiFi
        RCLCPP_DEBUG(logger, "[UTILS] 使用nmcli重新连接WiFi: %s", connection_name.c_str());
        exec_command("nmcli connection up \"" + connection_name + "\"");
        
        // 4. 等待获取IP地址，最多等待30秒
        bool got_ip = wait_for_ip_address(interface_name, 30);
        if (!got_ip) {
          RCLCPP_ERROR(logger, "[UTILS] 无线网卡 %s 重新连接后仍未获取IP地址", interface_name.c_str());
          
          // 尝试使用 dhclient 手动获取 IP
          RCLCPP_DEBUG(logger, "[UTILS] 尝试使用dhclient手动获取IP");
          exec_command("dhclient -v " + interface_name);
          
          // 再次检查是否获取到IP
          got_ip = wait_for_ip_address(interface_name, 10);
          if (!got_ip) {
            RCLCPP_ERROR(logger, "[UTILS] 手动获取IP失败，无法继续");
            return false;
          }
        }
      } else {
        // 如果找不到连接名称，使用传统方法
        RCLCPP_DEBUG(logger, "[UTILS] 未找到WiFi连接名称，使用传统方法重连");
        
        // 1. 先关闭接口
        exec_command("ip link set " + interface_name + " down");
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 2. 重新启用接口
        exec_command("ip link set " + interface_name + " up");
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 3. 重新连接WiFi (使用wpa_cli重新连接)
        exec_command("wpa_cli -i " + interface_name + " reconnect");
        
        // 4. 等待获取IP地址，最多等待30秒
        bool got_ip = wait_for_ip_address(interface_name, 30);
        if (!got_ip) {
          RCLCPP_ERROR(logger, "[UTILS] 无线网卡 %s 重新连接后仍未获取IP地址", interface_name.c_str());
          return false;
        }
      }
      
      // 5. 重新获取路由信息
      routes = get_default_routes();
      target_routes.clear();
      other_routes.clear();
      
      for (const auto & route : routes) {
        if (route.find(interface_name) != std::string::npos) {
          target_routes.push_back(route);
        } else {
          other_routes.push_back(route);
        }
      }
      
      // 如果重新连接后仍没有路由，尝试手动添加默认路由
      if (target_routes.empty()) {
        RCLCPP_DEBUG(logger, "[UTILS] 重新连接后仍无默认路由，尝试手动添加");
        
        // 获取接口信息以获取网关
        InterfaceInfo info = get_interface_info(interface_name);
        if (info.gateway.empty()) {
          // 如果没有网关信息，尝试从DHCP租约文件获取
          std::string gateway = exec_command("grep routers /var/lib/dhcp/dhclient." + interface_name + ".leases | tail -1 | awk '{print $3}' | tr -d ';'");
          if (gateway.empty()) {
            // 尝试从NetworkManager获取网关信息
            gateway = exec_command("nmcli -t -f IP4.GATEWAY con show --active | grep : | cut -d: -f2");
            if (gateway.empty()) {
              RCLCPP_ERROR(logger, "[UTILS] 无法获取无线网卡网关信息");
              return false;
            }
          }
          info.gateway = gateway;
        }
        
        // 使用 add 命令添加默认路由，而不是 replace 命令，避免覆盖其他接口的路由
        std::string add_cmd = "ip route add default via " + info.gateway + " dev " + interface_name + " metric " + std::to_string(priority);
        std::string result = exec_command(add_cmd);
        RCLCPP_DEBUG(logger, "[UTILS] 添加默认路由: %s", add_cmd.c_str());
        
        if (!result.empty() && result.find("Error") != std::string::npos) {
          RCLCPP_ERROR(logger, "[UTILS] 添加路由失败: %s", result.c_str());
          
          // 如果添加失败，尝试先删除特定接口的默认路由，然后添加新路由
          RCLCPP_DEBUG(logger, "[UTILS] 尝试删除特定接口的默认路由后重新添加");
          exec_command("ip route del default dev " + interface_name + " 2>/dev/null");
          
          add_cmd = "ip route add default via " + info.gateway + " dev " + interface_name + " metric " + std::to_string(priority);
          result = exec_command(add_cmd);
          RCLCPP_DEBUG(logger, "[UTILS] 添加新默认路由: %s", add_cmd.c_str());
        }
        
        // 更新目标路由列表
        routes = get_default_routes();
        for (const auto & route : routes) {
          if (route.find(interface_name) != std::string::npos) {
            target_routes.push_back(route);
            break;  // 只需要找到一条
          }
        }
      }
    }
    
    // 情况1: 如果默认静态路由表只有一条且是需要设置的网卡，不需要更新优先级
    if (routes.size() == 1 && target_routes.size() == 1) {
      RCLCPP_DEBUG(logger, "[UTILS] 默认路由表只有一条且是目标网卡，无需更新优先级");
      return true;
    }
    
    // 情况3: 如果存在多条相同网卡的默认路由，只保留一条
    if (target_routes.size() > 1) {
      RCLCPP_DEBUG(logger, "[UTILS] 发现 %zu 条相同网卡的默认路由，只保留一条", target_routes.size());
      
      // 删除除第一条外的所有目标网卡路由
      for (size_t i = 1; i < target_routes.size(); ++i) {
        std::string del_cmd = "ip route del " + target_routes[i];
        exec_command(del_cmd);
        RCLCPP_DEBUG(logger, "[UTILS] 删除重复路由: %s", target_routes[i].c_str());
      }
      
      // 只保留第一条路由
      target_routes.resize(1);
    }
    
    // 情况2: 如果存在多条路由，将最高优先级设置为需要设置的网卡
    if (!other_routes.empty() || (target_routes.size() == 1 && target_routes[0].find("metric") == std::string::npos)) {
      // 获取接口信息以获取网关
      InterfaceInfo info = get_interface_info(interface_name);
      if (info.gateway.empty()) {
        RCLCPP_ERROR(logger, "[UTILS] 接口 %s 没有网关", interface_name.c_str());
        return false;
      }
      
      // 删除当前目标接口的默认路由
      if (!target_routes.empty()) {
        std::string del_cmd = "ip route del " + target_routes[0];
        exec_command(del_cmd);
        RCLCPP_DEBUG(logger, "[UTILS] 删除当前路由: %s", target_routes[0].c_str());
      }
      
      // 添加带优先级的新路由，使用 add 命令而不是 replace 命令，避免覆盖其他接口的路由
      std::string add_cmd = "ip route add default via " + info.gateway + " dev " + interface_name + " metric " + std::to_string(priority);
      std::string result = exec_command(add_cmd);
      RCLCPP_DEBUG(logger, "[UTILS] 添加默认路由: %s", add_cmd.c_str());
      
      if (!result.empty() && result.find("Error") != std::string::npos) {
        RCLCPP_ERROR(logger, "[UTILS] 添加路由失败: %s", result.c_str());
        
        // 如果添加失败，可能是因为已经存在相同的路由，尝试先删除特定接口的默认路由，然后再添加
        RCLCPP_DEBUG(logger, "[UTILS] 尝试删除特定接口的默认路由后重新添加");
        exec_command("ip route del default dev " + interface_name + " 2>/dev/null");
        
        add_cmd = "ip route add default via " + info.gateway + " dev " + interface_name + " metric " + std::to_string(priority);
        result = exec_command(add_cmd);
        RCLCPP_DEBUG(logger, "[UTILS] 添加新默认路由: %s", add_cmd.c_str());
        
        if (!result.empty() && result.find("Error") != std::string::npos) {
          RCLCPP_ERROR(logger, "[UTILS] 添加路由仍然失败: %s", result.c_str());
          return false;
        }
      }
    }
    
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[UTILS] 更新路由优先级异常: %s", e.what());
    return false;
  }
}

// 设置WiFi路由优先级（最小跃点值）
bool NetworkUtils::set_wifi_route_priority(const std::string & wifi_interface) {
  static auto logger = rclcpp::get_logger("network_utils");
  try {
    RCLCPP_DEBUG(logger, "[UTILS] 设置WiFi路由为最高优先级: %s", wifi_interface.c_str());

    // 获取所有默认路由
    std::vector<std::string> routes = get_default_routes();
    
    // 删除WiFi相关的路由
    for (const auto & route : routes) {
      if (route.find(wifi_interface) != std::string::npos) {
        std::string del_cmd = "ip route del " + route + " 2>/dev/null";
        exec_command(del_cmd);
      }
    }

    // 为WiFi接口设置最小跃点值（优先级最高）
    return set_route_metric(wifi_interface, 100);

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[UTILS] 设置WiFi路由优先级异常: %s", e.what());
    return false;
  }
}

// 清理重复的WiFi路由
bool NetworkUtils::clean_duplicate_wifi_routes(const std::string & wifi_interface) {
  static auto logger = rclcpp::get_logger("network_utils");
  try {
    RCLCPP_DEBUG(logger, "[UTILS] 清理重复的WiFi路由: %s", wifi_interface.c_str());

    // 获取所有默认路由
    std::vector<std::string> routes = get_default_routes();
    std::vector<std::string> wifi_routes;

    // 找到所有WiFi相关的路由
    for (const auto & route : routes) {
      if (route.find(wifi_interface) != std::string::npos) {
        wifi_routes.push_back(route);
      }
    }

    // 如果有多个WiFi路由，删除除第一个外的所有路由
    if (wifi_routes.size() > 1) {
      RCLCPP_DEBUG(logger, "[UTILS] 发现 %zu 个重复的WiFi路由，开始清理", wifi_routes.size());
      
      for (size_t i = 1; i < wifi_routes.size(); ++i) {
        std::string del_cmd = "ip route del " + wifi_routes[i] + " 2>/dev/null";
        exec_command(del_cmd);
        RCLCPP_DEBUG(logger, "[UTILS] 删除重复路由: %s", wifi_routes[i].c_str());
      }
      return true;
    } else {
      RCLCPP_DEBUG(logger, "[UTILS] 没有发现重复的WiFi路由");
      return true;
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[UTILS] 清理重复WiFi路由异常: %s", e.what());
    return false;
  }
}

// 等待接口获取IP地址
bool NetworkUtils::wait_for_ip_address(const std::string & interface_name, int timeout_seconds) {
  static auto logger = rclcpp::get_logger("network_utils");
  RCLCPP_DEBUG(logger, "[UTILS] 等待接口 %s 获取IP地址，超时: %d秒", interface_name.c_str(), timeout_seconds);

  auto start_time = std::chrono::steady_clock::now();
  auto timeout = std::chrono::seconds(timeout_seconds);

  while (true) {
    try {
      // 检查接口是否有IP地址
      std::string ip_cmd = "ip addr show " + interface_name + " | grep 'inet ' | awk '{print $2}' | cut -d/ -f1";
      std::string ip_address = exec_command(ip_cmd);
      
      // 去除换行符
      ip_address.erase(ip_address.find_last_not_of(" \n\r\t") + 1);

      if (!ip_address.empty()) {
        RCLCPP_DEBUG(logger, "[UTILS] 接口 %s 获得IP地址: %s", interface_name.c_str(), ip_address.c_str());
        return true;
      }

      // 检查超时
      auto elapsed = std::chrono::steady_clock::now() - start_time;
      if (elapsed >= timeout) {
        RCLCPP_ERROR(logger, "[UTILS] 等待IP地址超时: %s", interface_name.c_str());
        return false;
      }

      // 等待1秒后重试
      std::this_thread::sleep_for(std::chrono::seconds(1));

    } catch (const std::exception & e) {
      RCLCPP_ERROR(logger, "[UTILS] 检查IP地址异常: %s", e.what());
      return false;
    }
  }
}

// 智能路由管理
bool NetworkUtils::manage_route_intelligently(const std::string & target_interface, uint8_t network_type) {
  static auto logger = rclcpp::get_logger("network_utils");
  try {
    RCLCPP_DEBUG(logger, "[UTILS] 开始智能路由管理: %s, 网络类型: %d", target_interface.c_str(), (int)network_type);

    // 获取当前所有默认路由
    std::vector<std::string> current_routes = get_default_routes();
    
    // 如果当前默认路由表只有目标接口相关的路由，不需要更新
    bool has_only_target_routes = true;
    bool has_target_route = false;
    
    for (const auto & route : current_routes) {
      if (route.find(target_interface) != std::string::npos) {
        has_target_route = true;
      } else {
        has_only_target_routes = false;
      }
    }

    if (has_only_target_routes && has_target_route) {
      RCLCPP_DEBUG(logger, "[UTILS] 当前路由表只有目标接口的路由，无需更新");
      return true;
    }

    // 如果是WiFi网络类型
    if (network_type == gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI) {
      // 等待WiFi接口获取IP地址
      if (!wait_for_ip_address(target_interface, 30)) {
        RCLCPP_ERROR(logger, "[UTILS] WiFi接口未获取到IP地址");
        return false;
      }

      // 检查是否存在WiFi默认路由
      if (!has_default_route_for_interface(target_interface)) {
        RCLCPP_DEBUG(logger, "[UTILS] 不存在WiFi默认路由，重新发起WiFi连接");
        // 这里应该调用WiFi管理器重新连接
        // 暂时先设置默认路由
        if (!set_default_route(target_interface)) {
          return false;
        }
      }

      // 如果存在多个路由，设置WiFi路由的跃点值为最小
      if (current_routes.size() > 1) {
        RCLCPP_DEBUG(logger, "[UTILS] 存在多个路由，设置WiFi路由优先级最高");
        return set_wifi_route_priority(target_interface);
      }

      // 清理重复的WiFi路由
      clean_duplicate_wifi_routes(target_interface);
      
      return true;
    } else {
      // 非WiFi网络，直接设置默认路由
      return set_default_route(target_interface);
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[UTILS] 智能路由管理异常: %s", e.what());
    return false;
  }
}

}  // namespace utils
}  // namespace gen3_network_manager_core