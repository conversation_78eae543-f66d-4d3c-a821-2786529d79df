#include "gen3_network_manager_core/utils/iptables_utils.hpp"
#include <rclcpp/rclcpp.hpp>
#include <sstream>
#include <fstream>
#include <algorithm>

namespace gen3_network_manager_core {
namespace utils {

std::string IptablesUtils::exec_command(const std::string & cmd) {
  static auto logger = rclcpp::get_logger("iptables_utils");
  RCLCPP_DEBUG(logger, "[IPTABLES] 执行命令: %s", cmd.c_str());
  
  std::string result;
  FILE* pipe = popen(cmd.c_str(), "r");
  if (!pipe) {
    RCLCPP_ERROR(logger, "[IPTABLES] 无法执行命令: %s", cmd.c_str());
    return result;
  }
  
  char buffer[128];
  while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
    result += buffer;
  }
  
  int return_code = pclose(pipe);
  if (return_code != 0) {
    RCLCPP_WARN(logger, "[IPTABLES] 命令执行返回码: %d, 命令: %s", return_code, cmd.c_str());
  }
  
  // 移除末尾的换行符
  if (!result.empty() && result.back() == '\n') {
    result.pop_back();
  }
  
  return result;
}

bool IptablesUtils::setup_nat_for_interface(const std::string & wan_interface, 
                                           const std::vector<std::string> & lan_interfaces) {
  static auto logger = rclcpp::get_logger("iptables_utils");
  RCLCPP_INFO(logger, "[IPTABLES] 为接口 %s 设置NAT规则", wan_interface.c_str());
  
  try {
    // 1. 清理旧的NAT规则（避免重复规则）
    cleanup_nat_rules();
    
    // 2. 启用IP转发
    if (!enable_ip_forwarding()) {
      RCLCPP_ERROR(logger, "[IPTABLES] 启用IP转发失败");
      return false;
    }
    
    // 3. 设置MASQUERADE规则（源NAT）
    std::string masquerade_cmd = "iptables -t nat -A POSTROUTING -o " + wan_interface + " -j MASQUERADE";
    std::string result = exec_command(masquerade_cmd);
    
    if (!result.empty() && result.find("iptables:") != std::string::npos) {
      RCLCPP_ERROR(logger, "[IPTABLES] 设置MASQUERADE规则失败: %s", result.c_str());
      return false;
    }
    
    // 4. 设置FORWARD规则允许内网到外网的流量
    for (const auto & lan_interface : lan_interfaces) {
      // 允许从内网接口到外网接口的流量
      std::string forward_out_cmd = "iptables -A FORWARD -i " + lan_interface + " -o " + wan_interface + " -j ACCEPT";
      result = exec_command(forward_out_cmd);
      
      if (!result.empty() && result.find("iptables:") != std::string::npos) {
        RCLCPP_WARN(logger, "[IPTABLES] 设置FORWARD规则失败: %s", result.c_str());
      }
      
      // 允许已建立连接的返回流量
      std::string forward_in_cmd = "iptables -A FORWARD -i " + wan_interface + " -o " + lan_interface + 
                                  " -m state --state RELATED,ESTABLISHED -j ACCEPT";
      result = exec_command(forward_in_cmd);
      
      if (!result.empty() && result.find("iptables:") != std::string::npos) {
        RCLCPP_WARN(logger, "[IPTABLES] 设置FORWARD返回规则失败: %s", result.c_str());
      }
      
      RCLCPP_INFO(logger, "[IPTABLES] 为内网接口 %s 设置转发规则完成", lan_interface.c_str());
    }
    
    // 5. 保存iptables规则（如果系统支持）
    save_iptables_rules();
    
    RCLCPP_INFO(logger, "[IPTABLES] NAT规则设置完成 - 外网接口: %s", wan_interface.c_str());
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[IPTABLES] 设置NAT规则异常: %s", e.what());
    return false;
  }
}

bool IptablesUtils::cleanup_nat_rules() {
  static auto logger = rclcpp::get_logger("iptables_utils");
  RCLCPP_DEBUG(logger, "[IPTABLES] 清理旧的NAT规则");
  
  try {
    // 清理POSTROUTING链中的MASQUERADE规则
    std::string cleanup_cmd = "iptables -t nat -F POSTROUTING 2>/dev/null || true";
    exec_command(cleanup_cmd);
    
    // 清理FORWARD链中的相关规则
    cleanup_cmd = "iptables -F FORWARD 2>/dev/null || true";
    exec_command(cleanup_cmd);
    
    RCLCPP_DEBUG(logger, "[IPTABLES] NAT规则清理完成");
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[IPTABLES] 清理NAT规则异常: %s", e.what());
    return false;
  }
}

bool IptablesUtils::enable_ip_forwarding() {
  static auto logger = rclcpp::get_logger("iptables_utils");
  RCLCPP_DEBUG(logger, "[IPTABLES] 启用IP转发");
  
  try {
    // 临时启用IP转发
    std::string enable_cmd = "echo 1 > /proc/sys/net/ipv4/ip_forward";
    std::string result = exec_command(enable_cmd);
    
    // 永久启用IP转发（修改sysctl配置）
    std::string sysctl_cmd = "sysctl -w net.ipv4.ip_forward=1 2>/dev/null || true";
    exec_command(sysctl_cmd);
    
    // 验证IP转发是否已启用
    std::string check_cmd = "cat /proc/sys/net/ipv4/ip_forward";
    std::string forward_status = exec_command(check_cmd);
    
    if (forward_status == "1") {
      RCLCPP_DEBUG(logger, "[IPTABLES] IP转发已启用");
      return true;
    } else {
      RCLCPP_ERROR(logger, "[IPTABLES] IP转发启用失败，当前状态: %s", forward_status.c_str());
      return false;
    }
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[IPTABLES] 启用IP转发异常: %s", e.what());
    return false;
  }
}

bool IptablesUtils::save_iptables_rules() {
  static auto logger = rclcpp::get_logger("iptables_utils");
  RCLCPP_DEBUG(logger, "[IPTABLES] 保存iptables规则");
  
  try {
    // 尝试不同的保存命令（不同系统可能使用不同的命令）
    std::vector<std::string> save_commands = {
      "iptables-save > /etc/iptables/rules.v4 2>/dev/null",
      "service iptables save 2>/dev/null",
      "netfilter-persistent save 2>/dev/null"
    };
    
    bool saved = false;
    for (const auto & cmd : save_commands) {
      std::string result = exec_command(cmd);
      if (result.empty() || result.find("command not found") == std::string::npos) {
        RCLCPP_DEBUG(logger, "[IPTABLES] 使用命令保存规则: %s", cmd.c_str());
        saved = true;
        break;
      }
    }
    
    if (!saved) {
      RCLCPP_WARN(logger, "[IPTABLES] 无法找到合适的保存命令，规则仅在当前会话有效");
    }
    
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[IPTABLES] 保存iptables规则异常: %s", e.what());
    return false;
  }
}

std::vector<std::string> IptablesUtils::get_current_nat_rules() {
  static auto logger = rclcpp::get_logger("iptables_utils");
  std::vector<std::string> rules;
  
  try {
    // 获取NAT表的POSTROUTING链规则
    std::string cmd = "iptables -t nat -L POSTROUTING -n --line-numbers";
    std::string output = exec_command(cmd);
    
    if (!output.empty()) {
      std::istringstream iss(output);
      std::string line;
      while (std::getline(iss, line)) {
        if (line.find("MASQUERADE") != std::string::npos) {
          rules.push_back(line);
        }
      }
    }
    
    RCLCPP_DEBUG(logger, "[IPTABLES] 获取到 %zu 条NAT规则", rules.size());
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[IPTABLES] 获取NAT规则异常: %s", e.what());
  }
  
  return rules;
}

bool IptablesUtils::check_nat_rule_exists(const std::string & wan_interface) {
  static auto logger = rclcpp::get_logger("iptables_utils");
  
  try {
    std::string check_cmd = "iptables -t nat -C POSTROUTING -o " + wan_interface + " -j MASQUERADE 2>/dev/null";
    std::string result = exec_command(check_cmd);
    
    // 如果命令成功执行（返回码为0），则规则存在
    bool exists = result.empty();
    
    RCLCPP_DEBUG(logger, "[IPTABLES] NAT规则检查 - 接口: %s, 存在: %s", 
                 wan_interface.c_str(), exists ? "是" : "否");
    
    return exists;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger, "[IPTABLES] 检查NAT规则异常: %s", e.what());
    return false;
  }
}

}  // namespace utils
}  // namespace gen3_network_manager_core
