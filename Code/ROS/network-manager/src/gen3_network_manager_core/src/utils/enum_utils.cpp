#include "gen3_network_manager_core/utils/enum_utils.hpp"

namespace gen3_network_manager_core
{
namespace utils
{

std::string EnumUtils::network_type_to_string(uint8_t network_type)
{
  switch (network_type) {
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_WIFI:
      return "WiFi";
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_ETHERNET:
      return "以太网";
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G:
      return "5G";
    case gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_UNKNOWN:
      return "未知";
    default:
      return "未知类型";
  }
}

std::string EnumUtils::connection_status_to_string(uint8_t connection_status)
{
  switch (connection_status) {
    case gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_CONNECTED:
      return "已连接";
    case gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_DISCONNECTED:
      return "未连接";
    case gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_CONNECTING:
      return "连接中";
    case gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_SWITCHING:
      return "切换中";
    case gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_ERROR:
      return "错误";
    default:
      return "未知状态";
  }
}

std::string EnumUtils::binding_status_to_string(uint8_t binding_status)
{
  switch (binding_status) {
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_IDLE:
      return "空闲";
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_WAITING:
      return "等待连接";
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_CONNECTING:
      return "正在连接";
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_CONFIGURING:
      return "正在配置";
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_REGISTERING:
      return "正在注册";
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_SUCCESS:
      return "绑定成功";
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_FAILED:
      return "绑定失败";
    case gen3_network_interfaces::msg::BindingStatus::BINDING_STATUS_TIMEOUT:
      return "绑定超时";
    default:
      return "未知状态";
  }
}

std::string EnumUtils::binding_method_to_string(uint8_t binding_method)
{
  switch (binding_method) {
    case gen3_network_interfaces::msg::BindingStatus::BINDING_METHOD_BLE:
      return "蓝牙";
    case gen3_network_interfaces::msg::BindingStatus::BINDING_METHOD_QR_CODE:
      return "二维码";
    case gen3_network_interfaces::msg::BindingStatus::BINDING_METHOD_MANUAL:
      return "手动";
    case gen3_network_interfaces::msg::BindingStatus::BINDING_METHOD_UNKNOWN:
      return "未知";
    default:
      return "未知方式";
  }
}

}  // namespace utils
}  // namespace gen3_network_manager_core
