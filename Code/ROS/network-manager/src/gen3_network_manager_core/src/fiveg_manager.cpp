// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "gen3_network_manager_core/fiveg_manager.hpp"
#include "gen3_network_manager_core/nr90_serial_comm.hpp"
#include "gen3_network_manager_core/utils/network_utils.hpp"

#include <functional>
#include <memory>
#include <string>
#include <chrono>
#include <thread>

using namespace std::chrono_literals;
using std::placeholders::_1;
using std::placeholders::_2;

namespace gen3_network_manager_core
{

FiveGManager::FiveGManager(const rclcpp::Node::SharedPtr & node)
: node_(node), module_status_(FiveGModuleStatus::UNKNOWN), serial_fd_(-1), 
  is_serial_open_(false), monitor_running_(false), logger_(rclcpp::get_logger("fiveg_manager"))
{
  RCLCPP_INFO(logger_, "创建5G模块管理器");
}

FiveGManager::~FiveGManager()
{
  // 停止监控线程
  monitor_running_.store(false);
  if (monitor_thread_.joinable()) {
    monitor_thread_.join();
  }
  
  RCLCPP_INFO(logger_, "销毁5G模块管理器");
}

bool FiveGManager::init()
{
  RCLCPP_INFO(logger_, "初始化5G模块管理器");
  
  try {
    // 获取配置参数
    auto network_config = utils::NetworkUtils::get_network_config();
    device_path_ = node_->declare_parameter("fiveg_device_path", "/dev/ttyUSB0");
    baud_rate_ = node_->declare_parameter("fiveg_baud_rate", 115200);
    status_update_interval_ = node_->declare_parameter("fiveg_status_interval", 10.0);
    quality_update_interval_ = node_->declare_parameter("fiveg_quality_interval", 5.0);
    
    RCLCPP_INFO(logger_, "[CONFIG] 5G设备配置: 路径=%s, 波特率=%d", 
                device_path_.c_str(), baud_rate_);
    
    // 创建发布者
    status_pub_ = node_->create_publisher<gen3_network_interfaces::msg::NetworkStatus>(
      "fiveg_status", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建5G状态发布者: fiveg_status");

    quality_pub_ = node_->create_publisher<gen3_network_interfaces::msg::NetworkQuality>(
      "fiveg_quality", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建5G质量发布者: fiveg_quality");

    debug_pub_ = node_->create_publisher<std_msgs::msg::String>(
      "fiveg_debug", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建5G调试发布者: fiveg_debug");

    // 创建服务
    get_info_srv_ = node_->create_service<gen3_network_interfaces::srv::GetFivegInfo>(
      "get_fiveg_info", std::bind(&FiveGManager::handle_get_info, this, _1, _2));
    RCLCPP_INFO(logger_, "[SRV] 创建5G信息服务: get_fiveg_info");

    // 创建定时器
    status_timer_ = node_->create_wall_timer(
      std::chrono::duration<double>(status_update_interval_),
      std::bind(&FiveGManager::status_timer_callback, this));
    RCLCPP_INFO(logger_, "[TIMER] 创建状态更新定时器，间隔: %.1f秒", 
                status_update_interval_);

    quality_timer_ = node_->create_wall_timer(
      std::chrono::duration<double>(quality_update_interval_),
      std::bind(&FiveGManager::quality_timer_callback, this));
    RCLCPP_INFO(logger_, "[TIMER] 创建质量更新定时器，间隔: %.1f秒", 
                quality_update_interval_);

    // 初始化5G模块状态
    current_info_.device_path = device_path_;
    current_info_.is_connected = false;
    module_status_.store(FiveGModuleStatus::INITIALIZING);
    
    // 启动监控线程
    monitor_running_.store(true);
    monitor_thread_ = std::thread(&FiveGManager::monitor_thread_function, this);
    
    RCLCPP_INFO(logger_, "5G模块管理器初始化完成");
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "5G模块管理器初始化失败: %s", e.what());
    return false;
  }
}

bool FiveGManager::start_module()
{
  RCLCPP_INFO(logger_, "启动5G模块");
  
  module_status_.store(FiveGModuleStatus::INITIALIZING);
  
  // 这里可以添加5G模块的启动逻辑
  // 例如：通过GPIO控制模块电源、复位等
  
  return true;
}

bool FiveGManager::stop_module()
{
  RCLCPP_INFO(logger_, "停止5G模块");
  
  module_status_.store(FiveGModuleStatus::DISCONNECTED);
  
  // 这里可以添加5G模块的停止逻辑
  
  return true;
}

bool FiveGManager::restart_module()
{
  RCLCPP_INFO(logger_, "重启5G模块");
  
  stop_module();
  std::this_thread::sleep_for(2s);
  return start_module();
}

FiveGNetworkInfo FiveGManager::get_network_info()
{
  std::lock_guard<std::mutex> lock(status_mutex_);
  return current_info_;
}

FiveGModuleStatus FiveGManager::get_module_status()
{
  return module_status_.load();
}

int32_t FiveGManager::get_signal_strength()
{
  std::lock_guard<std::mutex> lock(status_mutex_);
  return current_info_.signal_strength;
}

int32_t FiveGManager::get_signal_quality()
{
  std::lock_guard<std::mutex> lock(status_mutex_);
  return current_info_.signal_quality;
}

bool FiveGManager::is_module_ready()
{
  FiveGModuleStatus status = module_status_.load();
  return (status == FiveGModuleStatus::READY || 
          status == FiveGModuleStatus::CONNECTED);
}

bool FiveGManager::is_network_connected()
{
  std::lock_guard<std::mutex> lock(status_mutex_);
  return current_info_.is_connected;
}

bool FiveGManager::test_connectivity()
{
  // 使用网络工具测试连通性
  return utils::NetworkUtils::test_connectivity("8.8.8.8", 3000);
}

void FiveGManager::status_timer_callback()
{
  RCLCPP_DEBUG(logger_, "[TIMER] 5G状态更新定时器触发");
  
  update_module_status();
  publish_status();
}

void FiveGManager::quality_timer_callback()
{
  RCLCPP_DEBUG(logger_, "[TIMER] 5G质量更新定时器触发");
  
  update_network_info();
  publish_quality();
}

void FiveGManager::handle_get_info(
  const std::shared_ptr<gen3_network_interfaces::srv::GetFivegInfo::Request> request,
  std::shared_ptr<gen3_network_interfaces::srv::GetFivegInfo::Response> response)
{
  (void)request; // 避免未使用参数警告
  
  RCLCPP_INFO(logger_, "[SRV] 处理5G信息获取请求");
  
  std::lock_guard<std::mutex> lock(status_mutex_);
  
  response->success = true;
  response->message = "5G信息获取成功";
  response->operator_name = current_info_.operator_name;
  response->network_type = current_info_.network_type;
  response->signal_strength = current_info_.signal_strength;
  response->signal_quality = current_info_.signal_quality;
  response->is_connected = current_info_.is_connected;
  response->ip_address = current_info_.ip_address;
  response->cell_id = current_info_.cell_id;
  response->frequency = current_info_.frequency;
  response->imsi = current_info_.imsi;
  response->imei = current_info_.imei;
}

std::string FiveGManager::module_status_to_string(FiveGModuleStatus status)
{
  switch (status) {
    case FiveGModuleStatus::UNKNOWN: return "UNKNOWN";
    case FiveGModuleStatus::INITIALIZING: return "INITIALIZING";
    case FiveGModuleStatus::READY: return "READY";
    case FiveGModuleStatus::CONNECTING: return "CONNECTING";
    case FiveGModuleStatus::CONNECTED: return "CONNECTED";
    case FiveGModuleStatus::DISCONNECTED: return "DISCONNECTED";
    case FiveGModuleStatus::ERROR: return "ERROR";
    default: return "UNKNOWN";
  }
}

void FiveGManager::update_module_status()
{
  // 这里会在监控线程中实现具体的状态更新逻辑
  RCLCPP_DEBUG(logger_, "[UPDATE] 更新5G模块状态");
}

void FiveGManager::update_network_info()
{
  // 这里会在监控线程中实现具体的网络信息更新逻辑
  RCLCPP_DEBUG(logger_, "[UPDATE] 更新5G网络信息");
}

void FiveGManager::publish_status()
{
  auto msg = gen3_network_interfaces::msg::NetworkStatus();
  msg.header.stamp = node_->now();
  msg.network_type = gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G;

  {
    std::lock_guard<std::mutex> lock(status_mutex_);
    auto network_config = utils::NetworkUtils::get_network_config();
    msg.interface_name = network_config.g5_interface;
    msg.ip_address = current_info_.ip_address;

    // 获取网关信息
    auto interfaces = utils::NetworkUtils::get_network_interfaces();
    for (const auto & iface : interfaces) {
      if (iface.name == network_config.g5_interface) {
        msg.gateway = iface.gateway;
        break;
      }
    }
  }

  FiveGModuleStatus status = module_status_.load();
  msg.connection_status = (status == FiveGModuleStatus::CONNECTED) ?
    gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_CONNECTED :
    gen3_network_interfaces::msg::NetworkStatus::CONNECTION_STATUS_DISCONNECTED;

  status_pub_->publish(msg);
}

void FiveGManager::publish_quality()
{
  auto msg = gen3_network_interfaces::msg::NetworkQuality();
  msg.header.stamp = node_->now();
  msg.network_type = gen3_network_interfaces::msg::NetworkStatus::NETWORK_TYPE_5G;
  
  {
    std::lock_guard<std::mutex> lock(status_mutex_);
    auto network_config = utils::NetworkUtils::get_network_config();
    msg.interface_name = network_config.g5_interface;
    msg.signal_strength = current_info_.signal_strength;
    msg.signal_quality = current_info_.signal_quality;
  }
  
  msg.gateway_reachable = test_connectivity();
  msg.internet_accessible = msg.gateway_reachable;
  msg.dns_working = msg.gateway_reachable;
  
  quality_pub_->publish(msg);
}

void FiveGManager::monitor_thread_function()
{
  RCLCPP_INFO(logger_, "[MONITOR] 5G监控线程启动");

  // 创建串口通信对象
  auto serial_comm = std::make_unique<Nr90SerialComm>(node_);

  // 初始化串口
  Nr90Error result = serial_comm->init(device_path_, baud_rate_);
  if (result != Nr90Error::SUCCESS) {
    RCLCPP_ERROR(logger_, "[MONITOR] 串口初始化失败: %s",
                 Nr90SerialComm::error_to_string(result).c_str());
    module_status_.store(FiveGModuleStatus::ERROR);
    return;
  }

  // 打开串口设备
  result = serial_comm->open_device();
  if (result != Nr90Error::SUCCESS) {
    RCLCPP_ERROR(logger_, "[MONITOR] 串口打开失败: %s",
                 Nr90SerialComm::error_to_string(result).c_str());
    module_status_.store(FiveGModuleStatus::ERROR);
    return;
  }

  // 配置串口
  result = serial_comm->configure_device();
  if (result != Nr90Error::SUCCESS) {
    RCLCPP_ERROR(logger_, "[MONITOR] 串口配置失败: %s",
                 Nr90SerialComm::error_to_string(result).c_str());
    module_status_.store(FiveGModuleStatus::ERROR);
    return;
  }

  // 等待模块就绪
  RCLCPP_INFO(logger_, "[MONITOR] 等待5G模块就绪...");
  int retry_count = 0;
  const int max_retries = 10;

  while (monitor_running_.load() && retry_count < max_retries) {
    result = serial_comm->test_basic_communication();
    if (result == Nr90Error::SUCCESS) {
      RCLCPP_INFO(logger_, "[MONITOR] 5G模块通信正常");
      module_status_.store(FiveGModuleStatus::READY);
      break;
    }

    retry_count++;
    RCLCPP_WARN(logger_, "[MONITOR] 5G模块通信测试失败，重试 %d/%d",
                retry_count, max_retries);
    std::this_thread::sleep_for(2s);
  }

  if (retry_count >= max_retries) {
    RCLCPP_ERROR(logger_, "[MONITOR] 5G模块通信失败，超过最大重试次数");
    module_status_.store(FiveGModuleStatus::ERROR);
    return;
  }

  // 主监控循环
  RCLCPP_INFO(logger_, "[MONITOR] 开始5G模块监控循环");

  while (monitor_running_.load()) {
    try {
      // 更新信号强度
      int32_t rssi = 0, rsrp = 0, rsrq = 0;
      result = serial_comm->get_signal_strength(rssi, rsrp, rsrq);

      if (result == Nr90Error::SUCCESS) {
        std::lock_guard<std::mutex> lock(status_mutex_);
        current_info_.signal_strength = rssi;
        // 计算信号质量百分比 (假设-113dBm为0%, -51dBm为100%)
        current_info_.signal_quality = std::max(0, std::min(100,
          static_cast<int32_t>((rssi + 113) * 100 / 62)));

        RCLCPP_DEBUG(logger_, "[MONITOR] 信号强度: %ddBm, 质量: %d%%",
                     rssi, current_info_.signal_quality);
      }

      // 检查网络连接状态
      auto network_config = utils::NetworkUtils::get_network_config();
      auto interfaces = utils::NetworkUtils::get_network_interfaces();

      bool is_connected = false;
      std::string ip_address;

      for (const auto & iface : interfaces) {
        if (iface.name == network_config.g5_interface && !iface.ip_address.empty()) {
          is_connected = true;
          ip_address = iface.ip_address;
          break;
        }
      }

      {
        std::lock_guard<std::mutex> lock(status_mutex_);
        current_info_.is_connected = is_connected;
        current_info_.ip_address = ip_address;
      }

      // 更新模块状态
      if (is_connected) {
        module_status_.store(FiveGModuleStatus::CONNECTED);
      } else if (module_status_.load() == FiveGModuleStatus::CONNECTED) {
        module_status_.store(FiveGModuleStatus::READY);
      }

      // 发布调试信息
      auto debug_msg = std_msgs::msg::String();
      debug_msg.data = "5G Status: " + module_status_to_string(module_status_.load()) +
                      ", Signal: " + std::to_string(rssi) + "dBm" +
                      ", Connected: " + (is_connected ? "Yes" : "No");
      debug_pub_->publish(debug_msg);

    } catch (const std::exception & e) {
      RCLCPP_ERROR(logger_, "[MONITOR] 监控异常: %s", e.what());
      module_status_.store(FiveGModuleStatus::ERROR);
    }

    // 监控间隔
    std::this_thread::sleep_for(5s);
  }

  RCLCPP_INFO(logger_, "[MONITOR] 5G监控线程结束");
}

}  // namespace gen3_network_manager_core
