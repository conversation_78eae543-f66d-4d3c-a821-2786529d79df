// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "gen3_network_manager_core/wifi_manager.hpp"
#include "gen3_network_manager_core/utils/network_utils.hpp"

#include <functional>
#include <memory>
#include <string>
#include <chrono>
#include <vector>
#include <algorithm>
#include <fstream>
#include <iostream>
#include <mutex>
#include <thread>

using namespace std::chrono_literals;
using std::placeholders::_1;
using std::placeholders::_2;

namespace gen3_network_manager_core
{

WiFiManager::WiFiManager(const rclcpp::Node::SharedPtr & node)
: node_(node), is_scanning_(false), logger_(rclcpp::get_logger("wifi_manager"))
{
  RCLCPP_INFO(logger_, "创建WiFi管理器");
  
  // 声明参数
  node_->declare_parameter("wifi_scan_interval", 60.0);
}

WiFiManager::~WiFiManager()
{
  RCLCPP_INFO(logger_, "销毁WiFi管理器");
  
}

bool WiFiManager::init()
{
  RCLCPP_INFO(logger_, "初始化WiFi管理器");
  
  try {
    // 创建服务
    connect_wifi_srv_ = node_->create_service<gen3_network_interfaces::srv::ConnectWiFi>(
      "connect_wifi", std::bind(&WiFiManager::handle_connect_wifi, this, _1, _2));
    RCLCPP_INFO(logger_, "[SRV] 创建WiFi连接服务: connect_wifi");

    get_network_list_srv_ = node_->create_service<gen3_network_interfaces::srv::GetNetworkList>(
      "get_network_list", std::bind(&WiFiManager::handle_get_network_list, this, _1, _2));
    RCLCPP_INFO(logger_, "[SRV] 创建网络列表服务: get_network_list");

    set_network_priority_srv_ = node_->create_service<gen3_network_interfaces::srv::SetNetworkPriority>(
      "set_network_priority", std::bind(&WiFiManager::handle_set_network_priority, this, _1, _2));
    RCLCPP_INFO(logger_, "[SRV] 创建网络优先级服务: set_network_priority");

    // 创建发布者
    wifi_scan_results_pub_ = node_->create_publisher<gen3_network_interfaces::msg::WiFiNetwork>(
      "wifi_scan_results", 10);
    RCLCPP_INFO(logger_, "[PUB] 创建WiFi扫描结果发布者: wifi_scan_results");

    // 创建定时器
    double wifi_scan_interval = node_->get_parameter("wifi_scan_interval").as_double();

    scan_timer_ = node_->create_wall_timer(
      std::chrono::duration<double>(wifi_scan_interval),
      std::bind(&WiFiManager::scan_timer_callback, this));
    RCLCPP_INFO(logger_, "[TIMER] 创建WiFi扫描定时器，间隔: %.1f秒", wifi_scan_interval);

    // 立即获取当前WiFi连接状态
    RCLCPP_DEBUG(logger_, "[INIT] 立即获取当前WiFi连接状态");
    auto current_wifi = utils::NetworkUtils::get_current_wifi_info();
    if (current_wifi.is_connected) {
      RCLCPP_INFO(logger_, "[INIT] 当前已连接WiFi: %s, 信号强度: %ddBm",
        current_wifi.ssid.c_str(), current_wifi.signal_strength);
    } else {
      RCLCPP_INFO(logger_, "[INIT] 当前未连接WiFi网络");
    }

    // 立即执行一次WiFi扫描
    RCLCPP_DEBUG(logger_, "[TIMER] 立即执行首次WiFi扫描");
    scan_timer_callback();
    
    // 加载已保存的网络
    load_saved_networks();

    std::string wifi_interface = node_->get_parameter("wifi_interface").as_string();
    RCLCPP_INFO(logger_, "WiFi管理器初始化完成，使用接口: %s", wifi_interface.c_str());
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "WiFi管理器初始化失败: %s", e.what());
    return false;
  }
}

void WiFiManager::handle_connect_wifi(
  const std::shared_ptr<gen3_network_interfaces::srv::ConnectWiFi::Request> request,
  std::shared_ptr<gen3_network_interfaces::srv::ConnectWiFi::Response> response)
{
  RCLCPP_INFO(
    logger_, "[SRV] 收到连接WiFi请求 - SSID: %s, 安全类型: %s",
    request->ssid.c_str(), request->security_type.c_str());

  // 检查参数
  if (request->ssid.empty()) {
    response->success = false;
    response->message = "SSID不能为空";
    RCLCPP_ERROR(logger_, "[SRV] WiFi连接失败: SSID为空");
    return;
  }
  
  // 如果是加密网络，密码不能为空
  if (request->security_type != "OPEN" && request->password.empty()) {
    response->success = false;
    response->message = "加密网络需要提供密码";
    return;
  }

  uint8_t security_type_int = 0; // 默认为开放网络
  if (request->password.empty()) {
    security_type_int = 0;
  }
  else {
    if (request->security_type == "WPA") {
      security_type_int = 1;
    } else if (request->security_type == "WPA2") {
      security_type_int = 2;
    } else if (request->security_type == "WPA3") {
      security_type_int = 3;
    } else if (request->security_type == "WEP") {
      security_type_int = 4;
    }
    else {
      security_type_int = 1; // 默认为WPA
    }
  }
  
  bool result = connect_to_wifi(
    request->ssid, request->password, security_type_int, true);
  
  if (result) {
    response->success = true;
    response->message = "WiFi连接成功";
    
    // 获取IP地址
    std::string ip_address = "*************"; // 简化处理，实际应获取真实IP
    response->ip_address = ip_address;
  } else {
    response->success = false;
    response->message = "WiFi连接失败";
  }
}

void WiFiManager::handle_get_network_list(
  const std::shared_ptr<gen3_network_interfaces::srv::GetNetworkList::Request> request,
  std::shared_ptr<gen3_network_interfaces::srv::GetNetworkList::Response> response)
{
  (void)request;  // 未使用的参数
  RCLCPP_INFO(logger_, "[SRV] 收到获取网络列表请求");

  // 使用互斥锁保护缓存的网络列表
  std::lock_guard<std::mutex> lock(wifi_mutex_);

  // 检查是否有缓存的网络列表
  if (available_networks_.empty()) {
    // 如果缓存为空，触发一次扫描（但不等待结果）
    if (!is_scanning_.load()) {
      RCLCPP_INFO(logger_, "[SRV] 缓存为空，触发异步扫描");
      // 在后台线程中启动扫描
      std::thread([this]() {
        this->perform_background_scan();
      }).detach();
    }

    response->success = true;
    response->message = "网络列表为空，已触发后台扫描";
    response->available_networks.clear();
    RCLCPP_INFO(logger_, "[SRV] 返回空列表，后台扫描进行中");
    return;
  }

  // 直接返回缓存的网络列表
  response->available_networks = available_networks_;
  response->success = true;
  response->message = "获取网络列表成功";

  RCLCPP_INFO(logger_, "[SRV] 返回缓存的网络列表，共 %zu 个网络",
    available_networks_.size());

  // 记录返回的网络信息
  for (const auto & network : available_networks_) {
    RCLCPP_DEBUG(logger_, "[SRV] 返回网络: SSID=%s, 信号强度=%ddBm, 可用状态=%s",
      network.ssid.c_str(), network.signal_strength,
      network.is_available ? "可用" : "不可用");
  }
  response->message = "获取网络列表成功";
  response->total_networks_found = available_networks_.size();
  response->scan_time = node_->now();
  response->scan_duration_ms = 0; // 简化处理

  RCLCPP_INFO(logger_, "[SRV] 网络列表获取完成，返回 %zu 个网络", available_networks_.size());
}

void WiFiManager::perform_background_scan()
{
  RCLCPP_DEBUG(logger_, "[ASYNC] 开始后台WiFi扫描");

  // 设置扫描状态
  is_scanning_.store(true);

  try {
    // 执行实际的WiFi扫描
    std::vector<gen3_network_manager_core::utils::NetworkUtils::WiFiInfo> scan_results =
      gen3_network_manager_core::utils::NetworkUtils::scan_wifi_networks();

    if (!scan_results.empty()) {
      // 更新缓存的网络列表
      update_cached_networks(scan_results);
      RCLCPP_DEBUG(logger_, "[ASYNC] 后台扫描完成，发现 %zu 个网络", scan_results.size());
    } else {
      RCLCPP_WARN(logger_, "[ASYNC] 后台扫描未发现任何网络");
    }

  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "[ASYNC] 后台扫描异常: %s", e.what());
  }

  // 清除扫描状态
  is_scanning_.store(false);
}

void WiFiManager::update_cached_networks(
  const std::vector<gen3_network_manager_core::utils::NetworkUtils::WiFiInfo> & scan_results)
{
  RCLCPP_DEBUG(logger_, "[CACHE] 更新缓存的网络列表");

  std::lock_guard<std::mutex> lock(wifi_mutex_);

  // 清空当前缓存
  available_networks_.clear();

  // 转换并缓存新的扫描结果
  for (const auto & network : scan_results) {
    gen3_network_interfaces::msg::WiFiNetwork wifi_msg;
    wifi_msg.ssid = network.ssid;
    wifi_msg.bssid = network.bssid;
    wifi_msg.signal_strength = network.signal_strength;
    wifi_msg.frequency = network.frequency;
    wifi_msg.security_type = network.security_type;
    wifi_msg.is_available = true; // 扫描到的网络都标记为可用
    wifi_msg.is_in_range = true;  // 扫描到的网络都在信号范围内
    wifi_msg.last_seen_time = node_->now(); // 设置最后发现时间

    // 设置默认值
    wifi_msg.is_hidden = false;
    wifi_msg.auto_connect = false;
    wifi_msg.priority = 0;
    wifi_msg.is_bound_network = false;
    wifi_msg.connection_count = 0;
    wifi_msg.success_rate = 0.0;
    wifi_msg.min_signal_threshold = -80; // 默认最低信号阈值
    wifi_msg.avg_latency_ms = 0.0;
    wifi_msg.avg_download_speed = 0.0;
    wifi_msg.avg_upload_speed = 0.0;

    available_networks_.push_back(wifi_msg);

    RCLCPP_DEBUG(logger_, "[CACHE] 缓存网络: SSID=%s, 信号=%ddBm, 连接=%s",
      network.ssid.c_str(), network.signal_strength,
      network.is_connected ? "是" : "否");
  }

  RCLCPP_DEBUG(logger_, "[CACHE] 网络列表缓存更新完成，共 %zu 个网络",
    available_networks_.size());
}

void WiFiManager::handle_set_network_priority(
  const std::shared_ptr<gen3_network_interfaces::srv::SetNetworkPriority::Request> request,
  std::shared_ptr<gen3_network_interfaces::srv::SetNetworkPriority::Response> response)
{
  RCLCPP_INFO(
    logger_, "[SRV] 收到设置网络优先级请求 - SSID: %s, 优先级: %d, 自动连接: %s",
    request->ssid.c_str(), request->priority, request->auto_connect ? "是" : "否");

  // 检查参数
  if (request->ssid.empty()) {
    response->success = false;
    response->message = "SSID不能为空";
    response->error_code = "EMPTY_SSID";
    RCLCPP_ERROR(logger_, "[SRV] 设置网络优先级失败: SSID为空");
    return;
  }

  // 查找网络
  auto it = std::find_if(
    saved_networks_.begin(), saved_networks_.end(),
    [&request](const SavedNetwork & network) {
      return network.ssid == request->ssid;
    });

  if (it == saved_networks_.end()) {
    response->success = false;
    response->message = "未找到指定的网络";
    response->error_code = "NETWORK_NOT_FOUND";
    RCLCPP_ERROR(logger_, "[SRV] 设置网络优先级失败: 未找到网络 %s", request->ssid.c_str());
    return;
  }

  RCLCPP_DEBUG(logger_, "[SRV] 找到网络 %s，当前优先级: %d", request->ssid.c_str(), it->priority);

  // 更新优先级
  it->priority = request->priority;
  it->auto_connect = request->auto_connect;

  RCLCPP_INFO(logger_, "[SRV] 更新网络 %s 优先级: %d -> %d",
    request->ssid.c_str(), it->priority, request->priority);

  // 保存更改
  bool save_result = save_networks();

  if (save_result) {
    response->success = true;
    response->message = "设置网络优先级成功";
    response->error_code = "";
    RCLCPP_INFO(logger_, "[SRV] 网络优先级设置成功并已保存");
  } else {
    response->success = false;
    response->message = "保存网络配置失败";
    response->error_code = "SAVE_FAILED";
    RCLCPP_ERROR(logger_, "[SRV] 保存网络配置失败");
  }
}

void WiFiManager::scan_timer_callback()
{
  RCLCPP_DEBUG(logger_, "[TIMER] WiFi扫描定时器触发");

  // 检查是否已有扫描在进行中
  if (is_scanning_.load()) {
    RCLCPP_DEBUG(logger_, "[TIMER] WiFi扫描正在进行中，跳过本次扫描");
    return;
  }

  // 启动异步后台扫描
  RCLCPP_DEBUG(logger_, "[TIMER] 启动异步WiFi扫描");
  std::thread([this]() {
    this->perform_background_scan();
  }).detach();

  RCLCPP_DEBUG(logger_, "[TIMER] WiFi扫描定时器处理完成");
}

bool WiFiManager::connect_to_wifi(
  const std::string & ssid, const std::string & password, uint8_t security_type, bool save_network)
{
  RCLCPP_INFO(logger_, "连接WiFi: SSID=%s", ssid.c_str());
  
  try {
    // 将安全类型转换为字符串
    std::string security_str;
    switch (security_type) {
      case 0: // NONE
        security_str = "OPEN";
        break;
      case 1: // WPA
      case 2: // WPA2
      case 3: // WPA3
        security_str = "WPA";
        break;
      case 4: // WEP
        security_str = "WEP";
        break;
      default:
        security_str = "WPA"; // 默认使用WPA
        break;
    }
    
    // 调用网络工具连接WiFi
    bool result = gen3_network_manager_core::utils::NetworkUtils::connect_to_wifi(
      ssid, password, security_str);
    
    if (result) {
      RCLCPP_INFO(logger_, "WiFi连接成功: %s", ssid.c_str());
      
      // 如果需要保存网络
      if (save_network) {
        SavedNetwork network;
        network.ssid = ssid;
        network.password = password;
        network.security_type = security_str;
        network.priority = 0;
        network.auto_connect = true;
        
        add_or_update_saved_network(network);
        save_networks();
      }
    } else {
      RCLCPP_ERROR(logger_, "WiFi连接失败: %s", ssid.c_str());
    }
    
    return result;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "WiFi连接异常: %s", e.what());
    return false;
  }
}

bool WiFiManager::scan_wifi_networks(std::vector<gen3_network_manager_core::utils::NetworkUtils::WiFiInfo> & networks)
{
  RCLCPP_INFO(logger_, "扫描WiFi网络");
  
  try {
    // 调用网络工具扫描WiFi
    networks = gen3_network_manager_core::utils::NetworkUtils::scan_wifi_networks();
    return !networks.empty();
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "扫描WiFi网络失败: %s", e.what());
    return false;
  }
}

bool WiFiManager::load_saved_networks()
{
  RCLCPP_INFO(logger_, "加载已保存的WiFi网络");
  
  try {
    // 获取配置文件路径
    std::string config_dir = "/tmp";
    std::string config_file = config_dir + "/wifi_networks.conf";
    
    // 打开配置文件
    std::ifstream file(config_file);
    if (!file.is_open()) {
      RCLCPP_WARN(logger_, "无法打开WiFi配置文件: %s", config_file.c_str());
      return false;
    }
    
    // 清空当前列表
    saved_networks_.clear();
    
    // 读取配置
    std::string line;
    SavedNetwork current_network;
    bool in_network_section = false;
    
    while (std::getline(file, line)) {
      // 跳过空行和注释
      if (line.empty() || line[0] == '#') {
        continue;
      }
      
      // 检查是否是新的网络节
      if (line == "[network]") {
        // 如果已经在处理一个网络，保存它
        if (in_network_section && !current_network.ssid.empty()) {
          saved_networks_.push_back(current_network);
        }
        
        // 开始新的网络
        current_network = SavedNetwork();
        in_network_section = true;
        continue;
      }
      
      // 解析键值对
      size_t pos = line.find('=');
      if (pos != std::string::npos) {
        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);
        
        if (key == "ssid") {
          current_network.ssid = value;
        } else if (key == "password") {
          current_network.password = value;
        } else if (key == "security_type") {
          current_network.security_type = value;
        } else if (key == "priority") {
          current_network.priority = std::stoi(value);
        } else if (key == "auto_connect") {
          current_network.auto_connect = (value == "true");
        }
      }
    }
    
    // 添加最后一个网络
    if (in_network_section && !current_network.ssid.empty()) {
      saved_networks_.push_back(current_network);
    }
    
    file.close();
    
    RCLCPP_INFO(logger_, "已加载%zu个WiFi网络配置", saved_networks_.size());
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "加载WiFi网络配置异常: %s", e.what());
    return false;
  }
}

bool WiFiManager::save_networks()
{
  std::lock_guard<std::mutex> lock(wifi_mutex_);
  
  try {
    // 获取配置文件路径
    std::string config_dir = "/tmp";
    std::string config_file = config_dir + "/wifi_networks.conf";
    
    // 创建配置文件
    std::ofstream file(config_file);
    if (!file.is_open()) {
      RCLCPP_ERROR(logger_, "无法创建WiFi配置文件: %s", config_file.c_str());
      return false;
    }
    
    // 写入文件头
    file << "# Gen3 WiFi Networks Configuration\n";
    file << "# Generated at " << node_->now().seconds() << "\n\n";
    
    // 写入网络配置
    for (const auto & network : saved_networks_) {
      file << "[network]\n";
      file << "ssid=" << network.ssid << "\n";
      file << "password=" << network.password << "\n";
      file << "security_type=" << network.security_type << "\n";
      file << "priority=" << network.priority << "\n";
      file << "auto_connect=" << (network.auto_connect ? "true" : "false") << "\n\n";
    }
    
    file.close();
    RCLCPP_INFO(logger_, "WiFi网络配置已保存到: %s", config_file.c_str());
    return true;
    
  } catch (const std::exception & e) {
    RCLCPP_ERROR(logger_, "保存WiFi网络配置异常: %s", e.what());
    return false;
  }
}

void WiFiManager::add_or_update_saved_network(const SavedNetwork & network)
{
  std::lock_guard<std::mutex> lock(wifi_mutex_);
  
  // 查找是否已存在相同SSID的网络
  auto it = std::find_if(
    saved_networks_.begin(), saved_networks_.end(),
    [&network](const SavedNetwork & saved) {
      return saved.ssid == network.ssid;
    });
  
  if (it != saved_networks_.end()) {
    // 更新现有网络
    *it = network;
    RCLCPP_INFO(logger_, "更新已保存的网络: %s", network.ssid.c_str());
  } else {
    // 添加新网络
    saved_networks_.push_back(network);
    RCLCPP_INFO(logger_, "添加新的网络: %s", network.ssid.c_str());
  }
}

// 获取当前连接的WiFi SSID
std::string WiFiManager::get_current_ssid()
{
  // 使用NetworkUtils获取WiFi信息
  auto wifi_info = utils::NetworkUtils::get_current_wifi_info();
  
  if (wifi_info.is_connected) {
    return wifi_info.ssid;
  }
  
  return "";
}

}  // namespace gen3_network_manager_core