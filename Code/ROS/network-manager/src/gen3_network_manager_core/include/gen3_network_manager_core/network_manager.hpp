// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef GEN3_NETWORK_MANAGER_CORE__NETWORK_MANAGER_HPP_
#define GEN3_NETWORK_MANAGER_CORE__NETWORK_MANAGER_HPP_

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <mutex>
#include <chrono>
#include <atomic>
#include <future>
#include <thread>

#include "rclcpp/rclcpp.hpp"
#include "rclcpp/service.hpp"
#include "std_msgs/msg/string.hpp"
#include "std_srvs/srv/trigger.hpp"

#include "gen3_network_interfaces/msg/network_status.hpp"
#include "gen3_network_interfaces/msg/network_quality.hpp"
#include "gen3_network_interfaces/srv/switch_network.hpp"

// 前向声明
namespace gen3_network_manager_core {
  class CellularManager;
}

namespace gen3_network_manager_core
{

class NetworkManager
{
public:
  explicit NetworkManager(const rclcpp::Node::SharedPtr & node);
  ~NetworkManager();

  // 初始化函数
  bool init();

private:
  // ROS节点
  rclcpp::Node::SharedPtr node_;
  
  // 日志记录器
  rclcpp::Logger logger_;

  // 发布者
  rclcpp::Publisher<gen3_network_interfaces::msg::NetworkStatus>::SharedPtr network_status_pub_;
  rclcpp::Publisher<std_msgs::msg::String>::SharedPtr status_pub_;

  // 订阅者
  rclcpp::Subscription<gen3_network_interfaces::msg::NetworkQuality>::SharedPtr network_quality_sub_;
  rclcpp::Subscription<gen3_network_interfaces::msg::NetworkQuality>::SharedPtr fiveg_quality_sub_;

  // 服务
  rclcpp::Service<gen3_network_interfaces::srv::SwitchNetwork>::SharedPtr switch_network_srv_;
  rclcpp::Service<std_srvs::srv::Trigger>::SharedPtr refresh_network_srv_;

  // 定时器
  rclcpp::TimerBase::SharedPtr status_timer_;
  rclcpp::TimerBase::SharedPtr init_timer_;  // 一次性初始化定时器
  
  // 内部状态
  std::mutex status_mutex_;
  gen3_network_interfaces::msg::NetworkStatus current_status_;
  std::map<std::string, gen3_network_interfaces::msg::NetworkQuality> network_quality_map_;

  // 5G质量信息
  gen3_network_interfaces::msg::NetworkQuality latest_fiveg_quality_;
  std::mutex fiveg_quality_mutex_;

  // 配置参数
  bool enable_auto_switch_;
  int preferred_network_type_;
  int min_signal_strength_;
  double min_connectivity_score_;

  // NAT和内网卡配置
  std::vector<std::string> lan_interfaces_;
  bool enable_nat_;

  // 科学化自动切换状态
  std::chrono::steady_clock::time_point last_switch_time_;
  std::map<std::string, gen3_network_interfaces::msg::NetworkQuality> quality_history_;

  // 异步网络质量处理
  std::atomic<bool> quality_processing_in_progress_{false};
  std::future<void> quality_processing_future_;

  // 5G管理器
  std::unique_ptr<CellularManager> cellular_manager_;
  
  // 回调函数
  void network_quality_callback(const gen3_network_interfaces::msg::NetworkQuality::SharedPtr msg);
  void fiveg_quality_callback(const gen3_network_interfaces::msg::NetworkQuality::SharedPtr msg);
  void status_timer_callback();
  void init_timer_callback();  // 一次性初始化定时器回调
  void handle_switch_network(
    const std::shared_ptr<gen3_network_interfaces::srv::SwitchNetwork::Request> request,
    std::shared_ptr<gen3_network_interfaces::srv::SwitchNetwork::Response> response);
  void handle_refresh_network(
    const std::shared_ptr<std_srvs::srv::Trigger::Request> request,
    std::shared_ptr<std_srvs::srv::Trigger::Response> response);
  
  // 科学化自动切换决策结构体
  struct SwitchDecision {
    bool should_switch;
    std::string reason;
    double confidence;
    uint8_t recommended_network_type;
    std::vector<std::string> detailed_reasons;
  };

  // 辅助函数
  bool update_network_status();
  bool switch_to_network(uint8_t network_type, const std::string & wifi_ssid, bool force_switch);
  void publish_network_status();
  void check_auto_switch_conditions(const gen3_network_interfaces::msg::NetworkQuality & quality);

  // 科学化自动切换辅助函数
  SwitchDecision evaluate_switch_necessity(const gen3_network_interfaces::msg::NetworkQuality & quality);
  double calculate_switch_urgency(const gen3_network_interfaces::msg::NetworkQuality & quality);
  bool is_critical_failure(const gen3_network_interfaces::msg::NetworkQuality & quality);
  double get_network_stability_factor(const gen3_network_interfaces::msg::NetworkQuality & quality);
  bool should_prevent_frequent_switching();

  // 异步网络质量处理函数
  void process_quality_async(const gen3_network_interfaces::msg::NetworkQuality & quality);
  void check_async_quality_processing();

private:
  // 网络切换的具体实现函数
  bool switch_to_wifi(const std::string & ssid);
  bool switch_to_ethernet();
  bool switch_to_5g();
  bool verify_network_switch(uint8_t network_type, const std::string & wifi_ssid);

  // 网络切换失败回滚函数
  bool rollback_to_previous_network(uint8_t previous_type, const std::string & previous_ssid,
                                   const std::string & previous_interface);

  // NAT规则管理函数
  bool update_nat_rules(const std::string & wan_interface);
  void initialize_nat_rules();

  // 基本网络恢复函数（最后手段）
  bool attempt_basic_network_recovery(const std::string & target_interface);

  // 自动切换相关函数
  uint8_t get_preferred_network_type(uint8_t current_type);
  std::string get_best_available_wifi();

  // 更新路由优先级函数
  bool update_route_priority(const std::string & interface_name, int priority);

  // 静态路由保存和恢复函数
  std::vector<std::string> save_static_routes();
  bool restore_static_routes(const std::vector<std::string> & routes);

  // 安全的路由管理函数
  bool safe_update_default_route(const std::string & interface_name, int priority);
};

}  // namespace gen3_network_manager_core

#endif  // GEN3_NETWORK_MANAGER_CORE__NETWORK_MANAGER_HPP_ 