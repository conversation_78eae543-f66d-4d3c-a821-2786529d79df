// Copyright 2025 Gen3 Robotics
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef GEN3_NETWORK_MANAGER_CORE__DNS_MANAGER_HPP_
#define GEN3_NETWORK_MANAGER_CORE__DNS_MANAGER_HPP_

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <mutex>
#include <chrono>
#include <atomic>

#include "rclcpp/rclcpp.hpp"
#include "std_msgs/msg/string.hpp"

#include "gen3_network_interfaces/msg/network_status.hpp"

namespace gen3_network_manager_core
{

class DNSManager
{
public:
  explicit DNSManager(const rclcpp::Node::SharedPtr & node);
  ~DNSManager();

  // 初始化函数
  bool init();

private:
  // ROS节点
  rclcpp::Node::SharedPtr node_;
  
  // 日志记录器
  rclcpp::Logger logger_;

  // 发布者
  rclcpp::Publisher<std_msgs::msg::String>::SharedPtr dns_status_pub_;

  // 订阅者
  rclcpp::Subscription<gen3_network_interfaces::msg::NetworkStatus>::SharedPtr network_status_sub_;

  // 定时器
  rclcpp::TimerBase::SharedPtr dns_check_timer_;
  
  // DNS服务器信息结构体
  struct DNSServerInfo {
    std::string address;
    int priority;                    // 优先级（数值越小优先级越高）
    double success_rate;             // 成功率（0.0-1.0）
    int consecutive_failures;        // 连续失败次数
    std::chrono::steady_clock::time_point last_test_time;
    std::chrono::milliseconds avg_response_time;
    bool is_available;               // 当前是否可用
    std::string region;              // 地区标识（domestic/international）
    bool is_domestic;                // 是否为国内DNS服务器
  };

  // 网络环境检测结果
  struct NetworkEnvironment {
    bool is_in_china;                // 是否在中国境内
    bool can_access_international;   // 是否可以访问国际网站
    bool prefer_domestic_dns;        // 是否优先使用国内DNS
    std::string detected_region;     // 检测到的地区
  };

  // 内部状态
  std::mutex dns_mutex_;
  std::vector<std::string> current_dns_servers_;
  std::map<std::string, std::vector<std::string>> network_dns_map_;
  bool dns_override_enabled_;

  // 智能DNS管理状态
  std::vector<DNSServerInfo> configured_dns_servers_;  // 配置的DNS服务器列表
  std::atomic<bool> dns_check_in_progress_{false};     // DNS检查进行中标志
  std::chrono::steady_clock::time_point last_dns_check_time_;
  int dns_switch_count_;                               // DNS切换次数统计

  // 中国网络环境相关
  NetworkEnvironment network_env_;                     // 网络环境检测结果
  std::chrono::steady_clock::time_point last_env_check_time_;  // 上次环境检测时间
  
  // 回调函数
  void network_status_callback(const gen3_network_interfaces::msg::NetworkStatus::SharedPtr msg);
  void dns_check_timer_callback();
  
  // 辅助函数（保留有实现的函数）
  // bool update_dns_servers(const std::vector<std::string> & dns_servers);  // 已删除，使用set_dns_servers替代
  // bool check_dns_servers();  // 已删除，使用intelligent_dns_check替代
  bool add_dns_server(const std::string & dns_server);
  bool remove_dns_server(const std::string & dns_server);
  // void publish_dns_status(const std::string & status);  // 未实现，暂时删除
  std::vector<std::string> get_current_dns_servers();
  // bool load_dns_configuration();  // 已删除，使用load_dns_config替代
  // bool save_dns_configuration();  // 未实现，暂时删除
  
  // 原有函数声明
  bool set_dns_servers(const std::vector<std::string> & dns_servers);
  bool set_default_dns_servers();
  // bool check_dns_config();  // 已删除，使用intelligent_dns_check()替代
  void load_dns_config();
  bool backup_dns_config();
  bool restore_dns_config();

  // 智能DNS管理函数
  void intelligent_dns_check();                       // 智能DNS检查主函数
  bool test_dns_server(const std::string & dns_server, std::chrono::milliseconds & response_time);
  void update_dns_server_stats(const std::string & dns_server, bool success, std::chrono::milliseconds response_time);
  std::vector<DNSServerInfo> get_sorted_dns_servers(); // 按优先级排序DNS服务器
  bool switch_to_best_dns_server();                   // 切换到最佳DNS服务器
  void load_dns_server_config();                      // 从配置文件加载DNS服务器列表
  void save_dns_server_stats();                       // 保存DNS服务器统计信息
  void update_dns_priorities();                       // 更新DNS服务器优先级
  bool is_dns_resolution_working();                   // 检查DNS解析是否正常工作

  // 中国网络环境检测函数
  void detect_network_environment();                  // 检测网络环境
  bool is_in_china_mainland();                        // 检测是否在中国大陆
  bool test_domestic_connectivity();                  // 测试国内网络连通性
  void load_china_dns_config();                       // 加载中国DNS配置
  std::vector<DNSServerInfo> get_china_optimized_dns_servers(); // 获取中国优化的DNS服务器列表
};

}  // namespace gen3_network_manager_core

#endif  // GEN3_NETWORK_MANAGER_CORE__DNS_MANAGER_HPP_ 