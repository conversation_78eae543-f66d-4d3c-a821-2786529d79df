from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument, SetEnvironmentVariable
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.substitutions import FindPackageShare
import os

def generate_launch_description():
    # 配置日志格式环境变量，显示完整时间戳
    set_log_format = SetEnvironmentVariable(
        'RCUTILS_LOGGING_BUFFERED_STREAM', '1'
    )

    set_log_format_detailed = SetEnvironmentVariable(
        'RCUTILS_LOGGING_USE_STDOUT', '1'
    )

    # 设置日志时间戳格式
    set_timestamp_format = SetEnvironmentVariable(
        'RCUTILS_CONSOLE_OUTPUT_FORMAT',
        '[{severity}] [{time}] [{name}]: {message}'
    )

    # 声明启动参数
    log_level = LaunchConfiguration('log_level')
    log_level_arg = DeclareLaunchArgument(
        'log_level',
        default_value='info',
        description='日志级别'
    )

    # 配置文件路径参数
    config_file = LaunchConfiguration('config_file')
    config_file_arg = DeclareLaunchArgument(
        'config_file',
        default_value=PathJoinSubstitution([
            FindPackageShare('gen3_network_manager_core'),
            'config',
            'network_config.yaml'
        ]),
        description='网络配置文件路径'
    )
    
    # 网络管理器节点
    network_manager_node = Node(
        package='gen3_network_manager_core',
        executable='network_manager_node',
        name='gen3_network_manager',
        output='screen',
        parameters=[
            config_file,  # 加载配置文件
            {'log_level': log_level},  # 日志级别可以通过启动参数覆盖
        ],
        remappings=[
            ('network_status', '/gen3/network/status'),
            ('network_quality', '/gen3/network/quality'),
            ('binding_status', '/gen3/network/binding_status'),
            ('switch_network', '/gen3/network/switch'),
            ('connect_wifi', '/gen3/network/connect_wifi'),
            ('get_network_list', '/gen3/network/list'),
            ('set_network_priority', '/gen3/network/priority'),
            ('start_binding', '/gen3/network/start_binding'),
            ('network_binding', '/gen3/network/binding'),
        ]
    )
    
    return LaunchDescription([
        # 设置日志格式环境变量
        set_log_format,
        set_log_format_detailed,
        set_timestamp_format,

        # 启动参数
        log_level_arg,
        config_file_arg,

        # 网络管理器节点
        network_manager_node,
    ])