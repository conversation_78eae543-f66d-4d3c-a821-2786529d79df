# 请求
string ssid                    # 网络名称
uint32 priority                # 新的优先级 (数值越大优先级越高)
bool is_bound_network          # 是否标记为绑定网络
bool auto_connect              # 是否自动连接

---

# 响应
bool success                   # 设置是否成功
string message                 # 结果消息
string error_code              # 错误代码

# 更新后的网络信息
uint32 old_priority            # 原优先级
uint32 new_priority            # 新优先级
uint32 current_rank            # 当前排名 (在所有网络中的位置) 