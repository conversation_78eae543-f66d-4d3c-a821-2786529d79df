# 绑定方式常量
uint8 BINDING_METHOD_BLE = 1
uint8 BINDING_METHOD_QR_CODE = 2
uint8 BINDING_METHOD_BOTH = 3

# Goal - 绑定目标
uint8 binding_method           # 绑定方式
uint32 timeout_seconds         # 超时时间
string device_name             # 设备名称
bool reset_existing_binding    # 是否重置现有绑定

---

# Result - 绑定结果
bool success                   # 绑定是否成功
string message                 # 结果消息
string error_code              # 错误代码

# 绑定信息
string device_id               # 设备ID
string user_device_id          # 用户设备ID
builtin_interfaces/Time binding_completion_time

# WiFi配置结果
bool wifi_configured           # WiFi是否配置成功
string wifi_ssid               # 配置的WiFi网络
string wifi_ip_address         # 获得的IP地址

# 平台注册结果
bool platform_registered       # 是否成功注册到平台
string registration_token      # 注册令牌

---

# Feedback - 绑定进度反馈
uint8 current_step             # 当前步骤
string step_description        # 步骤描述
uint8 progress_percentage      # 进度百分比 (0-100)
string status_message          # 状态消息

# 步骤常量
uint8 STEP_INITIALIZING = 1    # 初始化
uint8 STEP_WAITING_CONNECTION = 2  # 等待连接
uint8 STEP_RECEIVING_CONFIG = 3    # 接收配置
uint8 STEP_CONNECTING_WIFI = 4     # 连接WiFi
uint8 STEP_REGISTERING_PLATFORM = 5  # 注册平台
uint8 STEP_FINALIZING = 6      # 完成绑定

# 实时状态
bool ble_advertising           # BLE是否在广播
bool qr_scanner_active         # 二维码扫描是否激活
uint32 connection_attempts     # 连接尝试次数
builtin_interfaces/Time step_start_time 