# 网络基本信息
string ssid                    # 网络名称
string bssid                   # MAC地址
int32 signal_strength          # 信号强度 (dBm)
uint32 frequency               # 频率 (MHz)
string security_type           # 加密类型 (OPEN/WEP/WPA/WPA2/WPA3)
bool is_hidden                 # 是否隐藏网络

# 连接配置
string password                # 网络密码 (加密存储)
bool auto_connect              # 是否自动连接
uint32 priority                # 优先级 (数值越大优先级越高)

# 绑定信息
bool is_bound_network          # 是否通过绑定流程添加
builtin_interfaces/Time binding_time    # 绑定时间
string binding_source          # 绑定来源 (BLE/QR_CODE/MANUAL)

# 连接历史
uint32 connection_count        # 连接次数
builtin_interfaces/Time last_connected_time  # 最后连接时间
float64 success_rate           # 连接成功率 (0-1)
int32 min_signal_threshold     # 最低信号阈值

# 网络质量历史
float64 avg_latency_ms         # 平均延迟
float64 avg_download_speed     # 平均下载速度
float64 avg_upload_speed       # 平均上传速度

# 状态标记
bool is_available              # 当前是否可用
bool is_in_range               # 是否在信号范围内
builtin_interfaces/Time last_seen_time   # 最后发现时间 