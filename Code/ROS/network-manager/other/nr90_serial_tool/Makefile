# Makefile for NR90 Serial Communication Tool
# NR90 5G设备串口通信工具编译配置

# 编译器设置
CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE -D_DEFAULT_SOURCE
LDFLAGS = 
LIBS = 

# 目标文件和源文件
TARGET = nr90_test
SOURCES = main.c nr90_serial_comm.c
HEADERS = nr90_serial_comm.h
OBJECTS = $(SOURCES:.c=.o)

# 默认目标
all: $(TARGET)

# 编译目标程序
$(TARGET): $(OBJECTS)
	@echo "链接 $(TARGET)..."
	$(CC) $(LDFLAGS) -o $@ $^ $(LIBS)
	@echo "编译完成: $(TARGET)"

# 编译对象文件
%.o: %.c $(HEADERS)
	@echo "编译 $<..."
	$(CC) $(CFLAGS) -c $< -o $@

# 清理编译文件
clean:
	@echo "清理编译文件..."
	rm -f $(OBJECTS) $(TARGET)
	@echo "清理完成"

# 安装程序（可选）
install: $(TARGET)
	@echo "安装 $(TARGET) 到 /usr/local/bin/..."
	sudo cp $(TARGET) /usr/local/bin/
	sudo chmod +x /usr/local/bin/$(TARGET)
	@echo "安装完成"

# 卸载程序
uninstall:
	@echo "卸载 $(TARGET)..."
	sudo rm -f /usr/local/bin/$(TARGET)
	@echo "卸载完成"

# 运行测试（需要设备连接）
test: $(TARGET)
	@echo "运行设备测试..."
	./$(TARGET) -v

# 列出可用设备
list-devices: $(TARGET)
	@echo "列出可用的ttyUSB设备..."
	./$(TARGET) -l

# 检查代码风格（需要安装cppcheck）
check:
	@echo "检查代码..."
	@if command -v cppcheck >/dev/null 2>&1; then \
		cppcheck --enable=all --std=c99 $(SOURCES); \
	else \
		echo "cppcheck 未安装，跳过代码检查"; \
	fi

# 格式化代码（需要安装clang-format）
format:
	@echo "格式化代码..."
	@if command -v clang-format >/dev/null 2>&1; then \
		clang-format -i $(SOURCES) $(HEADERS); \
		echo "代码格式化完成"; \
	else \
		echo "clang-format 未安装，跳过代码格式化"; \
	fi

# 创建发布包
dist: clean
	@echo "创建发布包..."
	@mkdir -p nr90_tool
	@cp $(SOURCES) $(HEADERS) Makefile README.md nr90_tool/
	@tar -czf nr90_tool.tar.gz nr90_tool/
	@rm -rf nr90_tool/
	@echo "发布包创建完成: nr90_tool.tar.gz"

# 显示帮助信息
help:
	@echo "NR90串口通信工具 Makefile"
	@echo ""
	@echo "可用目标:"
	@echo "  all          - 编译程序 (默认)"
	@echo "  clean        - 清理编译文件"
	@echo "  install      - 安装程序到系统"
	@echo "  uninstall    - 从系统卸载程序"
	@echo "  test         - 运行设备测试"
	@echo "  list-devices - 列出可用设备"
	@echo "  check        - 检查代码质量"
	@echo "  format       - 格式化代码"
	@echo "  dist         - 创建发布包"
	@echo "  help         - 显示此帮助信息"
	@echo ""
	@echo "使用示例:"
	@echo "  make                    # 编译程序"
	@echo "  make clean              # 清理文件"
	@echo "  make test               # 测试设备"
	@echo "  make install            # 安装到系统"

# 声明伪目标
.PHONY: all clean install uninstall test list-devices check format dist help

# 依赖关系
main.o: main.c nr90_serial_comm.h
nr90_serial_comm.o: nr90_serial_comm.c nr90_serial_comm.h
