#ifndef NR90_SERIAL_COMM_H
#define NR90_SERIAL_COMM_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h>
#include <errno.h>
#include <time.h>
#include <sys/select.h>
#include <sys/time.h>
#include <signal.h>
#include <strings.h>

// 功能测试宏（如果未定义）
#ifndef _GNU_SOURCE
#define _GNU_SOURCE
#endif
#ifndef _DEFAULT_SOURCE
#define _DEFAULT_SOURCE
#endif

// 常量定义
#define MAX_DEVICE_PATH 256
#define MAX_COMMAND_LEN 128
#define MAX_RESPONSE_LEN 2048
#define MAX_RETRY_COUNT 3
#define DEFAULT_TIMEOUT 5
#define DEFAULT_BAUD_RATE B115200
#define DEFAULT_DEVICE "/dev/ttyUSB0"

// 错误代码定义
typedef enum {
    NR90_SUCCESS = 0,
    NR90_ERROR_DEVICE_OPEN = -1,
    NR90_ERROR_DEVICE_CONFIG = -2,
    NR90_ERROR_COMMAND_SEND = -3,
    NR90_ERROR_RESPONSE_TIMEOUT = -4,
    NR90_ERROR_RESPONSE_INVALID = -5,
    NR90_ERROR_DEVICE_NOT_READY = -6,
    NR90_ERROR_MEMORY_ALLOC = -7,
    NR90_ERROR_INVALID_PARAM = -8
} nr90_error_t;

// 日志级别定义
typedef enum {
    LOG_ERROR = 0,
    LOG_WARNING = 1,
    LOG_INFO = 2,
    LOG_DEBUG = 3
} log_level_t;

// 设备状态结构体
typedef struct {
    int fd;                           // 文件描述符
    char device_path[MAX_DEVICE_PATH]; // 设备路径
    struct termios original_termios;   // 原始串口设置
    int is_configured;                // 配置状态标志
    int baud_rate;                    // 波特率
    log_level_t log_level;            // 日志级别
} nr90_device_t;

// AT命令响应结构体
typedef struct {
    char response[MAX_RESPONSE_LEN];  // 响应内容
    int length;                       // 响应长度
    int is_ok;                        // 是否成功响应
    int is_error;                     // 是否错误响应
    time_t timestamp;                 // 响应时间戳
} nr90_response_t;

// AT命令定义结构体
typedef struct {
    const char* command;              // AT命令
    const char* description;          // 命令描述
    int timeout;                      // 超时时间(秒)
    int is_critical;                  // 是否为关键命令
} nr90_at_command_t;

// 函数声明

// 设备管理函数
nr90_error_t nr90_device_init(nr90_device_t* device, const char* device_path);
nr90_error_t nr90_device_open(nr90_device_t* device);
nr90_error_t nr90_device_configure(nr90_device_t* device);
nr90_error_t nr90_device_close(nr90_device_t* device);
void nr90_device_cleanup(nr90_device_t* device);

// 串口通信函数
nr90_error_t nr90_send_command(nr90_device_t* device, const char* command);
nr90_error_t nr90_read_response(nr90_device_t* device, nr90_response_t* response, int timeout);
nr90_error_t nr90_send_at_command(nr90_device_t* device, const char* command, 
                                  nr90_response_t* response, int timeout);

// AT命令验证函数
nr90_error_t nr90_test_basic_communication(nr90_device_t* device);
nr90_error_t nr90_get_device_info(nr90_device_t* device);
nr90_error_t nr90_check_network_status(nr90_device_t* device);
nr90_error_t nr90_verify_5g_capability(nr90_device_t* device);
nr90_error_t nr90_run_full_test(nr90_device_t* device);

// 工具函数
void nr90_log(log_level_t level, const char* format, ...);
void nr90_set_log_level(log_level_t level);
const char* nr90_error_string(nr90_error_t error);
void nr90_print_response(const nr90_response_t* response);
int nr90_is_response_ok(const char* response);
int nr90_is_response_error(const char* response);



// 信号处理函数
void nr90_signal_handler(int sig);
void nr90_setup_signal_handlers(void);

// 全局变量声明
extern volatile sig_atomic_t g_interrupted;
extern log_level_t g_log_level;

#endif // NR90_SERIAL_COMM_H
