#!/bin/bash

# =============================================================================
# Git更新脚本 - 拉取远程仓库更新，同时保留本地未提交内容
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 获取当前目录
CURRENT_DIR="$(pwd)"

# 打印标题
echo -e "${CYAN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    Git 更新脚本                              ║"
echo "║          拉取远程仓库更新，同时保留本地未提交内容            ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# 检查是否在git仓库中
if [ ! -d ".git" ]; then
    echo -e "${RED}错误: 当前目录不是git仓库的根目录${NC}"
    echo "请在git仓库的根目录运行此脚本"
    exit 1
fi

# 显示当前仓库状态
echo -e "${BLUE}当前仓库状态:${NC}"
git status -s

# 检查是否有未提交的更改
if [ -z "$(git status --porcelain)" ]; then
    echo -e "${GREEN}本地没有未提交的更改，直接执行pull操作...${NC}"
    git pull
    echo -e "${GREEN}更新完成!${NC}"
    exit 0
fi

# 询问是否继续
echo -e "${YELLOW}检测到本地有未提交的更改。${NC}"
read -p "是否继续更新操作? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}操作已取消${NC}"
    exit 0
fi

# 获取当前分支名
CURRENT_BRANCH=$(git symbolic-ref --short HEAD)
echo -e "${BLUE}当前分支: ${CURRENT_BRANCH}${NC}"

# 保存未提交的更改
echo -e "${BLUE}保存本地未提交的更改...${NC}"
git stash save "自动保存的更改 - $(date '+%Y-%m-%d %H:%M:%S')"

# 检查stash是否成功
if [ $? -ne 0 ]; then
    echo -e "${RED}错误: 无法保存本地更改${NC}"
    exit 1
fi

# 拉取远程更新
echo -e "${BLUE}拉取远程更新...${NC}"
git pull

# 检查pull是否成功
if [ $? -ne 0 ]; then
    echo -e "${RED}错误: 拉取远程更新失败${NC}"
    echo -e "${YELLOW}正在恢复本地更改...${NC}"
    git stash pop
    echo -e "${YELLOW}已恢复本地更改，但远程更新失败${NC}"
    exit 1
fi

# 恢复本地更改
echo -e "${BLUE}恢复本地未提交的更改...${NC}"
git stash pop

# 检查是否有冲突
if [ $? -ne 0 ]; then
    echo -e "${RED}警告: 恢复本地更改时发生冲突${NC}"
    echo -e "${YELLOW}请手动解决冲突后继续${NC}"
    echo -e "${BLUE}冲突文件:${NC}"
    git status -s | grep "^UU"
    exit 1
fi

# 显示最终状态
echo -e "${GREEN}更新完成! 已成功拉取远程更新并恢复本地更改${NC}"
echo -e "${BLUE}当前仓库状态:${NC}"
git status -s

# 显示最近的提交记录
echo -e "${BLUE}最近的提交记录:${NC}"
git log --oneline -n 5

echo
echo -e "${GREEN}操作成功完成!${NC}" 