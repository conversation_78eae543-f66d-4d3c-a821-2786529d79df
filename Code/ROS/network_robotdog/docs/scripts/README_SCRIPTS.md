# 网络模块自动运行脚本使用指南

## 📋 脚本概览

本项目提供了三个自动化脚本，用于简化网络模块的部署和运行：

1. **`auto_run.sh`** - 完整的自动化运行脚本（功能最全面）
2. **`quick_start.sh`** - 快速启动脚本（日常使用推荐）
3. **`install_service.sh`** - 系统服务安装脚本（开机自启动）

## 🚀 快速开始

### 方法一：快速启动（推荐日常使用）

```bash
# 给脚本添加执行权限（如果还没有）
chmod +x quick_start.sh

# 运行快速启动脚本
./quick_start.sh
```

这个脚本会：
- 自动检测ROS环境
- 检查并构建工作空间
- 自动寻找并启动网络模块
- 记录日志并显示状态

### 方法二：完整自动化模式

```bash
# 给脚本添加执行权限
chmod +x auto_run.sh

# 自动模式（构建+启动+监控+自动重启）
./auto_run.sh -a
```

### 方法三：系统服务（开机自启动）

```bash
# 给脚本添加执行权限
chmod +x install_service.sh

# 安装系统服务
./install_service.sh
```

## 📖 详细使用说明

### auto_run.sh - 完整自动化脚本

#### 基本用法

```bash
./auto_run.sh [选项]
```

#### 可用选项

| 选项 | 长选项 | 说明 |
|------|--------|------|
| `-h` | `--help` | 显示帮助信息 |
| `-l FILE` | `--launch FILE` | 指定launch文件 |
| `-r DISTRO` | `--ros-distro DISTRO` | 指定ROS版本 (humble/foxy) |
| `-b` | `--build` | 构建工作空间 |
| `-s` | `--start` | 启动网络模块 |
| `-t` | `--stop` | 停止网络模块 |
| `-R` | `--restart` | 重启网络模块 |
| `-m` | `--monitor` | 监控网络模块状态 |
| `-a` | `--auto` | 自动模式 (构建+启动+监控) |
| `--status` | | 显示网络模块状态 |

#### 使用示例

```bash
# 查看帮助
./auto_run.sh -h

# 自动模式启动
./auto_run.sh -a

# 启动指定的launch文件
./auto_run.sh -l network.py -s

# 使用humble版本构建
./auto_run.sh -r humble -b

# 查看网络模块状态
./auto_run.sh --status

# 停止网络模块
./auto_run.sh -t

# 重启网络模块
./auto_run.sh -R
```

### quick_start.sh - 快速启动脚本

#### 特点
- 自动检测网络模块launch文件
- 自动环境检测和构建
- 简化的操作流程
- 适合日常使用

### install_service.sh - 系统服务安装

#### 功能
- 创建systemd服务
- 设置网络模块开机自启动
- 自动重启和错误恢复
- 日志管理

#### 安装后管理命令

```bash
# 启动服务
sudo systemctl start network-module

# 停止服务
sudo systemctl stop network-module

# 重启服务
sudo systemctl restart network-module

# 查看状态
sudo systemctl status network-module

# 查看日志
sudo journalctl -u network-module -f

# 禁用服务（取消开机自启动）
sudo systemctl disable network-module
```

## 🔧 配置说明

### 环境要求

- **操作系统**: Ubuntu 20.04+ 或 WSL2
- **ROS版本**: ROS2 Foxy 或 Humble
- **Python版本**: 3.8 (Foxy) 或 3.10 (Humble)

### 目录结构

```
network_robotdog/
├── auto_run.sh              # 完整自动化脚本
├── quick_start.sh           # 快速启动脚本
├── install_service.sh       # 系统服务安装脚本
├── log/                     # 日志目录
├── xiaoli_application_ros2/ # ROS2工作空间
│   ├── src/
│   │   └── launch_package/
│   │       └── launch/
│   │           └── network.py  # 网络模块launch文件
│   ├── build/
│   └── install/
└── README_SCRIPTS.md        # 本文档
```

### 日志文件

- **位置**: `./log/`
- **格式**: `network_launch_YYYYMMDD_HHMMSS.log`
- **查看实时日志**: `tail -f log/network_launch_*.log`

## 🛠️ 故障排除

### 常见问题

#### 1. ROS环境未找到
```bash
# 检查ROS安装
ls /opt/ros/

# 手动设置环境
source /opt/ros/humble/setup.bash
```

#### 2. 构建失败
```bash
# 清理构建文件
cd xiaoli_application_ros2
rm -rf build/ install/

# 重新构建
colcon build --symlink-install
```

#### 3. 权限问题
```bash
# 给脚本添加执行权限
chmod +x *.sh
```

#### 4. 服务启动失败
```bash
# 查看服务日志
sudo journalctl -u network-module -n 50

# 检查服务状态
sudo systemctl status network-module
```

### 调试模式

```bash
# 使用详细输出模式
./auto_run.sh -l network.py -s 2>&1 | tee debug.log

# 查看实时日志
tail -f log/network_launch_*.log
```

## 📊 监控和维护

### 系统状态检查

```bash
# 查看网络模块状态
./auto_run.sh --status

# 查看网络相关ROS节点
ros2 node list | grep network

# 查看网络相关ROS话题
ros2 topic list | grep network

# 查看网络相关ROS服务
ros2 service list | grep network
```

### 网络状态监控

```bash
# 查看网络状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getNetworkStatus\"}'}"

# 监听网络状态变化
ros2 topic echo /network_status
```

## 🔄 更新和维护

### 更新脚本

```bash
# 备份当前脚本
cp auto_run.sh auto_run.sh.backup

# 更新脚本后重新设置权限
chmod +x *.sh
```

### 清理日志

```bash
# 清理旧日志文件（保留最近7天）
find log/ -name "*.log" -mtime +7 -delete
```

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 检查系统环境是否符合要求
3. 确认ROS2安装和配置是否正确
4. 查看项目文档中的故障排除部分

---

**注意**: 这些脚本专注于网络模块的启动和管理，请确保您的系统环境满足基本要求。 