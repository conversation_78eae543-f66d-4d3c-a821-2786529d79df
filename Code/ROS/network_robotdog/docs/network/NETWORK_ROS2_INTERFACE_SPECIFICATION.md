# Network模块ROS2接口标准规范

## 📋 文档概述

本文档定义了network模块对外提供的所有ROS2接口规范，包括服务接口、话题接口、消息格式和错误码标准。

**版本**: v2.0  
**更新时间**: 2025-07-27  
**适用模块**: network_node  
**节点名称**: `/network_node`

---

## 🔧 服务接口规范

### 1. 网络控制服务 (NetCtrl)

**服务名称**: `/homi_speech/network_service`  
**服务类型**: `homi_speech_interface/srv/NetCtrl`  
**功能描述**: 网络状态查询、控制和WiFi管理的主要服务接口

#### 服务定义
```idl
# 请求
string data

---
# 响应  
int32 error_code
string result
```

#### 支持的命令列表

| 命令 | 功能描述 | 必需参数 | 可选参数 | 错误码 |
|------|---------|---------|---------|--------|
| `getNetworkStatus` | 获取当前网络状态 | 无 | 无 | 200/500 |
| `setNetworkStatus` | 设置网络开关状态 | 无 | `networkStatus` | 200/400/500 |
| `getDnsStatus` | 获取DNS状态信息 | 无 | 无 | 200/500 |
| `switchDnsServer` | 切换DNS服务器 | `dnsServer` | 无 | 200/400/500 |
| `refreshDnsServers` | 刷新并选择最佳DNS | 无 | 无 | 200/500 |
| `getNetworkDiagnostic` | 获取网络诊断信息 | 无 | `interface` | 200/500 |
| `performDnsHealthCheck` | 执行DNS健康检查 | 无 | `interface` | 200/500 |
| `getBindStatus` | 获取用户绑定状态 | 无 | 无 | 200/500 |
| **WiFi管理命令** | | | | |
| `scanWifiNetworks` | 扫描可用WiFi网络 | 无 | 无 | 200/500 |
| `connectWifi` | 连接WiFi网络 | `ssid` | `password` | 200/400/401/402/403/408/500 |
| `disconnectWifi` | 断开WiFi连接 | 无 | 无 | 200/500 |
| `getWifiConnectionStatus` | 获取WiFi连接状态 | 无 | 无 | 200/500 |
| `getWifiHardwareStatus` | 获取WiFi硬件状态 | 无 | 无 | 200/500 |

### 2. 机器人控制服务 (SIGCData)

**服务名称**: `/homi_speech/robot_control_service`  
**服务类型**: `homi_speech_interface/srv/SIGCData`  
**功能描述**: AP热点开关控制服务

#### 服务定义
```idl
# 请求
string data

---
# 响应
int32 error_code
```

#### 支持的命令

| 命令 | 功能描述 | 错误码 |
|------|---------|--------|
| `openap_notify` | 开启AP热点 | 0/1 |
| `closeap_notify` | 关闭AP热点 | 0/1 |

---

## 📡 话题接口规范

### 订阅话题

| 话题名称 | 消息类型 | 功能描述 | 队列大小 | 回调函数 |
|---------|---------|---------|---------|---------|
| `/andlink_network` | `std_msgs/msg/String` | 用户绑定控制消息 | 10 | `bind_notify_callback` |

#### 支持的绑定控制消息
- `notify_userbind_start`: 开始用户绑定，暂停网络状态检测
- `notify_userbind_end`: 结束用户绑定，恢复网络状态检测

### 发布话题

| 话题名称 | 消息类型 | 功能描述 | 发布频率 | 数据格式 |
|---------|---------|---------|---------|---------|
| `/network_status` | `std_msgs/msg/String` | 基础网络状态 | 事件触发 | JSON |
| `/internet_conflict` | `std_msgs/msg/String` | 网络冲突信息 | 事件触发 | 字符串 |
| `/dns_health_status` | `std_msgs/msg/String` | DNS健康状态和增强网络信息 | 定时检查(20秒间隔) | JSON |

---

## 📊 消息格式规范

### 请求消息格式

#### 基础请求格式
```json
{
  "command": "命令名称"
}
```

#### 带参数请求格式
```json
{
  "command": "命令名称",
  "参数名1": "参数值1",
  "参数名2": "参数值2"
}
```

### 响应消息格式

#### 标准响应格式
```json
{
  "error_code": 200,
  "result": "JSON格式的结果数据或字符串"
}
```

### 具体命令的消息格式

#### 1. getNetworkStatus 响应
```json
{
  "wifiState": "on|off",
  "mobileDataState": "on|off", 
  "wifiName": "WiFi网络名称",
  "isWifiConnect": "true|false",
  "isInternetConnect": "true|false",
  "bind_status": {
    "is_bound": true,
    "user_id": "用户ID",
    "bind_time": "绑定时间"
  }
}
```

#### 2. setNetworkStatus 请求
```json
{
  "command": "setNetworkStatus",
  "networkStatus": {
    "wifiState": "on|off",      // 可选
    "mobileDataState": "on|off" // 可选
  }
}
```

#### 3. scanWifiNetworks 响应
```json
{
  "networks": [
    {
      "ssid": "WiFi名称",
      "bssid": "MAC地址", 
      "signal_level": -45,
      "security": "WPA2",
      "quality": "excellent|good|fair|poor"
    }
  ],
  "count": 网络数量
}
```

#### 4. connectWifi 请求
```json
{
  "command": "connectWifi",
  "ssid": "WiFi网络名称",
  "password": "WiFi密码"  // 开放网络时可省略
}
```

#### 5. connectWifi 响应
```json
{
  "success": true,
  "message": "连接结果描述",
  "error_code": 200
}
```

#### 6. getWifiConnectionStatus 响应
```json
{
  "interface": "wlan0",
  "is_up": true,
  "current_ssid": "当前连接的WiFi",
  "has_internet": true,
  "signal_quality": "good",
  "signal_strength": -45,
  "ip_address": "*************",
  "mac_address": "aa:bb:cc:dd:ee:ff"
}
```

#### 7. getDnsStatus 响应
```json
{
  "current_dns": "*********",
  "failure_count": 0,
  "last_check_time": 1640995200,
  "available_servers": ["*********", "************"],
  "server_status": {
    "*********": {
      "status": "good",
      "response_time": 15.5
    }
  }
}
```

---

## ⚠️ 错误码规范

### HTTP风格错误码

| 错误码 | 含义 | 适用场景 |
|--------|------|---------|
| **成功状态** | | |
| 200 | 成功 | 操作成功完成 |
| **客户端错误** | | |
| 400 | 请求参数错误 | JSON格式错误、缺少必需参数 |
| 401 | 认证失败 | WiFi密码错误 |
| 402 | 连接后验证失败 | WiFi连接成功但验证失败 |
| 403 | 操作被禁止 | WiFi连接失败（非密码问题） |
| 408 | 请求超时 | WiFi连接超时 |
| **服务器错误** | | |
| 500 | 内部服务器错误 | 系统异常、命令执行失败 |

### SIGCData服务错误码

| 错误码 | 含义 |
|--------|------|
| 0 | 成功 |
| 1 | 失败或未知命令 |

---

## 🔒 安全和权限

### 权限要求
- 所有网络操作需要sudo权限
- WiFi密码在日志中不会明文显示
- 系统配置文件的读写需要适当权限

### 安全考虑
- 连接操作有超时限制，防止长时间阻塞
- 状态验证确保操作的实际效果
- 错误信息不泄露敏感系统信息

---

## 🎯 设备兼容性

### 支持的设备类型
- **unitree设备**: 使用wpa_cli进行WiFi管理
- **ysc设备**: 使用nmcli进行WiFi管理

### 网络接口配置
```yaml
network_node:
  ros__parameters:
    wifi_connect_interface: "wlp1s0"        # WiFi接口
    p2p_connect_interface: "wlan1"          # AP热点接口  
    mobile_connect_interface: "enx00e04c6801d0"  # 蜂窝网络接口
    device_type: "unitree"                  # 设备类型
```

---

## 📝 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|---------|
| v2.0 | 2025-07-27 | 添加WiFi管理接口，完善错误码规范 |
| v1.0 | 2025-01-01 | 初始版本，基础网络控制接口 |

---

## 🚀 使用示例

### Python客户端示例

```python
import rclpy
from rclpy.node import Node
from homi_speech_interface.srv import NetCtrl, SIGCData
import json

class NetworkClient(Node):
    def __init__(self):
        super().__init__('network_client')
        self.net_client = self.create_client(NetCtrl, '/homi_speech/network_service')
        self.robot_client = self.create_client(SIGCData, '/homi_speech/robot_control_service')

    def get_network_status(self):
        """获取网络状态"""
        request = NetCtrl.Request()
        request.data = json.dumps({"command": "getNetworkStatus"})

        future = self.net_client.call_async(request)
        rclpy.spin_until_future_complete(self, future)

        if future.result():
            response = future.result()
            if response.error_code == 200:
                return json.loads(response.result)
        return None

    def connect_wifi(self, ssid, password=None):
        """连接WiFi网络"""
        request = NetCtrl.Request()
        command_data = {"command": "connectWifi", "ssid": ssid}
        if password:
            command_data["password"] = password
        request.data = json.dumps(command_data)

        future = self.net_client.call_async(request)
        rclpy.spin_until_future_complete(self, future)

        if future.result():
            response = future.result()
            return response.error_code, json.loads(response.result)
        return None, None
```

### Shell命令示例

```bash
# 获取网络状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \
  "{data: '{\"command\": \"getNetworkStatus\"}'}"

# 设置网络状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \
  "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"wifiState\": \"on\", \"mobileDataState\": \"off\"}}'}"

# 扫描WiFi网络
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \
  "{data: '{\"command\": \"scanWifiNetworks\"}'}"

# 连接WiFi
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \
  "{data: '{\"command\": \"connectWifi\", \"ssid\": \"MyWiFi\", \"password\": \"mypassword\"}'}"

# 开启AP热点
ros2 service call /homi_speech/robot_control_service homi_speech_interface/srv/SIGCData \
  "{data: 'openap_notify'}"
```

---

## 📋 最佳实践

### 1. 错误处理
```python
def safe_network_call(self, command_data):
    """安全的网络服务调用"""
    try:
        request = NetCtrl.Request()
        request.data = json.dumps(command_data)

        future = self.net_client.call_async(request)
        rclpy.spin_until_future_complete(self, future, timeout_sec=30.0)

        if future.result():
            response = future.result()
            if response.error_code == 200:
                return True, json.loads(response.result)
            else:
                self.get_logger().error(f"服务调用失败: {response.error_code}")
                return False, response.result
        else:
            self.get_logger().error("服务调用超时")
            return False, "Service call timeout"

    except Exception as e:
        self.get_logger().error(f"服务调用异常: {e}")
        return False, str(e)
```

### 2. 状态监控
```python
def setup_status_monitoring(self):
    """设置状态监控"""
    # 监听网络状态变化
    self.status_sub = self.create_subscription(
        String, '/network_status', self.network_status_callback, 10)

    # 监听DNS健康状态
    self.dns_sub = self.create_subscription(
        String, '/dns_health_status', self.dns_health_callback, 10)

def network_status_callback(self, msg):
    """网络状态变化回调"""
    try:
        status = json.loads(msg.data)
        self.get_logger().info(f"网络状态更新: {status}")
    except json.JSONDecodeError:
        self.get_logger().warning(f"无法解析网络状态: {msg.data}")
```

### 3. 超时和重试
```python
def connect_wifi_with_retry(self, ssid, password, max_retries=3):
    """带重试的WiFi连接"""
    for attempt in range(max_retries):
        self.get_logger().info(f"WiFi连接尝试 {attempt + 1}/{max_retries}")

        success, result = self.connect_wifi(ssid, password)
        if success and result.get('success'):
            return True, result

        if attempt < max_retries - 1:
            time.sleep(5)  # 等待5秒后重试

    return False, "连接失败，已达到最大重试次数"
```

---

## 🔍 调试和故障排除

### 检查服务状态
```bash
# 检查节点是否运行
ros2 node list | grep network

# 检查服务是否可用
ros2 service list | grep homi_speech

# 检查服务类型
ros2 service type /homi_speech/network_service

# 查看服务接口定义
ros2 interface show homi_speech_interface/srv/NetCtrl
```

### 监控话题消息
```bash
# 监听网络状态
ros2 topic echo /network_status

# 监听DNS健康状态
ros2 topic echo /dns_health_status

# 检查话题发布频率
ros2 topic hz /dns_health_status
```

### 常见问题解决

1. **服务调用超时**
   - 检查network_node是否正常运行
   - 确认网络接口配置正确
   - 查看节点日志排查具体错误

2. **WiFi连接失败**
   - 确认SSID和密码正确
   - 检查WiFi接口是否启用
   - 查看错误码确定具体失败原因

3. **权限问题**
   - 确保节点以适当权限运行
   - 检查sudo配置是否正确

---

## 📚 相关文档

- [Network模块README](./README.md)
- [WiFi接口功能总结](./WIFI_INTERFACES_SUMMARY.md)
- [服务和发布者测试文档](./test/SERVICE_AND_PUBLISHER_TESTING.md)

---

*本规范文档定义了network模块的完整ROS2接口标准，为开发者提供统一的接口调用规范。*
