# 网络模块快速入门指南

## 🚀 快速开始

### 1. 启动网络模块

使用快速启动脚本：
```bash
# 给脚本添加执行权限
chmod +x quick_start.sh

# 运行快速启动脚本
./quick_start.sh
```

### 2. 检查网络模块状态

```bash
# 查看网络模块状态
./auto_run.sh --status

# 查看网络模块节点
ros2 node list | grep network
```

### 3. 常用网络命令

```bash
# 获取网络状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getNetworkStatus\"}'}"

# 扫描WiFi网络
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"scanWifiNetworks\"}'}"

# 连接WiFi
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"connectWifi\", \"ssid\": \"WiFi名称\", \"password\": \"密码\"}'}"
```

### 4. 停止网络模块

```bash
# 停止网络模块
./auto_run.sh -t
```

## 📋 常见操作

### WiFi管理

| 操作 | 命令 |
|------|------|
| 扫描WiFi | `./auto_run.sh -s` 启动后，使用 `ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"scanWifiNetworks\"}'}"` |
| 连接WiFi | `ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"connectWifi\", \"ssid\": \"WiFi名称\", \"password\": \"密码\"}'}"` |
| 断开WiFi | `ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"disconnectWifi\"}'}"` |
| 获取WiFi状态 | `ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getWifiConnectionStatus\"}'}"` |

### 网络状态监控

| 操作 | 命令 |
|------|------|
| 监控网络状态 | `ros2 topic echo /network_status` |
| 监控DNS状态 | `ros2 topic echo /dns_health_status` |
| 获取网络诊断 | `ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getNetworkDiagnostic\"}'}"` |

## 🔧 故障排除

### 常见问题

1. **网络模块无法启动**
   - 检查ROS环境: `source /opt/ros/humble/setup.bash`
   - 检查工作空间: `cd xiaoli_application_ros2 && source install/setup.bash`

2. **WiFi连接失败**
   - 检查WiFi硬件: `ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getWifiHardwareStatus\"}'}"` 
   - 检查错误码: 401=密码错误, 408=超时

3. **服务调用失败**
   - 检查服务是否可用: `ros2 service list | grep network`
   - 检查节点是否运行: `ros2 node list | grep network`

### 日志查看

```bash
# 查看网络模块日志
tail -f log/network_launch_*.log
```

## 📚 更多资源

- **详细文档**: 查看 [网络模块综合文档](./NETWORK_MODULE_DOCUMENTATION.md)
- **接口规范**: 查看 [Network模块ROS2接口标准规范](./NETWORK_ROS2_INTERFACE_SPECIFICATION.md)
- **命令速查**: 查看 [Network快速参考](./NETWORK_QUICK_REFERENCE.md)
- **脚本使用**: 查看 [自动运行脚本使用指南](../scripts/README_SCRIPTS.md)

---

*网络模块快速入门指南 v1.0 | 更新时间: 2023-07-29* 