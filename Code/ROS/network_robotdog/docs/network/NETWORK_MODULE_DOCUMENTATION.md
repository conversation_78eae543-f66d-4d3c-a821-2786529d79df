# 网络模块综合文档

## 📋 目录

1. [模块概述](#模块概述)
2. [接口规范](#接口规范)
3. [常用命令](#常用命令)
4. [WiFi管理功能](#wifi管理功能)
5. [自动运行脚本](#自动运行脚本)
6. [故障排除](#故障排除)
7. [测试验证](#测试验证)
8. [相关文档](#相关文档)

---

## 模块概述

网络模块(`network_node`)是机器人系统中负责网络管理的核心组件，提供了完整的网络状态管理、WiFi连接控制、移动数据管理和DNS配置等功能。

### 主要功能

- **网络状态管理**: 获取和设置网络状态（WiFi、移动数据）
- **WiFi管理**: 扫描、连接、断开WiFi网络，获取连接状态
- **DNS管理**: 获取DNS状态、切换DNS服务器、刷新DNS配置
- **AP热点控制**: 开启和关闭AP热点
- **网络诊断**: 提供网络诊断和故障排除功能

### 节点信息

- **节点名称**: `/network_node`
- **服务名称**: `/homi_speech/network_service`
- **主要话题**: `/network_status`, `/dns_health_status`, `/internet_conflict`

---

## 接口规范

### 服务调用模板

基础命令格式：
```bash
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \
  "{data: '{\"command\": \"命令名称\"}'}"
```

带参数命令格式：
```bash
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \
  "{data: '{\"command\": \"命令名称\", \"参数名\": \"参数值\"}'}"
```

### 响应格式

所有命令响应都遵循统一格式：
```json
{
  "error_code": 200,
  "result": "JSON格式的结果数据"
}
```

### 错误码定义

| 错误码 | 含义 | 常见原因 |
|--------|------|---------|
| 200 | 成功 | 操作正常完成 |
| 400 | 参数错误 | JSON格式错误、缺少参数 |
| 401 | 密码错误 | WiFi密码不正确 |
| 402 | 连接后验证失败 | 连接成功但验证失败 |
| 403 | 连接失败 | 其他连接失败原因 |
| 408 | 超时 | WiFi连接超时 |
| 500 | 系统错误 | 网络接口异常、权限不足 |

---

## 常用命令

### 网络状态管理

```bash
# 获取网络状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getNetworkStatus\"}'}"

# 开启WiFi，关闭移动数据
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"wifiState\": \"on\", \"mobileDataState\": \"off\"}}'}"

# 开启移动数据，关闭WiFi
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"wifiState\": \"off\", \"mobileDataState\": \"on\"}}'}"
```

### WiFi管理

```bash
# 扫描WiFi网络
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"scanWifiNetworks\"}'}"

# 连接WiFi（有密码）
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"connectWifi\", \"ssid\": \"WiFi名称\", \"password\": \"密码\"}'}"

# 连接开放WiFi
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"connectWifi\", \"ssid\": \"开放WiFi名称\"}'}"

# 断开WiFi
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"disconnectWifi\"}'}"

# 获取WiFi连接状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getWifiConnectionStatus\"}'}"

# 获取WiFi硬件状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getWifiHardwareStatus\"}'}"
```

### DNS管理

```bash
# 获取DNS状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getDnsStatus\"}'}"

# 切换DNS服务器
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"switchDnsServer\", \"dnsServer\": \"8.8.8.8\"}'}"

# 刷新DNS服务器
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"refreshDnsServers\"}'}"

# DNS健康检查
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"performDnsHealthCheck\"}'}"
```

### AP热点控制

```bash
# 开启AP热点
ros2 service call /homi_speech/robot_control_service homi_speech_interface/srv/SIGCData "{data: 'openap_notify'}"

# 关闭AP热点
ros2 service call /homi_speech/robot_control_service homi_speech_interface/srv/SIGCData "{data: 'closeap_notify'}"
```

### 话题监控

```bash
# 监听网络状态变化
ros2 topic echo /network_status

# 监听DNS健康状态
ros2 topic echo /dns_health_status

# 监听网络冲突
ros2 topic echo /internet_conflict
```

---

## WiFi管理功能

### 功能特点

- 支持WiFi网络扫描、连接、断开和状态获取
- 适配不同设备类型（unitree使用wpa_cli，ysc使用nmcli）
- 完整的错误处理和状态验证
- 支持有密码和开放网络的连接

### 技术实现

- `scan_wifi_networks()`: WiFi网络扫描
- `connect_wifi_network(ssid, password)`: WiFi网络连接
- `disconnect_wifi()`: WiFi连接断开
- `get_wifi_connection_status()`: 连接状态获取
- `get_wifi_hardware_status()`: 硬件状态获取

### 安全考虑

1. **权限管理**: 所有WiFi操作需要sudo权限
2. **密码处理**: 密码参数在日志中不会明文显示
3. **超时控制**: 连接操作有超时限制，防止长时间阻塞
4. **状态验证**: 连接后会验证实际连接状态

---

## 自动运行脚本

为简化网络模块的部署和运行，提供了以下自动化脚本：

### 快速启动脚本 (quick_start.sh)

```bash
# 给脚本添加执行权限（如果还没有）
chmod +x quick_start.sh

# 运行快速启动脚本
./quick_start.sh
```

这个脚本会：
- 自动检测ROS环境
- 检查并构建工作空间
- 自动寻找并启动网络模块
- 记录日志并显示状态

### 完整自动化脚本 (auto_run.sh)

```bash
# 查看帮助
./auto_run.sh -h

# 自动模式启动
./auto_run.sh -a

# 启动指定的launch文件
./auto_run.sh -l network.py -s

# 查看网络模块状态
./auto_run.sh --status

# 停止网络模块
./auto_run.sh -t

# 重启网络模块
./auto_run.sh -R
```

### 系统服务安装脚本 (install_service.sh)

```bash
# 安装系统服务
./install_service.sh
```

安装后管理命令：

```bash
# 启动服务
sudo systemctl start network-module

# 停止服务
sudo systemctl stop network-module

# 重启服务
sudo systemctl restart network-module

# 查看状态
sudo systemctl status network-module

# 查看日志
sudo journalctl -u network-module -f
```

---

## 故障排除

### 常见问题

#### 1. 网络模块无法启动

```bash
# 检查ROS环境
source /opt/ros/humble/setup.bash

# 检查工作空间
cd xiaoli_application_ros2
source install/setup.bash

# 检查节点状态
ros2 node list | grep network
```

#### 2. WiFi连接失败

```bash
# 检查WiFi硬件状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getWifiHardwareStatus\"}'}"

# 检查可用WiFi网络
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"scanWifiNetworks\"}'}"

# 检查错误码，401表示密码错误，408表示连接超时
```

#### 3. 服务调用失败

```bash
# 检查服务是否可用
ros2 service list | grep network

# 检查服务类型
ros2 service type /homi_speech/network_service

# 检查节点是否运行
ros2 node list | grep network
```

#### 4. 系统服务问题

```bash
# 查看服务日志
sudo journalctl -u network-module -n 50

# 检查服务状态
sudo systemctl status network-module
```

### 日志查看

```bash
# 查看网络模块日志
tail -f log/network_launch_*.log

# 使用详细输出模式
./auto_run.sh -l network.py -s 2>&1 | tee debug.log
```

---

## 测试验证

### 测试脚本

```bash
# 运行WiFi接口测试
cd xiaoli_application_ros2/src/network
python3 test/test_wifi_interfaces.py

# 运行使用示例
python3 examples/wifi_management_example.py

# 交互式演示
python3 examples/wifi_management_example.py --interactive

# 一键测试所有功能
./test/test_network_services.sh
```

### 手动验证

```bash
# 检查节点是否正常运行
ros2 node list | grep network

# 测试基本服务
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getNetworkStatus\"}'}"

# 监控网络状态话题
ros2 topic echo /network_status
```

---

## 相关文档

1. **[Network模块ROS2接口标准规范](./NETWORK_ROS2_INTERFACE_SPECIFICATION.md)** - 完整的接口规范文档
2. **[Network快速参考](./NETWORK_QUICK_REFERENCE.md)** - 常用命令速查手册
3. **[WiFi接口功能总结](./WIFI_INTERFACES_SUMMARY.md)** - WiFi功能实现详情
4. **[Network模块文档索引](./NETWORK_DOCUMENTATION_INDEX.md)** - 文档导航
5. **[自动运行脚本使用指南](../scripts/README_SCRIPTS.md)** - 脚本使用说明

---

*网络模块综合文档 v1.0 | 更新时间: 2023-07-29* 