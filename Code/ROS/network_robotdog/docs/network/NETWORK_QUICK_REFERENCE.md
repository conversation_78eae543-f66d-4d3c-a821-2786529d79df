# Network模块ROS2接口快速参考

## 🚀 快速开始

### 服务调用模板
```bash
# 基础命令模板
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \
  "{data: '{\"command\": \"命令名称\"}'}"

# 带参数命令模板  
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \
  "{data: '{\"command\": \"命令名称\", \"参数名\": \"参数值\"}'}"
```

## 📋 常用命令速查

### 网络状态管理
```bash
# 获取网络状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getNetworkStatus\"}'}"

# 开启WiFi，关闭移动数据
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"wifiState\": \"on\", \"mobileDataState\": \"off\"}}'}"

# 开启移动数据，关闭WiFi
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"wifiState\": \"off\", \"mobileDataState\": \"on\"}}'}"
```

### WiFi管理
```bash
# 扫描WiFi网络
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"scanWifiNetworks\"}'}"

# 连接WiFi（有密码）
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"connectWifi\", \"ssid\": \"WiFi名称\", \"password\": \"密码\"}'}"

# 连接开放WiFi
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"connectWifi\", \"ssid\": \"开放WiFi名称\"}'}"

# 断开WiFi
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"disconnectWifi\"}'}"

# 获取WiFi连接状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getWifiConnectionStatus\"}'}"
```

### DNS管理
```bash
# 获取DNS状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getDnsStatus\"}'}"

# 切换DNS服务器
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"switchDnsServer\", \"dnsServer\": \"8.8.8.8\"}'}"

# 刷新DNS服务器
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"refreshDnsServers\"}'}"

# DNS健康检查
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"performDnsHealthCheck\"}'}"
```

### AP热点控制
```bash
# 开启AP热点
ros2 service call /homi_speech/robot_control_service homi_speech_interface/srv/SIGCData "{data: 'openap_notify'}"

# 关闭AP热点
ros2 service call /homi_speech/robot_control_service homi_speech_interface/srv/SIGCData "{data: 'closeap_notify'}"
```

## 📊 错误码速查

| 错误码 | 含义 | 常见原因 |
|--------|------|---------|
| 200 | 成功 | 操作正常完成 |
| 400 | 参数错误 | JSON格式错误、缺少参数 |
| 401 | 密码错误 | WiFi密码不正确 |
| 408 | 超时 | WiFi连接超时 |
| 500 | 系统错误 | 网络接口异常、权限不足 |

## 📡 话题监控

```bash
# 监听网络状态变化
ros2 topic echo /network_status

# 监听DNS健康状态
ros2 topic echo /dns_health_status

# 监听网络冲突
ros2 topic echo /internet_conflict
```

## 🔍 调试命令

```bash
# 检查节点状态
ros2 node list | grep network

# 检查服务状态
ros2 service list | grep homi_speech

# 检查话题状态
ros2 topic list | grep -E "(network|dns|conflict)"

# 查看节点信息
ros2 node info /network_node
```

## 🐍 Python快速示例

```python
import rclpy
from rclpy.node import Node
from homi_speech_interface.srv import NetCtrl
import json

class QuickNetworkClient(Node):
    def __init__(self):
        super().__init__('quick_client')
        self.client = self.create_client(NetCtrl, '/homi_speech/network_service')
        
    def call_service(self, command_data):
        request = NetCtrl.Request()
        request.data = json.dumps(command_data)
        future = self.client.call_async(request)
        rclpy.spin_until_future_complete(self, future)
        return future.result()

# 使用示例
def main():
    rclpy.init()
    client = QuickNetworkClient()
    
    # 获取网络状态
    response = client.call_service({"command": "getNetworkStatus"})
    print(f"网络状态: {response.result}")
    
    # 扫描WiFi
    response = client.call_service({"command": "scanWifiNetworks"})
    print(f"WiFi网络: {response.result}")
    
    client.destroy_node()
    rclpy.shutdown()
```

## 📝 常用JSON格式

### 网络状态设置
```json
{
  "command": "setNetworkStatus",
  "networkStatus": {
    "wifiState": "on",
    "mobileDataState": "off"
  }
}
```

### WiFi连接
```json
{
  "command": "connectWifi", 
  "ssid": "WiFi名称",
  "password": "WiFi密码"
}
```

### DNS切换
```json
{
  "command": "switchDnsServer",
  "dnsServer": "8.8.8.8"
}
```

## 🔧 故障排除清单

- [ ] 检查network_node是否运行
- [ ] 确认ROS2环境正确设置
- [ ] 验证网络接口配置
- [ ] 检查sudo权限配置
- [ ] 查看节点日志输出
- [ ] 确认服务接口可用
- [ ] 测试基础网络连接

## 📚 更多信息

- 完整规范: [NETWORK_ROS2_INTERFACE_SPECIFICATION.md](./NETWORK_ROS2_INTERFACE_SPECIFICATION.md)
- 详细文档: [README.md](./xiaoli_application_ros2/src/network/README.md)
- WiFi功能: [WIFI_INTERFACES_SUMMARY.md](./WIFI_INTERFACES_SUMMARY.md)

---
*快速参考 v2.0 | 更新时间: 2025-07-27*
