# Network模块服务和发布者测试文档

## 📋 概述

本文档提供了通过shell命令测试network模块服务接口和发布者功能的详细指南。

## 🔧 前置条件

确保network_node正在运行：
```bash
cd xiaoli_application_ros2
./debug_network_node.sh --test --log-level DEBUG
```

## 📡 ROS2接口总览

### 服务接口
| 服务名称 | 服务类型 | 功能描述 |
|---------|---------|---------|
| `/homi_speech/network_service` | homi_speech_interface/srv/NetCtrl | 网络控制服务 |
| `/homi_speech/sigc_data_service_APP` | homi_speech_interface/srv/SIGCData | 机器人控制服务 |

### 发布话题
| 话题名称 | 消息类型 | 功能描述 |
|---------|---------|---------|
| `/internet_connect_status` | std_msgs/msg/String | 互联网连接状态 |
| `/internet_conflict` | std_msgs/msg/String | 网络冲突信息 |
| `/dns_status` | std_msgs/msg/String | DNS状态信息 |

## 🧪 测试步骤

### 步骤1: 检查服务和话题状态

```bash
# 检查所有ROS2节点
ros2 node list

# 检查服务列表
ros2 service list | grep -E "(network|homi_speech)"

# 检查话题列表
ros2 topic list | grep -E "(internet|dns|conflict)"

# 检查服务类型
ros2 service type /homi_speech/network_service
ros2 service type /homi_speech/sigc_data_service_APP

# 检查话题类型
ros2 topic type /internet_connect_status
ros2 topic type /internet_conflict
ros2 topic type /dns_status
```

### 步骤2: 测试发布者功能

#### 监听网络状态话题
```bash
# 在新终端中监听互联网连接状态
ros2 topic echo /internet_connect_status

# 在新终端中监听网络冲突信息
ros2 topic echo /internet_conflict

# 在新终端中监听DNS状态
ros2 topic echo /dns_status
```

#### 检查话题发布频率
```bash
# 检查话题发布频率
ros2 topic hz /internet_connect_status
ros2 topic hz /dns_status

# 查看话题详细信息
ros2 topic info /internet_connect_status
ros2 topic info /internet_conflict
ros2 topic info /dns_status
```

### 步骤3: 测试NetCtrl服务

#### 检查服务接口
```bash
# 查看服务接口定义
ros2 interface show homi_speech_interface/srv/NetCtrl
```

#### 调用网络控制服务
```bash
# 获取网络状态（需要JSON格式）
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getNetworkStatus\"}'}"

# 获取DNS状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getDnsStatus\"}'}"

# 获取绑定状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getBindStatus\"}'}"

# 刷新DNS服务器
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"refreshDnsServers\"}'}"

# 设置网络状态（控制WiFi和移动数据开关）
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"wifiState\": \"on\", \"mobileDataState\": \"off\"}}'}"

# 只设置WiFi状态（移动数据状态保持不变）
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"wifiState\": \"off\"}}'}"

# 只设置移动数据状态（WiFi状态保持不变）
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"mobileDataState\": \"on\"}}'}"

# 切换DNS服务器
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"switchDnsServer\", \"dnsServer\": \"8.8.8.8\"}'}"

# 执行DNS健康检查
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"performDnsHealthCheck\", \"interface\": \"wlp1s0\"}'}"
```

### 步骤4: 测试SIGCData服务

#### 检查服务接口
```bash
# 查看服务接口定义
ros2 interface show homi_speech_interface/srv/SIGCData
```

#### 测试AP控制命令
```bash
# 开启AP热点
ros2 service call /homi_speech/robot_control_service homi_speech_interface/srv/SIGCData "{data: 'openap_notify'}"

# 关闭AP热点
ros2 service call /homi_speech/robot_control_service homi_speech_interface/srv/SIGCData "{data: 'closeap_notify'}"

# 测试无效命令
ros2 service call /homi_speech/robot_control_service homi_speech_interface/srv/SIGCData "{data: 'invalid_command'}"
```

## 📊 预期结果

### 服务调用预期响应

#### NetCtrl服务
```bash
# 获取网络状态成功响应示例
requester: making request: homi_speech_interface.srv.NetCtrl_Request(data='{"command": "getNetworkStatus"}')
response:
homi_speech_interface.srv.NetCtrl_Response(error_code=200, result='{"wifiState": "on", "isWifiConnect": "true", ...}')

# 获取DNS状态成功响应示例
requester: making request: homi_speech_interface.srv.NetCtrl_Request(data='{"command": "getDnsStatus"}')
response:
homi_speech_interface.srv.NetCtrl_Response(error_code=200, result='{"current_dns": "223.5.5.5", "failure_count": 0, ...}')
```

#### SIGCData服务
```bash
# 开启AP成功响应
response:
homi_speech_interface.srv.SIGCData_Response(error_code=0)

# 关闭AP成功响应
response:
homi_speech_interface.srv.SIGCData_Response(error_code=0)

# 无效命令响应
response:
homi_speech_interface.srv.SIGCData_Response(error_code=1)
```

### 发布者消息示例

#### 互联网连接状态
```bash
data: "connected"
# 或
data: "disconnected"
```

#### 网络冲突信息
```bash
data: "wlan0_conflict_notify"
```

#### DNS状态信息
```bash
data: '{"primary_dns": "223.5.5.5", "status": "healthy", "response_time": 15}'
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 服务不存在
```bash
# 检查network_node是否运行
ros2 node list | grep network

# 如果没有，重启network_node
./debug_network_node.sh --test
```

#### 2. 服务调用失败
```bash
# 检查服务是否可用
ros2 service list | grep homi_speech

# 检查服务类型是否正确
ros2 service type /homi_speech/network_service
```

#### 3. 话题没有消息
```bash
# 检查发布者数量
ros2 topic info /internet_connect_status

# 如果发布者为0，检查network_node日志
```

#### 4. 权限问题
```bash
# 确保有网络管理权限
sudo -v

# 检查NetworkManager服务状态
systemctl status NetworkManager
```

## 📝 测试记录模板

### 服务测试记录
```
测试时间: ___________
测试人员: ___________

NetCtrl服务测试:
□ 服务存在: ___________
□ 服务调用成功: ___________
□ 响应内容正确: ___________

SIGCData服务测试:
□ openap_notify: ___________
□ closeap_notify: ___________
□ 无效命令处理: ___________
```

### 发布者测试记录
```
话题监听测试:
□ internet_connect_status: ___________
□ internet_conflict: ___________
□ dns_status: ___________

消息频率:
□ 定时发布正常: ___________
□ 事件触发发布: ___________
```

## 🎯 高级测试

### 压力测试
```bash
# 快速连续调用服务
for i in {1..10}; do
  ros2 service call /homi_speech/robot_control_service homi_speech_interface/srv/SIGCData "{data: 'openap_notify'}"
  sleep 1
done
```

### 并发测试
```bash
# 在多个终端同时监听不同话题
# 终端1
ros2 topic echo /internet_connect_status

# 终端2  
ros2 topic echo /dns_status

# 终端3
ros2 topic echo /internet_conflict
```

## 📚 相关文档

- [Network模块主文档](../README.md)
- [订阅消息验证文档](../README_SUBSCRIPTION_VERIFICATION.md)
- [快速参考手册](../QUICK_REFERENCE.md)
- [调试配置指南](../../DEBUG_README.md)

---

**注意**: 测试时请确保有适当的网络权限，某些操作可能需要sudo权限。
