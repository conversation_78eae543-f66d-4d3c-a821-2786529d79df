# 网络机器狗项目

## 📋 项目概述

网络机器狗项目是一个基于ROS2的网络管理系统，专注于机器人的网络连接、WiFi管理和网络状态监控。

## 🚀 快速开始

### 启动网络模块

```bash
# 快速启动网络模块
./quick_start.sh
```

### 查看网络状态

```bash
# 获取网络状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getNetworkStatus\"}'}"
```

## 📚 文档导航

### 文档结构

```
docs/
├── overview/                  # 概览文档
│   ├── PROJECT_DOCUMENTATION_OVERVIEW.md  # 简明文档概览
│   └── PROJECT_DOCUMENTATION_INDEX.md     # 详细文档索引
│
├── network/                   # 网络模块文档
│   ├── NETWORK_MODULE_QUICKSTART.md       # 快速入门指南
│   ├── NETWORK_MODULE_DOCUMENTATION.md    # 综合文档
│   ├── NETWORK_ROS2_INTERFACE_SPECIFICATION.md  # 接口规范
│   ├── NETWORK_QUICK_REFERENCE.md         # 命令速查
│   ├── WIFI_INTERFACES_SUMMARY.md         # WiFi功能总结
│   └── NETWORK_DOCUMENTATION_INDEX.md     # 网络文档索引
│
├── ros/                       # ROS环境文档
│   ├── DYNAMIC_ROS_SETUP_GUIDE.md         # ROS配置指南
│   └── ROS2_HUMBLE_UPGRADE_SUMMARY.md     # 升级总结
│
├── scripts/                   # 脚本文档
│   └── README_SCRIPTS.md                  # 脚本使用说明
│
└── testing/                   # 测试文档
    └── SERVICE_AND_PUBLISHER_TESTING.md   # 测试指南
```

### 快速链接

- [项目文档概览](./docs/overview/PROJECT_DOCUMENTATION_OVERVIEW.md) - 简明的文档总览
- [网络模块快速入门](./docs/network/NETWORK_MODULE_QUICKSTART.md) - 新用户入门指南
- [网络模块综合文档](./docs/network/NETWORK_MODULE_DOCUMENTATION.md) - 完整功能说明
- [自动运行脚本说明](./docs/scripts/README_SCRIPTS.md) - 脚本使用指南

## 🔧 主要功能

- **网络状态管理**: 获取和设置网络状态（WiFi、移动数据）
- **WiFi管理**: 扫描、连接、断开WiFi网络，获取连接状态
- **DNS管理**: 获取DNS状态、切换DNS服务器、刷新DNS配置
- **AP热点控制**: 开启和关闭AP热点
- **自动运行脚本**: 简化部署和运行过程

## 🛠️ 技术栈

- **ROS2**: 支持Foxy和Humble版本
- **Python**: 主要使用Python实现
- **Bash脚本**: 自动化部署和运行
- **Systemd服务**: 支持开机自启动

## 📊 项目结构

```
network_robotdog/
├── docs/                      # 项目文档
│   ├── overview/              # 概览文档
│   ├── network/               # 网络模块文档
│   ├── ros/                   # ROS环境文档
│   ├── scripts/               # 脚本文档
│   └── testing/               # 测试文档
│
├── auto_run.sh                # 完整自动化脚本
├── quick_start.sh             # 快速启动脚本
├── install_service.sh         # 系统服务安装脚本
│
└── xiaoli_application_ros2/   # ROS2工作空间
    └── src/
        └── network/           # 网络模块源代码
```

## 🔄 使用流程

1. 使用`quick_start.sh`启动网络模块
2. 通过ROS2服务调用进行网络管理
3. 监控网络状态话题获取实时信息
4. 使用`auto_run.sh --status`查看系统状态

## 📞 支持与反馈

如遇问题，请参考:
- [故障排除指南](./docs/network/NETWORK_MODULE_DOCUMENTATION.md#故障排除)
- 查看日志: `tail -f log/network_launch_*.log`

---

*网络机器狗项目 v1.0 | 更新时间: 2023-07-29*
