# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a ROS2-based network management system for robotic dogs (机器狗), focusing on network connectivity, WiFi management, and network state monitoring. The project includes network control services, cellular module management, and automated deployment scripts.

## Common Development Commands

### Build and Setup
```bash
# Quick start (builds if needed and launches network module)
./quick_start.sh

# Full auto mode (build + start + monitor)
./auto_run.sh -a

# Build only
./auto_run.sh -b
cd xiaoli_application_ros2
colcon build --symlink-install
```

### Running the System
```bash
# Start network module
./quick_start.sh

# Start with specific launch file
./auto_run.sh -l network.py -s

# Monitor system status
./auto_run.sh --status

# Stop system
./auto_run.sh -t
```

### Testing
```bash
# Run all tests
cd xiaoli_application_ros2/src/network
python3 -m pytest test/

# Run specific test files
python3 test/test_services_and_publishers.py
python3 test/verify_network_subscriptions.py

# Check network topics
bash test/check_network_topics.sh

# Test network service calls
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getNetworkStatus\"}'}"
```

### Code Quality
```bash
# Linting
python3 test/test_flake8.py
python3 test/test_pep257.py

# Copyright check
python3 test/test_copyright.py
```

## High-Level Architecture

### ROS2 Workspace Structure
- **Root**: `xiaoli_application_ros2/` - Main ROS2 workspace
- **Network Package**: `src/network/` - Core network management functionality
- **Interface Package**: `src/homi_speech_interface/` - Custom ROS2 message/service definitions
- **Launch Package**: `src/launch_package/` - Launch configurations and YAML configs

### Core Components

#### 1. Network Node (`network_node.py`)
- Main ROS2 node that orchestrates network operations
- Provides `/homi_speech/network_service` service for network control
- Publishes network status to `/network_status` topic
- Subscribes to `/andlink_network` for bind mode control

#### 2. Network Manager (`network_manager.py`)
- Core network management logic
- Handles WiFi scanning, connection, and disconnection
- Manages cellular/mobile data interfaces
- Integrates with D-Bus for system-level network control
- DNS server management and switching

#### 3. Cellular Control (`cellular_control.py`)
- Manages cellular/mobile data connections
- Device-specific implementations for different hardware platforms
- AT command interface for cellular modules

#### 4. Configuration Management (`network_config_manager.py`)
- Persistent network state storage
- File-based configuration with locking mechanisms
- State synchronization between network operations

### Key ROS2 Interfaces

#### Services
- `/homi_speech/network_service` (NetCtrl): Primary network control interface
- `/homi_speech/sigc_data_service_APP` (SIGCData): Additional data service

#### Topics
- `/network_status` (String): Network status publications
- `/andlink_network` (String): Bind mode control messages

#### Supported Network Commands
- `getNetworkStatus`: Get current network state
- `wifiScan`: Scan for available WiFi networks
- `wifiConnect`: Connect to WiFi network
- `wifiDisconnect`: Disconnect from WiFi
- `setMobileDataState`: Enable/disable mobile data
- `getDNSStatus`: Get DNS configuration
- `switchDNS`: Change DNS servers
- `refreshDNS`: Refresh DNS configuration

### Device Support
The system supports multiple hardware platforms:
- **Standard devices**: Full D-Bus integration
- **Unitree devices**: Specialized cellular control without D-Bus
- **Different cellular modules**: AT command variations

### Configuration Files
- `configs/handle_network.yaml`: Network service configuration
- `configs/robot_config.yaml`: Robot-specific settings
- Various launch files in `launch/` directory for different deployment scenarios

### Logging and Monitoring
- Logs stored in `log/` directory with timestamps
- Real-time status monitoring via ROS2 topics
- Automated restart capabilities in auto_run.sh
- System status checks and health monitoring

### Development Notes
- Uses Python 3 with ROS2 (Humble/Foxy)
- Threading and async operations for network scanning
- File locking for configuration management
- Comprehensive error handling and recovery
- Multi-language support (Chinese/English logging)

### Testing Infrastructure
- Comprehensive test suite in `test/` directory
- Service and publisher verification scripts
- Network topic monitoring tools
- Performance and async operation tests
- Code quality checks (flake8, pep257, copyright)