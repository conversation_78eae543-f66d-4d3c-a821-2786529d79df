#!/bin/bash

# =============================================================================
# 网络模块快速启动脚本
# 简化版本，专注于启动network模块
# =============================================================================

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
WORKSPACE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LAUNCH_DIR="$WORKSPACE_DIR/xiaoli_application_ros2/src/launch_package/launch"
LOG_DIR="$WORKSPACE_DIR/log"

# 默认配置
DEFAULT_LAUNCH="network.py"
ROS_DISTRO="humble"

echo -e "${BLUE}🚀 网络模块快速启动${NC}"
echo "=================================="

# 检查ROS环境
if [ ! -f "/opt/ros/$ROS_DISTRO/setup.bash" ]; then
    echo -e "${RED}❌ 未找到ROS2 $ROS_DISTRO 安装${NC}"
    exit 1
fi

# 设置ROS环境
source "/opt/ros/$ROS_DISTRO/setup.bash"

# 检查工作空间
if [ ! -d "$WORKSPACE_DIR/xiaoli_application_ros2" ]; then
    echo -e "${RED}❌ 工作空间不存在${NC}"
    exit 1
fi

# 进入工作空间
cd "$WORKSPACE_DIR/xiaoli_application_ros2"

# 检查是否需要构建
if [ ! -d "install" ] || [ ! -f "install/setup.bash" ]; then
    echo -e "${YELLOW}⚠️  需要构建工作空间...${NC}"
    # 修改构建命令，删除--symlink-install选项
    colcon build
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 构建失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 构建完成${NC}"
fi

# 设置工作空间环境
source "install/setup.bash"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 默认使用network.py
LAUNCH_FILE="$DEFAULT_LAUNCH"

# 检查launch文件是否存在
if [ ! -f "$LAUNCH_DIR/$LAUNCH_FILE" ]; then
    echo -e "${RED}❌ Network launch文件不存在: $LAUNCH_FILE${NC}"
    echo -e "${YELLOW}尝试查找可用的network相关launch文件...${NC}"
    
    # 查找包含network的launch文件
    NETWORK_LAUNCH_FILES=$(find "$LAUNCH_DIR" -name "*network*.py" 2>/dev/null)
    
    if [ -z "$NETWORK_LAUNCH_FILES" ]; then
        echo -e "${RED}❌ 未找到任何network相关launch文件${NC}"
        exit 1
    else
        echo -e "${GREEN}找到以下network相关launch文件:${NC}"
        echo "$NETWORK_LAUNCH_FILES" | while read -r file; do
            echo "- $(basename "$file")"
        done
        
        # 使用找到的第一个文件
        LAUNCH_FILE=$(basename "$(echo "$NETWORK_LAUNCH_FILES" | head -n1)")
        echo -e "${YELLOW}自动选择: $LAUNCH_FILE${NC}"
    fi
fi

# 停止可能正在运行的进程
echo -e "${YELLOW}停止现有进程...${NC}"
pkill -f "ros2 launch" 2>/dev/null
sleep 2

# 启动系统
echo -e "${GREEN}🚀 启动网络模块...${NC}"
echo "Launch文件: $LAUNCH_FILE"
echo "日志目录: $LOG_DIR"

# 创建日志文件
timestamp=$(date +"%Y%m%d_%H%M%S")
log_file="$LOG_DIR/network_launch_${timestamp}.log"

# 启动ROS系统
nohup ros2 launch launch_package "$LAUNCH_FILE" > "$log_file" 2>&1 &
ROS_PID=$!

echo -e "${GREEN}✅ 网络模块已启动 (PID: $ROS_PID)${NC}"
echo -e "${BLUE}📋 日志文件: $log_file${NC}"
echo -e "${YELLOW}💡 使用 'tail -f $log_file' 查看实时日志${NC}"
echo -e "${YELLOW}💡 使用 './auto_run.sh --status' 查看系统状态${NC}"
echo -e "${YELLOW}💡 使用 './auto_run.sh -t' 停止系统${NC}"

# 等待几秒检查启动状态
sleep 3
if kill -0 "$ROS_PID" 2>/dev/null; then
    echo -e "${GREEN}✅ 网络模块启动成功${NC}"
else
    echo -e "${RED}❌ 网络模块启动失败，请检查日志文件${NC}"
    exit 1
fi 