#!/bin/bash

# =============================================================================
# Git高级更新脚本 - 拉取远程仓库更新，同时保留本地未提交内容
# 支持指定远程仓库、分支和更多选项
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 默认值
REMOTE="origin"
AUTO_MODE=false
VERBOSE=false
FORCE_MODE=false
SHOW_DIFF=false
REBASE_MODE=false

# 打印标题
print_header() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Git 高级更新脚本                          ║"
    echo "║          拉取远程仓库更新，同时保留本地未提交内容            ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 打印帮助信息
print_help() {
    echo -e "${CYAN}用法:${NC} $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help            显示此帮助信息"
    echo "  -r, --remote REMOTE   指定远程仓库 (默认: origin)"
    echo "  -b, --branch BRANCH   指定分支 (默认: 当前分支)"
    echo "  -a, --auto            自动模式，不询问确认"
    echo "  -v, --verbose         显示详细信息"
    echo "  -f, --force           强制模式，尝试解决简单冲突"
    echo "  -d, --diff            显示stash操作前后的差异"
    echo "  --rebase              使用rebase而不是merge进行pull操作"
    echo
    echo "示例:"
    echo "  $0                    使用默认设置更新"
    echo "  $0 -r upstream        从upstream远程仓库拉取更新"
    echo "  $0 -b develop         拉取develop分支的更新"
    echo "  $0 -a -v              自动模式并显示详细信息"
    echo "  $0 --rebase           使用rebase模式拉取更新"
    echo
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                print_help
                exit 0
                ;;
            -r|--remote)
                REMOTE="$2"
                shift 2
                ;;
            -b|--branch)
                BRANCH="$2"
                shift 2
                ;;
            -a|--auto)
                AUTO_MODE=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -f|--force)
                FORCE_MODE=true
                shift
                ;;
            -d|--diff)
                SHOW_DIFF=true
                shift
                ;;
            --rebase)
                REBASE_MODE=true
                shift
                ;;
            *)
                echo -e "${RED}错误: 未知选项 $1${NC}"
                print_help
                exit 1
                ;;
        esac
    done
}

# 打印信息
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 打印成功信息
log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 打印警告信息
log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 打印错误信息
log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 打印详细信息
log_verbose() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${PURPLE}[VERBOSE]${NC} $1"
    fi
}

# 检查git仓库
check_git_repo() {
    if [ ! -d ".git" ]; then
        log_error "当前目录不是git仓库的根目录"
        echo "请在git仓库的根目录运行此脚本"
        exit 1
    fi
}

# 获取远程仓库信息
check_remote() {
    if ! git remote | grep -q "^${REMOTE}$"; then
        log_error "远程仓库 '$REMOTE' 不存在"
        echo "可用的远程仓库:"
        git remote -v
        exit 1
    fi
    
    log_verbose "远程仓库 '$REMOTE' 已验证"
}

# 获取分支信息
check_branch() {
    # 获取当前分支
    CURRENT_BRANCH=$(git symbolic-ref --short HEAD 2>/dev/null)
    
    if [ -z "$CURRENT_BRANCH" ]; then
        log_error "无法获取当前分支信息，可能处于分离HEAD状态"
        exit 1
    fi
    
    # 如果未指定分支，使用当前分支
    if [ -z "$BRANCH" ]; then
        BRANCH="$CURRENT_BRANCH"
    fi
    
    log_info "当前分支: $CURRENT_BRANCH"
    
    if [ "$BRANCH" != "$CURRENT_BRANCH" ]; then
        log_info "目标分支: $BRANCH"
    fi
}

# 检查本地更改
check_local_changes() {
    # 显示当前仓库状态
    if [ "$VERBOSE" = true ]; then
        log_info "当前仓库状态:"
        git status -s
    fi
    
    # 检查是否有未提交的更改
    if [ -z "$(git status --porcelain)" ]; then
        log_success "本地没有未提交的更改，直接执行pull操作..."
        return 1
    else
        local_changes=$(git status --porcelain | wc -l)
        log_warning "检测到本地有 $local_changes 个未提交的更改"
        return 0
    fi
}

# 确认操作
confirm_operation() {
    if [ "$AUTO_MODE" = true ]; then
        log_verbose "自动模式，跳过确认"
        return 0
    fi
    
    read -p "是否继续更新操作? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_warning "操作已取消"
        exit 0
    fi
    
    return 0
}

# 保存本地更改
stash_changes() {
    local stash_name="自动保存的更改 - $(date '+%Y-%m-%d %H:%M:%S')"
    
    log_info "保存本地未提交的更改..."
    
    if [ "$SHOW_DIFF" = true ]; then
        log_info "本地更改差异:"
        git diff
    fi
    
    git stash save "$stash_name"
    
    if [ $? -ne 0 ]; then
        log_error "无法保存本地更改"
        exit 1
    fi
    
    log_success "本地更改已保存"
    return 0
}

# 拉取远程更新
pull_updates() {
    log_info "从 $REMOTE/$BRANCH 拉取更新..."
    
    if [ "$REBASE_MODE" = true ]; then
        log_verbose "使用rebase模式"
        git pull --rebase "$REMOTE" "$BRANCH"
    else
        git pull "$REMOTE" "$BRANCH"
    fi
    
    if [ $? -ne 0 ]; then
        log_error "拉取远程更新失败"
        log_warning "正在恢复本地更改..."
        git stash pop
        log_warning "已恢复本地更改，但远程更新失败"
        exit 1
    fi
    
    log_success "远程更新已拉取"
}

# 恢复本地更改
pop_stash() {
    log_info "恢复本地未提交的更改..."
    
    git stash pop
    
    # 检查是否有冲突
    if [ $? -ne 0 ]; then
        log_warning "恢复本地更改时发生冲突"
        
        if [ "$FORCE_MODE" = true ]; then
            log_info "尝试自动解决冲突..."
            
            # 尝试使用--strategy-option theirs解决冲突
            git checkout --theirs .
            git add .
            
            if [ -z "$(git diff --name-only --diff-filter=U)" ]; then
                log_success "冲突已自动解决"
            else
                log_warning "无法自动解决所有冲突"
                log_info "冲突文件:"
                git diff --name-only --diff-filter=U
                log_info "请手动解决冲突后继续"
                exit 1
            fi
        else
            log_warning "请手动解决冲突后继续"
            log_info "冲突文件:"
            git status -s | grep "^UU"
            exit 1
        fi
    fi
    
    log_success "本地更改已恢复"
}

# 显示最终状态
show_final_status() {
    log_success "更新完成! 已成功拉取远程更新并恢复本地更改"
    
    if [ "$VERBOSE" = true ]; then
        log_info "当前仓库状态:"
        git status -s
        
        log_info "最近的提交记录:"
        git log --oneline -n 5
    fi
}

# 主函数
main() {
    print_header
    parse_args "$@"
    check_git_repo
    check_remote
    check_branch
    
    if ! check_local_changes; then
        # 没有本地更改，直接拉取
        pull_updates
        show_final_status
        exit 0
    fi
    
    confirm_operation
    stash_changes
    pull_updates
    pop_stash
    show_final_status
    
    echo
    log_success "操作成功完成!"
}

# 执行主函数
main "$@" 