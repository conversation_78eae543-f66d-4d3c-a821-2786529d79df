#!/bin/bash

# =============================================================================
# 网络模块自动运行脚本
# 支持多种启动模式和自动错误恢复
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="$SCRIPT_DIR"
LAUNCH_DIR="$WORKSPACE_DIR/xiaoli_application_ros2/src/launch_package/launch"
LOG_DIR="$WORKSPACE_DIR/log"
BUILD_DIR="$WORKSPACE_DIR/xiaoli_application_ros2/build"
INSTALL_DIR="$WORKSPACE_DIR/xiaoli_application_ros2/install"

# 默认配置
DEFAULT_ROS_DISTRO="humble"
DEFAULT_LAUNCH_FILE="network.py"
MAX_RESTART_ATTEMPTS=3
RESTART_DELAY=5

# 全局变量
ROS_DISTRO=""
LAUNCH_FILE=""
RESTART_COUNT=0
IS_RUNNING=false

# =============================================================================
# 工具函数
# =============================================================================

print_header() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    网络模块自动运行脚本                      ║"
    echo "║                    Network Module Auto Runner                ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查ROS环境
check_ros_environment() {
    print_step "检查ROS环境..."
    
    # 检查ROS_DISTRO环境变量
    if [ -z "$ROS_DISTRO" ]; then
        if [ -n "$ROS_DISTRO" ]; then
            ROS_DISTRO="$ROS_DISTRO"
        else
            # 尝试自动检测
            if [ -d "/opt/ros/humble" ]; then
                ROS_DISTRO="humble"
            elif [ -d "/opt/ros/foxy" ]; then
                ROS_DISTRO="foxy"
            else
                print_error "无法检测到ROS2安装，请手动设置ROS_DISTRO环境变量"
                return 1
            fi
        fi
    fi
    
    print_info "检测到ROS版本: $ROS_DISTRO"
    
    # 检查ROS setup文件
    ROS_SETUP_FILE="/opt/ros/$ROS_DISTRO/setup.bash"
    if [ ! -f "$ROS_SETUP_FILE" ]; then
        print_error "ROS setup文件不存在: $ROS_SETUP_FILE"
        return 1
    fi
    
    # 设置ROS环境 - 直接source，不要使用.命令
    source "$ROS_SETUP_FILE"
    print_success "ROS环境设置完成"
    return 0
}

# 检查工作空间
check_workspace() {
    print_step "检查工作空间..."
    
    if [ ! -d "$WORKSPACE_DIR" ]; then
        print_error "工作空间目录不存在: $WORKSPACE_DIR"
        return 1
    fi
    
    if [ ! -d "$LAUNCH_DIR" ]; then
        print_error "Launch目录不存在: $LAUNCH_DIR"
        return 1
    fi
    
    print_success "工作空间检查完成"
    return 0
}

# 创建日志目录
create_log_directory() {
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
        print_info "创建日志目录: $LOG_DIR"
    fi
}

# 构建工作空间
build_workspace() {
    print_step "构建工作空间..."
    
    cd "$WORKSPACE_DIR/xiaoli_application_ros2" || {
        print_error "无法进入工作空间目录"
        return 1
    }
    
    # 清理旧的构建文件
    if [ -d "$BUILD_DIR" ]; then
        print_info "清理旧的构建文件..."
        rm -rf "$BUILD_DIR"
    fi
    
    # 构建
    print_info "开始构建..."
    # 修改构建命令，删除--symlink-install选项
    colcon build --cmake-args -DCMAKE_BUILD_TYPE=Release
    
    if [ $? -eq 0 ]; then
        print_success "构建完成"
        return 0
    else
        print_error "构建失败"
        return 1
    fi
}

# 显示可用的launch文件
show_available_launch_files() {
    print_info "可用的network相关launch文件:"
    echo -e "${CYAN}"
    find "$LAUNCH_DIR" -name "*network*.py" 2>/dev/null | while read -r file; do
        basename_file=$(basename "$file")
        echo "  - $basename_file"
    done
    echo -e "${NC}"
}

# 选择launch文件
select_launch_file() {
    if [ -n "$LAUNCH_FILE" ]; then
        if [ ! -f "$LAUNCH_DIR/$LAUNCH_FILE" ]; then
            print_error "指定的launch文件不存在: $LAUNCH_FILE"
            return 1
        fi
        return 0
    fi
    
    # 查找network相关launch文件
    NETWORK_LAUNCH_FILES=$(find "$LAUNCH_DIR" -name "*network*.py" 2>/dev/null)
    
    if [ -z "$NETWORK_LAUNCH_FILES" ]; then
        print_error "未找到任何network相关launch文件"
        return 1
    fi
    
    show_available_launch_files
    
    echo -e "${YELLOW}请选择要启动的network launch文件:${NC}"
    
    # 创建选择菜单
    count=1
    while IFS= read -r file; do
        basename_file=$(basename "$file")
        echo "$count) $basename_file"
        count=$((count + 1))
    done <<< "$NETWORK_LAUNCH_FILES"
    
    echo "$count) 自定义文件名"
    
    read -p "请输入选择 (1-$count): " choice
    
    if [ "$choice" -eq "$count" ]; then
        read -p "请输入launch文件名: " custom_file
        LAUNCH_FILE="$custom_file"
    elif [ "$choice" -ge 1 ] && [ "$choice" -lt "$count" ]; then
        LAUNCH_FILE=$(basename "$(echo "$NETWORK_LAUNCH_FILES" | sed -n "${choice}p")")
    else
        print_warning "无效选择，使用默认配置: $DEFAULT_LAUNCH_FILE"
        LAUNCH_FILE="$DEFAULT_LAUNCH_FILE"
    fi
    
    # 检查选择的文件是否存在
    if [ ! -f "$LAUNCH_DIR/$LAUNCH_FILE" ]; then
        print_error "选择的launch文件不存在: $LAUNCH_FILE"
        print_info "使用默认launch文件: $DEFAULT_LAUNCH_FILE"
        LAUNCH_FILE="$DEFAULT_LAUNCH_FILE"
        
        # 再次检查默认文件是否存在
        if [ ! -f "$LAUNCH_DIR/$LAUNCH_FILE" ]; then
            print_error "默认launch文件也不存在，无法继续"
            return 1
        fi
    fi
    
    print_info "选择的launch文件: $LAUNCH_FILE"
    return 0
}

# 启动ROS系统
start_ros_system() {
    print_step "启动网络模块..."
    
    # 设置环境变量
    export ROS_DISTRO="$ROS_DISTRO"
    source "/opt/ros/$ROS_DISTRO/setup.bash"
    
    # 检查工作空间setup.bash是否存在
    if [ -f "$INSTALL_DIR/setup.bash" ]; then
        source "$INSTALL_DIR/setup.bash"
    else
        print_warning "工作空间setup.bash不存在，仅使用ROS基础环境"
    fi
    
    # 创建日志文件名
    timestamp=$(date +"%Y%m%d_%H%M%S")
    log_file="$LOG_DIR/network_launch_${timestamp}.log"
    
    print_info "启动命令: ros2 launch launch_package $LAUNCH_FILE"
    print_info "日志文件: $log_file"
    
    # 启动ROS系统
    cd "$WORKSPACE_DIR/xiaoli_application_ros2" || {
        print_error "无法进入工作空间目录"
        return 1
    }
    
    IS_RUNNING=true
    
    # 使用nohup在后台运行
    nohup ros2 launch launch_package "$LAUNCH_FILE" > "$log_file" 2>&1 &
    ROS_PID=$!
    
    print_info "网络模块已启动，PID: $ROS_PID"
    
    # 等待系统启动
    sleep 3
    
    # 检查进程是否还在运行
    if kill -0 "$ROS_PID" 2>/dev/null; then
        print_success "网络模块启动成功"
        return 0
    else
        print_error "网络模块启动失败"
        IS_RUNNING=false
        return 1
    fi
}

# 监控系统状态
monitor_system() {
    print_step "开始监控网络模块状态..."
    
    while [ "$IS_RUNNING" = true ]; do
        # 检查ROS进程是否还在运行
        if ! kill -0 "$ROS_PID" 2>/dev/null; then
            print_warning "网络模块进程已停止 (PID: $ROS_PID)"
            IS_RUNNING=false
            break
        fi
        
        # 检查网络状态
        if command_exists "ros2"; then
            # 检查节点状态
            if ! ros2 node list | grep -q "network" 2>/dev/null; then
                print_warning "网络模块节点通信异常"
            fi
        fi
        
        sleep 10
    done
}

# 停止系统
stop_system() {
    print_step "停止网络模块..."
    
    if [ -n "$ROS_PID" ] && kill -0 "$ROS_PID" 2>/dev/null; then
        print_info "正在停止网络模块进程 (PID: $ROS_PID)..."
        kill "$ROS_PID"
        
        # 等待进程结束
        for i in {1..10}; do
            if ! kill -0 "$ROS_PID" 2>/dev/null; then
                break
            fi
            sleep 1
        done
        
        # 强制杀死进程
        if kill -0 "$ROS_PID" 2>/dev/null; then
            print_warning "强制停止网络模块进程..."
            kill -9 "$ROS_PID"
        fi
    fi
    
    # 停止所有ros2进程
    pkill -f "ros2 launch.*network" 2>/dev/null
    
    IS_RUNNING=false
    print_success "网络模块已停止"
}

# 重启系统
restart_system() {
    print_step "重启网络模块..."
    
    stop_system
    sleep "$RESTART_DELAY"
    
    if start_ros_system; then
        print_success "网络模块重启成功"
        return 0
    else
        print_error "网络模块重启失败"
        return 1
    fi
}

# 显示系统状态
show_system_status() {
    print_step "网络模块状态信息"
    
    echo -e "${CYAN}工作空间:${NC} $WORKSPACE_DIR"
    echo -e "${CYAN}ROS版本:${NC} $ROS_DISTRO"
    echo -e "${CYAN}Launch文件:${NC} $LAUNCH_FILE"
    echo -e "${CYAN}运行状态:${NC} $([ "$IS_RUNNING" = true ] && echo "运行中" || echo "已停止")"
    
    if [ -n "$ROS_PID" ]; then
        echo -e "${CYAN}网络模块进程PID:${NC} $ROS_PID"
        if kill -0 "$ROS_PID" 2>/dev/null; then
            echo -e "${CYAN}进程状态:${NC} ${GREEN}运行中${NC}"
        else
            echo -e "${CYAN}进程状态:${NC} ${RED}已停止${NC}"
        fi
    fi
    
    # 显示ROS节点
    if command_exists "ros2" && [ "$IS_RUNNING" = true ]; then
        echo -e "${CYAN}网络相关ROS节点:${NC}"
        ros2 node list 2>/dev/null | grep -i "network" | head -10
        
        echo -e "${CYAN}网络相关ROS话题:${NC}"
        ros2 topic list 2>/dev/null | grep -i "network" | head -5
    fi
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}网络模块自动运行脚本使用说明${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -l, --launch FILE       指定launch文件"
    echo "  -r, --ros-distro DISTRO 指定ROS版本 (humble/foxy)"
    echo "  -b, --build             构建工作空间"
    echo "  -s, --start             启动网络模块"
    echo "  -t, --stop              停止网络模块"
    echo "  -R, --restart           重启网络模块"
    echo "  -m, --monitor           监控网络模块状态"
    echo "  -a, --auto              自动模式 (构建+启动+监控)"
    echo "  --status                显示网络模块状态"
    echo ""
    echo "示例:"
    echo "  $0 -a                   自动模式启动"
    echo "  $0 -l network.py -s     启动指定launch文件"
    echo "  $0 -r humble -b         使用humble版本构建"
    echo "  $0 --status             查看网络模块状态"
    echo ""
}

# 自动模式
auto_mode() {
    print_step "进入自动模式..."
    
    # 检查环境
    if ! check_ros_environment; then
        return 1
    fi
    
    if ! check_workspace; then
        return 1
    fi
    
    # 创建日志目录
    create_log_directory
    
    # 选择launch文件
    if ! select_launch_file; then
        return 1
    fi
    
    # 构建工作空间
    if ! build_workspace; then
        print_error "构建失败，退出自动模式"
        return 1
    fi
    
    # 启动系统
    if ! start_ros_system; then
        print_error "启动失败，退出自动模式"
        return 1
    fi
    
    # 监控系统
    monitor_system
    
    # 如果系统停止，尝试重启
    while [ $RESTART_COUNT -lt $MAX_RESTART_ATTEMPTS ]; do
        print_warning "网络模块已停止，尝试重启 ($((RESTART_COUNT + 1))/$MAX_RESTART_ATTEMPTS)"
        
        if restart_system; then
            RESTART_COUNT=0
            monitor_system
        else
            RESTART_COUNT=$((RESTART_COUNT + 1))
            sleep "$RESTART_DELAY"
        fi
    done
    
    print_error "达到最大重启次数，退出自动模式"
    return 1
}

# 信号处理
cleanup() {
    print_info "收到退出信号，正在清理..."
    stop_system
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# =============================================================================
# 主程序
# =============================================================================

main() {
    print_header
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -l|--launch)
                LAUNCH_FILE="$2"
                shift 2
                ;;
            -r|--ros-distro)
                ROS_DISTRO="$2"
                shift 2
                ;;
            -b|--build)
                if ! check_ros_environment; then exit 1; fi
                if ! check_workspace; then exit 1; fi
                create_log_directory
                if ! build_workspace; then exit 1; fi
                exit 0
                ;;
            -s|--start)
                if ! check_ros_environment; then exit 1; fi
                if ! check_workspace; then exit 1; fi
                create_log_directory
                if ! select_launch_file; then exit 1; fi
                if ! start_ros_system; then exit 1; fi
                monitor_system
                exit 0
                ;;
            -t|--stop)
                stop_system
                exit 0
                ;;
            -R|--restart)
                if ! restart_system; then exit 1; fi
                exit 0
                ;;
            -m|--monitor)
                monitor_system
                exit 0
                ;;
            -a|--auto)
                auto_mode
                exit $?
                ;;
            --status)
                show_system_status
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有参数，显示帮助
    show_help
}

# 运行主程序
main "$@" 