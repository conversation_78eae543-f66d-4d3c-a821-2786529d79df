#!/bin/bash

# =============================================================================
# 网络模块系统服务安装脚本
# 用于设置网络模块开机自启动服务
# =============================================================================

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_NAME="network-module"
SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"
USER=$(whoami)

echo -e "${BLUE}🔧 网络模块系统服务安装${NC}"
echo "=================================="

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo -e "${RED}❌ 请不要使用root用户运行此脚本${NC}"
    echo "请使用普通用户运行，脚本会自动处理权限"
    exit 1
fi

# 检查systemd是否可用
if ! command -v systemctl >/dev/null 2>&1; then
    echo -e "${RED}❌ 系统不支持systemd${NC}"
    exit 1
fi

# 查找网络相关launch文件
LAUNCH_DIR="$SCRIPT_DIR/xiaoli_application_ros2/src/launch_package/launch"
NETWORK_LAUNCH_FILES=$(find "$LAUNCH_DIR" -name "*network*.py" 2>/dev/null)

if [ -z "$NETWORK_LAUNCH_FILES" ]; then
    echo -e "${RED}❌ 未找到任何network相关launch文件${NC}"
    exit 1
fi

# 显示可用的network launch文件
echo -e "${BLUE}📋 可用的network launch文件:${NC}"
count=1
while IFS= read -r file; do
    basename_file=$(basename "$file")
    echo "$count) $basename_file"
    count=$((count + 1))
done <<< "$NETWORK_LAUNCH_FILES"

# 选择launch文件
read -p "请选择要使用的launch文件 (1-$((count-1))): " choice

if [ -z "$choice" ] || ! [[ "$choice" =~ ^[0-9]+$ ]] || [ "$choice" -lt 1 ] || [ "$choice" -ge "$count" ]; then
    echo -e "${YELLOW}无效选择，使用第一个文件${NC}"
    LAUNCH_FILE=$(basename "$(echo "$NETWORK_LAUNCH_FILES" | head -n1)")
else
    LAUNCH_FILE=$(basename "$(echo "$NETWORK_LAUNCH_FILES" | sed -n "${choice}p")")
fi

echo -e "${GREEN}已选择: $LAUNCH_FILE${NC}"

# 创建服务文件内容
cat > /tmp/${SERVICE_NAME}.service << EOF
[Unit]
Description=Network Module ROS2 Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=$USER
Group=$USER
WorkingDirectory=$SCRIPT_DIR
Environment=ROS_DISTRO=humble
Environment=DISPLAY=:0
ExecStart=$SCRIPT_DIR/auto_run.sh -l $LAUNCH_FILE -s
ExecStop=$SCRIPT_DIR/auto_run.sh -t
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=PATH=/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
Environment=PYTHONPATH=/opt/ros/humble/lib/python3.10/site-packages

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=$SCRIPT_DIR

[Install]
WantedBy=multi-user.target
EOF

echo -e "${BLUE}📋 服务配置:${NC}"
echo "服务名称: $SERVICE_NAME"
echo "用户: $USER"
echo "工作目录: $SCRIPT_DIR"
echo "服务文件: $SERVICE_FILE"
echo "Launch文件: $LAUNCH_FILE"

# 确认安装
echo ""
read -p "是否继续安装服务? (y/N): " confirm
if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}安装已取消${NC}"
    exit 0
fi

# 复制服务文件
echo -e "${BLUE}📁 安装服务文件...${NC}"
sudo cp /tmp/${SERVICE_NAME}.service "$SERVICE_FILE"

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 服务文件安装失败${NC}"
    exit 1
fi

# 重新加载systemd
echo -e "${BLUE}🔄 重新加载systemd...${NC}"
sudo systemctl daemon-reload

# 启用服务
echo -e "${BLUE}✅ 启用服务...${NC}"
sudo systemctl enable "$SERVICE_NAME"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 服务安装成功${NC}"
else
    echo -e "${RED}❌ 服务启用失败${NC}"
    exit 1
fi

# 显示服务状态
echo ""
echo -e "${BLUE}📊 服务状态:${NC}"
sudo systemctl status "$SERVICE_NAME" --no-pager

echo ""
echo -e "${GREEN}🎉 安装完成!${NC}"
echo ""
echo -e "${BLUE}📋 服务管理命令:${NC}"
echo "启动服务: sudo systemctl start $SERVICE_NAME"
echo "停止服务: sudo systemctl stop $SERVICE_NAME"
echo "重启服务: sudo systemctl restart $SERVICE_NAME"
echo "查看状态: sudo systemctl status $SERVICE_NAME"
echo "查看日志: sudo journalctl -u $SERVICE_NAME -f"
echo "禁用服务: sudo systemctl disable $SERVICE_NAME"
echo ""
echo -e "${YELLOW}💡 网络模块服务将在下次开机时自动启动${NC}"

# 询问是否立即启动
read -p "是否立即启动网络模块服务? (y/N): " start_now
if [[ $start_now =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}🚀 启动网络模块服务...${NC}"
    sudo systemctl start "$SERVICE_NAME"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 网络模块服务启动成功${NC}"
        echo ""
        echo -e "${BLUE}📊 当前状态:${NC}"
        sudo systemctl status "$SERVICE_NAME" --no-pager
    else
        echo -e "${RED}❌ 网络模块服务启动失败${NC}"
        echo "请检查日志: sudo journalctl -u $SERVICE_NAME -n 50"
    fi
fi

# 清理临时文件
rm -f /tmp/${SERVICE_NAME}.service 