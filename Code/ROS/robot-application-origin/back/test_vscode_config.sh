#!/bin/bash

# 测试VSCode配置的脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 测试VSCode配置 ===${NC}"
echo ""

# 检查必要文件
echo -e "${BLUE}1. 检查配置文件${NC}"
files=(".vscode/launch.json" ".vscode/tasks.json" "xiaoli_application_ros2/build_xiaoli.sh")

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓ $file 存在${NC}"
    else
        echo -e "${RED}✗ $file 不存在${NC}"
    fi
done

echo ""

# 检查构建脚本权限
echo -e "${BLUE}2. 检查构建脚本权限${NC}"
if [ -x "xiaoli_application_ros2/build_xiaoli.sh" ]; then
    echo -e "${GREEN}✓ build_xiaoli.sh 有执行权限${NC}"
else
    echo -e "${YELLOW}! build_xiaoli.sh 没有执行权限，正在修复...${NC}"
    chmod +x xiaoli_application_ros2/build_xiaoli.sh
    echo -e "${GREEN}✓ 权限已修复${NC}"
fi

echo ""

# 检查构建目录结构
echo -e "${BLUE}3. 检查构建目录结构${NC}"
if [ -d "install/x86_64/Debug" ]; then
    echo -e "${GREEN}✓ 构建目录存在${NC}"
    
    # 检查是否有已编译的包
    if [ -d "install/x86_64/Debug/lib/network" ]; then
        echo -e "${GREEN}✓ network包已编译${NC}"
    else
        echo -e "${YELLOW}! network包未编译${NC}"
    fi
    
    if [ -d "install/x86_64/Debug/lib/peripherals" ]; then
        echo -e "${GREEN}✓ peripherals包已编译${NC}"
    else
        echo -e "${YELLOW}! peripherals包未编译${NC}"
    fi
else
    echo -e "${YELLOW}! 构建目录不存在，需要先编译${NC}"
fi

echo ""

# 检查ROS2环境
echo -e "${BLUE}4. 检查ROS2环境${NC}"
if [ -n "$ROS_DISTRO" ]; then
    echo -e "${GREEN}✓ ROS2环境已设置: $ROS_DISTRO${NC}"
else
    echo -e "${YELLOW}! ROS2环境未设置${NC}"
    
    # 尝试自动设置
    ros2_paths=("/opt/ros/humble" "/opt/ros/galactic" "/opt/ros/foxy")
    for ros_dir in "${ros2_paths[@]}"; do
        if [ -d "$ros_dir" ]; then
            echo -e "${YELLOW}找到ROS2: $ros_dir${NC}"
            break
        fi
    done
fi

echo ""

# 检查调试器
echo -e "${BLUE}5. 检查调试器${NC}"
if command -v python3 >/dev/null 2>&1; then
    python_version=$(python3 --version)
    echo -e "${GREEN}✓ Python3: $python_version${NC}"
else
    echo -e "${RED}✗ Python3 未安装${NC}"
fi

if command -v gdb >/dev/null 2>&1; then
    gdb_version=$(gdb --version | head -1)
    echo -e "${GREEN}✓ GDB: $gdb_version${NC}"
else
    echo -e "${RED}✗ GDB 未安装${NC}"
fi

echo ""

# 测试构建脚本
echo -e "${BLUE}6. 测试构建脚本${NC}"
if [ -x "xiaoli_application_ros2/build_xiaoli.sh" ]; then
    echo -e "${YELLOW}测试构建脚本帮助信息...${NC}"
    ./xiaoli_application_ros2/build_xiaoli.sh --help | head -5
    echo -e "${GREEN}✓ 构建脚本可以正常运行${NC}"
else
    echo -e "${RED}✗ 构建脚本无法执行${NC}"
fi

echo ""

# 显示使用建议
echo -e "${BLUE}=== 使用建议 ===${NC}"
echo -e "${GREEN}VSCode调试配置已就绪！${NC}"
echo ""
echo -e "${YELLOW}快速开始:${NC}"
echo "1. 打开VSCode调试面板 (Ctrl+Shift+D)"
echo "2. 选择 '🚀 xiaoli一键编译+调试 - network (Python)'"
echo "3. 点击绿色播放按钮开始调试"
echo ""
echo -e "${YELLOW}或者使用任务:${NC}"
echo "1. 按 Ctrl+Shift+P 打开命令面板"
echo "2. 输入 'Tasks: Run Task'"
echo "3. 选择 'xiaoli一键编译 - network'"
echo ""
echo -e "${CYAN}更多信息请查看: VSCODE_DEBUG_GUIDE.md${NC}"
