#!/bin/bash

# 工作空间清理脚本
# 用于清理重复包和备份文件，确保只编译ROS2包

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
function show_help() {
    echo -e "${BLUE}工作空间清理脚本${NC}"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  --dry-run           仅显示将要删除的文件，不实际删除"
    echo "  --backup-only       仅清理备份目录 (_gsdata_)"
    echo "  --ros1-only         仅清理ROS1相关目录"
    echo "  --all               清理所有冗余文件 (默认)"
    echo "  --list-duplicates   列出重复的包"
    echo ""
    echo "此脚本将清理以下内容:"
    echo "  - _gsdata_ 备份目录"
    echo "  - deeprobots_application_ros1 目录"
    echo "  - 3rdParty 重复目录"
    echo "  - 构建缓存目录"
}

# 列出重复包
function list_duplicates() {
    echo -e "${BLUE}=== 检测重复包 ===${NC}"
    
    # 使用colcon list检测重复包
    if command -v colcon >/dev/null 2>&1; then
        echo -e "${YELLOW}运行 colcon list 检测重复包...${NC}"
        colcon list 2>&1 | grep -A 100 "Duplicate package names" || echo -e "${GREEN}未检测到重复包${NC}"
    else
        echo -e "${YELLOW}colcon 未安装，手动检测重复包...${NC}"
        
        # 手动查找package.xml文件
        find . -name "package.xml" -type f | while read pkg_file; do
            pkg_name=$(grep -o '<name>[^<]*</name>' "$pkg_file" | sed 's/<[^>]*>//g' | head -1)
            pkg_dir=$(dirname "$pkg_file")
            echo "$pkg_name: $pkg_dir"
        done | sort | uniq -d -f1
    fi
}

# 清理备份目录
function clean_backup_dirs() {
    echo -e "${BLUE}=== 清理备份目录 ===${NC}"
    
    local dirs_to_clean=(
        "_gsdata_"
        ".git/gsdata"
    )
    
    for dir in "${dirs_to_clean[@]}"; do
        if [ -d "$dir" ]; then
            if [ "$DRY_RUN" = true ]; then
                echo -e "${YELLOW}[DRY RUN] 将删除目录: $dir${NC}"
                du -sh "$dir" 2>/dev/null || echo "  (无法获取大小)"
            else
                echo -e "${YELLOW}删除备份目录: $dir${NC}"
                du -sh "$dir" 2>/dev/null || echo "  (无法获取大小)"
                rm -rf "$dir"
                echo -e "${GREEN}已删除: $dir${NC}"
            fi
        fi
    done
}

# 清理ROS1目录
function clean_ros1_dirs() {
    echo -e "${BLUE}=== 清理ROS1目录 ===${NC}"
    
    local dirs_to_clean=(
        "deeprobots_application_ros1"
        "deeprob_ws_ctrl"
    )
    
    for dir in "${dirs_to_clean[@]}"; do
        if [ -d "$dir" ]; then
            if [ "$DRY_RUN" = true ]; then
                echo -e "${YELLOW}[DRY RUN] 将删除ROS1目录: $dir${NC}"
                du -sh "$dir" 2>/dev/null || echo "  (无法获取大小)"
            else
                echo -e "${YELLOW}删除ROS1目录: $dir${NC}"
                du -sh "$dir" 2>/dev/null || echo "  (无法获取大小)"
                rm -rf "$dir"
                echo -e "${GREEN}已删除: $dir${NC}"
            fi
        fi
    done
}

# 清理3rdParty重复目录
function clean_3rdparty_duplicates() {
    echo -e "${BLUE}=== 清理3rdParty重复目录 ===${NC}"
    
    # 查找重复的3rdParty目录
    find . -name "3rdParty" -type d | while read dir; do
        # 跳过主要的3rdParty目录
        if [[ "$dir" == "./3rdParty" ]]; then
            continue
        fi
        
        if [ "$DRY_RUN" = true ]; then
            echo -e "${YELLOW}[DRY RUN] 将删除重复的3rdParty目录: $dir${NC}"
        else
            echo -e "${YELLOW}删除重复的3rdParty目录: $dir${NC}"
            rm -rf "$dir"
            echo -e "${GREEN}已删除: $dir${NC}"
        fi
    done
}

# 清理构建缓存
function clean_build_cache() {
    echo -e "${BLUE}=== 清理构建缓存 ===${NC}"
    
    local cache_dirs=(
        "build"
        "install"
        "log"
        ".colcon"
    )
    
    for dir in "${cache_dirs[@]}"; do
        if [ -d "$dir" ]; then
            if [ "$DRY_RUN" = true ]; then
                echo -e "${YELLOW}[DRY RUN] 将删除构建缓存: $dir${NC}"
                du -sh "$dir" 2>/dev/null || echo "  (无法获取大小)"
            else
                echo -e "${YELLOW}删除构建缓存: $dir${NC}"
                du -sh "$dir" 2>/dev/null || echo "  (无法获取大小)"
                rm -rf "$dir"
                echo -e "${GREEN}已删除: $dir${NC}"
            fi
        fi
    done
}

# 默认参数
DRY_RUN=false
CLEAN_TYPE="all"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --backup-only)
            CLEAN_TYPE="backup"
            shift
            ;;
        --ros1-only)
            CLEAN_TYPE="ros1"
            shift
            ;;
        --all)
            CLEAN_TYPE="all"
            shift
            ;;
        --list-duplicates)
            list_duplicates
            exit 0
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 显示清理配置
echo -e "${BLUE}=== 工作空间清理配置 ===${NC}"
echo -e "清理类型: ${GREEN}$CLEAN_TYPE${NC}"
echo -e "预览模式: ${GREEN}$([ "$DRY_RUN" = true ] && echo "启用" || echo "禁用")${NC}"
echo -e "工作目录: ${GREEN}$(pwd)${NC}"
echo ""

if [ "$DRY_RUN" = true ]; then
    echo -e "${YELLOW}注意: 这是预览模式，不会实际删除文件${NC}"
    echo ""
fi

# 执行清理
case $CLEAN_TYPE in
    "backup")
        clean_backup_dirs
        ;;
    "ros1")
        clean_ros1_dirs
        ;;
    "all")
        clean_backup_dirs
        clean_ros1_dirs
        clean_3rdparty_duplicates
        clean_build_cache
        ;;
esac

echo ""
if [ "$DRY_RUN" = true ]; then
    echo -e "${BLUE}=== 预览完成 ===${NC}"
    echo -e "${YELLOW}要实际执行清理，请运行: $0 --$CLEAN_TYPE${NC}"
else
    echo -e "${GREEN}=== 清理完成 ===${NC}"
    echo -e "${BLUE}现在可以运行构建脚本:${NC}"
    echo -e "  ${GREEN}./build_xiaoli.sh${NC}  # xiaoli专用构建"
    echo -e "  ${GREEN}./build.sh${NC}         # 通用构建"
fi
