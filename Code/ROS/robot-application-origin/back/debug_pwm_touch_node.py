#!/usr/bin/env python3

"""
pwm_touch节点调试启动脚本
解决相对导入问题，用于VSCode调试
"""

import sys
import os

# 添加包路径到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
pkg_src_dir = os.path.join(current_dir, 'xiaoli_application_ros2', 'src', 'pwm_touch')
sys.path.insert(0, pkg_src_dir)

# 导入并运行主函数
try:
    from pwm_touch.pwm_touch_node import main
    
    if __name__ == '__main__':
        main()
except ImportError as e:
    print(f"导入错误: {e}")
    print(f"请检查 xiaoli_application_ros2/src/pwm_touch/pwm_touch/pwm_touch_node.py 是否存在")
    sys.exit(1)
