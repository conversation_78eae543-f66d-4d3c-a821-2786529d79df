#!/bin/bash

# xiaoli network节点快速启动脚本
# 简化版本，快速启动network节点

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}=== 启动xiaoli network节点 ===${NC}"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="$SCRIPT_DIR"

# 设置ROS2环境
if [ -z "$ROS_DISTRO" ]; then
    echo -e "${YELLOW}设置ROS2环境...${NC}"
    if [ -f "/opt/ros/humble/setup.bash" ]; then
        source /opt/ros/humble/setup.bash
        echo -e "${GREEN}✓ ROS2 Humble环境已设置${NC}"
    else
        echo -e "${RED}✗ 未找到ROS2环境${NC}"
        exit 1
    fi
fi

# 设置工作空间环境
INSTALL_DIR="$WORKSPACE_DIR/install/x86_64/Debug"
if [ -f "$INSTALL_DIR/setup.bash" ]; then
    source "$INSTALL_DIR/setup.bash"
    echo -e "${GREEN}✓ 工作空间环境已设置${NC}"
else
    echo -e "${RED}✗ 工作空间未编译，请先运行: ./xiaoli_application_ros2/build_xiaoli.sh network${NC}"
    exit 1
fi

# 设置环境变量
export ROS_DOMAIN_ID=0
export RCUTILS_LOGGING_BUFFERED_STREAM=1
export RCUTILS_CONSOLE_OUTPUT_FORMAT="[{severity}] [{name}]: {message}"

# 检查配置文件
CONFIG_FILE="$WORKSPACE_DIR/network_config.yaml"
if [ ! -f "$CONFIG_FILE" ]; then
    CONFIG_FILE="$WORKSPACE_DIR/xiaoli_application_ros2/src/launch_package/configs/robot_config.yaml"
fi

if [ -f "$CONFIG_FILE" ]; then
    echo -e "${GREEN}✓ 使用配置文件: $CONFIG_FILE${NC}"
    CONFIG_ARGS="--ros-args --params-file $CONFIG_FILE"
else
    echo -e "${YELLOW}⚠ 未找到配置文件，使用默认配置${NC}"
    CONFIG_ARGS=""
fi

echo -e "${BLUE}启动network节点...${NC}"
echo -e "${YELLOW}按Ctrl+C停止节点${NC}"
echo ""

# 启动节点
exec ros2 run network network_node $CONFIG_ARGS
