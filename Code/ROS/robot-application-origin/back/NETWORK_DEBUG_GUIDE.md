# 🐛 network节点VSCode调试指南

## 🎯 **可用的调试配置**

现在您有多种方式在VSCode中调试network节点：

### 1. **🔥 network节点快速调试** (推荐)
- **直接调试源码** - 在源码中设置断点
- **自定义配置** - 使用network_config.yaml配置文件
- **详细日志** - DEBUG级别日志输出
- **完整环境** - 自动设置所有环境变量

### 2. **🐛 network节点调试 (Python源码)**
- **源码调试** - 直接调试源码文件
- **路径映射** - 支持源码和安装版本的路径映射
- **自动编译** - 调试前自动编译

### 3. **🐛 network节点调试 (安装版本)**
- **安装版本调试** - 调试编译后的安装版本
- **生产环境** - 模拟实际运行环境

### 4. **🚀 xiaoli一键编译+调试 - network (Python)**
- **一键操作** - 编译+调试一步完成
- **调试脚本** - 使用debug_network_node.py启动脚本

## 🚀 **使用方法**

### 方法1: 调试面板 (推荐)
1. 按 `Ctrl+Shift+D` 打开调试面板
2. 选择 `🔥 network节点快速调试`
3. 在源码中设置断点：
   - `xiaoli_application_ros2/src/network/network/network_node.py`
   - `xiaoli_application_ros2/src/network/network/network_manager.py`
   - `xiaoli_application_ros2/src/network/network/dbus_monitor.py`
   - `xiaoli_application_ros2/src/network/network/cellular_control.py`
4. 点击绿色播放按钮 ▶️ 开始调试

### 方法2: 快捷键
1. 打开要调试的Python文件
2. 按 `F5` 启动调试
3. 选择调试配置

### 方法3: 命令面板
1. 按 `Ctrl+Shift+P`
2. 输入 "Debug: Start Debugging"
3. 选择network调试配置

## 🔧 **调试配置详解**

### 🔥 network节点快速调试
```json
{
  "name": "🔥 network节点快速调试",
  "type": "debugpy",
  "request": "launch",
  "program": "${workspaceFolder}/xiaoli_application_ros2/src/network/network/network_node.py",
  "args": [
    "--ros-args",
    "--params-file",
    "${workspaceFolder}/network_config.yaml",
    "--log-level",
    "DEBUG"
  ],
  "console": "integratedTerminal",
  "cwd": "${workspaceFolder}",
  "envFile": "${workspaceFolder}/.vscode/ros2.env",
  "python": "/usr/bin/python3.10",
  "justMyCode": false,
  "env": {
    "ROS_DOMAIN_ID": "0",
    "RCUTILS_LOGGING_BUFFERED_STREAM": "1",
    "RCUTILS_CONSOLE_OUTPUT_FORMAT": "[{severity}] [{time}] [{name}]: {message}"
  }
}
```

### 配置特点
- ✅ **直接源码调试** - 无需编译即可调试
- ✅ **自动环境设置** - 使用.vscode/ros2.env环境文件
- ✅ **自定义配置** - 使用network_config.yaml配置文件
- ✅ **详细日志** - DEBUG级别日志，带时间戳
- ✅ **完整调试** - justMyCode=false，可调试所有代码

## 🎯 **调试功能**

### 断点调试
- ✅ **行断点** - 在代码行设置断点
- ✅ **条件断点** - 设置条件断点
- ✅ **异常断点** - 在异常处自动停止
- ✅ **函数断点** - 在函数入口设置断点

### 变量查看
- ✅ **局部变量** - 查看当前作用域变量
- ✅ **全局变量** - 查看全局变量
- ✅ **对象属性** - 查看对象内部属性
- ✅ **ROS节点状态** - 查看节点、话题、服务状态

### 调试控制
- ✅ **单步执行** - F10 逐行执行
- ✅ **步入函数** - F11 进入函数内部
- ✅ **步出函数** - Shift+F11 跳出函数
- ✅ **继续执行** - F5 继续到下一个断点

### 调试控制台
- ✅ **表达式求值** - 在调试控制台执行Python表达式
- ✅ **变量修改** - 运行时修改变量值
- ✅ **函数调用** - 调用任意Python函数

## 📁 **调试文件结构**

### 源码文件 (可设置断点)
```
xiaoli_application_ros2/src/network/network/
├── network_node.py          # 主节点文件
├── network_manager.py       # 网络管理器
├── dbus_monitor.py         # DBus监控
├── cellular_control.py     # 蜂窝网络控制
└── __init__.py             # 包初始化
```

### 配置文件
```
├── network_config.yaml                    # 专用配置文件
├── xiaoli_application_ros2/src/launch_package/configs/robot_config.yaml  # 默认配置
└── .vscode/ros2.env                      # 环境变量文件
```

## 🔍 **调试技巧**

### 1. 设置断点的最佳位置
```python
# network_node.py
def __init__(self):  # 节点初始化
def check_network_conflict(self):  # 网络冲突检测
def handle_robot_control_service_request(self, req, response):  # 服务请求处理

# network_manager.py  
def check_network_status(self):  # 网络状态检查
def handle_network_status(self, request, response):  # 网络服务处理
def _perform_dns_health_check_and_fix(self, interface, timeout):  # DNS健康检查

# dbus_monitor.py
def monitor_network_status(self):  # 网络状态监控
def handle_state_change(self, device_path, new_state):  # 状态变化处理
```

### 2. 查看关键变量
```python
# 在调试控制台中执行
self.config                    # 网络配置状态
self.dns_servers              # DNS服务器配置
self.network_manager.bind_mode # 绑定模式状态
self.cellular_controller      # 蜂窝网络控制器状态
```

### 3. 测试网络功能
```python
# 在调试控制台中测试
self.network_manager.check_interface_status("wlan0")  # 检查WiFi状态
self.network_manager.check_external_connectivity("eth1")  # 检查外网连接
self.network_manager.find_best_dns_server()  # 查找最佳DNS
```

## 🛠️ **故障排除**

### 常见问题

1. **调试器无法启动**
   ```bash
   # 检查Python路径
   which python3.10
   
   # 检查环境文件
   cat .vscode/ros2.env
   
   # 重新生成环境
   ./update_vscode_env.sh
   ```

2. **断点不生效**
   - 确保使用源码文件路径
   - 检查justMyCode设置为false
   - 验证Python解释器路径

3. **模块导入错误**
   ```bash
   # 检查PYTHONPATH
   echo $PYTHONPATH
   
   # 重新编译
   ./xiaoli_application_ros2/run_network_node.sh --build-only
   ```

4. **ROS环境问题**
   ```bash
   # 检查ROS环境
   echo $ROS_DISTRO
   
   # 重新设置环境
   source /opt/ros/humble/setup.bash
   source install/x86_64/Debug/setup.bash
   ```

## 📊 **调试性能**

### 调试模式 vs 正常运行
- **调试模式**: 可设置断点，变量查看，单步执行
- **性能影响**: 轻微性能下降，适合开发调试
- **日志级别**: DEBUG级别，详细输出
- **环境隔离**: 使用独立的调试环境

## 🎊 **总结**

✅ **network节点VSCode调试配置完成！**

现在您可以：
- 🐛 **直接调试源码** - 在Python源码中设置断点
- 🔍 **实时变量查看** - 监控网络状态、DNS配置等
- ⚡ **快速问题定位** - 精确定位网络问题
- 🎯 **功能测试** - 在调试环境中测试网络功能
- 📊 **性能分析** - 分析网络检测和DNS切换性能

### 立即开始调试
1. 按 `Ctrl+Shift+D` 打开调试面板
2. 选择 `🔥 network节点快速调试`
3. 在 `network_node.py` 的 `__init__` 方法设置断点
4. 按 `F5` 开始调试
5. 享受专业级的Python调试体验！
