# xiaoli network节点运行脚本使用指南

## 🚀 **快速开始**

### 方法1: 快速启动 (推荐)
```bash
# 一键启动network节点
./start_network.sh
```

### 方法2: 完整功能启动
```bash
# 使用完整功能脚本
./run_network_node.sh

# 或使用自定义配置
./run_network_node.sh --config network_config.yaml --debug
```

## 📁 **文件说明**

### 启动脚本
- **`start_network.sh`** - 快速启动脚本，简单易用
- **`run_network_node.sh`** - 完整功能脚本，支持多种选项

### 配置文件
- **`network_config.yaml`** - 专用network节点配置文件
- **`xiaoli_application_ros2/src/launch_package/configs/robot_config.yaml`** - 默认配置文件

## 🎯 **使用方法**

### 快速启动脚本 (start_network.sh)
```bash
# 基本启动
./start_network.sh

# 脚本会自动：
# 1. 设置ROS2环境
# 2. 加载工作空间环境
# 3. 查找配置文件
# 4. 启动network节点
```

### 完整功能脚本 (run_network_node.sh)
```bash
# 显示帮助
./run_network_node.sh --help

# 基本启动
./run_network_node.sh

# 调试模式启动
./run_network_node.sh --debug

# 使用自定义配置文件
./run_network_node.sh --config /path/to/config.yaml

# 设置ROS域ID
./run_network_node.sh --domain-id 1

# 设置日志级别
./run_network_node.sh --log-level DEBUG

# 详细模式
./run_network_node.sh --verbose

# 仅检查环境
./run_network_node.sh --check-only
```

## ⚙️ **配置文件详解**

### network_config.yaml 主要配置项

#### 网络接口配置
```yaml
network_node:
  ros__parameters:
    wifi_connect_interface: "wlan0"        # WiFi接口
    p2p_connect_interface: "wlan1"         # AP接口
    mobile_connect_interface: "eth1"       # 蜂窝网络接口
```

#### AP热点配置
```yaml
    ssid: "xiaoli51"                       # 热点名称
    static_ip: "***********"               # 静态IP
    ap_subnet: "***********/24"            # 子网配置
```

#### 蜂窝网络配置
```yaml
    cellular_option: "enable"              # 启用蜂窝网络
    cellular_apn: "cmnet"                  # APN设置
    cellular_timeout: 30                   # 连接超时
```

#### 检测间隔配置
```yaml
    network_check_interval: 10.0           # 网络检查间隔
    dns_check_interval: 300.0              # DNS检查间隔
    conflict_check_interval: 5.0           # 冲突检查间隔
```

#### DNS配置
```yaml
    primary_dns: "*******"                # 主DNS
    secondary_dns: "*******"              # 备用DNS
    dns_test_domains:                      # 测试域名
      - "www.baidu.com"
      - "www.google.com"
```

## 🔧 **环境要求**

### 系统要求
- Ubuntu 20.04/22.04
- ROS2 Humble/Galactic
- Python 3.8+

### 依赖包
- network包 (已编译)
- homi_speech_interface包 (已编译)
- NetworkManager
- systemd-resolved

### 权限要求
- 网络配置权限 (sudo)
- GPIO访问权限 (如果使用硬件)

## 🚀 **启动流程**

### 自动检查项目
1. ✅ **ROS2环境** - 自动检测和设置
2. ✅ **工作空间** - 加载编译后的包
3. ✅ **依赖包** - 检查必要的依赖
4. ✅ **配置文件** - 查找和加载配置
5. ✅ **权限检查** - 验证必要权限

### 环境变量设置
```bash
# ROS2环境
ROS_DISTRO=humble
ROS_DOMAIN_ID=0

# Python路径
PYTHONPATH=.../network/lib/python3.10/site-packages:...

# 库路径
LD_LIBRARY_PATH=.../install/.../lib:...

# 日志配置
RCUTILS_LOGGING_BUFFERED_STREAM=1
RCUTILS_CONSOLE_OUTPUT_FORMAT="[{severity}] [{name}]: {message}"
```

## 🔍 **监控和调试**

### 节点状态检查
```bash
# 查看节点列表
ros2 node list

# 查看network节点信息
ros2 node info /network_node

# 查看节点日志
ros2 log view /network_node
```

### 话题和服务
```bash
# 查看发布的话题
ros2 topic list | grep network

# 查看提供的服务
ros2 service list | grep network

# 监控网络状态话题
ros2 topic echo /internet_connect_status

# 监控DNS状态话题
ros2 topic echo /dns_status
```

### 服务调用
```bash
# 调用网络服务
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{command: 'status'}"

# 调用机器人控制服务
ros2 service call /homi_speech/sigc_data_service_APP homi_speech_interface/srv/SIGCData "{data: 'openap_notify'}"
```

## 🛠️ **故障排除**

### 常见问题

1. **节点启动失败**
   ```bash
   # 检查环境
   ./run_network_node.sh --check-only
   
   # 重新编译
   ./xiaoli_application_ros2/build_xiaoli.sh network
   ```

2. **找不到配置文件**
   ```bash
   # 使用绝对路径
   ./run_network_node.sh --config /full/path/to/config.yaml
   ```

3. **权限问题**
   ```bash
   # 添加用户到相关组
   sudo usermod -a -G dialout,netdev $USER
   
   # 重新登录或重启
   ```

4. **网络接口问题**
   ```bash
   # 检查网络接口
   ip link show
   
   # 检查NetworkManager状态
   systemctl status NetworkManager
   ```

### 调试模式
```bash
# 启用调试模式
./run_network_node.sh --debug --verbose

# 查看详细日志
./run_network_node.sh --log-level DEBUG
```

## 📊 **性能监控**

### 系统资源
```bash
# 监控CPU和内存使用
top -p $(pgrep -f network_node)

# 监控网络流量
iftop -i wlan0
```

### ROS2性能
```bash
# 监控话题频率
ros2 topic hz /internet_connect_status

# 监控服务响应时间
ros2 service call /homi_speech/network_service ...
```

## 🎊 **总结**

✅ **network节点运行脚本已完成！**

- 🚀 **快速启动** - `./start_network.sh` 一键启动
- 🔧 **完整功能** - `./run_network_node.sh` 支持多种选项
- ⚙️ **配置文件** - `network_config.yaml` 详细配置
- 🔍 **环境检查** - 自动检查和设置环境
- 🐛 **调试支持** - 多种调试和监控选项

现在您可以轻松启动和管理xiaoli network节点了！
