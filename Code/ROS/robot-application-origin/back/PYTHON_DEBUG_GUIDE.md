# Python ROS2节点调试指南

## 🎯 配置完成

✅ **Python源码直接调试配置已完成！**

## 🚀 如何使用

### 1. 调试network节点
1. 在VSCode中打开调试面板 (`Ctrl+Shift+D`)
2. 选择 `🚀 ROS2 Debug - network_node (Python源码直调)`
3. 在 `xiaoli_application_ros2/src/network/network/network_node.py` 中设置断点
4. 按 `F5` 开始调试

### 2. 调试其他Python节点
- `🚀 ROS2 Debug - ble_node (Python源码直调)`
- `🚀 ROS2 Debug - a2dp_node (Python源码直调)`

## 🔧 配置特点

### ✅ 优势
- **无需编译**：Python节点直接运行源码
- **断点精确**：直接在源码中设置断点
- **启动快速**：跳过编译步骤，立即开始调试
- **实时修改**：修改代码后可立即重新调试

### 📁 文件结构
```
.vscode/
├── launch.json                 # 调试配置
├── ros2_python_debug.env      # Python调试专用环境变量
└── ...

xiaoli_application_ros2/src/network/network/
├── network_node.py            # 主节点文件 (可设置断点)
├── network_manager.py         # 网络管理器 (可设置断点)
├── cellular_control.py       # 蜂窝控制 (可设置断点)
└── ...
```

## 🎯 调试技巧

### 推荐断点位置
```python
# network_node.py
def __init__(self):                    # 节点初始化
def check_network_conflict(self):      # 网络冲突检测
def handle_robot_control_service_request(self, req, response):  # 服务请求
```

### 环境变量
- `ROS_DOMAIN_ID=0`
- `ROS_DISTRO=humble`
- 正确的`PYTHONPATH`配置

## 🔍 故障排除

### 如果遇到导入错误
1. 检查 `.vscode/ros2_python_debug.env` 文件
2. 确保 `PYTHONPATH` 包含正确路径
3. 运行测试脚本验证环境：`python3 test_network_simple.py`

### 如果节点启动失败
1. 检查ROS2环境是否正确
2. 确保必要的系统依赖已安装
3. 查看调试控制台的错误信息

## 🎉 开始调试

现在您可以：
1. 在 `network_node.py` 的第24行 `__init__` 方法中设置断点
2. 选择调试配置 `🚀 ROS2 Debug - network_node (Python源码直调)`
3. 按F5开始调试
4. 享受完整的Python调试体验！
