#!/bin/bash

# WiFi 连接测试脚本
# 使用方法: ./wifi_test.sh <SSID> <PASSWORD> <TEST_COUNT> <LOG_FILE>

# 检查参数数量
if [ $# -ne 4 ]; then
    echo "使用方法: $0 <SSID> <PASSWORD> <TEST_COUNT> <LOG_FILE>"
    echo "示例: $0 \"MyWiFi\" \"password123\" 10 wifi_test.log"
    exit 1
fi

SSID=$1
PASSWORD=$2
TEST_COUNT=$3
LOG_FILE=$4

# 检查测试次数是否为数字
if ! [[ "$TEST_COUNT" =~ ^[0-9]+$ ]]; then
    echo "错误: 测试次数必须是正整数"
    exit 1
fi

# 初始化成功和失败计数器
SUCCESS=0
FAILURE=0

echo "开始测试 WiFi 连接: $SSID" > "$LOG_FILE"
echo "测试时间: $(date)" >> "$LOG_FILE"
echo "测试次数: $TEST_COUNT" >> "$LOG_FILE"
echo "------------------------" >> "$LOG_FILE"

# 初始扫描一次
echo "正在扫描可用的 WiFi 网络..." | tee -a "$LOG_FILE"
nmcli device wifi rescan
sleep 3

for ((i=1; i<=TEST_COUNT; i++))
do
    echo "测试 $i: 尝试连接到 $SSID" | tee -a "$LOG_FILE"
    
    # 删除已保存的网络连接配置
    nmcli connection delete "$SSID" 2>/dev/null
    
    # 尝试连接到指定的 WiFi 网络
    if nmcli device wifi connect "$SSID" password "$PASSWORD" 2>/dev/null; then
        echo "测试 $i: 连接成功" | tee -a "$LOG_FILE"
        SUCCESS=$((SUCCESS+1))
        
        # 保持连接一段时间，确保连接稳定
        sleep 10
        
        # 断开连接
        nmcli connection down "$SSID" 2>/dev/null
    else
        echo "测试 $i: 连接失败" | tee -a "$LOG_FILE"
        FAILURE=$((FAILURE+1))
    fi
    
    # 等待一段时间再进行下一次测试
    sleep 8
done

# 计算成功率（使用整数计算避免bc依赖）
TOTAL=$TEST_COUNT
if [ $TOTAL -gt 0 ]; then
    SUCCESS_RATE=$((SUCCESS * 100 / TOTAL))
else
    SUCCESS_RATE=0
fi

# 输出测试结果
echo "------------------------" | tee -a "$LOG_FILE"
echo "测试完成!" | tee -a "$LOG_FILE"
echo "总测试次数: $TOTAL" | tee -a "$LOG_FILE"
echo "成功次数: $SUCCESS" | tee -a "$LOG_FILE"
echo "失败次数: $FAILURE" | tee -a "$LOG_FILE"
echo "成功率: ${SUCCESS_RATE}%" | tee -a "$LOG_FILE"
