# homi_speech 接口文档

## 1. 文档概述

### 1.1 文档目的
本文档详细描述了 homi_speech 模块提供的所有接口，包括 ROS2 Topics、Services、消息类型和平台通信接口。

### 1.2 适用范围
- ROS2 Topics/Services 接口
- 平台通信接口
- 音频流接口
- IoT控制接口

## 2. ROS2 Topics 接口

### 2.1 发布 Topics

#### 2.1.1 平台事件发布
```cpp
Topic: "/homi_speech/sigc_event_topic"
Type: homi_speech_interface::msg::SIGCEvent
QoS: 队列大小 10
功能: 发布平台事件到其他模块
```

**消息格式**:
```cpp
struct SIGCEvent {
    string event;  // JSON格式的事件数据
};
```

#### 2.1.2 语音助手状态发布
```cpp
Topic: "/homi_speech/speech_assistant_status_topic"
Type: homi_speech_interface::msg::AssistantEvent
QoS: 队列大小 10
功能: 发布语音助手状态变化
```

**消息格式**:
```cpp
struct AssistantEvent {
    int32 status;        // 状态码
    string description;  // 状态描述
    string msg;          // 消息内容
    string section_id;   // 会话ID
};
```

#### 2.1.3 快递事件发布
```cpp
Topic: "/express_event_topic"
Type: std_msgs::msg::String
QoS: 队列大小 10
功能: 发布快递相关事件
```

#### 2.1.4 连接状态发布
```cpp
Topic: "/connect_status_topic"
Type: std_msgs::msg::String
QoS: 队列大小 10
功能: 发布网络连接状态
```

**消息示例**:
```json
{"netstatus": 1}  // 1-连接正常, 0-连接异常
```

### 2.2 订阅 Topics

#### 2.2.1 PCM音频流订阅
```cpp
Topic: "/pcm_stream_topic"
Type: homi_speech_interface::msg::PCMStream
QoS: 队列大小 50
功能: 接收音频采集模块的PCM数据流
```

**消息格式**:
```cpp
struct PCMStream {
    std_msgs::Header header;
    bytes data;          // PCM音频数据
    int32 sample_rate;   // 采样率
    int32 channels;      // 通道数
    int32 bits_per_sample; // 位深度
};
```

#### 2.2.2 任务状态订阅
```cpp
Topic: "/task_status_topic"
Type: homi_speech_interface::msg::TaskStatus
QoS: 队列大小 10
功能: 接收任务执行状态
```

## 3. ROS2 Services 接口

### 3.1 核心服务

#### 3.1.1 平台数据服务
```cpp
Service: "/homi_speech/sigc_data_service"
Type: homi_speech_interface::srv::SIGCData
功能: 接收其他模块发送的平台数据并转发到机器人平台
```

**请求格式**:
```cpp
struct SIGCDataRequest {
    string data;  // JSON格式的数据
};
```

**响应格式**:
```cpp
struct SIGCDataResponse {
    int32 error_code;  // 0-成功, 非0-失败
};
```

**实现逻辑**:
```cpp
static void SGICDataCallback(const std::shared_ptr<homi_speech_interface::srv::SIGCData::Request> req,
                            std::shared_ptr<homi_speech_interface::srv::SIGCData::Response> res) {
    RCLCPP_INFO(rclcpp::get_logger("speech_core"), "SGICDataCallback %s", req->data.c_str());
    
    // 调用内部API发送到平台 (超时2秒)
    auto ret = homi::inner::sendEventOOB(std::make_shared<std::string>(req->data), false, 2000);
    
    if (ret < 0) {
        res->error_code = -1;
    } else {
        res->error_code = 0;
    }
}
```

#### 3.1.2 语音助手控制服务
```cpp
Service: "/homi_speech/speech_assistant_ctrl_service"
Type: homi_speech_interface::srv::AssistantCtrl
功能: 控制语音助手的启动、停止和配置
```

**请求格式**:
```cpp
struct AssistantCtrlRequest {
    bool is_normal;           // 是否正常模式
    bool start;               // 启动标志
    bool stop;                // 停止标志
    int32 start_wait_ms;      // 启动等待时间(ms)
    int32 stop_wait_ms;       // 停止等待时间(ms)
    string inquiry_text;      // 查询文本
    int32 inquiry_sub_type;   // 查询子类型
    bool multiple_wheels;     // 多轮对话
    bool notify_user_abort;   // 通知用户中止
};
```

**响应格式**:
```cpp
struct AssistantCtrlResponse {
    string event_id;     // 事件ID
    int32 error_code;    // 错误码
};
```

#### 3.1.3 IoT控制服务
```cpp
Service: "/homi_speech/speech_iot_control_service"
Type: homi_speech_interface::srv::IotControl
功能: 控制IoT设备
```

**请求格式**:
```cpp
struct IotControlRequest {
    string device_type;      // 设备类型
    string device_id;        // 设备ID
    string action;           // 控制动作
    string parameters;       // 控制参数(JSON)
};
```

**响应格式**:
```cpp
struct IotControlResponse {
    bool success;           // 是否成功
    string message;         // 响应消息
};
```

### 3.2 辅助服务

#### 3.2.1 语音播报服务
```cpp
Service: "/homi_speech/helper_assistant_speech_text_service"
Type: homi_speech_interface::srv::AssistantSpeechText
功能: 文本转语音播报
```

**请求格式**:
```cpp
struct AssistantSpeechTextRequest {
    string text;            // 要播报的文本
    string voice_name;      // 语音名称
    float speed;            // 播报速度
    float volume;           // 音量
};
```

**响应格式**:
```cpp
struct AssistantSpeechTextResponse {
    bool success;           // 是否成功
    string message;         // 响应消息
};
```

#### 3.2.2 语音中止服务
```cpp
Service: "/homi_speech/helper_assistant_abort_service"
Type: homi_speech_interface::srv::AssistantAbort
功能: 中止当前语音播报
```

#### 3.2.3 拍照服务
```cpp
Service: "/homi_speech/helper_assistant_takephoto_service"
Type: homi_speech_interface::srv::AssistantTakePhoto
功能: 拍照功能
```

**请求格式**:
```cpp
struct AssistantTakePhotoRequest {
    string save_path;       // 保存路径
    string photo_name;      // 照片名称
    int32 quality;          // 照片质量
};
```

**响应格式**:
```cpp
struct AssistantTakePhotoResponse {
    bool success;           // 是否成功
    string photo_path;      // 照片路径
    string message;         // 响应消息
};
```

#### 3.2.4 图片上传服务
```cpp
Service: "/homi_speech/speech_upload_image_url_service"
Type: homi_speech_interface::srv::UploadImageUrl
功能: 上传图片到服务器
```

### 3.3 多媒体服务

#### 3.3.1 视频流服务
```cpp
Service: "/homi_speech/helper_get_video_stream_service"
Type: homi_speech_interface::srv::GetVideoStream
功能: 获取视频流
```

**请求格式**:
```cpp
struct GetVideoStreamRequest {
    string resolution;      // 分辨率 (sd/hd/fhd)
};
```

**响应格式**:
```cpp
struct GetVideoStreamResponse {
    bool success;           // 是否成功
    string socket_path;     // Socket路径
};
```

#### 3.3.2 音频播放服务
```cpp
Service: "/homi_speech/helper_get_pcm_player_service"
Type: homi_speech_interface::srv::GetPcmPlayer
功能: 获取PCM播放权限
```

#### 3.3.3 文件播放服务
```cpp
Service: "/homi_speech/helper_play_file_service"
Type: homi_speech_interface::srv::PlayFile
功能: 播放音频文件
```

### 3.4 网络服务

#### 3.4.1 网络控制服务
```cpp
Service: "/homi_speech/net_ctrl_service"
Type: homi_speech_interface::srv::NetCtrl
功能: 网络控制和配置
```

#### 3.4.2 电话服务
```cpp
Service: "/homi_speech/helper_phone_call_service"
Type: homi_speech_interface::srv::PhoneCall
功能: 电话呼叫控制
```

**请求格式**:
```cpp
struct PhoneCallRequest {
    string type;            // 服务类型 (pickup/hangup/callout)
    string phone_number;    // 电话号码
};
```

**响应格式**:
```cpp
struct PhoneCallResponse {
    bool status;            // 操作状态
};
```

## 4. 消息类型定义

### 4.1 核心消息类型

#### 4.1.1 SIGCEvent
```cpp
// 平台事件消息
struct SIGCEvent {
    string event;  // JSON格式的事件数据
};
```

#### 4.1.2 AssistantEvent
```cpp
// 语音助手事件
struct AssistantEvent {
    int32 status;        // 状态码
    string description;  // 状态描述
    string msg;          // 消息内容
    string section_id;   // 会话ID
};
```

#### 4.1.3 PCMStream
```cpp
// PCM音频流
struct PCMStream {
    std_msgs::Header header;
    bytes data;              // PCM音频数据
    int32 sample_rate;       // 采样率
    int32 channels;          // 通道数
    int32 bits_per_sample;   // 位深度
};
```

#### 4.1.4 Wakeup
```cpp
// 语音唤醒消息
struct Wakeup {
    string ivw_word;     // 唤醒词
    int32 angle;         // 声源方向角度
    string extra_info;   // 额外信息
};
```

### 4.2 任务相关消息

#### 4.2.1 Task
```cpp
// 任务消息
struct Task {
    string task_id;         // 任务ID
    string actiontype;      // 动作类型
    string actionargument;  // 动作参数
    TaskStep[] steps;       // 任务步骤
};
```

#### 4.2.2 TaskStatus
```cpp
// 任务状态消息
struct TaskStatus {
    string task_id;         // 任务ID
    int32 status;           // 状态码
    string message;         // 状态消息
    float progress;         // 进度百分比
};
```

## 5. 平台通信接口

### 5.1 内部API接口

#### 5.1.1 事件发送接口
```cpp
// 发送事件到平台 (带超时)
int sendEventOOB(const std::shared_ptr<std::string> &dataPtr, 
                 const bool isBinary = false, 
                 unsigned int milliseconds = 0);

// 发送结构化事件到平台
int sendEventOOB(const std::string &domain, 
                 const std::string &event, 
                 const bool response, 
                 const nlohmann::json &body, 
                 unsigned int milliseconds = 0);
```

#### 5.1.2 事件注册接口
```cpp
// 注册默认事件处理器
int registerDefaultEvent(const std::function<void(const std::string &json)> &func);

// 注册用户事件处理器
int regeisterUserEvent(const std::string &domain, 
                       const std::string &event, 
                       const std::function<void(const std::string &json)> &func);

// 注册网络状态回调
int registerSIGCNetWorkStatus(const std::function<void(const int status)> &func);
```

### 5.2 数据流向

#### 5.2.1 上行数据流
```
其他模块 -> SIGCData服务 -> homi::inner::sendEventOOB -> 机器人平台
```

#### 5.2.2 下行数据流
```
机器人平台 -> homi::inner事件 -> SIGCEvent Topic -> 其他模块
```

## 6. 错误码定义

### 6.1 服务错误码
- `0`: 操作成功
- `-1`: 操作失败
- `-2`: 参数错误
- `-3`: 超时错误
- `-4`: 网络错误

### 6.2 语音助手状态码
- `0`: 空闲状态
- `1`: 监听状态
- `2`: 识别中
- `3`: 处理中
- `4`: 播报中
- `5`: 错误状态

## 7. 使用示例

### 7.1 发送平台数据
```cpp
// 创建服务客户端
auto client = node->create_client<homi_speech_interface::srv::SIGCData>(
    "/homi_speech/sigc_data_service");

// 创建请求
auto request = std::make_shared<homi_speech_interface::srv::SIGCData::Request>();
request->data = R"({"event": "test", "data": "hello"})";

// 发送请求
auto future = client->async_send_request(request);
```

### 7.2 语音播报
```cpp
// 创建服务客户端
auto client = node->create_client<homi_speech_interface::srv::AssistantSpeechText>(
    "/homi_speech/helper_assistant_speech_text_service");

// 创建请求
auto request = std::make_shared<homi_speech_interface::srv::AssistantSpeechText::Request>();
request->text = "你好，我是机器狗";
request->voice_name = "xiaoli";
request->speed = 1.0;
request->volume = 0.8;

// 发送请求
auto future = client->async_send_request(request);
```

### 7.3 订阅语音助手状态
```cpp
// 创建订阅者
auto subscription = node->create_subscription<homi_speech_interface::msg::AssistantEvent>(
    "/homi_speech/speech_assistant_status_topic", 10,
    [](const homi_speech_interface::msg::AssistantEvent::SharedPtr msg) {
        RCLCPP_INFO(rclcpp::get_logger("example"), 
                   "Assistant status: %d, msg: %s", msg->status, msg->msg.c_str());
    });
```

---

**文档版本**: v1.0  
**创建日期**: 2025-07-14  
**最后更新**: 2025-07-14  
**维护者**: homi_speech 开发团队  
**审核者**: 系统架构师
