# homi_speech 模块需求文档

## 1. 项目概述

### 1.1 项目背景
homi_speech 是基于 ROS2 的智能语音交互系统，为机器人提供完整的语音识别、语音合成、语音交互和平台通信功能。该模块是机器人与用户进行自然语言交互的核心组件。

### 1.2 技术栈
- **框架**: ROS2 (Foxy/Humble)
- **编程语言**: C++14
- **语音引擎**: homi_sdk (自研语音SDK)
- **音频处理**: ALSA、PCM流处理
- **通信协议**: ROS2 Topics/Services、WebSocket
- **依赖库**: nlohmann/json、ThreadPool

### 1.3 核心功能
- 语音识别 (ASR)
- 语音合成 (TTS)
- 语音唤醒
- 语音交互管理
- 平台数据通信
- IoT设备控制
- 多媒体处理

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    homi_speech 主程序                       │
├─────────────────────────────────────────────────────────────┤
│  speech_core (语音核心)                                     │
│  ├── SimpleSpeechApp (语音应用引擎)                         │
│  ├── AudioStream (音频流处理)                               │
│  ├── ThreadPool (线程池)                                    │
│  └── Platform Communication (平台通信)                     │
├─────────────────────────────────────────────────────────────┤
│  capture (音频采集)                                         │
│  ├── AlsaHelperCapture (ALSA音频采集)                       │
│  └── PCM Stream Publisher (PCM流发布)                       │
├─────────────────────────────────────────────────────────────┤
│  helper (辅助服务)                                          │
│  ├── Assistant Services (语音助手服务)                      │
│  ├── Photo Services (拍照服务)                              │
│  ├── Media Services (多媒体服务)                            │
│  └── Network Services (网络服务)                            │
├─────────────────────────────────────────────────────────────┤
│  wakeup (语音唤醒)                                          │
│  └── Wake Word Detection (唤醒词检测)                       │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 模块组成
- **speech_core**: 语音核心处理模块
- **capture**: 音频采集模块
- **helper**: 辅助服务模块
- **wakeup**: 语音唤醒模块

## 3. 功能需求

### 3.1 语音识别功能
**需求描述**: 提供实时语音识别能力

**功能列表**:
- 连续语音识别
- 多语言支持
- 噪声抑制
- 回声消除
- 语音端点检测
- 识别结果置信度评估

**技术指标**:
- 识别准确率: >95% (安静环境)
- 响应延迟: <500ms
- 支持采样率: 16kHz
- 音频格式: PCM
- 通道数: 单声道/立体声

### 3.2 语音合成功能
**需求描述**: 提供高质量的语音合成能力

**功能列表**:
- 文本转语音 (TTS)
- 多种音色选择
- 语速控制
- 音量控制
- 情感语音合成
- 实时语音播放

**技术指标**:
- 合成质量: 自然度 >4.0 (5分制)
- 合成延迟: <200ms
- 支持语言: 中文、英文
- 音频格式: WAV、PCM

### 3.3 语音交互管理
**需求描述**: 提供完整的语音交互流程管理

**功能列表**:
- 对话状态管理
- 多轮对话支持
- 上下文理解
- 意图识别
- 实体提取
- 对话中断和恢复

**交互流程**:
1. 语音唤醒
2. 语音识别
3. 意图理解
4. 业务处理
5. 语音反馈
6. 对话结束

### 3.4 平台通信功能
**需求描述**: 与机器人平台进行数据交互

**功能列表**:
- 平台事件接收
- 状态数据上报
- 指令下发处理
- 网络状态监控
- 数据格式转换

**通信接口**:
- SIGC数据服务
- 事件发布订阅
- 网络状态通知

### 3.5 IoT设备控制
**需求描述**: 通过语音控制IoT设备

**功能列表**:
- 智能家居设备控制
- 设备状态查询
- 场景模式切换
- 设备联动控制

**支持设备类型**:
- 灯光控制
- 空调控制
- 窗帘控制
- 音响控制
- 安防设备

### 3.6 多媒体处理
**需求描述**: 提供多媒体内容处理能力

**功能列表**:
- 拍照服务
- 视频流处理
- 图片上传
- 音频播放
- 视频播放

## 4. 非功能需求

### 4.1 性能需求
- **实时性**: 语音识别延迟 <500ms，语音合成延迟 <200ms
- **并发性**: 支持多线程处理，线程池管理
- **稳定性**: 系统连续运行时间 >24小时
- **资源占用**: CPU使用率 <30%，内存使用 <512MB

### 4.2 可靠性需求
- **异常处理**: 完善的异常捕获和恢复机制
- **错误恢复**: 自动重连和故障恢复
- **数据完整性**: 音频数据传输完整性保证
- **服务可用性**: 服务可用率 >99%

### 4.3 兼容性需求
- **操作系统**: Ubuntu 20.04/22.04
- **ROS版本**: ROS2 Foxy/Humble
- **音频设备**: 支持ALSA兼容的音频设备
- **网络协议**: 支持TCP/UDP/WebSocket

### 4.4 安全性需求
- **数据加密**: 敏感数据传输加密
- **访问控制**: 服务访问权限控制
- **隐私保护**: 语音数据隐私保护
- **审计日志**: 操作日志记录和审计

## 5. 接口规范

### 5.1 ROS2 Topics
**发布 Topics**:
- `/homi_speech/sigc_event_topic`: 平台事件发布
- `/homi_speech/speech_assistant_status_topic`: 语音助手状态
- `/express_event_topic`: 快递事件
- `/connect_status_topic`: 连接状态

**订阅 Topics**:
- `/pcm_stream_topic`: PCM音频流
- `/task_status_topic`: 任务状态

### 5.2 ROS2 Services
**提供的服务**:
- `/homi_speech/sigc_data_service`: 平台数据服务
- `/homi_speech/speech_assistant_ctrl_service`: 语音助手控制
- `/homi_speech/speech_upload_image_url_service`: 图片上传
- `/homi_speech/speech_iot_control_service`: IoT控制
- `/homi_speech/helper_assistant_abort_service`: 语音中止
- `/homi_speech/helper_assistant_quiet_service`: 静音控制
- `/homi_speech/helper_assistant_takephoto_service`: 拍照服务
- `/homi_speech/helper_assistant_speech_text_service`: 语音播报

### 5.3 消息类型
**核心消息**:
- `SIGCEvent`: 平台事件消息
- `SIGCData`: 平台数据消息
- `AssistantEvent`: 语音助手事件
- `PCMStream`: PCM音频流
- `Wakeup`: 语音唤醒消息

## 6. 数据模型

### 6.1 语音识别结果
```json
{
    "text": "识别的文本内容",
    "confidence": 0.95,
    "timestamp": "2025-07-14T10:30:00Z",
    "language": "zh-CN"
}
```

### 6.2 语音合成请求
```json
{
    "text": "要合成的文本",
    "voice": "xiaoli",
    "speed": 1.0,
    "volume": 0.8,
    "emotion": "neutral"
}
```

### 6.3 平台事件数据
```json
{
    "deviceId": "device_001",
    "domain": "DEVICE_INTERACTION",
    "event": "speech_recognition",
    "eventId": "event_123456789",
    "body": {
        "text": "识别结果",
        "confidence": 0.95
    }
}
```

## 7. 部署要求

### 7.1 硬件要求
- **CPU**: ARM64 或 x86_64
- **内存**: 最小 2GB RAM
- **存储**: 最小 16GB 存储空间
- **音频设备**: 麦克风、扬声器
- **网络**: 以太网或 WiFi 连接

### 7.2 软件依赖
- **操作系统**: Ubuntu 20.04/22.04
- **ROS2**: Foxy/Humble
- **编译器**: GCC 9.0+
- **CMake**: 3.8+
- **ALSA**: 音频驱动支持

### 7.3 配置文件
- **音频配置**: 采样率、通道数、设备选择
- **语音引擎配置**: 识别模型、合成音色
- **网络配置**: 平台连接地址、超时设置
- **日志配置**: 日志级别、输出路径

## 8. 测试要求

### 8.1 功能测试
- 语音识别准确性测试
- 语音合成质量测试
- 语音交互流程测试
- 平台通信测试

### 8.2 性能测试
- 响应延迟测试
- 并发处理测试
- 长时间稳定性测试
- 资源占用测试

### 8.3 兼容性测试
- 不同音频设备测试
- 不同网络环境测试
- 不同ROS2版本测试

---

**文档版本**: v1.0  
**创建日期**: 2025-07-14  
**维护者**: homi_speech 开发团队
