#!/usr/bin/env python3
"""
DNS配置更新测试脚本
用于验证 _ensure_dns_in_resolved 和 _update_systemd_resolved_conf 函数的功能
"""

import os
import subprocess
import tempfile

def test_resolv_conf_format():
    """测试 /etc/resolv.conf 格式是否正确"""
    print("=== 测试 /etc/resolv.conf 格式 ===")
    
    # 模拟DNS服务器列表
    test_dns_servers = ["223.5.5.5", "119.29.29.29", "114.114.114.114"]
    
    # 构建resolv.conf内容
    resolv_conf_content = "# Generated by NetworkManager\n"
    resolv_conf_content += "# DNS servers configured by xiaoli network manager\n"
    
    # 添加DNS服务器
    for dns in test_dns_servers:
        resolv_conf_content += f"nameserver {dns}\n"
    
    # 添加一些优化选项
    resolv_conf_content += "\n# DNS resolution options\n"
    resolv_conf_content += "options timeout:2\n"
    resolv_conf_content += "options attempts:3\n"
    resolv_conf_content += "options rotate\n"
    
    print("生成的 /etc/resolv.conf 内容:")
    print(resolv_conf_content)
    
    # 验证格式
    lines = resolv_conf_content.split('\n')
    nameserver_count = sum(1 for line in lines if line.startswith('nameserver'))
    
    print(f"DNS服务器数量: {nameserver_count}")
    print(f"预期DNS服务器数量: {len(test_dns_servers)}")
    
    if nameserver_count == len(test_dns_servers):
        print("✅ /etc/resolv.conf 格式测试通过")
    else:
        print("❌ /etc/resolv.conf 格式测试失败")
    
    return nameserver_count == len(test_dns_servers)

def test_systemd_resolved_conf_format():
    """测试 /etc/systemd/resolved.conf 格式是否正确"""
    print("\n=== 测试 /etc/systemd/resolved.conf 格式 ===")
    
    # 模拟DNS服务器列表
    test_dns_servers = ["223.5.5.5", "119.29.29.29"]
    
    # 模拟现有配置文件内容
    existing_config = """#  This file is part of systemd.
#
#  systemd is free software; you can redistribute it and/or modify it
#  under the terms of the GNU Lesser General Public License as published by
#  the Free Software Foundation; either version 2.1 of the License, or
#  (at your option) any later version.
#
# Entries in this file show the compile time defaults.
# You can change settings by editing this file.
# Defaults can be restored by simply deleting this file.
#
# See resolved.conf(5) for details

[Resolve]
#FallbackDNS=
#Domains=
#LLMNR=no
#MulticastDNS=no
#DNSSEC=no
#DNSOverTLS=no
#Cache=no-negative
#DNSStubListener=yes
#ReadEtcHosts=yes
"""
    
    # 处理配置文件
    lines = existing_config.split('\n')
    new_lines = []
    dns_line_added = False
    
    for line in lines:
        # 跳过现有的DNS配置行
        if line.strip().startswith('DNS=') or line.strip().startswith('#DNS='):
            continue
        new_lines.append(line)
    
    # 添加DNS配置
    dns_config_line = f"DNS={' '.join(test_dns_servers)}"
    
    # 在[Resolve]段后添加DNS配置
    for i, line in enumerate(new_lines):
        if '[Resolve]' in line:
            # 在[Resolve]段后插入DNS配置
            new_lines.insert(i + 1, dns_config_line)
            dns_line_added = True
            break
    
    if not dns_line_added:
        # 如果没有找到合适的位置，添加到文件末尾
        new_lines.append('[Resolve]')
        new_lines.append(dns_config_line)
    
    new_config = '\n'.join(new_lines)
    
    print("生成的 /etc/systemd/resolved.conf 内容:")
    print(new_config)
    
    # 验证格式
    has_resolve_section = '[Resolve]' in new_config
    has_dns_config = f"DNS={' '.join(test_dns_servers)}" in new_config
    
    print(f"包含 [Resolve] 段: {has_resolve_section}")
    print(f"包含 DNS 配置: {has_dns_config}")
    
    if has_resolve_section and has_dns_config:
        print("✅ /etc/systemd/resolved.conf 格式测试通过")
    else:
        print("❌ /etc/systemd/resolved.conf 格式测试失败")
    
    return has_resolve_section and has_dns_config

def check_current_dns_config():
    """检查当前系统的DNS配置"""
    print("\n=== 检查当前系统DNS配置 ===")
    
    # 检查 /etc/resolv.conf
    try:
        with open('/etc/resolv.conf', 'r') as f:
            resolv_content = f.read()
        print("当前 /etc/resolv.conf 内容:")
        print(resolv_content)
    except Exception as e:
        print(f"读取 /etc/resolv.conf 失败: {e}")
    
    # 检查 /etc/systemd/resolved.conf
    try:
        with open('/etc/systemd/resolved.conf', 'r') as f:
            resolved_content = f.read()
        print("\n当前 /etc/systemd/resolved.conf 内容:")
        print(resolved_content)
    except Exception as e:
        print(f"读取 /etc/systemd/resolved.conf 失败: {e}")
    
    # 检查 systemd-resolved 状态
    try:
        result = subprocess.run(['sudo', 'resolvectl', 'status'], 
                              capture_output=True, text=True, check=True)
        print("\n当前 systemd-resolved 状态:")
        print(result.stdout)
    except Exception as e:
        print(f"获取 systemd-resolved 状态失败: {e}")

def main():
    """主测试函数"""
    print("DNS配置更新功能测试")
    print("=" * 50)
    
    # 运行测试
    test1_passed = test_resolv_conf_format()
    test2_passed = test_systemd_resolved_conf_format()
    
    # 检查当前配置
    check_current_dns_config()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"resolv.conf 格式测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"systemd resolved.conf 格式测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("🎉 所有测试通过！DNS配置更新功能正常")
    else:
        print("⚠️  部分测试失败，请检查配置逻辑")

if __name__ == "__main__":
    main() 