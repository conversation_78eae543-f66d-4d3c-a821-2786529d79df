# xiaoli_application_ros2 快速开始指南

## 🚀 一键构建 (推荐)

```bash
# 1. 修复重复包问题
./fix_duplicate_packages.sh

# 2. 编译所有ROS2包
./build_xiaoli.sh

# 3. 设置环境并运行
source ./setup_env.sh
./run_network.sh
```

## 📋 常用命令

### 构建命令
```bash
# 编译所有包
./build_xiaoli.sh

# 编译特定包
./build_xiaoli.sh network
./build_xiaoli.sh network peripherals

# Release模式编译
./build_xiaoli.sh --release

# 清理后重新编译
./build_xiaoli.sh --clean --release
```

### 查看可用包
```bash
# 列出所有xiaoli包
./build_xiaoli.sh --list

# 列出所有包 (包括描述)
./build.sh --list-packages
```

### 运行节点
```bash
# 设置环境
source ./setup_env.sh

# 运行网络节点
./run_network.sh

# 运行外设节点
./run_peripherals.sh

# 手动运行其他节点
ros2 run <package_name> <node_name>
```

## 🔧 故障排除

### 重复包错误
```bash
# 如果看到 "Duplicate package names not supported" 错误
./fix_duplicate_packages.sh
```

### 构建失败
```bash
# 清理后重新构建
./build_xiaoli.sh --clean --verbose
```

### 环境问题
```bash
# 检查ROS2环境
echo $ROS_DISTRO

# 手动设置环境
source /opt/ros/humble/setup.bash
```

## 📁 主要包说明

| 包名 | 功能 | 启动脚本 |
|------|------|----------|
| network | 网络管理 (WiFi/蜂窝网络) | `./run_network.sh` |
| peripherals | 外设控制 (RGB灯/触摸) | `./run_peripherals.sh` |
| homi_speech | 语音处理 | 手动启动 |
| robdog_control | 机器狗控制 | 手动启动 |

## 🎯 开发工作流

```bash
# 1. 修改代码
vim xiaoli_application_ros2/src/network/...

# 2. 编译修改的包
./build_xiaoli.sh network

# 3. 测试
source ./setup_env.sh
./run_network.sh

# 4. 调试 (如需要)
./build_xiaoli.sh --clean network --verbose
```

## 📝 注意事项

1. **默认只编译ROS2包** - 自动排除ROS1和备份文件
2. **首次构建** - 可能需要较长时间下载依赖
3. **磁盘空间** - 定期清理构建缓存 `rm -rf build install`
4. **权限问题** - 确保脚本有执行权限 `chmod +x *.sh`

## 🆘 获取帮助

```bash
# 查看构建脚本帮助
./build_xiaoli.sh --help
./build.sh --help

# 查看清理脚本帮助
./clean_workspace.sh --help
```
