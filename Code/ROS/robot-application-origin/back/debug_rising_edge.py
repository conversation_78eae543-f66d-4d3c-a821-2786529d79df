#!/usr/bin/env python3
"""
调试上升沿发布问题的脚本
监听所有相关的日志信息
"""

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
from rcl_interfaces.srv import GetParameters
import json
import time
import threading

class RisingEdgeDebugNode(Node):
    def __init__(self):
        super().__init__('rising_edge_debug_node')
        
        # 订阅触摸状态
        self.subscription = self.create_subscription(
            String,
            'touch_status',
            self.touch_callback,
            10
        )
        
        # 创建参数客户端
        self.param_client = self.create_client(GetParameters, '/pwm_touch_node/get_parameters')
        
        self.rising_detected_count = 0
        self.rising_published_count = 0
        self.falling_published_count = 0
        
        self.get_logger().info("🔍 上升沿调试节点已启动")
        
    def touch_callback(self, msg):
        """处理触摸消息"""
        try:
            data = json.loads(msg.data)
            current_time = time.time()
            
            command = data.get('command', 'unknown')
            
            if command == 'touch_rising_edge':
                self.rising_published_count += 1
                pin = data.get('rising_edge_pin')
                self.get_logger().info(f"✅ 上升沿已发布 #{self.rising_published_count} - GPIO {pin}")
                
            elif command == 'touch_falling_edge':
                self.falling_published_count += 1
                pin = data.get('falling_edge_pin')
                self.get_logger().info(f"⬇️ 下降沿已发布 #{self.falling_published_count} - GPIO {pin}")
                
        except Exception as e:
            self.get_logger().error(f"❌ 处理消息失败: {e}")
    
    def check_parameters(self):
        """检查关键参数"""
        if not self.param_client.wait_for_service(timeout_sec=5.0):
            self.get_logger().error("❌ 无法连接到pwm_touch_node参数服务")
            return False
        
        # 获取关键参数
        from rcl_interfaces.srv import GetParameters
        request = GetParameters.Request()
        request.names = ['min_high_duration', 'touch_gpio_pins']
        
        future = self.param_client.call_async(request)
        rclpy.spin_until_future_complete(self, future, timeout_sec=5.0)
        
        if future.result() is not None:
            values = future.result().values
            if len(values) >= 2:
                min_duration = values[0].double_value if values[0].type == 3 else "未设置"
                gpio_pins = values[1].integer_array_value if values[1].type == 7 else "未设置"
                
                self.get_logger().info(f"📋 当前参数:")
                self.get_logger().info(f"   min_high_duration: {min_duration}")
                self.get_logger().info(f"   touch_gpio_pins: {gpio_pins}")
                
                if isinstance(min_duration, float) and min_duration > 0.1:
                    self.get_logger().warn(f"⚠️ min_high_duration ({min_duration}s) 可能过高")
                    self.get_logger().info("💡 建议设置为 0.02-0.05 秒")
                
                return True
        
        return False
    
    def print_status(self):
        """打印当前状态"""
        self.get_logger().info(f"\n📊 当前状态:")
        self.get_logger().info(f"   上升沿已发布: {self.rising_published_count}")
        self.get_logger().info(f"   下降沿已发布: {self.falling_published_count}")
        
        if self.rising_published_count == 0:
            self.get_logger().warn("⚠️ 没有检测到上升沿发布")
            self.get_logger().info("💡 可能的原因:")
            self.get_logger().info("   1. 高电平持续时间不足")
            self.get_logger().info("   2. GPIO读取问题")
            self.get_logger().info("   3. 监控循环问题")
            self.get_logger().info("   4. 过滤阈值过高")
        
        if self.falling_published_count == 0:
            self.get_logger().warn("⚠️ 没有检测到下降沿发布")
    
    def run_debug(self):
        """运行调试"""
        self.get_logger().info("🚀 开始上升沿调试...")
        
        # 检查参数
        if not self.check_parameters():
            self.get_logger().error("❌ 参数检查失败")
            return
        
        self.get_logger().info("\n👆 请触摸触摸板进行测试...")
        self.get_logger().info("   建议测试方式:")
        self.get_logger().info("   1. 长按 2-3 秒")
        self.get_logger().info("   2. 短按 0.5 秒")
        self.get_logger().info("   3. 快速点击")
        
        # 监听20秒
        start_time = time.time()
        test_duration = 20.0
        
        while rclpy.ok() and (time.time() - start_time) < test_duration:
            rclpy.spin_once(self, timeout_sec=0.1)
            
            # 每5秒显示状态
            elapsed = time.time() - start_time
            if int(elapsed) % 5 == 0 and elapsed > 0:
                remaining = test_duration - elapsed
                if remaining > 0:
                    self.get_logger().info(f"⏱️ 剩余: {remaining:.0f}s (上升沿:{self.rising_published_count}, 下降沿:{self.falling_published_count})")
        
        self.get_logger().info("🏁 调试完成！")
        self.print_status()

def main(args=None):
    rclpy.init(args=args)
    
    node = RisingEdgeDebugNode()
    
    try:
        node.run_debug()
    except KeyboardInterrupt:
        node.get_logger().info("🛑 调试被用户中断")
        node.print_status()
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
