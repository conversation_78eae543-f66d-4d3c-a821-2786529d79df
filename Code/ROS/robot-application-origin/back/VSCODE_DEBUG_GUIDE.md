# VSCode 一键编译+调试配置指南

## 🚀 新增功能

### 一键编译+调试配置
已为xiaoli_application_ros2项目添加了一键编译和调试功能，让开发更加高效！

## 📋 可用的调试配置

### 🚀 一键编译+调试
1. **🚀 xiaoli一键编译+调试 - network (Python)**
   - 自动编译network包及其依赖
   - 启动Python调试器调试network_node

2. **🚀 xiaoli一键编译+调试 - peripherals (C++)**
   - 自动编译peripherals包及其依赖
   - 启动GDB调试器调试peripherals_node

3. **🚀 xiaoli一键编译+调试 - 自定义包**
   - 可选择任意包进行编译和调试
   - 支持自定义节点名

### 🔧 编译任务
- **xiaoli编译所有包** - 编译所有xiaoli包
- **xiaoli Release编译** - Release模式清理编译
- **xiaoli清理构建** - 清理构建目录

## 🎯 使用方法

### 方法1: 使用调试面板
1. 打开VSCode调试面板 (`Ctrl+Shift+D`)
2. 选择对应的调试配置
3. 点击绿色播放按钮开始调试

### 方法2: 使用快捷键
1. 按 `F5` 启动调试
2. 选择对应的调试配置

### 方法3: 使用命令面板
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Debug: Start Debugging"
3. 选择对应的调试配置

## 🔧 构建任务使用

### 使用任务面板
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Tasks: Run Task"
3. 选择对应的构建任务

### 可用任务
- **xiaoli一键编译 - network** - 编译network包及依赖
- **xiaoli一键编译 - peripherals** - 编译peripherals包及依赖
- **xiaoli一键编译 - 自定义包** - 编译指定包及依赖
- **xiaoli编译所有包** - 编译所有包
- **xiaoli Release编译** - Release模式编译
- **xiaoli清理构建** - 清理构建目录

## 🎯 调试配置详解

### Python节点调试 (network为例)
```json
{
  "name": "🚀 xiaoli一键编译+调试 - network (Python)",
  "type": "debugpy",
  "preLaunchTask": "xiaoli一键编译 - network",
  "program": "${workspaceFolder}/install/x86_64/Debug/lib/network/network_node",
  "env": {
    "ROS_DOMAIN_ID": "0",
    "PYTHONPATH": "...",
    "LD_LIBRARY_PATH": "...",
    "AMENT_PREFIX_PATH": "..."
  }
}
```

### C++节点调试 (peripherals为例)
```json
{
  "name": "🚀 xiaoli一键编译+调试 - peripherals (C++)",
  "type": "cppdbg",
  "preLaunchTask": "xiaoli一键编译 - peripherals",
  "program": "${workspaceFolder}/install/x86_64/Debug/lib/peripherals/peripherals_node",
  "MIMode": "gdb"
}
```

## 🔍 调试功能

### Python调试功能
- ✅ 断点调试
- ✅ 变量查看
- ✅ 调用栈查看
- ✅ 表达式求值
- ✅ 条件断点
- ✅ 日志断点

### C++调试功能
- ✅ GDB断点调试
- ✅ 内存查看
- ✅ 寄存器查看
- ✅ 反汇编查看
- ✅ 核心转储分析

## 📁 文件结构

### 构建输出
```
workspace/
├── build/x86_64/Debug/           # 构建文件
├── install/x86_64/Debug/         # 安装文件
│   ├── lib/                      # 可执行文件和库
│   │   ├── network/
│   │   │   └── network_node      # Python节点
│   │   └── peripherals/
│   │       └── peripherals_node  # C++节点
│   └── setup.bash               # ROS2环境设置
├── setup_xiaoli_env.sh          # 环境设置脚本
└── run_xiaoli_*.sh              # 启动脚本
```

### VSCode配置文件
```
.vscode/
├── launch.json                   # 调试配置
├── tasks.json                    # 构建任务配置
├── settings.json                 # 工作空间设置
└── c_cpp_properties.json        # C++配置
```

## 🛠️ 自定义配置

### 添加新的包调试配置
1. 复制现有的调试配置
2. 修改包名和节点名
3. 更新路径和环境变量
4. 添加对应的构建任务

### 修改构建参数
在 `tasks.json` 中修改构建任务的 `args` 参数：
```json
"args": ["--deps", "--verbose", "your_package"]
```

## 🔧 故障排除

### 常见问题

1. **调试器无法启动**
   - 检查节点路径是否正确
   - 确保包已成功编译
   - 检查Python/GDB是否正确安装

2. **环境变量问题**
   - 检查ROS2环境是否正确设置
   - 确保PYTHONPATH包含正确的路径
   - 验证LD_LIBRARY_PATH设置

3. **构建失败**
   - 检查构建脚本是否有执行权限
   - 确保在正确的工作空间目录
   - 查看构建日志中的错误信息

### 调试技巧

1. **设置断点**
   - 在代码行号左侧点击设置断点
   - 使用条件断点进行精确调试

2. **查看变量**
   - 在调试面板的"变量"区域查看
   - 使用"监视"功能监控特定变量

3. **调试控制台**
   - 在调试控制台中执行表达式
   - 查看实时变量值

## 🎊 优势

### 开发效率提升
- ✅ **一键操作**: 编译+调试一步完成
- ✅ **智能依赖**: 自动编译依赖包
- ✅ **环境自动化**: 自动设置ROS2环境
- ✅ **错误定位**: 快速定位代码问题

### 调试体验优化
- ✅ **可视化调试**: 图形化调试界面
- ✅ **实时监控**: 实时查看变量状态
- ✅ **断点管理**: 灵活的断点控制
- ✅ **多语言支持**: Python和C++调试

现在您可以享受更高效的xiaoli_application_ros2开发和调试体验！
