#!/bin/bash

# 为所有CMakeLists.txt文件添加clangd支持
find . -name "CMakeLists.txt" -not -path "./xiaoli_application_ros2/build*" -not -path "./xiaoli_application_ros2/install*" -not -path "*/build/*" | while read file; do
    echo "Processing: $file"
    
    # 检查是否已经包含CMAKE_EXPORT_COMPILE_COMMANDS
    if grep -q "CMAKE_EXPORT_COMPILE_COMMANDS" "$file"; then
        echo "  Already has CMAKE_EXPORT_COMPILE_COMMANDS, skipping..."
        continue
    fi
    
    # 查找cmake_minimum_required和project行
    cmake_line=$(grep -n "cmake_minimum_required" "$file" | head -1 | cut -d: -f1)
    project_line=$(grep -n "^[[:space:]]*[Pp][Rr][Oo][Jj][Ee][Cc][Tt](" "$file" | head -1 | cut -d: -f1)
    
    if [ -n "$cmake_line" ] && [ -n "$project_line" ]; then
        # 在project行之后插入clangd配置
        insert_line=$((project_line + 1))
        
        # 创建临时文件
        temp_file=$(mktemp)
        
        # 插入配置
        {
            head -n "$project_line" "$file"
            echo ""
            echo "# Enable clangd support by generating compile_commands.json"
            echo "set(CMAKE_EXPORT_COMPILE_COMMANDS ON)"
            tail -n +$((project_line + 1)) "$file"
        } > "$temp_file"
        
        # 替换原文件
        mv "$temp_file" "$file"
        echo "  Added clangd support"
    else
        echo "  Could not find cmake_minimum_required or project line, skipping..."
    fi
done 