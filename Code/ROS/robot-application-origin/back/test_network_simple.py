#!/usr/bin/env python3
"""
简化的network节点测试
直接运行network_node.py进行调试测试
"""

import sys
import os

# 添加必要的路径
current_dir = os.path.dirname(os.path.abspath(__file__))
network_src_dir = os.path.join(current_dir, 'xiaoli_application_ros2', 'src', 'network')
sys.path.insert(0, network_src_dir)

def main():
    print("🚀 启动network节点调试测试...")
    print(f"📁 工作目录: {os.getcwd()}")
    print(f"🐍 Python路径: {sys.path[:3]}")  # 只显示前3个路径
    print("=" * 50)
    
    try:
        # 设置环境变量
        os.environ.setdefault('ROS_DOMAIN_ID', '0')
        
        # 导入ROS2
        import rclpy
        print("✅ rclpy导入成功")
        
        # 导入network模块
        from network.network_manager import NetworkManager
        from network.cellular_control import CellularModuleController
        print("✅ network模块导入成功")
        
        # 初始化ROS2
        rclpy.init()
        print("✅ ROS2初始化成功")
        
        # 创建一个简单的节点来测试
        node = rclpy.create_node('test_network_debug')
        print("✅ 测试节点创建成功")
        
        print("=" * 50)
        print("🎉 调试环境测试成功！")
        print("💡 现在可以在VSCode中调试network节点了")
        print("🔧 在network_node.py中设置断点，然后按F5开始调试")
        print("=" * 50)
        
        # 清理
        node.destroy_node()
        rclpy.shutdown()
        
        return 0
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请检查ROS2环境是否正确配置")
        return 1
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
