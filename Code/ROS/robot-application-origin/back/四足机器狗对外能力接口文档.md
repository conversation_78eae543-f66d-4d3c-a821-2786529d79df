 

 

 

 

 

 

 

 

**四足机器狗终端对外开放接口_v1.0.0**

** **

** **

版本号：1.0.0

 

 

 

 

 

 

 

 

 

 

 

 

 

**文档修订记录**

---

   版本号    修订日期                变更概述                    作者

   V1.0.0   2025-07-16            文档创建，初版                高亚军

   V1.0.0   2025-07-16

---

 

 

 

## 1.范围

本文档旨在描述对外API接口规范，包括四足机器狗运动控制、传感器数据上报、状态监控、语音交互、算法模块等能力对外功能接口，基于此接口文档可以进行二次开发。

## 2.术语、定义和缩略语

### 2.1.术语与定义

四足机器狗步态控制

协调腿部运动相位与轨迹，实现行走、快走、跑步等步态，确保稳定移动与姿态平衡。（核心要素：步态生成、相位协调、足端轨迹规划、稳定性控制）。

### 2.2.缩略语

下列缩略术语适用于本标准：

表2-1 缩略语

+:---------:+:----------------------------------------------------------:+
| > 缩略语  | > 解释                                                     |
+-----------+------------------------------------------------------------+
| > ROS     | > Robot Operating System 机器人操作系统                    |
+-----------+------------------------------------------------------------+
| > SLAM    | > ‌Simultaneous Localization and Mapping                    |
|           | >  即时定位与地图构建                                      |
+-----------+------------------------------------------------------------+
| > OTA     | > Over-the-AirTechnology 远程在线升级                      |
+-----------+------------------------------------------------------------+
| > SDK     | > Software Development Kit 软件开发包                      |
+-----------+------------------------------------------------------------+
| UWB       | Ultra Wide Band，UWB无线载波通信技术(超宽带)               |
+-----------+------------------------------------------------------------+
| > CM-M1-A | 四足机器狗领动版                                           |
+-----------+------------------------------------------------------------+
| CM-M2-S   | 四足机器狗智动版                                         |
+-----------+------------------------------------------------------------+

## 3.总体概述

### 3.1.总体架构

## 4.接口规范

### 4.1 运动控制接口

#### 4.1.1 速度控制接口

接口名称：

消息类型：

详细描述：

使用实例：

#### 4.1.2 设备移动控制接口

ros2 topic pub /catch_turtle/continue_move

homi_speech_interface/msg/ContinueMove

event = \"robot_move\"

x = 0.0

y = 0.0

z = 0.0

pitch = 0.0

yaw = 0.0

roll = 0.0

**event取值：**

"robot_move"：x、y、z方向上控制狗子运动，仅x、y有效

"robot_view"：pitch、yaw、roll方向上控制狗子运动，仅yaw有效

"stopAction"：运动停止，判断x、y、yaw方向为0后停止，调用前需要先调用rebot_move和robot_view清零数据，才能停止

"stopActionDelay"：强制停止

**x/y/z/pitch/yaw/roll取值：**

x、y、yaw有效，取值为1或-1，指示运动方向

#### 4.1.3 动作执行接口

//ros2 topic pub /catch_turtle/action_type
homi_speech_interface/msg/RobdogAction

actiontype:\"sportMode\"

actionargument:\"walk\"

**actiontype取值：**

"sportMode"，运动模式

"motorSkill"，运动技能

\"emergencyStop\"，急停

\"gaitControl\"，和强化学习有关的

\"resetZero\"，置零

\"rotateCtl\"：旋转

\"NavCtrl\"：自主模式

\"followMe\"：跟随功能

\"tripStart\":：自主出行

**actionargument取值：**

**对应"sportMode"，取值：**

CM-M1-H：\"walk\"，\"run\"，\"stairClimbe\"，\"climbe\"，\"medium_speed\"

CM-M2-S：\"walk\"，\"run\"，\"AIClassic\"，\"AINimble\"，\"jumpRun\"，\"runSide\"，\"medium_speed\"

**对应"motorSkill"，取值：**

CM-M1-H：\"standUp\"，\"getDown\"，\"twistBody\"，\"greeting\"，\"sitDown\"，\"twistAss\"，\"shakeBody\"，\"fastShakeBody\"，\"dance\"，\"stretch\"，\"chestOut\"，\"newYearCall\"，\"fingerHeart\"

CM-M2-S：\"standUp\"，\"getDown\"，\"twistBody\"，\"greeting\"，\"sitDown\"，\"dance\"，\"stretch\"，\"newYearCall\"，\"fingerHeart\"，\"happy\"，\"jumpForward\"，\"leap\"，\"danceV2\"，\"walkUpsideDown\"，\"standErect\"

**对应"gaitControl"，取值：**

CM-M1-H：\"obstacleCross\"，\"flatGround\"，\"exit\"

**对应\"followMe\"，取值：**

\"on\"，\"off\"，\"comeHere\"

**对应\"tripStart\"，取值：**

\"on\"，\"off\"，\"comeHere\"

### 4.2 设备状态上报接口

（1）状态请求接口：通过ROS2的topic机制，请求机器狗相关状态。

1. topic名称：/homi_speech/sigc_event_topic
2. topic数据格式：SIGCEvent.msg，该文件定义了数据格式为string
   event，传输的为json结构。
3. 接口定义：

（2）状态返回接口：通过ROS2 service机制传输数据。

（3）主动定时上报接口：通过ROS2
service机制传输数据。平台主动查询(hardware_state_query)及主动上报均采用此接口

\-\-\-\-\-\-\-\-\-\--硬件状态参数说明\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\--

关节温度：

FLSwingJoint \# 1号电机左前侧摆关节

FLHipJoint \# 2号电机左前髋关节

FLKneeJoint \# 3号电机左前膝关节

FRSwingJoint \# 4号右前侧摆电机

FRHipJoint \# 5号右前髋电机

FRKneeJoint \# 6号右前膝电机

BLSwingJoint \# 7号左后侧摆电机

BLHipJoint \# 8号左后髋电机

BLKneeJoint \# 9号左后膝电机

BRSwingJoint \# 10号右后侧摆电机

BRHipJoint \# 11号右后髋电机

BRKneeJoint 12号右后膝电机

\"stateCode\": 0, \# 0: normal, 1: overheating, \...

\"temperature\": 45 \# int value

深度相机：

realSense

\"stateCode\"： 0, \# 0: normal, 1: driver issue, 2: no data, \...

激光雷达：

lidar

\"stateCode\"： 0, \# 0： normal, 1: driver issue, 2: no data, \...

# 4.3 传感器数据上报：

### 3.1 激光雷达：

ros2 topic echo /lidar_point sensor_msgs/msg/PointCloud2

### 3.2 imu：

### 3.3 相机:

额头相机：

ros2 service call /video_gst/get_video_service
homi_speech_interface/srv/GetVideoStream "{resolution: \'hd\'}"

(1) 参数可选：jpeg，fhd，hd，sd

(2) 返回结果：success为false为失败，后续结果不需要，true表示请求对应分辨率视频（图片）流成功，socket_path为共享内存文件描述符地址，使用gstreamer的shmsrc进行连接取流

领结相机：

(1) CM-M1-H：ros2 service call /video_gst_nv/get_video_service
    homi_speech_interface/srv/GetVideoStream "{resolution: \'hd\'}"

(2) CM-M2-S：ros2 service call /video_gst_neck/get_video_service
    homi_speech_interface/srv/GetVideoStream "{resolution: \'hd\'}"

(3) 参数可选：fhd，hd（CM-M1-H可额外选fhd_udp，hd_udp）

(4) 返回结果：success为false为失败，后续结果不需要，true表示请求对应分辨率视频（图片）流成功，socket_path为共享内存文件描述符地址，使用gstreamer的shmsrc进行连接取流（CM-M1-H请求fhd_udp，hd_udp收到的socket_path为端口号，在交互板对应使用udpsrc接收对应端口数据）

(5) jpeg流无需请求直接建联即可，文件描述符地址/tmp/neck_jpeg(CM-M1-H文件描述符地址/tmp/foo_jpeg)

### 3.4 UWB：

ros2 topic echo /uwb_triple/raw_data geometry_msgs::msg::Twist

twist.linear.z = distance; //距离

twist.angular.z = angle; //角度

twist.angular.x = 0/1/2; //0-左耳1-右耳 2-前鼻

# 4.4 语音交互能力：

### 4.1 TTS语音播报

ros2 service call /homi_speech/helper_assistant_speech_text_service

homi_speech_interface/srv/AssistantSpeechText
\"{\\\"msg\\\":\\\"哎呀，当前我的关节温度过高！当前温度为69度，请将我移动至合适位置或者让我休息一下吧\\\"}\"

### 4.2 开始语音对话（开始拾音）

ros2 topic pub -1 /audio_recorder/wakeup_event

homi_speech_interface/msg/Wakeup \"ivw_word: \'灵犀灵犀\'

angle: 0

\"

### 4.3 强制结束语音对话

ros2 service call /homi_speech/helper_assistant_abort_service

homi_speech_interface/srv/AssistantAbort \"{}\"

## 4.5 外设控制能力

### 4.5.1 外设控制服务接口

**接口名称：** 外设控制服务

**服务名称：** robdog_control/peripherals_ctrl

**消息类型：** homi_speech_interface/srv/PeripheralsCtrl

**详细描述：** 通过JSON格式数据控制机器狗的各种外设，包括RGB灯、照明灯等

**请求格式：**

```
string data  # JSON格式的控制命令
```

**响应格式：**

```
int32 error_code  # 错误码，true表示成功，false表示失败
string result     # 执行结果描述
```

### 4.5.2 RGB灯控制接口

**功能描述：** 控制机器狗身上的RGB LED灯效果，支持多种颜色模式和动态效果

**使用实例：**

#### 基础颜色设置

```bash
ros2 service call robdog_control/peripherals_ctrl homi_speech_interface/srv/PeripheralsCtrl \
'{
  "data": "{
    \"command\": \"set_led_mode\",
    \"mode\": \"solid\",
    \"color\": \"red\",
    \"brightness\": 0.8
  }"
}'
```

#### 渐变效果设置

```bash
ros2 service call robdog_control/peripherals_ctrl homi_speech_interface/srv/PeripheralsCtrl \
'{
  "data": "{
    \"command\": \"set_led_mode\",
    \"mode\": \"gradient\",
    \"color\": \"blue\",
    \"secondary_color\": \"green\",
    \"effect_speed\": 1.5,
    \"brightness\": 0.9
  }"
}'
```

#### 呼吸灯效果

```bash
ros2 service call robdog_control/peripherals_ctrl homi_speech_interface/srv/PeripheralsCtrl \
'{
  "data": "{
    \"command\": \"set_led_mode\",
    \"mode\": \"breathing\",
    \"color\": \"purple\",
    \"effect_speed\": 2.0,
    \"brightness\": 0.7
  }"
}'
```

#### 闪烁效果

```bash
ros2 service call robdog_control/peripherals_ctrl homi_speech_interface/srv/PeripheralsCtrl \
'{
  "data": "{
    \"command\": \"set_led_mode\",
    \"mode\": \"blink\",
    \"color\": \"yellow\",
    \"effect_speed\": 3.0,
    \"brightness\": 1.0
  }"
}'
```

**支持的LED模式：**

- `solid`: 纯色常亮
- `gradient`: 渐变效果
- `breathing`: 呼吸灯效果
- `blink`: 闪烁效果
- `rainbow`: 彩虹循环
- `chase`: 追逐效果
- `off`: 关闭LED

**支持的颜色：**

- 基础颜色：`red`, `green`, `blue`, `yellow`, `purple`, `cyan`, `white`, `orange`, `pink`
- 十六进制颜色：`#FF0000`, `#00FF00`, `#0000FF` 等
- RGB值：`rgb(255,0,0)`, `rgb(0,255,0)` 等

**参数说明：**

- `mode`: LED模式（必需）
- `color`: 主颜色（必需）
- `secondary_color`: 次要颜色（可选，用于渐变等效果）
- `brightness`: 亮度值，范围0.0-1.0（可选，默认0.8）
- `effect_speed`: 效果速度，范围0.1-5.0（可选，默认1.0）

### 4.5.3 照明灯控制接口

**功能描述：** 控制机器狗的照明灯开关和亮度

**使用实例：**

#### 开启照明灯

```bash
ros2 service call robdog_control/peripherals_ctrl homi_speech_interface/srv/PeripheralsCtrl \
'{
  "data": "{
    \"command\": \"set_led_mode\",
    \"mode\": \"illumination\",
    \"brightness\": 1.0
  }"
}'
```

#### 调节照明灯亮度

```bash
ros2 service call robdog_control/peripherals_ctrl homi_speech_interface/srv/PeripheralsCtrl \
'{
  "data": "{
    \"command\": \"set_led_mode\",
    \"mode\": \"illumination\",
    \"brightness\": 0.5
  }"
}'
```

#### 关闭照明灯

```bash
ros2 service call robdog_control/peripherals_ctrl homi_speech_interface/srv/PeripheralsCtrl \
'{
  "data": "{
    \"command\": \"set_led_mode\",
    \"mode\": \"off\"
  }"
}'
```

### 4.5.5 触摸传感器监控接口

**功能描述：** 监控机器狗身上的触摸传感器状态，获取触摸事件

**监控话题：** /robdog_control/peripherals_monitor

**消息类型：** std_msgs/msg/String

**数据格式：** JSON格式

**使用实例：**

#### 订阅触摸事件

```bash
ros2 topic echo /robdog_control/peripherals_monitor
```

**触摸事件数据格式：**

```json
{
  "command": "touch_event",
  "data": {
    "gpio_pin_1": true,
    "gpio_pin_2": false,
    "gpio_pin_3": false
  },
  "timestamp": 1642678800,
  "trigger_pin": "gpio_pin_1",
  "event_type": "rising_edge"
}
```

**字段说明：**

- `command`: 事件类型，固定为"touch_event"
- `data`: GPIO引脚状态，true表示被触摸（高电平），false表示未触摸（低电平）
- `timestamp`: 事件时间戳
- `trigger_pin`: 触发事件的GPIO引脚名称
- `event_type`: 事件类型，"rising_edge"表示触摸开始，"falling_edge"表示触摸结束

**触摸传感器位置：**

- `gpio_pin_1`: 头部触摸传感器
- `gpio_pin_2`: 背部触摸传感器
- `gpio_pin_3`: 侧面触摸传感器

#### 触摸状态话题

**话题名称：** touch_status

**消息类型：** std_msgs/msg/String

**使用实例：**

```bash
ros2 topic echo touch_status
```

**数据格式：**

```json
{
  "gpio_state": {
    "pin_1": 1,
    "pin_2": 0,
    "pin_3": 0
  },
  "timestamp": 1642678800
}
```

### 4.5.6 外设状态查询接口

**接口名称：** 外设状态查询服务

**服务名称：** robdog_control/peripherals_status

**消息类型：** homi_speech_interface/srv/PeripheralsStatus

**详细描述：** 查询机器狗外设的当前状态

**请求格式：**

```
string data  # JSON格式的查询命令
```

**响应格式：**

```
int32 error_code  # 错误码，true表示成功，false表示失败
string result     # 查询结果，JSON格式
```

**使用实例：**

#### 查询LED状态

```bash
ros2 service call robdog_control/peripherals_status homi_speech_interface/srv/PeripheralsStatus \
'{
  "data": "{
    \"command\": \"get_led_status\"
  }"
}'
```

#### 查询触摸传感器状态

```bash
ros2 service call robdog_control/peripherals_status homi_speech_interface/srv/PeripheralsStatus \
'{
  "data": "{
    \"command\": \"get_touch_status\"
  }"
}'
```

#### 查询PWM参数状态

```bash
ros2 service call robdog_control/peripherals_status homi_speech_interface/srv/PeripheralsStatus \
'{
  "data": "{
    \"command\": \"get_pwm_status\"
  }"
}'
```

**状态响应格式：**

```json
{
  "status": "success",
  "data": {
    "led_mode": "solid",
    "led_color": "red",
    "led_brightness": 0.8,
    "touch_sensors": {
      "gpio_pin_1": false,
      "gpio_pin_2": false,
      "gpio_pin_3": true
    },
    "pwm_values": {
      "red": 7550,
      "green": 8280,
      "blue": 64
    }
  }
}
```

### 4.5.7 外设控制命令总览

**支持的控制命令：**

| 命令                 | 功能         | 参数                                  |
| -------------------- | ------------ | ------------------------------------- |
| `set_led_mode`     | 设置LED模式  | mode, color, brightness, effect_speed |
| `update_pwm`       | 更新PWM参数  | data{red, green, blue}                |
| `get_led_status`   | 查询LED状态  | 无                                    |
| `get_touch_status` | 查询触摸状态 | 无                                    |
| `get_pwm_status`   | 查询PWM状态  | 无                                    |

**错误码说明：**

- `true`: 操作成功
- `false`: 操作失败，详细错误信息在result字段中

**注意事项：**

1. 所有JSON数据需要进行适当的转义
2. PWM值设置过高可能导致LED过热，建议不超过8000
3. 触摸传感器状态变化会自动发布到监控话题
4. LED模式切换时会自动停止之前的效果
5. 建议在设置新的LED效果前先查询当前状态
