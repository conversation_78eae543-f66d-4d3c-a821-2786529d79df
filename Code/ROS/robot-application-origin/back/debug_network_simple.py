#!/usr/bin/env python3
"""
Network节点简化调试版本
移除homi_speech_interface依赖，专注于核心网络功能调试
"""

import sys
import os
import json
import socket
import time
import subprocess
import rclpy
from rclpy.node import Node
from std_msgs.msg import String

# 添加包路径到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
network_pkg_dir = os.path.join(current_dir, 'xiaoli_application_ros2', 'src', 'network')
network_src_dir = os.path.join(network_pkg_dir, 'network')

sys.path.insert(0, network_pkg_dir)
sys.path.insert(0, network_src_dir)

# 设置环境变量
os.environ.setdefault('ROS_DOMAIN_ID', '0')
os.environ.setdefault('RCUTILS_LOGGING_BUFFERED_STREAM', '1')

class NetworkNodeSimple(Node):
    """简化的Network节点，用于调试核心功能"""
    
    def __init__(self):
        super().__init__('network_node_debug')
        self.get_logger().set_level(rclpy.logging.LoggingSeverity.DEBUG)
        self.get_logger().info("🚀 初始化简化版Network节点")

        # 基本参数
        self.declare_parameter('device_type', "unitree")
        self.device_type = self.get_parameter('device_type').value
        
        self.declare_parameter('wifi_connect_interface', "wlan0")
        self.wifi_interface = self.get_parameter('wifi_connect_interface').value
        
        self.declare_parameter('mobile_connect_interface', "eth1")
        self.mobile_interface = self.get_parameter('mobile_connect_interface').value
        
        self.declare_parameter('timer_interval', 20)
        self.timer_interval = self.get_parameter('timer_interval').value

        # 导入网络管理器（这里可以设置断点）
        try:
            from network_manager import NetworkManager
            from cellular_control import CellularModuleController
            
            self.get_logger().info("✅ 网络模块导入成功")
            
            # 初始化网络管理器（这里可以设置断点）
            self.network_manager = NetworkManager(
                node=self,
                wifi_interface=self.wifi_interface,
                mobile_interface=self.mobile_interface,
                ap_interface="wlan1",
                logger=self.get_logger()
            )
            
            # 初始化蜂窝控制器（这里可以设置断点）
            self.cellular_controller = CellularModuleController(
                node=self, 
                logger=self.get_logger()
            )
            
            self.get_logger().info("✅ 网络管理器初始化成功")
            
        except Exception as e:
            self.get_logger().error(f"❌ 网络模块初始化失败: {e}")
            raise

        # 创建发布者（用于测试）
        self.status_publisher = self.create_publisher(
            String, 
            '/network/status', 
            10
        )

        # 创建定时器进行网络状态检查（这里可以设置断点）
        self.timer = self.create_timer(
            self.timer_interval, 
            self.check_network_status_callback
        )
        
        self.get_logger().info(f"✅ 定时器已启动，每 {self.timer_interval} 秒检查网络状态")
        self.get_logger().info("🎉 Network节点初始化完成！")

    def check_network_status_callback(self):
        """网络状态检查回调（可以在这里设置断点）"""
        try:
            self.get_logger().debug("🔍 开始检查网络状态...")
            
            # 检查网络接口状态
            status_info = {
                "timestamp": time.time(),
                "wifi_interface": self.wifi_interface,
                "mobile_interface": self.mobile_interface,
                "device_type": self.device_type
            }
            
            # 发布状态信息
            status_msg = String()
            status_msg.data = json.dumps(status_info)
            self.status_publisher.publish(status_msg)
            
            self.get_logger().debug("✅ 网络状态检查完成")
            
        except Exception as e:
            self.get_logger().error(f"❌ 网络状态检查失败: {e}")

    def get_interface_ip(self, interface):
        """获取网络接口IP地址（调试用）"""
        try:
            result = subprocess.run(
                ['ip', 'addr', 'show', interface],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                self.get_logger().debug(f"接口 {interface} 信息: {result.stdout[:100]}...")
                return result.stdout
            else:
                self.get_logger().warning(f"无法获取接口 {interface} 信息")
                return None
        except Exception as e:
            self.get_logger().error(f"获取接口信息失败: {e}")
            return None


def main(args=None):
    """主函数"""
    print("=" * 60)
    print("🚀 启动Network节点简化调试版本")
    print("=" * 60)
    
    try:
        # 初始化ROS2
        rclpy.init(args=args)
        
        # 创建节点
        node = NetworkNodeSimple()
        
        print("✅ 节点创建成功！")
        print("💡 现在可以在源码中设置断点进行调试")
        print("🎯 推荐断点位置:")
        print("   - NetworkNodeSimple.__init__() - 节点初始化")
        print("   - check_network_status_callback() - 网络状态检查")
        print("   - NetworkManager.__init__() - 网络管理器初始化")
        print("🛑 按Ctrl+C停止节点")
        print("=" * 60)
        
        # 运行节点
        try:
            rclpy.spin(node)
        except KeyboardInterrupt:
            node.get_logger().info("🛑 检测到 KeyboardInterrupt, 正在关闭节点...")
        finally:
            node.destroy_node()
            rclpy.shutdown()
            print("👋 节点已关闭")
            
    except Exception as e:
        print(f"❌ 节点运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
