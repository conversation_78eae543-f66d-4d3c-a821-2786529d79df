# sendRequestData 接口详细说明

## 1. 接口概述

### 1.1 接口定义
```cpp
void RobdogCenter::sendRequestData(const std::string &data);
```

### 1.2 功能描述
`sendRequestData` 是 robdog_control 模块与机器人平台交互的核心接口，负责向平台发送各种状态数据、事件响应和主动上报信息。这是一个关键的通信桥梁，确保机器人的状态和事件能够及时传递给上层平台。

### 1.3 接口特点
- **异步通信**: 使用ROS2异步服务调用，不阻塞主线程
- **统一入口**: 所有向平台的数据上报都通过此接口
- **JSON格式**: 数据以JSON字符串格式传输
- **错误处理**: 完善的超时和错误处理机制

## 2. 技术实现

### 2.1 服务客户端初始化
```cpp
// 在RobdogCenter构造函数中初始化
platform_client = node_->create_client<homi_speech_interface::srv::SIGCData>(
    "/homi_speech/sigc_data_service"); // 上发给平台的消息
```

### 2.2 接口实现
```cpp
void RobdogCenter::sendRequestData(const std::string &data) {
    // 1. 创建服务请求
    auto request_sigc_data = std::make_shared<homi_speech_interface::srv::SIGCData::Request>();
    request_sigc_data->data = data;  // 设置JSON数据
    
    // 2. 等待服务可用 (超时1秒)
    auto ret = platform_client->wait_for_service(std::chrono::seconds(1));
    if (!ret) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), 
                   "Failed to waitForExistence service assistant");
        return;
    }
    
    // 3. 异步发送请求
    auto result = platform_client->async_send_request(request_sigc_data, 
        std::bind(&RobdogCenter::plat_srv_callback, this, std::placeholders::_1));
}
```

### 2.3 响应回调处理
```cpp
void RobdogCenter::plat_srv_callback(
    rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedFuture response) {
    
    try {
        auto response_value = response.get();
        int errorCode = response_value->error_code;
        
        if (errorCode != 0) {
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"),
                       "Platform service returned error code: %d", errorCode);
        } else {
            // 发送成功，可以进行后续处理
            RCLCPP_DEBUG(rclcpp::get_logger("robdog_control"),
                        "Data sent to platform successfully");
        }
    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"),
                    "Exception in platform callback: %s", e.what());
    }
}
```

## 3. 服务接口规范

### 3.1 服务配置
- **服务名称**: `/homi_speech/sigc_data_service`
- **服务类型**: `homi_speech_interface::srv::SIGCData`
- **提供者**: `homi_speech` 模块
- **消费者**: `robdog_control` 模块

### 3.2 消息格式
```cpp
// 请求消息
struct SIGCDataRequest {
    string data;  // JSON格式的数据字符串
};

// 响应消息
struct SIGCDataResponse {
    int32 error_code;  // 错误码: 0-成功, 非0-失败
};
```

### 3.3 错误码定义
- `0`: 成功发送到平台
- `-1`: 发送失败 (网络异常、平台不可达、数据格式错误等)

## 4. 数据流向

### 4.1 完整数据流
```
robdog_control -> sendRequestData() -> /homi_speech/sigc_data_service -> homi_speech -> 机器人平台
```

### 4.2 homi_speech模块处理
```cpp
void SGICDataCallback(const std::shared_ptr<homi_speech_interface::srv::SIGCData::Request> req,
                     std::shared_ptr<homi_speech_interface::srv::SIGCData::Response> res) {
    RCLCPP_INFO(rclcpp::get_logger("speech_core"), "Received data: %s", req->data.c_str());
    
    // 调用内部接口发送到平台 (超时2秒)
    auto ret = homi::inner::sendEventOOB(std::make_shared<std::string>(req->data), false, 2000);
    
    if (ret < 0) {
        RCLCPP_WARN(rclcpp::get_logger("speech_core"), "Failed to send to platform: %d", ret);
        res->error_code = -1;
    } else {
        res->error_code = 0;
    }
}
```

## 5. 调用场景

### 5.1 定时上报
```cpp
// 点位上报 (每2秒)
void RobdogCenter::timerRobotPoseCallback() {
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_INTERACTION";
    response["event"] = "point_report";
    response["eventId"] = "robdog_plat_" + std::to_string(getCurrentTimeStamp());
    response["body"]["x"] = currentPosition.x;
    response["body"]["y"] = currentPosition.y;
    response["body"]["angle"] = currentPosition.angle;
    
    Json::FastWriter writer;
    sendRequestData(writer.write(response));
}
```

### 5.2 事件响应
```cpp
// 建图完成响应
void RobdogCenter::sendMapCompleteResponse(int code, long mapId) {
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_INTERACTION";
    response["event"] = "map_complete";
    response["eventId"] = "map_complete_" + std::to_string(getCurrentTimeStamp());
    response["body"]["code"] = code;
    response["body"]["mapId"] = Json::Int64(mapId);
    
    Json::FastWriter writer;
    sendRequestData(writer.write(response));
}
```

### 5.3 异常上报
```cpp
// 设备告警上报
void RobdogCenter::deviceAlarmReport(int alarmCode) {
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_INTERACTION";
    response["event"] = "device_alarm_report";
    response["eventId"] = "alarm_" + std::to_string(getCurrentTimeStamp());
    response["body"]["alarmCode"] = alarmCode;
    response["body"]["timestamp"] = getCurrentTimeString();
    
    Json::FastWriter writer;
    sendRequestData(writer.write(response));
}
```

### 5.4 状态变更通知
```cpp
// 智能播报状态上报
void RobdogCenter::RobotBroadcastStatusToPlat(int status) {
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "ROBOT_BUSINESS_DEVICE";
    response["event"] = "broadcast_report";
    response["eventId"] = "broadcast_" + std::to_string(getCurrentTimeStamp());
    response["body"]["status"] = status;  // 0-被打断 1-正常运行
    response["body"]["remindId"] = Json::Int64(RobotState::getInstance().getRemindId());
    
    Json::FastWriter writer;
    sendRequestData(writer.write(response));
}
```

## 6. 超时和错误处理

### 6.1 超时机制
- **服务等待超时**: 1秒 (在robdog_control中)
- **平台发送超时**: 2秒 (在homi_speech中)
- **总体超时**: 最大3秒

### 6.2 错误处理策略
```cpp
void RobdogCenter::sendRequestData(const std::string &data) {
    // 1. 参数验证
    if (data.empty()) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Empty data, skip sending");
        return;
    }
    
    // 2. 服务可用性检查
    if (!platform_client) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Platform client not initialized");
        return;
    }
    
    // 3. 服务等待超时处理
    auto ret = platform_client->wait_for_service(std::chrono::seconds(1));
    if (!ret) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), 
                   "Platform service not available, data may be lost");
        // 可以考虑将数据缓存，稍后重试
        return;
    }
    
    // 4. 发送请求
    try {
        auto request = std::make_shared<homi_speech_interface::srv::SIGCData::Request>();
        request->data = data;
        auto result = platform_client->async_send_request(request, 
            std::bind(&RobdogCenter::plat_srv_callback, this, std::placeholders::_1));
    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), 
                    "Exception when sending to platform: %s", e.what());
    }
}
```

## 7. 性能考虑

### 7.1 调用频率
- **高频调用**: 点位上报 (2秒间隔)
- **中频调用**: 状态上报 (按需)
- **低频调用**: 事件响应 (事件触发)

### 7.2 数据大小
- **典型大小**: 200-500字节 (JSON格式)
- **最大建议**: 不超过4KB
- **压缩考虑**: 对于大数据可考虑压缩

### 7.3 并发处理
- **异步调用**: 不阻塞主线程
- **队列机制**: ROS2内部处理请求队列
- **线程安全**: ROS2保证服务调用的线程安全

## 8. 调试和监控

### 8.1 日志记录
```cpp
// 发送前记录
RCLCPP_DEBUG(rclcpp::get_logger("robdog_control"), 
            "Sending to platform: %s", data.c_str());

// 响应后记录
RCLCPP_INFO(rclcpp::get_logger("robdog_control"), 
           "Platform response: error_code=%d", errorCode);
```

### 8.2 监控指标
- 发送成功率
- 平均响应时间
- 错误码分布
- 服务可用性

### 8.3 故障排查
1. 检查 `/homi_speech/sigc_data_service` 服务是否运行
2. 检查网络连接到机器人平台
3. 验证JSON数据格式正确性
4. 查看homi_speech模块日志

---

**文档版本**: v1.0  
**创建日期**: 2025-07-14  
**维护者**: robdog_control 开发团队
