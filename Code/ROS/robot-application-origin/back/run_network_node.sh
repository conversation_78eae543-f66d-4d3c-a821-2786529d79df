#!/bin/bash

# xiaoli network节点启动脚本
# 包含完整的环境设置和配置文件

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="$SCRIPT_DIR"
BUILD_TYPE="Debug"
ARCH=$(uname -m)

# 路径配置
INSTALL_DIR="$WORKSPACE_DIR/install/$ARCH/$BUILD_TYPE"
CONFIG_FILE="$WORKSPACE_DIR/xiaoli_application_ros2/src/launch_package/configs/robot_config.yaml"
NETWORK_SRC_DIR="$WORKSPACE_DIR/xiaoli_application_ros2/src/network"

# 显示帮助信息
function show_help() {
    echo -e "${BLUE}xiaoli network节点启动脚本${NC}"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -c, --config FILE   指定配置文件路径"
    echo "  -d, --debug         启用调试模式"
    echo "  -v, --verbose       显示详细信息"
    echo "  --domain-id ID      设置ROS_DOMAIN_ID (默认: 0)"
    echo "  --log-level LEVEL   设置日志级别 (DEBUG|INFO|WARN|ERROR)"
    echo "  --check-only        仅检查环境，不启动节点"
    echo ""
    echo "示例:"
    echo "  $0                           # 使用默认配置启动"
    echo "  $0 --debug                   # 调试模式启动"
    echo "  $0 --config custom.yaml     # 使用自定义配置文件"
    echo "  $0 --domain-id 1 --verbose  # 指定域ID和详细模式"
}

# 默认参数
DEBUG_MODE=false
VERBOSE=false
ROS_DOMAIN_ID=0
LOG_LEVEL="INFO"
CHECK_ONLY=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -d|--debug)
            DEBUG_MODE=true
            LOG_LEVEL="DEBUG"
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --domain-id)
            ROS_DOMAIN_ID="$2"
            shift 2
            ;;
        --log-level)
            LOG_LEVEL="$2"
            shift 2
            ;;
        --check-only)
            CHECK_ONLY=true
            shift
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 检查环境函数
function check_environment() {
    echo -e "${BLUE}=== 检查运行环境 ===${NC}"
    
    # 检查ROS2环境
    if [ -z "$ROS_DISTRO" ]; then
        echo -e "${YELLOW}ROS2环境未设置，正在自动设置...${NC}"
        if [ -f "/opt/ros/humble/setup.bash" ]; then
            source /opt/ros/humble/setup.bash
            echo -e "${GREEN}✓ ROS2 Humble环境已设置${NC}"
        elif [ -f "/opt/ros/galactic/setup.bash" ]; then
            source /opt/ros/galactic/setup.bash
            echo -e "${GREEN}✓ ROS2 Galactic环境已设置${NC}"
        else
            echo -e "${RED}✗ 未找到ROS2环境${NC}"
            return 1
        fi
    else
        echo -e "${GREEN}✓ ROS2环境: $ROS_DISTRO${NC}"
    fi
    
    # 检查安装目录
    if [ ! -d "$INSTALL_DIR" ]; then
        echo -e "${RED}✗ 安装目录不存在: $INSTALL_DIR${NC}"
        echo -e "${YELLOW}请先编译network包: ./xiaoli_application_ros2/build_xiaoli.sh network${NC}"
        return 1
    else
        echo -e "${GREEN}✓ 安装目录: $INSTALL_DIR${NC}"
    fi
    
    # 检查network包
    if [ ! -f "$INSTALL_DIR/network/lib/network/network_node" ]; then
        echo -e "${RED}✗ network_node可执行文件不存在${NC}"
        echo -e "${YELLOW}请先编译network包: ./xiaoli_application_ros2/build_xiaoli.sh network${NC}"
        return 1
    else
        echo -e "${GREEN}✓ network_node可执行文件存在${NC}"
    fi
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        echo -e "${YELLOW}⚠ 配置文件不存在: $CONFIG_FILE${NC}"
        echo -e "${YELLOW}将使用默认配置启动${NC}"
        CONFIG_FILE=""
    else
        echo -e "${GREEN}✓ 配置文件: $CONFIG_FILE${NC}"
    fi
    
    # 检查Python包
    if [ ! -d "$INSTALL_DIR/network/lib/python3.10/site-packages/network" ]; then
        echo -e "${RED}✗ network Python包不存在${NC}"
        return 1
    else
        echo -e "${GREEN}✓ network Python包存在${NC}"
    fi
    
    # 检查依赖包
    if [ ! -d "$INSTALL_DIR/homi_speech_interface" ]; then
        echo -e "${YELLOW}⚠ homi_speech_interface包不存在，正在编译...${NC}"
        if [ -x "$WORKSPACE_DIR/xiaoli_application_ros2/build_xiaoli.sh" ]; then
            "$WORKSPACE_DIR/xiaoli_application_ros2/build_xiaoli.sh" homi_speech_interface
        else
            echo -e "${RED}✗ 无法编译依赖包${NC}"
            return 1
        fi
    else
        echo -e "${GREEN}✓ homi_speech_interface依赖包存在${NC}"
    fi
    
    return 0
}

# 设置环境变量
function setup_environment() {
    echo -e "${BLUE}=== 设置环境变量 ===${NC}"
    
    # 设置ROS2环境
    if [ -f "$INSTALL_DIR/setup.bash" ]; then
        source "$INSTALL_DIR/setup.bash"
        echo -e "${GREEN}✓ 已加载工作空间环境${NC}"
    fi
    
    # 设置Python路径
    export PYTHONPATH="$INSTALL_DIR/network/lib/python3.10/site-packages:$INSTALL_DIR/homi_speech_interface/local/lib/python3.10/dist-packages:/opt/ros/$ROS_DISTRO/lib/python3.10/site-packages:/opt/ros/$ROS_DISTRO/local/lib/python3.10/dist-packages:$PYTHONPATH"
    
    # 设置库路径
    export LD_LIBRARY_PATH="$INSTALL_DIR/lib:/opt/ros/$ROS_DISTRO/lib:$LD_LIBRARY_PATH"
    
    # 设置ROS2参数
    export ROS_DOMAIN_ID="$ROS_DOMAIN_ID"
    export RCUTILS_LOGGING_BUFFERED_STREAM=1
    export RCUTILS_LOGGING_USE_STDOUT=0
    
    # 设置日志级别
    case $LOG_LEVEL in
        DEBUG)
            export RCUTILS_CONSOLE_OUTPUT_FORMAT="[{severity}] [{time}] [{name}]: {message}"
            ;;
        *)
            export RCUTILS_CONSOLE_OUTPUT_FORMAT="[{severity}] [{name}]: {message}"
            ;;
    esac
    
    if [ "$VERBOSE" = true ]; then
        echo -e "${CYAN}环境变量设置:${NC}"
        echo -e "  ROS_DISTRO: ${GREEN}$ROS_DISTRO${NC}"
        echo -e "  ROS_DOMAIN_ID: ${GREEN}$ROS_DOMAIN_ID${NC}"
        echo -e "  PYTHONPATH: ${GREEN}$(echo $PYTHONPATH | cut -c1-80)...${NC}"
        echo -e "  LD_LIBRARY_PATH: ${GREEN}$(echo $LD_LIBRARY_PATH | cut -c1-80)...${NC}"
    fi
}

# 显示启动信息
function show_startup_info() {
    echo -e "${BLUE}=== network节点启动信息 ===${NC}"
    echo -e "工作空间: ${GREEN}$WORKSPACE_DIR${NC}"
    echo -e "构建类型: ${GREEN}$BUILD_TYPE${NC}"
    echo -e "架构: ${GREEN}$ARCH${NC}"
    echo -e "ROS域ID: ${GREEN}$ROS_DOMAIN_ID${NC}"
    echo -e "日志级别: ${GREEN}$LOG_LEVEL${NC}"
    if [ -n "$CONFIG_FILE" ]; then
        echo -e "配置文件: ${GREEN}$CONFIG_FILE${NC}"
    else
        echo -e "配置文件: ${YELLOW}使用默认配置${NC}"
    fi
    echo -e "调试模式: ${GREEN}$([ "$DEBUG_MODE" = true ] && echo "启用" || echo "禁用")${NC}"
    echo ""
}

# 启动节点
function start_network_node() {
    echo -e "${BLUE}=== 启动network节点 ===${NC}"
    
    # 构建启动命令
    local cmd="ros2 run network network_node"
    
    # 添加ROS参数
    if [ -n "$CONFIG_FILE" ]; then
        cmd="$cmd --ros-args --params-file $CONFIG_FILE"
    fi
    
    # 添加日志级别参数
    cmd="$cmd --ros-args --log-level network_node:=$LOG_LEVEL"
    
    echo -e "${CYAN}执行命令: ${GREEN}$cmd${NC}"
    echo ""
    
    if [ "$DEBUG_MODE" = true ]; then
        echo -e "${YELLOW}调试模式启动，按Ctrl+C停止节点${NC}"
        echo -e "${YELLOW}可以使用以下命令监控节点:${NC}"
        echo -e "  ${GREEN}ros2 node list${NC}                    # 查看节点列表"
        echo -e "  ${GREEN}ros2 node info /network_node${NC}      # 查看节点信息"
        echo -e "  ${GREEN}ros2 topic list${NC}                   # 查看话题列表"
        echo -e "  ${GREEN}ros2 service list${NC}                 # 查看服务列表"
        echo ""
    fi
    
    # 启动节点
    exec $cmd
}

# 主函数
function main() {
    echo -e "${BLUE}=== xiaoli network节点启动脚本 ===${NC}"
    echo ""
    
    # 检查环境
    if ! check_environment; then
        echo -e "${RED}环境检查失败，退出${NC}"
        exit 1
    fi
    
    # 如果只是检查环境，则退出
    if [ "$CHECK_ONLY" = true ]; then
        echo -e "${GREEN}环境检查完成，所有依赖都已满足${NC}"
        exit 0
    fi
    
    # 设置环境
    setup_environment
    
    # 显示启动信息
    show_startup_info
    
    # 启动节点
    start_network_node
}

# 信号处理
function cleanup() {
    echo ""
    echo -e "${YELLOW}正在关闭network节点...${NC}"
    exit 0
}

trap cleanup SIGINT SIGTERM

# 执行主函数
main "$@"
