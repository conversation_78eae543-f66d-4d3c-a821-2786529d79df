# xiaoli network节点配置文件
# 用于配置网络管理节点的各种参数

network_node:
  ros__parameters:
    # === 网络接口配置 ===
    wifi_connect_interface: "wlan0"        # WiFi连接接口
    p2p_connect_interface: "wlan1"         # P2P连接接口（AP模式）
    mobile_connect_interface: "eth1"       # 移动网络接口（蜂窝网络）
    
    # === AP热点配置 ===
    ssid: "xiaoli51"                       # AP热点名称
    static_ip: "***********"               # AP静态IP地址
    ap_subnet: "***********/24"            # AP子网配置
    dhcp_range_start: "***********0"       # DHCP起始地址
    dhcp_range_end: "***********00"        # DHCP结束地址
    
    # === 设备类型配置 ===
    device_type: "unitree"                 # 设备类型: "unitree" 或 "ysc"
    
    # === 蜂窝网络配置 ===
    cellular_option: "enable"              # 蜂窝网络选项: "enable" 或 "disable"
    cellular_apn: "cmnet"                  # APN设置
    cellular_timeout: 30                   # 连接超时时间（秒）
    
    # === 网络检测配置 ===
    network_check_interval: 10.0           # 网络状态检查间隔（秒）
    dns_check_interval: 300.0              # DNS健康检查间隔（秒）
    connection_timeout: 5.0                # 连接超时时间（秒）
    
    # === DNS配置 ===
    dns_primary_servers:                   # 主DNS服务器列表
      - "*********"                       # 阿里云DNS
      - "119.29.29.29"                    # 腾讯DNS
      - "114.114.114.114"                 # 114DNS
    dns_backup_servers:                    # 备用DNS服务器列表
      - "180.76.76.76"                    # 百度DNS
      - "1.2.4.8"                         # sDNS
      - "8.8.8.8"                         # Google DNS
    dns_test_domains:                      # DNS测试域名列表
      - "www.baidu.com"
      - "www.google.com"
      - "www.qq.com"
    
    # === 网络冲突检测 ===
    conflict_detection_enabled: true       # 启用网络冲突检测
    conflict_check_interval: 5.0           # 冲突检查间隔（秒）
    
    # === 日志配置 ===
    log_level: "INFO"                      # 日志级别: DEBUG, INFO, WARN, ERROR
    enable_network_logs: true              # 启用网络操作日志
    enable_dns_logs: true                  # 启用DNS操作日志
    
    # === 高级配置 ===
    auto_reconnect: true                   # 自动重连
    max_reconnect_attempts: 3              # 最大重连尝试次数
    reconnect_delay: 10.0                  # 重连延迟（秒）
    
    # === 系统集成配置 ===
    use_network_manager: true              # 使用NetworkManager
    use_systemd_resolved: true             # 使用systemd-resolved
    
    # === 监控配置 ===
    publish_network_status: true           # 发布网络状态
    publish_dns_status: true               # 发布DNS状态
    publish_conflict_status: true          # 发布冲突状态
    
    # === 安全配置 ===
    ap_password: ""                        # AP密码（空表示开放网络）
    ap_security: "open"                    # AP安全类型: "open", "wpa2", "wpa3"
    
    # === 性能配置 ===
    max_concurrent_connections: 10         # 最大并发连接数
    bandwidth_limit: 0                     # 带宽限制（0表示无限制）
    
    # === 调试配置 ===
    debug_mode: false                      # 调试模式
    verbose_logging: false                 # 详细日志
    save_debug_info: false                 # 保存调试信息
