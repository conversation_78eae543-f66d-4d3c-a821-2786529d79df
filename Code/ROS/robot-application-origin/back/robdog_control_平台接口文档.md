# robdog_control 平台接口文档

## 1. 文档概述

### 1.1 文档目的
本文档详细描述了 robdog_control 模块与各个平台的接口定义，包括硬件平台、ROS2平台、WebSocket平台和外部服务平台的通信协议和数据格式。

### 1.2 适用范围
- 硬件平台：Deep Lite、Unitree GO2
- 通信平台：ROS2、WebSocket
- 外部服务：导航、语音、视觉、SLAM等

## 2. 硬件平台接口

### 2.1 Deep Lite 平台接口

#### 2.1.1 通信配置
- **通信方式**: UDP Socket
- **服务器地址**: 192.168.1.120:6688
- **本地端口**: 43897
- **协议格式**: 自定义二进制协议
- **心跳频率**: 100ms

#### 2.1.2 协议数据结构
```cpp
// 协议头
struct head_t {
    int len;    // 消息长度
    int cmd;    // 命令码
    int stat;   // 状态码
};

// 命令数据结构
struct CommandHead {
    uint32_t code;  // 命令码
    uint32_t size;  // 数据大小
    uint32_t type;  // 命令类型
};

// 完整命令包
struct Command {
    CommandHead head;
    uint32_t data[256];  // 数据载荷
};
```

#### 2.1.3 主要命令码定义
```cpp
// 基础运动控制
#define STAND_UP_CODE           0x21010202  // 起立/趴下
#define TWIST_BODY_CODE         0x21010204  // 扭身体
#define TURN_OVER_CODE          0x21010205  // 翻身
#define BACKFLIP_CODE           0x21010502  // 后空翻
#define GREETING_CODE           0x21010507  // 打招呼
#define DANCE_CODE              0x2101030C  // 跳舞

// 移动控制
#define DEEP_CMD_MOVE_X         0x140       // 前后移动速度
#define DEEP_CMD_MOVE_Y         0x145       // 左右移动速度
#define DEEP_CMD_MOVE_YAW       0x141       // 旋转角速度
#define POSITION_CMD            0x31010D07  // 位置控制
#define POSITION_ANG_VEL        0x122       // 角速度控制

// 系统控制
#define DEEP_CMD_HEARTBEAT      0x21040001  // 心跳包
#define DEEP_CMD_TEMPERATURE    0x21040002  // 温度查询
#define DEEP_CMD_EMERGENCY_STOP 0x21020C0E  // 急停

// 动作模式
#define DEEP_CMD_ACTION_WALK    0x21010300  // 步行模式
#define DEEP_CMD_ACTION_RUN     0x21010307  // 奔跑模式
#define DEEP_CMD_ACTION_STAIR   0x21010407  // 爬楼模式
#define DEEP_CMD_ACTION_CLIMB   0x21010402  // 爬坡模式
```

#### 2.1.4 传感器数据结构
```cpp
// 传感器数据
struct sensor_t {
    int sensor_id;   // 传感器ID
    int value;       // 传感器值
    int timestamp;   // 时间戳
};

// 灯光控制
struct light_t {
    int color;       // 颜色
    int duration;    // 持续时间
};

// 表情控制
struct expression_t {
    int type;        // 表情类型
};

// 音频控制
struct audio_t {
    char path[256];  // 音频文件路径
};
```

#### 2.1.5 状态反馈
```cpp
// 机器狗基础状态
#define STATE_PRADOWN           1   // 趴下状态
#define STATE_READY_TO_STAND    4   // 准备起立状态
#define STATE_STANDING          5   // 正在起立状态
#define STATE_FORCE_CONTROL     6   // 力控状态
#define STATE_DOWNING           7   // 正在趴下状态
#define STATE_PROTECTION        8   // 失控保护状态
#define STATE_ADJUSTMENT        9   // 姿态调整状态
#define STATE_TURN_OVER         11  // 执行翻身动作
#define STATE_ZEROING           17  // 回零状态
#define STATE_BACKFLIP          18  // 执行后空翻动作
#define STATE_GREET             20  // 执行打招呼动作
```

### 2.2 Unitree GO2 平台接口

#### 2.2.1 通信配置
- **通信方式**: Unitree SDK
- **协议**: DDS (Data Distribution Service)
- **初始化**: `unitree::robot::ChannelFactory::Instance()->Init(0)`

#### 2.2.2 主要数据结构
```cpp
// 运动模式状态
class SportModeState_ {
private:
    TimeSpec_ stamp_;                    // 时间戳
    uint32_t error_code_;               // 错误码
    IMUState_ imu_state_;               // IMU状态
    uint8_t mode_;                      // 模式
    float progress_;                    // 进度
    uint8_t gait_type_;                 // 步态类型
    float foot_raise_height_;           // 抬脚高度
    std::array<float, 3> position_;     // 位置[x, y, z]
    float body_height_;                 // 身体高度
    std::array<float, 3> velocity_;     // 速度[vx, vy, vz]
    float yaw_speed_;                   // 偏航角速度
    std::array<float, 4> range_obstacle_; // 障碍物距离
    std::array<int16_t, 4> foot_force_; // 足部力
    std::array<float, 12> foot_position_body_; // 足部位置
    std::array<float, 12> foot_speed_body_;    // 足部速度
    std::array<PathPoint_, 10> path_point_;    // 路径点
};

// 运动模式命令
class SportModeCmd_ {
private:
    uint8_t mode_;                      // 模式
    uint8_t gait_type_;                 // 步态类型
    uint8_t speed_level_;               // 速度等级
    float foot_raise_height_;           // 抬脚高度
    float body_height_;                 // 身体高度
    std::array<float, 2> position_;     // 位置[x, y]
    std::array<float, 3> euler_;        // 欧拉角[roll, pitch, yaw]
    std::array<float, 2> velocity_;     // 速度[vx, vy]
    float yaw_speed_;                   // 偏航角速度
    BmsCmd_ bms_cmd_;                   // 电池管理命令
    std::array<PathPoint_, 30> path_point_; // 路径点
};

// 低级状态
class LowState_ {
private:
    std::array<uint8_t, 2> head_;       // 头部标识
    uint8_t level_flag_;                // 级别标志
    uint8_t frame_reserve_;             // 帧保留
    std::array<uint32_t, 2> sn_;        // 序列号
    std::array<uint32_t, 2> version_;   // 版本
    uint16_t bandwidth_;                // 带宽
    IMUState_ imu_state_;               // IMU状态
    std::array<MotorState_, 20> motor_state_; // 电机状态
    BmsState_ bms_state_;               // 电池状态
    std::array<int16_t, 4> foot_force_; // 足部力
    std::array<int16_t, 4> foot_force_est_; // 足部力估计
    uint32_t tick_;                     // 时钟
    std::array<uint8_t, 40> wireless_remote_; // 无线遥控
    uint8_t bit_flag_;                  // 位标志
    float adc_reel_;                    // ADC卷轴
    uint8_t temperature_ntc1_;          // 温度传感器1
    uint8_t temperature_ntc2_;          // 温度传感器2
    float power_v_;                     // 电压
    float power_a_;                     // 电流
    std::array<uint16_t, 4> fan_frequency_; // 风扇频率
    uint32_t reserve_;                  // 保留
    uint32_t crc_;                      // 校验码
};
```

#### 2.2.3 主要接口函数
```cpp
// 基础运动控制
int32_t robdogCtrl_StandUp();           // 起立
int32_t robdogCtrl_GetDown();           // 趴下
int32_t robdogCtrl_Sit();               // 坐下
int32_t robdogCtrl_Move(double x, double y, double yaw); // 移动控制

// 运动技能
int32_t robdogCtrl_Locomotion(RobdogCtrlMotion motion);
// 支持的运动技能：
// - ROBDOGCTRL_MOTION_TWIST: 扭身体(开心)
// - ROBDOGCTRL_MOTION_DANCE: 跳舞
// - ROBDOGCTRL_MOTION_FINGERHEART: 比心
// - ROBDOGCTRL_MOTION_HELLO: 打招呼
// - ROBDOGCTRL_MOTION_HANDSTAND: 倒立

// 步态控制
int32_t robdogCtrl_ChangeGait(RobdogCtrlGait gait);

// 模式控制
int32_t robdogCtrl_ManualMode();        // 手动模式
int32_t robdogCtrl_AutoMode();          // 自主模式
int32_t robdogCtrl_MoveMode();          // 移动模式

// 避障控制
int32_t robdogCtrl_AvoidOpen();         // 开启避障
int32_t robdogCtrl_AvoidClose();        // 关闭避障

// 系统控制
int32_t robdogCtrl_EmergencyStop();     // 急停
int32_t robdogCtrl_HeartBeat();         // 心跳
int32_t robdogCtrl_Temperature();       // 温度查询
```

## 3. ROS2 平台接口

### 3.1 Topics 接口

#### 3.1.1 订阅 Topics
```cpp
// 运动控制
"/catch_turtle/ctrl_instruct"          // geometry_msgs::msg::Twist
"/catch_turtle/action_type"            // homi_speech_interface::msg::RobdogAction
"/catch_turtle/continue_move"          // homi_speech_interface::msg::ContinueMove

// 网络连接
"/catch_turtle/ctrl_udpconnect"        // homi_speech_interface::msg::NewUdpConnect

// 语音交互
"/audio_node/wakeup_event"             // homi_speech_interface::msg::Wakeup

// 状态控制
"/deep_udp_ctrl/status_ctrl"           // homi_speech_interface::msg::ProprietySet

// 平台消息
"/homi_speech/sigc_event_topic"        // homi_speech_interface::msg::SIGCEvent

// 导航相关
"/navigation_status"                   // std_msgs::msg::String
"/navigation_status_notify"            // std_msgs::msg::String
"/navigation_position"                 // geometry_msgs::msg::Pose

// 外设监控
"/robdog_control/peripherals_monitor"  // std_msgs::msg::String

// SLAM相关
"/uslam/server_log"                    // std_msgs::msg::String
"/internet_conflict_status"            // std_msgs::msg::String
```

#### 3.1.2 发布 Topics
```cpp
// 状态上报
"/deep_udp_ctrl/status_report"         // homi_speech_interface::msg::ProprietySet
"/robdog_control/robot_status"         // homi_speech_interface::msg::RobdogState

// 导航控制
"/navigation_control"                  // std_msgs::msg::String
"/mapping_control"                     // std_msgs::msg::String
"/virtual_wall_control"                // std_msgs::msg::String
"/point_transform"                     // std_msgs::msg::String

// SLAM控制
"/uslam/client_command"                // std_msgs::msg::String
```

### 3.2 Services 接口

#### 3.2.1 客户端 Services
```cpp
// 语音服务
"/homi_speech/helper_assistant_speech_text_service"  // homi_speech_interface::srv::AssistantSpeechText
"/homi_speech/helper_assistant_abort_service"        // homi_speech_interface::srv::AssistantAbort
"/homi_speech/helper_set_wake_event_service"         // homi_speech_interface::srv::SetWakeEvent

// 平台服务
"/homi_speech/sigc_data_service"                     // homi_speech_interface::srv::SIGCData
"/homi_speech/net_ctrl_service"                      // homi_speech_interface::srv::NetCtrl

// 拍照服务
"/homi_speech/helper_assistant_take_photo_service"   // homi_speech_interface::srv::AssistantTakePhoto

// 外设控制
"/robdog_control/peripherals_ctrl"                  // homi_speech_interface::srv::PeripheralsCtrl
"/robdog_control/peripherals_status"                // homi_speech_interface::srv::PeripheralsStatus

// 系统服务
"/current_task"                                      // std_srvs::srv::Trigger

// IoT控制
"/homi_speech/iot_control_service"                   // homi_speech_interface::srv::IotControl
```

### 3.3 消息类型定义

#### 3.3.1 运动控制消息
```cpp
// geometry_msgs::msg::Twist
struct Twist {
    Vector3 linear;   // 线速度 [x, y, z]
    Vector3 angular;  // 角速度 [x, y, z]
};

// homi_speech_interface::msg::RobdogAction
struct RobdogAction {
    string actiontype;     // 动作类型
    string actionargument; // 动作参数
};

// homi_speech_interface::msg::ContinueMove
struct ContinueMove {
    string event;          // 事件类型
    int32 x, y, z;        // 移动参数
    int32 yaw, pitch, roll; // 姿态参数
};
```

#### 3.3.2 状态消息
```cpp
// homi_speech_interface::msg::ProprietySet
struct ProprietySet {
    string event;          // 事件类型
    string data;           // JSON数据
};

// homi_speech_interface::msg::RobdogState
struct RobdogState {
    string robot_state_details; // 机器人状态详情(JSON格式)
};
```

## 4. 机器人平台接口

### 4.1 事件接收接口 (handleEvent)

#### 4.1.1 接口定义
```cpp
void RobdogCenter::handleEvent(const std::string &eventType, const Json::Value &inValue, const Json::Value &jBody, const std::string& topic_name);
```

#### 4.1.2 支持的事件类型
```cpp
// 核心事件处理器映射
static const std::unordered_map<std::string, std::function<void(const Json::Value&,const Json::Value&,const std::string&)>> eventHandlers = {
    {"robot_action", handleRobotAction},           // 机器人动作控制
    {"robot_move", handleRobotMove},               // 机器人移动控制
    {"robot_view", handleRobotView},               // 机器人视角控制
    {"robot_task", handleRobotTask},               // 机器人任务执行
    {"robot_follow", handleRobotFollow},           // 机器人跟随功能
    {"robot_charge", handleRobotCharge},           // 机器人充电功能
    {"robot_navigation", handleRobotNavigation},   // 机器人导航功能
    {"robot_mapping", handleRobotMapping},         // 机器人建图功能
    {"robot_localization", handleRobotLocalization}, // 机器人定位功能
    {"robot_guard", handleRobotGuard},             // 机器人守护功能
    {"robot_trip", handleRobotTrip},               // 机器人出行功能
    {"robot_remind", handleRobotRemind},           // 机器人提醒功能
    {"robot_setting", handleRobotSetting},         // 机器人设置功能
    {"robot_status", handleRobotStatus},           // 机器人状态查询
    {"map_draw", handleMapDraw},                   // 地图绘制
    {"map_reposition", handleMapReposition},       // 地图重定位
    {"charging_pile_mark", handleChargeMark},      // 充电桩标记
    {"guard_done", handleGuardDone},               // 守护完成
    {"create_trace_action", handleTraceTrip},      // 创建轨迹动作
    {"data_report_ctl", handleDataReportCtl},      // 数据上报控制
    {"unknow", handleUnknown}                      // 未知事件
};
```

#### 4.1.3 事件消息格式
```json
// 基础事件消息格式
{
    "deviceId": "device_001",
    "domain": "DEVICE_INTERACTION",
    "event": "robot_action",
    "eventId": "event_123456789",
    "seq": 1,
    "body": {
        "actionType": "move",
        "parameters": {
            // 具体参数
        }
    }
}
```

#### 4.1.4 主要事件类型详解

**robot_action 事件**:
```json
{
    "event": "robot_action",
    "body": {
        "actionType": "standUp|getDown|sit|move|dance|greeting|fingerHeart",
        "parameters": {
            "x": 0.5,      // 移动速度 (m/s)
            "y": 0.0,      // 侧移速度 (m/s)
            "yaw": 0.0     // 旋转速度 (rad/s)
        }
    }
}
```

**robot_task 事件**:
```json
{
    "event": "robot_task",
    "body": {
        "taskType": "takePhotos|deliverExpress|fetchExpress|parkPatrol|familyMovePoint|batteryCharging",
        "targetPosition": {
            "x": 1.0,
            "y": 2.0,
            "theta": 0.0
        },
        "mapId": "map_001"
    }
}
```

**robot_follow 事件**:
```json
{
    "event": "robot_follow",
    "body": {
        "status": "start|stop",
        "followType": "vision|uwb",
        "targetId": "person_001"
    }
}
```

**map_draw 事件**:
```json
{
    "event": "map_draw",
    "body": {
        "action": "start|stop|save",
        "mapName": "office_map",
        "savePath": "/maps/"
    }
}
```

**data_report_ctl 事件**:
```json
{
    "event": "data_report_ctl",
    "body": {
        "actionType": "start|stop",
        "dataTypeList": ["position", "temperature", "rtk", "indoor"],
        "interval": 2000  // 上报间隔(ms)
    }
}
```

### 4.2 主动上报接口

#### 4.2.1 核心上报接口 - sendRequestData

**接口定义**:
```cpp
void RobdogCenter::sendRequestData(const std::string &data);
```

**功能描述**:
这是与机器人平台交互的核心接口，用于向平台发送各种状态数据、事件响应和主动上报信息。

> **详细说明**: 关于此接口的完整技术实现、调用场景、错误处理等详细信息，请参考 [sendRequestData_接口详细说明.md](./sendRequestData_接口详细说明.md)

**技术实现**:
```cpp
// 服务客户端初始化
platform_client = node_->create_client<homi_speech_interface::srv::SIGCData>(
    "/homi_speech/sigc_data_service"); // 上发给平台的消息

// 接口实现
void RobdogCenter::sendRequestData(const std::string &data) {
    auto request_sigc_data = std::make_shared<homi_speech_interface::srv::SIGCData::Request>();
    request_sigc_data->data = data;  // 设置请求数据

    // 等待服务可用 (超时1秒)
    auto ret = platform_client->wait_for_service(std::chrono::seconds(1));
    if (!ret) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),
                   "Failed to waitForExistence service assistant");
    }

    // 异步发送请求
    auto result = platform_client->async_send_request(request_sigc_data,
        std::bind(&RobdogCenter::plat_srv_callback, this, std::placeholders::_1));
}

// 平台响应回调
void RobdogCenter::plat_srv_callback(
    rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedFuture response) {
    auto response_value = response.get();
    int errorCode = response_value->error_code;
    if (errorCode != 0) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),
                   "service assistant return error code %d", errorCode);
    }
}
```

**服务接口规范**:
- **服务名称**: `/homi_speech/sigc_data_service`
- **服务类型**: `homi_speech_interface::srv::SIGCData`
- **请求格式**:
  ```cpp
  struct SIGCDataRequest {
      string data;  // JSON格式的数据字符串
  };
  ```
- **响应格式**:
  ```cpp
  struct SIGCDataResponse {
      int32 error_code;  // 错误码: 0-成功, 非0-失败
  };
  ```

**数据流向**:
```
robdog_control -> sendRequestData() -> /homi_speech/sigc_data_service -> homi_speech -> 机器人平台
```

**调用场景**:
1. **定时上报**: 点位信息、路径信息、状态信息
2. **事件响应**: 任务完成、建图完成、充电完成等
3. **异常上报**: 设备告警、错误状态、异常事件
4. **状态变更**: 机器人状态变化时的主动通知

**其他上报接口**:
```cpp
void sendMapInitialResponse(int code, const std::string& msg);    // 建图初始化响应
void sendMapCompleteResponse(int code, long mapId);               // 建图完成响应
void sendMapRepositionResponse(int code, const Json::Value &jBody); // 重定位响应
void sendChargeMarkResponse(int code, const std::string& msg);    // 充电桩标记响应
void deviceAlarmReport(int code);                                 // 设备告警上报
void processRepositioningResult(int taskStatusCode, const Json::Value &jBody); // 重定位结果处理
```

**FollowMe模块的平台接口**:
```cpp
// FollowNode中的平台通信接口
void FollowNode::sendDataToPlatform(const std::string &data);
// 使用相同的服务: /homi_speech/sigc_data_service
// 用于跟随功能的状态上报和异常通知
```

**平台服务处理流程**:
```cpp
// homi_speech模块中的服务处理
void SGICDataCallback(const std::shared_ptr<homi_speech_interface::srv::SIGCData::Request> req,
                     std::shared_ptr<homi_speech_interface::srv::SIGCData::Response> res) {
    RCLCPP_INFO(rclcpp::get_logger("speech_core"), "SGICDataCallback %s", req->data.c_str());

    // 调用内部接口发送到平台
    auto ret = homi::inner::sendEventOOB(std::make_shared<std::string>(req->data), false, 2000);

    if (ret < 0) {
        RCLCPP_WARN(rclcpp::get_logger("speech_core"), "SGICDataCallback ret=%d", ret);
        res->error_code = -1;  // 发送失败
    } else {
        res->error_code = 0;   // 发送成功
    }
}
```

**错误码定义**:
- `0`: 成功发送到平台
- `-1`: 发送失败 (网络异常、平台不可达等)

**超时机制**:
- 服务等待超时: 1秒
- 平台发送超时: 2秒 (在homi_speech中配置)

**使用示例**:
```cpp
// 发送点位上报
Json::Value response;
response["deviceId"] = RobotState::getInstance().getDeviceId();
response["domain"] = "DEVICE_INTERACTION";
response["event"] = "point_report";
response["eventId"] = "robdog_plat_" + std::to_string(getCurrentTimeStamp());
response["body"]["x"] = 1.23;
response["body"]["y"] = 2.45;
response["body"]["angle"] = 0.78;

Json::FastWriter writer;
std::string jsonString = writer.write(response);
sendRequestData(jsonString);  // 发送到平台
```

#### 4.2.2 定时上报接口
```cpp
// 定时器上报
void timerRobotPoseCallback();                    // 点位上报 (可配置间隔)
void timerRobotPathCallback();                    // 路径上报
void check_pos_and_temp_status(const std::string& device_id); // 位置温度状态上报
void traceRecordDynamicRep();                     // 轨迹动态上报
void traceRecordPathRep();                        // 轨迹路径上报
void RobotBroadcastStatusToPlat(int status);     // 智能播报状态上报
```

#### 4.2.3 上报消息格式

**点位上报 (point_report)**:
```json
{
    "deviceId": "device_001",
    "domain": "DEVICE_INTERACTION",
    "event": "point_report",
    "eventId": "robdog_plat_1234567890",
    "seq": 0,
    "body": {
        "x": 1.23,           // 位置X坐标
        "y": 2.45,           // 位置Y坐标
        "angle": 0.78,       // 角度
        "pointStatus": 1     // 点位状态
    }
}
```

**设备告警上报 (device_alarm_report)**:
```json
{
    "deviceId": "device_001",
    "domain": "DEVICE_INTERACTION",
    "event": "device_alarm_report",
    "eventId": "alarm_1234567890",
    "seq": 0,
    "body": {
        "alarmCode": 4103,
        "alarmMessage": "跟随目标丢失",
        "timestamp": "2025-07-14T10:30:00Z",
        "severity": "warning"
    }
}
```

**建图完成上报 (map_complete)**:
```json
{
    "deviceId": "device_001",
    "domain": "DEVICE_INTERACTION",
    "event": "map_complete",
    "eventId": "map_complete_1234567890",
    "seq": 0,
    "body": {
        "code": 11000,       // 状态码
        "mapId": 123456,     // 地图ID
        "mapUrl": "http://example.com/map.pgm",
        "message": "建图完成"
    }
}
```

**充电桩标记响应 (charging_pile_mark_response)**:
```json
{
    "deviceId": "device_001",
    "domain": "DEVICE_INTERACTION",
    "event": "charging_pile_mark_response",
    "eventId": "charge_mark_1234567890",
    "seq": 0,
    "body": {
        "code": 0,           // 0-成功 非0-失败
        "msg": "充电桩标记成功",
        "mapId": 123456
    }
}
```

**智能播报状态上报 (broadcast_report)**:
```json
{
    "deviceId": "device_001",
    "domain": "ROBOT_BUSINESS_DEVICE",
    "event": "broadcast_report",
    "eventId": "111111111",
    "seq": "1234567890",
    "body": {
        "status": 0,         // 0-被打断 1-正常运行
        "remindId": 123456   // 提醒ID
    }
}
```

**位置温度状态上报 (position_temperature_report)**:
```json
{
    "deviceId": "device_001",
    "domain": "DEVICE_INTERACTION",
    "event": "position_temperature_report",
    "eventId": "pos_temp_1234567890",
    "seq": 0,
    "body": {
        "dataType": "position|temperature|rtk|indoor",
        "value": "25.5",     // 温度值或位置状态
        "timestamp": "2025-07-14T10:30:00Z"
    }
}
```

#### 4.2.4 上报触发机制

**定时上报**:
- 点位上报: 可配置间隔 (默认2秒)
- 路径上报: 导航过程中定时上报
- 位置温度上报: 2秒间隔

**事件触发上报**:
- 任务完成/失败时上报
- 设备异常时立即上报
- 状态变更时上报
- 用户交互时上报

**条件上报**:
- 仅在导航状态下上报点位信息
- 温度变化超过阈值时上报
- RTK状态变化时上报
- 室内外状态切换时上报

## 5. WebSocket 平台接口

### 4.1 连接配置
- **默认端口**: 19002
- **默认URL**: ws://192.168.1.110:19002
- **消息格式**: JSON
- **连接类型**: 客户端模式

### 4.2 连接流程
```cpp
// 1. 初始化WebSocket
WS_Init(EN_WS_ClIENT, ws_port);

// 2. 设置消息回调
WS_SetMsgCallback(notifyWsMsgCallback, this);

// 3. 连接服务器
WS_Connect(strConnectUrl_.c_str());

// 4. 连接成功响应
{
    "client_type": "CLIENT_LAUNCHER",
    "action": "success"
}
```

### 4.3 消息格式

#### 4.3.1 基础消息结构
```json
{
    "type": "消息类型",
    "body": {
        // 消息体内容
    },
    "timestamp": "时间戳"
}
```

#### 4.3.2 运动控制消息
```json
// 基础运动
{
    "type": "robot_action",
    "body": {
        "actionType": "standUp|getDown|sit|move",
        "parameters": {
            "x": 0.5,      // 前后速度 (m/s)
            "y": 0.0,      // 左右速度 (m/s)
            "yaw": 0.0     // 旋转速度 (rad/s)
        }
    }
}

// 运动技能
{
    "type": "robot_action",
    "body": {
        "actionType": "motorSkill",
        "skill": "dance|greeting|fingerHeart|hello"
    }
}
```

#### 4.3.3 任务控制消息
```json
// 定点移动任务
{
    "type": "robot_task",
    "body": {
        "taskType": "takePhotos|deliverExpress|fetchExpress|parkPatrol",
        "targetPosition": {
            "x": 1.0,
            "y": 2.0,
            "theta": 0.0
        },
        "mapId": "map_001"
    }
}

// 跟随任务
{
    "type": "robot_action",
    "body": {
        "actionType": "followMe",
        "status": "start|stop",
        "followType": "vision|uwb"
    }
}
```

#### 4.3.4 导航控制消息
```json
// 导航控制
{
    "type": "navigation_control",
    "body": {
        "action": 0,        // 0:终止 1:启动 11:重定位
        "mapId": "map_001"
    }
}

// 建图控制
{
    "type": "mapping_control",
    "body": {
        "action": 1,        // 建图动作
        "url": "map_url",
        "mapId": "map_001"
    }
}
```

#### 4.3.5 状态查询消息
```json
// 机器人状态查询
{
    "type": "get_robot_status",
    "body": {}
}

// 状态响应
{
    "type": "robot_status",
    "body": {
        "currentState": "NORMAL|NAVIGATION|PATROL|CHARGING|FOLLOWING",
        "battery": 85,
        "position": {"x": 1.0, "y": 2.0, "theta": 0.0},
        "velocity": {"x": 0.0, "y": 0.0, "yaw": 0.0},
        "temperature": 45.5,
        "isCharging": false
    }
}
```

### 4.4 事件通知
```json
// 任务完成通知
{
    "type": "task_completed",
    "body": {
        "taskId": "task_001",
        "taskType": "takePhotos",
        "result": "success|failed",
        "message": "任务执行结果描述"
    }
}

// 异常通知
{
    "type": "error_notification",
    "body": {
        "errorCode": 4103,
        "errorMessage": "跟随目标丢失",
        "timestamp": "2025-07-14T10:30:00Z"
    }
}
```

## 5. 外部服务接口

### 5.1 导航服务接口

#### 5.1.1 SLAM服务
```cpp
// USLAM命令发送
Topic: "/uslam/client_command"
Type: std_msgs::msg::String
Format: JSON字符串

// 命令示例
{
    "command": "start_mapping|stop_mapping|start_localization|stop_localization",
    "parameters": {
        "map_name": "office_map",
        "save_path": "/maps/"
    }
}

// USLAM日志接收
Topic: "/uslam/server_log"
Type: std_msgs::msg::String
Callback: uslamServerLogCallback()
```

#### 5.1.2 导航控制服务
```cpp
// 导航控制发布
Topic: "/navigation_control"
Type: std_msgs::msg::String
Function: sendNavigationAction(int action, std::string mapId)

// 导航状态接收
Topic: "/navigation_status"
Type: std_msgs::msg::String
Callback: navStatusCallback()

// 导航状态通知
Topic: "/navigation_status_notify"
Type: std_msgs::msg::String
Callback: navStatusNotifyCallback()

// 导航位置接收
Topic: "/navigation_position"
Type: geometry_msgs::msg::Pose
Callback: navPositionCallback()
```

#### 5.1.3 地图管理服务
```cpp
// 建图控制
Topic: "/mapping_control"
Type: std_msgs::msg::String
Function: sendMapAction(int action, std::string url, std::string mapId)

// 虚拟墙控制
Topic: "/virtual_wall_control"
Type: std_msgs::msg::String

// 点位变换
Topic: "/point_transform"
Type: std_msgs::msg::String
```

### 5.2 语音服务接口

#### 5.2.1 语音播报服务
```cpp
// 服务名称
Service: "/homi_speech/helper_assistant_speech_text_service"
Type: homi_speech_interface::srv::AssistantSpeechText

// 请求格式
struct AssistantSpeechTextRequest {
    string text;           // 要播报的文本
    string voice_name;     // 语音名称
    float speed;           // 播报速度
    float volume;          // 音量
};

// 响应格式
struct AssistantSpeechTextResponse {
    bool success;          // 是否成功
    string message;        // 响应消息
};
```

#### 5.2.2 语音中止服务
```cpp
// 服务名称
Service: "/homi_speech/helper_assistant_abort_service"
Type: homi_speech_interface::srv::AssistantAbort

// 功能：中止当前语音播报
```

#### 5.2.3 唤醒事件服务
```cpp
// 服务名称
Service: "/homi_speech/helper_set_wake_event_service"
Type: homi_speech_interface::srv::SetWakeEvent

// 唤醒消息接收
Topic: "/audio_node/wakeup_event"
Type: homi_speech_interface::msg::Wakeup
Callback: iflyCallback()

// 唤醒消息格式
struct Wakeup {
    string wake_word;      // 唤醒词
    float confidence;      // 置信度
    int32 direction;       // 声源方向
    string timestamp;      // 时间戳
};
```

### 5.3 视觉服务接口

#### 5.3.1 拍照服务
```cpp
// 服务名称
Service: "/homi_speech/helper_assistant_take_photo_service"
Type: homi_speech_interface::srv::AssistantTakePhoto

// 请求格式
struct AssistantTakePhotoRequest {
    string save_path;      // 保存路径
    string photo_name;     // 照片名称
    int32 quality;         // 照片质量
};

// 响应格式
struct AssistantTakePhotoResponse {
    bool success;          // 是否成功
    string photo_path;     // 照片路径
    string message;        // 响应消息
};
```

#### 5.3.2 跟随视觉服务
```cpp
// 跟随控制
Function: actionFollow(int status)
// status: 0-停止跟随 1-开启跟随 2-开启UWB跟随

// 跟随状态监控
- 目标检测状态
- 目标丢失检测
- 跟随距离控制
- 障碍物避障
```

### 5.4 网络服务接口

#### 5.4.1 网络控制服务
```cpp
// 服务名称
Service: "/homi_speech/net_ctrl_service"
Type: homi_speech_interface::srv::NetCtrl

// 网络状态监控
Topic: "/internet_conflict_status"
Type: std_msgs::msg::String
Callback: checkInternetConflictStatus()
```

#### 5.4.2 IoT控制服务
```cpp
// 服务名称
Service: "/homi_speech/iot_control_service"
Type: homi_speech_interface::srv::IotControl

// IoT设备控制
- 智能家居设备控制
- 环境传感器数据获取
- 设备状态查询
```

### 5.5 外设服务接口

#### 5.5.1 外设控制服务
```cpp
// 服务名称
Service: "/robdog_control/peripherals_ctrl"
Type: homi_speech_interface::srv::PeripheralsCtrl

// 外设状态服务
Service: "/robdog_control/peripherals_status"
Type: homi_speech_interface::srv::PeripheralsStatus

// 外设监控
Topic: "/robdog_control/peripherals_monitor"
Type: std_msgs::msg::String
Callback: handle_peripherals_monitor_message()
```

#### 5.5.2 支持的外设类型
```cpp
// 传感器类型
- UWB定位传感器
- 蓝牙设备
- USB音频设备
- 激光雷达
- 深度相机
- RTK定位模块

// 控制设备
- LED灯光系统
- 音响系统
- 表情显示屏
- 机械臂/手势控制
```

## 6. 数据格式规范

### 6.1 JSON数据格式

#### 6.1.1 位置数据格式
```json
{
    "position": {
        "x": 1.0,          // 米
        "y": 2.0,          // 米
        "z": 0.0,          // 米
        "theta": 0.0       // 弧度
    }
}
```

#### 6.1.2 速度数据格式
```json
{
    "velocity": {
        "linear": {
            "x": 0.5,      // m/s
            "y": 0.0,      // m/s
            "z": 0.0       // m/s
        },
        "angular": {
            "x": 0.0,      // rad/s
            "y": 0.0,      // rad/s
            "z": 0.2       // rad/s
        }
    }
}
```

#### 6.1.3 状态数据格式
```json
{
    "robot_status": {
        "basic_state": 5,           // 基础状态码
        "motion_state": 0,          // 运动状态码
        "gait_state": 0,           // 步态状态码
        "battery_level": 85,        // 电池电量(%)
        "is_charging": false,       // 充电状态
        "temperature": 45.5,        // 温度(°C)
        "error_state": 0,          // 错误状态码
        "task_state": 4209664      // 任务状态码
    }
}
```

### 6.2 错误码规范

#### 6.2.1 系统错误码
```cpp
#define ROBDOGCTRL_ERROR_SUCCESS        0    // 成功
#define ROBDOGCTRL_ERROR_INVALID_PARA   -1   // 无效参数
#define ROBDOGCTRL_ERROR_INVALID_STATE  -2   // 无效状态
#define ROBDOGCTRL_ERROR_TIMEOUT        -3   // 超时
#define ROBDOGCTRL_ERROR_COMM_FAILED    -4   // 通信失败
```

#### 6.2.2 跟随功能错误码
```cpp
#define FollowNodeStarted               4000  // 跟随节点启动
#define FollowNodeStopped               4001  // 跟随节点停止
#define NoUWBData                       4101  // UWB检测失败
#define NoCameraData                    4102  // 相机检测失败
#define TargetLostDuringTracking        4103  // 跟随目标丢失
#define NoFollowTargetDetected          4106  // 未检测到跟随目标
```

#### 6.2.3 避障功能错误码
```cpp
#define ObstacleAvoidanceError_DeepCam      7101  // 无深度相机数据
#define ObstacleAvoidanceError_Elevation_Map 7102  // 无高程图数据
#define ObstacleAvoidanceError_TIMEOUT      7103  // 避障超时
#define ObstacleAvoidanceError_Elevation_IMU 7104  // 无IMU数据
```

## 7. 接口使用示例

### 7.1 基础运动控制示例
```cpp
// Deep Lite平台移动控制
CommandHead command;
command.code = 0x140;      // 前后移动
command.size = sizeof(float);
command.type = 0;
float speed = 0.5;         // 0.5 m/s
memcpy(command.data, &speed, sizeof(float));
deepSendPacket((uint8_t*)&command, sizeof(command));

// Unitree GO2平台移动控制
sport_client.Move(0.5, 0.0, 0.0);  // 前进0.5m/s
```

### 7.2 WebSocket消息发送示例
```cpp
// 发送运动控制消息
Json::Value message;
message["type"] = "robot_action";
message["body"]["actionType"] = "move";
message["body"]["parameters"]["x"] = 0.5;
message["body"]["parameters"]["y"] = 0.0;
message["body"]["parameters"]["yaw"] = 0.0;

WS_Send(message.toStyledString().c_str(), nConnectIndex_);
```

### 7.3 ROS2服务调用示例
```cpp
// 语音播报服务调用
auto request = std::make_shared<homi_speech_interface::srv::AssistantSpeechText::Request>();
request->text = "Hello, I am a robot dog";
request->voice_name = "default";
request->speed = 1.0;
request->volume = 0.8;

auto future = brocast_client->async_send_request(request);
```

---

**文档版本**: v1.0
**创建日期**: 2025-07-14
**最后更新**: 2025-07-14
**维护者**: robdog_control 开发团队
**审核者**: 系统架构师
