# robdog_control 模块需求文档

## 1. 项目概述

### 1.1 项目背景
robdog_control 是基于 ROS2 的机器狗控制系统，支持 Deep Lite 和 Unitree GO2 两种硬件平台，提供完整的机器狗控制、导航、跟随、任务管理等功能。

### 1.2 技术栈
- **框架**: ROS2 (Foxy/Humble)
- **编程语言**: C++14
- **通信协议**: UDP、WebSocket、ROS2 Topics/Services
- **依赖库**: jsoncpp、cv_bridge、tf2、ament_index_cpp
- **硬件平台**: Deep Lite、Unitree GO2

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    robdog_control 主程序                    │
├─────────────────────────────────────────────────────────────┤
│  ROS2 MultiThreadedExecutor (4线程)                        │
│  ├── RobdogCtrlNode (主控制节点)                           │
│  └── FollowNode (跟随功能节点)                             │
├─────────────────────────────────────────────────────────────┤
│                      核心管理模块                           │
│  ├── RobotInfoMgr (机器人信息管理)                         │
│  ├── RobdogCenter (中央控制管理)                           │
│  ├── TaskInfoMgr (任务管理)                                │
│  ├── AlarmInfoMgr (告警管理)                               │
│  ├── RobdogHandPosCtrl (手势控制)                          │
│  └── RobotState (状态管理)                                 │
├─────────────────────────────────────────────────────────────┤
│                      硬件适配层                             │
│  ├── RobDog_Ctrl_Deep (Deep Lite 适配)                    │
│  └── RobDog_Ctrl_Unitree (Unitree GO2 适配)               │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 通信架构
- **上层通信**: WebSocket (端口19002)
- **ROS2通信**: Topics/Services/Actions
- **硬件通信**: 
  - Deep Lite: UDP Socket (192.168.1.120:6688)
  - Unitree GO2: Unitree SDK
- **心跳机制**: 100ms 周期心跳包

## 3. 功能需求

### 3.1 基础运动控制
**需求描述**: 提供机器狗基础运动控制功能

**功能列表**:
- 起立/趴下/坐下控制
- 前后左右移动控制
- 旋转控制 (左转90°/右转90°)
- 位置精确控制
- 步态切换控制
- 运动技能执行

**接口定义**:
```cpp
void StandUp();                                    // 起立
void GetDown();                                    // 趴下
void Sit();                                        // 坐下
void positionCtrl(float x, float y, float radian); // 位置控制
int32_t robdogCtrl_Move(double x, double y, double yaw); // 移动控制
```

### 3.2 导航功能
**需求描述**: 提供自主导航和路径规划功能

**功能列表**:
- SLAM 建图和定位
- 路径规划和导航
- 重定位功能
- 导航状态监控
- 导航超时处理 (30秒)

**接口定义**:
```cpp
void sendNavigationAction(int action, std::string mapId);  // 导航控制
void sendMapAction(int action, std::string url, std::string mapId); // 建图操作
void sendLoaclizationAction(const std::string& action);   // 定位控制
```

### 3.3 跟随功能
**需求描述**: 提供人员跟随功能

**功能列表**:
- 视觉目标检测和跟随
- UWB 目标跟随
- 目标丢失处理 (15秒超时)
- 障碍物避障
- 安全监控 (电池、温度)

**安全阈值**:
- 电池低电量: 15%
- 关节温度: 110°C
- CPU温度: 67°C
- 目标丢失超时: 15秒

### 3.4 任务管理
**需求描述**: 提供任务调度和管理功能

**支持任务类型**:
- `takePhotos`: 拍照任务
- `deliverExpress`: 取快递任务
- `fetchExpress`: 寄快递任务
- `parkPatrol`: 园区巡逻任务
- `familyMovePoint`: 家庭定点移动
- `batteryCharging`: 自动充电任务
- `cancelMovement`: 取消移动任务

**任务状态**:
- `ROBOT_TASK_STATUS_PENDING`: 等待执行
- `ROBOT_TASK_STATUS_RUNNING`: 执行中
- `ROBOT_TASK_STATUS_COMPLETED`: 已完成
- `ROBOT_TASK_STATUS_FAILED`: 执行失败

## 4. 非功能需求

### 4.1 性能需求
- **实时性**: 控制指令响应时间 < 100ms
- **稳定性**: 系统连续运行时间 > 24小时
- **并发性**: 支持多线程并发处理 (4线程)
- **心跳频率**: 100ms 周期心跳包

### 4.2 可靠性需求
- **异常处理**: 完善的信号处理机制 (SIGINT/SIGTSTP)
- **超时机制**: 导航、跟随等功能的超时保护
- **状态监控**: 实时硬件状态监控和告警
- **故障恢复**: 自动重连和故障恢复机制

### 4.3 兼容性需求
- **硬件平台**: 支持 Deep Lite 和 Unitree GO2
- **操作系统**: Ubuntu 20.04 (ROS2 Foxy) / Ubuntu 22.04 (ROS2 Humble)
- **网络协议**: 支持 UDP、WebSocket、ROS2 通信

## 5. 接口规范

### 5.1 ROS2 接口
**Topics**:
- `/cmd_vel`: 速度控制指令
- `/robdog_action`: 机器狗动作指令
- `/continue_move`: 连续移动指令
- `/nav_status`: 导航状态反馈
- `/ifly_wakeup`: 语音唤醒消息

**Services**:
- `/assistant_speech_text`: 语音播报服务
- `/sigc_data`: SIGC数据服务
- `/assistant_abort`: 中止服务

### 5.2 WebSocket 接口
**端口**: 19002
**消息格式**: JSON
**主要消息类型**:
- 运动控制消息
- 任务执行消息
- 状态查询消息
- 配置设置消息

### 5.3 硬件接口
**Deep Lite 平台**:
- 通信方式: UDP Socket
- 地址: 192.168.1.120:6688
- 协议: 自定义二进制协议

**Unitree GO2 平台**:
- 通信方式: Unitree SDK
- 协议: DDS 通信协议

## 6. 数据模型

### 6.1 机器人状态模型
```cpp
enum class RobotStateEnum {
    NORMAL,      // 正常状态
    NAVIGATION,  // 导航中
    PATROL,      // 巡逻中
    CHARGING,    // 充电中
    FOLLOWING,   // 跟随中
    ERROR        // 错误状态
};
```

### 6.2 任务信息模型
```cpp
struct TaskInfo {
    std::string uuid;           // 任务唯一标识
    std::string taskType;       // 任务类型
    HomiTaskStatus status;      // 任务状态
    Json::Value parameters;     // 任务参数
    std::chrono::time_point<std::chrono::steady_clock> createTime; // 创建时间
};
```

### 6.3 机器人状态数据模型
```cpp
struct RobotStatusData {
    VelArray RPY;              // 姿态角 [Roll, Pitch, Yaw]
    VelArray RPYVel;           // 角速度
    VelArray XYZAcc;           // 加速度
    VelArray PosWorld;         // 世界坐标位置
    VelArray VelWorld;         // 世界坐标速度
    VelArray VelBody;          // 机体坐标速度
    bool IsCharging;           // 充电状态
    unsigned TouchDownAndStairTrot; // 触地和楼梯步态
};
```

## 7. 部署要求

### 7.1 硬件要求
- **CPU**: ARM64 或 x86_64
- **内存**: 最小 4GB RAM
- **存储**: 最小 32GB 存储空间
- **网络**: 以太网或 WiFi 连接

### 7.2 软件依赖
- **操作系统**: Ubuntu 20.04/22.04
- **ROS2**: Foxy/Humble
- **编译器**: GCC 9.0+
- **CMake**: 3.8+

### 7.3 配置文件
- **资源目录**: `resource/`
  - `audio/`: 音频文件
  - `config/`: 配置文件
  - `video/`: 视频文件
  - `bind_network/`: 网络配置
  - `internet/`: 网络相关配置

## 8. 错误处理和异常管理

### 8.1 跟随功能异常码
- `4000`: FollowNodeStarted - 跟随节点启动
- `4001`: FollowNodeStopped - 跟随节点停止
- `4101`: NoUWBData - UWB检测失败异常
- `4102`: NoCameraData - 相机检测失败异常
- `4103`: TargetLostDuringTracking - 跟随目标丢失异常
- `4106`: NoFollowTargetDetected - 未检测到跟随目标
- `7101`: ObstacleAvoidanceError_DeepCam - 避障功能异常(无深度相机数据)
- `7102`: ObstacleAvoidanceError_Elevation_Map - 避障功能异常(无高程图数据)
- `7103`: ObstacleAvoidanceError_TIMEOUT - 避障功能异常(避障超时)

### 8.2 系统监控和告警
- **CPU温度监控**: 实时监控CPU温度，超过阈值告警
- **内存使用监控**: 监控内存使用率，防止内存泄漏
- **网络状态监控**: 监控网络连接状态和质量
- **设备状态监控**: 监控USB音频、UWB、蓝牙等设备状态
- **进程状态监控**: 监控关键进程运行状态

## 9. 配置管理

### 9.1 机器人配置参数
```cpp
// 机器人制造商类型
enum RobManuModel {
    ROB_MANU_DEEP_LITE,    // Deep Lite 平台
    ROB_MANU_UNITREE_GO2   // Unitree GO2 平台
};

// 机器人模式配置
PropertyBuilderByName(int, RobdogStatus, 0);  // 机器狗当前模式(宅家或外出等)
PropertyBuilderByName(std::string, DeviceId, "");  // 设备ID
PropertyBuilderByName(std::string, UwbTag, "");    // UWB标签
```

### 9.2 家庭成员管理
```cpp
// 应急联系人信息
struct EmergencyContact {
    std::string name;        // 联系人姓名
    std::string phone;       // 联系电话
    std::string relation;    // 关系
};

// 家庭成员信息
struct FamilyMember {
    std::string name;        // 成员姓名
    std::string relation;    // 家庭关系
    std::string uwbTag;      // UWB标签ID
};
```

## 10. 扩展功能

### 10.1 智能提醒功能
- **定时提醒**: 基于时间的智能提醒
- **位置提醒**: 基于位置的智能提醒
- **事件提醒**: 基于事件的智能提醒

### 10.2 多媒体功能
- **音频播放**: 支持多种音频格式播放
- **视频播放**: 支持视频文件播放
- **TTS语音合成**: 文本转语音功能
- **表情控制**: 机器狗表情显示控制

### 10.3 网络功能
- **网络配置**: 自动网络配置和绑定
- **远程控制**: 支持远程控制和监控
- **数据同步**: 状态数据云端同步

## 11. 测试要求

### 11.1 单元测试
- 各模块功能接口测试
- 异常处理测试
- 边界条件测试
- 状态转换测试

### 11.2 集成测试
- 模块间通信测试
- 硬件平台适配测试
- 端到端功能测试
- 多线程并发测试

### 11.3 性能测试
- 实时性能测试 (控制延迟 < 100ms)
- 并发性能测试 (4线程并发)
- 长时间稳定性测试 (24小时+)
- 内存泄漏测试

### 11.4 硬件测试
- Deep Lite 平台功能测试
- Unitree GO2 平台功能测试
- 传感器数据准确性测试
- 通信稳定性测试

## 12. 维护和支持

### 12.1 日志管理
- **ROS2日志**: 使用RCLCPP_INFO/WARN/ERROR进行日志记录
- **日志级别**: 支持不同级别的日志输出
- **日志轮转**: 防止日志文件过大

### 12.2 调试支持
- **调试接口**: 提供调试模式和调试接口
- **状态查询**: 实时状态查询和监控
- **参数调整**: 运行时参数调整功能

### 12.3 版本管理
- **版本控制**: 严格的版本控制和发布流程
- **兼容性**: 向后兼容性保证
- **升级支持**: 在线升级和回滚支持

---

**文档版本**: v1.0
**创建日期**: 2025-07-14
**最后更新**: 2025-07-14
**维护者**: robdog_control 开发团队
**审核者**: 技术架构师
