#!/usr/bin/env python3
"""
最终调试配置测试
验证编译后的network节点是否可以正常调试
"""

import sys
import os
import subprocess

def main():
    print("🎯 测试编译后的network节点调试配置")
    print("=" * 60)
    
    # 设置环境
    workspace = "/mine/note/Code/ROS/robot-application-origin"
    xiaoli_ws = os.path.join(workspace, "xiaoli_application_ros2")
    
    print(f"📁 工作空间: {xiaoli_ws}")
    
    # 检查可执行文件
    network_exe = os.path.join(xiaoli_ws, "install/network/lib/network/network_node")
    if os.path.exists(network_exe):
        print(f"✅ 找到network节点可执行文件: {network_exe}")
    else:
        print(f"❌ 未找到network节点可执行文件: {network_exe}")
        return 1
    
    # 检查Python包
    network_pkg = os.path.join(xiaoli_ws, "install/network/lib/python3.10/site-packages")
    if os.path.exists(network_pkg):
        print(f"✅ 找到network Python包: {network_pkg}")
    else:
        print(f"❌ 未找到network Python包: {network_pkg}")
        return 1
    
    # 检查homi_speech_interface
    homi_pkg = os.path.join(xiaoli_ws, "install/homi_speech_interface/local/lib/python3.10/dist-packages")
    if os.path.exists(homi_pkg):
        print(f"✅ 找到homi_speech_interface包: {homi_pkg}")
    else:
        print(f"❌ 未找到homi_speech_interface包: {homi_pkg}")
        return 1
    
    print("\n🔧 VSCode调试配置:")
    print("1. 打开VSCode调试面板 (Ctrl+Shift+D)")
    print("2. 选择: '🚀 ROS2 Debug - network_node (Python源码直调)'")
    print("3. 在源码中设置断点:")
    print("   - xiaoli_application_ros2/src/network/network/network_node.py:26 (__init__)")
    print("   - xiaoli_application_ros2/src/network/network/network_node.py:106 (create_service)")
    print("4. 按F5开始调试")
    
    print("\n🎯 推荐断点位置:")
    print("   - InternetStartNode.__init__() - 节点初始化")
    print("   - handle_network_status() - 服务处理")
    print("   - check_network_conflict() - 网络冲突检测")
    
    print("\n✅ 调试环境配置完成！")
    print("💡 现在可以在VSCode中进行Python源码级调试了")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
