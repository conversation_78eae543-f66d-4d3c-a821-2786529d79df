#!/bin/bash

# 创建调试脚本生成器
# 为每个Python包创建调试启动脚本，解决相对导入问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 创建调试启动脚本 ===${NC}"

# 检查xiaoli_application_ros2目录
if [ ! -d "xiaoli_application_ros2/src" ]; then
    echo -e "${RED}错误: xiaoli_application_ros2/src 目录不存在${NC}"
    exit 1
fi

# 扫描Python包
python_packages=()
for pkg_dir in xiaoli_application_ros2/src/*; do
    if [ -d "$pkg_dir" ]; then
        pkg_name=$(basename "$pkg_dir")
        
        # 检查是否有Python包结构
        if [ -d "$pkg_dir/$pkg_name" ] && [ -f "$pkg_dir/$pkg_name/__init__.py" ]; then
            # 查找主节点文件
            main_files=(
                "$pkg_dir/$pkg_name/${pkg_name}_node.py"
                "$pkg_dir/$pkg_name/main.py"
                "$pkg_dir/$pkg_name/node.py"
            )
            
            for main_file in "${main_files[@]}"; do
                if [ -f "$main_file" ]; then
                    python_packages+=("$pkg_name")
                    echo -e "${GREEN}✓ 找到Python包: $pkg_name${NC}"
                    break
                fi
            done
        fi
    fi
done

echo -e "${CYAN}总计找到 ${#python_packages[@]} 个Python包${NC}"

# 为每个包创建调试脚本
for pkg in "${python_packages[@]}"; do
    debug_script="debug_${pkg}_node.py"
    
    echo -e "${YELLOW}创建调试脚本: $debug_script${NC}"
    
    cat > "$debug_script" << EOF
#!/usr/bin/env python3

"""
${pkg}节点调试启动脚本
解决相对导入问题，用于VSCode调试
"""

import sys
import os

# 添加包路径到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
pkg_src_dir = os.path.join(current_dir, 'xiaoli_application_ros2', 'src', '${pkg}')
sys.path.insert(0, pkg_src_dir)

# 导入并运行主函数
try:
    from ${pkg}.${pkg}_node import main
    
    if __name__ == '__main__':
        main()
except ImportError as e:
    print(f"导入错误: {e}")
    print(f"请检查 xiaoli_application_ros2/src/${pkg}/${pkg}/${pkg}_node.py 是否存在")
    sys.exit(1)
EOF
    
    chmod +x "$debug_script"
    echo -e "${GREEN}✓ 已创建: $debug_script${NC}"
done

# 更新launch.json配置建议
echo ""
echo -e "${BLUE}=== 调试脚本创建完成 ===${NC}"
echo -e "${YELLOW}可用的调试脚本:${NC}"
for pkg in "${python_packages[@]}"; do
    echo -e "  ${GREEN}debug_${pkg}_node.py${NC} - 调试${pkg}包"
done

echo ""
echo -e "${CYAN}VSCode调试配置建议:${NC}"
echo "在 .vscode/launch.json 中使用这些脚本："
echo ""
for pkg in "${python_packages[@]}"; do
    echo "\"program\": \"\${workspaceFolder}/debug_${pkg}_node.py\","
done

echo ""
echo -e "${YELLOW}使用方法:${NC}"
echo "1. 在VSCode中设置断点"
echo "2. 选择对应的调试配置"
echo "3. 按F5开始调试"
echo "4. 调试器会正确处理相对导入"
