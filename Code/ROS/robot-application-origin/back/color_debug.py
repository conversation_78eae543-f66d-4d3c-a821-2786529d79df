#!/usr/bin/env python3
"""
颜色调试脚本
用于诊断和修复颜色显示问题
"""

import rclpy
from rclpy.node import Node
from rcl_interfaces.srv import SetParameters
from rcl_interfaces.msg import Parameter, ParameterValue, ParameterType
import time
import sys

class ColorDebugger(Node):
    def __init__(self):
        super().__init__('color_debugger')
        self.client = self.create_client(SetParameters, '/pwm_touch_node/set_parameters')
        
        while not self.client.wait_for_service(timeout_sec=1.0):
            self.get_logger().info('等待pwm_touch_node服务...')

    def set_color(self, color_hex, mode='static'):
        """设置LED颜色"""
        request = SetParameters.Request()
        
        # 设置模式参数
        mode_param = Parameter()
        mode_param.name = 'mode'
        mode_param.value = ParameterValue()
        mode_param.value.type = ParameterType.PARAMETER_STRING
        mode_param.value.string_value = mode
        
        # 设置颜色参数
        color_param = Parameter()
        color_param.name = 'color'
        color_param.value = ParameterValue()
        color_param.value.type = ParameterType.PARAMETER_STRING
        color_param.value.string_value = color_hex
        
        request.parameters = [mode_param, color_param]
        
        future = self.client.call_async(request)
        rclpy.spin_until_future_complete(self, future)
        
        if future.result() is not None:
            result = future.result()
            if result.results[0].successful and result.results[1].successful:
                self.get_logger().info(f'成功设置颜色: {color_hex}')
                return True
            else:
                self.get_logger().error(f'设置颜色失败: {result.results[0].reason if not result.results[0].successful else result.results[1].reason}')
                return False
        else:
            self.get_logger().error('服务调用失败')
            return False

def test_primary_colors(debugger):
    """测试三原色"""
    print("\n=== 测试三原色 ===")
    
    primary_colors = [
        ("红色", "#FF0000"),
        ("绿色", "#00FF00"), 
        ("蓝色", "#0000FF")
    ]
    
    for name, color in primary_colors:
        print(f"\n测试 {name} ({color})")
        print("请观察LED颜色是否正确显示为纯" + name)
        
        if debugger.set_color(color):
            input("按回车键继续下一个颜色...")
        else:
            print(f"设置{name}失败")
            return False
    
    return True

def test_secondary_colors(debugger):
    """测试二次色"""
    print("\n=== 测试二次色 ===")
    
    secondary_colors = [
        ("黄色 (红+绿)", "#FFFF00"),
        ("青色 (绿+蓝)", "#00FFFF"),
        ("品红 (红+蓝)", "#FF00FF")
    ]
    
    for name, color in secondary_colors:
        print(f"\n测试 {name} ({color})")
        print(f"请观察LED颜色是否正确显示为{name.split('(')[0]}")
        
        if debugger.set_color(color):
            input("按回车键继续下一个颜色...")
        else:
            print(f"设置{name}失败")
            return False
    
    return True

def test_problematic_colors(debugger):
    """测试问题颜色"""
    print("\n=== 测试问题颜色 ===")
    
    problematic_colors = [
        ("橙色", "#FFA500"),
        ("粉色", "#FFC0CB"),
        ("紫色", "#800080"),
        ("深紫色", "#4B0082"),
        ("浅紫色", "#DDA0DD")
    ]
    
    results = {}
    
    for name, color in problematic_colors:
        print(f"\n测试 {name} ({color})")
        print(f"请观察LED颜色是否正确显示为{name}")
        
        if debugger.set_color(color):
            response = input("颜色显示是否正确? (y/n): ").lower().strip()
            results[name] = response == 'y'
        else:
            print(f"设置{name}失败")
            results[name] = False
    
    return results

def test_channel_mapping(debugger):
    """测试通道映射"""
    print("\n=== 测试PWM通道映射 ===")
    print("这个测试将帮助确定RGB通道是否正确映射")
    
    # 测试每个通道
    channels = [
        ("第一个通道 (应该是红色)", "#FF0000"),
        ("第二个通道 (应该是绿色)", "#00FF00"),
        ("第三个通道 (应该是蓝色)", "#0000FF")
    ]
    
    mapping_results = {}
    
    for channel_name, color in channels:
        print(f"\n测试 {channel_name}")
        print(f"设置颜色: {color}")
        
        if debugger.set_color(color):
            actual_color = input("实际显示的颜色是什么? (red/green/blue/other): ").lower().strip()
            mapping_results[channel_name] = actual_color
        else:
            mapping_results[channel_name] = "failed"
    
    return mapping_results

def analyze_results(primary_ok, secondary_ok, problematic_results, mapping_results):
    """分析测试结果并给出建议"""
    print("\n" + "="*60)
    print("测试结果分析")
    print("="*60)
    
    if primary_ok:
        print("✓ 三原色测试通过")
    else:
        print("✗ 三原色测试失败")
        
    if secondary_ok:
        print("✓ 二次色测试通过")
    else:
        print("✗ 二次色测试失败")
    
    print("\n问题颜色测试结果:")
    for color, result in problematic_results.items():
        status = "✓" if result else "✗"
        print(f"  {status} {color}")
    
    print("\nPWM通道映射结果:")
    for channel, actual in mapping_results.items():
        print(f"  {channel}: 实际显示 {actual}")
    
    # 分析和建议
    print("\n" + "="*60)
    print("问题分析和建议")
    print("="*60)
    
    if not primary_ok:
        print("🔴 严重问题: 三原色显示异常")
        print("   建议: 检查硬件连接和PWM配置")
        return
    
    if not secondary_ok:
        print("🟡 中等问题: 二次色显示异常")
        print("   可能原因: PWM通道映射错误或极性问题")
    
    # 分析通道映射
    expected_mapping = {
        "第一个通道 (应该是红色)": "red",
        "第二个通道 (应该是绿色)": "green", 
        "第三个通道 (应该是蓝色)": "blue"
    }
    
    mapping_correct = True
    for channel, expected in expected_mapping.items():
        actual = mapping_results.get(channel, "unknown")
        if actual != expected:
            mapping_correct = False
            print(f"🔴 通道映射错误: {channel} 显示为 {actual}，应该是 {expected}")
    
    if mapping_correct:
        print("✓ PWM通道映射正确")
    else:
        print("\n建议的修复方案:")
        print("1. 检查pwm_gpio_map参数配置")
        print("2. 确认硬件连接是否正确")
        print("3. 可能需要调整通道顺序")
    
    # 分析问题颜色
    failed_colors = [color for color, result in problematic_results.items() if not result]
    if failed_colors:
        print(f"\n🟡 以下颜色显示异常: {', '.join(failed_colors)}")
        print("可能的解决方案:")
        print("1. 尝试伽马校正")
        print("2. 检查PWM极性设置")
        print("3. 调整颜色空间转换")

def main():
    rclpy.init()
    
    debugger = ColorDebugger()
    
    try:
        print("颜色调试工具")
        print("="*60)
        print("这个工具将帮助诊断LED颜色显示问题")
        print("请确保pwm_touch_node正在运行")
        
        input("\n按回车键开始测试...")
        
        # 先关闭LED
        debugger.set_color("#000000")
        time.sleep(1)
        
        # 执行测试
        primary_ok = test_primary_colors(debugger)
        secondary_ok = test_secondary_colors(debugger) if primary_ok else False
        problematic_results = test_problematic_colors(debugger)
        mapping_results = test_channel_mapping(debugger)
        
        # 分析结果
        analyze_results(primary_ok, secondary_ok, problematic_results, mapping_results)
        
        # 关闭LED
        print("\n测试完成，关闭LED...")
        debugger.set_color("#000000")
        
    except KeyboardInterrupt:
        print("\n测试被中断")
        debugger.set_color("#000000")
    finally:
        debugger.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
