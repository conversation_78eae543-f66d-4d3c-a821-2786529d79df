#!/bin/bash

# 快速修复重复包问题的脚本
# 专门解决colcon构建时的重复包名错误

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 快速修复重复包问题 ===${NC}"
echo ""

# 检查是否在正确的目录
if [ ! -d "xiaoli_application_ros2" ]; then
    echo -e "${RED}错误: 当前目录下未找到 xiaoli_application_ros2${NC}"
    echo -e "${YELLOW}请在包含 xiaoli_application_ros2 的根目录中运行此脚本${NC}"
    exit 1
fi

echo -e "${YELLOW}正在修复重复包问题...${NC}"
echo ""

# 1. 删除备份目录
echo -e "${BLUE}1. 清理备份目录${NC}"
if [ -d "_gsdata_" ]; then
    echo -e "  删除 _gsdata_ 目录..."
    rm -rf _gsdata_
    echo -e "  ${GREEN}✓ 已删除 _gsdata_${NC}"
else
    echo -e "  ${GREEN}✓ _gsdata_ 目录不存在${NC}"
fi

# 2. 删除ROS1相关目录
echo -e "${BLUE}2. 清理ROS1目录${NC}"
ros1_dirs=("deeprobots_application_ros1" "deeprob_ws_ctrl")
for dir in "${ros1_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo -e "  删除 $dir 目录..."
        rm -rf "$dir"
        echo -e "  ${GREEN}✓ 已删除 $dir${NC}"
    else
        echo -e "  ${GREEN}✓ $dir 目录不存在${NC}"
    fi
done

# 3. 处理3rdParty重复包
echo -e "${BLUE}3. 处理3rdParty重复包${NC}"
if [ -d "3rdParty" ]; then
    echo -e "  检查3rdParty目录中的重复包..."

    # 删除重复的SensorHub包，只保留一个
    if [ -d "3rdParty/SensorHub" ] && [ -d "3rdParty/SensorHub-CB1" ]; then
        echo -e "  删除重复的SensorHub-CB1..."
        rm -rf "3rdParty/SensorHub-CB1"
        echo -e "  ${GREEN}✓ 已删除 SensorHub-CB1${NC}"
    fi

    if [ -d "3rdParty/new_sensor" ]; then
        echo -e "  删除重复的new_sensor..."
        rm -rf "3rdParty/new_sensor"
        echo -e "  ${GREEN}✓ 已删除 new_sensor${NC}"
    fi

    # 检查其他可能的重复包
    duplicates=$(find 3rdParty -name "package.xml" -exec dirname {} \; | xargs -I {} basename {} | sort | uniq -d)
    if [ -n "$duplicates" ]; then
        echo -e "  ${YELLOW}警告: 发现其他重复包: $duplicates${NC}"
        echo -e "  ${YELLOW}建议手动检查3rdParty目录${NC}"
    fi
else
    echo -e "  ${GREEN}✓ 3rdParty目录不存在${NC}"
fi

# 4. 清理构建缓存
echo -e "${BLUE}4. 清理构建缓存${NC}"
build_dirs=("build" "install" "log")
for dir in "${build_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo -e "  删除 $dir 目录..."
        rm -rf "$dir"
        echo -e "  ${GREEN}✓ 已删除 $dir${NC}"
    else
        echo -e "  ${GREEN}✓ $dir 目录不存在${NC}"
    fi
done

# 5. 检查剩余的包
echo -e "${BLUE}5. 检查剩余包${NC}"
if [ -d "xiaoli_application_ros2/src" ]; then
    pkg_count=$(find xiaoli_application_ros2/src -name "package.xml" | wc -l)
    echo -e "  找到 ${GREEN}$pkg_count${NC} 个ROS2包"
    
    echo -e "  ${BLUE}可用的包:${NC}"
    find xiaoli_application_ros2/src -name "package.xml" -exec dirname {} \; | sed 's|xiaoli_application_ros2/src/||' | sort | while read pkg; do
        echo -e "    ${GREEN}$pkg${NC}"
    done
else
    echo -e "  ${RED}错误: xiaoli_application_ros2/src 目录不存在${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}=== 修复完成 ===${NC}"
echo ""
echo -e "${BLUE}现在可以重新构建:${NC}"
echo -e "  ${GREEN}./build_xiaoli.sh${NC}                    # 编译所有xiaoli包"
echo -e "  ${GREEN}./build_xiaoli.sh network${NC}            # 只编译network包"
echo -e "  ${GREEN}./build.sh --workspace-type xiaoli${NC}   # 使用通用脚本"
echo ""
echo -e "${YELLOW}提示: 如果仍有问题，请检查是否有其他重复的目录${NC}"
