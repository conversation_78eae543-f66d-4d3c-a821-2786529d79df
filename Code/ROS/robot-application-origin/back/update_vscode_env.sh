#!/bin/bash

# 自动更新VSCode调试环境配置
# 扫描所有已编译的包并生成正确的PYTHONPATH

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 更新VSCode调试环境配置 ===${NC}"

# 检查安装目录
INSTALL_DIR="install/x86_64/Debug"
if [ ! -d "$INSTALL_DIR" ]; then
    echo -e "${RED}错误: 安装目录 $INSTALL_DIR 不存在${NC}"
    echo -e "${YELLOW}请先编译包: ./xiaoli_application_ros2/build_xiaoli.sh${NC}"
    exit 1
fi

# 扫描所有已编译包的Python路径
echo -e "${YELLOW}扫描已编译包的Python路径...${NC}"
PYTHON_PATHS=""
count=0

for pkg_dir in "$INSTALL_DIR"/*; do
    if [ -d "$pkg_dir" ]; then
        pkg_name=$(basename "$pkg_dir")

        # 检查两种可能的Python路径
        python_paths=(
            "$pkg_dir/lib/python3.10/site-packages"
            "$pkg_dir/local/lib/python3.10/dist-packages"
        )

        for python_path in "${python_paths[@]}"; do
            if [ -d "$python_path" ]; then
                if [ -n "$PYTHON_PATHS" ]; then
                    PYTHON_PATHS="$PYTHON_PATHS:"
                fi
                PYTHON_PATHS="$PYTHON_PATHS$(pwd)/$python_path"
                echo -e "${GREEN}✓ 找到包: $pkg_name ($(basename $(dirname $python_path)))${NC}"
                count=$((count + 1))
                break  # 找到一个就够了
            fi
        done
    fi
done

echo -e "${CYAN}总计找到 $count 个包的Python路径${NC}"

# 添加ROS2系统路径
ROS2_PATHS="/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages"
PYTHON_PATHS="$PYTHON_PATHS:$ROS2_PATHS"

# 生成环境文件
ENV_FILE=".vscode/ros2.env"
echo -e "${BLUE}生成环境文件: $ENV_FILE${NC}"

cat > "$ENV_FILE" << EOF
# ROS2环境变量配置文件 - 自动生成
# 生成时间: $(date)
# 用于VSCode调试配置

# ROS2基础环境
ROS_VERSION=2
ROS_DISTRO=humble
ROS_DOMAIN_ID=0

# ROS2路径配置
AMENT_PREFIX_PATH=$(pwd)/$INSTALL_DIR:/opt/ros/humble
CMAKE_PREFIX_PATH=$(pwd)/$INSTALL_DIR:/opt/ros/humble

# Python路径配置 - 包含所有已编译包
PYTHONPATH=$PYTHON_PATHS

# 库路径配置
LD_LIBRARY_PATH=$(pwd)/$INSTALL_DIR/lib:/opt/ros/humble/lib

# PKG_CONFIG路径
PKG_CONFIG_PATH=$(pwd)/$INSTALL_DIR/lib/pkgconfig:/opt/ros/humble/lib/pkgconfig

# ROS2工具路径
PATH=/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

# RMW实现
RMW_IMPLEMENTATION=rmw_fastrtps_cpp

# 日志配置
RCUTILS_LOGGING_BUFFERED_STREAM=1
RCUTILS_LOGGING_USE_STDOUT=0

# 调试配置
RCUTILS_CONSOLE_OUTPUT_FORMAT=[{severity}] [{name}]: {message}
EOF

echo -e "${GREEN}环境文件已生成: $ENV_FILE${NC}"

# 显示包含的包
echo ""
echo -e "${BLUE}包含的包:${NC}"
for pkg_dir in "$INSTALL_DIR"/*; do
    if [ -d "$pkg_dir" ]; then
        pkg_name=$(basename "$pkg_dir")
        python_path="$pkg_dir/lib/python3.10/site-packages"
        
        if [ -d "$python_path" ]; then
            echo -e "  ${GREEN}$pkg_name${NC}"
        fi
    fi
done

echo ""
echo -e "${YELLOW}提示:${NC}"
echo "• 每次编译新包后，请重新运行此脚本更新环境配置"
echo "• VSCode调试配置将自动使用更新后的环境变量"
echo "• 现在可以在VSCode中调试xiaoli包了！"

echo ""
echo -e "${CYAN}快速测试调试配置:${NC}"
echo "1. 在VSCode中打开调试面板 (Ctrl+Shift+D)"
echo "2. 选择 '🚀 xiaoli一键编译+调试 - network (Python)'"
echo "3. 点击绿色播放按钮开始调试"
