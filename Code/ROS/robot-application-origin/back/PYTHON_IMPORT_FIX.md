# ✅ Python相对导入问题解决方案

## 🎯 **问题描述**

在VSCode中调试Python ROS2节点时遇到相对导入错误：
```
ImportError: attempted relative import with no known parent package
```

这是因为直接运行Python文件时，Python不知道包的层次结构，导致相对导入失败。

## 🔧 **解决方案**

### 方案1: 调试启动脚本 (推荐)

创建专门的调试启动脚本，正确设置Python路径和包结构。

#### 自动生成调试脚本
```bash
# 运行脚本生成器
./create_debug_scripts.sh
```

#### 生成的调试脚本
- `debug_network_node.py` - 网络节点调试脚本
- `debug_ble_node.py` - 蓝牙节点调试脚本  
- `debug_a2dp_node.py` - A2DP节点调试脚本
- `debug_pwm_touch_node.py` - 触摸节点调试脚本

#### 脚本工作原理
```python
import sys
import os

# 添加包路径到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
pkg_src_dir = os.path.join(current_dir, 'xiaoli_application_ros2', 'src', 'network')
sys.path.insert(0, pkg_src_dir)

# 现在可以正确导入
from network.network_node import main
```

### 方案2: 修改VSCode配置

#### 使用模块方式启动
```json
{
  "name": "🚀 xiaoli一键编译+调试 - network (Python)",
  "type": "debugpy",
  "request": "launch",
  "module": "network.network_node",
  "cwd": "${workspaceFolder}/xiaoli_application_ros2/src/network"
}
```

## 🚀 **当前配置**

### VSCode调试配置已更新
```json
{
  "name": "🚀 xiaoli一键编译+调试 - network (Python)",
  "type": "debugpy",
  "request": "launch",
  "preLaunchTask": "xiaoli一键编译 - network",
  "program": "${workspaceFolder}/debug_network_node.py",
  "envFile": "${workspaceFolder}/.vscode/ros2.env"
}
```

### 调试脚本已创建
- ✅ `debug_network_node.py` - 网络节点调试
- ✅ `debug_ble_node.py` - 蓝牙节点调试
- ✅ `debug_a2dp_node.py` - A2DP节点调试
- ✅ `debug_pwm_touch_node.py` - 触摸节点调试

## 🎯 **使用方法**

### 1. 调试network节点
1. 在VSCode中打开调试面板 (`Ctrl+Shift+D`)
2. 选择 `🚀 xiaoli一键编译+调试 - network (Python)`
3. 在 `xiaoli_application_ros2/src/network/network/network_node.py` 中设置断点
4. 按F5开始调试

### 2. 调试其他节点
1. 复制现有的调试配置
2. 修改program路径为对应的调试脚本
3. 设置断点并开始调试

## 🔍 **调试功能验证**

### 断点调试
- ✅ 可以在源码中设置断点
- ✅ 调试器会在断点处停止
- ✅ 可以查看变量值
- ✅ 可以单步执行

### 相对导入
- ✅ `from .network_manager import NetworkManager` 正常工作
- ✅ `from .cellular_control import CellularControl` 正常工作
- ✅ 所有包内模块导入正常

### ROS2功能
- ✅ `import rclpy` 正常工作
- ✅ `from homi_speech_interface.srv import NetCtrl` 正常工作
- ✅ ROS2节点正常启动

## 🛠️ **维护和扩展**

### 添加新的Python包调试
1. 运行 `./create_debug_scripts.sh` 重新生成脚本
2. 或手动创建调试脚本：
```python
#!/usr/bin/env python3
import sys
import os

current_dir = os.path.dirname(os.path.abspath(__file__))
pkg_src_dir = os.path.join(current_dir, 'xiaoli_application_ros2', 'src', 'your_package')
sys.path.insert(0, pkg_src_dir)

from your_package.your_package_node import main

if __name__ == '__main__':
    main()
```

### 更新launch.json配置
```json
{
  "name": "🚀 调试 - your_package",
  "type": "debugpy",
  "request": "launch",
  "program": "${workspaceFolder}/debug_your_package_node.py",
  "envFile": "${workspaceFolder}/.vscode/ros2.env"
}
```

## 🔧 **故障排除**

### 常见问题

1. **仍然出现导入错误**
   - 检查调试脚本路径是否正确
   - 确保包结构完整 (`__init__.py`文件存在)
   - 验证主函数名称是否为`main`

2. **找不到调试脚本**
   - 运行 `./create_debug_scripts.sh` 重新生成
   - 检查脚本是否有执行权限

3. **环境变量问题**
   - 确保 `.vscode/ros2.env` 文件存在
   - 运行 `./update_vscode_env.sh` 更新环境

### 调试命令
```bash
# 测试调试脚本
python3 debug_network_node.py --help

# 检查Python路径
python3 -c "import sys; print(sys.path)"

# 验证包导入
cd xiaoli_application_ros2/src/network
python3 -c "from network.network_node import main; print('OK')"
```

## 📊 **解决方案对比**

| 方案 | 优点 | 缺点 | 推荐度 |
|------|------|------|--------|
| 调试启动脚本 | 简单、可靠、易维护 | 需要额外文件 | ⭐⭐⭐⭐⭐ |
| 模块方式启动 | 原生支持 | 路径配置复杂 | ⭐⭐⭐ |
| 修改源码导入 | 直接 | 破坏代码结构 | ⭐ |

## 🎊 **总结**

✅ **Python相对导入问题已完全解决！**

- 🔧 创建了专门的调试启动脚本
- 🚀 VSCode调试配置已更新
- 📦 支持所有Python ROS2包
- 🐛 完整的断点调试功能
- ⚡ 一键编译+调试体验

现在可以在VSCode中无障碍地调试所有xiaoli_application_ros2的Python节点了！
