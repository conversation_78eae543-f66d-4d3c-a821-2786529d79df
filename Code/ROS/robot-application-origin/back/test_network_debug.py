#!/usr/bin/env python3
"""
测试network节点调试配置
验证环境变量和导入是否正常
"""

import sys
import os

def test_environment():
    """测试环境变量"""
    print("🔍 测试环境变量...")
    print(f"ROS_DISTRO: {os.environ.get('ROS_DISTRO', 'NOT SET')}")
    print(f"ROS_DOMAIN_ID: {os.environ.get('ROS_DOMAIN_ID', 'NOT SET')}")
    print(f"PYTHONPATH: {os.environ.get('PYTHONPATH', 'NOT SET')}")
    print(f"工作目录: {os.getcwd()}")
    print(f"Python版本: {sys.version}")
    print()

def test_imports():
    """测试关键导入"""
    print("🔍 测试关键导入...")
    
    try:
        import rclpy
        print("✅ rclpy 导入成功")
    except ImportError as e:
        print(f"❌ rclpy 导入失败: {e}")
        return False
    
    try:
        from homi_speech_interface.srv import NetCtrl, SIGCData
        print("✅ homi_speech_interface 导入成功")
    except ImportError as e:
        print(f"❌ homi_speech_interface 导入失败: {e}")
        return False
    
    try:
        # 添加src目录到Python路径
        src_path = os.path.join(os.getcwd(), 'xiaoli_application_ros2', 'src')
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        from network.network_manager import NetworkManager
        from network.cellular_control import CellularModuleController
        print("✅ network包模块导入成功")
    except ImportError as e:
        print(f"❌ network包模块导入失败: {e}")
        return False
    
    return True

def test_node_creation():
    """测试节点创建"""
    print("🔍 测试节点创建...")
    
    try:
        import rclpy
        rclpy.init()
        
        # 添加src目录到Python路径
        src_path = os.path.join(os.getcwd(), 'xiaoli_application_ros2', 'src')
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        from network.network_node import InternetStartNode
        
        print("✅ 正在创建network节点...")
        node = InternetStartNode()
        print("✅ network节点创建成功!")
        
        # 清理
        node.destroy_node()
        rclpy.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ 节点创建失败: {e}")
        return False

def main():
    print("=" * 60)
    print("🚀 Network节点调试环境测试")
    print("=" * 60)
    
    test_environment()
    
    if not test_imports():
        print("❌ 导入测试失败，请检查环境配置")
        return 1
    
    if not test_node_creation():
        print("❌ 节点创建测试失败")
        return 1
    
    print("=" * 60)
    print("🎉 所有测试通过！调试环境配置正确")
    print("💡 现在可以使用VSCode调试network节点了")
    print("=" * 60)
    return 0

if __name__ == "__main__":
    sys.exit(main())
