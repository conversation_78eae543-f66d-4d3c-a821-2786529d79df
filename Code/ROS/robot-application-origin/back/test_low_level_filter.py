#!/usr/bin/env python3
"""
低电平时间过滤测试脚本
用于测试和验证 PWM 触摸节点的低电平过滤功能
"""

import rclpy
from rclpy.node import Node
from rcl_interfaces.srv import SetParameters
from rcl_interfaces.msg import Parameter, ParameterValue, ParameterType
import time
import json

class LowLevelFilterTester(Node):
    def __init__(self):
        super().__init__('low_level_filter_tester')
        
        # 创建参数设置客户端
        self.param_client = self.create_client(
            SetParameters, 
            '/pwm_touch_node/set_parameters'
        )
        
        self.get_logger().info("低电平过滤测试器初始化完成")
    
    def set_filter_parameters(self, min_duration=None, max_duration=None):
        """设置过滤参数"""
        if not self.param_client.wait_for_service(timeout_sec=5.0):
            self.get_logger().error("无法连接到参数服务")
            return False
        
        request = SetParameters.Request()
        
        if min_duration is not None:
            param = Parameter()
            param.name = 'min_low_level_duration'
            param.value = ParameterValue()
            param.value.type = ParameterType.PARAMETER_DOUBLE
            param.value.double_value = float(min_duration)
            request.parameters.append(param)
        
        if max_duration is not None:
            param = Parameter()
            param.name = 'max_low_level_duration'
            param.value = ParameterValue()
            param.value.type = ParameterType.PARAMETER_DOUBLE
            param.value.double_value = float(max_duration)
            request.parameters.append(param)
        
        future = self.param_client.call_async(request)
        rclpy.spin_until_future_complete(self, future)
        
        if future.result() is not None:
            result = future.result()
            if result.results[0].successful:
                self.get_logger().info("参数设置成功")
                return True
            else:
                self.get_logger().error(f"参数设置失败: {result.results[0].reason}")
                return False
        else:
            self.get_logger().error("参数设置请求失败")
            return False
    
    def run_test_scenarios(self):
        """运行测试场景"""
        self.get_logger().info("开始运行低电平过滤测试场景")
        
        # 测试场景1: 设置较短的最小持续时间 (20ms)
        self.get_logger().info("=== 测试场景1: 设置最小持续时间为20ms ===")
        if self.set_filter_parameters(min_duration=0.02):
            self.get_logger().info("请进行快速触摸测试，观察过滤效果")
            time.sleep(10)
        
        # 测试场景2: 设置较长的最小持续时间 (100ms)
        self.get_logger().info("=== 测试场景2: 设置最小持续时间为100ms ===")
        if self.set_filter_parameters(min_duration=0.1):
            self.get_logger().info("请进行快速触摸测试，观察过滤效果")
            time.sleep(10)
        
        # 测试场景3: 设置正常的最小持续时间 (50ms)
        self.get_logger().info("=== 测试场景3: 恢复默认最小持续时间50ms ===")
        if self.set_filter_parameters(min_duration=0.05):
            self.get_logger().info("请进行正常触摸测试")
            time.sleep(10)
        
        # 测试场景4: 测试最大持续时间限制
        self.get_logger().info("=== 测试场景4: 设置较短的最大持续时间2秒 ===")
        if self.set_filter_parameters(max_duration=2.0):
            self.get_logger().info("请进行长时间按压测试，观察异常检测")
            time.sleep(15)
        
        # 恢复默认设置
        self.get_logger().info("=== 恢复默认设置 ===")
        self.set_filter_parameters(min_duration=0.05, max_duration=5.0)
        
        self.get_logger().info("测试完成")

def main():
    rclpy.init()
    
    tester = LowLevelFilterTester()
    
    try:
        # 等待一下让系统稳定
        time.sleep(2)
        
        # 运行测试
        tester.run_test_scenarios()
        
    except KeyboardInterrupt:
        tester.get_logger().info("测试被用户中断")
    finally:
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
