#!/bin/bash

# xiaoli_application_ros2 专用构建脚本
# 只编译 xiaoli_application_ros2/src 下的包，忽略所有其他包

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认参数
BUILD_TYPE="Debug"
PACKAGES=""
ARCH=$(uname -m)
CLEAN=false
VERBOSE=false
JOBS=$(nproc)

# 显示帮助信息
function show_help() {
    echo -e "${BLUE}xiaoli_application_ros2 专用构建脚本${NC}"
    echo "用法: $0 [选项] [包名...]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -r, --release  使用Release模式编译 (默认: Debug)"
    echo "  -c, --clean    清理后重新编译"
    echo "  -v, --verbose  显示详细编译信息"
    echo "  -j, --jobs N   使用N个并行任务编译 (默认: $(nproc))"
    echo "  -l, --list     列出所有可用包"
    echo ""
    echo "示例:"
    echo "  $0                    # 编译所有xiaoli包"
    echo "  $0 network            # 只编译network包"
    echo "  $0 network peripherals # 编译network和peripherals包"
    echo "  $0 -r --clean         # 清理后用Release模式编译"
}

# 检查xiaoli_application_ros2目录
if [ ! -d "xiaoli_application_ros2/src" ]; then
    echo -e "${RED}错误: 当前目录下未找到 xiaoli_application_ros2/src${NC}"
    echo -e "${YELLOW}请在包含 xiaoli_application_ros2 的根目录中运行此脚本${NC}"
    exit 1
fi

# 列出可用包
function list_packages() {
    echo -e "${BLUE}xiaoli_application_ros2 可用包:${NC}"
    find xiaoli_application_ros2/src -name "package.xml" -exec dirname {} \; | sed 's|xiaoli_application_ros2/src/||' | sort | while read pkg; do
        if [ -f "xiaoli_application_ros2/src/$pkg/package.xml" ]; then
            desc=$(grep -o '<description>[^<]*</description>' "xiaoli_application_ros2/src/$pkg/package.xml" 2>/dev/null | sed 's/<[^>]*>//g' | head -1)
            if [ -n "$desc" ] && [ "$desc" != "TODO: Package description" ]; then
                echo -e "  ${GREEN}$pkg${NC} - $desc"
            else
                echo -e "  ${GREEN}$pkg${NC}"
            fi
        fi
    done
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -l|--list)
            list_packages
            exit 0
            ;;
        -r|--release)
            BUILD_TYPE="Release"
            shift
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -j|--jobs)
            JOBS="$2"
            shift 2
            ;;
        *)
            PACKAGES="$PACKAGES $1"
            shift
            ;;
    esac
done

# 设置ROS2环境
if [ -z "$ROS_DISTRO" ]; then
    echo -e "${BLUE}=== 设置 ROS2 环境 ===${NC}"
    ROS2_PATHS=("/opt/ros/humble" "/opt/ros/galactic" "/opt/ros/foxy")
    
    for ros_dir in "${ROS2_PATHS[@]}"; do
        if [ -d "$ros_dir" ] && [ -f "$ros_dir/setup.bash" ]; then
            echo -e "${YELLOW}找到 ROS2: $ros_dir${NC}"
            source "$ros_dir/setup.bash"
            break
        fi
    done
    
    if [ -z "$ROS_DISTRO" ]; then
        echo -e "${RED}错误: 未找到 ROS2 环境${NC}"
        exit 1
    fi
fi

# 设置构建目录
WORKSPACE_DIR=$(pwd)
BUILD_DIR="${WORKSPACE_DIR}/build/${ARCH}/${BUILD_TYPE}"
INSTALL_DIR="${WORKSPACE_DIR}/install/${ARCH}/${BUILD_TYPE}"

# 显示配置信息
echo -e "${BLUE}=== xiaoli_application_ros2 专用构建 ===${NC}"
echo -e "ROS2 发行版: ${GREEN}${ROS_DISTRO}${NC}"
echo -e "源码目录: ${GREEN}xiaoli_application_ros2/src${NC}"
echo -e "构建类型: ${GREEN}${BUILD_TYPE}${NC}"
echo -e "架构: ${GREEN}${ARCH}${NC}"
echo -e "构建目录: ${GREEN}${BUILD_DIR}${NC}"
echo -e "安装目录: ${GREEN}${INSTALL_DIR}${NC}"
if [ -n "$PACKAGES" ]; then
    echo -e "编译包: ${GREEN}${PACKAGES}${NC}"
else
    echo -e "编译包: ${GREEN}所有xiaoli包${NC}"
fi
echo -e "并行任务: ${GREEN}${JOBS}${NC}"
echo ""

# 验证包名
if [ -n "$PACKAGES" ]; then
    echo -e "${BLUE}=== 验证包名 ===${NC}"
    for pkg in $PACKAGES; do
        if [ ! -d "xiaoli_application_ros2/src/$pkg" ]; then
            echo -e "${RED}错误: 包 '$pkg' 在 xiaoli_application_ros2/src 中不存在${NC}"
            echo -e "${YELLOW}可用的包:${NC}"
            list_packages
            exit 1
        else
            echo -e "${GREEN}✓ 找到包: $pkg${NC}"
        fi
    done
fi

# 清理构建目录
if [ "$CLEAN" = true ]; then
    echo -e "${BLUE}=== 清理构建目录 ===${NC}"
    rm -rf "${BUILD_DIR}" "${INSTALL_DIR}"
    echo -e "${GREEN}构建目录已清理${NC}"
fi

# 创建构建目录
mkdir -p "${BUILD_DIR}" "${INSTALL_DIR}"

# 构建命令
BUILD_CMD="colcon build"
BUILD_CMD+=" --build-base ${BUILD_DIR}"
BUILD_CMD+=" --install-base ${INSTALL_DIR}"
BUILD_CMD+=" --cmake-args -DCMAKE_BUILD_TYPE=${BUILD_TYPE} -DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
BUILD_CMD+=" --parallel-workers ${JOBS}"

# 临时切换到xiaoli_application_ros2目录进行构建
ORIGINAL_DIR=$(pwd)
cd xiaoli_application_ros2

# 如果指定了包名，则只构建指定的包
if [ -n "$PACKAGES" ]; then
    BUILD_CMD+=" --packages-select $PACKAGES"
fi

# 是否显示详细信息
if [ "$VERBOSE" = true ]; then
    BUILD_CMD+=" --event-handlers console_direct+"
fi

# 执行构建
echo -e "${BLUE}=== 开始构建 ===${NC}"
echo -e "执行命令: ${GREEN}${BUILD_CMD}${NC}"
echo ""

eval ${BUILD_CMD}
BUILD_RESULT=$?

# 切换回原目录
cd "$ORIGINAL_DIR"

# 检查构建结果
if [ $BUILD_RESULT -eq 0 ]; then
    echo -e "${GREEN}构建成功!${NC}"
    
    # 创建快速启动脚本
    echo -e "${BLUE}=== 创建启动脚本 ===${NC}"
    
    # 环境设置脚本
    cat > setup_xiaoli_env.sh << EOF
#!/bin/bash
source ${INSTALL_DIR}/setup.bash
echo "xiaoli_application_ros2 环境已设置 (${BUILD_TYPE}/${ARCH})"
echo "可用命令示例:"
echo "  ros2 run network network_node"
echo "  ros2 run peripherals peripherals_node"
EOF
    chmod +x setup_xiaoli_env.sh
    
    # 网络节点启动脚本
    if [ -d "xiaoli_application_ros2/src/network" ]; then
        cat > run_xiaoli_network.sh << EOF
#!/bin/bash
source ${INSTALL_DIR}/setup.bash
ros2 run network network_node
EOF
        chmod +x run_xiaoli_network.sh
    fi
    
    # 外设节点启动脚本
    if [ -d "xiaoli_application_ros2/src/peripherals" ]; then
        cat > run_xiaoli_peripherals.sh << EOF
#!/bin/bash
source ${INSTALL_DIR}/setup.bash
ros2 run peripherals peripherals_node
EOF
        chmod +x run_xiaoli_peripherals.sh
    fi
    
    echo -e "${GREEN}启动脚本已创建${NC}"
    
    # 显示使用说明
    echo ""
    echo -e "${BLUE}=== 使用说明 ===${NC}"
    echo -e "环境设置: ${GREEN}source ./setup_xiaoli_env.sh${NC}"
    if [ -f "./run_xiaoli_network.sh" ]; then
        echo -e "网络节点: ${GREEN}./run_xiaoli_network.sh${NC}"
    fi
    if [ -f "./run_xiaoli_peripherals.sh" ]; then
        echo -e "外设节点: ${GREEN}./run_xiaoli_peripherals.sh${NC}"
    fi
    echo -e "手动启动: ${GREEN}source ${INSTALL_DIR}/setup.bash && ros2 run <package> <node>${NC}"
    
else
    echo -e "${RED}构建失败!${NC}"
    exit 1
fi
