#!/bin/bash

# 修复xiaoli_application_ros2中setup.py的警告问题
# 主要修复 tests_require 弃用警告

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 修复setup.py警告问题 ===${NC}"
echo ""

# 检查是否在xiaoli_application_ros2目录
if [ ! -d "src" ]; then
    echo -e "${RED}错误: 请在xiaoli_application_ros2目录中运行此脚本${NC}"
    exit 1
fi

# 查找所有包含tests_require的setup.py文件
setup_files=$(find src -name "setup.py" -exec grep -l "tests_require" {} \;)

if [ -z "$setup_files" ]; then
    echo -e "${GREEN}没有找到需要修复的setup.py文件${NC}"
    exit 0
fi

echo -e "${YELLOW}找到以下需要修复的文件:${NC}"
echo "$setup_files"
echo ""

# 备份和修复每个文件
for file in $setup_files; do
    echo -e "${BLUE}修复文件: $file${NC}"
    
    # 创建备份
    cp "$file" "$file.backup"
    
    # 使用sed进行替换
    # 1. 将 tests_require=['pytest'] 替换为 extras_require={'test': ['pytest']}
    # 2. 更新描述信息
    # 3. 更新许可证信息
    
    sed -i.tmp '
        # 替换 tests_require
        s/tests_require=\[\x27pytest\x27\]/extras_require={\x27test\x27: [\x27pytest\x27]}/g
        
        # 更新TODO描述
        s/description=\x27TODO: Package description\x27/description=\x27xiaoli应用包\x27/g
        
        # 更新TODO许可证
        s/license=\x27TODO: License declaration\x27/license=\x27Apache License 2.0\x27/g
        
        # 添加注释说明
        /extras_require=/ i\    # 使用extras_require替代弃用的tests_require
    ' "$file"
    
    # 删除临时文件
    rm -f "$file.tmp"
    
    echo -e "${GREEN}✓ 已修复: $file${NC}"
done

echo ""
echo -e "${GREEN}=== 修复完成 ===${NC}"
echo -e "${YELLOW}修复内容:${NC}"
echo "  • 将 tests_require 替换为 extras_require"
echo "  • 更新包描述信息"
echo "  • 更新许可证信息"
echo ""
echo -e "${BLUE}备份文件已保存为 .backup${NC}"
echo -e "${YELLOW}如需恢复，可以使用备份文件${NC}"

# 显示修复后的差异示例
echo ""
echo -e "${BLUE}修复示例:${NC}"
echo -e "${RED}- tests_require=['pytest'],${NC}"
echo -e "${GREEN}+ # 使用extras_require替代弃用的tests_require${NC}"
echo -e "${GREEN}+ extras_require={'test': ['pytest']},${NC}"

echo ""
echo -e "${CYAN}现在可以重新编译包，警告应该消失了:${NC}"
echo -e "${GREEN}./build_xiaoli.sh network${NC}"
