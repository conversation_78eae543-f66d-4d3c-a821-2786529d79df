#!/usr/bin/env python3
"""
检查pwm_touch_node日志的脚本
帮助诊断上升沿检测但未发布的问题
"""

import subprocess
import time
import re
import threading
from collections import defaultdict

class LogMonitor:
    def __init__(self):
        self.rising_detected = 0
        self.rising_published = 0
        self.falling_detected = 0
        self.falling_published = 0
        self.filtered_edges = 0
        
        self.log_patterns = {
            'rising_valid': re.compile(r'GPIO (\d+) 上升沿有效.*持续 ([\d.]+)s'),
            'rising_filtered': re.compile(r'GPIO (\d+) 上升沿被过滤.*持续 ([\d.]+)s'),
            'falling_detected': re.compile(r'检测到GPIO (\d+) 下降沿'),
            'rising_publish': re.compile(r'检测到GPIO (\d+) rising沿，准备发布消息'),
            'falling_publish': re.compile(r'检测到GPIO (\d+) falling沿，准备发布消息'),
            'threshold_reached': re.compile(r'GPIO (\d+) 高电平持续时间达到阈值'),
        }
        
        self.pin_stats = defaultdict(lambda: {
            'rising_valid': 0,
            'rising_filtered': 0,
            'rising_published': 0,
            'falling_detected': 0,
            'falling_published': 0
        })
        
    def parse_log_line(self, line):
        """解析日志行"""
        for pattern_name, pattern in self.log_patterns.items():
            match = pattern.search(line)
            if match:
                pin = int(match.group(1)) if match.group(1).isdigit() else 0
                
                if pattern_name == 'rising_valid':
                    self.rising_detected += 1
                    self.pin_stats[pin]['rising_valid'] += 1
                    duration = float(match.group(2))
                    print(f"✅ GPIO {pin} 上升沿有效 (持续 {duration:.3f}s)")
                    
                elif pattern_name == 'rising_filtered':
                    self.filtered_edges += 1
                    duration = float(match.group(2))
                    print(f"🚫 GPIO {pin} 上升沿被过滤 (持续 {duration:.3f}s)")
                    
                elif pattern_name == 'falling_detected':
                    self.falling_detected += 1
                    self.pin_stats[pin]['falling_detected'] += 1
                    print(f"⬇️ GPIO {pin} 下降沿检测")
                    
                elif pattern_name == 'rising_publish':
                    self.rising_published += 1
                    self.pin_stats[pin]['rising_published'] += 1
                    print(f"📤 GPIO {pin} 上升沿准备发布")
                    
                elif pattern_name == 'falling_publish':
                    self.falling_published += 1
                    self.pin_stats[pin]['falling_published'] += 1
                    print(f"📤 GPIO {pin} 下降沿准备发布")
                    
                elif pattern_name == 'threshold_reached':
                    print(f"⏰ GPIO {pin} 高电平持续时间达到阈值")
                
                break
    
    def print_summary(self):
        """打印统计摘要"""
        print(f"\n📊 日志分析摘要:")
        print(f"   上升沿有效检测: {self.rising_detected}")
        print(f"   上升沿准备发布: {self.rising_published}")
        print(f"   下降沿检测: {self.falling_detected}")
        print(f"   下降沿准备发布: {self.falling_published}")
        print(f"   被过滤的边沿: {self.filtered_edges}")
        
        # 检查问题
        if self.rising_detected > self.rising_published:
            missing = self.rising_detected - self.rising_published
            print(f"⚠️ 发现问题: {missing} 个有效上升沿未发布!")
            
        if self.falling_detected > self.falling_published:
            missing = self.falling_detected - self.falling_published
            print(f"⚠️ 发现问题: {missing} 个下降沿未发布!")
        
        # 按引脚统计
        if self.pin_stats:
            print(f"\n📌 按引脚统计:")
            for pin, stats in self.pin_stats.items():
                print(f"   GPIO {pin}:")
                print(f"     上升沿有效: {stats['rising_valid']}")
                print(f"     上升沿发布: {stats['rising_published']}")
                print(f"     下降沿检测: {stats['falling_detected']}")
                print(f"     下降沿发布: {stats['falling_published']}")

def monitor_logs():
    """监控pwm_touch_node的日志"""
    print("🔍 开始监控pwm_touch_node日志...")
    print("📝 请在另一个终端触摸触摸板进行测试")
    print("⏹️ 按 Ctrl+C 停止监控\n")
    
    monitor = LogMonitor()
    
    try:
        # 启动ros2 log监控
        cmd = ["ros2", "topic", "echo", "/rosout"]
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            bufsize=1
        )
        
        start_time = time.time()
        
        for line in iter(process.stdout.readline, ''):
            if line.strip():
                # 只处理pwm_touch_node的日志
                if 'pwm_touch_node' in line or 'GPIO' in line:
                    monitor.parse_log_line(line)
            
            # 每10秒显示一次状态
            if time.time() - start_time > 10:
                print(f"\n⏱️ 监控中... (有效上升沿:{monitor.rising_detected}, 发布上升沿:{monitor.rising_published})")
                start_time = time.time()
        
    except KeyboardInterrupt:
        print("\n🛑 监控被用户中断")
    except Exception as e:
        print(f"❌ 监控异常: {e}")
    finally:
        if 'process' in locals():
            process.terminate()
        
        monitor.print_summary()

def check_node_running():
    """检查pwm_touch_node是否在运行"""
    try:
        result = subprocess.run(
            ["ros2", "node", "list"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if "/pwm_touch_node" in result.stdout:
            print("✅ pwm_touch_node 正在运行")
            return True
        else:
            print("❌ pwm_touch_node 未运行")
            print("💡 请先启动: ros2 run pwm_touch pwm_touch_node")
            return False
            
    except Exception as e:
        print(f"❌ 检查节点状态失败: {e}")
        return False

def main():
    print("🔧 PWM触摸节点日志检查工具")
    print("=" * 50)
    
    # 检查节点是否运行
    if not check_node_running():
        return
    
    # 开始监控
    monitor_logs()

if __name__ == '__main__':
    main()
