#!/usr/bin/env python3

"""
network节点直接调试启动脚本
解决相对导入问题，支持VSCode断点调试
"""

import sys
import os

# 添加包路径到sys.path，确保相对导入正常工作
current_dir = os.path.dirname(os.path.abspath(__file__))
network_src_dir = os.path.join(current_dir, 'xiaoli_application_ros2', 'src', 'network')
sys.path.insert(0, network_src_dir)

# 设置ROS2和工作空间环境
install_dir = os.path.join(current_dir, 'install', 'x86_64', 'Debug')
if os.path.exists(install_dir):
    # 添加Python包路径
    python_paths = []
    for pkg in ['network', 'homi_speech_interface', 'mock']:
        pkg_python_path = os.path.join(install_dir, pkg, 'lib', 'python3.10', 'site-packages')
        if os.path.exists(pkg_python_path):
            python_paths.append(pkg_python_path)
        # 检查local路径（接口包）
        pkg_local_path = os.path.join(install_dir, pkg, 'local', 'lib', 'python3.10', 'dist-packages')
        if os.path.exists(pkg_local_path):
            python_paths.append(pkg_local_path)

    # 添加ROS2系统路径
    ros_python_paths = [
        '/opt/ros/humble/lib/python3.10/site-packages',
        '/opt/ros/humble/local/lib/python3.10/dist-packages'
    ]

    # 设置PYTHONPATH
    current_pythonpath = os.environ.get('PYTHONPATH', '')
    all_paths = python_paths + ros_python_paths + ([current_pythonpath] if current_pythonpath else [])
    os.environ['PYTHONPATH'] = ':'.join(all_paths)

    # 添加到sys.path
    for path in python_paths + ros_python_paths:
        if path not in sys.path:
            sys.path.insert(0, path)

# 设置环境变量
os.environ.setdefault('ROS_DOMAIN_ID', '0')
os.environ.setdefault('RCUTILS_LOGGING_BUFFERED_STREAM', '1')
os.environ.setdefault('RCUTILS_CONSOLE_OUTPUT_FORMAT', '[{severity}] [{time}] [{name}]: {message}')

def main():
    """主函数"""
    try:
        # 导入并运行network节点
        from network.network_node import main as network_main
        
        print("🚀 启动network节点调试...")
        print(f"📁 工作目录: {os.getcwd()}")
        print(f"🐍 Python路径: {sys.path[0]}")
        print(f"🌐 ROS域ID: {os.environ.get('ROS_DOMAIN_ID', 'None')}")
        print("=" * 50)
        
        # 启动节点
        network_main()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保network包已正确编译")
        print("🔧 运行命令: ./xiaoli_application_ros2/run_network_node.sh --build-only")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
