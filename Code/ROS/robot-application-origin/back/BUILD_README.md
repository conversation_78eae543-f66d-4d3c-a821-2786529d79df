# ROS2 构建脚本使用指南

## 概述

本项目提供了两个构建脚本来支持不同类型的ROS2工作空间：

- **`build.sh`** - 通用ROS2构建脚本，支持标准和xiaoli_application_ros2工作空间
- **`build_xiaoli.sh`** - 专门为xiaoli_application_ros2优化的快速构建脚本

## 工作空间类型

### 标准ROS2工作空间
```
workspace/
├── src/
│   ├── package1/
│   ├── package2/
│   └── ...
├── build.sh
└── ...
```

### xiaoli_application_ros2工作空间
```
workspace/
├── xiaoli_application_ros2/
│   └── src/
│       ├── network/
│       ├── peripherals/
│       ├── speech/
│       ├── vision/
│       └── ...
├── build.sh
├── build_xiaoli.sh
└── ...
```

## ROS2专用构建

**默认情况下，构建脚本只编译ROS2包，自动排除ROS1包和备份文件。**

### 快速开始 (仅ROS2)

```bash
# 1. 修复重复包问题 (如果存在)
./fix_duplicate_packages.sh

# 2. 编译所有xiaoli ROS2包
./build_xiaoli.sh

# 3. 编译特定ROS2包
./build_xiaoli.sh network peripherals

# 4. Release模式编译
./build_xiaoli.sh --release --clean
```

## 使用方法

### 1. 通用构建脚本 (build.sh)

#### 基本用法
```bash
# 编译所有包
./build.sh

# 编译指定包
./build.sh network peripherals

# Release模式编译
./build.sh --release

# 清理后重新编译
./build.sh --clean

# 显示详细信息
./build.sh --verbose
```

#### 高级选项
```bash
# 列出所有可用包
./build.sh --list-packages

# 指定工作空间类型
./build.sh --workspace-type xiaoli

# 更新依赖
./build.sh --update-deps

# 使用多线程编译
./build.sh --jobs 8

# 跳过ROS2环境设置
./build.sh --skip-ros-setup
```

### 2. xiaoli专用构建脚本 (build_xiaoli.sh)

#### 基本用法
```bash
# 编译所有xiaoli节点
./build_xiaoli.sh

# 编译指定节点
./build_xiaoli.sh network
./build_xiaoli.sh network peripherals

# Release模式编译
./build_xiaoli.sh --release network

# 清理后重新编译
./build_xiaoli.sh --clean --release
```

#### 查看可用节点
```bash
# 列出所有可用节点
./build_xiaoli.sh --list
```

## 常用xiaoli_application_ros2节点

| 节点名 | 功能描述 |
|--------|----------|
| network | 网络管理节点 - 处理WiFi、蜂窝网络等 |
| peripherals | 外设控制节点 - 控制RGB灯、触摸传感器等 |
| speech | 语音处理节点 - TTS、语音识别等 |
| vision | 视觉处理节点 - 图像处理、目标检测等 |

## 构建输出

构建完成后会生成以下文件和目录：

```
workspace/
├── build/           # 构建临时文件
│   └── x86_64/     # 按架构分类
│       ├── Debug/
│       └── Release/
├── install/         # 安装文件
│   └── x86_64/     # 按架构分类
│       ├── Debug/
│       └── Release/
├── setup_env.sh     # 环境设置脚本
├── run_network.sh   # 网络节点启动脚本 (xiaoli)
├── run_peripherals.sh # 外设节点启动脚本 (xiaoli)
└── ...
```

## 快速启动

### 方法1: 使用生成的启动脚本
```bash
# 设置环境
source ./setup_env.sh

# 启动特定节点 (xiaoli)
./run_network.sh
./run_peripherals.sh
```

### 方法2: 手动启动
```bash
# 设置环境
source install/x86_64/Debug/setup.bash

# 启动节点
ros2 run network network_node
ros2 run peripherals peripherals_node
```

## 开发工具支持

### VSCode集成
构建脚本会自动：
- 生成 `compile_commands.json` 供clangd使用
- 更新 `.vscode/launch.json` 中的路径
- 支持代码补全和调试

### clangd支持
```bash
# 启用clangd支持 (默认启用)
./build.sh

# 禁用clangd支持
./build.sh --no-clangd
```

## 故障排除

### 常见问题

1. **重复包名错误**
   ```bash
   # 错误信息: "Duplicate package names not supported"
   # 快速修复
   ./fix_duplicate_packages.sh

   # 或使用清理脚本
   ./clean_workspace.sh --all

   # 或在构建时自动清理
   ./build.sh --clean-workspace
   ```

2. **ROS2环境未设置**
   ```bash
   # 手动设置ROS2环境
   source /opt/ros/humble/setup.bash

   # 或跳过自动设置
   ./build.sh --skip-ros-setup
   ```

3. **包不存在错误**
   ```bash
   # 列出可用包
   ./build.sh --list-packages
   ```

4. **依赖问题**
   ```bash
   # 更新依赖
   ./build.sh --update-deps
   ```

5. **构建失败**
   ```bash
   # 清理后重新构建
   ./build.sh --clean --verbose
   ```

### 重复包问题详解

当工作空间中存在以下情况时会出现重复包错误：
- `_gsdata_` 备份目录
- `deeprobots_application_ros1` ROS1包
- 多个版本的同一个包

**解决方案:**
1. **快速修复** (推荐)
   ```bash
   ./fix_duplicate_packages.sh
   ```

2. **手动清理**
   ```bash
   # 删除备份目录
   rm -rf _gsdata_

   # 删除ROS1目录
   rm -rf deeprobots_application_ros1
   rm -rf deeprob_ws_ctrl

   # 清理构建缓存
   rm -rf build install log
   ```

3. **使用清理脚本**
   ```bash
   # 预览将要删除的文件
   ./clean_workspace.sh --dry-run

   # 执行清理
   ./clean_workspace.sh --all
   ```

### 调试模式
```bash
# 启用详细输出
./build.sh --verbose

# 仅编译不安装
./build.sh --no-install
```

## 性能优化

```bash
# 使用多线程编译 (默认使用所有CPU核心)
./build.sh --jobs $(nproc)

# Release模式编译 (性能优化)
./build.sh --release
```

## 环境要求

- ROS2 (Humble/Galactic/Foxy等)
- colcon构建工具
- 适当的编译器 (gcc/clang)
- Python 3.x

## 注意事项

1. 确保在正确的工作空间根目录运行脚本
2. 首次构建可能需要较长时间下载依赖
3. 建议使用Release模式进行最终部署
4. 定期清理构建目录以节省磁盘空间
