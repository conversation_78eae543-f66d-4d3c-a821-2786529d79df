#!/usr/bin/env python3
"""
颜色转换测试脚本
用于测试和调试颜色显示问题
"""

class Color:
    def __init__(self, r=0, g=0, b=0):
        self.r = r
        self.g = g
        self.b = b

    @staticmethod
    def from_hex(hex_color):
        """从十六进制颜色代码创建Color对象"""
        hex_color = hex_color.lstrip('#')
        return Color(
            int(hex_color[0:2], 16),
            int(hex_color[2:4], 16),
            int(hex_color[4:6], 16)
        )

    def to_duty_cycle(self, max_duty):
        """将RGB值（0-255）转换为PWM占空比"""
        return (
            int(self.r / 255 * max_duty),
            int(self.g / 255 * max_duty),
            int(self.b / 255 * max_duty)
        )
    
    def __str__(self):
        return f"RGB({self.r}, {self.g}, {self.b})"

def test_colors():
    """测试各种颜色的转换"""
    test_colors = {
        "红色": "#FF0000",
        "绿色": "#00FF00", 
        "蓝色": "#0000FF",
        "白色": "#FFFFFF",
        "黑色": "#000000",
        "黄色": "#FFFF00",
        "青色": "#00FFFF",
        "品红": "#FF00FF",
        "橙色": "#FFA500",
        "粉色": "#FFC0CB",
        "紫色": "#800080",
        "紫罗兰": "#8A2BE2",
        "深紫色": "#4B0082",
        "浅紫色": "#DDA0DD",
        "橙红色": "#FF4500",
        "深橙色": "#FF8C00",
        "金色": "#FFD700",
        "玫瑰红": "#FF1493"
    }
    
    max_duty = 10000  # PWM周期
    
    print("颜色转换测试结果:")
    print("=" * 80)
    print(f"{'颜色名称':<10} {'十六进制':<10} {'RGB值':<20} {'PWM占空比':<30}")
    print("-" * 80)
    
    for name, hex_color in test_colors.items():
        color = Color.from_hex(hex_color)
        r_duty, g_duty, b_duty = color.to_duty_cycle(max_duty)
        
        print(f"{name:<10} {hex_color:<10} {str(color):<20} R:{r_duty} G:{g_duty} B:{b_duty}")
        
        # 检查是否有异常的占空比值
        if r_duty == 0 and g_duty == 0 and b_duty == 0 and hex_color != "#000000":
            print(f"  ⚠️  警告: {name} 转换后所有通道都为0!")
        elif max(r_duty, g_duty, b_duty) == max_duty and min(r_duty, g_duty, b_duty) == 0:
            print(f"  ✓  正常: {name} 是纯色")
        else:
            print(f"  ℹ️  混合色: {name}")

def test_specific_problematic_colors():
    """测试特定的问题颜色"""
    print("\n\n特定问题颜色测试:")
    print("=" * 50)
    
    problematic_colors = {
        "橙色": "#FFA500",
        "粉色": "#FFC0CB", 
        "紫色": "#800080",
        "深紫色": "#4B0082",
        "浅紫色": "#DDA0DD"
    }
    
    max_duty = 10000
    
    for name, hex_color in problematic_colors.items():
        color = Color.from_hex(hex_color)
        r_duty, g_duty, b_duty = color.to_duty_cycle(max_duty)
        
        print(f"\n{name} ({hex_color}):")
        print(f"  原始RGB: R={color.r}, G={color.g}, B={color.b}")
        print(f"  PWM占空比: R={r_duty}, G={g_duty}, B={b_duty}")
        print(f"  占空比百分比: R={r_duty/max_duty*100:.1f}%, G={g_duty/max_duty*100:.1f}%, B={b_duty/max_duty*100:.1f}%")
        
        # 分析可能的问题
        if r_duty > 0 and g_duty > 0 and b_duty == 0:
            print(f"  分析: 这是红绿混合色(黄色系)，蓝色通道为0")
        elif r_duty > 0 and g_duty == 0 and b_duty > 0:
            print(f"  分析: 这是红蓝混合色(品红系)，绿色通道为0")
        elif r_duty == 0 and g_duty > 0 and b_duty > 0:
            print(f"  分析: 这是绿蓝混合色(青色系)，红色通道为0")
        elif r_duty > 0 and g_duty > 0 and b_duty > 0:
            print(f"  分析: 这是三色混合，可能需要检查硬件连接或PWM极性")

def analyze_pwm_issues():
    """分析可能的PWM问题"""
    print("\n\nPWM问题分析:")
    print("=" * 50)
    
    print("可能的问题原因:")
    print("1. PWM通道映射错误 - RGB通道可能接错了")
    print("2. PWM极性设置错误 - 可能需要反向极性")
    print("3. 硬件连接问题 - LED驱动电路可能有问题")
    print("4. 占空比计算问题 - 线性转换可能不适合LED特性")
    print("5. 颜色空间问题 - sRGB到硬件RGB的转换")
    
    print("\n建议的调试步骤:")
    print("1. 测试单色 - 分别测试纯红、纯绿、纯蓝")
    print("2. 检查PWM通道映射 - 确认R、G、B通道对应关系")
    print("3. 测试PWM极性 - 尝试反向极性设置")
    print("4. 检查硬件连接 - 用万用表测量PWM输出")
    print("5. 尝试伽马校正 - 应用非线性颜色校正")

def gamma_correction_test():
    """测试伽马校正"""
    print("\n\n伽马校正测试:")
    print("=" * 50)
    
    def apply_gamma(value, gamma=2.2):
        """应用伽马校正"""
        return int(255 * ((value / 255) ** (1/gamma)))
    
    test_colors = ["#FFA500", "#FFC0CB", "#800080"]  # 橙色、粉色、紫色
    max_duty = 10000
    
    for hex_color in test_colors:
        color = Color.from_hex(hex_color)
        
        # 原始转换
        r_duty, g_duty, b_duty = color.to_duty_cycle(max_duty)
        
        # 伽马校正后转换
        gamma_r = apply_gamma(color.r)
        gamma_g = apply_gamma(color.g)
        gamma_b = apply_gamma(color.b)
        gamma_color = Color(gamma_r, gamma_g, gamma_b)
        gamma_r_duty, gamma_g_duty, gamma_b_duty = gamma_color.to_duty_cycle(max_duty)
        
        print(f"\n颜色: {hex_color}")
        print(f"原始: R={r_duty}, G={g_duty}, B={b_duty}")
        print(f"伽马校正: R={gamma_r_duty}, G={gamma_g_duty}, B={gamma_b_duty}")

if __name__ == "__main__":
    test_colors()
    test_specific_problematic_colors()
    analyze_pwm_issues()
    gamma_correction_test()
