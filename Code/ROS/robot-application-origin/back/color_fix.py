#!/usr/bin/env python3
"""
颜色修复脚本
提供多种颜色显示问题的修复方案
"""

import math

class ColorFixer:
    """颜色修复工具类"""
    
    def __init__(self):
        self.gamma = 2.2  # 默认伽马值
        self.channel_mapping = [0, 1, 2]  # 默认RGB通道映射
        self.invert_channels = [False, False, False]  # 通道反转设置
    
    def set_gamma(self, gamma):
        """设置伽马校正值"""
        self.gamma = gamma
    
    def set_channel_mapping(self, mapping):
        """设置通道映射 [R_index, G_index, B_index]"""
        self.channel_mapping = mapping
    
    def set_channel_inversion(self, inversions):
        """设置通道反转 [R_invert, G_invert, B_invert]"""
        self.invert_channels = inversions
    
    def apply_gamma_correction(self, r, g, b):
        """应用伽马校正"""
        def gamma_correct(value):
            return int(255 * ((value / 255) ** (1/self.gamma)))
        
        return gamma_correct(r), gamma_correct(g), gamma_correct(b)
    
    def apply_channel_mapping(self, r, g, b):
        """应用通道映射"""
        rgb = [r, g, b]
        return rgb[self.channel_mapping[0]], rgb[self.channel_mapping[1]], rgb[self.channel_mapping[2]]
    
    def apply_channel_inversion(self, r, g, b, max_value=255):
        """应用通道反转"""
        new_r = (max_value - r) if self.invert_channels[0] else r
        new_g = (max_value - g) if self.invert_channels[1] else g  
        new_b = (max_value - b) if self.invert_channels[2] else b
        return new_r, new_g, new_b
    
    def fix_color(self, hex_color):
        """修复颜色"""
        # 解析十六进制颜色
        hex_color = hex_color.lstrip('#')
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
        
        # 应用伽马校正
        r, g, b = self.apply_gamma_correction(r, g, b)
        
        # 应用通道反转
        r, g, b = self.apply_channel_inversion(r, g, b)
        
        # 应用通道映射
        r, g, b = self.apply_channel_mapping(r, g, b)
        
        # 转换回十六进制
        return f"#{r:02X}{g:02X}{b:02X}"

def generate_color_fixes():
    """生成各种颜色修复方案"""
    
    # 测试颜色
    test_colors = {
        "橙色": "#FFA500",
        "粉色": "#FFC0CB", 
        "紫色": "#800080",
        "深紫色": "#4B0082",
        "浅紫色": "#DDA0DD"
    }
    
    print("颜色修复方案生成器")
    print("="*80)
    
    # 方案1: 伽马校正
    print("\n方案1: 伽马校正")
    print("-" * 40)
    fixer1 = ColorFixer()
    fixer1.set_gamma(2.2)
    
    for name, color in test_colors.items():
        fixed_color = fixer1.fix_color(color)
        print(f"{name:<10} {color} -> {fixed_color}")
    
    # 方案2: 通道映射修复 (BGR)
    print("\n方案2: 通道映射修复 (BGR顺序)")
    print("-" * 40)
    fixer2 = ColorFixer()
    fixer2.set_channel_mapping([2, 1, 0])  # BGR
    
    for name, color in test_colors.items():
        fixed_color = fixer2.fix_color(color)
        print(f"{name:<10} {color} -> {fixed_color}")
    
    # 方案3: 通道映射修复 (GRB)
    print("\n方案3: 通道映射修复 (GRB顺序)")
    print("-" * 40)
    fixer3 = ColorFixer()
    fixer3.set_channel_mapping([1, 0, 2])  # GRB
    
    for name, color in test_colors.items():
        fixed_color = fixer3.fix_color(color)
        print(f"{name:<10} {color} -> {fixed_color}")
    
    # 方案4: 通道反转
    print("\n方案4: 通道反转 (反向PWM)")
    print("-" * 40)
    fixer4 = ColorFixer()
    fixer4.set_channel_inversion([True, True, True])
    
    for name, color in test_colors.items():
        fixed_color = fixer4.fix_color(color)
        print(f"{name:<10} {color} -> {fixed_color}")
    
    # 方案5: 组合修复 (伽马校正 + BGR)
    print("\n方案5: 组合修复 (伽马校正 + BGR)")
    print("-" * 40)
    fixer5 = ColorFixer()
    fixer5.set_gamma(2.2)
    fixer5.set_channel_mapping([2, 1, 0])
    
    for name, color in test_colors.items():
        fixed_color = fixer5.fix_color(color)
        print(f"{name:<10} {color} -> {fixed_color}")

def generate_pwm_config_suggestions():
    """生成PWM配置建议"""
    print("\n\nPWM配置修复建议")
    print("="*80)
    
    print("当前配置:")
    print("pwm_gpio_map = 'pwmchip6:0,pwmchip2:0,pwmchip3:0'")
    print("(对应 R, G, B 通道)")
    
    print("\n可能的修复配置:")
    
    # BGR配置
    print("\n1. BGR通道顺序:")
    print("pwm_gpio_map = 'pwmchip3:0,pwmchip2:0,pwmchip6:0'")
    
    # GRB配置  
    print("\n2. GRB通道顺序:")
    print("pwm_gpio_map = 'pwmchip2:0,pwmchip6:0,pwmchip3:0'")
    
    # RBG配置
    print("\n3. RBG通道顺序:")
    print("pwm_gpio_map = 'pwmchip6:0,pwmchip3:0,pwmchip2:0'")
    
    # GBR配置
    print("\n4. GBR通道顺序:")
    print("pwm_gpio_map = 'pwmchip2:0,pwmchip3:0,pwmchip6:0'")
    
    # BRG配置
    print("\n5. BRG通道顺序:")
    print("pwm_gpio_map = 'pwmchip3:0,pwmchip6:0,pwmchip2:0'")

def generate_polarity_suggestions():
    """生成极性修复建议"""
    print("\n\nPWM极性修复建议")
    print("="*80)
    
    print("如果颜色反转(亮度相反)，可能需要修改PWM极性:")
    print("在PwmChannel类的__init__方法中，尝试将polarity改为'inversed'")
    print()
    print("修改位置: xiaoli_application_ros2/src/pwm_touch/pwm_touch/pwm_touch_node.py")
    print("第65行附近:")
    print("class PwmChannel:")
    print("    def __init__(self, pwmchip, channel, period, duty, polarity=\"inversed\"):  # 改为inversed")

def generate_code_patches():
    """生成代码补丁建议"""
    print("\n\n代码修复补丁")
    print("="*80)
    
    print("1. 添加伽马校正支持:")
    print("""
在Color类中添加伽马校正方法:

def to_duty_cycle_with_gamma(self, max_duty, gamma=2.2):
    \"\"\"将RGB值转换为PWM占空比，应用伽马校正\"\"\"
    def gamma_correct(value):
        return int(255 * ((value / 255) ** (1/gamma)))
    
    r = gamma_correct(self.r)
    g = gamma_correct(self.g) 
    b = gamma_correct(self.b)
    
    return (
        int(r / 255 * max_duty),
        int(g / 255 * max_duty),
        int(b / 255 * max_duty)
    )
""")
    
    print("\n2. 添加通道映射支持:")
    print("""
在LightController类中添加通道映射:

def _set_color(self, color):
    \"\"\"设置RGB颜色，支持通道映射\"\"\"
    try:
        brightness = self.node.get_parameter('brightness').get_parameter_value().double_value
        r, g, b = color.to_duty_cycle(self.pwm_channels[0].period)
        
        # 应用亮度
        r = int(r * brightness)
        g = int(g * brightness)
        b = int(b * brightness)
        
        # 通道映射 - 根据实际硬件连接调整
        # 如果是BGR顺序，使用: [b, g, r]
        # 如果是GRB顺序，使用: [g, r, b]
        rgb_values = [r, g, b]  # 默认RGB
        # rgb_values = [b, g, r]  # BGR
        # rgb_values = [g, r, b]  # GRB
        
        self.pwm_channels[0].set_duty(rgb_values[0])
        self.pwm_channels[1].set_duty(rgb_values[1])
        self.pwm_channels[2].set_duty(rgb_values[2])
        
        self.node.get_logger().debug(f"设置PWM占空比 - 通道0: {rgb_values[0]}, 通道1: {rgb_values[1]}, 通道2: {rgb_values[2]}")
    except Exception as e:
        self.node.get_logger().error(f"设置颜色失败: {str(e)}")
""")

if __name__ == "__main__":
    generate_color_fixes()
    generate_pwm_config_suggestions()
    generate_polarity_suggestions()
    generate_code_patches()
