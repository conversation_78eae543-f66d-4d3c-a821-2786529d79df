按优先级，改动范围，改动难易程度排列
1. 资源文件(配置，脚本，音视频等)分布
    存在问题
        (1) 节点可能需要单独输出，工程需要自给
        (2) 节点只能感知自身工程的目录结构，导致资源文件必须要放在固定目录
    解决方案
        (1) 资源文件分到到工程中
        (2) 资源文件命名只允许数字，字母，下划线组合
        示例，
            robdog_control 节点使用 xiaoli_application_ros2/resource/audio/到位打开氛围灯.wav
            改造
                (1) 音频命名修改为 open_ambient_light_when_arrive.wav
                (2) 将该音频移动至 robdog_control 工程下，假设为 xiaoli_application_ros2/src/robdog_control/resource
                (3) 通过 CMakeLists.txt 或 setup.py 在编译时将资源文件与程序一并安装
                (4) 程序运行时，访问文件路径为 [robdog_control]运行目录/resource/audio/open_ambient_light_when_arrive.wav

2. 不同硬件参数不同，尤其是硬件标识
    存在问题
        需要修改各工程或节点的配置，可能会遗漏
    解决方案
        短期方案
            维持当前现状，设备固定位置维护相应配置，部署时通过脚本读入修改模块内配置文件
        长期方案
            (1) 统一配置文件，配置文件格式待定，要求必须可读可改
            (2) 可能需要放到指定位置以便节点读取
        示例，
            xiaoli_application_ros2/src/cmcc_rtc/config/param.yaml 中
            device_info.sn/device_info.cmei/device_info.mac

            短期方案
                设备端维护唯一配置，/$HOME/device_info.conf
                    robdog_control_sn=6361752000000014
                    robdog_control_cmei=212345670000012
                    robdog_control_mac=b6:5e:94:d4:2b:6a
                    ...

3. 话题，服务规范化
    存在问题
        (1) 隐式发布话题，隐式创建服务问题
        (2) 话题超过 60 个
    解决方案
        短期方案
            显式发布话题，显式订阅话题
            显式创建服务，显式使用服务
        长期方案
            (1) 通过配置文件方式管理话题及服务，允许动态加载
            (2) 合并冗余的话题及服务，如功能相近，消息格式相近，发布周期较长等

        示例，
            节点 audio_recorder，
                pushStream_ = this->create_publisher<homi_speech_interface::msg::PCMStream>("pcm_stream", 10);
                pushOrigin_ = this->create_publisher<homi_speech_interface::msg::PCMStream>("pcm_origin", 10);
                pushWakeup_ = this->create_publisher<homi_speech_interface::msg::Wakeup>("wakeup_event", 10);
                wakeServer_ = this->create_service<homi_speech_interface::srv::SetWakeEvent>("set_wake_event_service",
                    std::bind(&BaiduAudioNode::setWakeEvent, this, std::placeholders::_1, std::placeholders::_2));
            通过 xiaoli_application_ros2/src/audio_recorder/launch/audio_recorder.launch.py 启动
                package="audio_recorder",
                namespace="audio_recorder",
                name="audio_recorder_node",
                executable="audio_recorder_node"
            最终发布的话题及服务，
                /audio_recorder/pcm_stream
                /audio_recorder/pcm_origin
                /audio_recorder/wakeup_event
                /audio_recorder/set_wake_event_service
            订阅者或客户端需要显式订阅上述话题及服务才能成功，但是 audio_recorder 代码端并未明显展示，理解成本高
            短期方案修改如下，
                pushStream_ = this->create_publisher<homi_speech_interface::msg::PCMStream>("/audio_recorder/pcm_stream", 10);
                pushOrigin_ = this->create_publisher<homi_speech_interface::msg::PCMStream>("/audio_recorder/pcm_origin", 10);
                pushWakeup_ = this->create_publisher<homi_speech_interface::msg::Wakeup>("/audio_recorder/wakeup_event", 10);
                wakeServer_ = this->create_service<homi_speech_interface::srv::SetWakeEvent>("/audio_recorder/set_wake_event_service",
                    std::bind(&BaiduAudioNode::setWakeEvent, this, std::placeholders::_1, std::placeholders::_2));
