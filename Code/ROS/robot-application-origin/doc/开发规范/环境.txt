1. 编译 rcutils
    sudo apt install ros-foxy-mimick-vendor -y
    sudo apt install ros-foxy-performance-test-fixture -y
2. 基础环境
    sudo pip3 install transforms3d
    sudo apt install python3-pip
    pip install --upgrade pip
    pip install scipy

    >>> CRLF -> LF
        sudo apt install dos2unix -y

    >>> xunfei SDK
        sudo apt install libasound2-dev

    >>> update time
        sudo apt install ntpdate -y

    >>> build live_stream
        sudo apt install libgstreamer1.0-dev libgstreamer-plugins-base1.0-dev

    >>> ModuleNotFound tf-transformations
        sudo apt install ros-foxy-tf-transformations

    >>> mpv not found
        sudo apt install mpv socat -y

    >>> please install yq
        sudo apt install jq -y
        pip install yq

    >>> please install sshpass
        sudo apt install sshpass -y
