按当前优先级处理
1. git
    分支及tag
        master/develop(or release)/develop function/hotfix
    强制要求
        (1) Master权限圈定最小范围
        (2) 新的需求必须从Master上切出分支开发
        (3) tag必须携带版本号，并与程序内部版本保持一致
        (4) hotfix必须是对旧需求缺陷修复，而不能承接新需求
        (5) 合并分支必须进行二次核验，确保合并之后与预期完全一致
        (6) 禁止reset git提交记录
        (7) 冲突必须在本地解决然后上传，禁止强制更新远程仓库，如git --force/git -f
    提交规范
        (1) 先pull再push，防止出现多余的merge记录，一般这种记录无意义，且会暴露仓库地址
        (2) 每笔提交最好与jira上需求编号绑定
        (3) 提交信息精简，准确，如修改了某个模块某个功能，是修复缺陷还是添加新功能等，但不宜超过50个字符(中英文合计)
        (4) 解冲突是一件很正常的事情，但必须要在本地仓库解决冲突然后同步至远程仓库。一般来说常规的指令及指令入参即可满足开发需求，
            如git pull/push/checkout/add/commit/stash/merge，附带的指令参数更是很少
2. 文件
    (1) 当前优先解决代码中绝对路径问题
    (2) 现阶段为开发，调试方便，可将资源文件，配置文件，日志等放入程序目录下，便于查找，备份，拷贝等
3. 一键打包及部署
    ./build.sh
        --help/--h
            查看使用方法
        [--select=],a,b,c
            可选，选择需要打包的 packages。默认打包整个 install 文件夹；如果有多个，使用 , 隔开
        --debug
            可选，默认release模式
        [--oem=]
            可选，厂商名称
        [--product=]
            可选，产品名称
        [--cpu=]
            可选，CPU架构，x86/arm/riscv/mips/...
    ./pack.sh
        --help/--h
            查看使用方法
        [--select=],a,b,c
            可选，选择需要打包的 packages。默认打包整个 install 文件夹；如果有多个，使用 , 隔开
        --debug
            可选，默认release模式
        [--oem=]
            可选，厂商名称
        [--product=]
            可选，产品名称
        [--cpu=]
            可选，CPU架构，x86/arm/riscv/mips/...
    ./[product]_[oem]_[cpu]_[ver]_[time]_[debug_type].bin
        --help/--h
            查看使用方法
        --remain
            可选，是否备份旧的 packages
        --extract
            可选，是否只提取 .tar.gz
        [--select=],a,b,c
            可选，选择需要安装的 packages。默认安装整个 bin；如果有多个，使用 , 隔开
        [--install=]
            可选，安装目录。默认安装在与 install_xxx.bin 同级目录下 install 中
    pack.sh: func_pack_per_package() 及 install.sh: func_install_per_package() 需要各模块负责人填充
4. 版本管理
    版本号
        V1.0.0
        第一位主版本号，与需求强绑定
        第二位次版本号，功能改进或大缺陷修复，产品快速开发阶段(如项目刚启动)，需求多，迭代快，做好需求版本与程序版本内部映射即可
        第三位修订号，小缺陷修复等
    模块版本管理
        创建版本管理文件version，统一管理模块，允许修改，新增，删除
    版本回溯
        版本号：必须创建version文件写入版本号，编译时写入文件
        编译时间：编译时生成写入文件
        分支或tag名称：编译时生成写入文件
        commit：编译时生成写入文件
        除版本号必须创建，其他部分各模块也建议添加，因为随着模块功能复杂化可能需要分仓，进入插件化开发阶段，就是独立的模块
    注意事项
        (1) version文件中没有的模块无法使用pack.sh打包
        (2) version文件中模块确实版本号无法使用pack.sh打包
        (3) robot_body为机器人固件版本，与模块版本区分开
5. 日志管理
    日志级别
        FATAL(致命错误) > ERROR(错误) > NOTE(常规) > INFO(信息) > DEBUG(调试)
        常用日志级别，ERROR/NOTE/DEBUG
        允许动态调整程序的打印级别
    日志内容
        基础信息：[时间(精准至秒)][日志级别][模块名][文件名:函数:行数]，基础信息允许增删
    日志规范
        (1) 默认打印级别>=NOTE
        (2) 正常情况下极难执行到的语句推荐添加打印
        (3) 程序入口，动作执行，与平台交互等非频繁执行的地方推荐添加打印
        (4) 高频执行地方禁止添加>=NOTE级别打印
        (5) 敏感信息，如用户名，密码，网站，姓名，邮箱等需要进行脱敏，如使用*/.等代替部分信息
        (6) 打印内容要精简，准确，有效内容占比高
        (7) 不要逐行打印，接口调用嵌套打印
    日志管理
        存储：跟随程序运行目录或大版本直接指定存储目录，各模块在其中自行开辟存储空间
        命名：各模块自定义
        大小：必须限定大小，由大版本直接分配各模块日志最大存储空间
        覆盖：建议循环覆盖，可以保持最近一次日志备份
    日志进阶
        事件日志：关键事件，如报警，异常等
        操作日志：用户操作，平台操作，运维操作等
        埋点日志：与运维平台对接，加密通道上传
6. 代码同步
    Windows(开发) + Linux(编译运行)模式，采用Samba方式
    (1) 安装samba
        sudo apt-get install gsettings-deskdop-schemas
        sudo apt-get install samba samba-commom
    (2) 修改samba配置文件，/ect/samba/smb.conf，最好备份一份
        [work]
            comment = samba
            path = /home/<USER>/work
            create mask = 0777
            directory mask = 0777
            valid users = cat
            force user = cat
            force group = cat
            public = yes
            guest ok = yes
            read only = no
            browseable = yes
            available = yes
            writable = yes
    (3) 创建用户，与上一步保持一致
        sudo smbpasswd -a cat
        最好和开发板的ssh登录用户名和密码保持一致，防止忘记
    (4) 重启服务
        sudo /ect/init.d/smbd restart
    (5) Windows侧添加网络磁盘
        添加网络驱动器 -> \\Linux侧ip\文件夹名称(见(2) path，work)，因此最好创建一个work工作目录，存放工作内容
    可能存在的问题，
        (1) git status或VSCode打开发现文件全是修改状态 ------ 进入代码目录，git config core.fileMode false // 忽略文件权限
        (2) Linux和Windows存在LF和CRLF换行符差别，因此提交代码需要注意将CRLF转成LF，VSCode自带这个功能，右下角。Render Line Endings
7. 快速编译
    当前采用开发板编译
8. 研发代码AI代码生成率
