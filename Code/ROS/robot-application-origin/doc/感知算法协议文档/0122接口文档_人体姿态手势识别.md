# 机器狗感知主机接口文档

- 增加人体姿态手势识别接口

## 1.人体姿态识别算法
### 1.1 启动
| 说明         | 启动人体姿态识别算法  |
|--------------|---------------------|
| **操作**     |   启动              |
| **Topic**    | /action/humanpos_start      |
| **消息类型** | std_msgs.msg        |

- 参数 

| 参数名称    | 类型     | 是否必填 |参数说明                 |
|------------|----------|---------|-------------------------|
| code      | int       | 是      | 状态码，0: 正常，1: 异常  |
| msg       | str       | 是      | 消息字符串               |
| data      | int       | 是      | 0: 启动算法 <br> 2: 开始  |

- 接口示例
```
# 启动
{
    "code": 0,
    "msg": "ok",
	"data": 0
}
```

### 1.2 控制
| 说明          | 控制                  |
|--------------|-----------------------|
| **操作**     | 控制算法状态           |
| **Topic**    | /action/humanpos_control       |
| **消息类型**  | std_msgs.msg          |

- 参数

| 参数名称     | 类型    | 是否必填 | 参数说明                                                 |
|-------------|---------|---------|----------------------------------------------------------|
| code      | int       | 是      | 状态码，0: 正常，1: 异常  |
| msg       | str       | 是      | 消息字符串               |
| data      | int       | 是      | 0: 停止（释放资源）<br> 1: 暂停（算法不识别）<br> 2: 继续    |

- 接口示例
```
# 停止算法
{
    "code": 0,
    "msg": "ok",
    "data": 0
}
```

### 1.3 识别结果
| 说明          | 识别结果              |
|--------------|-----------------------|
| **操作**     | 控制算法状态           |
| **Topic**    | /action/humanpos_result        |
| **消息类型**  | std_msgs.msg          |

- 参数

| 参数名称     | 类型    | 是否必填 | 参数说明                                       |
|-------------|---------|---------|------------------------------------------------|
| code        | int     | 是      | 状态码，0: 正常，1: 异常  |
| msg         | str     | 是      | 消息字符串               |
| data        | List\<Json\> | 是 | 识别结果                 | 
| +score      | float   | 是      | 姿态动作的置信度分数      |
| +pos_type   | int     | 是      | 1: 挥手<br> 2: 鼓掌<br> 3: 坐下<br> 4: 站起<br> 5: 手指左边<br> 6: 手指右边|

- 接口示例
```
# 挥手
{
    "code": 0,
    "msg": "ok",
    "data": [{ 
        "score": 0.99, 
        "pos_type": 1
    }]
}

```

### 1.4 执行状态
| 说明          | 控制                  |
|--------------|-----------------------|
| **操作**     | 控制算法状态           |
| **Topic**    | /action/humanpos_status       |
| **消息类型**  | std_msgs.msg          |

- 参数

| 参数名称     | 类型    | 是否必填 | 参数说明                                                 |
|-------------|---------|---------|----------------------------------------------------------------|
| code      | int       | 是      | 状态码，0: 正常，1: 异常  |
| msg       | str       | 是      | 消息字符串               |
| data      | int       | 是      | 0: 停止<br> 1: 暂停<br> 2: 开始或继续<br> 10: 初始化完成|

- 接口示例
```
# 算法已经停止
{
    "code": 0,
    "msg": "ok",
    "data": 0
}
```

## 2. 状态码说明

| Code   | Type | Msg                      | 说明                        |
|--------|------|--------------------------|-----------------------------|
| 0      | 0    | {"msg": [具体消息内容]}   | 状态正常                     |
| 1      | 0    | {"msg": [具体消息内容]}   | 状态异常                     |

