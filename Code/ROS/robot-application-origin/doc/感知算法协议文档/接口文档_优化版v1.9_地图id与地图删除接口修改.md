# 机器狗感知主机接口文档
## v1.9
1.将地图id统一从entityId修改为mapId,与平台接口保持统一.

2.修改地图删除接口,支持下发地图id列表实现批量删除。详见[建图接口](#chapter-1)

----------------------------------
先前修改记录
- v1.8修改:
1.增加室内漫步功能接口.详见[室内漫步接口](#chapter-4)与[室内漫步状态上报](#f6)

- v1.7修改:

  1.增加自动回充功能下发接口,包括点位录入与启动回充功能.详见[自动回充接口](#chapter-3) 与 [自动回充状态上报](#f5)

  2.任务状态上报(/task_status)增加自动回充功能的状态上报


- v1.6修改:

    1.位置上报接口增加两个与路径规划相关的字段"batchId"和"status"

    2.多点导航任务中的"taskId"改为"batchId",同步修改状态码2006的消息内容


- v1.5修改:

    1.增加建图功能的删除与取消接口

    2.建图的action增加2和3，消息上报增加1002,1003,1102，修改了1202,1203的描述

## <h2 id="chapter-1">1.建图相关操作 </h2>
### 地图构建
| 说明        | 执行“建图”相关的操作指令下发  |
|-----------|------------------|
| **操作**    | 下发至感知主机          |
| **Topic** | /mapping_control |
| **消息类型**  | std_msgs.msg     |

- 参数

| 参数名称   | 类型     | 是否必填 | 参数说明                                                                          |
|--------|--------|------|-------------------------------------------------------------------------------|
| action | Int    | 是    | 具体建图操作<br> - 0: 建图开始<br> - 1: 建图完成<br> - 2: 建图取消<br> - 3: 删除已有地图,需处在**非建图状态** |
| mapId  | String | 是    | 地图Id,在删除功能中为**list**                                                          |
| url    | String | 否    | 上传地图的OSS地址,开始建图时**必填**,完成建图时可缺省                                               |
相关状态上报见[建图状态上报](#f1)

- 接口示例
```
- 建图开始:
{
	"mapId": "1234",
	"action": 0,
	"url": "可写的OSS URL"
}

- 建图完成:
{
	"mapId": "1234",
	"action": 1,
}

- 建图取消:不保存当前建图结果
{
	"mapId": "1234",
	"action": 2,
}

- 删除已有地图
-- mapId字段为list,支持单张与批量删除
{
	"mapId": ["1", "2", "3"],
	"action": 3,
}
```

### 虚拟墙构建
| 说明        | 执行虚拟墙相关操作指令的下发        |
|-----------|-----------------------|
| **操作**    | 下发至感知主机               |
| **Topic** | /virtual_wall_control |
| **消息类型**  | std_msgs.msg          |

- 参数

| 参数名称           | 类型           | 是否必填 | 参数说明                             |
|----------------|--------------|------|----------------------------------|
| mapId          | String       | 是    | 地图Id                             |
| virtualWall    | List\<Json\> | 是    | 当前地图的**最新**虚拟墙列表<br>* 为空时清空当前虚拟墙 |
| -  id          | Int          | 是    | 虚拟墙id                            |
| -  coordinate  | Json         | 是    | 虚拟墙坐标信息                          |
| --  type       | Int          | 是    | 虚拟墙类型, 1-线段; 2-区域                |
| --  startPoint | Json         | 否    | 起点坐标                             |
| ----  x        | float        | 否    | 起点x轴坐标                           |
| ----  y        | float        | 否    | 起点y轴坐标                           |
| --  endPoint   | Json         | 否    | 终点坐标                             |
| ----  x        | float        | 否    | 终点x轴坐标                           |
| ----  y        | float        | 否    | 终点y轴坐标                           |
相关状态上报见[虚拟墙状态上报](#f3)

- 接口示例
```
{
	"mapId": "1234",  // 地图id
	"virtualWall": [{
	    "id": 1,         // 虚拟墙id
	    "coordinate":{
	        "type": 1,   // 1-线段; 2-区域
	        "startPoint":{
	            "x": 0.0,
	            "y": 0.0
	        },
	        "endPoint":{
	            "x": 100.0,
	            "y": 100.0	        
	        }
	    }
	}, ...]
}

```

## <h2 id="chapter-2">2.导航相关操作</h2>
### 导航任务操作

| 说明        | 执行导航相关操作指令的下发       |
|-----------|---------------------|
| **操作**    | 下发至感知主机             |
| **Topic** | /navigation_control |
| **消息类型**  | std_msgs.msg        |

- 参数

| 参数名称               | 类型           | 是否必填 | 参数说明                                                                                                                                                                     |
|--------------------|--------------|------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| action             | Int          | 是    | 具体建图操作<br> - 0: 结束导航功能<br> - 1: 开启导航功能<br> - 10: 执行多点导航<br> - 11: 执行重定位 <br> - 12: 取消当前导航任务 <br> - 13: 暂停当前导航任务<br> - 14: 继续当前导航任务                                       |
| mapId              | String       | 是    | 导航功能使用的地图Id                                                                                                                                                              |
| batchId            | String       | 否    | 多点导航任务的id, **执行**多点导航时**必填**,其余情况可缺省                                                                                                                                     |
| points             | List\<Json\> | 否    | 多点导航的目标列表, **执行**多点导航时**必填**,其余情况可缺省                                                                                                                                     |
| -  x               | Float        | 否    | 目标点x轴像素值                                                                                                                                                                 |
| -  y               | Float        | 否    | 目标点y轴像素值                                                                                                                                                                 |
| -  angle           | Float        | 否    | 目标点朝向                                                                                                                                                                    |
| -  option          | String       | 否    | 前往目标点时的步态,默认为"平地低速"<br> - "even_low_speed": 平地低速<br> - "even_medium_speed": 平地中速<br> - "uneven_high_step": 非平地高步态<br> - "even_rl": 平地强化学习步态<br> - "uneven_rl": 非平地强化学习步态 |
| -  uid             | String       | 否    | 目标点uid,导航任务到达相应点位后会返回该值                                                                                                                                                  |
| need_relocalize    | Bool         | 否    | 多点导航任务的重定位开关, **启动**多点导航时**选填**,默认为True                                                                                                                                  |
| need_drift_checker | Bool         | 否    | 多点导航任务的漂移检测开关, **启动**多点导航时**选填**,默认为True                                                                                                                                 |
相关状态上报见[导航状态上报](#f2)

- 接口示例
```
- 结束导航
{
    "action": 0, 
    "mapId": "1234",  // 地图id
}

- 开启导航
{
    "action": 1, 
    "mapId": "1234",
}

- 执行多点导航
{
    "action": 10, 
    "mapId": "1234",
    "batchId": "xxxx"
    "points": [{ 
        "x": 100.0, 
        "y": 200.0, 
        "angle": 10.0, 
        "option": "even_low_speed",  // 步态名称见上
        "uid": "1"
    }]
}

- 执行重定位
{
    "action": 11, 
    "mapId": "1234", 
}

- 取消导航任务
{
    "action": 12, 
    "mapId": "1234", 
}

- 暂停导航任务
{
    "action": 13, 
    "mapId": "1234", 
}

- 继续导航任务
{
    "action": 14, 
    "mapId": "1234", 
}
```

### 导航位置上报
| 说明        | 执行当前坐标位置与朝向角度的上报     |
|-----------|----------------------|
| **操作**    | 感知主机上报               |
| **Topic** | /navigation_position |
| **消息类型**  | std_msgs.msg         |

- 参数

| 参数名称         | 类型           | 是否必填 | 参数说明                                                                             |
|--------------|--------------|------|----------------------------------------------------------------------------------|
| x            | Float        | 是    | 地图坐标系下的x轴**像素值**                                                                 |
| y            | Float        | 是    | 地图坐标系下的y轴**像素值**                                                                 |
| angle        | Float        | 是    | 地图坐标系下的角度,范围为 [-π, π]                                                            |
| batchId      | String       | 是    | 一次导航任务的唯一标识, 非导航状态时**缺省**, 导航状态时与任务下发的**batchId**保存一致                            |
| status       | Int          | 是    | 导航任务状态码<br> - 0: 未进行(目前直接缺省) <br> - 1: 进行中 <br> - 2: 已完成 (完成导航后的3次点位上报会保持为已完成状态) |
| path         | List\<Json\> | 否    | 导航任务时的规划路径,为多个点位组成的列表                                                            |
| - path_x     | Float        | 否    | 规划路径的点位x轴**像素值**                                                                 |
| - path_y     | Float        | 否    | 规划路径的点位y轴**像素值**                                                                 |
| - path_angle | Float        | 否    | 规划路径的点位角度,范围为 [-π, π]                                                            |

- 接口示例
```
- 通常点位上报
{
    "x": 100.0, 
    "y": 60.0, 
    "angle": 10.0,
}

- 导航过程中的点位上报
{
    "x": 100.0, 
    "y": 60.0, 
    "angle": 10.0,
    "batchId": xxxx
    "status": 1
    "path":[
        {
            "path_x": 110.0, 
            "path_y": 70.0, 
            "path_angle": 10.0
        },
        {
            "path_x": 120.0, 
            "path_y": 80.0, 
            "path_angle": 10.0
        }, .....
    ]
}

- 导航任务完成后的点位上报
* 完成导航后的3次点位上报会保持为已完成状态
{
    "x": 100.0, 
    "y": 60.0, 
    "angle": 10.0,
    "batchId": xxxx
    "status": 2
}
```

## <h2 id="chapter-3">3.自动回充 </h2>
| 说明        | 执行“自动回充”相关的操作指令下发 |
|-----------|-------------------|
| **操作**    | 下发至感知主机           |
| **Topic** | /charge_control   |
| **消息类型**  | std_msgs.msg      |

- 参数

| 参数名称   | 类型     | 是否必填 | 参数说明                                                      |
|--------|--------|------|-----------------------------------------------------------|
| action | Int    | 是    | 具体回充操作<br> - 0: 二维码点位录入<br> - 1: 开始回充,注意**需要先导航至先前录入的点位** |
| mapId  | String | 是    | 地图Id                                                      | 
相关状态上报见[自动回充状态上报](#f5)

- 接口示例
```
- 二维码点位录入
{
    "action": 0, 
    "mapId":"1234"
}

- 开始回充
{
    "action": 1, 
    "mapId":"1234"
}

```

## <h2 id="chapter-4">4.室内漫步 </h2>
| 说明        | 执行“室内漫步”相关的操作指令下发 |
|-----------|-------------------|
| **操作**    | 下发至感知主机           |
| **Topic** | /ramble_control   |
| **消息类型**  | std_msgs.msg      |

- 参数

| 参数名称   | 类型  | 是否必填 | 参数说明                              |
|--------|-----|------|-----------------------------------|
| action | Int | 是    | 具体操作<br> - 0: 开始漫步 <br> - 1: 结束漫步 |

相关状态上报见[室内漫步状态上报](#f6)

- 接口示例
```
- 开始漫步
{
    "action": 0, 
}

- 结束漫步
{
    "action": 1, 
}

```


## <h2 id="chapter-5">5.任务状态上报 </h2>
| 说明        | 任务执行的状态上报,包含正常与异常上报 |
|-----------|---------------------|
| **操作**    | 感知主机上报              |
| **Topic** | /task_status        |
| **消息类型**  | std_msgs.msg        |

- 参数

| 参数名称 | 类型   | 是否必填 | 参数说明                                                                              |
|------|------|------|-----------------------------------------------------------------------------------|
| code | Int  | 是    | 状态码, 结构为: 任务-类型-编号                                                                |
| type | Int  | 是    | 状态码的类型/优先级 <br> - 0: 正常 <br> - 1: 异常1级,记录日志 <br> - 2: 异常2级,上报app<br> - 3: 异常3级,告警 |
| msg  | Json | 否    | 状态相关描述, 可缺省                                                                       |

- 接口示例
```
{
    "code": 1000,
    "type": 0,
    "msg": {"msg": xxxx}
}

- 单点导航任务已完成
{
    "code": 2002,
    "type": 0,
    "msg": {"uid": [导航任务下发点位对应uid]}
}

- 整体导航任务已完成
{
    "code": 2006,
    "type": 0,
    "msg": {"taskId": [任务对应id]}
}

```

### <h2 id="f1">建图状态码详细说明</h2>
[建图接口](#chapter-1)

| Code   | Type | Msg                 | 说明                          |
|--------|------|---------------------|-----------------------------|
| 1000   | 0    | {"msg": [具体消息内容]}   | 建图节点已开启                     |
| 1001   | 0    | {"msg": [具体消息内容]}   | 建图节点已关闭                     |
| 1002   | 0    | {"msg": [具体消息内容]}   | 当前建图流程已取消,建图节点关闭            |
| 1003   | 0    | {"msg": [具体消息内容]}   | 已删除指定地图, msg中返回删除的地图id列表    |
| 1100   | 1    | {"error": [具体消息内容]} | 下发指令的消息格式错误                 |
| 1101   | 1    | {"error": [具体消息内容]} | 下发指令未提供“任务”参数，或“任务”参数不合法    |
| 1102   | 1    | {"error": [具体消息内容]} | 无指定id的地图,删除操作失败             |
| 1103   | 1    | {"error": [具体消息内容]} | 地图保存过程出错                    |
| 1200   | 2    | {"error": [具体消息内容]} | 建图结束指令的地图id与当前地图id不匹配       |
| 1201   | 2    | {"error": [具体消息内容]} | 建图过程中上传地图失败，URL不合法或感知主机无法上传 |
| 1202   | 2    | {"error": [具体消息内容]} | 处于建图流程时，收到新的建图开始指令或删除地图指令   |
| 1203   | 2    | {"error": [具体消息内容]} | 处于导航流程时，收到建图开始/结束/取消/删除地图指令 |
| 1204   | 2    | {"error": [具体消息内容]} | 不处于建图流程时，收到建图结束/取消指令        |
| 1205 * | 2    | {"error": [具体消息内容]} | 节点崩溃，请重启建图流程                |
 
### <h2 id="f2">导航状态码详细说明</h2>
[导航接口](#chapter-2)

| Code   | Type | Msg                                   | 说明                                    |
|--------|------|---------------------------------------|---------------------------------------|
| 2000   | 0    | {"msg": [具体消息内容]}                     | 导航节点已开启                               |
| 2001   | 0    | {"msg": [具体消息内容]}                     | 导航节点已关闭                               |
| 2002   | 0    | **{"uid": [点位对应uid]}**                | 单点导航任务已完成,返回下发点位时的uid                 |
| 2003   | 0    | {"msg": [具体消息内容]}                     | 导航任务已暂停                               |
| 2004   | 0    | **{"uid": [点位对应uid]}**                | 导航任务已取消                               |
| 2005   | 0    | {"msg": [具体消息内容]}                     | 重定位任务已完成                              |
| 2006   | 0    | **{"batchId": [任务对应id]}**             | 整体导航任务已完成,返回任务的batchId                |
| 2100   | 1    | {"error": [具体消息内容]}                   | 检测到漂移，正在自动重定位                         |
| 2101   | 1    | {"error": [具体消息内容]}                   | 下发指令的消息格式错误                           |
| 2102   | 1    | {"error": [具体消息内容]}                   | 下发指令未提供“任务”参数，或“任务”参数不合法              |
| 2103   | 1    | {"error": [具体消息内容]}                   | 下发的步态类型不合法(会采取默认步态)                   |
| 2104   | 1    | {"error": [具体消息内容]}                   | 无法出发,可能处于匍匐状态或定位在障碍物/膨胀层中             |
| 2200   | 2    | {"error": [具体消息内容], "uid": [点位对应uid]} | 下发的目标点位不可达或点位不合法                      |
| 2201   | 2    | {"error": [具体消息内容]}                   | 导航任务启动失败，地图id不存在                      |
| 2202   | 2    | {"error": [具体消息内容]}                   | 不处于导航流程时，收到导航结束/目标点设置/暂停/取消/重定位指令     |
| 2203   | 2    | {"error": [具体消息内容]}                   | 导航结束/目标点设置/暂停/取消/重定位指令的地图id与当前地图id不匹配 |
| 2204   | 2    | {"error": [具体消息内容]}                   | 目标点设置/暂停/取消/重定位任务失败                   |
| 2205   | 2    | {"error": [具体消息内容]}                   | 处于导航流程时，收到新的导航开始指令                    |
| 2206   | 2    | {"error": [具体消息内容]}                   | 处于建图流程时，收到导航开始指令                      |
| 2207   | 2    | {"error": [具体消息内容]}                   | 重定位误差过大，请手动进行定位或检查机器狗所处地图是否正确         |
| 2208 * | 2    | {"error": [具体消息内容]}                   | 节点崩溃，请重启导航流程                          |
| 2209   | 2    | {"error": [具体消息内容], "uid": [点位对应uid]} | 导航任务失败,可能是路径规划或导航执行失败                 |

### <h2 id="f3">虚拟墙状态码详细说明</h2>
| Code   | Type | Msg                 | 说明                       |
|--------|------|---------------------|--------------------------|
| 3000   | 0    | {"msg": [具体消息内容]}   | 虚拟墙状态更新成功                |
| 3100   | 1    | {"error": [具体消息内容]} | 下发指令的消息格式错误              |
| 3200 * | 2    | {"error": [具体消息内容]} | 节点崩溃，请重启导航流程             |
| 3201 * | 2    | {"error": [具体消息内容]} | 虚拟墙状态更新失败                |
| 3202   | 2    | {"error": [具体消息内容]} | 不处于导航过程时，收到虚拟墙更新指令       |
| 3203   | 2    | {"error": [具体消息内容]} | 下发指令的地图id与当前导航流程的地图id不匹配 |

### <h2 id="f5">自动回充状态码详细说明</h2>
[自动回充接口](#chapter-3)

‘5x0x’为点位录入相关状态, ‘5x1x’为回充过程相关状态

| Code | Type | Msg                                                                                              | 说明                                 |
|------|------|--------------------------------------------------------------------------------------------------|------------------------------------|
| 5000 | 0    | {"msg": [具体消息内容]}                                                                                | 点位录入功能启动成功                         |
| 5001 | 0    | {"msg": [具体消息内容]}                                                                                | 视野内出现二维码,开始对准电极片                   |
| 5002 | 0    | {"msg": [具体消息内容], <br>"charge_point":<br>  {"x":0.0, "y":0.0, "angle":0.0}, <br>"mapId": "map1"} | 电极片已对准,点位录入功能完成,并返回回充点位导航坐标与对应地图id |
| 5010 | 0    | {"msg": [具体消息内容]}                                                                                | 回充功能启动成功                           |
| 5011 | 0    | {"msg": [具体消息内容]}                                                                                | 回充功能完成,可以趴下充电                      |
| 5100 | 0    | {"error": [具体消息内容]}                                                                              | 下发指令格式错误                           |
| 5200 | 0    | {"error": [具体消息内容]}                                                                              | 点位录入功能启动失败,视野内未出现二维码               |
| 5201 | 0    | {"error": [具体消息内容]}                                                                              | 点位录入功能启动失败,算法出现异常                  |
| 5210 | 0    | {"error": [具体消息内容]}                                                                              | 回充功能失败,未在指定时间内对齐电极片                |


### <h2 id="f6">室内漫步状态码详细说明</h2>
[室内漫步接口](#chapter-4)

| Code | Type | Msg                 | 说明                         |
|------|------|---------------------|----------------------------|
| 6000 | 0    | {"msg": [具体消息内容]}   | 室内漫步已启动                    |
| 6001 | 0    | {"msg": [具体消息内容]}   | 室内漫步已关闭                    |
| 6100 | 0    | {"error": [具体消息内容]} | 下发指令格式错误                   |
| 6200 | 0    | {"error": [具体消息内容]} | 漫步功能启动失败,目前处于建图/导航等其他任务状态中 |
| 6201 | 0    | {"error": [具体消息内容]} | 漫步功能启动失败,算法出现异常            |


