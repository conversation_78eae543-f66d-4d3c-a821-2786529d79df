# 机器狗感知主机接口文档
## 合大优化版 v1.2
## 1.建图相关操作
### 地图构建
| 说明        | 执行“开始建图”与“建图完成”操作指令的下发 |
|-----------|------------------------|
| **操作**    | 下发至感知主机                |
| **Topic** | /mapping_control       |
| **消息类型**  | std_msgs.msg           |

- 参数

| 参数名称     | 类型     | 是否必填 | 参数说明                               |
|----------|--------|------|------------------------------------|
| action   | Int    | 是    | 具体建图操作<br> - 0: 建图开始<br> - 1: 建图完成 |
| entityId | String | 是    | 地图Id                               |
| url      | String | 否    | 上传地图的OSS地址,开始建图时**必填**,完成建图时可缺省    |

- 接口示例
```
- 建图开始:
{
	"entityId": "1234",
	"action": 0,
	"url": "可写的OSS URL"
}

- 建图完成:
{
	"entityID": "1234",
	"action": 1,
}
```

### 虚拟墙构建
| 说明        | 执行虚拟墙相关操作指令的下发        |
|-----------|-----------------------|
| **操作**    | 下发至感知主机               |
| **Topic** | /virtual_wall_control |
| **消息类型**  | std_msgs.msg          |

- 参数

| 参数名称           | 类型           | 是否必填 | 参数说明                        |
|----------------|--------------|------|-----------------------------|
| entityId       | String       | 是    | 地图Id                        |
| virtualWall    | List\<Json\> | 是    | 当前地图的**最新**虚拟墙列表,为空时清空当前虚拟墙 |
| -  id          | Int          | 是    | 虚拟墙id                       |
| -  coordinate  | Json         | 是    | 虚拟墙坐标信息                     |
| --  type       | Int          | 是    | 虚拟墙类型, 1-线段; 2-区域           |
| --  startPoint | Json         | 否    | 起点坐标                        |
| ----  x        | float        | 否    | 起点x轴坐标                      |
| ----  y        | float        | 否    | 起点y轴坐标                      |
| --  endPoint   | Json         | 否    | 终点坐标                        |
| ----  x        | float        | 否    | 终点x轴坐标                      |
| ----  y        | float        | 否    | 终点y轴坐标                      |


- 接口示例
```
{
	"entityId": "1234",  // 地图id
	"virtualWall": [{
	    "id": 1,         // 虚拟墙id
	    "coordinate":{
	        "type": 1,   // 1-线段; 2-区域
	        "startPoint":{
	            "x": 0.0,
	            "y": 0.0
	        },
	        "endPoint":{
	            "x": 100.0,
	            "y": 100.0	        
	        }
	    }
	}, ...]
}

```

## 2.导航相关操作
### 导航任务操作
**【需要和产品确认流程】**
 app端如何切换地图，流程控制如何?

**【需要确定“暂停功能”的实现方案】**

| 说明        | 执行导航相关操作指令的下发       |
|-----------|---------------------|
| **操作**    | 下发至感知主机             |
| **Topic** | /navigation_control |
| **消息类型**  | std_msgs.msg        |

- 参数

| 参数名称      | 类型           | 是否必填 | 参数说明                                                                                                                                                                     |
|-----------|--------------|------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| action    | Int          | 是    | 具体建图操作<br> - 0: 结束导航功能<br> - 1: 开启导航功能<br> - 10: 执行多点导航<br> - 11: 执行重定位 <br> - 12: 取消当前导航任务 <br> - 13: 暂停当前导航任务<br> - 14: 继续当前导航任务                                       |
| entityId  | String       | 是    | 导航功能使用的地图Id                                                                                                                                                              |
| points    | List\<Json\> | 否    | 多点导航的目标列表, 执行多点导航时**必填**,其余情况可缺省                                                                                                                                         |
| -  x      | Float        | 否    | 目标点x轴像素值                                                                                                                                                                 |
| -  y      | Float        | 否    | 目标点y轴像素值                                                                                                                                                                 |
| -  angle  | Float        | 否    | 目标点朝向                                                                                                                                                                    |
| -  option | String       | 否    | 前往目标点时的步态,默认为"平地低速"<br> - "even_low_speed": 平地低速<br> - "even_medium_speed": 平地中速<br> - "uneven_high_step": 非平地高步态<br> - "even_rl": 平地强化学习步态<br> - "uneven_rl": 非平地强化学习步态 |

```
- 结束导航
{
    "action": 0, 
    "entityId": "1234",  // 地图id
}

- 开启导航
{
    "action": 1, 
    "entityId": "1234",
}

- 执行多点导航
{
    "action": 10, 
    "entityId": "1234",
    "points": [{ 
        "x": 100.0, 
        "y": 200.0, 
        "angle": 10.0, 
        "option": "even_low_speed"  // 步态名称见上
    }]
}

- 执行重定位
{
    "action": 11, 
    "entityId": "1234", 
}

- 取消导航任务
{
    "action": 12, 
    "entityId": "1234", 
}
```

### 导航位置上报
| 说明        | 执行当前坐标位置与朝向角度的上报     |
|-----------|----------------------|
| **操作**    | 感知主机上报               |
| **Topic** | /navigation_position |
| **消息类型**  | std_msgs.msg         |

- 参数

| 参数名称 | 类型    | 是否必填 | 参数说明                  |
|------|-------|------|-----------------------|
| x    | Float | 是    | 地图坐标系下的x轴**像素值**      |
| y    | Float | 是    | 地图坐标系下的y轴**像素值**      |
| y    | Float | 是    | 地图坐标系下的角度,范围为 [-π, π] |
- 接口示例
```
{
    "x": 100.0, 
    "y": 60.0, 
    "angle": 10.0
}
```

## 3.任务状态上报
| 说明        | 任务执行的状态上报,包含正常与异常上报 |
|-----------|---------------------|
| **操作**    | 感知主机上报              |
| **Topic** | /task_status        |
| **消息类型**  | std_msgs.msg        |

- 参数

| 参数名称 | 类型     | 是否必填 | 参数说明                                                                              |
|------|--------|------|-----------------------------------------------------------------------------------|
| code | Int    | 是    | 状态码, 结构为: 任务-类型-编号                                                                |
| type | Int    | 是    | 状态码的类型/优先级 <br> - 0: 正常 <br> - 1: 异常1级,记录日志 <br> - 2: 异常2级,上报app<br> - 3: 异常3级,告警 |
| msg  | String | 否    | 状态相关描述, 可缺省                                                                       |

- 接口示例
```
{
    "code": 1000,
    "type": 0,
    "msg": ""
}

```

### 建图状态码详细说明
| Code | Type | Msg | 说明                          |
|------|------|-----|-----------------------------|
| 1000 | 0    | ""  | 建图节点已开启                     |
| 1001 | 0    | ""  | 建图节点已关闭                     |
| 1100 | 1    | ""  | 下发指令的消息格式错误                 |
| 1101 | 1    | ""  | 下发指令未提供“任务”参数，或“任务”参数不合法    |
| 1200 | 2    | ""  | 建图结束指令的地图id与当前地图id不匹配       |
| 1201 | 2    | ""  | 建图过程中上传地图失败，URL不合法或感知主机无法上传 |
| 1202 | 2    | ""  | 处于建图流程时，收到新的建图开始指令          |
| 1203 | 2    | ""  | 处于导航流程时，收到建图开始/结束指令         |
| 1205 | 2    | ""  | 不处于建图流程时，收到建图结束指令           |
| 1205 | 2    | ""  | 节点崩溃，请重启建图流程                |

### 导航状态码详细说明
| Code | Type | Msg | 说明                                    |
|------|------|-----|---------------------------------------|
| 2000 | 0    | ""  | 导航节点已开启                               |
| 2001 | 0    | ""  | 导航节点已关闭                               |
| 2002 | 0    | ""  | 单点导航任务已完成                             |
| 2003 | 0    | ""  | 所有导航任务已完成                             |
| 2004 | 0    | ""  | 导航任务已暂停                               |
| 2005 | 0    | ""  | 导航任务已取消                               |
| 2006 | 0    | ""  | 重定位任务已完成                              |
| 2100 | 1    | ""  | 检测到漂移，正在自动重定位                         |
| 2101 | 1    | ""  | 下发指令的消息格式错误                           |
| 2102 | 1    | ""  | 下发指令未提供“任务”参数，或“任务”参数不合法              |
| 2103 | 1    | ""  | 下发的步态类型不合法                            |
| 2200 | 2    | ""  | 下发的目标点位不可达或点位不合法                      |
| 2201 | 2    | ""  | 导航任务启动失败，地图id不存在                      |
| 2202 | 2    | ""  | 不处于导航流程时，收到导航结束/目标点设置/暂停/取消/重定位指令     |
| 2203 | 2    | ""  | 导航结束/目标点设置/暂停/取消/重定位指令的地图id与当前地图id不匹配 |
| 2204 | 2    | ""  | 目标点设置/暂停/取消/重定位任务失败                   |
| 2205 | 2    | ""  | 处于导航流程时，收到新的导航开始指令                    |
| 2206 | 2    | ""  | 处于建图流程时，收到导航开始指令                      |
| 2207 | 2    | ""  | 重定位误差过大，请手动进行定位或检查机器狗所处地图是否正确         |
| 2208 | 2    | ""  | 节点崩溃，请重启导航流程                          |


### 虚拟墙状态码详细说明
| Code | Type | Msg | 说明                       |
|------|------|-----|--------------------------|
| 3000 | 0    | ""  | 虚拟墙状态更新成功                |
| 3001 | 1    | ""  | 下发指令的消息格式错误              |
| 3101 | 1    | ""  | 下发指令的地图id与当前导航流程的地图id不匹配 |
| 3201 | 2    | ""  | 虚拟墙状态更新失败                |
| 3202 | 2    | ""  | 不处于导航过程时，收到虚拟墙更新指令       |
| 3203 | 2    | ""  | 节点崩溃，请重启导航流程             |
