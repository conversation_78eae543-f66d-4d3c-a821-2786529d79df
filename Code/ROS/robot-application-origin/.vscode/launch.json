{"version": "0.2.0", "configurations": [{"name": "Debug Network Node", "type": "python", "request": "launch", "program": "${workspaceFolder}/xiaoli_application_ros2/src/network/network/network_node.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/xiaoli_application_ros2", "preLaunchTask": "Build Network Dependencies", "args": ["--ros-args", "--params-file", "${workspaceFolder}/xiaoli_application_ros2/src/launch_package/configs/robot_config.yaml"], "env": {"ROS_DOMAIN_ID": "0", "PYTHONPATH": "${workspaceFolder}/xiaoli_application_ros2/src:${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/local/lib/python3.10/dist-packages:${env:PYTHONPATH}", "ROS_LOG_DIR": "${workspaceFolder}/log", "RCUTILS_LOGGING_BUFFERED_STREAM": "1", "RCUTILS_COLORIZED_OUTPUT": "1", "NETWORK_DEBUG_MODE": "1", "AMENT_PREFIX_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface:${env:AMENT_PREFIX_PATH}", "CMAKE_PREFIX_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface:${env:CMAKE_PREFIX_PATH}", "LD_LIBRARY_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/lib:${env:LD_LIBRARY_PATH}", "ROS_PACKAGE_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/share:${env:ROS_PACKAGE_PATH}"}, "justMyCode": false, "stopOnEntry": false, "python": "/usr/bin/python3"}, {"name": "Debug Network Node (Test Mode)", "type": "python", "request": "launch", "program": "${workspaceFolder}/xiaoli_application_ros2/src/network/network/network_node.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/xiaoli_application_ros2", "preLaunchTask": "Build Network Dependencies", "args": ["--ros-args", "--params-file", "${workspaceFolder}/xiaoli_application_ros2/src/launch_package/configs/robot_config.yaml"], "env": {"ROS_DOMAIN_ID": "0", "PYTHONPATH": "${workspaceFolder}/xiaoli_application_ros2/src:${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/local/lib/python3.10/dist-packages:${env:PYTHONPATH}", "ROS_LOG_DIR": "${workspaceFolder}/log", "RCUTILS_LOGGING_BUFFERED_STREAM": "1", "RCUTILS_COLORIZED_OUTPUT": "1", "NETWORK_DEBUG_MODE": "1", "NETWORK_TEST_MODE": "1", "AMENT_PREFIX_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface:${env:AMENT_PREFIX_PATH}", "CMAKE_PREFIX_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface:${env:CMAKE_PREFIX_PATH}", "LD_LIBRARY_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/lib:${env:LD_LIBRARY_PATH}", "ROS_PACKAGE_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/share:${env:ROS_PACKAGE_PATH}"}, "justMyCode": false, "stopOnEntry": false, "python": "/usr/bin/python3"}, {"name": "Debug Network Node (Custom Config)", "type": "python", "request": "launch", "program": "${workspaceFolder}/xiaoli_application_ros2/src/network/network/network_node.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/xiaoli_application_ros2", "preLaunchTask": "Build Network Dependencies", "args": ["--ros-args", "--params-file", "${input:configFile}"], "env": {"ROS_DOMAIN_ID": "0", "PYTHONPATH": "${workspaceFolder}/xiaoli_application_ros2/src:${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/local/lib/python3.10/dist-packages:${env:PYTHONPATH}", "ROS_LOG_DIR": "${workspaceFolder}/log", "RCUTILS_LOGGING_BUFFERED_STREAM": "1", "RCUTILS_COLORIZED_OUTPUT": "1", "NETWORK_DEBUG_MODE": "1", "AMENT_PREFIX_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface:${env:AMENT_PREFIX_PATH}", "CMAKE_PREFIX_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface:${env:CMAKE_PREFIX_PATH}", "LD_LIBRARY_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/lib:${env:LD_LIBRARY_PATH}", "ROS_PACKAGE_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/share:${env:ROS_PACKAGE_PATH}"}, "justMyCode": false, "stopOnEntry": false, "python": "/usr/bin/python3"}, {"name": "Debug Network Node (DEBUG Level)", "type": "python", "request": "launch", "program": "${workspaceFolder}/xiaoli_application_ros2/src/network/network/network_node.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/xiaoli_application_ros2", "preLaunchTask": "Build Network Dependencies", "args": ["--ros-args", "--params-file", "${workspaceFolder}/xiaoli_application_ros2/src/launch_package/configs/robot_config.yaml", "--log-level", "DEBUG"], "env": {"ROS_DOMAIN_ID": "0", "PYTHONPATH": "${workspaceFolder}/xiaoli_application_ros2/src:${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/local/lib/python3.10/dist-packages:${env:PYTHONPATH}", "ROS_LOG_DIR": "${workspaceFolder}/log", "RCUTILS_LOGGING_BUFFERED_STREAM": "1", "RCUTILS_COLORIZED_OUTPUT": "1", "NETWORK_DEBUG_MODE": "1", "AMENT_PREFIX_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface:${env:AMENT_PREFIX_PATH}", "CMAKE_PREFIX_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface:${env:CMAKE_PREFIX_PATH}", "LD_LIBRARY_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/lib:${env:LD_LIBRARY_PATH}", "ROS_PACKAGE_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/share:${env:ROS_PACKAGE_PATH}"}, "justMyCode": false, "stopOnEntry": false, "python": "/usr/bin/python3"}, {"name": "Debug Network Node (ENV Log Level)", "type": "python", "request": "launch", "program": "${workspaceFolder}/xiaoli_application_ros2/src/network/network/network_node.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/xiaoli_application_ros2", "preLaunchTask": "Build Network Dependencies", "args": ["--ros-args", "--params-file", "${workspaceFolder}/xiaoli_application_ros2/src/launch_package/configs/robot_config.yaml"], "env": {"ROS_DOMAIN_ID": "0", "PYTHONPATH": "${workspaceFolder}/xiaoli_application_ros2/src:${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/local/lib/python3.10/dist-packages:${env:PYTHONPATH}", "ROS_LOG_DIR": "${workspaceFolder}/log", "RCUTILS_LOGGING_BUFFERED_STREAM": "1", "RCUTILS_COLORIZED_OUTPUT": "1", "NETWORK_DEBUG_MODE": "1", "ROS_LOG_LEVEL": "DEBUG", "AMENT_PREFIX_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface:${env:AMENT_PREFIX_PATH}", "CMAKE_PREFIX_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface:${env:CMAKE_PREFIX_PATH}", "LD_LIBRARY_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/lib:${env:LD_LIBRARY_PATH}", "ROS_PACKAGE_PATH": "${workspaceFolder}/xiaoli_application_ros2/install/homi_speech_interface/share:${env:ROS_PACKAGE_PATH}"}, "justMyCode": false, "stopOnEntry": false, "python": "/usr/bin/python3"}], "inputs": [{"id": "configFile", "description": "选择配置文件", "type": "pickString", "options": ["${workspaceFolder}/xiaoli_application_ros2/src/launch_package/configs/robot_config.yaml", "${workspaceFolder}/xiaoli_application_ros2/back/network_config.yaml"], "default": "${workspaceFolder}/xiaoli_application_ros2/src/launch_package/configs/robot_config.yaml"}, {"id": "logLevel", "description": "选择日志级别", "type": "pickString", "options": ["DEBUG", "INFO", "WARN", "ERROR", "FATAL"], "default": "INFO"}]}