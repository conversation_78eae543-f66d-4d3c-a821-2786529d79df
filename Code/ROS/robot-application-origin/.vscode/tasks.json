{"version": "2.0.0", "tasks": [{"label": "Build Network Dependencies", "type": "shell", "command": "colcon", "args": ["build", "--packages-select", "homi_speech_interface", "network", "--cmake-args", "-DCMAKE_EXPORT_COMPILE_COMMANDS=1"], "options": {"cwd": "${workspaceFolder}/xiaoli_application_ros2"}, "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Build Network Package Only", "type": "shell", "command": "colcon", "args": ["build", "--packages-select", "network", "--cmake-args", "-DCMAKE_EXPORT_COMPILE_COMMANDS=1"], "options": {"cwd": "${workspaceFolder}/xiaoli_application_ros2"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Run Network Node", "type": "shell", "command": "python3", "args": ["src/network/network/network_node.py", "--ros-args", "--params-file", "src/launch_package/configs/robot_config.yaml"], "options": {"cwd": "${workspaceFolder}/xiaoli_application_ros2", "env": {"ROS_DOMAIN_ID": "0", "PYTHONPATH": "${workspaceFolder}/xiaoli_application_ros2/src", "NETWORK_TEST_MODE": "1"}}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}, "problemMatcher": []}, {"label": "Check Config File", "type": "shell", "command": "cat", "args": ["src/launch_package/configs/robot_config.yaml"], "options": {"cwd": "${workspaceFolder}/xiaoli_application_ros2"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Setup ROS2 Environment", "type": "shell", "command": "bash", "args": ["-c", "source /opt/ros/foxy/setup.bash && echo 'ROS2 environment loaded'"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Check Network Node", "type": "shell", "command": "python3", "args": ["-c", "import sys; sys.path.insert(0, 'src'); exec(open('src/network/network/network_node.py').read()); print('✅ Network node syntax OK')"], "options": {"cwd": "${workspaceFolder}/xiaoli_application_ros2"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}