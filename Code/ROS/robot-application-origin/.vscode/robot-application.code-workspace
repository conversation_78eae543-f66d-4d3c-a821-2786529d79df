{"folders": [{"path": ".."}, {"path": "../../homiSDK"}, {"path": "../../../../../worktrees"}], "settings": {"files.associations": {"mutex": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "strstream": "cpp", "*.tcc": "cpp", "bitset": "cpp", "chrono": "cpp", "codecvt": "cpp", "complex": "cpp", "condition_variable": "cpp", "cstdint": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "filesystem": "cpp", "functional": "cpp", "iterator": "cpp", "map": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "set": "cpp", "string": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "hash_map": "cpp", "hash_set": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "ostream": "cpp", "shared_mutex": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "cfenv": "cpp", "cinttypes": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "valarray": "cpp", "variant": "cpp", "bit": "cpp"}, "workbench.colorCustomizations": {"statusBar.noFolderBackground": "#5f00af", "statusBar.debuggingBackground": "#5f00af", "statusBar.debuggingForeground": "#ffffff", "editor.selectionBackground": "#f54813", "editor.selectionHighlightBackground": "#139bf5", "editorCursor.foreground": "#ff0015", "terminalCursor.foreground": "#FF0000"}, "ros.distro": "humble", "workbench.editor.alwaysShowEditorActions": true, "search.followSymlinks": false, "search.quickOpen.history.filterSortOrder": "recency", "files.exclude": {"**/*sync-conf*": true, "cmcc_audio_ros1": true, "deeprob_ws_ctrl": true, "deeprobots_application_ros1": true, "include": true, "lib": true, "log": true}, "marscode.chatLanguage": "cn", "marscode.codeCompletionPro": {"enableCodeCompletionPro": true}, "marscode.enableInlineCommand": true}}