#!/bin/bash
###
 # @Author: 高亚军 <EMAIL>
 # @Date: 2025-04-21 09:25:41
 # @LastEditors: 高亚军 <EMAIL>
 # @LastEditTime: 2025-04-21 22:34:53
 # @FilePath: \robot-application\deeprob_ws_ctrl\uwb_ws_server\run_uwb_server.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 

g_root_of_script=$(dirname $(readlink -f "$0"))
g_install_path=/usr/bin/cmcc_robot/install

# 导出GPIO153
echo 153 > /sys/class/gpio/export
# 设置GPIO153为输出模式
echo out > /sys/class/gpio/gpio153/direction
# 设置GPIO153的值为0
echo 0 > /sys/class/gpio/gpio153/value


# 导出GPIO0
echo 0 > /sys/class/gpio/export
# 设置GPIO153为输出模式
echo out > /sys/class/gpio/gpio0/direction
# 设置GPIO153的值为0
echo 0 > /sys/class/gpio/gpio0/value


# 导出GPIO106
echo 106 > /sys/class/gpio/export
# 设置GPIO153为输出模式
echo out > /sys/class/gpio/gpio106/direction
# 设置GPIO153的值为0
echo 0 > /sys/class/gpio/gpio106/value

sudo LD_LIBRARY_PATH=${g_install_path}/env/lib ${g_root_of_script}/uwb_ws_server
