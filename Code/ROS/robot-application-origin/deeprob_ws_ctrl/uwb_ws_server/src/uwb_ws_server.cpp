#include <iostream>
#include <fcntl.h>
#include <errno.h>
#include <termios.h>
#include <unistd.h>
#include <cstring>
#include <chrono>
#include <thread>
#include <cstdint>
#include <ctime>
#include <vector>
#include <arpa/inet.h>
#include <cmath>
#include "libWebSocket.h"
#include <jsoncpp/json/json.h>
#include <sstream>
#include <iomanip>

using namespace std;
using namespace WS;

#define WEBSOCKET_CON_TIMEOUT  30                            //webSocket 连接超时时间 30s
#define WEBSOCKET_CON_URL      "ws://*************:19002"    //webSocket url

int serial_right_fd_ = 0;
int serial_left_fd_ = 0;
int serial_front_fd_ = 0;

typedef struct {
    string id;
    double linear = 0.0;
    double angular = 0.0;
} uwbData;

uwbData uwbDataRight;
uwbData uwbDataLeft;
uwbData uwbDataFront;

unsigned long getCurrentTimeStramp() {
    // 使用high_resolution_clock获取当前时间点
    auto now = std::chrono::high_resolution_clock::now();
    // 将时间点转换为time_t以便转换为本地时间
    auto time_now = std::chrono::high_resolution_clock::to_time_t(now);
    // 转换为本地时间
    std::tm now_tm = *std::localtime(&time_now);
    // 输出时间戳
    return std::mktime(&now_tm);
}

unsigned long currentTimeStramp_ = getCurrentTimeStramp();
bool bConnected_ = false;
int nConnectIndex_ = 0;

std::pair<float, float> parseData(const uint8_t *buf, int n) {
    float distance_m = 0.0f;
    float angle_deg = 0.0f;
    if (n >= 27) {
        if(bConnected_)  {
            Json::Value value;
            Json::Value params;
            value["client_type"] = "deeprobot";
            value["target_client"] = "nvidia";
            value["action"] = "uwb_data";
            params["distance"] = 0;
            params["angle"] = 0;
            value["params"] = params; 
            WS_Send(value.toStyledString().c_str(), nConnectIndex_);
        }
        std::cout << "Distance: " << distance_m << " m, " << "Angle: " << angle_deg << " degrees" << std::endl;
    }
    return {distance_m, angle_deg};
}

bool parse_line(const std::string& line, double& linearSpeed,
    double& angularSpeed, 
    std::string& label_id) {
        std::vector<std::string> tokens;
        std::istringstream iss(line);
        std::string token;

        // 分割令牌
        while(std::getline(iss, token, ' ')) {
            size_t comma_pos;
            while((comma_pos = token.find(',')) != std::string::npos) {
                tokens.push_back(token.substr(0, comma_pos));
                token = token.substr(comma_pos+1);
            }
            tokens.push_back(token);
        }

        // 解析逻辑
        int d_pos = -1, a_pos = -1, id_pos = -1;
        for(size_t i=0; i<tokens.size(); ++i) {
            if(tokens[i] == "D:") d_pos = i;
            if(tokens[i] == "A:") a_pos = i;
            if(tokens[i].find("ID[0x") != std::string::npos) id_pos = i;
        }

        if(d_pos != -1 && a_pos != -1 && id_pos != -1) {
            label_id = tokens[id_pos].substr(5, 2);
            linearSpeed = std::stod(tokens[d_pos+1]);
            angularSpeed= std::stod(tokens[a_pos+1]);
            return true;
        }
        return false;
    }


void setup_serial_port(int& serial_fd_,  const char* device, speed_t baud_rate) {
    // 打开串口设备
    serial_fd_ = open(device, O_RDWR | O_NOCTTY | O_NONBLOCK);
    if (serial_fd_ == -1) {
    }
    // 获取当前串口配置
    struct termios tty;
    if(tcgetattr(serial_fd_, &tty) != 0) {
        close(serial_fd_);
    }

    // 配置串口参数
    tty.c_cflag &= ~PARENB;   // 无奇偶校验
    tty.c_cflag &= ~CSTOPB;   // 1位停止位
    tty.c_cflag &= ~CSIZE;    // 清除数据位掩码
    tty.c_cflag |= CS8;       // 8位数据位
    tty.c_cflag &= ~CRTSCTS;  // 无硬件流控
    tty.c_cflag |= CREAD | CLOCAL; // 启用接收，忽略控制线

    tty.c_lflag &= ~ICANON;   // 非规范模式
    tty.c_lflag &= ~ECHO;     // 禁用回显
    tty.c_lflag &= ~ECHOE;
    tty.c_lflag &= ~ECHONL;
    tty.c_lflag &= ~ISIG;     // 禁用信号

    tty.c_iflag &= ~(IXON | IXOFF | IXANY); // 禁用软件流控
    tty.c_iflag &= ~(IGNBRK|BRKINT|PARMRK|ISTRIP|INLCR|IGNCR|ICRNL);

    tty.c_oflag &= ~OPOST;    // 原始输出模式
    tty.c_oflag &= ~ONLCR;

    // 设置超时：100ms等待时间，最多读取255字节
    tty.c_cc[VTIME] = 1;     // 0.1秒超时
    tty.c_cc[VMIN]  = 0;     // 最小读取字节数

    // 设置波特率
    cfsetispeed(&tty, baud_rate);
    cfsetospeed(&tty, baud_rate);

    // 应用配置
    if (tcsetattr(serial_fd_, TCSANOW, &tty) != 0) {
        close(serial_fd_);
    }
}

void process_data(const char* data, uwbData& stUwbData) {
    std::stringstream ss(data);
    std::string line;
    while(std::getline(ss, line, '\n')) {
        double linearSpeed = 0.0;
        double angularSpeed = 0.0;
        std::string label_id;
        if(parse_line(line, linearSpeed, angularSpeed, label_id)) {
            //std::cerr << "line:"  << line <<std::endl;
            //std::cerr << "linear:"  << linearSpeed << "    angular:" << angularSpeed <<  "   id:" << label_id <<std::endl;
            //if(label_id == "11"){
                stUwbData.id = label_id;
                stUwbData.linear = linearSpeed;
                stUwbData.angular = angularSpeed;
            //}
            /*if(label_id == "22"){
                uwbDataRight.id = label_id;
                uwbDataRight.linear = linearSpeed;
                uwbDataRight.angular = angularSpeed;
            }
            if(label_id == "33"){
                uwbDataFront.id = label_id;
                uwbDataFront.linear = linearSpeed;
                uwbDataFront.angular = angularSpeed;
            }*/
        }
    }
}

void notifyWsMsgCallback(void *handle, const char *msg, int index){
    nConnectIndex_ = index;
    std::cout << "notifyWsMsgCallback: " << msg << std::endl;
    currentTimeStramp_ = getCurrentTimeStramp();

    Json::Reader reader;
    Json::Value value;  
    if (false == reader.parse(msg, value)) {
        return;
    }
    if(!value["type"].isNull()) {
       string strType = value["type"].asString();
       if ("connect_success" == strType) {
            bConnected_ = true;
            std::this_thread::sleep_for(std::chrono::milliseconds(20));
            Json::Value value;
            Json::Value params;
            value["client_type"] = "uwb";
            WS_Send(value.toStyledString().c_str(), nConnectIndex_);
       }
    }
}

int main() {
    //WS服务启动
    WS_Init(EN_WS_ClIENT, 19002);
    //设置接受msg的回调函数
    WS_SetMsgCallback(notifyWsMsgCallback, nullptr);

    WS_Connect(WEBSOCKET_CON_URL);
    const char *portnameRight = "/dev/ttyS9";
    const char *portnameLeft = "/dev/ttyS8";
    const char *portnameFront = "/dev/ttyS0";
    // 打开并配置串口
    setup_serial_port(serial_right_fd_, portnameRight, B460800);
    setup_serial_port(serial_left_fd_, portnameLeft, B460800);
    setup_serial_port(serial_front_fd_, portnameFront, B460800);
    if (serial_right_fd_ < 0 && serial_left_fd_ < 0 && serial_front_fd_ < 0) {
        std::cerr << "Error opening " << ": " << strerror(errno) << std::endl;
        return 1;
    }
  
    while (true) {
        uwbDataRight.linear = 0.0;
        uwbDataRight.angular = 0.0;
        uwbDataLeft.linear = 0.0;
        uwbDataLeft.angular = 0.0;
        uwbDataFront.linear = 0.0;
        uwbDataFront.angular = 0.0;
        char buffer[256];
        //只要接收到一个uwb数据就置位
        bool bFlag = false;
        //右基站
        ssize_t n = read(serial_right_fd_, buffer, sizeof(buffer)-1);
        if(n > 0) {
            bFlag = true;
            buffer[n] = '\0';
            process_data(buffer, uwbDataRight);
        } else if(n < 0 && errno != EAGAIN) {
            std::cerr << "Error reading from " << ": " << strerror(errno) << std::endl;
        }
        //左基站
        n = read(serial_left_fd_, buffer, sizeof(buffer)-1);
        if(n > 0) {
            bFlag = true;
            buffer[n] = '\0';
            process_data(buffer, uwbDataLeft);
	    //std::cerr << "read msg left" << buffer << std::endl;
        } else if(n < 0 && errno != EAGAIN) {
            std::cerr << "Error reading from " << ": " << strerror(errno) << std::endl;
        }
        //前基站
        n = read(serial_front_fd_, buffer, sizeof(buffer)-1);
        if(n > 0) {
            bFlag = true;
            buffer[n] = '\0';
            process_data(buffer, uwbDataFront);
	    //std::cerr << "read msg front" << buffer << std::endl;
        } else if(n < 0 && errno != EAGAIN) {
            std::cerr << "Error reading from " << ": " << strerror(errno) << std::endl;
        }

        if(bConnected_ && bFlag)  {
            Json::Value value;
            Json::Value params;
            value["client_type"] = "uwb";
            value["target_client"] = "nvidia";
            value["action"] = "uwb_data_array";

            Json::Value valueRight;
            Json::Value valueLeft;
            Json::Value valueFront;

            valueRight["distance"] = uwbDataRight.linear;
            valueRight["angle"] = uwbDataRight.angular;
            params["right"] = valueRight;

            valueLeft["distance"] = uwbDataLeft.linear;
            valueLeft["angle"] = uwbDataLeft.angular;
            params["left"] = valueLeft;

            valueFront["distance"] = uwbDataFront.linear;
            valueFront["angle"] = uwbDataFront.angular;
            params["front"] = valueFront;

            value["params"] = params; 
            WS_Send(value.toStyledString().c_str(), nConnectIndex_);
            // std::cerr << value.toStyledString().c_str() << std::endl;

        }

        std::this_thread::sleep_for(std::chrono::milliseconds(30));
        if((getCurrentTimeStramp() - currentTimeStramp_) > WEBSOCKET_CON_TIMEOUT) {
            bConnected_ = false;
            std::cerr << "websocket reconnect" << std::endl;
            WS_Connect(WEBSOCKET_CON_URL);
        }
    }
    if(serial_right_fd_ >=0 ) {
        close(serial_right_fd_);
    }
    if(serial_left_fd_ >=0 ) {
        close(serial_right_fd_);
    }
    if(serial_front_fd_ >=0 ) {
        close(serial_right_fd_);
    }
    return 0;
}

