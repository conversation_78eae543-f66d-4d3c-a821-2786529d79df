#!/bin/bash

work_dir=$1
work_dir=${work_dir}/uwb_ws_server
echo "work_dir = $work_dir"
arch=$(uname -m)
echo "cpu arch = $arch"
usrname=$(whoami)
echo "usrname = $usrname"
cp -r ${work_dir}/lib/${arch}/* /usr/lib/
cp -r ${work_dir}/include/* /usr/include/ 

set -e


cd ${work_dir}
# 设置项目名称和构建目录
PROJECT_NAME="uwb_ws_server"
BUILD_DIR="build"

# 创建构建目录（如果不存在）
if [ ! -d "$BUILD_DIR" ]; then
    mkdir "$BUILD_DIR"
fi


# 进入构建目录
cd "$BUILD_DIR" || exit

# 运行 CMake 配置项目
cmake .. -DCMAKE_BUILD_TYPE=Release

# 编译项目
make

# 可选：安装项目
sudo make install

# 输出编译结果
if [ $? -eq 0 ]; then
    echo "$PROJECT_NAME compile successed !!"
else
    echo "$PROJECT_NAME compile failed!!"
fi

cd ..

chmod +x ./run_uwb_server.sh
sudo cp uwb_start.service  /etc/systemd/system/