cmake_minimum_required(VERSION 3.10)
#项目名称
PROJECT(uwb_ws_server)  

set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}")
#添加C++11支持及其他选项 
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}  -std=c++11 -g  -fPIC") 

#获取当前路径的上层路径
set(PROJECT_INIT_PATH ${CMAKE_CURRENT_SOURCE_DIR})
message("DIR=" ${PROJECT_INIT_PATH})

#添加头文件
include_directories(
    ${PROJECT_INIT_PATH}/include
    ${PROJECT_INIT_PATH}/src
)

#将所有的源文件
file(GLOB DIR_LIB_SRCS
	"${CMAKE_CURRENT_SOURCE_DIR}/src/*"
)

if (${CMAKE_HOST_WIN32})
    set(LIB_PATH "${PROJECT_INIT_PATH}/lib/win/${CMAKE_CXX_FLAGS}")
elseif(${CMAKE_HOST_UNIX})
    set(LIB_PATH "${PROJECT_INIT_PATH}/lib/${CMAKE_HOST_SYSTEM_PROCESSOR}")
elseif (${CMAKE_SYSTEM} MATCHES "FreeBSD|OpenBSD")
endif()

message("LIB_DIR=" ${LIB_PATH})

find_library(LIB_DIR NAMES WebSocket PATHS ${LIB_PATH})

add_executable(uwb_ws_server
  src/uwb_ws_server.cpp 
)

target_link_libraries(uwb_ws_server 
  PRIVATE ${LIB_DIR}
  jsoncpp 
  pthread
)

install(TARGETS
  uwb_ws_server
  DESTINATION ${PROJECT_INIT_PATH}/install/${PROJECT_NAME}
)

install(PROGRAMS
  run_uwb_server.sh
  DESTINATION ${PROJECT_INIT_PATH}/install/${PROJECT_NAME}
)
