#!/bin/bash
#work_dir = $(cd $(dirname $0); pwd)
# sudo apt update
# sudo apt install libasound2-dev
# sudo apt install libgstreamer1.0-dev libgstreamer-plugins-base1.0-dev

work_dir=$(pwd)
echo "work_dir = $work_dir"
arch=$(uname -m)
echo "cpu arch = $arch"
usrname=$(whoami)
echo "usrname = $usrname"
cp -r ${work_dir}/lib/${arch}/* /usr/lib/
cp -r ${work_dir}/include/* /usr/include/ 
chmod 777 ${work_dir}/config_robotdog.xml
mkdir -p /home/<USER>/.config
cp -r ${work_dir}/config_robotdog.xml /home/<USER>/.config
cp -r ${work_dir}/config/map_points.json /home/<USER>/.config

chmod u+x ${work_dir}/src/homi_speech/scripts/display_assistant_idle.sh
dos2unix  ${work_dir}/src/homi_speech/scripts/display_assistant_idle.sh
chmod u+x ${work_dir}/src/homi_speech/scripts/display_assistant_wakeup.sh
dos2unix  ${work_dir}/src/homi_speech/scripts/display_assistant_wakeup.sh
chmod u+x ${work_dir}/src/homi_speech/scripts/get_capture_card.sh
dos2unix  ${work_dir}/src/homi_speech/scripts/get_capture_card.sh
chmod u+x ${work_dir}/src/homi_speech/scripts/get_playback_card.sh
dos2unix  ${work_dir}/src/homi_speech/scripts/get_playback_card.sh
chmod u+x ${work_dir}/src/homi_speech/src/upload_image.py
dos2unix  ${work_dir}/src/homi_speech/src/upload_image.py


cp  -rf ${work_dir}/resource /home/<USER>/
set -e

source /opt/ros/noetic/setup.bash
source ~/.bashrc

catkin_make -j4

chmod +x ./service/run_deep_rob.sh
sudo chmod 777 /home/<USER>/.config -R
sudo cp ./service/run_deep_rob.service  /etc/systemd/system/


#sudo cp ./service/sensorhub.service  /etc/systemd/system/

sudo cp ./service/expression.service  /etc/systemd/system/
mkdir -p /home/<USER>/.config/autostart
sudo cp ./service/autostartexpression.sh.desktop  /home/<USER>/.config/autostart/autostartexpression.sh.desktop
sudo cp ./service/autostartexpression.sh  /home/<USER>/autostartexpression.sh
sudo cp ./service/autostartexpression2.sh  /home/<USER>/autostartexpression2.sh
sudo cp ./service/updateexpression.sh /home/<USER>/updateexpression.sh
sudo cp ./service/updateexpression2.sh /home/<USER>/updateexpression2.sh
sudo cp ./service/updateexpression_fork.sh  /home/<USER>/updateexpression_fork.sh

chmod +x /home/<USER>/autostartexpression.sh
chmod +x /home/<USER>/autostartexpression2.sh
chmod +x /home/<USER>/updateexpression.sh
chmod +x /home/<USER>/updateexpression2.sh
chmod +x /home/<USER>/updateexpression_fork.sh


killall -9 SensorHubNode &
mkdir -p /home/<USER>/sensorHub
sudo cp ./service/SensorHubNode  /home/<USER>/sensorHub/
sudo cp ./service/sensorhub.service  /etc/systemd/system/


systemctl daemon-reload

sudo systemctl enable run_deep_rob.service

sudo systemctl start run_deep_rob.service

sudo systemctl enable sensorhub.service
sudo systemctl start sensorhub.service

sudo systemctl enable expression.service
sudo systemctl start expression.service


# 查看启动的状态
systemctl status run_deep_rob.service &
systemctl status sensorhub.service &
systemctl status expression.service &

# 如果failed，使用以下命令查看日志
#journalctl -u prometheus

#ldconfig

