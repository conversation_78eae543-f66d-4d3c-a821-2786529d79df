#!/bin/bash
#work_dir = $(cd $(dirname $0); pwd)
# sudo apt update
# sudo apt install libasound2-dev
# sudo apt install libgstreamer1.0-dev libgstreamer-plugins-base1.0-dev

work_dir=$(pwd)
echo "work_dir = $work_dir"
arch=$(uname -m)
echo "cpu arch = $arch"
usrname=$(whoami)
echo "usrname = $usrname"

IP=*************

ping -c 1 $IP > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "Ping to $IP was successful."
    export ROS_MASTER_URI=http://*************:11311
else
    echo "Failed to ping $IP."
    export ROS_MASTER_URI=http://*************:11311
fi
export ROS_IP=*************

source /opt/ros/noetic/setup.bash
catkin_make -DCATKIN_WHITELIST_PACKAGES="andlink"

source /mine/robot-application/deeprobots_application_ros1/devel/setup.bash


systemctl daemon-reload

sudo cp ./service/andlink/andlink.service  /etc/systemd/system/
sudo systemctl enable andlink.service
systemctl start andlink.service


sudo cp ./service/andlink/fiveg.service  /etc/systemd/system/
sudo systemctl enable fiveg.service
systemctl start fiveg.service


# 查看启动的状态
systemctl status andlink.service  &
systemctl status fiveg.service  &


# 如果failed，使用以下命令查看日志
#journalctl -u prometheus

#ldconfig

