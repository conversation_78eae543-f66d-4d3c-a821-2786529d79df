#!/bin/bash
#work_dir = $(cd $(dirname $0); pwd)
# sudo apt update
# sudo apt install libasound2-dev
# sudo apt install libgstreamer1.0-dev libgstreamer-plugins-base1.0-dev

work_dir=$(pwd)
echo "work_dir = $work_dir"
arch=$(uname -m)
echo "cpu arch = $arch"
usrname=$(whoami)
echo "usrname = $usrname"
cp -r ${work_dir}/lib/${arch}/* /usr/lib/
cp -r ${work_dir}/include/* /usr/include/ 

source /opt/ros/noetic/setup.bash

catkin_make -j4

chmod +x ./service/nvidia/run_nvidia_rob_ros1.sh
sudo cp ./service/nvidia/run_nvidia_rob_ros1.service  /etc/systemd/system/

systemctl daemon-reload

sudo systemctl enable run_nvidia_rob_ros1.service
sudo systemctl start run_nvidia_rob_ros1.service

# 查看启动的状态
systemctl status run_nvidia_rob_ros1.service &

# 如果failed，使用以下命令查看日志
#journalctl -u prometheus

#ldconfig

