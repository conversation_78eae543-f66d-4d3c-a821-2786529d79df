#!/bin/bash
###
 # @Author: 高亚军 <EMAIL>
 # @Date: 2024-09-29 15:15:19
 # @LastEditors: 高亚军 <EMAIL>
 # @LastEditTime: 2024-09-29 15:17:57
 # @FilePath: \robot-application\deeprobots_application_ros1\service\run_deep_rob.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 

source ~/.bashrc 

IP=*************

sleep 10

function run_sudo_command()
 {
    local password="$1"
    local command="$2"
    echo "$password" | sudo -S sh -c "$command"
    if [ $? -ne 0 ]; then
        error_message=$( { echo "$password"; echo "$command"; } | sudo -S sh -c "$command" 2>&1 )
        echo "An error occurred: $error_message"
    else
        echo "Command executed successfully."
    fi
}
password="temppwd"
command="systemctl restart systemd-resolved.service "
run_sudo_command "$password" "$command"

ping -c 1 $IP > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "Ping to $IP was successful."
    export ROS_MASTER_URI=http://*************:11311
else
    echo "Failed to ping $IP."
    export ROS_MASTER_URI=http://*************:11311
fi
export ROS_IP=*************

SCRIPT_DIR=$(dirname "$0")
# 将目录名转换为绝对路径
SCRIPT_DIR=$(cd "$SCRIPT_DIR" && pwd)
echo "$SCRIPT_DIR"

# aplay -D plughw:4,0 /home/<USER>/robot-application/deeprobots_application_ros1/resource/audio/321茄子.wav

source ${SCRIPT_DIR}/../devel/setup.bash
echo "start roslaunch launch_package speech_ctrl_robdog.launch"
roslaunch launch_package speech_ctrl_robdog.launch &

# start ovd lib
source ${SCRIPT_DIR}/start_ovd.sh

echo "start log upload to platform!!!"
echo "python3 ${SCRIPT_DIR}/upload_log/hongkong.py"
python3 ${SCRIPT_DIR}/upload_log/hongkong.py &

# echo "22222222222222222222222222222222222222"
# echo "start sleep 5"
# sleep 5 &
# #pid=$(sleep 5 &)
# #wait $pid
# echo "start /home/<USER>/sensorHub/SensorHubNode"

# /home/<USER>/sensorHub/SensorHubNode &

# process1_name=SensorHubNode

# process_exists() {
#     pidCount1=`ps -aux|grep $process1_name |grep -v "grep"|grep -v "Z" | wc -l`
#     if [ "$pidCount1" = "0" ]; then
#             echo "$process1_name 进程未运行\n"
#             /home/<USER>/sensorHub/SensorHubNode &
#             echo "$process1_name 进程重新拉取"
#     else
#             echo "$process1_name 进程运行中\n"
#     fi
#     sleep 5
# }

while true; do
    sleep 5
    #TODO
        # process_exists
done