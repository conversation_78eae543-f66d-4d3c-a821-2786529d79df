import urllib.parse
from collections import OrderedDict
import hmac
import base64
import hashlib
import random
import time
import requests
import json
import os
import sys
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

SEPARATOR = "&"
url = 'https://homibot.onehome.hk.chinamobile.com:9443/robot/business/api/device/robot/client/log/requestUploadLog'

def generate_nonce(length=6):
    """生成指定长度的随机字符串"""
    characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    return ''.join(random.choice(characters) for _ in range(length))

def encode_special_characters(s):
    safe_chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_.~'
    encoded_string = ''

    for char in s:
        if char in safe_chars:
            encoded_string += char
        else:
            # 将字符编码为UTF-8字节序列
            utf8_bytes = char.encode('utf-8')
            # 逐个字节转换为 %XY 格式
            encoded_string += ''.join(f'%{byte:02X}' for byte in utf8_bytes)

    return encoded_string

def sign(secret='BBmsdeMhsJH213de', string_to_sign=None):
    if not secret:
        raise ValueError("secret参数为空！")
    if not string_to_sign:
        raise ValueError("stringToSign参数为空！")

    try:
        mac = hmac.new(secret.encode('utf-8'), string_to_sign.encode('utf-8'), hashlib.sha256)
        signature = base64.b64encode(mac.digest()).decode('utf-8')
        return signature
    except Exception as e:
        logging.error(f"sign error: {e}")
        return ""

def upload_file(write_url, file_path):
    try:
        with open(file_path, 'rb') as file:
            # 设置PUT请求的超时时间为30秒
            response = requests.put(write_url, data=file, timeout=30)
            if response.status_code in (200, 201):
                logging.info("文件 %s 上传成功", file_path.name)
                return True, file_path.name, None
            else:
                logging.error("文件 %s 上传失败，状态码: %s", file_path.name, response.status_code)
                return False, file_path.name, response.text
    except requests.Timeout:
        # 如果PUT请求超时，则记录错误信息并返回False
        logging.error("文件 %s 上传超时", file_path.name)
        return False, file_path.name, "请求超时"
    except Exception as e:
        logging.error("上传文件 %s 时发生错误: %s", file_path.name, e)
        return False, file_path.name, str(e)

def generate_query_string(params, is_encode_kv):
    canonicalize_query_string = []
    if is_encode_kv:
        params = OrderedDict(sorted(params.items()))
    for key, value in params.items():
        if is_encode_kv:
            canonicalize_query_string.append(f"{encode_special_characters(key)}={encode_special_characters(value)}")
        else:
            canonicalize_query_string.append(f"{key}={value}")

    return "&".join(canonicalize_query_string)

def get_write_url():
    nonce = generate_nonce()
    timestamp = str(int(time.time() * 1000))  # 当前时间戳，单位为毫秒
    response1 = {
        "deviceId": '1830004229212345670000029',
        "logTimestamp": timestamp,
        "source": 1
    }

    response2 = json.dumps(response1)
    logging.info("response2:%s",response2)
    # print(response2)
    params = {
        'tenantCode': '97823732',
        'accessKey': 'mskfwerhwsd',
        "nonce": nonce,
        "timestamp": timestamp,
        "content": response2
    }
    canonicalizeQueryString = generate_query_string(params, True)
    logging.info("first canonicalizeQueryString:%s",canonicalizeQueryString)
    # print('first canonicalizeQueryString:', canonicalizeQueryString)
    signString = 'POST' + SEPARATOR + encode_special_characters('/') + SEPARATOR + encode_special_characters(canonicalizeQueryString)
    logging.info("second canonicalizeQueryString:%s",signString)
    # print('second canonicalizeQueryString:', signString)
    signature = sign('BBmsdeMhsJH213de' + SEPARATOR, signString)
    print(encode_special_characters('/'))
    print(signature)
    logging.info("signs:%s",signature)
    headers = {
        "VSS-Tenant-Code": "97823732",
        "VSS-Nonce": nonce,
        "Vss-timestamp": timestamp,
        "VSS-Sign": signature,
        "VSS-Access-Key": "mskfwerhwsd"
    }
    a = requests.post(url=url, headers=headers, json=response1)
    print(a.json())
    try:
        response_json = a.json()
        write_url = response_json.get('data', {}).get('item', {}).get('writeUrl')
        logging.info("write_url:%s",write_url)
        # print(write_url)
        if not write_url:
            logging.error("write_url not found in response")
            return None
        return write_url
    except ValueError as e:
        logging.error(f"Failed to decode JSON: {e}")
        return None

def upload_log_files_sequentially(log_directory):
    log_files = list(log_directory.glob('*.log'))
    total_files = len(log_files)
    uploaded_count = 0

    for index, file_path in enumerate(log_files, start=1):
        try:
            logging.info("正在处理第 %d/%d 个文件: %s", index, total_files, file_path.name)
            write_url = get_write_url()
            if not write_url:
                logging.error("无法获取 write_url，跳过文件 %s", file_path.name)
                continue

            success, filename, error_msg = upload_file(write_url, file_path)
            if success:
                uploaded_count += 1
                logging.info("文件 %s 成功上传 (%d/%d)", filename, uploaded_count, total_files)
            else:
                logging.error("文件 %s 上传失败，原因: %s", filename, error_msg)

        except KeyError as e:
            logging.error("JSON解析错误: 键 %s 不存在", e)
        except Exception as e:
            logging.error("上传日志过程中发生未知错误: %s", e)

        # if uploaded_count < index:  # 如果有文件未成功上传，则暂停处理下一批文件
        #     break

def main_loop():
    hour_in_seconds = 60 * 60
    max_retries = 5
    retry_delay_seconds = 60
    log_directory = Path('/home/<USER>/.ros/log/latest/')

    while True:
        retries = 0

        try:
            # 统计.log文件数量
            log_files = list(log_directory.glob('*.log'))
            if not log_files:
                logging.info("没有找到需要上传的日志文件")
                time.sleep(hour_in_seconds)  
                continue

            logging.info("发现 %d 个日志文件待上传", len(log_files))
            upload_log_files_sequentially(log_directory)

        except KeyboardInterrupt:
            logging.info("crtl c interrupt !")
            sys.exit(0)
        except Exception as e:
            retries += 1
            logging.error("主循环中发生未知错误: %s, 尝试重试... (%d/%d)", e, retries, max_retries)
            if retries >= max_retries:
                logging.error("达到最大重试次数，退出循环")
                break

            time.sleep(retry_delay_seconds)

        logging.info("等待下一次上传...")
        time.sleep(hour_in_seconds)

if __name__ == '__main__':
    main_loop()
