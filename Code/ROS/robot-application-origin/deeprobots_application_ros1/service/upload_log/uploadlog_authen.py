import urllib.parse
from collections import OrderedDict
import hmac
import base64
import hashlib
import random
import time
import requests
import json
SEPARATOR = "&"
url='http://36.140.17.36:10000/robot/business/api/device/robot/client/log/requestUploadLog'
def generate_nonce(length=6):
    """生成指定长度的随机字符串"""
    characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    return ''.join(random.choice(characters) for _ in range(length))
                   
def _encode_special_characters(value): #not used
    # 模拟Java代码中encodeSpecialCharacters的功能
    return urllib.parse.quote(value, safe='')

def generate_query_string(params, is_encode_kv): #not used
    canonicalize_query_string = []
    if is_encode_kv:
        params = OrderedDict(sorted(params.items()))
    for key, value in params.items():
        if is_encode_kv:
            canonicalize_query_string.append(f"{encode_special_characters(key)}={encode_special_characters(value)}")
        else:
            canonicalize_query_string.append(f"{key}={value}")

    return "&".join(canonicalize_query_string)


def split_query_string(url):   #not used
    query_map = OrderedDict()

    try:
        parsed_url = urllib.parse.urlparse(url)
        pairs = parsed_url.query.split("&")

        for pair in pairs:
            idx = pair.find("=")
            key = pair[:idx] if idx > 0 else pair
            if key not in query_map:
                query_map[key] = urllib.parse.unquote(pair[idx + 1:])

    except Exception as e:
        print(f"split_query_string error: {e}")

    return query_map

def encode_url(url): #not used
    try:
        return urllib.parse.quote(url, encoding="UTF-8")
    except Exception as e:
        print(f"Url encode error: {e}")
        return None

def _encode_special_characters(value): #not used
    if value is not None:
        encoded_value = encode_url(value)
        if encoded_value is not None:
            return encoded_value.replace("+", "%20").replace("*", "%2A").replace("%7E", "~")
    return None

def encode_special_characters(s):
    safe_chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_.~'
    encoded_string = ''
    
    for char in s:
        if char in safe_chars:
            encoded_string += char
        else:
            # 将字符编码为UTF-8字节序列
            utf8_bytes = char.encode('utf-8')
            # 逐个字节转换为 %XY 格式
            encoded_string += ''.join(f'%{byte:02X}' for byte in utf8_bytes)
    
    return encoded_string

def sign(secret='sdfhweMhsJH213de', string_to_sign=None):
    if not secret:
        raise ValueError("secret参数为空!")
    if not string_to_sign:
        raise ValueError("stringToSign参数为空!")

    try:
        mac = hmac.new(secret.encode('utf-8'), string_to_sign.encode('utf-8'), hashlib.sha256)
        signature = base64.b64encode(mac.digest()).decode('utf-8')
        return signature
    except Exception as e:
        print(f"sign error: {e}")
        return ""
    
    
def main(httpMethod='POST',parameter=None):
# 示例使用
    canonicalizeQueryString = generate_query_string(params,True)
    print('first canonicalizeQueryString:',canonicalizeQueryString)
    signString = httpMethod+SEPARATOR+encode_special_characters('/')+SEPARATOR+encode_special_characters(canonicalizeQueryString)
    print('second canonicalizeQueryString:',signString)
    signature = sign('sdfhweMhsJH213de' + SEPARATOR, signString)
    return signature

if __name__ =='__main__':
    nonce = generate_nonce()
    timestamp = str(int(time.time() * 1000))  # 当前时间戳，单位为毫秒
    response1 = {

                "deviceId": '1830004229212345670000023',
                "logTimestamp": timestamp,
                "source": 1
            }
                
    response2=json.dumps(response1)
    print(response2)
    params ={
        'tenantCode':'97823732',
        'accessKey': 'ksdfwerhsdd',
        "nonce": nonce,
        "timestamp": timestamp,
        "content": response2
        }
    signs=main(parameter=params)
    print(encode_special_characters('/'))
    print(signs)
    headers = {
        "VSS-Tenant-Code": "97823732",
        "VSS-Nonce": nonce,
        "Vss-timestamp": timestamp,
        "VSS-Sign": signs,
        "VSS-Access-Key": "ksdfwerhsdd",
    }
    a=requests.post(url=url,headers=headers,json=response1)
    print(a.json())

#add log upload function
    
    response_json = a.json()
   
    if response_json.get('success', False):
       
        try:
            
            write_url = response_json['data']['item']['writeUrl']

            print("解析出的writeUrl:")
            print(write_url)            

            file_path = './test.log'  

            try:
                
                with open(file_path, 'rb') as file:
                    # 发送PUT请求
                    response = requests.put(write_url, data=file)
                    print(response.status_code)
                    
                    # 检查响应
                    if response.status_code == 200 or response.status_code == 201:
                        print("文件上传成功")
                        print("响应内容:", response.text)
                    else:
                        print(f"文件上传失败，状态码: {response.status_code}")
                        print("错误信息:", response.text)

            except Exception as e:
                print(f"发生错误: {e}")
                            
        except KeyError as e:
            print(f"JSON解析错误: 键 {e} 不存在")
    else:
        
        print("请求业务逻辑失败，原因可能是：")
        print(response_json.get('message', '未知原因'))





