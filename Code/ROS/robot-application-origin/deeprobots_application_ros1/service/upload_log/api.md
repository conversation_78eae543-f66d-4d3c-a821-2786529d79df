# API接口鉴权文档

# 设计目的

## 安全

由于接口暴露到了公网，所以存在较大的安全风险，一方面存在被恶意大量调用导致服务压力上升，无法响应正常请求的风险；另一方明存在伪造信息修改或窃取他人信息的风险。

1. **防篡改**：防止黑客通过抓包等方式获取到合法的请求体，并对请求体中个别参数进行修改后重新发送，达到修改或获取用户数据的行为，一般采用签名验证的方式进行预防。
2. **防重放**：防止黑客获取到合法请求后，大量进行调用，导致系统压力过大影响正常服务的行为，一般通过随机数和时间戳进行预防。
3. **认证**：通过凭证验证用户的身份
4. **授权**：完成认证后，系统会授予用户访问资源的权限，每次接口调用时，都会判断当前认证后的用户是否具有访问对应资源的权限。

## 租户

由于业务平台会对接多个第三方平台和应用，所以需要进行资源隔离，不同的组织（企业）共同使用平台提供的能力和资源。

1. **租户编码（tenantCode）**：平台为第三方组织和企业分配的标识；
2. **访问凭证（access）**：平台为第三方平台颁发的凭证，一个租户可以有多个访问凭证，一个访问凭证由accessKey和accessSecret组成
3. **应用（app）**：平台为第三方应用颁发的凭证，一个租户可以有多个应
   用，一个应用由appKey和appSecret组成

# 名词解释

## 参数

1. **平台参数**：与业务无关，但是在安全、统计等方面具有重要用途的参数，
2. **业务参数**：与业务相关的参数，是对应接口执行业务时必须包含的变量。
3. **参数字典（paramMap）**:由平台参数和业务参数共同组成的字典

# 接入方式

接入方通过邮件的方式，发送到[<EMAIL>](mailto:<EMAIL>)，邮件中说明所属部门和项目，接入应用的信息，用途，上线时间。

审批通过后，会通过邮件的方式颁发对应的凭证。

携参方式

平台对外提供http 接口，参数可以通过以下不同的载体发送到平台。

## url param

通过url可以携带参数，结构为：`http://server/path/document?name1=value1&name2=value2`，其中包含了name1: value1和name2: value2两个参数。

特殊的，参数如果要支持列表，参数值采用逗号 `,`进行分隔。

## header

http header中除了http协议标准参数外，还可以携带平台参数，为了区分，所有平台参数header名格式为：`VSS-{paramName驼峰转中划线}`。

规则为：对paramName按照驼峰进行分词，每个分词首字母大写后用中划线”-“进行拼接。

例如：参数名为 `deviceId`的参数，对应的header名为”`VSS-Device-Id`。

特殊的，参数如果要支持列表，参数值采用逗号”,”进行分隔。

## body: x-www-form-urlencoed

该ContentType的body格式与url param类似，例如：“`name1=value1&name2=value2`”，参数为name1: value1和name2: value2。

特殊的，参数如果要支持列表，参数值采用逗号”,”进行分隔。

## body: form-data

form-data的所有文本类型参数全部参与签名计算，文件类型字段不参与签名计算。

特殊的，参数如果要支持列表，参数值采用逗号”,”进行分隔。

## body: application/json

由于json不是key:value格式，所以使用content作为参数名，整个json字符串作为参数值，例如请求body为 `{"name1": "value1", "name2": "value2"}`，则参数为content: {”name1”: “value1”, “name2”: “value2”}

# 公共参数

| 参数名             | 必填         | 说明                                |
| ------------------ | ------------ | ----------------------------------- |
| tenantCode         | 是           | 租户编码，由家庭安防业务平台颁发    |
| nonce              | 是           | 随机值，长度不低于6                 |
| timestamp          | 是           | 请求时间戳，单位为毫秒              |
| sign               | 平台调用必填 | 签名，通过签名计算获得              |
| accessKey          | 平台调用必填 | 平台凭证                            |
| appKey             | app调用必填  | 应用凭证                            |
| authorizationToken | app调用必填  | jwt token                           |
| deviceId           | app调用必填  | 客户端唯一标识                      |
| deviceType         | app调用必填  | 设备类型, IOS, ANDROID, WEB，PC，TV |

# Sign签名计算

## 1. 生成参数字典

将请求的所有除了 `sign`以外的参数集合在一起，组合成一个Map，就形成了一个参数字典，按照参数名称的字典顺序进行排序。

## 2. 参数编码

1. 对排序之后的请求参数的名称和值分别用 UTF-8 字符集进行 URL 编码。该编码方式和一般采用的 `application/x-www-form-urlencoded` MIME 格式编码算法（比如 Java 标准库中的 `java.net.URLEncoder` 的实现）相似，编码的规则如下：
   * 对于字符 A\~Z、a\~z、0\~9 以及字符“-”、“_”、“.”、“\~”不编码
   * 对于其它字符编码成 `%XY` 的格式，其中 `XY` 是字符对应 ASCII 码的 16 进制表示。例如：英文的双引号（”）对 应的编码为 `%22`。
   * 对于扩展的 UTF-8 字符，编码成 `%XY%ZA…` 的格式。
   * 英文空格（ ）要编码成 `%20`，而不是加号（+）。
2. 将编码后的参数名称和值用英文等号（=）进行连接
3. 将等号连接得到的参数组合按排好的顺序依次使用“&”符号连接，前面拼接上http method name，即得到规范化请求字符串CanonicalizedQueryString。例如，对于GET请求http://{{bizBaseUrl}}/vss/biz/gateway/personal/appapi/group/getAllGroup，它的CanonicalizedQueryString为 `GET&%2F&appKey%3DYJj8D9OGyh9a%26authorizationToken%**********************************%26deviceId%3D0e603646254df389%26deviceType%3DANDROID%26nonce%3Dtuo%26tenantCode%3D26969249%26timestamp%3D1628578804270`
4. 对CanonicalizedQueryString再进行一次相同的编码。

## 3. 计算

1. 按照 RFC2104 的定义，计算待签名字符串 StringToSign 的 HMAC 值，密钥采用 `accessSecret + '&'`
2. 按照 Base64 编码规则把上面的 HMAC 值编码成字符串，即得到签名值（sign）
3. 将得到的签名值作为 sign参数添加到请求中

# App Token校验

## 获取token的途径

由于平台相关App接口依赖于和家亲平台，token的获取通过验证和家亲token有效后，发放平台token。

相关API接口

* [通过和家亲token登录](http://36.138.107.130:9988/business/doc.html#/robot-business-user/APP%E7%99%BB%E5%BD%95/loginByHjqTokenUsingPOST)
* [刷新token](http://36.138.107.130:9988/business/doc.html#/robot-business-user/APP%E7%99%BB%E5%BD%95/refreshTokenUsingPOST)

\

\

# 附录

## python 客户端鉴权示例

[test_authen.py 4524](attachments/3c4a36c7-3e6d-4b53-bd30-3d50474a1936.false)

\