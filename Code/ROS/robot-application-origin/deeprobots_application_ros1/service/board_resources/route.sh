#!/bin/bash

# 初始化最小 Metric 和对应的网关及接口
min_metric=99999
# min_gateway=""
min_interface=""

interface_wifi=wlan0
interface_5G=eth2
# interface_wifi=wlp2s0
# interface_5G=enp7s0f4u1u1
interface=wlan0

if [[ "$1" == "5g" ]]; then
    interface=$interface_5G
elif [[ "$1" == "wifi" ]]; then
    interface=$interface_wifi
fi
# start_5G=/home/<USER>/board_resources/5G/5G_ctl.sh enable

# 使用 route -n 命令获取所有路由条目，并筛选出默认路由
default_routes=$(route -n | grep 'UG')

# # 执行route -n命令并获取输出
# route_output=$(route -n)

# # 使用grep筛选出目标是0.0.0.0的行
# target_0_0_0_0=$(echo "$route_output" | grep '^0.0.0.0')

# # 进一步筛选出接口是network_interface的行
# gateway_data=$(echo "$target_0_0_0_0" | grep "$wifi_interface" )


# 遍历所有默认路由条目
while IFS= read -r line; do
    # 解析每行数据
    # destination=$(echo $line | awk '{print $1}')
    gateway_tmp=$(echo $line | awk '{print $2}')
    metric=$(echo $line | awk '{print $5}')
    interface_tmp=$(echo $line | awk '{print $8}')
	echo "gateway: $gateway_tmp"
    if [[ "$interface" == "$interface_tmp" ]]; then
        if [[ "$metric" != "40" ]]; then
            /usr/sbin/route del default gw "$gateway_tmp" "$interface_tmp"
            /usr/sbin/route add default gw "$gateway_tmp" dev "$interface_tmp" metric 40
            echo "修改 $interface_tmp  metric 40"
        fi
    elif [[ "$interface_tmp" == "$interface_wifi" ]] || [[ "$interface_tmp" == "$interface_5G" ]] ; then
        if [[ "$metric" != "60" ]]; then
            /usr/sbin/route del default gw "$gateway_tmp" "$interface_tmp"
            /usr/sbin/route add default gw "$gateway_tmp" dev "$interface_tmp" metric 60
            echo "修改 $interface_tmp  metric 60"
        fi
    else
        echo "网卡错误"
        # min_metric="$metric"
        # # min_gateway="$gateway"
        # min_interface="$interface"
    fi
done <<< "$default_routes"

# # 检查是否有足够的参数被传递
# if [ "$#" -ne 2 ]; then

#     # 输出结果
#     # echo "Minimum Metric Default Route:"
#     # echo "Destination: $destination"
#     # echo "Gateway: $min_gateway"
#     # echo "Metric: $min_metric"
#     echo "$min_interface"
#     exit 0
# elif [ "$#" -eq 1 ]; then
#     if [ "$1" = "5G" ]; then
#         network_interface=$5G_interface
#     elif [ "$1" = "wifi" ]; then
#         network_interface=$wifi_interface
#     else
#         echo "Invalid argument. Please provide either '5G' or 'wifi'."
#         exit 1
#     fi
# else
# 	network_interface=$1
# fi

# # 执行route -n命令并获取输出
# route_output=$(route -n)

# # 使用grep筛选出目标是0.0.0.0的行
# target_0_0_0_0=$(echo "$route_output" | grep '0.0.0.0')

# # 进一步筛选出接口是network_interface的行
# gateway_data=$(echo "$target_0_0_0_0" | grep "$wifi_interface" )

# # 提取并打印网关数据
# if [ -n "$gateway_data" ]; then
#     IFS=' ' read -r -a fields <<< "$gateway_data"
#     gateway_ip="${fields[1]}"
# else
#     # /home/<USER>/board_resources/5G/5G_ctl.sh
#     echo -1
# 	exit 1
# fi

# /usr/sbin/route del default gw "$gateway_ip" "$network_interface"
# /usr/sbin/route add default gw "$gateway_ip" dev "$network_interface" metric "$metric_value"
# echo 0