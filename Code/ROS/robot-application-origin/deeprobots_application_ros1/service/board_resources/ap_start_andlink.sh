#!/bin/bash

AP_INTERFACE="p2p0"
SCRIPT_PATH=$(dirname "$(readlink -f "$0")")

# 检查接口状态
get_interface_status() {
    local interface=$1
    local status=$(ip link show $interface | grep -oP '(?<=state )\w+')
    
    if [ "$status" = "UP" ]; then
        echo "UP"
        return 0
    else
        echo "ERROR: Interface $interface is $status"
        return 1
    fi
}

start_ap() {
    nmcli dev set $AP_INTERFACE managed no
    #ip link set p2p0 up
    iw dev $AP_INTERFACE set type __ap

    # 未配置需要配置 AP
    ap_status=$(get_interface_status "$AP_INTERFACE")
    if [ $? -eq 1 ]; then
        bash $SCRIPT_PATH/wifi/ap_start.sh start 5G
    fi

    # 检测当前 ap 配置是否异常
    # 获取当前的发射功率值
    TXPOWER=$(iw dev p2p0 info | grep -oiP 'txpower\s*\K[\d.-]+' | head -n 1)

    # 检查是否成功获取到了发射功率值
    if [[ -z "$TXPOWER" ]]; then
        echo "未能获取发射功率值, 请检查 p2p0 网络接口状态。"
    fi

    if (( $(echo "$TXPOWER >= 0 && $TXPOWER <= 30" | bc -l) )); then
        echo "发射功率正常：$TXPOWER dBm, p2p0 创建成功。"
    else
        echo "发射功率异常：$TXPOWER dBm, 将尝试重新配置 AP..."
        bash $SCRIPT_PATH/wifi/ap_start.sh start 24G
    fi

    # 获取发射功率值（重新获取）
    TXPOWER=$(iw dev p2p0 info | grep -oiP 'txpower\s*\K[\d.-]+' | head -n 1)
    echo "p2p0 txpower: $TXPOWER"

    # 添加 IP 地址
    ip addr add ***********/24 dev p2p0
    echo "已为 p2p0 接口添加 IP 地址 ***********/24"
}

# 调用 start_ap 函数
start_ap
