#!/bin/bash

GPIO_PIN=52

if [[ $# -ne 1 ]]; then
    echo "Usage: $0 <enable|disable>"
    exit 1
fi

if [[ "$1" == "enable" ]]; then
    echo $GPIO_PIN > /sys/class/gpio/export
    echo "out" > /sys/class/gpio/gpio$GPIO_PIN/direction
    echo 1 > /sys/class/gpio/gpio$GPIO_PIN/value
    echo "GPIO $GPIO_PIN enabled and set to 1"
elif [[ "$1" == "disable" ]]; then
    echo 0 > /sys/class/gpio/gpio$GPIO_PIN/value
    echo $GPIO_PIN > /sys/class/gpio/unexport
    echo "GPIO $GPIO_PIN disabled"
else
    echo "Invalid argument. Usage: $0 <enable|disable>"
    exit 1
fi


