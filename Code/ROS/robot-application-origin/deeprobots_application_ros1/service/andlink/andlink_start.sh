#!/bin/bash

sleep 30

IP=*************

export ROS_IP=*************

ping -c 1 $IP > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "Ping to $IP was successful."
    export ROS_MASTER_URI=http://*************:11311
else
    echo "Failed to ping $IP."
    export ROS_MASTER_URI=http://*************:11311
fi

SCRIPT_DIR=$(dirname "$0")
# 将目录名转换为绝对路径
SCRIPT_DIR=$(cd "$SCRIPT_DIR" && pwd)
echo "$SCRIPT_DIR"

source ${SCRIPT_DIR}/../../devel/setup.bash

echo "加载环境变量 ${SCRIPT_DIR}/../../devel/setup.bash"
echo "start handlernetwork service!"
roslaunch  handlernetwork_service command_server.launch &

bash ${SCRIPT_DIR}/configsync.sh

process1_name=andlinkdaemon
process2_name=andlinkscript.py

if [ $# -ge 1 ]; then
	if [ $1 == "reset" ]; then
		sudo rm /etc/andlink/andlinkSdk.conf
		wifiname=`nmcli connection show |grep wlan0|awk -F " " '{print $1}'`
		nmcli con down $wifiname
		echo "恢复出厂配置"
	fi
fi

pidCount1=`ps -ef|grep $process1_name |grep -v "grep"|grep -v " Z " | wc -l`
pidCount2=`ps -ef|grep $process2_name |grep -v "grep"|grep -v " Z " | wc -l`

#如果行数=1，就杀掉进程
if [ "$pidCount1" = "0" ]; then
	echo "$process1_name 进程未运行"
else
	ps -ef | grep $process1_name |grep -v grep | awk '{print $2}' |xargs kill -9
	echo "$process1_name 进程已杀掉"
fi

sleep 1

if [ "$pidCount2" = "0" ]; then
	echo "$process2_name 进程未运行"
else
	ps -ef | grep $process2_name |grep -v grep | awk '{print $2}' |xargs kill -9
	echo "$process2_name 进程已杀掉"
fi

sleep 1

ros_dir=${SCRIPT_DIR}/../..


while true; do
    pidCount1=`ps -ef|grep $process1_name |grep -v "grep"|grep -v " Z " |wc -l`
    pidCount2=`ps -ef|grep $process2_name |grep -v "grep"|grep -v " Z " |wc -l`

    # 检查任一进程是否未运行
    if [ "$pidCount1" = "0" ] || [ "$pidCount2" = "0" ]; then
        echo "重启 andlink 进程"
        ${SCRIPT_DIR}/andlink_stop.sh
        cd $ros_dir && source devel/setup.bash && rosrun andlink $process2_name &
    fi
    sleep 30
done



