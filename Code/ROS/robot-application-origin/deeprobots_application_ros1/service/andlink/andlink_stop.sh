#!/bin/bash


process1_name=andlinkdaemon
process2_name=andlinkscript.py


pidCount1=$(ps -ef|grep $process1_name |grep -v "grep"|wc -l)
pidCount2=$(ps -ef|grep $process2_name |grep -v "grep"|wc -l)

#如果行数=1，就杀掉进程
if [ "$pidCount1" = "0" ]; then
	echo "$process1_name 进程未运行"
else
	ps -ef | grep $process1_name |grep -v grep | awk '{print $2}' |xargs kill -9
	echo "$process1_name 进程已杀掉"
fi

sleep 1

if [ "$pidCount2" = "0" ]; then
	echo "$process2_name 进程未运行"
else
	ps -ef | grep $process2_name |grep -v grep | awk '{print $2}' |xargs kill -9
	echo "$process2_name 进程已杀掉"
fi




