[Unit]
Description=andlink
After=network.target
Before=

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/mine/robot-application/deeprobots_application_ros1
KillMode=control-group
Restart=always
ExecStart=/mine/robot-application/deeprobots_application_ros1/service/andlink/andlink_start.sh
ExecStop=/mine/robot-application/deeprobots_application_ros1/service/andlink/andlink_stop.sh


[Install]
WantedBy=multi-user.target