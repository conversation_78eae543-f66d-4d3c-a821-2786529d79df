#!/bin/bash

# 定义变量
USER="root"
HOST="*************"
# HOST="ubuntu2004.nevin.email"

FACDEVINFO="/etc/andlink/facDevinfo.conf"
# PARAMLAUNCH="/home/<USER>/robot-application/deeprobots_application_ros1/src/homi_speech/launch/config/param.launch"
# PARAMYAML="/home/<USER>/robot-application/deeprobots_application_ros1/src/cmcc_rtc/launch/config/param.yaml"

#PARAMLAUNCH="/mine/robot-application/deeprobots_application_ros1/src/homi_speech/launch/config/param.launch"
#PARAMYAML="/mine/robot-application/deeprobots_application_ros1/src/cmcc_rtc/launch/config/param.yaml"

PARAMOVD="/home/<USER>/robot-application/deeprobots_application_ros1/service/ovd/DeviceConf.ini"
PARAMLAUNCH="/home/<USER>/robot-application/deeprobots_application_ros1/src/homi_speech/launch/config/param.launch"
PARAMYAML="/home/<USER>/robot-application/deeprobots_application_ros1/src/cmcc_rtc/launch/config/param.yaml"

DEVICEID=$(grep "^deviceMac=" $FACDEVINFO | sed 's/deviceMac=//')
echo "$DEVICEID"

DEVICECMEI=${DEVICEID: -15}
echo "$DEVICECMEI"

DEVICEMAC=$(grep "^mac=" $FACDEVINFO | sed 's/mac=//')
DEVICEMAC2=$(echo "$DEVICEMAC" | sed 's/\(..\)/\1:/g' | sed 's/:$//')
echo "$DEVICEMAC"
echo "$DEVICEMAC2"
ssh -o StrictHostKeyChecking=no $USER@$HOST << EOF
echo "开始执行"

line_content=\$(grep '"sn":' $PARAMLAUNCH)
sed -i "s/\"sn\":.*$/\"sn\": \"$DEVICEID\",/" "$PARAMLAUNCH"
line_content=\$(grep '"deviceId":' $PARAMLAUNCH)
sed -i "s/\"deviceId\":.*$/\"deviceId\": \"$DEVICEID\",/" "$PARAMLAUNCH"
line_content=\$(grep '"macId":' "$PARAMLAUNCH")
sed -i "s/\"macId\":.*$/\"macId\": \"$DEVICEMAC2\",/" "$PARAMLAUNCH"


line_content=\$(grep 'sn:' "$PARAMYAML")
sed -i "s/sn:.*$/sn: \"$DEVICEID\"/" "$PARAMYAML"
line_content=\$(grep 'mac:' "$PARAMYAML")
sed -i "s/mac:.*$/mac: \"$DEVICEMAC2\"/" "$PARAMYAML"
sed -i "s/\(device_id: \"cmcc-2320647-\)[^\"]*/\1$DEVICEID/" "$PARAMYAML"


line_content=\$(grep 'devId:' $PARAMOVD)
sed -i "s/devId:.*$/devId:$DEVICEID/" "$PARAMOVD"
line_content=\$(grep 'devCmei:' $PARAMOVD)
sed -i "s/devCmei:.*$/devCmei:$DEVICECMEI/" "$PARAMOVD"

echo "修改完成"
# EOF

# exit
