import os
import re
import subprocess

# 在脚本开头提示是否需要重新绑定
rebind_choice = input("\n是否需要重新绑定[y/n]: ")
if rebind_choice.lower() == 'y':
    andlink_config_path = "/etc/andlink/andlinkSdk.conf"
    if os.path.exists(andlink_config_path):
        os.remove(andlink_config_path)
        print(f"\n已删除 {andlink_config_path}")
    subprocess.run("systemctl restart andlink.service", shell=True, check=True)
    print("\n已重启 andlink 服务")

# 判断当前是 ROS1 还是 ROS2
ros_version = input("\n请输入当前版本 (1.ros1; 2.ros2): ")
if ros_version == '1':
    PARAMLAUNCH1 = "/mine/robot-application/deeprobots_application_ros1/src/homi_speech/launch/config/param.launch"
    PARAMLAUNCH2 = "/home/<USER>/robot-application/deeprobots_application_ros1/src/homi_speech/launch/config/param.launch"
    service_name = "run_deep_rob.service"
elif ros_version == '2':
    PARAMLAUNCH1 = "/home/<USER>/robot-application/xiaoli_application_ros2/install/homi_speech/share/homi_speech/launch/config/param.yaml"
    PARAMLAUNCH2 = "/home/<USER>/robot-application/xiaoli_application_ros2/src/homi_speech/launch/config/param.yaml"
    PARAMLAUNCH3 = "/mine/robot-application/xiaoli_application_ros2/install/homi_speech/share/homi_speech/launch/config/param.yaml"
    PARAMLAUNCH4 = "/mine/robot-application/xiaoli_application_ros2/src/homi_speech/launch/config/param.yaml"
    service_name = "run_xiaoli_server.service"
else:
    print("\n无效的版本号, 请重新运行脚本.")
    exit(1)

# 定义其他变量
USER = "root"
HOST = "*************"
FACDEVINFO = "/etc/andlink/facDevinfo.conf"

# 获取设备的配置值
with open(FACDEVINFO, 'r') as file:
    content = file.read()
    DEVICEID = re.search(r"^deviceMac=(.*)$", content, re.MULTILINE).group(1)
    print(f"设备ID: {DEVICEID}")
    
    DEVICECMEI = DEVICEID[-15:]
    print(f"设备CMEI: {DEVICECMEI}")
    
    DEVICEMAC = re.search(r"^mac=(.*)$", content, re.MULTILINE).group(1)
    DEVICEMAC2 = ':'.join([DEVICEMAC[i:i+2] for i in range(0, len(DEVICEMAC), 2)])
    print(f"设备MAC: {DEVICEMAC}")
    print(f"格式化的设备MAC: {DEVICEMAC2}")

# 提示用户选择不同版本的URL
print("\n请选择需要的版本:\n")
print("1. 现网版本")
print("2. 测试版本")
print("3. 香港版本")

choice = input("\n请输入选项号 (1/2/3): ")

if choice == '1':
    new_url = "https://business.homibot.komect.com:9443/robot/business/api/device/client/connect/url"
elif choice == '2':
    new_url = "http://************:10000/robot/business/api/device/client/connect/url"
elif choice == '3':
    new_url = "https://homibot.onehome.hk.chinamobile.com:9443/robot/business/api/device/client/connect/url"
else:
    print("\n无效的选项, 请重新运行脚本.")
    exit(1)

# 远程登录并修改 param.launch 或 param.yaml 文件中的 "url" 字段
cmd = f"""ssh -o StrictHostKeyChecking=no {USER}@{HOST} << 'EOF'
echo "\\n开始修改 {PARAMLAUNCH1}"
line_content=$(grep '"url":' {PARAMLAUNCH1})
echo "\\n当前的url字段: $line_content"
sed -i 's#\\"url\\":.*#\\"url\\":\\"{new_url}\\",#' {PARAMLAUNCH1}
line_content=$(grep '"url":' {PARAMLAUNCH1})
echo "\\n修改后的url字段: $line_content"
echo "\\n修改完成 {PARAMLAUNCH1} 文件的 'url' 字段"

echo "\\n开始修改 {PARAMLAUNCH2}"
line_content=$(grep '"url":' {PARAMLAUNCH2})
echo "\\n当前的url字段: $line_content"
sed -i 's#\\"url\\":.*#\\"url\\":\\"{new_url}\\",#' {PARAMLAUNCH2}
line_content=$(grep '"url":' {PARAMLAUNCH2})
echo "\\n修改后的url字段: $line_content"
echo "\\n修改完成 {PARAMLAUNCH2} 文件的 'url' 字段"

"""

# 如果是 ROS2，修改所有 4 个文件
if ros_version == '2':
    cmd += f"""
    echo "\\n开始修改 {PARAMLAUNCH3}"
    line_content=$(grep '"url":' {PARAMLAUNCH3})
    echo "\\n当前的url字段: $line_content"
    sed -i 's#\\"url\\":.*#\\"url\\":\\"{new_url}\\",#' {PARAMLAUNCH3}
    line_content=$(grep '"url":' {PARAMLAUNCH3})
    echo "\\n修改后的url字段: $line_content"
    echo "\\n修改完成 {PARAMLAUNCH3} 文件的 'url' 字段"

    echo "\\n开始修改 {PARAMLAUNCH4}"
    line_content=$(grep '"url":' {PARAMLAUNCH4})
    echo "\\n当前的url字段: $line_content"
    sed -i 's#\\"url\\":.*#\\"url\\":\\"{new_url}\\",#' {PARAMLAUNCH4}
    line_content=$(grep '"url":' {PARAMLAUNCH4})
    echo "\\n修改后的url字段: $line_content"
    echo "\\n修改完成 {PARAMLAUNCH4} 文件的 'url' 字段"
    """

# 添加服务重启命令
cmd += f"""
systemctl restart {service_name}
echo "\\n已重启 {service_name} 服务"
EOF"""

# 执行命令
subprocess.run(cmd, shell=True, check=True)

print("\n脚本执行完成, 远程文件已更新并重启了相关服务")




