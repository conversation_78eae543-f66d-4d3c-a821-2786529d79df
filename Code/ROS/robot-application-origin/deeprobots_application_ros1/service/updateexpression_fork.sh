#!/bin/bash

# 检查是否有足够的参数被传递
if [ "$#" -ne 3 ]; then
    echo "Usage: $0 <filename1> <delay> <filename2> "
    exit 1
fi

# 接收参数
filename1=$1
delay=$2
filename2=$3

# 检查更新是否成功
if [ $? -eq 0 ]; then
	echo "{ \"command\": [\"loadfile\", \"$filename1\", \"replace\"] }" | socat - /tmp/mpv-socket
    if [ $? -eq 0 ]; then
        sleep "$delay"
	    echo "{ \"command\": [\"loadfile\", \"$filename2\", \"replace\"] }" | socat - /tmp/mpv-socket
    fi
else
    echo "Failed to update expression."
fi