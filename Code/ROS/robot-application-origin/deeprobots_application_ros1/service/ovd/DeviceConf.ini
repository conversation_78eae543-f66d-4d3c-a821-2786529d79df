﻿#deviceConfiguration
[deviceConfiguration]
#设备ID
devId:1830004229212345670000035
devCmei:212345670000035
#硬件名称
hardwareModel:test_device
#硬件版本号
firmware_model:1.2.11
#modelId
modelId:IPC_HDC56
wifi_ssid:
wifi_signal:
upBandwidth:
downBandwidth:
ipAddr:
macAddr:00:0c:29:25:cd:b0
battery:100
#是否后台执行
run_deamo:0

[voipDevice]
#固话平台产品创建后自动生成的
app_key:
# 固话平台产品创建后自动生成的
app_secret:
# 格式为cmcc-AndlinkID-序列号
device_id:
#deviceClientInfo
[deviceClientInfo]
#OVDDeviceID:1401705980000022
OVDLoginPassword:GYusFKsb
OVDMediaEncPassword:GYusFKsb
#OVDHardWareModel:test_device
#是否开启服务调度
enableserviceschedule:1
#服务调度url
#servicescheduleurl:http://elb-1-2446eb7f46ea7316.elb.cn-northwest-1.amazonaws.com.cn:9080/ovp/getServerAddr
#servicescheduleurl:https://**************:10000/ovp/getServerAddr
servicescheduleurl:https://video.komect.com/ovp/getServerAddr
#OVC服务器地址
passDomain:************
#OVC服务器端口号
passPort:9001
#OVC服务器加密端口号
securepassport:
#p2p服务器地址
p2p_passDomain:
#p2p服务器端口号
p2p_passPort:
#turn服务器地址
turnDomain:
#turn服务器端口号
turnPort:
#休眠服务器地址
hibernationDomain:
#休眠服务器端口号
hibernationPort:
#休眠时心跳间隔
hibernationHBInterval:100
#设备最大支持的P2P个数
maxP2PSession:4
#bindID 取值为字符串。若值为0，则采用声波/二维码识别出来的值；否则为此设置的值
bindId:0

#deviceConfig  设备配置
[deviceConfigInfo]
#设备支持多少个channel，模拟器一般是一个
channelsInfoCount:1
#时区信息
tz:8
#<必填，只读,字符串型：上次重启时间，格式”YY-MM-DDTHH:MM:SS”>
autoreboot_last_reboot:"2020-07-20T20:18:18"
ovd_data_path:"./"
SDK_partition_mounting_path:"./"
ovd_log_path:"./log/"
#第一个channel信息
[channel0]
#channel ID
channel:0
#通道使能开关，通道关闭后，应停止该通道的流媒体采集、告警、云台操作等相关功能
# <可选，只读，整形：每秒帧数>
video_framerate:15
#<可选，只读，整形：码流比特率>
video_bitrate:1024
#<可选，只读，整形：图像宽度像素>
video_width:256
#<可选，只读，整形：图像高度像素>
video_height:256
#video_framerate:10
#video_colorDepth:2
#<可选，只读，整形：码流gop,单位帧>
video_frameInterval:60
#video_reserve:0

#<可选，只读，整形：采样率，即每秒钟采用数目，合法值8000/16000/32000/44100/48000>
audio_samplesRate:16000
#<可选，只读，整形：码流比特率>
audio_bitrate:1024
#audio_waveFormat:0x6163
#audio_channelNumber:2
#audio_blockAlign:0
#<可选，只读，整形：位宽，即每个sample的比特数>
audio_bitsPerSample:10
#<可选，只读，整形：每一帧中包含的sample数，AAC算法标准固定为1024>
audio_samplePerframe:1024
#<可选，只读，整形：声道数>
audio_channel:1
#audio_frameInterval:1
#audio_reserve:0

[devCap]
#设备类型 0 代表普通IPC， 1代表低功耗中继型设备， 2代表低功耗非中继型设备，默认值为0，普通IPC， 1.21版本新增
device_type: 0
#布尔型，是否支持低功耗相关功能
enable_low:0
#布尔型，是否支持低功耗唤醒（低功耗设备）
support_awaked: 0
#布尔型，是否支持低功耗唤醒开关（低功耗设备）
support_awaked_switch: 0
#布尔型，是否智能模式（低功耗设备）
smart_mode:0
#布尔型，是否支持4G
support_4G: 0
#布尔型，是否支持主子码流
multi_stream:1
#布尔型，是否支持AOV
support_aov:0
#字符串，码流加密
stream_encryption_mode:0


#是否支持云台
have_ptz: 0
#是否支持云台预置位
support_ptz_preset: 0
#是否支持消控指令
stopalarm: 0
#是否支持手动调节倍率
zoomcontrol: 0
#是否支持手动调焦
focuscontrol: 0
#是否支持电池供电
have_battery: 0
#是否支持视频输出
have_audio_out: 0
#是否支持对讲输出
have_voice_out: 0
#是否支持移动追踪
have_trace: 0
#"ivrs": <布尔型：是否支持ivrs协议，默认值为false，表示云存只支持ivr协议>,开启时，云存走ivrs
ivrs:1
support_set_gop:0
#AI相关
#是否支持设备上报人脸AI，若不支持，则无该字段
AI_supportface:0
#是否支持人脸曝光度调节，默认值为false
AI_face_exposureadjust:0
#抓拍模式调节列表选项，若没有，则设备不支持模式切换,默认“quality”:代表质量抓拍，“quick”:代表快速抓拍
AI_face_capturemodelists:quality,quick
#0不支持，1：只支持矩形，n(n>=3):支持不规则n边形
AI_face_zone:0
#是否口罩检测功能
AI_face_maskDetection:0
#是否支持明厨亮灶功能
AI_supportkitchen:0
#检测类型列表选项,选项之间以逗号相隔，若没有，则设备不支持检测类型更改，检测类型选项有“mask"(口罩),"cap"(帽子),“clothes"(衣服)
AI_kitchen_capturemodelists:clothes
#设备所支持工服颜色列表，逗号相隔，目前所支持的有“white”(白),"black"(黑),"red"(红),"blue"(蓝),"green"（绿）
AI_kitchen_clothesColorList:white
#0:不支持，1:只支持矩形，n(n>=3)支持不规则n边形
AI_kitchen_zone:0
#是否支持车型侦测
AI_supportvehicle:0
#0不支持， 1：只支持矩形， n(n>=3)，支持不规则n边形
AI_vehicle_zone:0
#是否支持车辆识别的安装场景
AI_vehicle_detect_site:10
#是否支持非机动车检测（梯控）
AI_supportnonmotorvehicle:10
#检测类型选项列表，选项之间用逗号相隔，目前端侧类型选项有”electromobile"（电瓶车),”bike"(自行车）
AI_nonmotorvehicle_capturemodelists:electromobile,bike
#0不支持， 1：只支持矩形， n(n>=3)，支持不规则n边形
AI_nonmotorvehicle_zone:0
#是否支持设备上报客流AI，若不支持，则无该字段
AI_supportpassenger:0
#是否支持客流曝光度调节，默认值为false
AI_passenger_exposureadjust:0
#客流抓拍模式调节列表选项，若没有，则设备不支持模式切换,默认“quality”:代表质量抓拍，“quick”:代表快速抓拍
AI_passenger_capturemodelists:quality,quick
#客流区域, 0不支持，1：只支持矩形，n(n>=3):支持不规则n边形
AI_passenger_zone:0
#1.46.0-布尔型： 迎宾促销， 0不支持，1：只支持矩形
AI_passenger_supportwelcome:0
#1.46.0-布尔型： 迎宾促销时间段， 0不支持，1：只支持矩形
AI_passenger_supportalerttime:0
#1.46.0-布尔型： 手势识别， 0不支持，1：只支持矩形
AI_support_gesture_recognition:0

#是否支持设备上报高空抛物，若不支持，则无该字段
AI_support_parabolic_aerial:0
#高空抛物检测区域最大个数
AI_parabolic_aerial_detect_zone_num:3
#高空抛物检测区域绘制多边形最大边数, 0不支持，1：只支持矩形，n(n>=3):支持不规则n边形
AI_parabolic_aerial_detect_zone:6
#高空抛物屏蔽区域最大个数
AI_parabolic_aerial_shield_zone_num:2
#高空抛物屏蔽区域绘制多边形最大边数, 0不支持，1：只支持矩形，n(n>=3):支持不规则n边形
AI_parabolic_aerial_shield_zone:5
#高空抛物楼层设置最大个数
AI_parabolic_aerial_floor_configuration_num:3


#是否支持设备上报区域人数统计，若不支持，则无该字段, 0表示不支持，1表示支持，默认不支持
AI_support_regional_people_statistics:0
#是否支持设置检测时间， 0表示不支持，1表示支持，默认不支持
AI_support_regional_people_alert_time:1
#是否支持设置OSD叠加人数， 0表示不支持，1表示支持，默认不支持
AI_support_regional_people_osd_status:1
#区域人数触发预警的人数，个数，0表示不支持，其他值表示对应配置项“触发预警的人数”的最大值限制，默认不支持
AI_support_regional_people_count:100
#区域人数告警上报间隔时间，分钟，0表示不支持，其他值表示对应配置项“告警上报间隔时间”的最大值限制，默认不支持
AI_support_regional_people_alarm_report_duration:60
#区域人数定时上传，分钟，0表示不支持，其他值表示对应配置项“区域人数定时上传”的最大值限制，默认不支持
AI_support_regional_people_detect_result_report_duration:30
#检测计划的个数，0不支持，最大值10个，默认不支持
AI_support_regional_people_detect_plans_num:1
#检测区域的个数，0不支持，最大值10个，默认不支持
AI_support_regional_people_alarm_area_num:1
#检测区域, 整型：0不支持，1：只支持矩形，n(n>=3):支持不规则n边形，目前只支持4边形。最大支持10边形
AI_support_regional_people_alarm_zone:1


#是否支持设备上报车道线，若不支持，则无该字段
AI_support_lane_line:0
#支持车道线数量，1：单个车道线， 2：双车道线， 目前只支持配置1和2
AI_lane_line_num:1
#车道线区域, 整型：0不支持， 1：只支持矩形， n(n>=3)，支持不规则n边形，目前只支持矩形
AI_lane_line_zone:1

#是否支持设备上报离岗检测，若不支持，则无该字段
AI_support_off_duty:0
#设置在岗人数的最大值，目前支持最大值5
AI_support_on_duty_count:5
#设置离岗时长的最大值，目前支持最大值60
AI_support_off_duty_durtion:60
#是否支持检测时间设置
AI_support_off_duty_alert_time:1
#离岗检测，检测计划数设置,目前只支持1个
AI_support_off_duty_detect_plans_num:1
#离岗检测，检测区域数设置,目前只支持1个
AI_support_off_duty_alarm_area_num:1
#检测区域, 整型：0不支持， 1：只支持矩形， n(n>=3)，支持不规则n边形，目前只支持矩形
AI_off_duty_zone:1


#告警区域联动方式
#字符串，警戒区域告警所支持的联动策略列表，选项之间以逗号相隔，可选项有”speech“（语音输出), "light"(联动警灯) , "buz"(蜂鸣器）
linkagemode_alertarea:speech,light,buz
linkagemode_vehicledetection:speech,light
linkagemode_nonmotorvehicledetection:speech,light,buz
linkagemode_maskdetection:speech
linkagemode_regionalpeopledetection:speech


#告警相关
#是否支持外包报警
have_alarms_io: 0
#是否支持哭声侦测
have_alarms_cry: 0
#是否支持啼哭安抚功能
support_crying_pacify: 0
crying_pacify_audio_playing_count: 10
support_crying_pacify_audio_playing_volumn: 1
crying_pacify_audio_playing_type: 1
#是否支持声音侦测
have_alarms_voice: 0
#是否支持移动侦测
have_alarms_motion: 0
support_alarms_motion_zone:1
#是否支持人形侦测：
have_alarms_body: 0
support_alarms_body_zone:1
#是否支持伴网侦测
have_alarms_cross: 0
#是否支持pir红外物体移动侦测
have_alarms_pir: 0
#是否支持pir逗留模式
have_alarms_pir_staymode: 0
#是否支持撬锁侦测
have_alarms_lossLock: 0
#是否支撑警戒功能
have_alarms_alertarea:0
have_alarms_alertarea_zone:1
support_alarms_alertarea_zone:4
#是否支持智能驱离（针对带云台的警戒设备）
have_alertarea_expel:0


#庭院灯控制
yard_light_brightness:1
yard_light_manual_control:1
yard_light_timed_schedule:2

#是否支持通道使能开关
have_switch: 1
#是否支持SD卡
have_SD: 0
#是否支持带屏开关
have_screen: 0
#是否支持led灯开关
have_led: 0
#是否支持软探针
have_support_softprobe: 0
#是否支持自动重启
have_auto_reboot: 0
#"video_quality": <字符串列表：支持的视频清晰度选项列表，列表元素可选值为：ld/sd/hd/fhd，分别代表低清/标清/高清/全高清，默认支持sd和hd>
video_quality:sd,hd,fhd
#<字符串列表(中间用,隔开)：支持的视频编码格式列表，列表元素可选值为：h264/h265,默认支持h264>
video_formats_supportlists:h264,h265
#<布尔型： 设备是否支持对讲音量调节，默认值为false>
have_voiceout_volume: 0
#<布尔型： 是否支持设备支持门铃音量设置,默认值为false>
have_doorbell_volume: 0
#<布尔型: 普通摄像头是否支持设备切换夜视模式，夜视模式分为开启/关闭/自动>
support_set_normal_nightvision_mode: 0
#<布尔型：全彩摄像头是否支持设备切换夜视模式，夜视模式分为黑白夜视/全彩夜视/智能夜视>
support_set_color_nightvision_mode: 0
#<布尔型：人脸识别是否支持区域配置，1支持 0不支持>
have_face_zone:0
#<布尔型：是否支持OSD文本>
osd_text: 0
#<布尔型： 是否支持OSDlog>
osd_logo: 0
#<布尔型，设备是否集成和家固话>
enable_hjkh:0
#<布尔型，是否支持呼叫偏好功能；呼叫偏好：可设置在发起呼叫时，普通电话优先或者APP通话优先
preference_call:0
#<布尔型，是否支持呼叫号码设置的功能；呼叫号码：可设置点击呼叫按键后，摄像头呼叫的号码（被叫号码）
set_calling_number:0
#<布尔型，是否支持手机勿扰模式；手机勿扰是控制设备端主叫手机端。可设置设备勿扰开关是否打开，如果打开，可设置勿扰时间>
not_disturb_mode:0
#<布尔型，是否支持设备勿扰模式，设备勿扰是控制手机端主叫设备端>
not_disturb_device_mode:0
#<布尔型，是否支持自动挂断功能；自动挂断：可设置检测到访客离开/进门后自行停止呼叫>
auto_hangup:1
#<布尔型，是否支持语音呼叫号码功能；如果设备上报支持，则APP新增“语音呼叫号码”配置项，配置完成后，可实现通过语音给指定的号码打电话>
voice_calling_number:0
#<字符串列表(中间用,隔开)：支持的extension方法列表，列表元素可选值为：echotest/ccrtc>
have_support_extension:echotest,ccrtc

#<布尔型，设备是否支持机器人协议>
enable_EBO:0
#<布尔型，是否支持左右前后移动>
EBOmove:0
#<布尔型，是否支持防跌落>
EBOnofall:0
#<布尔型，是否支持激光>
EBOlaser:0
#<布尔型，是否支持向前猛冲>
EBOonrush:0
#<布尔型，是否支持回充>
EBOrecharge:0
#<布尔型，是否支持抖动>
EBOshake:0
#<布尔型，是否支持转圈>
EBOspin:0
#<布尔型，是否支持移动追踪，开启后  默认值是： 人形追踪> 1.27新增
EBOtrack:0
#<布尔型，是否支持人形追踪追踪，开启后支持人形追踪> 1.27新增
EBObody_track:0
#<布尔型，是否支持宠物追踪追踪，开启后支持宠物追踪> 1.27新增
EBOpets_track:0
#<布尔型，是否支持巡航功能，开启后支持巡航> 1.29新增
EBOcruise:0
#<布尔型，是否支持防碰撞功能，开启后支持防碰撞> 1.29新增
EBOcollision:0
#<布尔型，是否支持提示音功能，开启后支持提示音> 1.34新增
EBOreminder:0
#<字符串，支持机器人控制类型> 1.33新增
robot_ctrl:ROBOT_OWL

#喂食功能
enable_petfeeder:0
#<int型，数量为支持的喂食份数，值必须大于0>
petfeeder_feed_num:0
#<int型，数量为支持的最大计划条数，值必须大于0，最大值为10>
petfeeder_plan_num:0

#<整型，位运算0x0F。设备是否支持设置检测目标。最低位表示"人形" 0x01，第二位表示"机动车" 0x02，第三位表示"非机动车" 0x04，第四位表示"其他" 0x08，默认0表示不支持>
alertarea_support_set_target:0
#<布尔型，是否支持区域入侵后设置停留时长。0表示不支持，1表示支持，默认不支持>
alertarea_support_staymode_time:0
#<布尔型，设备是否支持设置检测时间段，0表示不支持，1表示支持，默认不支持>
alertarea_support_set_alerttime:0

#<1.33 新增 布尔型：是否支持越界侦测功能，默认值为false>
enable_transgression:0
#<整型，位运算0x0F。设备是否支持设置检测目标。最低位表示"人形" 0x01，第二位表示"机动车" 0x02，第三位表示"非机动车" 0x04，第四位表示"其他" 0x08，默认0表示不支持>
transgression_support_set_target:0
#<布尔型，设备是否支持设置检测时间段，0表示不支持，1表示支持，默认不支持>
transgression_support_set_alerttime:0
#<布尔型，设备是否支持设置越界规则线，0表示不支持，1表示支持，默认不支持>
transgression_upport_set_statistics_line:0
#<1.33新增，字符串，越界侦测告警所支持的联动策略列表，选项之间以逗号相隔，可选项有"speech"（语音输出), "light"(联动警灯) , "buz"(蜂鸣器），默认值为空字符串
linkagemode_transgression:speech,light,buz

#<1.33新增，整型，位运算0x0F。设备是否支持机器人管家相关功能。最低位表示"天气预报" 0x01，第二位表示"吃药提醒" 0x02，默认0表示不支持>
robot_housekeeper:2

#<1.36新增，布尔型，设备是否支持四合一烟感功能>
smoke_sensor:0

[logConfiguration]
#日志输出位置，详细见OpenAPI枚举值LogSTD      0:代表不输出  1：输出到标准输出  2：输出到标准出错
logSTD:2
#日志输出级别，详细见OpenAPI枚举值OVDLogLevel
logLevel:1
#设备是否提供日志写入回调函数。若置为y，则设备提供回调函数OVD_DEMO_LogOutCallBack，日志写入到demo所在的bin目录下的OVDSdkDemo.log中
callback:y

[sourceFile]
#告警图片的目录
alarm:./configure/resources/alarmpic.bmp
#服务器截图命令时的源文件目录
snapshot:./configure/resources/snapshotPic.bmp
#模拟摄像头的视频源文件的目录
video:./configure/resources/video.h265
#模拟器变更视频格式后h264的文件路径
h264file:./configure/resources/video.h264
#模拟器变更视频格式后h265的文件格式
h265file:./configure/resources/video.h265
#模拟器卡回放使用h264还是h265: 0 代表h264, 1代表h265
p2ptfflag:1
#模拟器卡h264录像
p2ptfh264file:./configure/resources/video.h264
#模拟器卡h265卡录像
p2ptfh265file:./configure/resources/video.h265
#日志文件地址
logfile:./OVDSdkDemo.log

#设备采集视频的每秒传输帧数，需根据准备的视频源文件进行设置
fps:15
#模拟麦克的音频源文件的目录
audio:./configure/resources/audio.aac
#二维码配网的目录
qr:./configure/resources/qr.bmp
#声波配网的目录
wave:./configure/resources/voiceRecognize.pcm
#语音对讲保存目录
audioplay:./configure/resources/voiceRecord.pcm
#截图或者告警时的缩略图的大小，单位为byte
imageSize:524288
#设备启动后，等待连接到OVC时的音视频流最长保存时间，单位为s
avDuration:10
#设备ca文件,开启wss时，ca文件不为空时，用此文件校验服务器证书，若ca文件为空，则在ssl握手时跳过校验服务器证书流程
cafile:

dnsfile:.
[debug]
#是否开启输出数据帧的timestame,0不开启，1开启
debug_ts:0
live_filepath:./
live_filepre:wjj
record_filepath:./
record_filepre:wxh

[deviceAI]
#<必填，是否上报AI平台>
AI_on:0
#<必填，可读可写,布尔型，人脸曝光度调节功能开关
AI_exposure_on:0
#<必填，可读可写，整形，人脸曝光度调节，0~100>
AI_exposure_adjust:0
#<可选，可读可写，字符串，人脸抓拍模式，“quality”:代表质量抓拍，“quick”:代表快速抓拍
AI_mode:quick
#<可选，可读写，整形，选择质量抓拍时，人脸质量抓拍的阈值设置，0~100>
AI_quality_value:50
#<必填，可读可写，整型，人脸探测灵敏度，0~100>
AI_alarm_sensitivity:80
faceAlarm_left_up_x:1993
faceAlarm_left_up_y:1525
faceAlarm_right_bottom_x:10000
faceAlarm_right_bottom_y:10000
#<口罩检测>
mask_detection_on:0
maskDete_alerttime_on:1
maskDete_alerttime_start:00:00:00
maskDete_alerttime_end:06:00:00
maskDete_alerttime_repeat:127
maskDete_speech_on:1
maskDete_speech_vol:60
maskDete_speech_repeat:3
maskDete_light_on:1
maskDete_light_mode:1
maskDete_light_dur:10
maskDete_buz_on:1
maskDete_buz_dur:1
kitchen_detection_on:1
kitchen_mask_on:1
kitchen_cap_on:1
kitchen_clothes_on:1
kitchen_clothes_color:white
kitchen_alarm_sensitivity:1
kitchenAlarm_left_up_x:1
kitchenAlarm_left_up_y:1
kitchenAlarm_left_bottom_x:3120
kitchenAlarm_left_bottom_y:1750
kitchenAlarm_right_bottom_x:0
kitchenAlarm_right_bottom_y:249
kitchenAlarm_right_up_x:0
kitchenAlarm_right_up_y:7350
vehicle_detection_on:1
vehicle_alarm_sensitivity:1
vehicle_quality:80
vehicle_capture_mode:0
vehicle_scheduled_capture_time:60
vehicleAlarm_left_up_x:1
vehicleAlarm_left_up_y:1
vehicleAlarm_right_bottom_x:1
vehicleAlarm_right_bottom_y:1
veh_speech_on:1
veh_speech_vol:60
veh_speech_repeat:3
veh_light_on:1
veh_light_mode:1
veh_light_dur:10
veh_buz_on:1
veh_buz_dur:1
veh_detect_site:2
veh_exit_mode:1
nonmotorvehicle_detection_on:1
nonmotorvehicle_detect_mode_list:electromobile
nonmotorvehicle_alarm_sensitivity:80
nonmotorvehicle_quality:80
nonmotorvehicle_capture_mode:0
nonmotorvehicle_scheduled_capture_time:60
nonmotorvehicleAlarm_left_up_x:1
nonmotorvehicleAlarm_left_up_y:1
nonmotorvehicleAlarm_right_bottom_x:1
nonmotorvehicleAlarm_right_bottom_y:1
nonveh_speech_on:1
nonveh_speech_vol:60
nonveh_speech_repeat:3
nonveh_light_on:1
nonveh_light_mode:1
nonveh_light_dur:10
nonveh_buz_on:1
nonveh_buz_dur:1
#<客流>
AI_passenger_on:1
AI_passenger_exposure_on:1
AI_passenger_exposure_adjust:1
AI_passenger_mode:quick
AI_passenger_quality_value:50
AI_passenger_alarm_sensitivity:80
passenger_staticstic_line_a_x:100
passenger_staticstic_line_a_y:100
passenger_staticstic_line_b_x:200
passenger_staticstic_line_b_y:100
passenger_staticstic_mode:0
passenger_Alarm_left_up_x:1993
passenger_Alarm_left_up_y:1525
passenger_Alarm_right_bottom_x:100
passenger_Alarm_right_bottom_y:10000

#<高空抛物>
AI_parabolic_aerial_on:1
AI_parabolic_aerial_sensitivity:80

AI_parabolic_aerial_floor_id_0:0
AI_parabolic_aerial_floor_detail_0:floor0
AI_parabolic_aerial_floor_line_A_x_0:100
AI_parabolic_aerial_floor_line_A_y_0:100
AI_parabolic_aerial_floor_line_B_x_0:200
AI_parabolic_aerial_floor_line_B_y_0:100

AI_parabolic_aerial_floor_id_1:1
AI_parabolic_aerial_floor_detail_1:floor1
AI_parabolic_aerial_floor_line_A_x_1:100
AI_parabolic_aerial_floor_line_A_y_1:200
AI_parabolic_aerial_floor_line_B_x_1:200
AI_parabolic_aerial_floor_line_B_y_1:200

AI_parabolic_aerial_floor_id_2:2
AI_parabolic_aerial_floor_detail_2:floor2
AI_parabolic_aerial_floor_line_A_x_2:100
AI_parabolic_aerial_floor_line_A_y_2:300
AI_parabolic_aerial_floor_line_B_x_2:200
AI_parabolic_aerial_floor_line_B_y_2:300


AI_parabolic_aerial_detect_zone_areaId_0:0
AI_parabolic_aerial_detect_zone_areaId_0_pointId_0:0
AI_parabolic_aerial_detect_zone_areaId_0_point_x_0:110
AI_parabolic_aerial_detect_zone_areaId_0_point_y_0:110
AI_parabolic_aerial_detect_zone_areaId_0_pointId_1:1
AI_parabolic_aerial_detect_zone_areaId_0_point_x_1:210
AI_parabolic_aerial_detect_zone_areaId_0_point_y_1:210
AI_parabolic_aerial_detect_zone_areaId_0_pointId_2:2
AI_parabolic_aerial_detect_zone_areaId_0_point_x_2:310
AI_parabolic_aerial_detect_zone_areaId_0_point_y_2:310
AI_parabolic_aerial_detect_zone_areaId_0_pointId_3:3
AI_parabolic_aerial_detect_zone_areaId_0_point_x_3:410
AI_parabolic_aerial_detect_zone_areaId_0_point_y_3:410
AI_parabolic_aerial_detect_zone_areaId_0_pointId_4:4
AI_parabolic_aerial_detect_zone_areaId_0_point_x_4:510
AI_parabolic_aerial_detect_zone_areaId_0_point_y_4:510
AI_parabolic_aerial_detect_zone_areaId_0_pointId_5:5
AI_parabolic_aerial_detect_zone_areaId_0_point_x_5:610
AI_parabolic_aerial_detect_zone_areaId_0_point_y_5:610

AI_parabolic_aerial_detect_zone_areaId_1:1
AI_parabolic_aerial_detect_zone_areaId_1_pointId_0:0
AI_parabolic_aerial_detect_zone_areaId_1_point_x_0:120
AI_parabolic_aerial_detect_zone_areaId_1_point_y_0:120
AI_parabolic_aerial_detect_zone_areaId_1_pointId_1:1
AI_parabolic_aerial_detect_zone_areaId_1_point_x_1:220
AI_parabolic_aerial_detect_zone_areaId_1_point_y_1:220
AI_parabolic_aerial_detect_zone_areaId_1_pointId_2:2
AI_parabolic_aerial_detect_zone_areaId_1_point_x_2:320
AI_parabolic_aerial_detect_zone_areaId_1_point_y_2:320
AI_parabolic_aerial_detect_zone_areaId_1_pointId_3:3
AI_parabolic_aerial_detect_zone_areaId_1_point_x_3:420
AI_parabolic_aerial_detect_zone_areaId_1_point_y_3:420
AI_parabolic_aerial_detect_zone_areaId_1_pointId_4:4
AI_parabolic_aerial_detect_zone_areaId_1_point_x_4:520
AI_parabolic_aerial_detect_zone_areaId_1_point_y_4:520
AI_parabolic_aerial_detect_zone_areaId_1_pointId_5:5
AI_parabolic_aerial_detect_zone_areaId_1_point_x_5:620
AI_parabolic_aerial_detect_zone_areaId_1_point_y_5:620

AI_parabolic_aerial_detect_zone_areaId_2:2
AI_parabolic_aerial_detect_zone_areaId_2_pointId_0:0
AI_parabolic_aerial_detect_zone_areaId_2_point_x_0:130
AI_parabolic_aerial_detect_zone_areaId_2_point_y_0:130
AI_parabolic_aerial_detect_zone_areaId_2_pointId_1:1
AI_parabolic_aerial_detect_zone_areaId_2_point_x_1:230
AI_parabolic_aerial_detect_zone_areaId_2_point_y_1:230
AI_parabolic_aerial_detect_zone_areaId_2_pointId_2:2
AI_parabolic_aerial_detect_zone_areaId_2_point_x_2:330
AI_parabolic_aerial_detect_zone_areaId_2_point_y_2:330
AI_parabolic_aerial_detect_zone_areaId_2_pointId_3:3
AI_parabolic_aerial_detect_zone_areaId_2_point_x_3:430
AI_parabolic_aerial_detect_zone_areaId_2_point_y_3:430
AI_parabolic_aerial_detect_zone_areaId_2_pointId_4:4
AI_parabolic_aerial_detect_zone_areaId_2_point_x_4:530
AI_parabolic_aerial_detect_zone_areaId_2_point_y_4:530
AI_parabolic_aerial_detect_zone_areaId_2_pointId_5:5
AI_parabolic_aerial_detect_zone_areaId_2_point_x_5:630
AI_parabolic_aerial_detect_zone_areaId_2_point_y_5:630

AI_parabolic_aerial_shield_zone_areaId_0:0
AI_parabolic_aerial_shield_zone_areaId_0_pointId_0:0
AI_parabolic_aerial_shield_zone_areaId_0_point_x_0:10
AI_parabolic_aerial_shield_zone_areaId_0_point_y_0:10
AI_parabolic_aerial_shield_zone_areaId_0_pointId_1:1
AI_parabolic_aerial_shield_zone_areaId_0_point_x_1:20
AI_parabolic_aerial_shield_zone_areaId_0_point_y_1:20
AI_parabolic_aerial_shield_zone_areaId_0_pointId_2:2
AI_parabolic_aerial_shield_zone_areaId_0_point_x_2:30
AI_parabolic_aerial_shield_zone_areaId_0_point_y_2:30
AI_parabolic_aerial_shield_zone_areaId_0_pointId_3:3
AI_parabolic_aerial_shield_zone_areaId_0_point_x_3:40
AI_parabolic_aerial_shield_zone_areaId_0_point_y_3:40
AI_parabolic_aerial_shield_zone_areaId_0_pointId_4:4
AI_parabolic_aerial_shield_zone_areaId_0_point_x_4:50
AI_parabolic_aerial_shield_zone_areaId_0_point_y_4:50


AI_parabolic_aerial_shield_zone_areaId_1:1
AI_parabolic_aerial_shield_zone_areaId_1_pointId_0:0
AI_parabolic_aerial_shield_zone_areaId_1_point_x_0:11
AI_parabolic_aerial_shield_zone_areaId_1_point_y_0:11
AI_parabolic_aerial_shield_zone_areaId_1_pointId_1:1
AI_parabolic_aerial_shield_zone_areaId_1_point_x_1:21
AI_parabolic_aerial_shield_zone_areaId_1_point_y_1:21
AI_parabolic_aerial_shield_zone_areaId_1_pointId_2:2
AI_parabolic_aerial_shield_zone_areaId_1_point_x_2:31
AI_parabolic_aerial_shield_zone_areaId_1_point_y_2:31
AI_parabolic_aerial_shield_zone_areaId_1_pointId_3:3
AI_parabolic_aerial_shield_zone_areaId_1_point_x_3:41
AI_parabolic_aerial_shield_zone_areaId_1_point_y_3:41
AI_parabolic_aerial_shield_zone_areaId_1_pointId_4:4
AI_parabolic_aerial_shield_zone_areaId_1_point_x_4:51
AI_parabolic_aerial_shield_zone_areaId_1_point_y_4:51

#区域人数统计
regional_people_stat_detection_on:1
regional_people_stat_plan0_on:1
regional_people_stat_plan0_id:0
regional_people_stat_plan0_name:stat1
regional_people_stat_plan0_count:30
regional_people_stat_plan0_alarm_report_duration:5
regional_people_stat_plan0_alerttime_on:1
regional_people_stat_plan0_alerttime_start:00:00:00
regional_people_stat_plan0_alerttime_end:06:00:00
regional_people_stat_plan0_alerttime_repeat:127
regional_people_stat_plan0_speech_on:1
regional_people_stat_plan0_speech_vol:60
regional_people_stat_plan0_speech_repeat:3
regional_people_stat_plan0_light_on:1
regional_people_stat_plan0_light_mode:1
regional_people_stat_plan0_light_dur:10
regional_people_stat_plan0_buz_on:1
regional_people_stat_plan0_buz_dur:1
regional_people_stat_plan0_detect_result_report_duration:5
regional_people_stat_plan0_osd_status:1
regional_people_stat_plan0_left_up_x:1
regional_people_stat_plan0_left_up_y:1
regional_people_stat_plan0_left_bottom_x:3120
regional_people_stat_plan0_left_bottom_y:1750
regional_people_stat_plan0_right_bottom_x:0
regional_people_stat_plan0_right_bottom_y:249
regional_people_stat_plan0_right_up_x:0
regional_people_stat_plan0_right_up_y:7350

#车道线
lane_line_detection_on:1
lane_line_num:2
lane_line_direction_0:0
lane_line_zone_0_left_up_x:0
lane_line_zone_0_left_up_y:10000
lane_line_zone_0_left_bottom_x:0
lane_line_zone_0_left_bottom_y:0
lane_line_zone_0_right_bottom_x:5000
lane_line_zone_0_right_bottom_y:0
lane_line_zone_0_right_up_x:5000
lane_line_zone_0_right_up_y:10000
lane_line_direction_1:0
lane_line_zone_1_left_up_x:5000
lane_line_zone_1_left_up_y:10000
lane_line_zone_1_left_bottom_x:5000
lane_line_zone_1_left_bottom_y:0
lane_line_zone_1_right_bottom_x:10000
lane_line_zone_1_right_bottom_y:0
lane_line_zone_1_right_up_x:10000
lane_line_zone_1_right_up_y:10000

#离岗检测
off_duty_detection_on:1
off_duty_plan0_on:1
off_duty_plan0_id:0
off_duty_plan0_name:stat1
off_duty_plan0_on_duty_count:1
off_duty_plan0_off_duty_duration:10
off_duty_plan0_alerttime_on:1
off_duty_plan0_alerttime_start:00:00:00
off_duty_plan0_alerttime_end:06:00:00
off_duty_plan0_alerttime_repeat:127
off_duty_plan0_left_up_x:1
off_duty_plan0_left_up_y:1
off_duty_plan0_left_bottom_x:3120
off_duty_plan0_left_bottom_y:1750
off_duty_plan0_right_bottom_x:0
off_duty_plan0_right_bottom_y:249
off_duty_plan0_right_up_x:0
off_duty_plan0_right_up_y:7350

AI_passenger_osd_status:1

[deviceAlarms]
#外部告警
ioAlarm_isEffect:1
#<必填，可读可写,布尔型：使能开关>
ioAlarm_on:1
#<必填，可读可写,整型：探测灵敏度， 0 - 100>
ioAlarm_sensitivity:100
#人形告警
bodyAlarm_isEffect:0
bodyAlarm_on:1
bodyAlarm_sensitivity:0
bodyAlarm_left_up_x:1993
bodyAlarm_left_up_y:1525
bodyAlarm_right_bottom_x:10000
bodyAlarm_right_bottom_y:10000
#pir红外告警
pirAlarm_isEffect:1
pirAlarm_on:1
pirAlarm_sensitivity:100
#声音告警
voiceAlarm_isEffect:1
voiceAlarm_on:1
voiceAlarm_sensitivity:100
#移动告警
motionAlarm_isEffect:1
motionAlarm_on:1
motionAlarm_sensitivity:100
#区域告警
alertareaAlarm_isEffect:1
alertareaAlarm_on:1
alertareaAlarm_expel:1
alertareaAlarm_sensitivity:100
alertareaAlarm_left_up_x:0
alertareaAlarm_left_up_y:0
alertareaAlarm_left_bottom_x:3120
alertareaAlarm_left_bottom_y:1750
alertareaAlarm_right_bottom_x:6880
alertareaAlarm_right_bottom_y:7350
alertareaAlarm_right_up_x:0
alertareaAlarm_right_up_y:7350
alerttime_on:1
alerttime_start:00:00:00
alerttime_end:06:00:00
alerttime_repeat:127
alertspeech_on:1
alertspeech_vol:60
alertspeech_repeat:1
alertlight_on:1
alertlight_mode:1
alertlight_dur:10
alertbuz_on:1
alertbuz_dur:10
alert_targetType:4
alert_staymode_time:80
#越界侦测
transgression_isEffect:1
transgression_on:1
transgression_targetType:8
transgressiontime_on:1
transgressiontime_start:00:00:00
transgressiontime_end:06:00:00
transgressiontime_repeat:127
statistics_line_A_x:13
statistics_line_A_y:15
statistics_line_B_x:14
statistics_line_B_y:1
transgression_statistics_mode_style:1
transgression_statistics_mode_direction:1
transgression_speech_on:1
transgression_speech_vol:1
transgression_speech_repeat:0
transgression_light_on:60
transgression_light_mode:1
transgression_light_dur:10
transgression_buz_on:1
transgression_buz_dur:10


#哭声告警
cryAlarm_isEffect:1
cryAlarm_on:1
cryAlarm_sensitivity:100
crying_pacify_on:1
crying_pacify_audio_playing_count:5
crying_pacify_audio_playing_volumn:1
crying_pacify_audio_playing_url:
#拌网配置，若OVD不具备该能力，该字段不存在
crossAlarm_isEffect:1
crossAlarm_on:1
crossAlarm_sensitivity:100
#撬锁告警
lossLockAlarm_on:1
lossLockAlarm_isEffect:1

[deviceOSD]
OSD_count:2
#布尔型 是否显示
OSD_on_0:0
#可选,通道视频的OSD配置,若设备不支持OSD，该字段不存在
#必填，可读可写，字符串类型：OSD文本内容,若为空串,则当前没有OSD显示
OSD_text_0:1342456
#该osd左上角坐标信息，将图形10000等分
OSD_pos_x_0:1000
OSD_pos_y_0:200
#color 字体颜色默认下发RGB（255，255,255），整数类型，厂家可处理为白色或者为黑白反色或者白色加黑色描边，以最佳用户体验呈现
red_0:10
green_0:20
blue_0:30
#<必填，字体大小，枚举值，0~3; 0代表厂商默认形式，1、2、3分别代码，小、中、大，三个等级>
OSD_font_0:1

#布尔型 是否显示
OSD_on_1:0
#可选,通道视频的OSD配置,若设备不支持OSD，该字段不存在
#必填，可读可写，字符串类型：OSD文本内容,若为空串,则当前没有OSD显示
OSD_text_1:987654321
#该osd左上角坐标信息，将图形10000等分
OSD_pos_x_1:2000
OSD_pos_y_1:3000
#color 字体颜色默认下发RGB（255，255,255），整数类型，厂家可处理为白色或者为黑白反色或者白色加黑色描边，以最佳用户体验呈现
red_1:20
green_1:30
blue_1:220
#<必填，字体大小，枚举值，0~3; 0代表厂商默认形式，1、2、3分别代码，小、中、大，三个等级>
OSD_font_1:2

#osd_logo水印是否显示 布尔型
OSD_logo_on:0

[setableKeys]
#通道使能开关，通道关闭后，应停止该通道的流媒体采集、告警、云台操作等相关功能
channel_on:1
#是否具备自动跟踪功能
traceAbility:0
#扬声器音量
audioOutValume:100
#门铃音量
doorbellOutValume:100
#h264对应枚举值为1,h265则传入2>
video_codec:2
#<必填，可读可写，字符串；可选值为：ld(0)、sd(1)、hd(2)、fhd(3)，分别代表低清，标清，高清，全高清>
video_quality:2

#<目前设备sdk音频需要传入的是带ADTS的aac, 3：AAC_WITH_ADTS 4：AAC>
audio_codec:16
#sd card 0：正常，1：未插卡，2：卡异常
sd_state:0
sd_total_size:256
sd_free_size:100
#整数：输出日志级别，0:trace/1:debug/2:info/3:warn/4:error/5:fatal
logLevel:3
#实时日志上传开关配置项，1为开启，0为关闭，默认为1
logcontrl:1
#可选，可读可写,布尔型：Led灯是否打开。若OVD不具备LED灯控制能力，该字段不存在>s
led:0
#可选，可读可写,布尔型：软探针是否打开。若OVD不具备软探针能力，该字段不存在>s
softprobe_on:0
#可选，可读可写,布尔型：智能模式是否打开。若OVD不具备智能模式能力，该字段不存在>s
smart_mode_switch_on:0
#图像纵向翻转  0-不翻转 1-翻转
horflip:0
#图像横向翻转  0-不翻转 1-翻转
verflip:0
#<可选，可读可写，整形，0：自动，1：开启，2：关闭，若OVD不具备普通摄像头夜视模式配置功能，则该字段不存在>
normal_nightvision_mode:0
#<可选，可读可写，整形，0：黑白夜视，1：全彩夜视，2：智能夜视，若OVD不具备彩色摄像头夜视模式配置功能，则该字段不存在>
color_nightvision_mode:2
#<必填，可读可写,布尔型：使能开关>
autoreboot_on:1
#<必填，可读可写,整型：自动维护（重启）的最短周期，单位秒，例如7天自动重启，可以设置为604800>
autoreboot_cycle:0
#<必填，可读可写,整型：自动维护（重启）开始时间，格式“HH:MM:SS”>
autoreboot_start:00:00:00
#<必填，可读可写,整型：自动维护（重启）结束时间，格式“HH:MM:SS”>设备可在start到end时间内随机选择一个时间重启>
autoreboot_end:02:00:00
#可选，通道定时使能计划，若设备不支持，该字段不存在
#<必填，可读可写,布尔型：定时使能计划开关>
switch_schedule_on:1
#<必填，可读可写,字符串型：通道使能时间，格式“HH:MM:SS”>
switch_start_time:00:00:00
#<必填，可读可写,字符串型：通道关闭时间，格式“HH:MM:SS”>
switch_shutdown_time:05:00:00
#<必填，位运算替代，字符串数组: 计划重复的星期号，数组内的的字符串枚举值可选为Mon(第0位)/Tue(第1位)/Wed(第2位)/Thu(第3位)/Fri(第4位)/Sat(第5位)/Sun(第六位)> 用位运算（0~127）
switch_repeat:127
#可选,通道视频的OSD配置,若设备不支持OSD，该字段不存在
#必填，可读可写，字符串类型：OSD文本内容,若为空串,则当前没有OSD显示
osd_text:demo
#<必填，可读可写，整型：字号大小，1为小字号，2为大字号>
osd_font:1
#<必填，可读可写，字符串枚举型：OSD位置，可选值为top-left/top-right/bottom-left/bottom-right
osd_pos:top-left
#<可选，可读可写,布尔型：机器人防跌落是否打开。若OVD不具备机器人防跌落能力，该字段不存在>
EBO_nofall_on:0
#<可选，可读可写,布尔型：机器人激光是否打开。若OVD不具备机器人激光能力，该字段不存在>
EBO_laser_on:0
#<可选，可读可写,布尔型：机器人追踪开关是否打开>，1.27新增
EBO_track_on:0
#<可选，可读可写,枚举型：1表示人形，2表示宠物追踪>1.27新增
EBO_track_type:2
#<可选，可读可写,布尔型：机器人防碰撞开关是否打开。若OVD不具备机器人防碰撞能力，该字段不存在> 1.29新增
EBO_collision_on:0
#<布尔型，是否开启巡航，默认关>1.29新增
EBO_cruise_on:0
#<可选,可读可写,布尔型：机器人提示音功能是否打开。若OVD不具备机器人提示音能力,该字段不存在,默认关>1.34.0新增
EBO_reminder_on:0
#<必填，可读可写,字符串型：开始巡航时间，格式“HH:MM”>1.29新增
EBO_cruise_start_time:08:00
#<int32型，单位分钟，巡航时长，默认3分钟>1.29新增
EBO_cruise_time_long:10
#<必填，位运算替代，字符串数组: 计划重复的星期号，数组内的的字符串枚举值可选为Mon(第0位)/Tue(第1位)/Wed(第2位)/Thu(第3位)/Fri(第4位)/Sat(第5位)/Sun(第六位)> 用位运算（0~127）1.29新增
EBO_cruise_repeat:127
#<可选，bool型，执行五种固定互动模式后，是否执行出粮指令,默认开>1.33新增
OWL_food_on:0
#<可选，bool型，打开或关闭PIR检测到物体之后，实现五种固定互动模式中的一种,默认开>1.33新增
OWL_track_mode:1
#<可选，bool型，打开或关闭触摸机器人的额头部分进行互动，实现五种固定互动模式中的一种,默认开>1.33新增
OWL_touch_mode:1
#<可选，bool型，打开和关闭互动时音效,默认开>1.33新增
OWL_sound_on:1
#<可选，int型,速度切换，0-低速，1-中速，2-高速>
OWL_speed_switch:0
#<必填，可读可写,字符串型：开始巡航时间，格式“HH:MM”>1.34新增
OWL_cruise_start_time:08:00
#<必填，位运算替代，字符串数组: 计划重复的星期号，数组内的的字符串枚举值可选为Mon(第0位)/Tue(第1位)/Wed(第2位)/Thu(第3位)/Fri(第4位)/Sat(第5位)/Sun(第六位)> 用位运算（0~127）1.34新增
OWL_cruise_repeat:127
#<可选，可读可写，整形，范围0~100,连续取值。若OVD不支持灯光亮度控制，该字段不存在>
light_brightness:50
#<整形，0表示智能控制(默认配置)，1表示手动控制，2表示计划控制>
control_mode:0
#<手动模式（1）下有效。bool型，默认false，表示灯关。若OVD不支持手动控制，该字段不存在>
light_on:0
#<计划控制（2）下有效。计划开关灯，按天循环，根据能力集设置几个时间段，在设置默认的计划内开关灯
#格式如下：[{"start_time":<可读可写，字符串，“hh:mm” 格式>,"end_time":<可读可写，字符串，“hh:mm” 格式>},...],时间段为左闭右开区间>
timed_schedule:

static_scence_ABR_on:0

light_supplement_lamp_on:1
screen:1
nightvision_detect_mode:0
AI_rules_mode_on:0
start_time_0:
end_time_0:
start_time_1:
end_time_1:
collision_alarm_sensitivity:50
parking_notice:10
parking_collision:10
driving_collision_before:10
driving_collision_after:10
voice_capture_before:10
voice_capture_after:10
EBO_speed_adjust:1
[DeviceFeeder]
#<int型，立即喂食，喂食份数，默认2份>
feeder_immed_quantity:2
#<int型，计划喂食条数，默认3条>
feeder_count:3
#<计划喂食条数，第一条>
feeder_plan_on_0:0
#<计划喂食，字符串型，计划名称,UTF8>
feeder_plan_name_0:早餐
#<计划喂食，字符串，“hh:mm” 格式>
feeder_plan_time_0:8:00
#<计划喂食，int型，喂食份数在，默认2份>
feeder_plan_quantity_0:2
#<计划喂食，枚举字符串数组: 计划重复的星期号>
feeder_plan_repeat_0:95
#<计划喂食条数，第二条>
feeder_plan_on_1:0
feeder_plan_name_1:午餐
feeder_plan_time_1:12:00
feeder_plan_quantity_1:2
feeder_plan_repeat_1:31
#<计划喂食条数，第三条>
feeder_plan_on_2:0
feeder_plan_name_2:晚餐
feeder_plan_time_2:16:00
feeder_plan_quantity_2:2
feeder_plan_repeat_2:127
