#!/bin/bash

# 检查是否有足够的参数被传递
if [ "$#" -ne 1 ]; then
    # echo "Usage: $0 <filename>"
    # exit 1
    filename=/home/<USER>/resource/vedio/default.mp4
else
    filename=$1
fi
echo "$filename"

command_to_check="mpv --fs --loop-file=inf --ontop --input-ipc-server=/tmp/mpv-socket \"$filename\""
echo $command_to_check

# 查询进程是否存在
process_exists() {
    # 使用 pgrep 查找进程
    if pgrep mpv > /dev/null; then
        echo "进程存在。"
    else
        echo "进程不存在。"
        eval "$command_to_check"
    fi
    sleep 5
}


export DISPLAY=:0

# 定义要查询的命令

while true; do
    process_exists
done
