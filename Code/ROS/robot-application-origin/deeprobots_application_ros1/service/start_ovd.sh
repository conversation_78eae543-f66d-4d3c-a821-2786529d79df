#!/bin/bash

# 获取当前路径
# CURRENT_DIR=$(pwd)

# 构建程序路径
SCRIPT_DIR=$(dirname "$0")
# 将目录名转换为绝对路径
SCRIPT_DIR=$(cd "$SCRIPT_DIR" && pwd)
echo "$SCRIPT_DIR"

PROGRAM_PATH="${SCRIPT_DIR}/ovd/"

# 检查程序路径是否存在
#if [ ! -f "$PROGRAM_PATH" ]; then
#  echo "程序路径不存在: $PROGRAM_PATH"
#  exit 1
#fi

if pgrep -x "ovd" > /dev/null; then
  echo "ovd 进程已存在，无需启动"
else
  # 启动程序
  echo "正在启动程序: $PROGRAM_PATH"
  cd "$PROGRAM_PATH"
  sudo ./ovd &
fi  