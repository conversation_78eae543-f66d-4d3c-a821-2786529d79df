#!/bin/bash

time=$1
time=$((time+2))
output_mp4=$2
output_flv="${output_mp4%.mp4}.flv"

gst-launch-1.0 v4l2src device=/dev/video0 ! image/jpeg, width=1280, height=720, framerate=30/1 ! jpegdec ! videoconvert ! queue ! mpph264enc ! h264parse ! filesink sync=false location=$output_flv &

recording_pid=$!

sleep $time

kill -2 $recording_pid

echo "录制已保存到 $output_flv"

ffmpeg -y -i "$output_flv" -c:v copy -c:a copy "$output_mp4"

echo "视频已转换为 $output_mp4"
