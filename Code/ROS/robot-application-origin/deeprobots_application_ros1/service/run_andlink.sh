#!/bin/bash
###
 # @Author: 高亚军 <EMAIL>
 # @Date: 2024-09-29 15:15:19
 # @LastEditors: 高亚军 <EMAIL>
 # @LastEditTime: 2024-09-29 15:17:57
 # @FilePath: \robot-application\deeprobots_application_ros1\service\run_deep_rob.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 

source ~/.bashrc 

IP=*************

ping -c 1 $IP > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "Ping to $IP was successful."
    export ROS_MASTER_URI=http://*************:11311
else
    echo "Failed to ping $IP."
    export ROS_MASTER_URI=http://*************:11311
fi
export ROS_IP=*************

source /home/<USER>/robot-application/deeprobots_application_ros1/devel/setup.bash
echo "start roslaunch launch_package speech_ctrl_robdog.launch"
roslaunch launch_package speech_ctrl_robdog.launch &

echo "22222222222222222222222222222222222222"
echo "start sleep 5"
sleep 5 &
#pid=$(sleep 5 &)
#wait $pid
echo "start /home/<USER>/sensorHub/SensorHubNode"

/home/<USER>/sensorHub/SensorHubNode &

# process1_name=SensorHubNode

# process_exists() {
#     pidCount1=`ps -aux|grep $process1_name |grep -v "grep"|grep -v "Z" | wc -l`
#     if [ "$pidCount1" = "0" ]; then
#             echo "$process1_name 进程未运行\n"
#             /home/<USER>/sensorHub/SensorHubNode &
#             echo "$process1_name 进程重新拉取"
#     else
#             echo "$process1_name 进程运行中\n"
#     fi
#     sleep 5
# }

while true; do
    sleep 5
        # process_exists
done