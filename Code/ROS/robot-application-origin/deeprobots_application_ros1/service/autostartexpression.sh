#!/bin/bash

process1_name=mpv

if [ "$#" -ne 1 ]; then
    # echo "Usage: $0 <filename>"
    # exit 1
    filename=/home/<USER>/resource/vedio/default.mp4
else
    filename=$1
fi
echo "$filename"

pidCount1=`ps -aux|grep $process1_name |grep -v "grep"|grep -v "Z" | wc -l`
if [ "$pidCount1" = "0" ]; then
	echo "$process1_name 进程未运行\n"
else
	ps -ef | grep $process1_name |grep -v grep | awk '{print $2}' |xargs kill -9
	echo "$process1_name 进程已杀掉\n"
fi

export DISPLAY=:0
mpv --fs --loop-file=inf --ontop --input-ipc-server=/tmp/mpv-socket "$filename" &

process_exists() {
    pidCount1=`ps -aux|grep $process1_name |grep -v "grep"|grep -v "Z" | wc -l`
    if [ "$pidCount1" = "0" ]; then
	    echo "$process1_name 进程未运行\n"
        mpv --fs --loop-file=inf --ontop --input-ipc-server=/tmp/mpv-socket "$filename" &
    else
	    echo "$process1_name 进程运行中\n"
    fi
    sleep 5
}

while true; do
    process_exists
done
