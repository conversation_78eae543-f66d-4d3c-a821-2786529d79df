#!/bin/bash

# 检查是否安装了 ffmpeg
if ! command -v ffmpeg &> /dev/null; then
    echo "Error: ffmpeg is not installed. Please install ffmpeg first."
    exit 1
fi

# 递归遍历目录并转换视频文件
function convert_videos() {
    local dir="$1"
    echo "Processing directory: $dir"

    # 遍历当前目录下的所有文件和子目录
    for file in "$dir"/*; do
        if [ -d "$file" ]; then
            # 如果是目录，递归处理
            convert_videos "$file"
        elif [ -f "$file" ] && [[ "$file" =~ \.mp4$ ]]; then
            # 如果是 MP4 文件，检查编码方式
            local codec=$(ffprobe -v error -select_streams v:0 -show_entries stream=codec_name -of default=noprint_wrappers=1:nokey=1 "$file")
            if [ "$codec" == "h264" ]; then
                # 获取颜色采样格式
                local pix_fmt=$(ffprobe -v error -select_streams v:0 -show_entries stream=pix_fmt -of default=noprint_wrappers=1:nokey=1 "$file")
                if [ "$pix_fmt" == "yuv444p" ]; then
                    echo "Converting file: $file"
                    # 使用临时文件名
                    local temp_file="${file}tmp.mp4"
                    # 确保输出目录存在
                    mkdir -p "$(dirname "$temp_file")"
                    # 转换颜色空间为 YUV420
                    ffmpeg -i "$file" -c:v libx264 -pix_fmt yuv420p -c:a copy "$temp_file"
                    if [ $? -eq 0 ]; then
                        # 替换原文件
                        mv "$temp_file" "$file"
                        echo "Conversion successful: $file"
                    else
                        echo "Error: Conversion failed for file: $file"
                        exit 1
                    fi
                else
                    echo "Skipping file: $file (not YUV444)"
                fi
            else
                echo "Skipping file: $file (not h264)"
            fi
        fi
    done
}

# 检查参数
if [ -z "$1" ]; then
    echo "Usage: $0 <directory>"
    exit 1
fi

# 开始处理指定目录
convert_videos "$1"