#!/bin/sh

# 定义顶级目录
top_dir="src"

# 用于存储所有生成的deb包路径的临时文件
temp_file=$(mktemp)

# 查找src目录下的一级子目录并执行bloom - generate命令
for sub_dir in $(find "$top_dir" -maxdepth 1 -type d); do
    # 排除顶级目录本身
    if [ "$sub_dir" != "$top_dir" ]; then
        echo "Generating Debian in $sub_dir"
        (cd "$sub_dir" && bloom-generate rosdebian --os-name ubuntu --ros-distro noetic)
        if [ $? -ne 0 ]; then
            echo "Error generating Debian in $sub_dir"
            continue
        fi

        # 生成Debian目录后执行编译命令
        (cd "$sub_dir" && fakeroot debian/rules binary)
        if [ $? -ne 0 ]; then
            echo "Error building Debian package in $sub_dir"
            continue
        fi

        # 将生成的deb包路径添加到临时文件中
        for deb in "$top_dir"/*.deb; do
            if [ -f "$deb" ]; then
                echo "$deb" >> "$temp_file"
            fi
        done
    fi
done

# 安装所有的deb包
if [ -s "$temp_file" ]; then
    echo "Installing all generated Debian packages"
    xargs -a "$temp_file" sudo dpkg -i
    if [ $? -ne 0 ]; then
        echo "Error installing Debian packages"
    fi
else
    echo "No Debian packages generated to install"
fi

# 清理临时文件
rm -f "$temp_file"