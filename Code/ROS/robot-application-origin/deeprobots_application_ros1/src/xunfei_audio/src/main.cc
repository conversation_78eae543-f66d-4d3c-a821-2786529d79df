#ifdef XUNFEI
#include "xunfei_audio_node.h"
#else
#include "baidu_audio_node.h"
#endif

int main(int argc, char *argv[])
{
    char tmp[256];
    getcwd(tmp, 256);
    ROS_INFO("Current working directory: %s\n", tmp);
    ros::init(argc, argv, "xunfei_audio_node");
    ros::NodeHandle nh;
#ifdef XUNFEI
    auto xunfei = std::make_shared<XunfeiAudioNode>(nh);
#else
    auto baidu = std::make_shared<BaiduAudioNode>(nh);
    baidu->init();
#endif
    ros::spin();
    return 0;
}
