#include <stdint.h>

extern "C" {
#include "vs_define.h"
}

//音频降噪模式转换为整数
int32_t nrModeToInt32(VS_NR_Mode_e mode) {
    return static_cast<int32_t>(mode);
}

//从整数转换回音频降噪模式
VS_NR_Mode_e int32ToNrMode(int32_t mode) {
    return static_cast<VS_NR_Mode_e>(mode);
}

const char* int32To<PERSON>har(int32_t mode) {
    switch (int32ToNrMode(mode))
    {
    case NR_MODE_NONE:
        return "NR_MODE_NONE";
    case NR_MODE_INTERACT:
        return "NR_MODE_INTERACT";
    case NR_MODE_CALL:
        return "NR_MODE_CALL";
    case NR_MODE_MAX:
        return "NR_MODE_MAX";
    default:
        return "";
    }
}