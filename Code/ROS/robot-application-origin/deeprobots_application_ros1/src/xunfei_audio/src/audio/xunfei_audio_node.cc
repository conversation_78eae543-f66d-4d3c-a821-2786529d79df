#include "xunfei_audio_node.h"


XunfeiAudioNode::XunfeiAudioNode(ros::NodeHandle& nh) : nh_(nh)
{
	VS_CB.vs_voice_nr_data = vs_voice_nr_data;
    VS_CB.vs_voice_origin_data = vs_voice_origin_data;
	VS_CB.vs_event_cb = vs_event_cb;

    while(VS_init(VS_CB) != 0) {
        ROS_INFO("======================= failed to init xunfei sdk =======================\n");
        //SDK初始化失败，等待一段时间重试
        usleep(1000 * 1000* 10);
    }

    pushStream_ = std::make_shared<ros::Publisher>(nh_.advertise<xunfei_audio::PCMStream>("/audio_node/pcm_stream", 10));
    pushOrigin_ = std::make_shared<ros::Publisher>(nh_.advertise<xunfei_audio::PCMStream>("/audio_node/pcm_origin", 10));
    pushWakeup_ = std::make_shared<ros::Publisher>(nh_.advertise<xunfei_audio::Wakeup>("/audio_node/wakeup_event", 10));
    modeServer_ = nh.advertiseService("/audio_node/set_nr_mode_service", &XunfeiAudioNode::setNrMode, this);
    wakeServer_ = nh.advertiseService("/audio_node/set_wake_event_service", &XunfeiAudioNode::setWakeEvent, this);

    VS_set_nr_mode(NR_MODE_INTERACT);
    ROS_INFO("======================= init finished =======================\n");
}

XunfeiAudioNode::~XunfeiAudioNode()
{
    while(VS_unInit() != 0) {
        ROS_INFO("======================= failed to uninit xunfei sdk =======================\n");
        //SDK反初始化失败，等待一段时间重试
        usleep(1000 * 1000* 10);
    }
    ROS_INFO("======================= uninit finished =======================\n");
}

bool XunfeiAudioNode::setNrMode(xunfei_audio::SetNrMode::Request& req, xunfei_audio::SetNrMode::Response& resp)
{
    VS_NR_Mode_e mode = int32ToNrMode(req.vs_nr_mode);
    if(mode == NR_MODE_NONE || mode == NR_MODE_INTERACT || mode == NR_MODE_CALL || mode == NR_MODE_MAX) {
        VS_set_nr_mode(mode);
        resp.vs_nr_mode = nrModeToInt32(VS_get_nr_mode());
        ROS_INFO("Successed change nr mode to %s\n", int32ToChar(req.vs_nr_mode));
        return true;
    } else {
        resp.vs_nr_mode = nrModeToInt32(VS_get_nr_mode());
        ROS_ERROR("No such nr mode, current mode is %s\n", int32ToChar(resp.vs_nr_mode));
        return false;
    }
}

bool XunfeiAudioNode::setWakeEvent(xunfei_audio::SetWakeEvent::Request& req, xunfei_audio::SetWakeEvent::Response& resp)
{
    isWaking = req.target;
    resp.current = isWaking;

    if(resp.current) {
        ROS_INFO("Wakeup event is running.\n");
    } else {
        ROS_INFO("Wakeup event is abort.\n");
    }

    return true;
}

int XunfeiAudioNode::vs_voice_nr_data(unsigned char *data, int size, int mode, VS_VAD_Status_e vad_status)
{
    // ROS_INFO("======================= voice receviced =======================\n");
    auto now = std::chrono::system_clock::now();
    auto ts = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    xunfei_audio::PCMStream msg;
    msg.ts = ts;
    msg.data = std::vector<unsigned char>(data, data+size);
    pushStream_->publish(msg);

    // 将收到的降噪音频写入文件
    // if (NULL == g_audio_pcm_fp_output) {
    //     g_audio_pcm_fp_output = fopen("cmcc_audio/xunfei16000.pcm", "a");
    //     if (!g_audio_pcm_fp_output) {
    //         ROS_INFO("failed to open xunfei16000 file!!!\n");
    //         return -1;
    //     }
    // }
    // int write_data_len = fwrite(data, 1, size, g_audio_pcm_fp_output);
	// fflush(g_audio_pcm_fp_output);

    return 0;
}

int XunfeiAudioNode::vs_voice_origin_data(unsigned char *data, int size, int mode)
{
    // ROS_INFO("======================= origin voice receviced =======================\n");
    auto now = std::chrono::system_clock::now();
    auto ts = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    xunfei_audio::PCMStream msg;
    msg.ts = ts;
    msg.data = std::vector<unsigned char>(data, data+size);
    pushOrigin_->publish(msg);

    return 0;
}

int XunfeiAudioNode::vs_event_cb(IFLY_EVENT_e event_type, void *data)
{
    // ROS_INFO("======================= event receviced =======================\n");
    int ret = 0;

	if(data == NULL)
	{
		return -1;
	}

	switch(event_type)
	{
	case EVENT_WAKEUP_WORD:
		{
				// Event_key_word_st *temp = (Event_key_word_st *)data;
			//	printf("DEMO: EVENT: type[%d] word[%s] info[%s]\n", event_type, temp->keyWord, temp->extraInfo);
            if(isWaking) {
                Event_wakeup_word_st *temp = (Event_wakeup_word_st *)data;
                ROS_INFO("DEMO: EVENT: type[%d] word[%s] angel[%d] info[%s]\n", event_type, temp->ivwWord, temp->angle, temp->extraInfo);

                xunfei_audio::Wakeup msg;
                msg.ivwWord = temp->ivwWord;
                msg.angle = temp->angle;
                if(temp->extraInfo) {
                    msg.extraInfo = temp->extraInfo;
                } else {
                    msg.extraInfo = "";
                }
                pushWakeup_->publish(msg);
            }

			break;
		}
	case EVENT_NR_MODE_SET_RET:
		{
			Event_ret_st *temp = (Event_ret_st *)data;
			printf("DEMO: EVENT: type[%d] ret[%d]\n", event_type, temp->ack);

			break;
		}
	case EVENT_OTA_RET:
		{
			Event_ret_st *temp = (Event_ret_st *)data;
			printf("DEMO: EVENT: type[%d] ret[%d]\n", event_type, temp->ack);

			break;
		}
	case EVENT_WAKEUP_WORD_UPDATE_RET:
		{
			Event_ret_st *temp = (Event_ret_st *)data;
			printf("DEMO: EVENT: type[%d] ret[%d]\n", event_type, temp->ack);

			break;
		}
	default:
		{
			printf("DEMO: EVENT: unsupport type[%d]\n", event_type);
			ret = -2;
			break;
		}
	}

	return ret;
}

bool XunfeiAudioNode::isWaking = true;

std::shared_ptr<ros::Publisher>  XunfeiAudioNode::pushStream_ = nullptr ;

std::shared_ptr<ros::Publisher>  XunfeiAudioNode::pushOrigin_ = nullptr ;

std::shared_ptr<ros::Publisher>  XunfeiAudioNode::pushWakeup_ = nullptr ;

FILE* XunfeiAudioNode::g_audio_pcm_fp_output = NULL;
