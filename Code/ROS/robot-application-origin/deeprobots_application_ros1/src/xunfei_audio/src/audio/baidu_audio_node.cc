#include "baidu_audio_node.h"


BaiduAudioNode::BaiduAudioNode(ros::NodeHandle& nh) : nh_(nh)
{
	VS_CB.voice_nr_data = voice_nr_data;
    VS_CB.voice_origin_data = voice_origin_data;
	VS_CB.event_cb = event_cb;

    pushStream_ = std::make_shared<ros::Publisher>(nh_.advertise<xunfei_audio::PCMStream>("/audio_node/pcm_stream", 10));
    pushOrigin_ = std::make_shared<ros::Publisher>(nh_.advertise<xunfei_audio::PCMStream>("/audio_node/pcm_origin", 10));
    pushWakeup_ = std::make_shared<ros::Publisher>(nh_.advertise<xunfei_audio::Wakeup>("/audio_node/wakeup_event", 10));
    wakeServer_ = nh.advertiseService("/audio_node/set_wake_event_service", &BaiduAudioNode::setWakeEvent, this);
}

BaiduAudioNode::~BaiduAudioNode()
{
    while(duwen_unInit() != 0) {
        ROS_INFO("======================= failed to uninit baidu sdk =======================\n");
        //SDK反初始化失败，等待一段时间重试
        usleep(1000 * 1000* 10);
    }
    ROS_INFO("======================= uninit finished =======================\n");
}

void BaiduAudioNode::init()
{
    if(VS_CB.voice_nr_data==nullptr) ROS_ERROR("VS_CB.voice_nr_data is nullptr");
    if(VS_CB.voice_origin_data==nullptr) ROS_ERROR("VS_CB.voice_origin_data is nullptr");
    if(VS_CB.event_cb==nullptr) ROS_ERROR("VS_CB.event_cb is nullptr");
    while(duwen_init(VS_CB) < 0) {
        ROS_INFO("======================= failed to init baidu sdk =======================\n");
        //SDK初始化失败，等待一段时间重试
        usleep(1000 * 1000* 10);
    }
    ROS_INFO("======================= init finished =======================\n");
}

bool BaiduAudioNode::setWakeEvent(xunfei_audio::SetWakeEvent::Request& req, xunfei_audio::SetWakeEvent::Response& resp)
{
    isWaking = req.target;
    resp.current = isWaking;

    if(resp.current) {
        ROS_INFO("Wakeup event is running.\n");
    } else {
        ROS_INFO("Wakeup event is abort.\n");
    }

    return true;
}

int BaiduAudioNode::voice_nr_data(unsigned char *data, int size)
{
    // ROS_INFO("======================= voice receviced =======================\n");
    auto now = std::chrono::system_clock::now();
    auto ts = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    xunfei_audio::PCMStream msg;
    msg.ts = ts;
    msg.data = std::vector<unsigned char>(data, data+size);
    pushStream_->publish(msg);

    // 将收到的降噪音频写入文件
    // if (NULL == g_audio_pcm_fp_output) {
    //     g_audio_pcm_fp_output = fopen("cmcc_audio/xunfei16000.pcm", "a");
    //     if (!g_audio_pcm_fp_output) {
    //         ROS_INFO("failed to open xunfei16000 file!!!\n");
    //         return -1;
    //     }
    // }
    // int write_data_len = fwrite(data, 1, size, g_audio_pcm_fp_output);
	// fflush(g_audio_pcm_fp_output);

    return 0;
}

int BaiduAudioNode::voice_origin_data(unsigned char *data, int size)
{
    // ROS_INFO("======================= origin voice receviced =======================\n");
    auto now = std::chrono::system_clock::now();
    auto ts = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    xunfei_audio::PCMStream msg;
    msg.ts = ts;
    msg.data = std::vector<unsigned char>(data, data+size);
    pushOrigin_->publish(msg);

    return 0;
}

int BaiduAudioNode::event_cb(EVENT_e event_type, void *data)
{
    // ROS_INFO("======================= event receviced =======================\n");
    int ret = 0;

	if(data == NULL)
	{
		return -1;
	}

	switch(event_type)
	{
	case EVENT_WAKEUP_WORD:
		{
				// Event_key_word_st *temp = (Event_key_word_st *)data;
			//	printf("DEMO: EVENT: type[%d] word[%s] info[%s]\n", event_type, temp->keyWord, temp->extraInfo);
            if(isWaking) {
                Event_wakeup_word_st *temp = (Event_wakeup_word_st *)data;
                ROS_INFO("EVENT: type[%d] word[%s] angel[%d] info[%s]\n", event_type, temp->ivwWord, temp->angle, temp->extraInfo);

                xunfei_audio::Wakeup msg;
                msg.ivwWord = temp->ivwWord;
                msg.angle = temp->angle;
                if(temp->extraInfo) {
                    msg.extraInfo = temp->extraInfo;
                } else {
                    msg.extraInfo = "";
                }
                pushWakeup_->publish(msg);
            }

			break;
		}
	default:
		{
			printf("DEMO: EVENT: unsupport type[%d]\n", event_type);
			ret = -2;
			break;
		}
	}

	return ret;
}

bool BaiduAudioNode::isWaking = true;

std::shared_ptr<ros::Publisher>  BaiduAudioNode::pushStream_ = nullptr ;

std::shared_ptr<ros::Publisher>  BaiduAudioNode::pushOrigin_ = nullptr ;

std::shared_ptr<ros::Publisher>  BaiduAudioNode::pushWakeup_ = nullptr ;
