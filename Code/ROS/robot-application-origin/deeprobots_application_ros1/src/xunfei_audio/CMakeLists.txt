cmake_minimum_required(VERSION 3.0.2)
project(xunfei_audio)

## Compile as C++11, supported in ROS Kinetic and newer
# add_compile_options(-std=c++11)
SET(CMAKE_BUILD_TYPE "Debug")
SET(CMAKE_CXX_FLAGS_DEBUG "$ENV{CXXFLAGS} -O0 -Wall -g -ggdb")
SET(CMAKE_CXX_FLAGS_RELEASE "$ENV{CXXFLAGS} -O3 -Wall")

SET(COMPANY "xunfei")

find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
  message_generation
)

add_message_files(
  FILES
  PCMStream.msg
  Wakeup.msg
)

add_service_files(
  FILES
  SetNrMode.srv
  SetWakeEvent.srv
)

generate_messages(
  DEPENDENCIES
  std_msgs
)

catkin_package(
#  INCLUDE_DIRS include
#  LIBRARIES xunfei_audio
  CATKIN_DEPENDS roscpp std_msgs message_runtime
#  DEPENDS system_lib
)


include_directories(
# include
  ${catkin_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/include
)

if(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64|i386")
    message(STATUS "Detected x86 architecture")

    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib/x86_64/${COMPANY}/)

    if(COMPANY MATCHES "xunfei")
      add_executable(${PROJECT_NAME}_node
          src/main.cc
          src/audio/${COMPANY}_audio_node.cc
          src/audio/vs_define_trans.cc)

      add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
      # 添加 x86 相关的依赖库
      target_compile_definitions(${PROJECT_NAME}_node PUBLIC XUNFEI)
      target_link_libraries(${PROJECT_NAME}_node
        ${catkin_LIBRARIES}
        vs
        cjson
      )
    elseif(COMPANY MATCHES "baidu")
      add_executable(${PROJECT_NAME}_node
          src/main.cc
          src/audio/${COMPANY}_audio_node.cc)

      add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
      # 添加 x86 相关的依赖库
      target_link_libraries(${PROJECT_NAME}_node
        ${catkin_LIBRARIES}
        duerwen_wakeup
        asound
        pthread
      )
    endif()
elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
    message(STATUS "Detected ARM architecture")

    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib/aarch64/${COMPANY}/)

    if(COMPANY MATCHES "xunfei")
      add_executable(${PROJECT_NAME}_node
          src/main.cc
          src/audio/${COMPANY}_audio_node.cc
          src/audio/vs_define_trans.cc)

      add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
      # 添加 arm 相关的依赖库
      target_compile_definitions(${PROJECT_NAME}_node PUBLIC XUNFEI)
      target_link_libraries(${PROJECT_NAME}_node
        ${catkin_LIBRARIES}
        vs
        cjson
      )
    elseif(COMPANY MATCHES "baidu")
      add_executable(${PROJECT_NAME}_node
          src/main.cc
          src/audio/${COMPANY}_audio_node.cc)

      add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
      # 添加 arm 相关的依赖库
      target_link_libraries(${PROJECT_NAME}_node
        ${catkin_LIBRARIES}
        duerwen_wakeup
        asound
        pthread
      )
    endif()
endif()

install(TARGETS ${PROJECT_NAME}_node
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)
