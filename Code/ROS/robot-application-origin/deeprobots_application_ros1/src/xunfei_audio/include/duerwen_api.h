#ifndef __DUERWEN_API_H__
#define __DUERWEN_API_H__

// voice suite(VS) sdk header file
//事件类型
typedef enum {
	EVENT_TYPE_NONE,			//不使用
	EVENT_WAKEUP_WORD,			//唤醒词事件
	EVENT_KEY_WORD,				//命令词事件
	EVENT_NR_MODE_SET_RET,		//降噪模式设置结果
	EVENT_WAKEUP_WORD_UPDATE_RET,//唤醒词更新结果
	EVENT_OTA_RET,				//ota升级结果
}EVENT_e;

//唤醒事件数据结构体
typedef struct {
	char ivwWord[64];	//唤醒词
	int angle;			//声源定位角度
	char *extraInfo;	//扩展信息
}Event_wakeup_word_st;

/**
 * 音频数据采用pcm编码，格式为16K，16bit，1ch
 * voice_nr_data和voice_origin_data回调定时回传降噪音频和原始音频，推荐每次发送音频数据长度1024*2字节
 * 在事件发生时，调用event_cb回调，唤醒词事件使用该回调函数，对应的唤醒事件结构体信息Event_wakeup_word_st通过void *指针传递
 */

typedef struct
{
	/**
	 * 音频降噪数据
	 * @param data	音频数据
	 * @param size	音频数据长度
	 */
	int (*voice_nr_data)(unsigned char *data, int size);

	/**
	 * 原始数据
	 * @param data	音频数据
	 * @param size	音频数据长度
	 */
	int (*voice_origin_data)(unsigned char *data, int size);


	/**
	 * 事件回调
	 * @param event_type	事件类型
	 * @param data	事件数据（需根据事件类型转换给不同的数据结构）
	 */
	int (*event_cb)(EVENT_e event_type, void *data);

}CallBackFunList;

//sdk初始化
int duwen_init(CallBackFunList cb);

//sdk逆初始化
int duwen_unInit(void);

#endif

