#ifndef __XUNFEI_AUDIO_NODE_H__
#define __XUNFEI_AUDIO_NODE_H__

#include <ros/ros.h>
#include <string.h>
#include "xunfei_audio/PCMStream.h"
#include "xunfei_audio/Wakeup.h"
#include "xunfei_audio/SetWakeEvent.h"
#include <memory>

extern "C" {
#include "duerwen_api.h"
}

class BaiduAudioNode
{
private:
    static int voice_nr_data(unsigned char *data, int size);
    static int voice_origin_data(unsigned char *data, int size);
    static int event_cb(EVENT_e event_type, void *data);
    bool setWakeEvent(xunfei_audio::SetWakeEvent::Request& req, xunfei_audio::SetWakeEvent::Response& resp);

    CallBackFunList VS_CB;
    static bool isWaking;
protected:
    ros::NodeHandle nh_;            //节点
    static std::shared_ptr<ros::Publisher> pushStream_;    //声明降噪音频流发布者
    static std::shared_ptr<ros::Publisher> pushOrigin_;    //声明原声音频流发布者
    static std::shared_ptr<ros::Publisher> pushWakeup_;    //声明唤醒事件发布者
    ros::ServiceServer wakeServer_; //声明唤醒开关服务
public:
    BaiduAudioNode(ros::NodeHandle& nh);
    ~BaiduAudioNode();
    void init();
};


#endif
