#ifndef __VS_API_H__
#define __VS_API_H__

// voice suite(VS) sdk header file

typedef struct
{
	/**
	 * 音频降噪数据
	 * @param data	音频数据
	 * @param size	音频数据长度
	 * @param mode	音频降噪模式，详见VS_NR_Mode_e
	 * @param vad_status	vad状态，详见VS_VAD_Status_e
	 */
	int (*vs_voice_nr_data)(unsigned char *data, int size, int mode, VS_VAD_Status_e vad_status);

	/**
	 * 原始数据
	 * @param data	音频数据
	 * @param size	音频数据长度
	 */
	int (*vs_voice_origin_data)(unsigned char *data, int size, int mode);


	/**
	 * 事件回调
	 * @param event_type	事件类型
	 * @param data	事件数据（需根据事件类型转换给不同的数据结构）
	 */
	int (*vs_event_cb)(IFLY_EVENT_e event_type, void *data);

}VS_CallBackFunList;



//sdk初始化
int VS_init(VS_CallBackFunList cb);

//sdk逆初始化
int VS_unInit(void);

//设置降噪模式
int VS_set_nr_mode(VS_NR_Mode_e mode);

//获取当前降噪模式
VS_NR_Mode_e VS_get_nr_mode(void);

//更新唤醒词资源（自定义唤醒词使用）
//mode: 0 追加，1 替换
int VS_update_wakeup_word(char *data, int size, int mode);

//ota升级
int VS_ota(char *data, int size);

#endif

