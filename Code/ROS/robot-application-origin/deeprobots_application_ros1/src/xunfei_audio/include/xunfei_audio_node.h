#ifndef __XUNFEI_AUDIO_NODE_H__
#define __XUNFEI_AUDIO_NODE_H__

#include <ros/ros.h>
#include <string.h>
#include "xunfei_audio/PCMStream.h"
#include "xunfei_audio/Wakeup.h"
#include "xunfei_audio/SetNrMode.h"
#include "xunfei_audio/SetWakeEvent.h"
#include <memory>

extern "C" {
#include "vs_define.h"
#include "vs_api.h"
}

class XunfeiAudioNode
{
private:
    static int vs_voice_nr_data(unsigned char *data, int size, int mode, VS_VAD_Status_e vad_status);
    static int vs_voice_origin_data(unsigned char *data, int size, int mode);
    static int vs_event_cb(IFLY_EVENT_e event_type, void *data);
    // void eventCallback(const rtc::SIGCEvent::ConstPtr& msg);
    bool setNrMode(xunfei_audio::SetNrMode::Request& req, xunfei_audio::SetNrMode::Response& resp);
    bool setWakeEvent(xunfei_audio::SetWakeEvent::Request& req, xunfei_audio::SetWakeEvent::Response& resp);

    VS_CallBackFunList VS_CB;
    static bool isWaking;
protected:
    ros::NodeHandle nh_;            //节点
    static std::shared_ptr<ros::Publisher> pushStream_;    //声明降噪音频流发布者
    static std::shared_ptr<ros::Publisher> pushOrigin_;    //声明原声音频流发布者
    static std::shared_ptr<ros::Publisher> pushWakeup_;    //声明唤醒事件发布者
    ros::ServiceServer modeServer_; //声明降噪模式服务
    ros::ServiceServer wakeServer_; //声明唤醒开关服务
    static FILE* g_audio_pcm_fp_output; //降噪音频输出文件
    // Json::Reader str2json;          //String转Json
public:
    XunfeiAudioNode(ros::NodeHandle& nh);
    ~XunfeiAudioNode();
};


#endif
