#被下级目录makefile调用,CUR_DIR在下级目录定义
SRCDIR = .
INCDIR = .

OBJS = $(SRCS:%.c=%.o)

all: $(SRCDIR)/lib$(LIB).a
$(SRCDIR)/lib$(LIB).a:  $(OBJS)
	@$(AR) -r $@ $(OBJS)

$(OBJDIR):
	@test -d $@ || mkdir $@

$(SRCDIR)/%.o: $(SRCDIR)/%.c
	@echo "$(CC) $@"
	@$(CC) $(DEMO_CFLAGS) -o $@ -c $<
	
$(SRCDIR)/%.o: $(SRCDIR)/%.cpp
	@echo "$(CXX) $@"
	@$(CXX) $(DEMO_CFLAGS) -o $@ -c $<
cleann:
	rm -rf $(SRCDIR)/*.o
	@rm -rf $(SRCDIR)/lib$(LIB).a
	@rm -rf $(SRCDIR)/lib$(LIB).so