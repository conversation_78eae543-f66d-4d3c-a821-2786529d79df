#!/bin/bash

CROSS_COMPILE1=

export CC=${CROSS_COMPILE1}gcc
export CXX=${CROSS_COMPILE1}g++
export LD=${CROSS_COMPILE1}ld
export AR=${CROSS_COMPILE1}ar


# 是否需要指定特殊的链接选项
export SPECIAL_LDFLAGS=#-lz

# 使用示例工程二次编译，需要定义的符号
VERSION_ALL=V1.7.6

HOST_ARCH=$(uname -m)


case $HOST_ARCH in
    x86_64)
        echo "系统架构: x86_64"
        VERSION_ALL=V1.7.3
        # 在这里添加针对 x86_64 架构的操作
        ;;
    armv7l)
        echo "系统架构: armv7l"
        # 在这里添加针对 armv7l 架构的操作
        ;;
    aarch64)
        echo "系统架构: aarch64"
        # 在这里添加针对 aarch64 架构的操作
        ;;
    i386)
        echo "系统架构: i386"
        # 在这里添加针对 i386 架构的操作
        ;;
    *)
        echo "未知的系统架构: $architecture"
        # 在这里添加针对未知架构的操作
        ;;
esac

ANDLINK_DEV_ACCESS_LINK_ENABLE=y
ANDLINK_CFG_NET_ENABLE=y
SEC_LIB_ENABLE=n
SDK_USING_MBEDTLS=y
APP_USING_ANDLINK_CJSON=y
MAIN_APP_LINK_EXTERN_LIBS_STATIC=y

export VERSION_ALL ANDLINK_DEV_ACCESS_LINK_ENABLE ANDLINK_CFG_NET_ENABLE SEC_LIB_ENABLE SDK_USING_MBEDTLS APP_USING_ANDLINK_CJSON
export MAIN_APP_LINK_EXTERN_LIBS_STATIC

# The open source library version used by the ANDLINK:
HTTP_VER=curl-7.88.1
MQTT_VER=mqttclient-master
SSL_VER=mbedtls-2.4.2
COAP_VER=libcoap-4.1.1
JSON_VER=cJSON-1.7.11

BUILD_DIR=
if [ "$1" = "clean" ]; then
make clean
exit 0
fi

echo "user test,Gonna make with $*..."
BUILD_TYPE=debug
if [ -n "$2" ] ;then
    BUILD_TYPE=$2
fi
make all
