/*
 * 文件名称：andlink_app_utils.c
 * 说 明:集成andlink SDK,生成应用程序,所需要的工具接口实现.
 * 完成日期：2024年4月24日
 */
#include "andlink_adapt.h"

/************************************************************************
Description: 执行shell命令行,按行读取结果
Input:  const char *cmd, char res[][128], int count
Output: char res[][128]
Return: 行数
Others: execute shell command,返回结果的总行数,输出返回结果
************************************************************************/
int shellExecuteCmdAndReadResultsByRow(const char *cmd, char res[][128], int count)
{
    DEMO_DBG_PRINT("[enter]shellExecuteCmd: %s\n", cmd);

    FILE *pp = popen(cmd, "r");
    if (!pp)
    {
        DEMO_DBG_PRINT_ERROR("cannot popen cmd: %s\n", cmd);
        return -1;
    }

    int i = 0;
    char tmp[512] = { 0 };
    int endCharacterIndex = 0;
    while (fgets(tmp, sizeof(tmp), pp) != NULL)
    {
        endCharacterIndex = (int)strlen(tmp) - 1;
        if ((endCharacterIndex < sizeof(tmp)) && (tmp[endCharacterIndex] == '\n'))
        {
            tmp[endCharacterIndex] = '\0';
        }
        DEMO_DBG_PRINT("line[%d]:get return results: %s\n", i, tmp);
        strncpy(res[i], tmp, sizeof(res[0]));
        i++;
        if (i >= count)
        {
            DEMO_DBG_PRINT("already got enough results\n");
            break;
        }
        memset(tmp, 0, sizeof(tmp));
    }

    pclose(pp);

    return i;
}

/************************************************************************
Description: 执行shell命令行,按块读取结果
Input: const char *cmd, char *bufferOut, int bufferSize
Output: char *bufferOut
Return: 总字节数
Others: execute shell command，返回结果的总字节数，输出返回结果
************************************************************************/
int shellExecuteCmdAndReadResultsByBlock(const char *cmd, char *bufferOut, int bufferSize)
{
    int oneReadMaxSize = 256;
    DEMO_DBG_PRINT("[enter]shellExecuteCmdFreadRet: %s\n", cmd);

    if (NULL == bufferOut)
    {
        DEMO_DBG_PRINT_ERROR("func input parameter invalid\n");
        return -1;
    }

    if (bufferSize < oneReadMaxSize)
    {
        DEMO_DBG_PRINT_ERROR("bufferSize(%d) is too small,minSize =%d\n", bufferSize, oneReadMaxSize);
        return -1;
    }

    FILE *pp = popen(cmd, "r");
    if (!pp)
    {
        DEMO_DBG_PRINT_ERROR("cannot popen cmd: %s\n", cmd);
        return -1;
    }

    char *buff = bufferOut;
    int onereadSize = 0, totalreadSize = 0;

    while ((onereadSize = fread(buff + totalreadSize, 1, oneReadMaxSize, pp)))
    {
        totalreadSize += onereadSize;

        if (totalreadSize + oneReadMaxSize > bufferSize)
        {
            DEMO_DBG_PRINT("already got enough results\n");
            break;
        }
        DEMO_DBG_PRINT("onereadSize=%d,totalreadSize=%d,get return results: %s\n", onereadSize, totalreadSize, buff);
    }

    pclose(pp);

    return totalreadSize;
}

/************************************************************************
Description: 获取进程号
Input: const char Name[]
Output: None
Return: 进程号
Others:
************************************************************************/
int get_pid(const char Name[])
{
    int len;
    char name[20] = { 0 };
    len = strlen(Name);
    strncpy(name, Name, len);
    name[len] = '\0';
    char cmdresult[256] = { 0 };
    char cmd[20] = { 0 };
    FILE *pFile = NULL;
    int pid = 0;

    snprintf(cmd, sizeof(cmd)-1, "pidof %s", name);
    pFile = popen(cmd, "r");
    if (pFile != NULL)
    {
        while (fgets(cmdresult, sizeof(cmdresult), pFile))
        {
            pid = atoi(cmdresult);
            break;
        }
    }
    pclose(pFile);
    return pid;
}
/************************************************************************
Description: 从字符串中提取空格分隔的子字符串
Input: char *instr, char outBuffer[][64], int bufSize
Output: char outBuffer[][64]
Return: 返回子字符串的个数
Others:
************************************************************************/
int extractSubstring(char *instr, char outBuffer[][64], int bufSize)
{
    char *start = NULL, *end = NULL;
    int separatorIndex = 0;
    int subStringLen = 0;
    int subStringNum = 0;

    if (NULL == instr || 0 == strlen(instr))
    {
        return -1;
    }

    // 去掉字符串开头的空格;
    while ((*instr) != '\0')
    {
        if ((*instr) == ' ')
        {
            instr++;
        }
        else
        {
            break;
        }
    }

    // 说明全是空格
    if ((*instr) == '\0')
    {
        return -1;
    }

    // 现在这个字符串一开始就是字符了,不是空格开始
    for (separatorIndex = 0; separatorIndex < bufSize; separatorIndex++)
    {
        start = instr; // 子字符串首字符位置
        while (1)
        {
            instr++;
            if (((*instr) == ' ') || ((*instr) == 9) || ((*instr) == '\0')) // 空格,TAB,字符串结束
            {
                end = instr - 1; // 子字符串尾字符位置
                break;
            }
        }

        subStringLen = (end - start + 1 < sizeof(outBuffer[0])) ? (end - start + 1) : sizeof(outBuffer[0]) - 1;
        strncpy(outBuffer[separatorIndex], start, subStringLen);
        outBuffer[separatorIndex][subStringLen] = '\0';
        subStringNum++;

        // 空格位置
        while ((*instr) == ' ' || (*instr) == 9)
        {
            instr++;
        }
        if (((*instr) == '\0'))
        {
            break;
        }
    }

    return subStringNum;
}
/************************************************************************
Description: 检查文件是否为空
Input: char *filename
Output: None
Return: 文件长度
Others:
************************************************************************/
int file_is_null(char *filename)
{
    int length = -1;
    FILE *fp = fopen(filename, "r");
    if (fp == NULL)
    {
        return length;
    }
    fseek(fp, 0, SEEK_END);
    if (ftell(fp) > 80)
        length = 0;
    fclose(fp);
    return length;
}

/************************************************************************
Description: 实时获取网口的IP或广播地址
Input: char *ifname, char *outIpBuffer, char *outBcastBuffer, int addrBufferSize
Output: char *outIpBuffer, char *outBcastBuffer
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int if_get_ipv4(char *ifname, char *outIpBuffer, char *outBcastBuffer, int addrBufferSize)
{
    struct ifaddrs *ifAddrStruct = NULL;
    struct ifaddrs *ifa = NULL;
    void *tmpAddrPtr = NULL;
    int len = 0;
    unsigned int ipAddr = 0;
    unsigned int bcastAddr = 0;
    unsigned int maskAddr = 0;

    getifaddrs(&ifAddrStruct);
    if (-1 == getifaddrs(&ifAddrStruct))
    {
        printf("call getifaddrs error\n");
        return -1;
    }

    for (ifa = ifAddrStruct; ifa != NULL; ifa = ifa->ifa_next)
    {
        if (!ifa->ifa_addr)
        {
            continue;
        }

        if (!ifa->ifa_netmask)
        {
            continue;
        }
        if (ifa->ifa_addr->sa_family == AF_INET && 0 == strcmp(ifa->ifa_name, ifname)) // check it is IP4
        {
            // is a valid IP4 Address
            char addressBuffer[INET_ADDRSTRLEN];

            // ip地址
            tmpAddrPtr = &((struct sockaddr_in *)ifa->ifa_addr)->sin_addr;
            inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);
            printf("%s IP Address %s\n", ifa->ifa_name, addressBuffer);
            len = strlen(addressBuffer) < addrBufferSize ? strlen(addressBuffer) : addrBufferSize - 1;
            memcpy(outIpBuffer, addressBuffer, len);

            ipAddr = htonl(inet_addr(addressBuffer));

            // 掩码地址
            memset(addressBuffer, 0, sizeof(addressBuffer));
            tmpAddrPtr = &((struct sockaddr_in *)ifa->ifa_netmask)->sin_addr;
            inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);
            printf("%s mask Address %s\n", ifa->ifa_name, addressBuffer);
            maskAddr = htonl(inet_addr(addressBuffer));

            // 广播地址
            struct in_addr ip_addr;
            memset(addressBuffer, 0, sizeof(addressBuffer));
            bcastAddr = ipAddr | ~maskAddr;
            ip_addr.s_addr = htonl(bcastAddr);
            inet_ntop(AF_INET, &ip_addr, addressBuffer, INET_ADDRSTRLEN);
            printf("%s bcast Address %s\n", ifa->ifa_name, addressBuffer);
            len = strlen(addressBuffer) < addrBufferSize ? strlen(addressBuffer) : addrBufferSize - 1;
            memcpy(outBcastBuffer, addressBuffer, len);
        }
    }

    if (ifAddrStruct != NULL)
    {
        freeifaddrs(ifAddrStruct);
    }

    return 0;
}
/************************************************************************
Description: 实时获取设备IF_NAME口的MAC
Input: char *macAddress, int maxMacAddressLen
Output: char *macAddress
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int get_device_mac_address(char *ifname, char *macAddress, int maxMacAddressLen)
{
    FILE *fp = NULL;
    char *p = NULL;
    char macFile[40] = { 0 };
    char lineContent[128] = { 0 };
    int ret = -1;

    if (NULL == macAddress || 0 == maxMacAddressLen)
    {
        return ret;
    }

    snprintf(macFile, sizeof(macFile) - 1, "/sys/class/net/%s/address", ifname);

    fp = fopen(macFile, "r");
    if (NULL == fp)
    {
        DEMO_DBG_PRINT_ERROR("open %s failed\n", macFile);
        return ret;
    }
    DEMO_DBG_PRINT("open %s success\n", macFile);

    while (NULL != fgets(lineContent, sizeof(lineContent) - 1, fp))
    {
        // 过滤包含#的注释行
        if (NULL != (p = strstr(lineContent, "#")))
        {
            continue;
        }

        p = lineContent;
        if (NULL != p)
        {
            strncpy(macAddress, p, maxMacAddressLen);
            p = strchr(macAddress, '\n');
            if (NULL != p)
            {
                *p = 0;
            }
            ret = 0;
            DEMO_DBG_PRINT("read ifname(%s) success(%s)\n", ifname, macAddress);
            goto EXIT;
        }
    }
EXIT:
    fclose(fp);
    return ret;
}


/************************************************************************
Description:    向文件fileName中写入一个键值对配置项
Input:          const char *fileName, const char *item, const char *value
Output:         None
Return:         成功:0, 失败:-1
Others:         举例:writeCfgItem("/tmp/fac.conf", "deviceType", DEVICE_TYPE);
************************************************************************/
int writeCfgItem(const char *fileName, const char *item, const char *value)
{
    FILE *fp = NULL;
    FILE *fp_tmp = NULL;
    char buf[256];
    char *p = NULL;
    int tmpFileNameLen = strlen(fileName);
    char tmpfile[tmpFileNameLen + 10];
    memset(tmpfile, 0, tmpFileNameLen + 1);

    sprintf(tmpfile, "%s.tmp", fileName);
    int itemExist = 0;

    if ((NULL == item) || (NULL == value))
    {
        return -1;
    }

    fp = fopen(fileName, "r");

    fp_tmp = fopen(tmpfile, "w");
    if (NULL == fp_tmp)
    {
        if (fp)
        {
            fclose(fp);
            fp = NULL;
        }
        return -1;
    }

    if (NULL != fp)
    {
        while (NULL != fgets(buf, sizeof(buf), fp))
        {
            p = strstr(buf, item);
            if (NULL != p && 0 == strncmp(buf, item, strlen(item)))
            {
                strcpy(p + strlen(item) + 1, value);
                p += strlen(item) + 1 + strlen(value);
                *p = 0;
                fprintf(fp_tmp, "%s\n", buf);
                itemExist = 1;
            }
            else
            {
                fprintf(fp_tmp, "%s", buf);
            }
        }

        if (fp)
        {
            fclose(fp);
            fp = NULL;
        }

        unlink(fileName);
    }

    if (0 == itemExist)
    {
        sprintf(buf, "%s=%s", item, value);
        fprintf(fp_tmp, "%s\n", buf);
    }

    if (fp_tmp)
    {
        fclose(fp_tmp);
        fp_tmp = NULL;
    }

    rename(tmpfile, fileName);
    unlink(tmpfile);
    return 0;
}

/************************************************************************
Description:    从文件fileName中读取一个键值对配置项
Input:          char *filename, char *item, char *outbuf, int bufsize
Output:         char *outbuf
Return:         成功:0, 失败:-1
Others:         举例:readCfgItem("/tmp/fac.conf", "deviceType", lineBuf, sizeof(lineBuf) - 1));
************************************************************************/
int readCfgItem(char *filename, char *item, char *outbuf, int bufsize)
{
    FILE *fp = NULL;
    char lineContent[256] = { 0 };
    char *p = NULL;

    if ((NULL == filename) || (NULL == item) || (NULL == outbuf))
    {
        DEMO_DBG_PRINT("func parameter NULL\n");
        return -1;
    }

    fp = fopen(filename, "r");
    if (NULL == fp)
    {
        return -1;
    }

    while (NULL != fgets(lineContent, sizeof(lineContent) - 1, fp))
    {
        // 过滤包含#的注释行
        if (NULL != (p = strstr(lineContent, "#")))
        {
            continue;
        }

        p = strstr(lineContent, item);
        if (NULL != p && 0 == strncmp(lineContent, item, strlen(item)))
        {
            // DBG_PRINT("item exist p %s value %s\r\n", p,p+strlen(item)+1);

            strncpy(outbuf, p + strlen(item) + 1, bufsize);
            p = strchr(outbuf, '\n');
            if (NULL != p)
            {
                *p = 0;
            }
            goto EXIT;
        }
    }

FAIL_EXIT:
    fclose(fp);
    fp = NULL;
    return -1;

EXIT:
    fclose(fp);
    fp = NULL;
    return 0;
}