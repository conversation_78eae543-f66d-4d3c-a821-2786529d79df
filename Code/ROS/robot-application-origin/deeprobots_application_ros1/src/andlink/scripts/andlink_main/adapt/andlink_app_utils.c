/*
 * 文件名称：andlink_app_utils.c
 * 说 明:集成andlink SDK,生成应用程序,所需要的工具接口实现.
 * 完成日期：2024年4月24日
 */
#include "andlink_adapt.h"

/************************************************************************
Description: 执行shell命令行,按行读取结果
Input:  const char *cmd, char res[][128], int count
Output: char res[][128]
Return: 行数
Others: execute shell command,返回结果的总行数,输出返回结果
************************************************************************/
int shellExecuteCmdAndReadResultsByRow(const char *cmd, char res[][128], unsigned long count)
{
    DEMO_DBG_PRINT("[enter]shellExecuteCmd: %s\n", cmd);

    FILE *pp = popen(cmd, "r");
    if (!pp)
    {
        DEMO_DBG_PRINT_ERROR("cannot popen cmd: %s\n", cmd);
        return -1;
    }

    int i = 0;
    char tmp[512] = { 0 };
    int endCharacterIndex = 0;
    while (fgets(tmp, (int)sizeof(tmp), pp) != NULL)
    {
        endCharacterIndex = (int)app_strlen(tmp) - 1;
        if ((endCharacterIndex < (int)sizeof(tmp)) && (tmp[endCharacterIndex] == '\n'))
        {
            tmp[endCharacterIndex] = '\0';
        }
        DEMO_DBG_PRINT("line[%d]:get return results: %s\n", i, tmp);
        app_strncpy_s(res[i], sizeof(res[0]) - 1, tmp, app_strlen(tmp));
        i++;
        if (i >= (int)count)
        {
            DEMO_DBG_PRINT("already got enough results\n");
            break;
        }
        memset(tmp, 0, sizeof(tmp));
    }

    pclose(pp);

    return i;
}

/************************************************************************
Description: 执行shell命令行,按块读取结果
Input: const char *cmd, char *bufferOut, int bufferSize
Output: char *bufferOut
Return: 总字节数
Others: execute shell command，返回结果的总字节数，输出返回结果
************************************************************************/
int shellExecuteCmdAndReadResultsByBlock(const char *cmd, char *bufferOut, unsigned long bufferSize)
{
    unsigned long oneReadMaxSize = 256;
    DEMO_DBG_PRINT("[enter]shellExecuteCmdFreadRet: %s\n", cmd);

    if (NULL == bufferOut)
    {
        DEMO_DBG_PRINT_ERROR("func input parameter invalid\n");
        return -1;
    }

    if (bufferSize < oneReadMaxSize)
    {
        DEMO_DBG_PRINT_ERROR("bufferSize(%lu) is too small,minSize =%lu\n", bufferSize, oneReadMaxSize);
        return -1;
    }

    FILE *pp = popen(cmd, "r");
    if (!pp)
    {
        DEMO_DBG_PRINT_ERROR("cannot popen cmd: %s\n", cmd);
        return -1;
    }

    char *buff = bufferOut;
    unsigned long onereadSize = 0, totalreadSize = 0;

    while ((onereadSize = fread(buff + totalreadSize, 1, oneReadMaxSize, pp)))
    {
        totalreadSize += onereadSize;

        if (totalreadSize + oneReadMaxSize > bufferSize)
        {
            DEMO_DBG_PRINT("already got enough results\n");
            break;
        }
        DEMO_DBG_PRINT("onereadSize=%lu,totalreadSize=%lu,get return results: %s\n", onereadSize, totalreadSize, buff);
    }

    pclose(pp);

    return (int)totalreadSize;
}

/************************************************************************
Description: 获取进程号
Input: const char Name[]
Output: None
Return: 进程号
Others:
************************************************************************/
int get_pid(const char NAME[])
{
    unsigned int len;
    char name[20] = { 0 };
    len = app_strlen(NAME);
    app_strncpy_s(name, sizeof(name) - 1, NAME, len);
    name[len] = '\0';
    char cmdresult[256] = { 0 };
    char cmd[20] = { 0 };
    FILE *pFile = NULL;
    int pid = 0;

    app_snprintf_s(cmd, sizeof(cmd) - 1, "pidof %s", name);
    pFile = popen(cmd, "r");
    if (pFile != NULL)
    {
        while (fgets(cmdresult, (int)sizeof(cmdresult), pFile))
        {
            pid = atoi(cmdresult);
            break;
        }
    }
    pclose(pFile);
    return pid;
}
/************************************************************************
Description: 从字符串中提取空格分隔的子字符串
Input: char *instr, char outBuffer[][64], int bufSize
Output: char outBuffer[][64]
Return: 返回子字符串的个数
Others:
************************************************************************/
int extractSubstring(char *instr, char outBuffer[][64], unsigned long bufSize)
{
    char *start = NULL, *end = NULL;
    int separatorIndex = 0;
    int subStringNum = 0;

    if (NULL == instr || 0 == app_strlen(instr))
    {
        return -1;
    }

    // 去掉字符串开头的空格;
    while ((*instr) != '\0')
    {
        if ((*instr) == ' ')
        {
            instr++;
        }
        else
        {
            break;
        }
    }

    // 说明全是空格
    if ((*instr) == '\0')
    {
        return -1;
    }

    // 现在这个字符串一开始就是字符了,不是空格开始
    for (separatorIndex = 0; separatorIndex < (int)bufSize; separatorIndex++)
    {
        start = instr; // 子字符串首字符位置
        while (1)
        {
            instr++;
            if (((*instr) == ' ') || ((unsigned char)(*instr) == 9) || ((*instr) == '\0')) // 空格,TAB,字符串结束
            {
                end = instr - 1; // 子字符串尾字符位置
                break;
            }
        }

        app_strncpy_s(outBuffer[separatorIndex], sizeof(outBuffer[0]) - 1, start, (unsigned long)(end - start + 1 ));
        subStringNum++;

        // 空格位置
        while ((*instr) == ' ' || (unsigned char)(*instr) == 9)
        {
            instr++;
        }
        if (((*instr) == '\0'))
        {
            break;
        }
    }

    return subStringNum;
}
/************************************************************************
Description: 检查文件是否为空
Input: char *filename
Output: None
Return: 文件长度
Others:
************************************************************************/
int file_is_null(char *filename)
{
    int length = -1;
    FILE *fp = fopen(filename, "r");
    if (fp == NULL)
    {
        return length;
    }
    fseek(fp, 0, SEEK_END);
    if (ftell(fp) > 80)
        length = 0;
    fclose(fp);
    return length;
}

/************************************************************************
Description: 实时获取网口的IP或广播地址
Input: char *ifname, char *outIpBuffer, char *outBcastBuffer, int addrBufferSize
Output: char *outIpBuffer, char *outBcastBuffer
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int if_get_ipv4(char *ifname, char *outIpBuffer, char *outBcastBuffer, unsigned int addrBufferSize)
{
    struct ifaddrs *ifAddrStruct = NULL;
    struct ifaddrs *ifa = NULL;
    void *tmpAddrPtr = NULL;
    unsigned long ipAddr = 0;
    unsigned long bcastAddr = 0;
    unsigned long maskAddr = 0;

    getifaddrs(&ifAddrStruct);
    if (-1 == getifaddrs(&ifAddrStruct))
    {
        printf("call getifaddrs error\n");
        return -1;
    }

    for (ifa = ifAddrStruct; ifa != NULL; ifa = ifa->ifa_next)
    {
        if (!ifa->ifa_addr)
        {
            continue;
        }

        if (!ifa->ifa_netmask)
        {
            continue;
        }
        if (ifa->ifa_addr->sa_family == AF_INET && 0 == strcmp(ifa->ifa_name, ifname)) // check it is IP4
        {
            // is a valid IP4 Address
            char addressBuffer[INET_ADDRSTRLEN];

            // ip地址
            tmpAddrPtr = &((struct sockaddr_in *)ifa->ifa_addr)->sin_addr;
            inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);
            printf("%s IP Address %s\n", ifa->ifa_name, addressBuffer);

            app_strncpy_s(outIpBuffer, addrBufferSize, addressBuffer, app_strlen(addressBuffer));

            ipAddr = htonl(inet_addr(addressBuffer));

            // 掩码地址
            memset(addressBuffer, 0, sizeof(addressBuffer));
            tmpAddrPtr = &((struct sockaddr_in *)ifa->ifa_netmask)->sin_addr;
            inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);
            printf("%s mask Address %s\n", ifa->ifa_name, addressBuffer);
            maskAddr = htonl(inet_addr(addressBuffer));

            // 广播地址
            struct in_addr ip_addr;
            memset(addressBuffer, 0, sizeof(addressBuffer));
            bcastAddr = ipAddr | ~maskAddr;
            ip_addr.s_addr = htonl(bcastAddr);
            inet_ntop(AF_INET, &ip_addr, addressBuffer, INET_ADDRSTRLEN);
            printf("%s bcast Address %s\n", ifa->ifa_name, addressBuffer);

            app_strncpy_s(outBcastBuffer, addrBufferSize, addressBuffer, app_strlen(addressBuffer));
        }
    }

    if (ifAddrStruct != NULL)
    {
        freeifaddrs(ifAddrStruct);
    }

    return 0;
}
/************************************************************************
Description: 实时获取设备IF_NAME口的MAC
Input: char *macAddress, int maxMacAddressLen
Output: char *macAddress
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int get_device_mac_address(char *ifname, char *macAddress, unsigned int maxMacAddressLen)
{
    FILE *fp = NULL;
    char *p = NULL;
    char macFile[40] = { 0 };
    char lineContent[128] = { 0 };
    int ret = -1;

    if (NULL == macAddress || 0 == maxMacAddressLen)
    {
        return ret;
    }

    app_snprintf_s(macFile, sizeof(macFile) - 1, "/sys/class/net/%s/address", ifname);

    fp = fopen(macFile, "r");
    if (NULL == fp)
    {
        DEMO_DBG_PRINT_ERROR("open %s failed\n", macFile);
        return ret;
    }
    DEMO_DBG_PRINT("open %s success\n", macFile);

    while (NULL != fgets(lineContent, (int)sizeof(lineContent) - 1, fp))
    {
        // 过滤包含#的注释行
        if (NULL != (p = strstr(lineContent, "#")))
        {
            continue;
        }

        p = lineContent;
        if (NULL != p)
        {
            app_memcpy_s(macAddress, maxMacAddressLen, p, maxMacAddressLen);
            p = strchr(macAddress, '\n');
            if (NULL != p)
            {
                *p = 0;
            }
            ret = 0;
            DEMO_DBG_PRINT("read ifname(%s) success(%s)\n", ifname, macAddress);
            break;
        }
    }

    fclose(fp);
    return ret;
}


/************************************************************************
Description:    向文件fileName中写入一个键值对配置项
Input:          const char *fileName, const char *item, const char *value
Output:         None
Return:         成功:0, 失败:-1
Others:         举例:writeCfgItem("/tmp/fac.conf", "deviceType", DEVICE_TYPE);
************************************************************************/
int writeCfgItem(const char *fileName, const char *item, const char *value)
{
    FILE *fp = NULL;
    FILE *fp_tmp = NULL;
    char buf[256];
    char *p = NULL;
    unsigned long tmpFileNameLen = app_strlen(fileName);
    char tmpfile[tmpFileNameLen + 10];
    memset(tmpfile, 0, tmpFileNameLen + 1);

    app_snprintf_s(tmpfile, sizeof(tmpfile) - 1, "%s.tmp", fileName);
    int itemExist = 0;

    if ((NULL == item) || (NULL == value))
    {
        return -1;
    }

    fp = fopen(fileName, "r");

    fp_tmp = fopen(tmpfile, "w");
    if (NULL == fp_tmp)
    {
        if (fp)
        {
            fclose(fp);
            fp = NULL;
        }
        return -1;
    }

    if (NULL != fp)
    {
        while (NULL != fgets(buf, (int)sizeof(buf), fp))
        {
            p = strstr(buf, item);
            if (NULL != p && 0 == strncmp(buf, item, app_strlen(item)))
            {
                app_strncpy_s(p + app_strlen(item) + 1, sizeof(buf) - app_strlen(item) - 1, value, app_strlen(value));
                p += app_strlen(item) + 1 + app_strlen(value);
                *p = 0;
                fprintf(fp_tmp, "%s\n", buf);
                itemExist = 1;
            }
            else
            {
                fprintf(fp_tmp, "%s", buf);
            }
        }

        if (fp)
        {
            fclose(fp);
            fp = NULL;
        }

        unlink(fileName);
    }

    if (0 == itemExist)
    {
        app_snprintf_s(buf, sizeof(buf) - 1, "%s=%s", item, value);
        fprintf(fp_tmp, "%s\n", buf);
    }

    if (fp_tmp)
    {
        fclose(fp_tmp);
        fp_tmp = NULL;
    }

    rename(tmpfile, fileName);
    unlink(tmpfile);
    return 0;
}

/************************************************************************
Description:    从文件fileName中读取一个键值对配置项
Input:          char *filename, char *item, char *outbuf, int bufsize
Output:         char *outbuf
Return:         成功:0, 失败:-1
Others:         举例:readCfgItem("/tmp/fac.conf", "deviceType", lineBuf, sizeof(lineBuf) - 1));
************************************************************************/
int readCfgItem(char *filename, char *item, char *outbuf, unsigned long bufsize)
{
    FILE *fp = NULL;
    char lineContent[256] = { 0 };
    char *p = NULL;

    if ((NULL == filename) || (NULL == item) || (NULL == outbuf))
    {
        DEMO_DBG_PRINT("func parameter NULL\n");
        return -1;
    }

    fp = fopen(filename, "r");
    if (NULL == fp)
    {
        return -1;
    }

    while (NULL != fgets(lineContent, (int)sizeof(lineContent) - 1, fp))
    {
        // 过滤包含#的注释行
        // if (NULL != (lineContent = strstr(lineContent, "#")))
        if (0 == strncmp(lineContent, "#", app_strlen("#")))
        {
            continue;
        }

        p = strstr(lineContent, item);
        if (NULL != p && 0 == strncmp(lineContent, item, app_strlen(item)))
        {
            // DBG_PRINT("item exist p %s value %s\r\n", p,p+app_strlen(item)+1);
            app_strncpy_s(outbuf, bufsize, p + app_strlen(item) + 1, app_strlen(p) - app_strlen(item) - 1);
            p = strchr(outbuf, '\n');
            if (NULL != p)
            {
                *p = 0;
            }
            goto SUC_EXIT;
        }
    }

// FAIL_EXIT:
    fclose(fp);
    return -1;

SUC_EXIT:
    fclose(fp);
    return 0;
}

/************************************************************************
Description: 安全的字符串拷贝接口
Input: char *dest, size_t dmax, const char *src, size_t slen
Output: char *dest
Return: 成功:0, 失败:-1
Others: 要求dmax>slen,否则输出空字符串
************************************************************************/
int app_strncpy_s(char *dest, size_t dmax, const char *src, size_t slen)
{
    if (dest == NULL || src == NULL)
    {
        return -1;
    }
    if (dmax == 0)
    {
        return -1;
    }
    if (dmax <= slen)
    {
        memset(dest, 0, dmax);
        return -1;
    }

    size_t i;
    for (i = 0; i < slen && src[i] != '\0'; i++)
    {
        dest[i] = src[i];
    }
    if (i < dmax)
    {
        dest[i] = '\0';
    }
    return 0;
}
/************************************************************************
Description: 获取字符串的长度,不包括"\0"
Others:
************************************************************************/
unsigned int app_strlen(const char *s)
{
    if (NULL == s)
    {
        return 0;
    }
    return (unsigned int)strlen(s);
}
/************************************************************************
Description: 安全的snprintf函数
Input: char *str, size_t size, const char *format, ...
Output: None
Return: 成功:格式化的字节数, 失败:-1
Others: 要求dmax>slen,否则截断
************************************************************************/
int app_snprintf_s(char *dest, size_t dmax, const char *format, ...)
{
    if (dest == NULL || format == NULL)
    {
        return -1;
    }
    if (dmax == 0)
    {
        return -1;
    }

    va_list args;
    va_start(args, format);
    // 使用 vsnprintf 进行格式化
    int ret = vsnprintf(dest, dmax, format, args);
    va_end(args);

    // 确保字符串以空字符结尾
    if (ret < 0 || (size_t)ret >= dmax)
    {
        // 如果缓冲区不够大，确保最后一个字符是 '\0'
        if (dmax > 0)
        {
            dest[dmax - 1] = '\0';
        }
        return -1;
    }

    return ret;
}
/************************************************************************
Description: 安全的memcpy
Input: void *dest, size_t destsz, const void *src, size_t count
Output: void *dest
Return: 成功:目标内存首地址, 失败:NULL
Others: 要求dmax>=slen,否则返回NULL
************************************************************************/
int app_memcpy_s(void *dest, size_t destsz, const void *src, size_t count)
{
    if (NULL == dest || NULL == src)
    {
        return -1;
    }

    if (0 == destsz || 0 == count)
    {
        return -1;
    }

    if (count > destsz)
    {
        return -1;
    }

    // Check for overlap
    unsigned long src_ptr = (unsigned long)src;
    unsigned long dest_ptr = (unsigned long)dest;
    if ((src_ptr < dest_ptr && src_ptr + count > dest_ptr) || (dest_ptr < src_ptr && dest_ptr + count > src_ptr))
    {
        return -1;
    }
#if 1
    // Copy memory
    unsigned char *d = dest;
    const unsigned char *s = src;
    while (count--)
    {
        *d++ = *s++;
    }
#else
    memcpy(dest, src, count);
#endif
    return 0;
}
