/*
 *
 * 文件名称：andlink_app_adapt.c
 * 说 明:厂商集成andlink SDK后,进行功能及稳定性测试的接口
 *
 */
#include "andlink_adapt.h"


#if ANDLINK_DEBUG_ENABLE == 1
/************************************************************************
Description:    andlink SDK启动后,随机选择时间点,验证扫码绑定
Input:          int msBegin, int msEnd  单位毫秒
Output:         None
Return:         成功:0, 失败:-1
Others:         测试接口,SDK使用者无需关心
************************************************************************/
void test_randInputUserkey(int msBegin, int msEnd)
{
    int num = 10;
    for (int i = 0; i < num; i++)
    {
        int begin = msBegin;
        int end = msEnd;
        srand((unsigned int)time(NULL));

        int msNum = rand() % (end - begin + 1) + begin;

        int u_num = msNum * 1000; // us

        usleep(u_num);

        DEMO_DBG_PRINT("========demo scan code success,rand sleeped %d ms\r\n", msNum);
#ifdef ANDLINK_DEV_ACCESS_LINK_ENABLE
        setScanCodeBindConfigInfo("biOJWr2qoHyeefzz65KsfJwFzVMA8MYNHz5x17i_BSkJ3bwRDG-w2cf8tKpUKizb", NULL, NULL);
#endif

        sleep(1);
    }
}

/************************************************************************
Description:    andlink SDK启动后,随机选择时间点,验证复位流程
Input:          int msBegin, int msEnd  单位毫秒
Output:         None
Return:         成功:0, 失败:-1
Others:         测试接口,SDK使用者无需关心
************************************************************************/
void test_devReset(int msBegin, int msEnd)
{
    int begin = msBegin;
    int end = msEnd;
    srand((unsigned int)time(NULL));

    int msNum = rand() % (end - begin + 1) + begin;

    int u_num = msNum * 1000; // us

    usleep(u_num);

    DEMO_DBG_PRINT("========demo test devReset %d ms\r\n", msNum);

    devReset();
}

#endif // ANDLINK_DEBUG_ENABLE==1

/************************************************************************
Description: 调试andlink demo程序
Input: int argc, char **argv
Output: None
Return: 成功:0, 失败:-1
Others: 测试接口,供SDK的开发者调试; SDK使用者无需关心
************************************************************************/
int debug_demo_main(int argc, char **argv)
{
    DEMO_DBG_PRINT("[enter]debug_demo_main,argc =%d,argv[0] =%s\r\n", argc, argv[0]);
#if ANDLINK_DEBUG_ENABLE == 1 // 1.调试复位

    demo_main(argc, argv);
#ifdef DEMO_DEV_ACCESS_ENABLE
    // 1.1 andlink SDK启动后,取20-200ms的随机时刻,验证扫码绑定;
    test_randInputUserkey(20, 200);
#else
    // 1.2 andlink SDK启动后,取2s-5s的随机时刻,验证复位流程;
    test_devReset(5000, 10000);
#endif

#elif ANDLINK_DEBUG_ENABLE == 2 // 2.dmalloc调试内存泄漏
    void test_andlinkInit();
    void main_dmalloc_test();

    test_andlinkInit();
    main_dmalloc_test();

    while (1)
    {
        sleep(10);
    }

#elif ANDLINK_DEBUG_ENABLE == 3 // 3.virgrind调试内存泄漏
    void test_andlinkInit();
    test_andlinkInit();
#else
#endif
    return 0;
}