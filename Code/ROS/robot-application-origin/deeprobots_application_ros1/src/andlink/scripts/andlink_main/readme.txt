andlink BLE设备端SDK
# 一、SDK概述
BLE设备集成SDK后，可以支持和家亲APP发现及连接，然后自动获取入网信息(SSID,密码及用户信息),然后自动完成绑定。

# 二、SDK源码目录结构
|------app     主函数及目标程序源文件所在目录
|------lib     目标程序依赖的库文件目录
|------opensrc 目标程序依赖的开源代码目录
    |--bluez    蓝牙协议栈代码
    |--Makefile 控制开源代码编译的Makefile

|------Makefile              编译工程根目录下的主Makefile
|------readme.txt            本工程的简要说明文档,包括SDK概述,工程目录结构,编译方法,源码修改历史等.

# 三、编译方法
编译方法:
(1)全量编译:make type=sdkall
(2)增量编译(只编译核心代码):make