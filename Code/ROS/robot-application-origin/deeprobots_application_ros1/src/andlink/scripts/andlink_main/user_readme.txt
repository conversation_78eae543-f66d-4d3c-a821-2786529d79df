一、SDK相关文档
1、通过第三方平台间接接入Andlink平台的产品（平台接入），一般而言，摄像头、门铃、门禁等是平台接入
包名形如：andlinkDevSdk_Vx.x.x_dm_Vx.x.x.xxxxxx-$PackageDate-$ProductID-$CompilerName-$SSLVer.tar.gz

    1.1、AndlinkDM SDK(Linux)接入指南(平台接入)：
    https://docs.qq.com/doc/DY3pPSlFtTWZ3bVNN

    1.2、AndlinkDM-SDK(linux c) (平台接入)使用常见问题(FAQ)
    https://docs.qq.com/doc/DUFVLbnVTQllvWk1D

2、直接接入Andlink平台的产品（设备接入），一般而言，音箱、台灯、可视门锁、网关等是设备接入
包名形如：andlinkDevSdk_Vx.x.x.xxxxxx-$PackageDate-$ProductID-$CompilerName-$SSLVer.tar.gz

    2.1、Andlink SDK(Linux)接入指南(设备接入)：
    https://docs.qq.com/doc/DUERlaEJIS0RwYXNH

    2.2、Andlink-SDK(linux c) （设备接入）使用常见问题(FAQ)
    https://docs.qq.com/doc/DUHBpUVljbXhteHJr

3、若接入指南和FAQ都无法解决你的问题,可以参考FAQ4.2通过邮件进行提问.

二、工程说明
1.这是提供给厂商使用,可链接andlink库进行二次编译的小工程.

2.工程目录结构如下:
demo(厂商名称)/
|------adapt   厂商需要适配的文件
        |------andlink_adapt.c 集成andlink库,需要适配的文件
        |------xxx_adapt.c 集成其他库,需要适配的文件
        
|------include 头文件存放目录
        |------andlink_adapt.h main.c或andlink_adapt.c需要依赖的头文件,厂商可根据需要更改;
        |------andlink_pub.h   andlink对外提供的头文件,厂商只可引用,不可更改
        
|------lib     库文件存放目录
        |------libandlink.so/a.1.x          andlink库;无须依赖其他库;
        |------libandlink-core.so/a.1.x     andlink核心库;
        须依赖-lcoap -lpaho-mqtt3as -ljson  -lcurl  [-lalsec]  -lssl -lcrypto 
            或-lcoap -lmqttclient -ljson  -lcurl  [-lalsec]  -lmbedtls -lmbedcrypto -lmbedx509

|------bin      可执行demo程序生成目录
|------main.c   主函数所在文件
|------lib.mak  adapt目录需要依赖的Makefile文件
|------Makefile 主Makefile文件
|------run.sh   编译脚本,用于指定工具链路径和名称


三、demo程序验证
1、简单验证demo程序是否可用方法
(1)将bin目录下的demo文件+lib目录下的libandlink.so.1.x 2个文件推送到设备文件系统某个目录下(举例:/data);
(2)登陆到设备后台,并进入到/data目录
(3)创建一个so的软连接;ln -s libandlink.so.1.x libandlink.so
(4)运行demo程序;
a.作为无线设备启动
LD_LIBRARY_PATH=./ ./demo wireless wlan0
b.作为有线设备启动
LD_LIBRARY_PATH=./ ./demo wired eth0 

通过日志查看,若可以成功连接到andlink IoT平台,说明程序OK;此时厂商可开发自己的应用程序,来集成andlink能力.
日志含义可参见接入指南第4章<SDK日志简单排查方法>.

注:这是拿到andlink SDK后,第一步要的做的事情.

2、二次编译demo的方法
(1)修改run.sh脚本,指定工具链路径和名称;
(2)执行脚本./run.sh clean  清除编译环境
(3)执行脚本./run.sh all


四、正式移植的方法
1、添加头文件:include/andlink_pub.h

2、添加链接库:
    2.1设备接入产品,有三种链接方式可选:
        2.1.1所有版本:-landlink
        2.1.2通用版本:-landlink-core  -lcoap -lpaho-mqtt3as -ljson  -lcurl  [-lalsec]  -lssl -lcrypto 
        2.1.3轻量版本:-landlink-core  -lcoap -lmqttclient   -ljson  -lcurl  [-lalsec]  -lmbedtls -lmbedcrypto -lmbedx509

    2.2平台接入产品,有三种链接方式可选:
        2.2.1所有版本:-landlink
        2.2.2通用版本:-landlink-core  [-lcoap ]  -ljson  -lcurl   -lalsec   -lssl -lcrypto 
        2.2.3轻量版本:-landlink-core  [-lcoap ]  -ljson  -lcurl   -lalsec   -lmbedtls -lmbedcrypto -lmbedx509

3、参考main.c和adapt/andlink_adapt.c 调用SDK的普通接口,注册和实现SDK的回调接口.


五、其他事项说明

1、libandlink.so/a无外部库的依赖，其内部打包了如下库:
-landlink-core + -lcurl + -ljson + [-lpaho-mqtt3as(lmqttclient)] + [-lcoap] + -lssl -lcrypto (-lmbedtls -lmbedcrypto -lmbedx509)
所以四.2.1.1与四.2.1.2(2.1.3)是等价的,四.2.2.1与四.2.2.2(2.2.3)是等价的,用户可以根据是否需要复用公共库,自由选择.

2、sdk使用的开源库版本如下:
libcoap-4.1.1、curl-7.88.1、paho.mqtt.c-1.3.0(mqttclient-master)、cJSON-1.7.11、openssl-1.1.0h(mbedtls-2.4.2) 

六、版本更新记录
## 2019.05.20 [v0.1初版调试]


## 2019.07.01 [v1.0初版提测]
1.实现了《中国移动And-link协议规范（设备云网关管理分册）v2.0》.


## 2019.09.17 [v1.1.0提测]
1.支持云网关接入新方案《Andlink设备接入实现要求及补充协议V2-190905.docx》.
2.新增了远程诊断、日志分级上传、自检、周期诊断等功能.
3.新增了一机一密功能.
4.简化了OTA方案.


## 2020.04.26 [v1.3.1提测] 
1.新增快联信道(qlink)无感配网功能
(1)支持优先使用qlink方式接入IoT路由器，一旦接入失败，等待用户(APP)配网，接入云网关.
2.简化了对外接口,提升了用户使用体验.
3.优化了多项细节,提升了性能和稳定性.


## 2020.06.18 [v1.3.2提测] 
1.新增引导信道(guidelink)无感配网功能.
2.优化无感配网流程:
(1)优先尝试连接qlink完成配网;
(2)qlink连接失败,连接约定的引导热点完成配网;
(3)引导热点连接失败,启动softAP,等待APP发现.
3.新增密码同步功能.
4.支持普通组网路由器给设备配网.


## 2020.09.16 [v1.4.0提测] 
1.新增了本地诊断功能.
2.集成了中国移动终端公司的DM SDK(richinfo-lwm2msdk-M-v2.0.tar.gz).
3.支持了SDK启动默认userkey的注册.
4.对外接口更改:
adl_dev_attr_t中增加厂商名称字段deviceVendor,假设厂商需要升级SDK,注意头文件andlink_pub.h的同步更新;启动SDK时,需要新增此字段.
5.优化了示例工程(demo),提升了用户使用体验.


## 2020.12.30 [v1.4.2提测] 
1.优化了示例工程(demo),支持主程序对strip后的静态库的依赖.
2.优化了本地诊断功能,支持日志输出级别和日志输出方向的控制.
3.新增了有线设备以子设备的形式接入IOT路由器的逻辑.
4.新增了子设备OTA升级接口.
5.修复了环境中多台coap服务导致的coap广播收不到响应的问题.
6.对外接口更改:对外接口adl_dev_callback_t中增加厂商名称字段扩展升级接口,增加读写配置项回调接口,假设厂商需要升级SDK,注意andlink_pub.h的更新.
7.优化了多项细节,增强了安全性,提升了性能和稳定性.


## 2021.07.14 [V1.5.0提测]
1.对外开放了andlink封装的json编解码能力.
2.优化了本地诊断功能:(1)分开日志文件和配置文件的存储路径;(2)日志文件默认存储到内存;(3)日志文件存储路径见adlLogDebug.conf文件.
3.支持了mbedTLS方式进行链路认证,目前支持mbedTLS-2.4.2版本.
4.对外开放了限制andlink日志文件大小的接口.
5.对外提供了sdk是否支持一机一密的查询接口:getDeviceInfoStr(ADL_AUTH_MODE).
6.新增了OTA文件下载功能;对外开放了设置升级文件存储路径及扩展名设置接口.
7.支持了平台接入Andlink的逻辑,主要支持配网和DM上报.
8.优化了多项细节,提升了稳定性,能力实现了原子化;平台接入SDK支持了线上编译.


## 2021.10.25 [v1.5.1发布] 
1.新增了远程诊断功能.
2.优化了示例代码工程(demo),提升了用户使用体验.
3.修复了扫码绑定失败,设备重启设备直接显示绑定成功的BUG.
4.优化了多项细节,提升了性能和稳定性.


## 2021.12.09 [v1.5.2发布] 
1.发布连接组件的首个轻量版本.
与普通版本(依赖openssl-1.1.1c)相比,SDK(含所有依赖的第三方组件),X86实测,flash占用下降62%，达到1.56MB；内存占用下降52%，达到2.5MB.
2.修复了CPU休眠期间离线解绑失败问题,注意这种场景与断网或关机期间的离线解绑逻辑不同.
3.优化了多项细节,提升了性能和稳定性.


## 2022.05.22 [v1.5.3.230522发布] [V1.5.3_dm_V1.0.2.230522发布] 
1.新增了一机一密缓存目录配置项；增强了IP获取回调接口的安全性.
2.新增了私有版本号,提升了SDK的可维护性.
3.对外开放了用于调试的set_printLog_debug_level接口,仅调试阶段可用,商用阶段不允许使用.
4.平台接入版本:规范了设备ID,DM SDK按照CMCC-deviceType-deviceMac构造设备ID.
5.设备接入版本:链路维护状态增加了设备复位流程判断;修复了即将上线成功状态,设备复位失败的BUG.
6.设备接入版本:修复了轻量版本,断网链路重建程序崩溃问题.
7.支持了27位的一机一密串码.
8.显式设置了andlink各个任务优先级.
9.优化了配网流程中coap广播发送流程,实现非阻塞方式coap广播发送.
10.优化了多项细节,提升了性能,稳定性和可维护性.


## 2022.06.13 [v1.5.3.220613] [V1.5.3_dm_V1.0.2.220613] 
1.对外接口更改:对外接口ADL_DEV_STATE_e中新增了重新绑定的状态,有线设备以此为时机实现绑定失败逻辑.


## 2022.09.20 [v1.5.3.220920] [V1.5.3_dm_V1.0.2.220920] 
1.规范化了DM上报流程中的6项参数,cpuModel、romStorageSize、ramStorageSize、locationInfo、wlanMac、deviceIP 
这六项参数改为必填;若设备不存在此参数,则填"NONE".


## 2022.09.26 [v1.5.3.220926] [V1.5.3_dm_V1.0.2.220926] 
1.根据最新云网关协议,子设备注册和上线消息中,添加了andlink协议版本号.
2.新增了主动上报其下子设备属性信息的接口childDevDataReport,与设备本身属性上报接口devDataReport加以区分.
3.新增了主动上报其下子设备DM信息的接口childDevDmReport.


## 2022.10.13 [v1.5.3.221013] [V1.5.3_dm_V1.0.2.221013] 
1.针对平台对接的产品,增加了对ssl:mbedtls-2.28.1的支持.


## 2022.10.19 [v1.5.3.221019] [V1.5.3_dm_V1.0.2.221019] 
1.上述timestamp表示事件发生的时间戳,用于区分当前事件还是历史事件,可用于支持离线事件上传.
2.规范了DM上报流程中moduleType、networkType字段的含义,针对wifi进行统一表述为:Wi-Fi.
3.对外接口变更:
(1)设备数据上报接口devDataReport入参中添加timestamp,删除childDevId.
(2)子设备数据上报接口childDevDataReport增加timestamp.

## 2023.03.20 [v1.6.0.221215发布]
1.长连接通道中,新增了同步数据上报接口,devDataReport接口可以同步返回数据上报的真实结果,以支持离线续传.
2.事件上报中新增了deviceType.
3.优化了SDK架构,提升可靠性,可维护性.
4.对外接口变更:
(1)ADL_DEV_STATE_e中增加新状态ADL_CFGNET_NETINFO,删除ADL_RESET状态;
(2)wifi_cfg_info_t中增加新字段type,区分引导热点和工作热点.


## 2023.03.22 [v1.6.1.230322发布]
1.[重要更新]根据《中国移动and-link协议规范（设备云网关管理分册）v3.2.0-20221212.pdf》,实现了OTA新方案.
(1)新增了分片下载能力,目前OTA支持整包下载和分片下载;
(2)支持了主动升级和自检升级.
2.针对平台接入SDK和设备接入的DM上报流程,实现了日志文件脱敏.
3.对外开放了下行Bind指令,兼容了设备预绑定方案.
4.优化了SDK日志文件路径,日志路径由/tmp/adlklog/ 修改为 /tmp/andlink.
5.优化了配网流程:待机状态收到网络变化的netinfo,执行重新配网流程.
6.优化了多项细节,提升了性能和稳定性.
7.对外接口变更:
(1)download_upgrade_version_callback回调接口入参中新增了md5字段.
(2)ADL_DEV_STATE_e中新增了ADL_LOG_SYNC,设备根据需要可以此通知接口用于SDK日志文件的备份.
(3)合并接口setUpgradeFileStoragedPath 和 setUpgradeFilenameExtension为setOtaStoragePathAndFilename;
(4)新增了setOtaDownloadPolicy,支持由用户来设置OTA下载策略:整包下载;分片下载.


## 2023.05.29 [v1.6.2.230519发布]
1.[重要更新]修复了复位或重新绑定时coap开源组件产生的内存泄漏问题.
2.[重要更新]修复了轻量版本中断网重连时mqttclient开源组件产生的内存泄漏问题.
3.优化了多项细节,降低内存碎片产生的概率.
4.对外开放了配置接口,使SDK同时支持生产环境和测试环境接入,配置接口:setTestEnvFlag(1).


## 2023.06.09 [v1.6.3.230601发布]
1.新增了调试手段:支持无感配网中引导热点ssid名称的修改.
2.修复了绑定成功后,再次上线获取userkey异常的问题.
3.修复了先调用setScanCodeBindConfigInfo,再调用andlinkInit偶现的绑定异常的问题.[针对二合一设备]
4.修复了无感配网功能中coap发送偶现长时间阻塞的问题.
5.优化了多项细节,提升了稳定性和可维护性.


## 2023.09.12[v1.6.5.230901发布]
1.新增了移动闪联配网功能(通过信号强度字段).
2.新增了移动闪联配网自动绑定家庭流程.
3.修复了普通版本(基于openssl),mqtt连接失败,mqtt客户端可能重复摧毁的问题产生的概率绑定失败问题.
4.优化了本地诊断流程,若日志文件创建失败,日志默认输出到控制台.
5.优化了多项细节,提升了可维护性.
6.对外接口变更:
(1)wifi_cfg_info_t结构中,增加了mac和rssi字段.
(2)adl_dev_callback_t结构中,增加了scan_wifi_callback字段.
(3)对外开放了openAPCfgNet接口,用于用户主动按键启动SoftAP配网.
(4)对外开放了disableAdlFunc(ADL_CLOUD_ACCESS_IN_LOCAL_GW_ENV),用于禁止本地网关环境下的云网关接入,即开启本地网关接入功能.
(5)对外开放了setAndlinkLogFilePath接口,用于设置SDK日志文件存储路径.
(6)对外开放了devDataSyncReport接口,支持事件同步方式上报;而老接口devDataReport支持事件异步方式上报.
(7)MAX_PASSWD_LEN,由32修改为64.
(8)ADL_DEV_STATE_e枚举中,增加了ADL_CFGNET_REJECT_NETINFO.


## 2023.11.15[v1.6.6.231114发布]
1.新增了中国移动天玑产测能力;用户可以调用getProductMode()检查设备所处的模式(工厂测试模式or生产运行模式).
2.新增了andlink服务DNS缓存功能,用户可调用enableAdlFunc(ADL_DNS_CACHE)接口开启此功能.
3.修改了弱确权移动闪联(通过确权等级).
4.优化了Andlink状态提示接口,区分普通上线和绑定上线,便于绑定成功状态提示.
5.优化了多项细节,提升了程序健壮性.
6.开源库更改:针对平台接入版本,支持curl-8.4.0.
7.对外接口变更:
(1)对外接口adl_dev_attr_t中删除了dmKey字段.
(2)对外接口ADL_DEV_STATE_e中删除了ADL_LOG_SYNC,新增了ADL_BOUND.
(3)对外接口adl_dev_callback_t中新增了set_extfunc_notify_callback,用于通知设备执行某些特殊操作.


## 2024.03.20 [v1.6.7.240320发布]
1.新增了移动闪联强、弱确权配网兼容能力,并兼容特殊路由器的引导热点.
默认免确权,用户可以根据需要,调用setCfgNetAuthLevel(2)接口,开启强确权.
2.修复了设备未解绑的情况下,依次发起激活、注册请求,SDK两次通知设备绑定成功(ADL_BOUND)的问题.
3.修复了用户使用一机一密方案时,输入authMode字段为1,但SDK解析为0的问题.
4.优化了Andlink的诊断机制,参考Linux的errno机制,实现Andlink全局错误码,便于厂商快速定位Andlink相关的问题.
(1)对外提供了adlGetErrno接口,用于获取Andlink错误码.
(2)对外开放了andlink错误码通知接口,ADL_DEV_EXTFUNC_NOTIFY_e中添加ADL_ERRNO_GET，用户可在此回调接口实现中调用adlGetErrno获取错误码.
5.优化了多项细节,提升了程序健壮性和易用性.
6.开源库更改:curl库更新为curl-7.88.1,解决开源库安全漏洞问题.
7.开源库修复:修复了轻量版本mqttclient组件收到异常主题时偶现的crasn问题.

## 2024.05.29 [v1.7.1.240529发布]
1.新增数据采集功能,包括网络拓扑信息采集和网络质量信息采集
2.新增离线密码同步功能
3.新增拒绝入网黑名单功能
4.新增BLE配网功能
5.新增有线设备无感配网功能,有线带屏设备本机弹窗确认配网功能
6.修复了mqtt同步上报事件偶现失败问题.
7.优化了demo示例工程
8.对外接口变更：
(1)新增setWiFiName接口,数据采集时会用到,厂商根据需要设置wlan名称,默认为wlan0;如设备不是wlan0,则需要根据设备的wlan实际名称设置
(2)ADL_DEV_EXTFUNC_NOTIFY_e中新增了有线带屏设备的屏幕弹窗,提醒用户,有线带屏设备需要在set_extfunc_notify_callback接口中实现弹窗功能
(3)CFG_NET_MODE_e中新增了有线带屏设备的配网方式
(4)adl_dev_callback_t接口中新增set_voice_notify_callback,设备可以根据需要进行配网语音提醒
(5)adl_dev_callback_t接口中新增ble_start_server_callback,ble_stop_server_callback,ble_send_callback用于支持BLE配网

## 2024.07.22 [v1.7.2.240719发布]
1.新增了设备激活功能：支持移动路由器网络覆盖下,设备100%激活上线.
2.修改了闪联配网顺序:常电设备,优先手工配网;低功耗设备,优先闪联配网.
3.修改了闪联配网默认确权方式,默认改为强确权.
4.新增了闪联配网的路由器版本限制,闪联配网仅支持移动路由器V2.4.2及以上版本.
5.新增了本地诊断和调试功能.
6.新增了误绑踢除功能.
7.增强了离线密码同步功能:
(1)支持非闪联配网场景下的离线密码同步.
(2)支持营业厅绑定到家自动联网场景.
8.增强了有线闪联绑定功能:同时支持有线设备闪联及传统主动发现配网绑定流程.
9.优化了demo示例工程.
10.对外接口变更:
(1)对外接口WIFI_CTRL_OPT_e中添加WIFI_OPT_NET_CLEAN字段,用于清除wifi配置.
(2)对外接口scan_wifi_callback入参变更,扫描结果输出为CJSON对象.
(3)对外开放了禁止闪联配网、手工配网及优先手工配网功能的接口.
disableAdlFunc(ADL_FLASHLINK_CONFIG_NET) 
disableAdlFunc(ADL_MANUAL_CONFIG_NET)
disableAdlFunc(ADL_FIRST_MANUAL_CONFIGNET)