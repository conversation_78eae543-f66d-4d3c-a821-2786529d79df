/*
 *
 * 文件名称：andlink_ota_adapt.c
 * 说 明:厂商集成andlink SDK OTA功能需要适配的接口信息,由厂商补充实现.
 *
 */
#include "andlink_adapt.h"

/************************************************************************
Description:    SDK调用,通知设备执行下载&升级操作
Input:          char *childDeviceID,为NULL表示对父设备升级;否则是子设备
                char *downloadrul, 版本服务器的url,
                char *versiontype, 版本类型:FIRM/APP/ANDLINK等,
                int chkfilesize    版本文件大小
Output:         None
Return:         成功:0, 失败:-1
Others:         升级过程中,需要采用异步上报接口devDataReport,上报OTA进度.

OTA过程正常:{2002, "1"};{2002, "100"},{2000, "1"},{2000, "100"}按次序上报4个事件:下载开始;下载完成;升级开始;升级完成.
OTA过程异常:{2003, "300"}; {2003, "301"}; {2003, "310"};{2003, "320"}; {2003, "330"}; {2003, "340"}; {2003, "350"};上报6种情况之一的若干事件
上报正常事件eventType填 OTA; 上报异常事件eventType填 Response_OTA
过程异常信息,respCont取值如下:
300: 目标版本与当前版本一致,升级终止;
301:设备空间小于升级包大小,升级终止;
310:升级包下载失败;
320:升级包校验失败;
330:升级过程异常,升级终止;
340:子设备升级时,与子设备通信异常;
350:其他异常信息.
************************************************************************/
int demo_download_upgrade_version_callback(char *childDeviceID, char *downloadurl, char *versiontype, char *md5, int chkfilesize)
{
    DEMO_DBG_PRINT("[demo]download_upgrade_version_callback\r\n");

#ifdef ANDLINK_DEV_ACCESS_LINK_ENABLE

    //------------阶段1:下载处理------------
    // 1.上报下载进度:开始
    char data[64];
    memset(data, 0, sizeof(data));
    snprintf(data, sizeof(data) - 1, "{\"respCode\":2002,\"respCont\":\"%d\"}", 1);
    devDataReport("OTA", NULL, 0, data, strlen(data));

    // 2.从downloadrul下载版本
    // TODO

    // 3.上报下载进度:完成
    memset(data, 0, sizeof(data));
    if (1)
    {
        // 下载成功
        snprintf(data, sizeof(data) - 1, "{\"respCode\":2002,\"respCont\":\"%d\"}", 100);
        devDataReport("OTA", NULL, 0, data, strlen(data));
    }
    else
    {
        // 下载失败
        snprintf(data, sizeof(data) - 1, "{\"respCode\":2003,\"respCont\":\"%d\"}", 310);
        devDataReport("Response_OTA", NULL, 0, data, strlen(data));
    }

    // 5.版本校验
    // TODO

    // 6.上报升级进度:开始
    memset(data, 0, sizeof(data));
    snprintf(data, sizeof(data) - 1, "{\"respCode\":2000,\"respCont\":\"%d\"}", 1);
    devDataReport("OTA", NULL, 0, data, strlen(data));

    // 7.执行升级操作
    // TODO

    // 8.上报升级进度:完成
    memset(data, 0, sizeof(data));
    snprintf(data, sizeof(data) - 1, "{\"respCode\":2000,\"respCont\":\"%d\"}", 100);
    devDataReport("OTA", NULL, 0, data, strlen(data));

#endif // ANDLINK_DEV_ACCESS_LINK_ENABLE
    return 0;
}


/************************************************************************
Description:    SDK调用,通知设备仅执行升级操作
Input:          char *childDevId, char *filename, char *filetype
Output:         None
Return:         成功:0, 失败:-1
Others:         父/子设备仅升级版本,childDevId为空表示父设备,否则表示子设备(可选)

OTA过程正常:{2000, "1"},{2000, "100"}按次序上报2个事件:升级开始,升级完成.
OTA过程异常:{2003, "300"}; {2003, "301"}; {2003, "310"};{2003, "320"}; {2003, "330"}; {2003, "340"}; {2003, "350"};上报6种情况之一的若干事件
上报正常事件eventType填 OTA; 上报异常事件eventType填 Response_OTA
过程异常信息,respCont取值如下:
300: 目标版本与当前版本一致,升级终止;
301:设备空间小于升级包大小,升级终止;
310:升级包下载失败;
320:升级包校验失败;
330:升级过程异常,升级终止;
340:子设备升级时,与子设备通信异常;
350:其他异常信息.
************************************************************************/
int demo_upgrade_version_callback(char *childDevId, char *filename, char *filetype)
{
    DEMO_DBG_PRINT("[demo]demo_upgrade_version_callback\r\n");

#ifdef ANDLINK_DEV_ACCESS_LINK_ENABLE
    //------------阶段2:升级处理------------
    char data[64];
    // 1.上报升级进度:开始
    memset(data, 0, sizeof(data));
    snprintf(data, sizeof(data) - 1, "{\"respCode\":2000,\"respCont\":\"%d\"}", 1);
    devDataReport("OTA", NULL, 0, data, strlen(data));

    // 2.执行升级操作
    // TODO

    // 3.上报升级进度:完成
    memset(data, 0, sizeof(data));
    if (1)
    {
        // 升级完成
        snprintf(data, sizeof(data) - 1, "{\"respCode\":2000,\"respCont\":\"%d\"}", 100);
        devDataReport("OTA", NULL, 0, data, strlen(data));
    }
    else
    {
        // 升级异常
        snprintf(data, sizeof(data) - 1, "{\"respCode\":2003,\"respCont\":\"%d\"}", 330);
        devDataReport("Response_OTA", NULL, 0, data, strlen(data));
    }

#endif // ANDLINK_DEV_ACCESS_LINK_ENABLE
    return 0;
}