/*
 *
 * 文件名称：andlink_control_adapt.c
 * 说 明:厂商集成andlink SDK 管控功能需要适配的接口信息,由厂商补充实现.
 *
 */
#include "andlink_adapt.h"

// 功能参数索引-名称接口
typedef struct
{
    int funcIndex;
    char *function;
} FUNCTION_INDEX_MAP_T;

// 设备支持的下行管控功能列表
static FUNCTION_INDEX_MAP_T s_adlFunctionSets[] = {
    { 0, "Control" },
    { 1, "Unbind" },
    { 2, "SelfDetect" },
    { 3, "Reboot" },
    { 4, "Bind" }
};

/************************************************************************
Description: 获取管控功能的索引
Input: char *function, int *outIndex
Output: int *outIndex
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int checFunctionIndex(char *function, int *outIndex)
{
    int i = 0;
    if (NULL == function || 0 == strlen(function))
    {
        return -1;
    }

    char tmpFunc[16] = { 0 };
    strncpy(tmpFunc, function, sizeof(tmpFunc) - 1);
    // strtolower(tmpFunc);

    for (i = 0; i < sizeof(s_adlFunctionSets) / sizeof(FUNCTION_INDEX_MAP_T); ++i)
    {
        if (0 == strcmp(s_adlFunctionSets[i].function, tmpFunc))
        {
            *outIndex = s_adlFunctionSets[i].funcIndex;
            return 0;
        }
    }

    return -1;
}

/************************************************************************
Description:    SDK调用实现对设备的下行管控;
Input:          RESP_MODE_e mode, dn_dev_ctrl_frame_t *ctrlFrame
Output:         char *eventType, char *respData
Return:         成功:0, 失败:-1
Others:         注意:若mode要求同步响应,才会使用两个输出参数
                响应方式分为:无需响应;同步响应;异步响应
ctrlFrame->data的格式如下:
                {
                    "params": [ {"paramCode": "PARAMCODE1","paramValue": "10"},// 单端口
                               {"paramCode": "PARAMCODE2","paramValue": "20"},
                               {"paramCode": "PARAMCODE3","paramIndex": "1","paramValue": "10"},// 多端口,比如插排,1号开关状态,2号开关状态;
                               {"paramCode": "PARAMCODE3","paramIndex": "2","paramValue": "10"},

                               ... ...
                              ]
                }

某个灯举例:
                {
                    "params": [ {"paramCode": "powerStatus","paramValue": "1"},  // 开灯
                               {"paramCode": "brightness","paramValue": "50"},   // 亮度50
                               ... ...
                              ]
                }

输出参数eventType,默认最大空间为MAX_FUNCSTR_LEN=32B;

************************************************************************/
int demo_dn_send_cmd_callback(RESP_MODE_e mode, dn_dev_ctrl_frame_t *ctrlFrame, char *eventType, char *respData, int respBufSize)
{
#ifdef ANDLINK_DEV_ACCESS_LINK_ENABLE
    DEMO_DBG_PRINT("[demo]dn_send_cmd_callback(%s),mode=%d(0:no; 1:async; 2:sync;)\r\n", ctrlFrame->function, mode);
    // TODO
    int funcIndex = 0;

    char *localEventType = NULL;
    char localResp[256] = { 0 };

    // 处理管控指令
    if (0 != checFunctionIndex(ctrlFrame->function, &funcIndex))
    {
        goto FAIL_EXIT;
    }

    switch (funcIndex)
    {
    case 0: // Control
        DEMO_DBG_PRINT("andlink: Control\n");
        // 处理控制指令TODO

        localEventType = "ParamChange";
        break;

    case 1: // Unbind//解绑处理
        DEMO_DBG_PRINT("andlink: Unbind\n");
        break;

    case 2: // SelfDetect//自检
        DEMO_DBG_PRINT("andlink: SelfDetect\n");
        // 处理自检指令 TODO

        char *format = "{\"cpuRate\":%d,\"ramRate\":%d,\"upLinkType\":\"%s\",\"rssi\":\"%s\"}";
        localEventType = "SelfDetect";
        snprintf(localResp, sizeof(localResp) - 1, format, 30, 30, "Wi-Fi", "-45");

        break;

    case 3: // Reboot//重启,此接口最好必须实现,用于通过远程重启规避某些严重的现网问题.
        DEMO_DBG_PRINT("andlink: Reboot\n");
        // 指定设备重启
        break;
    default:
        goto FAIL_EXIT;
    }

    // 进行回应
    switch (mode)
    {
    case ASYNC_MODE:
        // 异步响应
        if (NULL != ctrlFrame->data && NULL != localEventType)
        {
            devDataReport(localEventType, ctrlFrame->seqId, 0, ctrlFrame->data, strlen(ctrlFrame->data));
        }

        break;

    case SYNC_MODE:

        // 同步响应
        if (NULL != eventType && NULL != localEventType)
        {
            strncpy(eventType, localEventType, sizeof(ctrlFrame->function) - 1);
        }

        if (NULL != respData)
        {
            if (strlen(localResp))
            {
                strncpy(respData, localResp, respBufSize);
            }
            else
            {
                strncpy(respData, ctrlFrame->data, respBufSize);
            }

            DEMO_DBG_PRINT("sync response=type:%s,data:%s,datalen=%d\n", eventType, respData, (int)strlen(respData));
        }

        break;
    default:
        break;
    }
    return 0;
#endif

FAIL_EXIT:
    return -1;
}

// 测试上报功能参数1
int demoDevParamsReport()
{
#ifdef ANDLINK_DEV_ACCESS_LINK_ENABLE

    // 设备状态参数上报 参考代码
    char data[1024] = { 0 };
    // 举例1的data格式: 设备主动上报版本号的格式
    char *format1 = "{\"params\": [{\"paramCode\": \"softVersion\",\"paramValue\": \"%s\"}]}";

    // 举例2的data格式: 单端口单属性的灯设备 上报状态开
    char *format2 = "{\"params\": [{\"paramCode\": \"outletStatus\",\"paramValue\": \"0\"}]}";

    // 举例3的data格式: 单端口的2个属性状态同时上报
    char *format3 = "{\"params\": [ {\"paramCode\": \"powerStatus\",\"paramValue\": \"1\"},{\"paramCode\": \"brightness\",\"paramValue\": \"0\"} ]}";

    // 举例4的data格式: 多端口插排设备 上报状态; 上报第一个插孔状态;
    char *format4 = "{\"params\": [{\"paramCode\": \"powerStatus\",\"paramIndex\": \"1\",\"paramValue\": \"1\"}]}";

    // 举例5的data格式: 多端口插排设备 上报状态; 上报第一个插孔状态和第二个插孔状态;
    char *format5 = "{\"params\": [{\"paramCode\": \"powerStatus\",\"paramIndex\": \"1\",\"paramValue\": \"1\"},{\"paramCode\": \"powerStatus\",\"paramIndex\": \"2\",\"paramValue\": \"1\"}]}";

    // 举例6的data格式: 单端口设备的多个属性同时上报
    /*
    {"params":[
        {"paramCode":"softVersion","paramValue":"1.10.0009"},
        {"paramCode":"firmware","paramValue":"1.10.0009"},
        {"paramCode":"STATE","paramValue":"1"},
        {"paramCode":"brightness","paramValue":"0"},
        {"paramCode":"powerStatus","paramValue":"0"},
        {"paramCode":"lightMode","paramValue":"0"},
        {"paramCode":"countDown","paramValue":"0"},
        {"paramCode":"AISwitch","paramValue":"1"},
        {"paramCode":"delaySwitch","paramValue":"0"},
        {"paramCode":"DelaySwitch","paramValue":"0"},
        {"paramCode":"strongLightProtect","paramValue":"0"}
        ]}
    */
    // 测试环境:产测demo测试
    char *format6 = "{\"params\": [ {\"paramCode\": \"batteryCapacity\",\"paramValue\": \"95\"},\
    {\"paramCode\": \"indoorTemperature\",\"paramValue\": \"25.0\"},\
    {\"paramCode\": \"mode\",\"paramValue\": \"auto\"},{\"paramCode\": \"software\",\"paramValue\": \"A\"},\
    {\"paramCode\": \"switch\",\"paramValue\": \"0\"}]}";

    // 生产环境:产测demo测试
    char *format7 = "{\"params\" : [ {\"paramCode\" : \"boolTest\", \"paramValue\" : \"0\"},{\"paramCode\" : \"doubleTest\", \"paramValue\" : \"1.0\"}, {\"paramCode\" : \"enumTest\", \"paramValue\" : \"0\"},{\"paramCode\" : \"floatTest\", \"paramValue\" : \"1.1\"}, {\"paramCode\" : \"intTest\", \"paramValue\" : \"1\"},{\"paramCode\" : \"stringTest\", \"paramValue\" : \"a\"}, {\"paramCode\" : \"motionSwitch\", \"paramValue\" : \"0\"} ]}";

    char *format8 = "{ \"params\" : [ {\"paramCode\" : \"boolTest\",\"paramValue\" : \"1\"}, {\"paramCode\" : \"doubleTest\", \"paramValue\" : \"20\"}, \
    {\"paramCode\" : \"enumTest\", \"paramValue\" : \"2\"}, {\"paramCode\" : \"floatTest\", \"paramValue\" : \"1.1\"}, \
    {\"paramCode\" : \"intTest\", \"paramValue\" : \"1\"}, {\"paramCode\" : \"stringTest\", \"paramValue\" : \"a\"}, \
    {\"paramCode\" : \"motionSwitch\", \"paramValue\" : \"0\"} ] }";

    char *format = NULL;
    if (1 == getTestEnvFlag())
    {
        format = format2;
    }
    else
    {
        format = format2;
    }
    // snprintf(data, sizeof(data) - 1, format, "0");
    strncpy(data, format, sizeof(data) - 1);

#if 1
    // 上报1:设备主动上报
    if (0 == devDataReport("Inform", NULL, 0, data, strlen(data)))
    {
        DEMO_DBG_PRINT("devDataReport Suc,data =%s\n\n", data);
    }
#else
    // 上报2:测试数据上报成功率 for test
    int i, num = 1;
    for (i = 0; i < num; i++)
    {
        char test[16] = { 0 };
        snprintf(test, sizeof(test) - 1, "Vtest0.%d", i);
        snprintf(data, sizeof(data) - 1, format, test);

        // 上报2:设备因用户按键或下行管控触发的数据上报
        if (0 == devDataReport("ParamChange", NULL, 0, data, strlen(data)))
        {
            DEMO_DBG_PRINT("devDataReport Suc,data =%s\n\n", data);
        }
    }
#endif

// wifi 感知使能
#ifdef SDK_USING_DEVICE_FUNCS_ENABLE
    // for SOS报警器 21.10.22
    // 向局域网内移动生态的路由器 上报wifi感知功能开启;这里仅是一个样例,具体paramCode和paramValue 可以在连楹家庭智慧平台门户定义,并且在端到端进行约定.
    int wifiSensorEnable = 1;
    char *format11 = "{\"mac\":\"%s\",\"params\": [{\"paramCode\":\"wifiSensor\",\"paramValue\":\"%d\"}]}";
    memset(data, 0, sizeof(data));
    snprintf(data, sizeof(data) - 1, format11, DEVICE_REAL_MAC, wifiSensorEnable);
    local_devDataReport(NULL, NULL, "Inform", NULL, data, strlen(data));
#endif

#endif // ANDLINK_DEV_ACCESS_LINK_ENABLE
    return 0;
}

// 测试启动任务上报功能参数2
void *paramsSyncTaskHandler(void *arg)
{
    demoDevParamsReport();
}

/************************************************************************
Description:    SDK调用,通知设备进行参数同步;
Input:          None
Output:         None
Return:         成功:0, 失败:-1
Others:         设备需要调用devDataReport上报一次设备属性信息
                sdk调用时机:设备MQTT上线成功;预绑定方案,设备被真实绑定时.
************************************************************************/
int demo_dev_paramsSync_callback()
{
    // 此接口中可以通过getDeviceInfoStr接口查看设备的真实绑定状态,1表示绑定.
    DEMO_DBG_PRINT("[demo]dev_paramsSync_callback,userBind =%s.\r\n", getDeviceInfoStr(ADL_BIND_STATUS));
    // TODO

#if 1
    // 直接上报参数
    demoDevParamsReport();
#else
    // 测试,创建多个任务上报参数
    int i, num = 10;
    for (i = 0; i < num; ++i)
    {
        pthread_t tid;
        int ret = pthread_create(&tid, NULL, paramsSyncTaskHandler, NULL);
        if (0 == ret)
        {
            DEMO_DBG_PRINT("start paramsSyncTask[%d] OK\r\n", i);
            pthread_detach(tid);
        }
    }
#endif

    return 0;
}