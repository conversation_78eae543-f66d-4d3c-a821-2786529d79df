/*
 * Copyright (c) 2019,中移(杭州)信息技术有限公司
 * All rights reserved.
 *
 * 文件名称：main.c
 * 说 明：主函数所在文件
 *
 * 当前版本：1.0
 * 作 者：wuh
 * 完成日期：2020年08月22日
 *
 */

#include "andlink_adapt.h"

static int s_quit = 0;
/************************************************************************
Description: 启动Andlink SDK
Input: None
Output: None
Return: 成功:0, 失败:-1
Others:
1.通过配置文件或其他方式 设置andlink_init 属性入参;
2.设置andlink_init 回调入参;
3.调用andlink_init 启动andlink SDK.
************************************************************************/
int demo_startAndlinkSdk(int mode, char *ifname)
{
    // 1.设置设备andlink基本信息
    static adl_dev_attr_t devAttr = { 0 };

    // 恢复厂商设备配置文件,一旦恢复失败,重新构造
    if (0 != recoverFacDevinfoCfg(&devAttr))
    {
        DEMO_DBG_PRINT("recoverFacDevinfoCfg failed\n");
        buildFacDevinfoCfgFile(ifname, &devAttr);
    }
    devAttr.cfgNetMode = mode;

    DEMO_DBG_PRINT("[enter]demo_startAndlinkSdk,cfgNetMode =%d,ifname =%s\n", devAttr.cfgNetMode, ifname);

    // 2.设置andlink回调接口
    static adl_dev_callback_t devCbs = {
        .scan_wifi_callback = demo_scan_wifi_callback,
        .ctrl_wifi_callback = demo_ctrl_wifi_callback,
        .set_led_callback = demo_set_led_callback,
        .set_extfunc_notify_callback = NULL,
        .dn_send_cmd_callback = demo_dn_send_cmd_callback,
        .dev_paramsSync_callback = demo_dev_paramsSync_callback,
        .download_upgrade_version_callback = NULL,
        //.download_upgrade_version_callback = demo_download_upgrade_version_callback,
        .upgrade_version_callback = demo_upgrade_version_callback,
        .get_device_ipaddr = demo_get_device_ipaddr_callback,
        .reset_device_Ipaddr = demo_reset_device_Ipaddr_callback,
        .getCfg_callback = NULL,
        .setCfg_callback = NULL,
        .get_dmInfo_callback = demo_get_dmInfo_callback,
        .get_extInfo_callback = demo_get_extInfo_callback,
        .set_voice_notify_callback = NULL,
        .ble_start_server_callback = demo_ble_start_server_callback,
        .ble_stop_server_callback = NULL,
        .ble_send_callback = NULL,
    };

    // 3.检查设备是否联网
    char ipAddr[40] = { 0 };
    char brdAddr[40] = { 0 };
    if (0 != check_device_ip_info(ifname, ipAddr, brdAddr))
    {
        DEMO_DBG_PRINT("device has NOT been connected to the network!!!\n");
        demo_set_device_ipaddr_for_callback("", "");
    }
    else
    {
        DEMO_DBG_PRINT("device has been connected to the network!\n");
        demo_set_device_ipaddr_for_callback(ipAddr, brdAddr);
    }

    DEMO_DBG_PRINT("\n");
    DEMO_DBG_PRINT("andlink_init========start\r\n");
    DEMO_DBG_PRINT("##############################################################################\n");
    DEMO_DBG_PRINT("andlinksdk demo   version:%s\n", adlDemoVersionGet());
    DEMO_DBG_PRINT("andlinksdk lib    version:%s\n", getAndlinkVersion());
    DEMO_DBG_PRINT("andlinksdk DM lib version:%s\n", getAndlinkVersion());
    DEMO_DBG_PRINT("##############################################################################\n");

    // 4.设置特殊的启动选项
    // 4.1设置andlink单个日志文件阈值;若不设置,默认单个日志文件最大512KB,即andlink诊断功能的日志文件占用空间最大为512KB*2;
    // setAndlinkLogMaxSize(0x80000);

    // 4.2设置andlink 日志文件存储路径;若不设置,默认是存储在/tmp/andlink目录下.
    // setAndlinkLogFilePath("/tmp/andlink1");

    // 4.3调试阶段设置andlink日志级别及输出位置;若不设置,默认日志文件存储在andlink.log中;
    // 强烈注意!!!!!,此接口仅用于测试阶段的调试,将日志输出到控制台,正式版本不可调用.
    set_printLog_debug_level(255, "terminal");

    // 4.4若是扫码绑定的设备,可以关闭APP发现服务;
    // disableAdlFunc(ADL_APP_SEARCH_SERVICE);

    // 4.5若存在离线解绑后,需要立马绑定的场景时,可以禁止自动默认用户注册功能;
    // disableAdlFunc(ADL_OFFLINE_UNBIND_AUTO_REBOOTSTRAP);

    // 4.6若设备在本地网关环境中,需要支持本地局域网内的管控和联动,可以禁止本地网关环境的云接入功能;
    // disableAdlFunc(ADL_CLOUD_ACCESS_IN_LOCAL_GW_ENV);

    // 4.7若是可视门锁、守护台灯等设备,使用扫码配网,可以调用下列接口禁止闪联配网和手工SoftAP或BLE配网
    // disableAdlFunc(ADL_FLASHLINK_CONFIG_NET);
    // disableAdlFunc(ADL_MANUAL_CONFIG_NET);

    // 4.8若是电池设备(低功耗设备),必须调用此接口,禁止手工配网优先功能,此时设备上电默认会执行闪联配网,若闪联失败,则会执行手工配网功能.
    // disableAdlFunc(ADL_FIRST_MANUAL_CONFIGNET);

// 用于SDK开发者自己调试,SDK使用者无需关心
#ifdef DEMO_TESTENV_DGS_ENABLE
// 测试环境周期诊断服务器地址,SDK使用者一般无需关心
#define ADL_CYC_DGS_SERVER "http://************:8085/dev/anddgs/chkdgs"
    setAdlDgsTestServUrl(ADL_CYC_DGS_SERVER);
#endif

    // 4.8设置OTA 文件下载路径及文件名称
    // setOtaStoragePathAndFilename("/tmp", "ota.zip");
    // 4.9设置OTA下载策略;1分片下载;0整包下载
    // setOtaDownloadPolicy(1, 2*1024*1024);

    /*
    5.启动andlink SDK(立即返回,内部不会阻塞)
    注意:sdk不会申请内存存储devAttr和devCbs,只会保存其指针;
    因此入参所需要的空间需要调用者申请且SDK运行期间不能释放这两个指针;假设用户需要调用andlink_destroy使SDK消亡时,需要释放这两个指针;
    */
    return andlink_init(&devAttr, &devCbs);
}

#ifdef AHM_DISCOVERY_ENABLE // 设备互联SDK
// 能力ID 需要从andlink门户申请
#define ABILITY_ID_DEGC 0x10086011
#define ABILITY_ID_HUMI 0x10086022

// 启动设备互联SDK
int demo_startAndlinkCsSdk()
{
    // 创建两个能力模型
    adl_cs_setModel(0, "degC", ABILITY_ID_DEGC);
    adl_cs_setModel(1, "relativeHumidity", ABILITY_ID_HUMI);

    // 注册两个回调接口
    set_andlink_callback(CB_GET_CS_PORT, demo_cs_get_port);
    set_andlink_callback(CB_GET_CS_VALUE, demo_cs_get_value);

    return 0;
}
#endif

// 方式1:主进程中启动Andlink
int demo_main(int argc, char **argv)
{
    int mode = -1;
    char *ifname = NULL;

    /************ 1.设置联网模式和网口名称 ************/
    if (argc >= 3)
    {
        if (0 == strcmp(argv[1], "wireless"))
        {
            // 测试: LD_LIBRARY_PATH=../lib ./demo wireless wlan0
            mode = NETWOKR_MODE_WIFI;
            ifname = argv[2];
        }
        else if (0 == strcmp(argv[1], "wired"))
        {
            // 测试: LD_LIBRARY_PATH=../lib ./demo wired ens33
            mode = NETWOKR_MODE_WIRED;
            ifname = argv[2];
        }
        else
        {
            // 测试: LD_LIBRARY_PATH=../lib ./demo $(1) $(2)
            // $(1)取值:1|2|3|4|5|6|7|...,1-wired; 2-wireless; 3-4G; 4-BLE; 5-ScanCode; 6-others; 7-wiredScreens;
            // $(2)取值:wlan0|eth0|ens33|...
            mode = atoi(argv[1]);
            ifname = argv[2];
        }
    }
    else
    {
        // 测试: LD_LIBRARY_PATH=../lib ./demo
        // 默认无线启动,网口名称为IF_NAME
        mode = NETWOKR_MODE_WIFI;
        ifname = IF_NAME;
    }

    /************ 2.加载Andlink SDK ************/
    if (0 != demo_startAndlinkSdk(mode, ifname))
    {
        DEMO_DBG_PRINT("demo_startAndlinkSdk failed\r\n");
    }

    /************ 2.加载设备互联 SDK ************ 供使用设备互联SDK的厂商使用, 一般无需关心 */
#ifdef AHM_DISCOVERY_ENABLE
    if (0 != demo_startAndlinkCsSdk())
    {
        DEMO_DBG_PRINT("demo_startAndlinkCsSdk failed\r\n");
    }
#endif

    return 0;
}

// 方式2:子进程中启动Andlink
int child_main(int argc, char **argv)
{
    void adl_open_http_func();
    void adl_close_http_func();
    pid_t pid;

    adl_open_http_func();

    pid = fork();
    if (pid < 0)
    {
        DEMO_DBG_PRINT("Failed to create new process!\n");
        exit(1);
    }
    if (pid > 0)
    {
        // father
        // adl_open_http_func();
        exit(0);
    }
    else
    {
        // child
        demo_main(argc, argv);
    }

    while (!s_quit)
    {
        sleep(5);
    }
    return 0;
}

int main(int argc, char **argv)
{
    /*
    说明:
    ANDLINK_DEBUG_ENABLE取值如下:
    0, 正式版本,For SDK使用者;
    非0,调试版本,For SDK开发者,SDK使用者无需关心.
    */

#ifndef ANDLINK_DEBUG_ENABLE
#define ANDLINK_DEBUG_ENABLE 0
#endif

    DEMO_DBG_PRINT("enter dev main,ANDLINK_DEBUG_ENABLE =%d!\n", ANDLINK_DEBUG_ENABLE);
    if (argc < 3)
    {
        DEMO_DBG_PRINT("Usage: cd bin;LD_LIBRARY_PATH=../lib %s wired ens33\n", argv[0]);
        DEMO_DBG_PRINT("Usage: cd bin;LD_LIBRARY_PATH=../lib %s 1 ens33\n\n", argv[0]);

        DEMO_DBG_PRINT("or\n");
        DEMO_DBG_PRINT("Usage: cd bin;LD_LIBRARY_PATH=../lib %s wireless wlan0\n", argv[0]);
        DEMO_DBG_PRINT("Usage: cd bin;LD_LIBRARY_PATH=../lib %s 2 wlan0\n\n", argv[0]);

        DEMO_DBG_PRINT("or\n");
        DEMO_DBG_PRINT("Usage: cd bin;LD_LIBRARY_PATH=../lib %s $(1) $(2)\n", argv[0]);
        DEMO_DBG_PRINT("$(1)=1|2|3|4|5|6|7|,1-wired; 2-wireless; 3-4G; 4-BLE; 5-ScanCode; 6-others; 7-wiredScreens\n");
        DEMO_DBG_PRINT("$(2)=wlan0|eth0|ens33|...\n\n");
        return -1;
    }

#if ANDLINK_DEBUG_ENABLE == 0
#if 1
    // 0.1 主进程中直接启动andlink
    demo_main(argc, argv);
#else
    // 0.2 子进程中启动andlink
    child_main(argc, argv);
#endif

    while (!s_quit)
    {
        sleep(5);
    }
#else
    // x.x SDK开发者调试andlink,SDK的使用者无需关心
    int debug_demo_main(int argc, char **argv);
    debug_demo_main(argc, argv);
#endif // ANDLINK_DEBUG_ENABLE

    return 0;
}
