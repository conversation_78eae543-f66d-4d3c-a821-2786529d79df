// TcpClient.cpp
#include "tcpclient.h"
#include <iostream>
#include <cstring>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <cstdlib>
#include <cerrno>

TcpClient::TcpClient(const char* ip, int port)
    : m_ip(ip), m_port(port), m_clientSocket(-1) {}

TcpClient::~TcpClient() {
    close();
}

bool TcpClient::connect_server() {
    // 创建套接字
    m_clientSocket = socket(AF_INET, SOCK_STREAM, 0);
    if (m_clientSocket == -1) {
        std::perror("socket");
        return false;
    }

    // 设置服务器地址信息
    struct sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(m_port);
    if (inet_pton(AF_INET, m_ip, &(serverAddr.sin_addr)) <= 0) {
        std::perror("inet_pton");
        close();
        return false;
    }

    // 连接到服务器
    if (connect(m_clientSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) == -1) {
        std::perror("connect");
        close();
        return false;
    }

    return true;
}

bool TcpClient::send2server(const char* message,int length) {
    if (send(m_clientSocket, message,length , 0) == -1) {
        perror("send");
        close();
        return false;
    }
    return true;
}

int TcpClient::receive() {
    int n = recv(m_clientSocket, buffer, 20, 0);
    if (n <= 0) {
        if (n == 0) {
            std::cout << "服务器关闭了连接。\n";
        } else {
            perror("recv");
        }
        close();
        // return "";
    } else {
        // std::cout << "tcp client recv length:"<< n <<std::endl;
        return n;
        // buffer[n] = '\0';
        // return std::string(buffer);
    }
    // char buffer[1024];
    // int n = recv(m_clientSocket, buffer, sizeof(buffer), 0);
    // if (n <= 0) {
    //     if (n == 0) {
    //         std::cout << "服务器关闭了连接。\n";
    //     } else {
    //         perror("recv");
    //     }
    //     close();
    //     return "";
    // } else {
    //     buffer[n] = '\0';
    //     return std::string(buffer);
    // }
}

void TcpClient::close() {
    if (m_clientSocket != -1) {
        ::close(m_clientSocket);
        m_clientSocket = -1;
    }
}