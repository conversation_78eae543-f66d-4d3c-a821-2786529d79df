/*
 *
 * 文件名称：andlink_adapt.c
 * 说 明:厂商集成andlink SDK需要适配的通用接口信息,由厂商补充实现.
 * 凡是标注了TODO的的函数都需要厂商重写或修改.
 */

/*
===================================== 强烈注意 start =====================================
一、若要对demo程序进行二次编译,需要指定以下2个符号.
ANDLINK_DEV_ACCESS_LINK_ENABLE      设备接入必须定义. 定义此宏,表示是设备接入,否则是平台接入.
APP_USING_ANDLINK_CJSON             是否使用SDK封装的json库函数.
这两个符号的含义可以搜索demo程序实例代码,可以在run.sh中指定,也可以在demo目录下的Makefile(line 10左右)中指定.


二、使用andlink SDK集成适配的过程中,有任何问题可以查阅以下文档:

(一)、通过第三方平台间接接入Andlink平台的产品（平台接入），一般而言，摄像头、门铃、门禁等是平台接入
包名形如：andlinkDevSdk_Vx.x.x_dm_Vx.x.x.xxxxxx-$PackageDate-$ProductID-$CompilerName-$SSLVer.tar.gz
1.1、AndlinkDM SDK(Linux)接入指南(平台接入)：
https://docs.qq.com/doc/DY3pPSlFtTWZ3bVNN

1.2、AndlinkDM-SDK(linux c) (平台接入)使用常见问题(FAQ)
https://docs.qq.com/doc/DUFVLbnVTQllvWk1D

(二)、直接接入Andlink平台的产品（设备接入），一般而言，音箱、台灯、可视门锁、网关等是设备接入
包名形如：andlinkDevSdk_Vx.x.x.xxxxxx-$PackageDate-$ProductID-$CompilerName-$SSLVer.tar.gz
2.1、Andlink SDK(Linux)接入指南(设备接入)：
https://docs.qq.com/doc/DUERlaEJIS0RwYXNH

2.2、Andlink-SDK(linux c) （设备接入）使用常见问题(FAQ)
https://docs.qq.com/doc/DUHBpUVljbXhteHJr


(三)、Andlink系列协议常见问题解答及问题收集：
https://docs.qq.com/sheet/DY0F1d2pEeGtDVEVz?tab=j5rcz6

===================================== 强烈注意 end   =====================================
*/
#include "andlink_adapt.h"

// andlink demo 示例程序 版本号
#define ADL_DEMO_MAJOR "2.3"
#define ADL_DEMO_ST    ".0demo240521"

/*
注意：下列定义的宏中，

1.DEVICE_TYPE、PRODUCTION_TOKEN、ANDLINK_TOKEN 是产品ID及 一型一密 的一对秘钥;
这些信息，在中国移动连楹家庭智慧平台上(https://open.home.10086.cn )创建产品后，会自动分配.

若是 平台接入 的产品, PRODUCTION_TOKEN,ANDLINK_TOKEN 若获取不到,可以填NULL;
若是 设备接入 的产品, PRODUCTION_TOKEN,ANDLINK_TOKEN 是必须的;


2.ANDLINK_SEC_AUTH_ID、ANDLINK_SEC_AUTH_KEY 是 一机一密 的串号+秘钥;
这些信息,可以查询andlink SDK的 FAQ中 "示例代码中的authMode、authId、authKey如何获取？"

若使用一机一密, 则是必须的;
若是平台接入的产品, 必须使用一机一密.
若是设备接入的产品, 一机一密是目前是非必须的,即可以使用一型一密.
*/

// 以下#if 1中的信息是样例数据,厂商集成适配时,需要改为厂商设备自身的信息
#if 1
#define DEVICE_TYPE        "30135"            // [须要申请]设备类型ID,即连楹家庭智慧平台上 创建完产品生成的产品ID;
#define PRODUCTION_TOKEN   "haNX13Bo0TPggyoI" // [须要申请]设备一型一密的预置秘钥,即连楹家庭智慧平台上的 产品验证码;平台对接,此字段是可选的;
#define ANDLINK_TOKEN      "fgSNpHworAEjBeSj" // [须要申请]Andlink IoT平台秘钥,即连楹家庭智慧平台上的 平台验证码;平台对接,此字段是可选的;
#define DEVICE_VENDOR_NAME "test"             // 厂商名称,使用简单的英文或拼音表示

#define DEVICE_REAL_MAC  "FF0822A08E00"     // 设备真实mac
#define DEVICE_REAL_SN   "5555660000000001" // 设备真实sn
#define DEVICE_REAL_CMEI "543427117609114"  // 设备真实cmei

// 设备唯一标识,可以根据需要填写设备真实mac或sn,最终andlink以此分配设备ID(deviceId),格式为CMCC-$DEVICE_TYPE-$DEVICE_MAC
#define DEVICE_MAC          "FF0822A08E00"
#define DEVICE_FMWARE_VER   "v1.0" // 设备固件版本号
#define DEVICE_SOFTWARE_VER "v2.0" // 设备软件版本号

// Andlink平台环境标志,1为测试环境,0为生产环境,默认是生产环境;厂商一般无需关心测试环境.
#define DEV_USING_TEST_ENVIRONMENT 0

// 测试环境:15位串码,72位一机一密秘钥,DEV_USING_TEST_ENVIRONMENT为1时生效. 厂商一般无需关注.
#define TEST_ANDLINK_SEC_AUTH_ID  "300001243836666"
#define TEST_ANDLINK_SEC_AUTH_KEY "00010001FB660A057B57E2893DA19FB6BE78B58D616943097DB14DBB7F644C0A8419BCD7"


// [若使用一机一密,须要申请]生产环境:测试数据2-27位串码,72位的一机一密 秘钥;厂商可以根据需要申请不同长度的秘钥.
#define ANDLINK_SEC_AUTH_ID  "057602400740002170000000002"
#define ANDLINK_SEC_AUTH_KEY "00010001B05ED1DB1F91747E8CCD03D94677B997637785FB53B6688FD9D44C970C5CDFD8"

#endif

// andlink相关的配置文件存储路径,注意需要断电不丢失
#define CFG_FILE_PATH "/etc/andlink"
// #define CFG_FILE_PATH "/tmp/andlink"

// OTA 分片下载使能
#define OTA_FRAG_DOWNLOAD_ENABLE 0
// OTA每个分片的大小,单位字节
#define OTA_FRAG_SIZE (2 * 1024 * 1024)
// OTA文件存储路径
#define OTA_FILE_PATH "/tmp"
// OTA文件存储的文件名
#define OTA_FILE_NAME "ota.zip"

// 保存设备基本信息
typedef struct
{
    CFG_NET_MODE_e netMode;
    char ip[40];
    char brdAddr[40];
    char mac[32];
    char sn[32];
    char cmei[16];
    int andlinkState;
} DMEO_DEVICE_INFO_T;

// 设备基本信息
static DMEO_DEVICE_INFO_T s_deviceInfo = {
    .netMode = NETWOKR_MODE_WIFI,
    .ip = "",
    .brdAddr = "",
    .mac = DEVICE_REAL_MAC,
    .sn = DEVICE_REAL_SN,
    .cmei = DEVICE_REAL_CMEI
};

/************************************************************************
Description: 获取sdk demo的版本号和编译时间
Input: None
Output: None
Return: sdk demo程序的版本号及编译时间
Others:
************************************************************************/
char *adlDemoVersionGet()
{
    static char s_demoSWVersion[64] = { 0 };
    snprintf(s_demoSWVersion, sizeof(s_demoSWVersion) - 1, "%s%s; "
                                                           "Compiled timestamp: %s %s",
             ADL_DEMO_MAJOR, ADL_DEMO_ST, __DATE__, __TIME__);
    return s_demoSWVersion;
}

/************************************************************************
Description:    设置设备的mac地址
Input:          char *mac
Output:         None
Return:         成功:0, 失败:-1
Others:
************************************************************************/
int demo_setDeviceMac(char *mac)
{
    DEMO_DBG_PRINT("dev setDeviceMac[%s]\n", mac);
    memset(s_deviceInfo.mac, 0, sizeof(s_deviceInfo.mac));
    if (mac && strlen(mac))
    {
        memcpy(s_deviceInfo.mac, mac, sizeof(s_deviceInfo.mac) - 1);
    }

    return 0;
}

/************************************************************************
Description:    获取设备的mac地址
Input:          None
Output:         None
Return:         mac地址字符串
Others:
************************************************************************/
char *demo_getDeviceMac()
{
    return s_deviceInfo.mac;
}

/************************************************************************
Description:    获取设备的sn地址
Input:          None
Output:         None
Return:         sn地址字符串
Others:
************************************************************************/
char *demo_getDeviceSn()
{
    return s_deviceInfo.sn;
}

/************************************************************************
Description:    获取设备的cmei地址
Input:          None
Output:         None
Return:         cmei地址字符串
Others:
************************************************************************/
char *demo_getDeviceCmei()
{
    return s_deviceInfo.cmei;
}

/************************************************************************
Description: 获取一机一密的ID
Input: None
Output: None
Return: 一机一密ID
Others:
************************************************************************/
char *demo_getAuthId()
{
    if (getTestEnvFlag())
    {
        return TEST_ANDLINK_SEC_AUTH_ID;
    }
    else
    {
        return ANDLINK_SEC_AUTH_ID;
    }
}

/************************************************************************
Description: 获取一机一密的预置秘钥
Input: None
Output: None
Return: 预置秘钥字符串
Others:
************************************************************************/
char *demo_getAuthKey()
{
    if (getTestEnvFlag())
    {
        return TEST_ANDLINK_SEC_AUTH_KEY;
    }
    else
    {
        return ANDLINK_SEC_AUTH_KEY;
    }
}

/************************************************************************
Description: 获取andlink的接入状态
Input: None
Output: None
Return: 状态
Others:
************************************************************************/
int demo_get_andlink_state()
{
    return s_deviceInfo.andlinkState;
}

/************************************************************************
Description:    SDK实时通知设备当前的andlink连接状态,设备以此实现灯光显示逻辑;
Input:          ADL_DEV_STATE_e state
Output:         None
Return:         成功:0, 失败:-1
Others:
************************************************************************/
int demo_set_led_callback(ADL_DEV_STATE_e state)
{
    DEMO_DBG_PRINT("[demo]set_led_callback,state=%d\r\n", state);

    s_deviceInfo.andlinkState = state;

    switch (demo_get_andlink_state())
    {
    case ADL_BOOTSTRAP:
        DEMO_DBG_PRINT("========demo andlink bootstrap!\r\n");
        break;

    case ADL_BOOTSTRAP_SUC:
        DEMO_DBG_PRINT("========demo andlink bootstrap SUC!\r\n");
        break;

    case ADL_BOOT:
        DEMO_DBG_PRINT("========demo andlink boot!\r\n");
        break;

    case ADL_BOOT_SUC:
        DEMO_DBG_PRINT("========demo andlink boot SUC!\r\n");
        break;

    case ADL_ONLINE:
        DEMO_DBG_PRINT("========demo andlink online!\r\n");
        break;

    case ADL_BOUND:
        DEMO_DBG_PRINT("========demo andlink bound!\r\n");
        break;
    default:
        break;
    }

    // TODO
    // 厂商根据需要使用
    return 0;
}
/************************************************************************
Description:    厂商调用设置IP地址和广播地址
Input:          char *ip, char *brdAddr
Output:         None
Return:         成功:0, 失败:-1
Others:
************************************************************************/
int demo_set_device_ipaddr_for_callback(char *ip, char *brdAddr)
{
    DEMO_DBG_PRINT("dev setDeviceIpAddr[%s, %s]\n", ip, brdAddr);

    memset(s_deviceInfo.ip, 0, sizeof(s_deviceInfo.ip) - 1);
    memset(s_deviceInfo.brdAddr, 0, sizeof(s_deviceInfo.brdAddr) - 1);

    if (ip && strlen(ip))
    {
        memcpy(s_deviceInfo.ip, ip, sizeof(s_deviceInfo.ip) - 1);
    }

    if (brdAddr && strlen(brdAddr))
    {
        memcpy(s_deviceInfo.brdAddr, brdAddr, sizeof(s_deviceInfo.brdAddr) - 1);
    }

    return 0;
}

/************************************************************************
Description: SDK调用,获取设备IP信息
Input: char *outip, char *outbrdAddr
Output: char *outip, char *outbrdAddr
Return: 成功:0, 失败:-1
Others: SDK以能否获取到IP来判断设备是否已经联网,注意入参可能是NULL;即SDK会单独获取IP或广播地址
************************************************************************/
int demo_get_device_ipaddr_callback(char *outip, char *outbrdAddr)
{
    int ret = -1;
    if (outip)
    {
        if (strlen(s_deviceInfo.ip))
        {
            memcpy(outip, s_deviceInfo.ip, sizeof(s_deviceInfo.ip) - 1);
            ret = 0;
        }
    }

    if (outbrdAddr)
    {
        if (strlen(s_deviceInfo.brdAddr))
        {
            memcpy(outbrdAddr, s_deviceInfo.brdAddr, sizeof(s_deviceInfo.brdAddr) - 1);
            ret = 0;
        }
    }
    return ret;
}

/************************************************************************
Description:    SDK调用,复位设备IP
Input:          None
Output:         None
Return:         成功:0, 失败:-1
Others:
************************************************************************/
int demo_reset_device_Ipaddr_callback()
{
    DEMO_DBG_PRINT("[demo]reset_device_Ipaddr\r\n");
    return demo_set_device_ipaddr_for_callback("", "");
}

/************************************************************************
Description:    SDK实时通知设备执行某些扩展功能;
Input:          ADL_DEV_EXTFUNC_NOTIFY_e type
Output:         None
Return:         成功:0, 失败:-1
Others:
************************************************************************/
int demo_set_extfunc_notify_callback(ADL_DEV_EXTFUNC_NOTIFY_e type)
{
    int ret = 0;
    switch (type)
    {
    case ADL_LOG_SYNC:
        // 进行日志同步,可选
        break;
    default:
        ret = -1;
        break;
    }
    return ret;
}
/************************************************************************
Description:    SDK实时通知设备进行语音提示;
Input:          ADL_DEV_STATE_e state
Output:         None
Return:         成功:0, 失败:-1
Others:         可选
************************************************************************/
int demo_set_voice_notify_callback(ADL_DEV_VOICE_NOTIFY_e type)
{
    // TODO
    switch (type)
    {
    case ADL_NET_START_CONNECTING:
        // 提示:开始配网认证中,请打开和家亲等待
        break;

    case ADL_NET_GUIDE_AP_CONN_FAILED:
        // 提示:连接引导热点失败,请扫码配网
        break;

    case ADL_NET_GUIDE_AP_SUC:
        // 提示:连接引导热点成功,请在和家亲确认
        break;

    case ADL_NET_WORK_AP_CONN_FAILED:
        // 提示:连接引导热点失败,请扫码配网
        break;

    case ADL_NET_WORK_AP_SUC:
        // 提示已成功连接网络,正在尝试连接服务器
        // 厂商可以在此处调用getDeviceInfoStr(ADL_MONITOR_USER_ID)获取userID,然后调用安防SDK绑定接口.
        // TODO
        break;
    default:
        break;
    }
    return 0;
}

/************************************************************************
Description: SDK调用,获取设备的扩展信息
Input:     char *childDeviceId, cJSON *root
Output:    cJSON *root
Return:    成功:0, 失败:-1
Others:    add 22.03.03 by wuhao  V1.6.1版本开始,用于替换getDevExtInfo接口.
childDevId为NULL表示父设备,否则表示子设备
对于平台对接的设备,以下信息中authMode必须为1,即必须走一机一密
注意:APP_USING_ANDLINK_CJSON包含的代码块是必须的,注意APP_USING_ANDLINK_CJSON的定义
************************************************************************/
int demo_get_extInfo_callback(char *childDeviceId, cJSON *root)
{
    if (NULL == root)
    {
        return -1;
    }

    if (childDeviceId)
    {
        // 查询子设备的扩展信息
    }
    else
    {
        // 查询设备的扩展信息
    }

#ifdef APP_USING_ANDLINK_CJSON

    // 1.添加cmei,真实mac,sn,操作系统信息
    adl_cJSON_AddStringToObject(root, "cmei", demo_getDeviceCmei()); // 必填,设备唯一标识
    adl_cJSON_AddStringToObject(root, "mac", demo_getDeviceMac());   // 必填,设备真实MAC,全大写不带冒号
    adl_cJSON_AddStringToObject(root, "sn", demo_getDeviceSn());     // 必填,设备真实SN
    adl_cJSON_AddStringToObject(root, "OS", "arm-linux32 2.6.18");   // 必填,操作系统(包含版本号)
    // adl_cJSON_AddStringToObject(root, "stbId", "11110");         // 机顶盒ID，机顶盒设备必填

    // 2.添加一机一密相关信息

    // sdk是否支持一机一密,若不支持,此处必须填0,否则由用户选择是否走一机一密.
    // 如摄像头等平台对接的产品,必须走一机一密,即authMode=1.
    int authMode = (atoi(getDeviceInfoStr(ADL_AUTH_MODE))) == 0 ? 0 : 1;

    DEMO_DBG_PRINT("getDeviceInfoStr(ADL_AUTH_MODE)=%d,select authMode=%d\r\n", atoi(getDeviceInfoStr(ADL_AUTH_MODE)), authMode);

    adl_cJSON_AddNumberToObject(root, "authMode", authMode); // 必填, 0 一型一密;1 一机一密
    if (1 == authMode)
    {
        char *authId = demo_getAuthId();
        char *authKey = demo_getAuthKey();

        adl_cJSON_AddStringToObject(root, "authId", authId);   // 一机一密必填,智能设备一机一密安全认证的唯一标识,即物料清单里的设备序列号
        adl_cJSON_AddStringToObject(root, "authKey", authKey); // 一机一密必填,智能设备一机一密安全认证的预置秘钥,即物料清单里的登录密码
    }

    // 3.添加保留字段及设备生产日期信息
    adl_cJSON_AddStringToObject(root, "reserve", "11110");    // 可选,厂商特殊标记字段
    adl_cJSON_AddStringToObject(root, "manuDate", "2019-06"); // 必填,设备生产日期,格式为:年-月

    // 4.产品依赖的众多中移插件版本信息
    cJSON *andVersionObj = NULL;
    adl_cJSON_AddItemToObject(root, "cmccVersion", andVersionObj = adl_cJSON_CreateObject());
    // adl_cJSON_AddStringToObject(andVersionObj, "andimsVersion", "v1.0");              // 和家固话 SDK,若使用必填;
    // adl_cJSON_AddStringToObject(andVersionObj, "andnlpVersion", "v1.0");              // 和家语音交互 SDK,若使用必填;
    // adl_cJSON_AddStringToObject(andVersionObj, "anddotVersion", "v1.0");              // 和家安防 SDK,若使用必填;
    // adl_cJSON_AddStringToObject(andVersionObj, "andfacerecVersion", "v1.0");          // 和家人脸识别 SDK,若使用必填;

    // 5.添加各个芯片信息
    // root对象中添加名为chips的一个数组
    cJSON *array_chips = NULL;
    adl_cJSON_AddItemToObject(root, "chips", array_chips = adl_cJSON_CreateArray());

    cJSON *chipObj = NULL;
    // 芯片1的信息
    {
        // array_chips数组内添加一个对象chipObj
        adl_cJSON_AddItemToArray(array_chips, chipObj = adl_cJSON_CreateObject());

        // chipObj对象内添加三个条目(芯片类型,芯片厂商,芯片型号)
        adl_cJSON_AddStringToObject(chipObj, "type", "Wi-Fi");      // 必填
        adl_cJSON_AddStringToObject(chipObj, "factory", "RTL");     // 必填
        adl_cJSON_AddStringToObject(chipObj, "model", "RTL8197FS"); // 必填
    }
#endif
    return 0;
}

/************************************************************************
Description:    SDK以24小时为周期调用,查询设备管理信息,替代之前中移终端公司DM功能
Input:          char *childDeviceId, cJSON *root
Output:         cJSON *root
Return:         成功:0, 失败:-1
Others:         childDevId为NULL表示父设备,否则表示子设备
注意:APP_USING_ANDLINK_CJSON包含的代码块是必须的,注意APP_USING_ANDLINK_CJSON的定义
************************************************************************/
int demo_get_dmInfo_callback(char *childDeviceId, cJSON *root)
{
    if (NULL == root)
    {
        return -1;
    }

    if (childDeviceId)
    {
        // 查询子设备的DM信息
    }
    else
    {
        // 查询设备的DM信息
    }

#ifdef APP_USING_ANDLINK_CJSON

    DEMO_DBG_PRINT("[demo]start demo_get_dmInfo_callback\r\n");

    // 1.添加cmei,真实mac,sn,操作系统信息
    adl_cJSON_AddStringToObject(root, "cmei", demo_getDeviceCmei()); // 必填,设备唯一标识
    adl_cJSON_AddStringToObject(root, "mac", demo_getDeviceMac());   // 必填,设备真实MAC,全大写不带冒号
    adl_cJSON_AddStringToObject(root, "sn", demo_getDeviceSn());     // 必填,设备真实SN
    adl_cJSON_AddStringToObject(root, "OS", "arm-linux32 2.6.18");   // 必填,操作系统(包含版本号)
    // adl_cJSON_AddStringToObject(root, "stbId", "11110");        // 机顶盒ID，机顶盒设备必填

    // 2.添加DM设备信息
    adl_cJSON_AddStringToObject(root, "cpuModel", "Allwinner-A40i"); // 必填,不存在填NONE,处理器型号;update by 220909
    adl_cJSON_AddStringToObject(root, "romStorageSize", "128MB");    // 必填,不存在填NONE,设备的存储总容量（ROM）大小，与工信部登记终端ROM信息一致;update by 220909
    adl_cJSON_AddStringToObject(root, "ramStorageSize", "64MB");     // 必填,不存在填NONE,设备的内存总容量（RAM）大小，与工信部登记争端RAM信息一致;update by 220909

    // 必填,网络类型.规则:RJ45, Wi-Fi, 5G, 4G, 3G, NB, ZigBee等,若多种连接方式,使用/分隔,比如Wi-Fi/RJ45
    adl_cJSON_AddStringToObject(root, "networkType", "Wi-Fi");

    // 设备当前位置，前面表示经度、后面表示维度可选（支持定位设备必填）,第三个参数取值:1：GPS; 2：北斗; 4：伽利略; 8：格洛纳斯; 16:基站定位; 32：WiFi定位;  协同定位：GPS+北斗,值为1|2=3
    adl_cJSON_AddStringToObject(root, "locationInfo", "123.52958679200002,25.77701556036132,1"); // 必填,不存在填NONE;update by 220909

    adl_cJSON_AddStringToObject(root, "deviceVendor", DEVICE_VENDOR_NAME);    // 必填,设备制造商
    adl_cJSON_AddStringToObject(root, "deviceBrand", "WJA");                  // 必填,设备品牌
    adl_cJSON_AddStringToObject(root, "deviceModel", "wja001200");            // 必填,设备型号
    adl_cJSON_AddStringToObject(root, "wlanMac", "112233445566");             // 必填,不存在填NONE,设备的WLAN MAC地址;update by 220909
    adl_cJSON_AddStringToObject(root, "bluetoothMacAddress", "665544332211"); // 蓝牙设备必填,蓝牙设备的 MAC地址

    // 必填,供电方式.规则:电池供电:battery; POE供电:POE; 市电:220V(110V); USB供电:USB; 其他方式:other
    adl_cJSON_AddStringToObject(root, "powerSupplyMode", "220V");

    // 必填,设备IP.规则:默认上报有线网卡的IP;单栈ipv6需要支持上报ipv6地址;对于双栈ipv4和ipv6同时存在的环境,上报由厂家任选一种;不存在填NONE
    adl_cJSON_AddStringToObject(root, "deviceIP", "*************");

    // 可选,Wi-Fi信号场强
    adl_cJSON_AddStringToObject(root, "wifiRssi", "-60");

    // 3.添加DM模组信息,根据实际情况可选
    // root对象中添加名为moduleInfo的一个数组
    cJSON *array_moduleInfo = NULL;
    adl_cJSON_AddItemToObject(root, "moduleInfo", array_moduleInfo = adl_cJSON_CreateArray());

    // 模组1:wifi模组信息
    cJSON *wifiModuleObj = NULL;
    {
        // array_moduleInfo数组内添加一个对象wifiModuleObj
        adl_cJSON_AddItemToArray(array_moduleInfo, wifiModuleObj = adl_cJSON_CreateObject());
        // wifiModuleObj对象内添加1个条目
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleType", "Wi-Fi");            // 模组类型, NB、Wi-Fi、Zigbee、Bluetooth、Thread、lora、ZWAVE等
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleVendor", "11110");          // 模组厂商名
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleBrand", "11110");           // 模组品牌名
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleModel", "11110");           // 模组型号
        adl_cJSON_AddStringToObject(wifiModuleObj, "ctaNetworkModel", "11110");       // CTA入网许可证型号,支持蜂窝入网的模组必填
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleFirmwareVersion", "11110"); // 模组固件版本
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleSystemVersion", "11110");   // 模组系统版本
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleImei", "11110");            // 模组IMEI
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleImsi", "11110");            // 模组IMSI
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleIccid", "11110");           // 模组ICCID号
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleCellid", "11110");          // 模组Cellid
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleLac", "11110");             // 模组Lac
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleBleMacAddress", "11110");   // 模组蓝牙MAC地址
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleMacAddress", "11110");      // 模组MAC地址
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleWlanMac", "11110");         // 模组采集的设备Wlan MAC地址
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleSn", "11110");              // 模组SN
    }

    // 模组2:蓝牙模组信息
    cJSON *btModuleObj = NULL;
    {
        // array_moduleInfo数组内添加一个对象wifiModuleObj
        adl_cJSON_AddItemToArray(array_moduleInfo, btModuleObj = adl_cJSON_CreateObject());
        // wifiModuleObj对象内添加1个条目
        adl_cJSON_AddStringToObject(btModuleObj, "moduleType", "Bluetooth");        // 模组类型, NB、WiFi、Zigbee、Bluetooth、Thread、lora、ZWAVE等
        adl_cJSON_AddStringToObject(btModuleObj, "moduleVendor", "11110");          // 模组厂商名
        adl_cJSON_AddStringToObject(btModuleObj, "moduleBrand", "11110");           // 模组品牌名
        adl_cJSON_AddStringToObject(btModuleObj, "moduleModel", "11110");           // 模组型号
        adl_cJSON_AddStringToObject(btModuleObj, "ctaNetworkModel", "11110");       // CTA入网许可证型号,支持蜂窝入网的模组必填
        adl_cJSON_AddStringToObject(btModuleObj, "moduleFirmwareVersion", "11110"); // 模组固件版本
        adl_cJSON_AddStringToObject(btModuleObj, "moduleSystemVersion", "11110");   // 模组系统版本
        adl_cJSON_AddStringToObject(btModuleObj, "moduleImei", "11110");            // 模组IMEI
        adl_cJSON_AddStringToObject(btModuleObj, "moduleImsi", "11110");            // 模组IMSI
        adl_cJSON_AddStringToObject(btModuleObj, "moduleIccid", "11110");           // 模组ICCID号
        adl_cJSON_AddStringToObject(btModuleObj, "moduleCellid", "11110");          // 模组Cellid
        adl_cJSON_AddStringToObject(btModuleObj, "moduleLac", "11110");             // 模组Lac
        adl_cJSON_AddStringToObject(btModuleObj, "moduleBleMacAddress", "11110");   // 模组蓝牙MAC地址
        adl_cJSON_AddStringToObject(btModuleObj, "moduleMacAddress", "11110");      // 模组MAC地址
        adl_cJSON_AddStringToObject(btModuleObj, "moduleWlanMac", "11110");         // 模组采集的设备Wlan MAC地址
        adl_cJSON_AddStringToObject(btModuleObj, "moduleSn", "11110");              // 模组SN
    }

    adl_cJSON_AddStringToObject(root, "additionalSlotImei", "11110");   // 可选,额外卡槽IMEI，模组出厂卡槽之外的卡槽信息
    adl_cJSON_AddStringToObject(root, "additionalSlotImsi", "11110");   // 可选,额外卡槽IMSI，模组出厂卡槽之外的卡槽信息
    adl_cJSON_AddStringToObject(root, "additionalSlotIccid", "11110");  // 可选,额外卡槽ICCID，模组出厂卡槽之外的卡槽信息
    adl_cJSON_AddStringToObject(root, "additionalSlotCellid", "11110"); // 可选,额外卡槽CellId，模组出厂卡槽之外的卡槽信息
    adl_cJSON_AddStringToObject(root, "additionalSlotLac", "11110");    // 可选, 额外卡槽Lac，模组出厂卡槽之外的卡槽信息

    // 4.添加版本信息
    // 4.1产品本身版本信息
    adl_cJSON_AddStringToObject(root, "firmwareVersion", DEVICE_FMWARE_VER);
    adl_cJSON_AddStringToObject(root, "softwareVersion", DEVICE_SOFTWARE_VER);

    // 4.2产品依赖的众多中移插件版本信息
    cJSON *andVersionObj = NULL;
    adl_cJSON_AddItemToObject(root, "cmccVersion", andVersionObj = adl_cJSON_CreateObject());
    // adl_cJSON_AddStringToObject(andVersionObj, "andimsVersion", "v1.0");              // 和家固话 SDK,若使用必填;
    // adl_cJSON_AddStringToObject(andVersionObj, "andnlpVersion", "v1.0");              // 和家语音交互 SDK,若使用必填;
    // adl_cJSON_AddStringToObject(andVersionObj, "anddotVersion", "v1.0");              // 和家安防 SDK,若使用必填;
    // adl_cJSON_AddStringToObject(andVersionObj, "andfacerecVersion", "v1.0");          // 和家人脸识别 SDK,若使用必填;

    // AHS中间件版本,必填; 组网设备:AOS-NET-1.0; 机顶盒:A0S-STB-1.0; 摄像头:AOS-IPC-Linux V1.0.0; 其他智能设备:AOS-DEV-1.0
    adl_cJSON_AddStringToObject(andVersionObj, "AHSVersion", "AOS-DEV-1.0");

    // 添加其他扩展字段
    cJSON *dmExt = NULL;
    adl_cJSON_AddItemToObject(root, "deviceManageExtInfo", dmExt = adl_cJSON_CreateObject()); // 必填

#endif
    return 0;
}

/************************************************************************
Description:    构造厂商设备信息配置文件,用于准备启动andlinkInit的入参信息
Input:          adl_dev_attr_t *outDevAttr
Output:         adl_dev_attr_t *outDevAttr
Return:         成功:0, 失败:-1
Others:         此接口只是为了调试demo方便,厂商可以不用
************************************************************************/
int buildFacDevinfoCfgFile(adl_dev_attr_t *outDevAttr)
{
    char MAC[32] = { 0 };
    // 为加载andlink SDK准备入参

    // 这三个信息在连楹家庭智慧平台门户上(https://open.home.10086.cn )创建产品后自动分配;
    outDevAttr->deviceType = DEVICE_TYPE;        // 产品类型ID,即连楹家庭智慧平台门户上 创建完产品后生成的 产品ID;
    outDevAttr->productToken = PRODUCTION_TOKEN; // 设备预置秘钥,即连楹家庭智慧平台门户上的 产品验证码;
    outDevAttr->andlinkToken = ANDLINK_TOKEN;    // 中国移动Andlink IoT云平台秘钥,即连楹家庭智慧平台门户上的 平台验证码;

    outDevAttr->deviceVendor = DEVICE_VENDOR_NAME; // 厂商名称
    outDevAttr->deviceMac = DEVICE_MAC;            // 设备MAC地址或SN

    /*
    ########################@@@@ notice notice notice @@@@########################
    1.deviceMac是设备唯一标识,这里仅是示例,可以填mac或sn
    2.若填mac,且设备存在有线和无线双网卡,不管有线启动,还是无线启动,均使用无线网卡MAC
    */
    if (0 == check_device_mac_info(IF_NAME, MAC) || 0 == check_device_mac_info(WIRED_IF_NAME, MAC))
    {
        demo_setDeviceMac(MAC);
        outDevAttr->deviceMac = demo_getDeviceMac();
    }

    outDevAttr->firmWareVersion = DEVICE_FMWARE_VER;   // 设备固件版本号
    outDevAttr->softWareVersion = DEVICE_SOFTWARE_VER; // 设备软件版本号
    outDevAttr->cfgPath = CFG_FILE_PATH;               // SDK配置文件存储位置

    // 构造配置文件
    char debugFileName[256] = { 0 };
    snprintf(debugFileName, sizeof(debugFileName) - 1, "%s/facDevinfo.conf", outDevAttr->cfgPath);

    FILE *fd = NULL;
    if (access(outDevAttr->cfgPath, W_OK) != 0)
    {
        if (mkdir(outDevAttr->cfgPath, 0777) < 0)
        {
            DEMO_DBG_PRINT("mkdir %s failed\n", outDevAttr->cfgPath);
            return -1;
        }
    }

    DEMO_DBG_PRINT("[fac]build devinfo cfg file=%s\n", debugFileName);

    // 不存在,则手动创建一个默认调试配置文件.
    if ((fd = fopen(debugFileName, "w+")) == NULL)
    {
        DEMO_DBG_PRINT("createFile %s failed\n", debugFileName);
        return -1;
    }

    // 构造默认调试配置文件
    fputs("#facturer dev configuration file\n", fd);

    fputs("#[basic]#\n", fd);
    char cfgStr[128];

    memset(cfgStr, 0, sizeof(cfgStr));
    snprintf(cfgStr, sizeof(cfgStr) - 1, "deviceType=%s\n", outDevAttr->deviceType);
    fputs(cfgStr, fd);

    memset(cfgStr, 0, sizeof(cfgStr));
    snprintf(cfgStr, sizeof(cfgStr) - 1, "productToken=%s\n", outDevAttr->productToken);
    fputs(cfgStr, fd);

    memset(cfgStr, 0, sizeof(cfgStr));
    snprintf(cfgStr, sizeof(cfgStr) - 1, "andlinkToken=%s\n", outDevAttr->andlinkToken);
    fputs(cfgStr, fd);

    memset(cfgStr, 0, sizeof(cfgStr));
    snprintf(cfgStr, sizeof(cfgStr) - 1, "deviceVendor=%s\n", outDevAttr->deviceVendor);
    fputs(cfgStr, fd);

    memset(cfgStr, 0, sizeof(cfgStr));
    snprintf(cfgStr, sizeof(cfgStr) - 1, "deviceMac=%s\n", outDevAttr->deviceMac);
    fputs(cfgStr, fd);

    memset(cfgStr, 0, sizeof(cfgStr));
    snprintf(cfgStr, sizeof(cfgStr) - 1, "firmWareVersion=%s\n", outDevAttr->firmWareVersion);
    fputs(cfgStr, fd);

    memset(cfgStr, 0, sizeof(cfgStr));
    snprintf(cfgStr, sizeof(cfgStr) - 1, "softWareVersion=%s\n", outDevAttr->softWareVersion);
    fputs(cfgStr, fd);

    memset(cfgStr, 0, sizeof(cfgStr));
    snprintf(cfgStr, sizeof(cfgStr) - 1, "cfgPath=%s\n", outDevAttr->cfgPath);
    fputs(cfgStr, fd);

    /*-----------------------start for sdk test, ignore the following code for user.-----------------------*/
    fputs("#[test]#\n", fd);
    // 设置为测试环境模式;一般情况下,SDK调用者只需要在生产环境验证.
    int flag = DEV_USING_TEST_ENVIRONMENT;
    if (flag)
    {
        memset(cfgStr, 0, sizeof(cfgStr));
        snprintf(cfgStr, sizeof(cfgStr) - 1, "testEnv=%d\n", flag);
        fputs(cfgStr, fd);

        setTestEnvFlag(flag);
    }

    // 设置mac
    memset(cfgStr, 0, sizeof(cfgStr));
    snprintf(cfgStr, sizeof(cfgStr) - 1, "mac=%s\n", DEVICE_REAL_MAC);
    fputs(cfgStr, fd);

    // 设置sn
    memset(cfgStr, 0, sizeof(cfgStr));
    snprintf(cfgStr, sizeof(cfgStr) - 1, "sn=%s\n", DEVICE_REAL_SN);
    fputs(cfgStr, fd);

    // 设置cmei
    memset(cfgStr, 0, sizeof(cfgStr));
    snprintf(cfgStr, sizeof(cfgStr) - 1, "cmei=%s\n", DEVICE_REAL_CMEI);
    fputs(cfgStr, fd);

    // 设置下载策略
    int mode = OTA_FRAG_DOWNLOAD_ENABLE;
    {
        memset(cfgStr, 0, sizeof(cfgStr));
        snprintf(cfgStr, sizeof(cfgStr) - 1, "otaPolicy=%d\n", mode);
        fputs(cfgStr, fd);

        int oneFragSize = OTA_FRAG_SIZE;
        memset(cfgStr, 0, sizeof(cfgStr));
        snprintf(cfgStr, sizeof(cfgStr) - 1, "otaFragSize=%d\n", oneFragSize);
        fputs(cfgStr, fd);

        setOtaDownloadPolicy(mode, oneFragSize);
    }

    // 设置下载的文件路径和文件名
    {
        char *filePath = OTA_FILE_PATH;
        memset(cfgStr, 0, sizeof(cfgStr));
        snprintf(cfgStr, sizeof(cfgStr) - 1, "otaFilePath=%s\n", filePath);
        fputs(cfgStr, fd);

        char *filename = OTA_FILE_NAME;
        memset(cfgStr, 0, sizeof(cfgStr));
        snprintf(cfgStr, sizeof(cfgStr) - 1, "otaFileName=%s\n", filename);
        fputs(cfgStr, fd);

        setOtaStoragePathAndFilename(filePath, filename);
    }

    /*-----------------------end for sdk test-----------------------*/

    fclose(fd);

    DEMO_DBG_PRINT("[fac]build devinfo cfg file OK\n");

    return 0;
}

/************************************************************************
Description:    恢复厂商设备信息配置文件,用于准备启动andlinkInit的入参信息
Input:          adl_dev_attr_t *outDevAttr
Output:         adl_dev_attr_t *outDevAttr
Return:         成功:0, 失败:-1
Others:         此接口只是为了调试demo方便,厂商可以不用
************************************************************************/
int recoverFacDevinfoCfg(adl_dev_attr_t *outDevAttr)
{
    int itemMemLen = 0;
    int lineLen = 0;
    char lineBuf[128] = { 0 };
    char debugFileName[256] = { 0 };

    snprintf(debugFileName, sizeof(debugFileName) - 1, "%s/facDevinfo.conf", CFG_FILE_PATH);

    /*----读取andlink SDK启动必须的一些信息----*/
    // 读取产品ID
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "deviceType", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover deviceType failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        itemMemLen = (strlen(lineBuf) + 7) & ~0x3;
        outDevAttr->deviceType = calloc(itemMemLen, 1);
        memcpy(outDevAttr->deviceType, lineBuf, itemMemLen);
    }

    // 读取 productToken
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "productToken", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover productToken failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        itemMemLen = (strlen(lineBuf) + 7) & ~0x3;
        outDevAttr->productToken = calloc(itemMemLen, 1);
        memcpy(outDevAttr->productToken, lineBuf, itemMemLen);
    }

    // 读取 andlinkToken
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "andlinkToken", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover andlinkToken failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        itemMemLen = (strlen(lineBuf) + 7) & ~0x3;
        outDevAttr->andlinkToken = calloc(itemMemLen, 1);
        memcpy(outDevAttr->andlinkToken, lineBuf, itemMemLen);
    }

    // 读取 deviceVendor
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "deviceVendor", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover deviceVendor failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        itemMemLen = (strlen(lineBuf) + 7) & ~0x3;
        outDevAttr->deviceVendor = calloc(itemMemLen, 1);
        memcpy(outDevAttr->deviceVendor, lineBuf, itemMemLen);
    }

    // 读取 deviceMac
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "deviceMac", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover deviceMac failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        itemMemLen = (strlen(lineBuf) + 7) & ~0x3;
        outDevAttr->deviceMac = calloc(itemMemLen, 1);
        memcpy(outDevAttr->deviceMac, lineBuf, itemMemLen);
    }

    // 读取 firmWareVersion
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "firmWareVersion", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover firmWareVersion failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        itemMemLen = (strlen(lineBuf) + 7) & ~0x3;
        outDevAttr->firmWareVersion = calloc(itemMemLen, 1);
        memcpy(outDevAttr->firmWareVersion, lineBuf, itemMemLen);
    }

    // 读取 softWareVersion
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "softWareVersion", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover softWareVersion failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        itemMemLen = (strlen(lineBuf) + 7) & ~0x3;
        outDevAttr->softWareVersion = calloc(itemMemLen, 1);
        memcpy(outDevAttr->softWareVersion, lineBuf, itemMemLen);
    }

    // 读取 cfgPath
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "cfgPath", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover cfgPath failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        itemMemLen = (strlen(lineBuf) + 7) & ~0x3;
        outDevAttr->cfgPath = calloc(itemMemLen, 1);
        memcpy(outDevAttr->cfgPath, lineBuf, itemMemLen);
    }

    /*----读取一些调试信息----*/
    // 读取 testEnv
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "testEnv", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover testEnv failed\n");
    }

    if ((lineLen = strlen(lineBuf)) > 0)
    {
        setTestEnvFlag(atoi(lineBuf));
    }

    // 读取 cmei
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "cmei", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover cmei failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        itemMemLen = (strlen(lineBuf) + 7) & ~0x3;
        memcpy(s_deviceInfo.cmei, lineBuf, sizeof(s_deviceInfo.cmei) - 1);
    }

    // 读取 sn
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "sn", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover sn failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        itemMemLen = (strlen(lineBuf) + 7) & ~0x3;
        memcpy(s_deviceInfo.sn, lineBuf, sizeof(s_deviceInfo.sn) - 1);
    }

    // 读取 mac
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "mac", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover mac failed\n");
    }
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        itemMemLen = (strlen(lineBuf) + 7) & ~0x3;
        memcpy(s_deviceInfo.mac, lineBuf, sizeof(s_deviceInfo.mac) - 1);
    }

#if 1 // 根据配置文件设置OTA的下载策略
    // 读取 otaPolicy
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "otaPolicy", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover otaPolicy failed\n");
    }

    int otaMode = 0;
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        otaMode = atoi(lineBuf);
    }

    // 读取 otaFragSize
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "otaFragSize", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover otaFragSize failed\n");
    }

    int oneFragSize = 0;
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        oneFragSize = atoi(lineBuf);
    }
    setOtaDownloadPolicy(otaMode, oneFragSize);
#endif

#if 1 // 根据配置文件设置OTA存储的路径及文件名
    // 读取 otaPolicy
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "otaFilePath", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover otaPolicy failed\n");
    }

    char otaFilePath[128] = { 0 };
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        memcpy(otaFilePath, lineBuf, sizeof(otaFilePath) - 1);
    }

    // 读取 otaFragSize
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "otaFileName", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover otaFragSize failed\n");
    }

    char otaFileName[128] = { 0 };
    if ((lineLen = strlen(lineBuf)) > 0)
    {
        memcpy(otaFileName, lineBuf, sizeof(otaFileName) - 1);
    }
    setOtaStoragePathAndFilename(otaFilePath, otaFileName);
#endif

    return 0;

FAILEXIT:
    return -1;
}

#if 1 // 厂商可选的配置文件读写接口;
// andlink 配置文件名
#define ANDLINK_CFG_FILENAME "/etc/andlink/andlinkSdk.conf"
/************************************************************************
Description:    SDK调用,读取配置项
Input:          char *item, char *value, int bufsize
Output:         None
Return:         成功:0, 失败:-1
Others:
************************************************************************/
int demo_getCfg_callback(char *item, char *outbuf, int bufsize)
{
    char *fileName = ANDLINK_CFG_FILENAME;

    FILE *fp = NULL;
    char lineContent[256] = { 0 };
    char *p = NULL;

    if ((NULL == fileName) || (NULL == item) || (NULL == outbuf))
    {
        DEMO_DBG_PRINT("func parameter NULL\n");
        return -1;
    }

    fp = fopen(fileName, "r");
    if (NULL == fp)
    {
        return -1;
    }

    while (NULL != fgets(lineContent, sizeof(lineContent) - 1, fp))
    {
        // 过滤包含#的注释行
        if (0 == strncmp(lineContent, "#", strlen("#")))
        {
            continue;
        }

        p = strstr(lineContent, item);
        if (NULL != p && 0 == strncmp(lineContent, item, strlen(item)))
        {
            strncpy(outbuf, p + strlen(item) + 1, bufsize);
            p = strchr(outbuf, '\n');
            if (NULL != p)
            {
                *p = 0;
            }
            goto EXIT;
        }
    }

FAIL_EXIT:
    fclose(fp);
    fp = NULL;
    return -1;

EXIT:
    fclose(fp);
    fp = NULL;
    return 0;
}

/************************************************************************
Description:    SDK调用,写配置项
Input:          char *item, char *value, int bufsize
Output:         None
Return:         成功:0, 失败:-1
Others:
************************************************************************/
int demo_setCfg_callback(char *item, char *value)
{
    const char *fileName = ANDLINK_CFG_FILENAME;

    FILE *fp = NULL;
    FILE *fp_tmp = NULL;
    char buf[256];
    char *p = NULL;
    int tmpFileNameLen = strlen(fileName);
    char tmpfile[tmpFileNameLen + 10];
    memset(tmpfile, 0, tmpFileNameLen + 1);

    sprintf(tmpfile, "%s.tmp", fileName);
    int itemExist = 0;

    if ((NULL == item) || (NULL == value))
    {
        return -1;
    }

    fp = fopen(fileName, "r");

    fp_tmp = fopen(tmpfile, "w");
    if (NULL == fp_tmp)
    {
        if (fp)
        {
            fclose(fp);
            fp = NULL;
        }
        return -1;
    }

    if (NULL != fp)
    {
        while (NULL != fgets(buf, sizeof(buf), fp))
        {
            p = strstr(buf, item);
            if (NULL != p && 0 == strncmp(buf, item, strlen(item)))
            {
                strcpy(p + strlen(item) + 1, value);
                p += strlen(item) + 1 + strlen(value);
                *p = 0;
                fprintf(fp_tmp, "%s\n", buf);
                itemExist = 1;
            }
            else
            {
                fprintf(fp_tmp, "%s", buf);
            }
        }

        if (fp)
        {
            fclose(fp);
            fp = NULL;
        }

        unlink(fileName);
    }

    if (0 == itemExist)
    {
        sprintf(buf, "%s=%s", item, value);
        fprintf(fp_tmp, "%s\n", buf);
    }

    if (fp_tmp)
    {
        fclose(fp_tmp);
        fp_tmp = NULL;
    }

    rename(tmpfile, fileName);
    unlink(tmpfile);
    return 0;
}
#endif