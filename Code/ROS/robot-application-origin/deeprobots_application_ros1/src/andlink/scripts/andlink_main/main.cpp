/*
 * Copyright (c) 2019,中移(杭州)信息技术有限公司
 * All rights reserved.
 *
 * 文件名称：main.c
 * 说 明：主函数所在文件
 *
 * 当前版本：1.0
 * 作 者：wuh
 * 完成日期：2020年08月22日
 *
 */

#include "include/andlink_adapt.h"
#include "include/tcpclient.h"
// 云深处实现
#include "Gobbledegook.h"
#include <iostream>
#include <thread>
#include <iomanip>
#include <sstream>
#include <memory>
#include <array>
#include <cstdlib>
#include <unistd.h>
#include <cstdio>

#include <cstring>
#include <ifaddrs.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <fstream>
#include <ctime>
#include <vector>
#include <regex>
#define ANDLINK_DEV_ACCESS_LINK_ENABLE 1

TcpClient tcp_client_t("127.0.0.1",31426 );
bool is_connect = false;

char g_ifname[16]= "";
int g_ble_init_flag = 0;

struct DeviceInfo {
    char ip[INET_ADDRSTRLEN];
    char brdAddr[INET_ADDRSTRLEN];
};

/************************************************************************
Description: 安全的字符串拷贝接口
Input: char *dest, size_t dmax, const char *src, size_t slen
Output: char *dest
Return: 成功:0, 失败:-1
Others: 要求dmax>slen,否则输出空字符串
************************************************************************/
int app_strncpy_s(char *dest, size_t dmax, const char *src, size_t slen)
{
    if (dest == NULL || src == NULL)
    {
        return -1;
    }
    if (dmax == 0)
    {
        return -1;
    }
    if (dmax <= slen)
    {
        memset(dest, 0, dmax);
        return -1;
    }

    size_t i;
    for (i = 0; i < slen && src[i] != '\0'; i++)
    {
        dest[i] = src[i];
    }
    if (i < dmax)
    {
        dest[i] = '\0';
    }
    return 0;
}
/************************************************************************
Description: 获取字符串的长度,不包括"\0"
Others:
************************************************************************/
unsigned int app_strlen(const char *s)
{
    if (NULL == s)
    {
        return 0;
    }
    return (unsigned int)strlen(s);
}

// 功能参数索引-名称接口
typedef struct
{
    int funcIndex;
    char *function;
} FUNCTION_INDEX_MAP_T;

// 设备支持的下行管控功能列表
static FUNCTION_INDEX_MAP_T s_adlFunctionSets[] = {
    { 0, "Control" },
    { 1, "Unbind" },
    { 2, "SelfDetect" },
    { 3, "Reboot" },
    { 4, "Bind" }
};

/************************************************************************
Description: 安全的snprintf函数
Input: char *str, size_t size, const char *format, ...
Output: None
Return: 成功:格式化的字节数, 失败:-1
Others: 要求dmax>slen,否则截断
************************************************************************/
int app_snprintf_s(char *dest, size_t dmax, const char *format, ...)
{
    if (dest == NULL || format == NULL)
    {
        return -1;
    }
    if (dmax == 0)
    {
        return -1;
    }

    va_list args;
    va_start(args, format);
    // 使用 vsnprintf 进行格式化
    int ret = vsnprintf(dest, dmax, format, args);
    va_end(args);

    // 确保字符串以空字符结尾
    if (ret < 0 || (size_t)ret >= dmax)
    {
        // 如果缓冲区不够大，确保最后一个字符是 '\0'
        if (dmax > 0)
        {
            dest[dmax - 1] = '\0';
        }
        return -1;
    }

    return ret;
}

/************************************************************************
Description: 获取管控功能的索引
Input: char *function, int *outIndex
Output: int *outIndex
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int checFunctionIndex(char *function, int *outIndex)
{
    int i = 0;
    if (NULL == function || 0 == strlen(function))
    {
        return -1;
    }

    char tmpFunc[16] = { 0 };
    strncpy(tmpFunc, function, sizeof(tmpFunc) - 1);
    // strtolower(tmpFunc);

    for (i = 0; i < sizeof(s_adlFunctionSets) / sizeof(FUNCTION_INDEX_MAP_T); ++i)
    {
        if (0 == strcmp(s_adlFunctionSets[i].function, tmpFunc))
        {
            *outIndex = s_adlFunctionSets[i].funcIndex;
            return 0;
        }
    }

    return -1;
}

int ysc_dn_send_cmd_callback(RESP_MODE_e mode, dn_dev_ctrl_frame_t *ctrlFrame, char *eventType, char *respData, unsigned int respBufSize)
{
#ifdef ANDLINK_DEV_ACCESS_LINK_ENABLE
    DEMO_DBG_PRINT("[demo]dn_send_cmd_callback(%s),mode=%d(0:no; 1:async; 2:sync;)\r\n", ctrlFrame->function, mode);
    // TODO
    int funcIndex = 0;

    char *localEventType = NULL;
    char localResp[256] = { 0 };
    unsigned long respSize = respBufSize;

    // 处理管控指令
    if (0 != checFunctionIndex(ctrlFrame->function, &funcIndex))
    {
        goto FAIL_EXIT;
    }

    switch (funcIndex)
    {
    case 0: {// Control
        DEMO_DBG_PRINT("andlink: Control\n");
        // 处理控制指令TODO

        localEventType = "ParamChange";
        break;
    }

    case 1: {// Unbind//解绑处理
        char time_str[20];
        strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", localtime(&(time_t){time(NULL)}));
        DEMO_DBG_PRINT("andlink: Unbind\n");
        char header[255] = {0x55,0x55,0x00,0x03};
        tcp_client_t.send2server(header,4);
        break;
    }

    case 2: {// SelfDetect//自检
        DEMO_DBG_PRINT("andlink: SelfDetect\n");
        // 处理自检指令 TODO

        char *format = "{\"cpuRate\":%d,\"ramRate\":%d,\"upLinkType\":\"%s\",\"rssi\":\"%s\"}";
        localEventType = "SelfDetect";
        app_snprintf_s(localResp, sizeof(localResp) - 1, format, 30, 30, "Wi-Fi", "-45");

        break;
    }

    case 3: {// Reboot//重启,此接口最好必须实现,用于通过远程重启规避某些严重的现网问题.
        DEMO_DBG_PRINT("andlink: Reboot\n");
        // 指定设备重启
        break;
    }
    default:
        goto FAIL_EXIT;
    }

    // 进行回应
    switch (mode)
    {
    case ASYNC_MODE:
        // 异步响应
        if (NULL != ctrlFrame->data && NULL != localEventType)
        {
            devDataReport(localEventType, ctrlFrame->seqId, 0, ctrlFrame->data, app_strlen(ctrlFrame->data));
        }

        break;

    case SYNC_MODE:

        // 同步响应
        if (NULL != eventType && NULL != localEventType)
        {
            app_strncpy_s(eventType, sizeof(ctrlFrame->function) - 1, localEventType, app_strlen(localEventType));
        }

        if (NULL != respData)
        {
            if (app_strlen(localResp))
            {
                app_strncpy_s(respData, respSize, localResp, app_strlen(localResp));
            }
            else
            {
                app_strncpy_s(respData, respSize, ctrlFrame->data, app_strlen(ctrlFrame->data));
            }

            DEMO_DBG_PRINT("sync response=type:%s,data:%s,datalen=%u\n", eventType, respData, app_strlen(respData));
        }

        break;
    default:
        break;
    }
    return 0;
FAIL_EXIT:
    return -1;
#else
    return -1;
#endif

}

int ysc_get_device_ipaddr_callback(char *outip, char *outbrdAddr)
{
    DeviceInfo s_deviceInfo = {"", ""};
    int ret = -1;

    std::cout<<"enter ysc_get_device_ipaddr_callback"<<std::endl;
    // 如果 IP 和广播地址还未获取，使用 getifaddrs 获取
    if (strlen(s_deviceInfo.ip) == 0 || strlen(s_deviceInfo.brdAddr) == 0) {
        struct ifaddrs *interfaces = nullptr;
        struct ifaddrs *tempAddr = nullptr;

        // 获取本机的所有网络接口信息
        if (getifaddrs(&interfaces) == 0) {
            tempAddr = interfaces;

            while (tempAddr != nullptr) {
                if (tempAddr->ifa_addr && tempAddr->ifa_addr->sa_family == AF_INET) { // 只处理IPv4地址
                    // 匹配特定网口，例如 "wlan0"
                    if (strcmp(tempAddr->ifa_name, g_ifname) == 0) {
                        // 获取 IP 地址
                        struct sockaddr_in *sockaddr_ipv4 = (struct sockaddr_in *)tempAddr->ifa_addr;
                        inet_ntop(AF_INET, &sockaddr_ipv4->sin_addr, outip, INET_ADDRSTRLEN);

                        // 获取广播地址
                        if (tempAddr->ifa_broadaddr) {
                            struct sockaddr_in *sockaddr_brd = (struct sockaddr_in *)tempAddr->ifa_broadaddr;
                            inet_ntop(AF_INET, &sockaddr_brd->sin_addr, outbrdAddr, INET_ADDRSTRLEN);
                        }
                        ret = 0;
                        break; // 找到指定接口后退出循环
                    }
                }
                tempAddr = tempAddr->ifa_next;
            }
        }

        // 释放内存
        if (interfaces != nullptr) {
            freeifaddrs(interfaces);
        }
    }

    // 返回 IP 地址
    if (outip) {
        if (strlen(s_deviceInfo.ip)) {
            memcpy(outip, s_deviceInfo.ip, sizeof(s_deviceInfo.ip) - 1);
            ret = 0;
        }
    }

    // 返回广播地址
    if (outbrdAddr) {
        if (strlen(s_deviceInfo.brdAddr)) {
            memcpy(outbrdAddr, s_deviceInfo.brdAddr, sizeof(s_deviceInfo.brdAddr) - 1);
            ret = 0;
        }
    }

    return ret;
}

// 判断 wlan0 是否已连接到指定的 SSID
bool isWlan0ConnectedToSSID(const std::string& ssid) {
    std::string cmd = "LANG=C nmcli -t -f DEVICE,STATE,CONNECTION device status";
    std::array<char, 128> buffer;
    std::string output;
    std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(cmd.c_str(), "r"), pclose);

    if (!pipe) {
        throw std::runtime_error("popen() failed!");
    }

    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        output += buffer.data();
    }

    // 逐行解析输出
    std::istringstream iss(output);
    std::string line;
    while (std::getline(iss, line)) {
        std::istringstream lineStream(line);
        std::string device, state, connection;
        if (std::getline(lineStream, device, ':') &&
            std::getline(lineStream, state, ':') &&
            std::getline(lineStream, connection, ':')) {
            if (device == "wlan0" && state == "connected" && connection == ssid) {
                return true;
            }
        }
    }
    return false;
}

bool scanForExactSSID(const std::string &ssid) {
    FILE *fp = popen("nmcli device wifi list --rescan yes", "r");
    if (!fp) {
        std::cerr << "Failed to execute nmcli scan command." << std::endl;
        return false;
    }

    char buffer[512];
    std::regex ssid_regex(R"((?:\*|\s)\s+([^\s]+(?:\s[^\s]+)*)\s+Infra)"); // 改进的正则表达式，捕获完整的 SSID，包括带有空格的 SSID
    std::smatch match;

    while (fgets(buffer, sizeof(buffer), fp) != nullptr) {
        std::string line(buffer);

        // 使用正则表达式提取 SSID 列
        if (std::regex_search(line, match, ssid_regex)) {
            std::string current_ssid = match[1].str();
            // 去除多余的空格
            current_ssid.erase(0, current_ssid.find_first_not_of(" "));
            current_ssid.erase(current_ssid.find_last_not_of(" ") + 1);

            std::cout << "Detected SSID: " << current_ssid << std::endl;

            // 检查是否与目标 SSID 完全匹配
            if (current_ssid == ssid) {
                pclose(fp);
                return true; // 找到目标 SSID
            }
        }
    }

    pclose(fp);
    return false; // 未找到目标 SSID
}

void stop_ap() {
    std::string interface = "p2p0";  // 固定接口为 p2p0

    std::cout << "正在停止 hostapd..." << std::endl;
    // 关闭hostapd进程
    system("pkill -f hostapd");

    std::cout << "正在将接口切换为管理模式..." << std::endl;
    // 将网络接口设为关闭
    std::string cmd_down = "ip link set " + interface + " down";
    system(cmd_down.c_str());

    // 切换为管理模式
    std::string cmd_set_type = "iw dev " + interface + " set type managed";
    system(cmd_set_type.c_str());

    // 重新启用网络接口
    std::string cmd_up = "ip link set " + interface + " up";
    system(cmd_up.c_str());

    std::cout << "正在重新启用 NetworkManager 管理该接口..." << std::endl;
    // 重新启用NetworkManager管理该接口
    std::string cmd_nmcli = "nmcli dev set " + interface + " managed yes";
    system(cmd_nmcli.c_str());
}


void start_ap() {
    // 相对路径
    std::string relative_script_path = "../../../../service/board_resources/ap_start.sh";

    std::cout << "正在启动 AP..." << std::endl;
    
    // 构造执行脚本的完整命令
    std::string cmd = relative_script_path;  // 直接调用脚本，不加参数
    int result = system(cmd.c_str());

    if (result == 0) {
        std::cout << "AP 启动成功。" << std::endl;
    } else {
        std::cerr << "AP 启动失败，错误码: " << result << std::endl;
    }
}

int wifi_fail(void)
{   
    devReset();
    char header[255] = {0x55,0x55,0x00,0x06};
    
    tcp_client_t.send2server(header,4);

    DEMO_DBG_PRINT("wifi_fail message send!");
    return 0;
}

int ysc_ctrl_wifi_callback(WIFI_CTRL_OPT_e opt, wifi_cfg_info_t *wificfg, char *outMsg, int msgBufSize)
{   
    // 打印 opt 的值
    std::cout << "opt value: " << opt << std::endl;
    
    if (opt == 1) {

        // 检查 wlan0 是否已连接到目标 WiFi
        if (isWlan0ConnectedToSSID(wificfg->ssid)) {
            std::cout << "wlan0 已经连接到 WiFi: " << wificfg->ssid << std::endl;
            return 0;
        }

        stop_ap();
        std::string wifi_cmd;
        int result;

        int retry_count = 0;
        const int max_retries = 3;
        bool found = false;

        while (retry_count < max_retries) {
            std::cout << "Scanning for exact SSID: " << wificfg->ssid << " (attempt " << retry_count + 1 << "/" << max_retries << ")" << std::endl;
            if (scanForExactSSID(wificfg->ssid)) {
                std::cout << "Found exact SSID: " << wificfg->ssid << ". Proceeding to connect..." << std::endl;
                found = true;
                break;
            } else {
                std::cerr << "SSID not found. Retrying scan..." << std::endl;
            }
            sleep(1);
            retry_count++;
        }

        if (!found) {
            std::cerr << "Failed to find exact SSID: " << wificfg->ssid << " after " << max_retries << " attempts." << std::endl;
            start_ap();
            wifi_fail();
            return -1;
        }
        
        // 断开 wlan0 连接
        wifi_cmd = "nmcli device disconnect wlan0";
        result = system(wifi_cmd.c_str());

        if (result == -1) {
            std::cerr << "Failed to execute disconnect command." << std::endl;
        } else {
            int exit_status = WEXITSTATUS(result);
            if (exit_status != 0) {
                std::cerr << "Disconnect command exited with status: " << exit_status << std::endl;
            } else {
                std::cout << "Command executed disconnect successfully." << std::endl;
            }
        }

        // 等待片刻，确保设备已断开
        sleep(2);

        // 构建连接 Wi-Fi 的命令
        wifi_cmd = std::string("nmcli dev wifi connect \"") + wificfg->ssid + "\" password \"" + wificfg->password + "\"";

        std::cout << "wifi cmd: " << wifi_cmd << std::endl;
        result = system(wifi_cmd.c_str());
        start_ap();
        if (result == -1) {
            std::cerr << "Failed to execute connect command." << std::endl;
            return -1;
        } else {
            int exit_status = WEXITSTATUS(result);
            if (exit_status != 0) {
                std::cerr << "Connect command exited with status: " << exit_status << std::endl;
                wifi_fail();
                return -1;
            } else {
                // 验证是否真正连接成功
                if (isWlan0ConnectedToSSID(wificfg->ssid)) {
                    std::cout << "WiFi connected successfully to TestSSID." << std::endl;
                } else {
                    std::cerr << "Command executed successfully, but failed to connect to WiFi." << std::endl;
                    wifi_fail();
                    return -1;
                }
            }
        }

        // 等待 Wi-Fi 连接稳定
        sleep(2);

        // === 修改 network.conf ===

        // 读取整个文件到字符串
        std::ifstream inFile("/mine/robot-application/deeprobots_application_ros1/service/board_resources/network.conf");
        if (!inFile.is_open()) {
            std::cerr << "Failed to open network.conf for reading." << std::endl;
            return -1;
        }

        std::string fileContents((std::istreambuf_iterator<char>(inFile)), std::istreambuf_iterator<char>());
        inFile.close();

        // 检查 "wifiState" 是否已经是 "on"
        if (fileContents.find("\"wifiState\": \"on\"") != std::string::npos) {
            std::cout << "\"wifiState\" is already set to \"on\". No changes needed." << std::endl;
        } else if (fileContents.find("\"wifiState\": \"off\"") != std::string::npos) {
            // 查找并替换 "wifiState": "off" 为 "wifiState": "on"
            size_t pos = fileContents.find("\"wifiState\": \"off\"");
            fileContents.replace(pos, strlen("\"wifiState\": \"off\""), "\"wifiState\": \"on\"");

            // 将修改后的字符串写回文件
            std::ofstream outFile("/mine/robot-application/deeprobots_application_ros1/service/board_resources/network.conf");
            if (!outFile.is_open()) {
                std::cerr << "Failed to open network.conf for writing." << std::endl;
                return -1;
            }

            outFile << fileContents;
            outFile.close();

            // 打印成功修改的提示
            std::cout << "Successfully updated wifiState to 'on' in network.conf." << std::endl;
        } else {
            std::cerr << "\"wifiState\" not found in network.conf." << std::endl;
        }

        // === 修改完成 ===

        // 获取当前路由表信息
        FILE *fp = popen("route -n", "r");
        if (!fp) {
            std::cerr << "Failed to run route command." << std::endl;
            return -1;
        }

        char buffer[1024];
        std::string route_output;
        std::string gateway;
        while (fgets(buffer, sizeof(buffer), fp) != nullptr) {
            route_output += buffer;

            // 提取 wlan0 的网关地址
            if (strstr(buffer, "wlan0") && strstr(buffer, "UG")) {
                std::istringstream iss(buffer);
                std::string destination, gw, genmask, flags, iface;
                iss >> destination >> gw >> genmask >> flags >> iface;
                gateway = gw;
            }
        }
        pclose(fp);

        // 打印初次查询的路由表信息，方便调试
        //std::cout << "Initial Routing Table: \n" << route_output << std::endl;

        // 检查并调整 wlan0 的 metric 值
        /*
        if (!gateway.empty()) {
            std::string delete_cmd = "ip route del default via " + gateway + " dev wlan0";
            std::string add_cmd = "ip route add default via " + gateway + " dev wlan0 metric 60";
            system(delete_cmd.c_str());
            result = system(add_cmd.c_str());

            if (result == -1) {
                std::cerr << "Failed to modify wlan0 metric." << std::endl;
            } 
        } else {
            std::cerr << "Failed to find wlan0 gateway in routing table." << std::endl;
            return -1;
        }
        */
        // 发送调试数据到服务器
        DEMO_DBG_PRINT(" ===== Send Data ===== ");
        char header[4] = {(char)0x55, (char)0x55, (char)0xff, (char)0xff};
        tcp_client_t.send2server(header, 4);

        return 0;
    } else {
        return 0;
    }
}




int ysc_gattServerStart(adl_ble_gatt *gattServer)
{

     DEMO_DBG_PRINT("advData:");
    for (size_t i = 0; i < gattServer->advDataLen; i++)
    {
        printf("%02X ",gattServer->advData[i]);
    }
    printf("\n");

    DEMO_DBG_PRINT("ysc_gattServerStart\n");
    DEMO_DBG_PRINT("gattServer->localName: %s\n",gattServer->localName);
    DEMO_DBG_PRINT("gattServer->scanRespData: %s\n",gattServer->scanRespData);
    DEMO_DBG_PRINT("gattServer->advData: %s\n",gattServer->advData);
    DEMO_DBG_PRINT("gattServer->serviceUUID: %02x\n",gattServer->serviceUUID);
    DEMO_DBG_PRINT("gattServer->characteristicUUID_Down_Write: %02x\n",gattServer->characteristicUUID_Down_Write);
    DEMO_DBG_PRINT("gattServer->characteristicUUID_Up_Notify: %02x\n",gattServer->characteristicUUID_Up_Notify);
    DEMO_DBG_PRINT("gattServer->characteristicUUID_Up_Indicate: %02x\n",gattServer->characteristicUUID_Up_Indicate);
    DEMO_DBG_PRINT("gattServer->timeout: %d\n",gattServer->timeout);

    // 如果已经连接，则不再尝试连接
    if (is_connect) {
        DEMO_DBG_PRINT("Already connected, skipping connection attempt.\n");
        return 0; // 可以根据需要返回适当的值
    }

    is_connect=tcp_client_t.connect_server();
    if(is_connect==true)
    {
        // 计算结构体的总大小
        size_t gattSize = sizeof(adl_ble_gatt);
        char header[4+gattSize] = {0x55,0x55,0x00,0x01};
        
        printf(" ===== Send Data ===== ");
        // 将 gatt 数据追加到 header 后面
        memcpy(header + 4, gattServer, gattSize);
        for (size_t i = 0; i < 4+gattSize; i++) {
            printf("%02X",header[i]);
        }
        printf("\n");
        tcp_client_t.send2server(header,4+sizeof(adl_ble_gatt));
        int length = tcp_client_t.receive();
        std::cout<<"recv string length:"<< length <<std::endl;
        for (size_t i = 0; i < length ; i++) {
            printf("%02X", tcp_client_t.buffer[i]);
        }
        printf("\n");
        if(length == 4)
        {
            DEMO_DBG_PRINT("ggkStart successfully!\n");
            g_ble_init_flag = 1;
            return 0;
        }
    }

    DEMO_DBG_PRINT("tcp server connect failed!\n");
    g_ble_init_flag = 0;
    return -1;
}

int ysc_gattServerStop(void)
{
    char header[255] = {0x55,0x55,0x00,0x05};
    
    tcp_client_t.send2server(header,4);

    DEMO_DBG_PRINT("ysc_gattServerStop");
    return 0;
}

int ysc_gattServerSend(char *data, int datalen)
{
    DEMO_DBG_PRINT("ysc_gattServerSend:%s\n", data);
    char header[255] = {0x55,0x55,0x00,0x02};
    
    // 将 gatt 数据追加到 header 后面
    memcpy(header + 4, data, datalen);

    tcp_client_t.send2server(header,datalen+4);

    return 0;
}


static int s_quit = 0;
/************************************************************************
Description: 启动Andlink SDK
Input: None
Output: None
Return: 成功:0, 失败:-1
Others:
1.通过配置文件或其他方式 设置andlink_init 属性入参;
2.设置andlink_init 回调入参;
3.调用andlink_init 启动andlink SDK.
************************************************************************/
int demo_startAndlinkSdk(int mode, char *ifname)
{
    // 1.设置设备andlink基本信息
    // static adl_dev_attr_t devAttr = { 0 };
    static adl_dev_attr_t devAttr ={
        .deviceType = "2320647",
        .deviceMac = "2CC3E6E3625A"
    };

    // 恢复厂商设备配置文件,一旦恢复失败,重新构造
    if (0 != recoverFacDevinfoCfg(&devAttr))
    {
        DEMO_DBG_PRINT("recoverFacDevinfoCfg failed\n");
        buildFacDevinfoCfgFile(ifname, &devAttr);
    }
    // devAttr.cfgNetMode = mode;
    devAttr.cfgNetMode = (CFG_NET_MODE_e)mode;

    

    DEMO_DBG_PRINT("[enter]demo_startAndlinkSdk,cfgNetMode =%d,ifname =%s\n", devAttr.cfgNetMode, ifname);

    // 2.设置andlink回调接口
    static adl_dev_callback_t devCbs = {
        .scan_wifi_callback = demo_scan_wifi_callback,
        .ctrl_wifi_callback = ysc_ctrl_wifi_callback, //demo_ctrl_wifi_callback
        // .ctrl_wifi_callback = demo_ctrl_wifi_callback, //demo_ctrl_wifi_callback
        .set_led_callback = demo_set_led_callback,
        .set_extfunc_notify_callback = NULL,
        .dn_send_cmd_callback = ysc_dn_send_cmd_callback,
        .dev_paramsSync_callback = demo_dev_paramsSync_callback,
        .download_upgrade_version_callback = NULL,
        //.download_upgrade_version_callback = demo_download_upgrade_version_callback,
        .upgrade_version_callback = demo_upgrade_version_callback,
        .get_device_ipaddr = ysc_get_device_ipaddr_callback,
        .reset_device_Ipaddr = demo_reset_device_Ipaddr_callback,
        .getCfg_callback = NULL,
        .setCfg_callback = NULL,
        .get_dmInfo_callback = demo_get_dmInfo_callback,
        .get_extInfo_callback = demo_get_extInfo_callback,
        .set_voice_notify_callback = NULL,
        .ble_start_server_callback = ysc_gattServerStart,
        .ble_stop_server_callback = ysc_gattServerStop,
        .ble_send_callback = ysc_gattServerSend,
    };

    // 3.检查设备是否联网
    char ipAddr[40] = { 0 };
    char brdAddr[40] = { 0 };

    
    if (0 != check_device_ip_info(ifname, ipAddr, brdAddr))
    {
        DEMO_DBG_PRINT("device has NOT been connected to the network!!!\n");
        demo_set_device_ipaddr_for_callback("", "");
    }
    else
    {
        DEMO_DBG_PRINT("device has been connected to the network!\n");
        demo_set_device_ipaddr_for_callback(ipAddr, brdAddr);
    }


   
    DEMO_DBG_PRINT("\n");
    DEMO_DBG_PRINT("andlink_init========start\r\n");
    DEMO_DBG_PRINT("##############################################################################\n");
    DEMO_DBG_PRINT("andlinksdk demo   version:%s\n", adlDemoVersionGet());
    DEMO_DBG_PRINT("andlinksdk lib    version:%s\n", getAndlinkVersion());
    DEMO_DBG_PRINT("andlinksdk DM lib version:%s\n", getAndlinkVersion());
    DEMO_DBG_PRINT("##############################################################################\n");

    // 4.设置特殊的启动选项
    // 4.1设置andlink单个日志文件阈值;若不设置,默认单个日志文件最大512KB,即andlink诊断功能的日志文件占用空间最大为512KB*2;
    // setAndlinkLogMaxSize(0x80000);

    // 4.2设置andlink 日志文件存储路径;若不设置,默认是存储在/tmp/andlink目录下.
    // setAndlinkLogFilePath("/tmp/andlink1");

    // 4.3调试阶段设置andlink日志级别及输出位置;若不设置,默认日志文件存储在andlink.log中;
    // 强烈注意!!!!!,此接口仅用于测试阶段的调试,将日志输出到控制台,正式版本不可调用.
    // set_printLog_debug_level(255, "terminal");

    // 4.4若是扫码绑定的设备,可以关闭APP发现服务;
    // disableAdlFunc(ADL_APP_SEARCH_SERVICE);

    // 4.5若存在离线解绑后,需要立马绑定的场景时,可以禁止自动默认用户注册功能;
    // disableAdlFunc(ADL_OFFLINE_UNBIND_AUTO_REBOOTSTRAP);

    // 4.6若设备在本地网关环境中,需要支持本地局域网内的管控和联动,可以禁止本地网关环境的云接入功能;
    // disableAdlFunc(ADL_CLOUD_ACCESS_IN_LOCAL_GW_ENV);

    // 4.7若是可视门锁、守护台灯等设备,使用扫码配网,可以调用下列接口禁止闪联配网和手工SoftAP或BLE配网
    disableAdlFunc(ADL_FLASHLINK_CONFIG_NET);
    // disableAdlFunc(ADL_MANUAL_CONFIG_NET);

    // 4.8若是电池设备(低功耗设备),必须调用此接口,禁止手工配网优先功能,此时设备上电默认会执行闪联配网,若闪联失败,则会执行手工配网功能.
    // disableAdlFunc(ADL_FIRST_MANUAL_CONFIGNET);

// 用于SDK开发者自己调试,SDK使用者无需关心
#ifdef DEMO_TESTENV_DGS_ENABLE
// 测试环境周期诊断服务器地址,SDK使用者一般无需关心
#define ADL_CYC_DGS_SERVER "http://************:8085/dev/anddgs/chkdgs"
    setAdlDgsTestServUrl(ADL_CYC_DGS_SERVER);
#endif

    // 4.8设置OTA 文件下载路径及文件名称
    // setOtaStoragePathAndFilename("/tmp", "ota.zip");
    // 4.9设置OTA下载策略;1分片下载;0整包下载
    // setOtaDownloadPolicy(1, 2*1024*1024);

    /*
    5.启动andlink SDK(立即返回,内部不会阻塞)
    注意:sdk不会申请内存存储devAttr和devCbs,只会保存其指针;
    因此入参所需要的空间需要调用者申请且SDK运行期间不能释放这两个指针;假设用户需要调用andlink_destroy使SDK消亡时,需要释放这两个指针;
    */
    return andlink_init(&devAttr, &devCbs);
}

#ifdef AHM_DISCOVERY_ENABLE // 设备互联SDK
// 能力ID 需要从andlink门户申请
#define ABILITY_ID_DEGC 0x10086011
#define ABILITY_ID_HUMI 0x10086022

// 启动设备互联SDK
int demo_startAndlinkCsSdk()
{
    // 创建两个能力模型
    adl_cs_setModel(0, "degC", ABILITY_ID_DEGC);
    adl_cs_setModel(1, "relativeHumidity", ABILITY_ID_HUMI);

    // 注册两个回调接口
    set_andlink_callback(CB_GET_CS_PORT, demo_cs_get_port);
    set_andlink_callback(CB_GET_CS_VALUE, demo_cs_get_value);

    return 0;
}
#endif

// 方式1:主进程中启动Andlink
int demo_main(int argc, char **argv)
{
    int mode = -1;
    char *ifname = NULL;

    /************ 1.设置联网模式和网口名称 ************/
    if (argc >= 3)
    {
        if (0 == strcmp(argv[1], "wireless"))
        {
            // 测试: LD_LIBRARY_PATH=../lib ./demo wireless wlan0
            mode = NETWOKR_MODE_WIFI;
            ifname = argv[2];
        }
        else if (0 == strcmp(argv[1], "wired"))
        {
            // 测试: LD_LIBRARY_PATH=../lib ./demo wired ens33
            mode = NETWOKR_MODE_WIRED;
            ifname = argv[2];
        }
        else
        {
            // 测试: LD_LIBRARY_PATH=../lib ./demo $(1) $(2)
            // $(1)取值:1|2|3|4|5|6|7|...,1-wired; 2-wireless; 3-4G; 4-BLE; 5-ScanCode; 6-others; 7-wiredScreens;
            // $(2)取值:wlan0|eth0|ens33|...
            mode = atoi(argv[1]);
            ifname = argv[2];
        }
    }
    else
    {
        // 测试: LD_LIBRARY_PATH=../lib ./demo
        // 默认无线启动,网口名称为IF_NAME
        mode = NETWOKR_MODE_WIFI;
        ifname = IF_NAME;
    }

    strcpy(g_ifname,ifname);

    /************ 2.加载Andlink SDK ************/
    if (0 != demo_startAndlinkSdk(mode, ifname))
    {
        DEMO_DBG_PRINT("demo_startAndlinkSdk failed\r\n");
    }    

//     /************ 2.加载设备互联 SDK ************ 供使用设备互联SDK的厂商使用, 一般无需关心 */
// #ifdef AHM_DISCOVERY_ENABLE
//     if (0 != demo_startAndlinkCsSdk())
//     {
//         DEMO_DBG_PRINT("demo_startAndlinkCsSdk failed\r\n");
//     }
// #endif


    return 0;
}

// 方式2:子进程中启动Andlink
int child_main(int argc, char **argv)
{
    // void adl_open_http_func();
    // void adl_close_http_func();
    pid_t pid;

    // adl_open_http_func();

    pid = fork();
    if (pid < 0)
    {
        DEMO_DBG_PRINT("Failed to create new process!\n");
        exit(1);
    }
    if (pid > 0)
    {
        // father
        // adl_open_http_func();
        exit(0);
    }
    else
    {
        // child
        demo_main(argc, argv);
    }
    
    // DEMO_DBG_PRINT("after");
    while (!s_quit)
    {
        sleep(5);
    }
}








int main(int argc, char **argv)
{
    /*
    说明:
    ANDLINK_DEBUG_ENABLE取值如下:
    0, 正式版本,For SDK使用者;
    非0,调试版本,For SDK开发者,SDK使用者无需关心.
    */

#ifndef ANDLINK_DEBUG_ENABLE
#define ANDLINK_DEBUG_ENABLE 0
#endif
    DEMO_DBG_PRINT("MAIN TEST\n");
    DEMO_DBG_PRINT("enter dev main,ANDLINK_DEBUG_ENABLE =%d!\n", ANDLINK_DEBUG_ENABLE);
    if (argc < 3)
    {
        DEMO_DBG_PRINT("Usage: cd bin;LD_LIBRARY_PATH=../lib %s wired ens33\n", argv[0]);
        DEMO_DBG_PRINT("Usage: cd bin;LD_LIBRARY_PATH=../lib %s 1 ens33\n\n", argv[0]);

        DEMO_DBG_PRINT("or\n");
        DEMO_DBG_PRINT("Usage: cd bin;LD_LIBRARY_PATH=../lib %s wireless wlan0\n", argv[0]);
        DEMO_DBG_PRINT("Usage: cd bin;LD_LIBRARY_PATH=../lib %s 2 wlan0\n\n", argv[0]);

        DEMO_DBG_PRINT("or\n");
        DEMO_DBG_PRINT("Usage: cd bin;LD_LIBRARY_PATH=../lib %s $(1) $(2)\n", argv[0]);
        DEMO_DBG_PRINT("$(1)=1|2|3|4|5|6|7|,1-wired; 2-wireless; 3-4G; 4-BLE; 5-ScanCode; 6-others; 7-wiredScreens\n");
        DEMO_DBG_PRINT("$(2)=wlan0|eth0|ens33|...\n\n");
        return -1;
    }

#if ANDLINK_DEBUG_ENABLE == 0
#if 1
    // // 0.1 主进程中直接启动andlink
    // demo_main(argc, argv);
#else
    // 0.2 子进程中启动andlink
    child_main(argc, argv);
#endif
    
    std::thread myThread(demo_main,argc, argv);

    // 等待线程完成
    myThread.join();

    while(!is_connect)
    {
        sleep(0.5);
    }
    sleep(2);
    std::cout<<"enter recv"<<std::endl;
    while (!s_quit)
    {
        if(g_ble_init_flag)
        {
            int length = tcp_client_t.receive();
            if(length !=-1)
            {
                std::cout<<"recv string length:"<< length <<std::endl;
                std::stringstream ss;
                ss << std::hex << std::setfill('0');  // 设置流格式为16进制并填充0
                for (size_t i = 0; i < length; ++i) {
                    ss << std::setw(2) << static_cast<int>(tcp_client_t.buffer[i]);  // 每个字节转换为16进制，并保证两位宽度
                }
                std::cout << "Hexadecimal representation: " << ss.str() << std::endl;
                // for (size_t i = 0; i < length ; i++) {
                //     // std::cout<<tcp_client_t.buffer[i];
                //     printf("%c",tcp_client_t.buffer[i]);
                // }
                // printf("\n");
                // std::cout<<std::endl;
                // std::string str(reinterpret_cast<const char*>(tcp_client_t.buffer ), length);
                // std::cout<<"recv string :"<< str <<std::endl;
                // for (size_t i = 0; i < length ; i++) {
                //     std::cout<<tcp_client_t.buffer[i];
                // }
                // std::cout<<std::endl;
                // for (size_t i = 0; i < length ; i++) {
                //     DEMO_DBG_PRINT("%02X", tcp_client_t.buffer[i]);
                // }
                // DEMO_DBG_PRINT("\n");
                // int length = strlen((char *)temp);
                int ret = adlBleRecvHandler(tcp_client_t.buffer,length);
            }
        }
        // sleep(5);
    }
#else
    // x.x SDK开发者调试andlink,SDK的使用者无需关心
    int debug_demo_main(int argc, char **argv);
    debug_demo_main(argc, argv);
#endif // ANDLINK_DEBUG_ENABLE

    return 0;
}
