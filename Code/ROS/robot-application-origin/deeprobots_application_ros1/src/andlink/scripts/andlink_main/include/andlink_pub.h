/*
 * Copyright (c) 2019,中移(杭州)信息技术有限公司
 * All rights reserved.
 *
 * 文件名称：andlink_pub.h
 * 说 明：andlinkSDK 对外接口信息
 *        !!!强烈注意:此文件由SDK维护,厂商只可引用,不可修改.!!!
 * 初始版本：1.0
 * 作 者：罗武通
 * 完成日期：2019年4月29日
 *
 * 取代版本：1.4
 * 原作者 ：wuhao
 * 完成日期：2020年02月01日
 *
 * 取代版本：1.6
 * 原作者 ：wuhao
 * 完成日期：2023年07月31日
 *
 * 取代版本：1.6.5
 * 原作者 ：wuhao
 * 完成日期：2023年09月01日
 *
 * 取代版本：1.6.6
 * 原作者 ：wuhao
 * 完成日期：2023年10月20日
 *
 * 取代版本：1.6.7
 * 原作者 ：wuhao
 * 完成日期：2024年02月07日
 *
 * 取代版本：1.7.1
 * 原作者 ：wuhao
 * 完成日期：2024年05月17日
 * 接口修改:
 * 1.新增了BLE配网相关接口.
 * 2.新增了配网语音通知接口.
 *
 * 取代版本：1.7.2
 * 原作者 ：wuhao
 * 完成日期：2024年07月17日
 * 接口修改:
 * 1.修改了Wi-Fi扫描回调接口scan_wifi_callback出参.
 * 2.对外提供了禁止闪联及手工配网的接口.
 */

#ifndef __ANDLINK_PUB_H
#define __ANDLINK_PUB_H

#include "cJSON.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
启动的SOFT AP要求满足:
热点名称:CMQLINK-{$deviceType}-****,devieType 为设备在开发者门户申请的设备类型码;****为设备产生的随机四位码，一般是 MAC 地址后缀
通道网络配置:支持 DHCP,设备地址为 *************
通道热点认证方式:广播、 开放式接入
*/

// SOFT AP 默认地址;AP_IP_ADDR,支持DHCP
#define AP_IP_ADDR "*************"

// 设备属性相关的宏
#define MAX_FUNCSTR_LEN       32  // 管控命令function长度
#define MAX_DEV_ID_LEN        64  // 设备ID长度
#define MAX_SEQID_LEN         32  // 管控流水号长度
#define MAX_GW_TOKEN_LEN      32  // andlinkGWToken长度
#define MAX_ANDLINK_TOKEN_LEN 32  // andlinkToken长度
#define MAX_TOKEN_LEN         32  // productToken长度
#define MAX_DEV_TOKEN_LEN     512 // 一机一密的productToken长度
#define MAX_DEV_EXT_LEN       512 // 设备扩展信息最大长度
#define MAX_CMDDATA_LEN       512 // 数据上报或管控数据的最大长度
#define MAX_DM_TOKEN_LEN      128 // dmToken最大长度

#define MAX_AUTH_ID_LEN   32   // authID最大长度
#define MAX_AUTH_KEY_LEN  128  // authKEY最大长度
#define MAX_AUTH_CODE_LEN 1024 // authCode最大长度

// wifi控制相关的宏
#define MAX_SSID_LEN    32
#define MAX_PASSWD_LEN  64
#define MAX_ENCRYPT_LEN 16
#define MAX_MAC_LEN     32
#define MAX_RSSI_LEN    8

// WiFi控制选项
typedef enum
{
    WIFI_OPT_STA_START = 1, // 表示打开STA模式
    WIFI_OPT_STA_STOP = 2,  // 表示关闭STA模式
    WIFI_OPT_AP_START = 3,  // 表示打开AP模式
    WIFI_OPT_AP_STOP = 4,   // 表示关闭AP模式
    WIFI_OPT_NET_STORE = 5, // 表示存储网络信息
    WIFI_OPT_NET_CLEAR = 6, // 表示清除网络信息
    WIFI_OPT_NET_GET = 7    // 表示获取网络信息
} WIFI_CTRL_OPT_e;

// 产品所处模式,SDK调用者一般无需关心研发者调试模式
typedef enum
{
    PRODUCTION_RUNNING_MODE = 0, // 生产运行模式
    FACTORY_TEST_MODE,           // 工厂测试模式
} PRODUCT_MODE_e;

// 配网模式
typedef enum
{
    NETWOKR_MODE_WIRED = 0x01, // 表示有线配网设备
    NETWOKR_MODE_WIFI = 0x02,  // 表示WI-FI配网设备
    NETWOKR_MODE_4G,           // 表示4G配网设备
    NETWOKR_MODE_BT,           // 表示蓝牙配网设备
    NETWOKR_MODE_SCAN_CODE,    // 表示扫码配网设备
    NETWOKR_MODE_OTHERS,       // 表示其他配网设备
    NETWOKR_MODE_WIRED_SCREEN, // 表示有线带屏配网设备
    NETWOKR_MODE_MAX
} CFG_NET_MODE_e;

// 下行控制命令响应模式
typedef enum
{
    NORSP_MODE = 0, // 无需响应
    ASYNC_MODE = 1, // 异步响应,采用devDataReport进行响应
    SYNC_MODE = 2,  // 同步响应,下行管控函数的输出参数进行响应,一般用户设备接入本地网关,不常用
} RESP_MODE_e;

// 设备的andlink核心状态
typedef enum
{
    ADL_INIT,
    ADL_CFGNET,                // 配网状态
    ADL_CFGNET_NETINFO,        // 配网状态 收到工作热点信息220816
    ADL_CFGNET_REJECT_NETINFO, // 配网状态 收到拒绝入网信息230901
    ADL_CFGNET_SUC,            // 配网成功状态
    ADL_CFGNET_FAIL,           // 配网失败状态
    ADL_BOOTSTRAP,             // 设备开始注册状态
    ADL_BOOTSTRAP_SUC,         // 设备注册成功状态
    ADL_BOOTSTRAP_FAIL,        // 设备注册失败状态
    ADL_BOOT,                  // 设备开始上线状态
    ADL_BOOT_SUC,              // 设备上线成功状态
    ADL_BOOT_FAIL,             // 设备上线失败状态
    ADL_ONLINE,                // 设备在线状态
    ADL_OFFLINE,               // 设备离线状态
    ADL_REBIND,                // 设备重新绑定 by 22.06.13 有线设备以此为时机,用于实现绑定失败的逻辑
    ADL_BOUND,                 // 设备完成绑定 by 23.10.20
    ADL_STATE_MAX
} ADL_DEV_STATE_e;

// Andlink扩展功能提示
typedef enum
{
    ADL_LOG_SYNC,     // 可选, 日志同步, 设备可以根据需要进行andlink日志文件的备份;
    ADL_ERRNO_GET,    // 可选, 错误码获取, 设备可以根据需要获取andlink运行的错误码;
    ADL_POPUP_WINDOW, // 可选，有线带屏设备必须设置，有线带屏设备的屏幕弹窗，提醒用户;
    ADL_EXT_NOTIFY_MAX
} ADL_DEV_EXTFUNC_NOTIFY_e;

// 设备语音提示
typedef enum
{
    ADL_NET_START_CONNECTING,     // 开始配网认证中,请打开和家亲等待
    ADL_NET_SCAN_WIFI_FAILED,     // 扫描引导热点失败,请扫码配网 not use
    ADL_NET_GUIDE_RSSI_FAILED,    // 周围引导热点信号较差,请靠近路由器或有线连接路由求后,并重启设备 not use
    ADL_NET_GUIDE_AP_CONN_FAILED, // 连接引导热点失败,请扫码配网
    ADL_NET_WORK_AP_CONN_FAILED,  // 连接工作热点失败,请扫码配网
    ADL_NET_GUIDE_AP_SUC,         // 连接引导热点成功,请在和家亲确认
    ADL_NET_WORK_AP_SUC,          // 已成功连接网络,正在尝试连接服务器
    ADL_VOICE_NOTIFY_MAX
} ADL_DEV_VOICE_NOTIFY_e;

// 设备对外开放查询的属性信息
typedef enum
{
    ADL_AUTH_CODE,       // 获取一机一密生成的设备工作秘钥;
    ADL_AUTH_DEVICE_ID,  // 获取一机一密生成的设备唯一ID;
    ADL_USER_KEY,        // 获取userkey,默认值为CMCC-${deviceVendor}-${deviceType}
    ADL_BIND_STATUS,     // 获取设备的绑定状态,1,绑定;非1,为非绑定;
    ADL_MONITOR_USER_ID, // 获取安防类设备的安防平台userid;for单独无感配网的SDK
    ADL_AUTH_MODE,       // 获取SDK是否支持一机一密,1支持
    ADL_DEV_ATTR_MAX
} EXPORT_DEVICE_ATTRS_e;

// ANDLINK默认开启的功能点,取值1,2,4,8,16...
typedef enum
{
    // 最多32个元素,每个元素表示一个功能
    ADL_APP_SEARCH_SERVICE = 0x01,              // APP发现服务;
    ADL_OFFLINE_UNBIND_AUTO_REBOOTSTRAP = 0x02, // 离线解绑再次自动注册功能
    ADL_CLOUD_ACCESS_IN_LOCAL_GW_ENV = 0x04,    // 本地网关环境下的云接入功能
    ADL_FLASHLINK_CONFIG_NET = 0x08,            // 闪联配网功能
    ADL_MANUAL_CONFIG_NET = 0x10,               // 手工配网功能(SoftAP配网或BLE配网)
    ADL_FIRST_MANUAL_CONFIGNET = 0x20,          // 手工配网优先功能(对于常电的WiFi设备,默认手工配网优先;若是电池设备,可以关闭手工配网优先功能)
    ADL_DEF_EN_FUNC_MAX = 0x80000000
} ADL_DEF_ENABLED_FUNCS_e;

// ANDLINK默认关闭的功能点,取值1,2,4,8,16...
typedef enum
{
    // 最多32个元素,每个元素表示一个功能
    ADL_DNS_CACHE = 0x01, // DNS缓存;
    ADL_DEF_DIS_FUNC_MAX = 0x80000000
} ADL_DEF_DISABLED_FUNCS_e;

#ifdef SDK_USING_DEVICE_FUNCS_ENABLE
// device的功能点
typedef enum
{
    // 最多32个元素,每个元素表示一个功能
    DEVICE_WIFI_SENSOR_ENABLE = 0x01, // wifi感知使能;
    DEVICE_XX1 = 0x02,
    DEVICE_XX2 = 0x04
} ADL_DEVICE_FUNCS_e;
#endif

// wifi控制接口
typedef struct
{
    char ssid[MAX_SSID_LEN];
    char password[MAX_PASSWD_LEN];
    char encrypt[MAX_ENCRYPT_LEN];
    int channel;
    int type;                // 热点类型,0是工作热点;1是配网引导热点
    char mac[MAX_MAC_LEN];   // 若strlen(mac)不为0,说明Bssid唯一,即连接指定热点;
    char rssi[MAX_RSSI_LEN]; // 若strlen(rssi)为0,说明是输出参数,连接热点成功后,输出该热点的信号强度(可选).
} wifi_cfg_info_t;

// 下行控制指令帧结构
typedef struct
{
    char function[MAX_FUNCSTR_LEN];
    char deviceId[MAX_DEV_ID_LEN];
    char childDeviceId[MAX_DEV_ID_LEN];
    char seqId[MAX_SEQID_LEN];
    unsigned int dataLen;
    char *data;
} dn_dev_ctrl_frame_t;

// 设备属性
typedef struct
{
    CFG_NET_MODE_e cfgNetMode; // 配网方式;
    char *deviceVendor;        // 厂商名称,使用简单的英文或拼音表示;
    char *deviceType;          // 设备厂商在连楹家庭智慧平台门户注册的产品的产品类型码;即连楹家庭智慧平台门户上创建完产品后,生成的 产品ID;
    char *deviceMac;           // 厂商可以填mac或sn,即设备唯一标识
    char *andlinkToken;        // 平台对接 非必选;设备厂商在连楹家庭智慧平台门户注册的产品的平台验证码
    char *productToken;        // 平台对接 非必选;设备厂商在连楹家庭智慧平台门户注册的产品的产品验证码
    char *firmWareVersion;     // 设备固件版本号
    char *softWareVersion;     // 设备软件版本号
    char *cfgPath;             // andlink配置文件存储路径,此路径需要可读写,断电不丢失;
    char *extInfo;             // 扩展信息获取,预留,暂不使用.
} adl_dev_attr_t;

// BLE 配网:btgatt-server结构
typedef struct
{
    unsigned char localName[16];                   // BLE外设名称
    unsigned char localNameLen;                    // BLE外设名称 长度
    unsigned char scanRespData[16];                // BLE扫描请求的响应内容
    unsigned char scanRespDataLen;                 // BLE扫描请求的响应内容 长度
    unsigned char advData[31];                     // BLE部分广播内容,仅包含发现模式和厂商数据2个属性(共12B)
    unsigned char advDataLen;                      // BLE部分广播内容 长度
    unsigned short serviceUUID;                    // 固定,上行数据和下行数据都使用该Service UUID
    unsigned short characteristicUUID_Down_Write;  // APP->设备数据帧
    unsigned short characteristicUUID_Up_Notify;   // 设备->APP状态通知
    unsigned short characteristicUUID_Up_Indicate; // 设备->APP指示
    unsigned short duration;                       // BLE广播发送持续时间,单位秒
    unsigned short timeout;                        // BLE广播发送间隔,单位秒
} adl_ble_gatt;

// 回调接口,用户注册、实现,杭研调用
typedef struct
{
    // 按需实现;扫描指定热点列表
    int (*scan_wifi_callback)(wifi_cfg_info_t *wificfg, cJSON *outApMsg);

    // 按需实现;控制WIFI(连接、断开热点;启动、关闭热点;保存、清除热点配置);
    int (*ctrl_wifi_callback)(WIFI_CTRL_OPT_e opt, wifi_cfg_info_t *wificfg, char *outMsg, unsigned int msgBufSize);

    // 按需实现;通知设备Andlink核心状态
    int (*set_led_callback)(ADL_DEV_STATE_e state);

    // 按需实现;Andlink扩展功能提示,通知设备执行某些特殊操作
    int (*set_extfunc_notify_callback)(ADL_DEV_EXTFUNC_NOTIFY_e type);

    // 按需实现,平台对接的设备不必实现;下行管控;
    int (*dn_send_cmd_callback)(RESP_MODE_e mode, dn_dev_ctrl_frame_t *ctrlFrame, char *eventType, char *respData, unsigned int respBufSize);

    // 按需实现,平台对接的设备不必实现;
    // 通知设备参数同步;sdk上线成功后调用;
    int (*dev_paramsSync_callback)();

    // 按需实现;平台接入的设备不必实现; 若需要支持andlink OTA,此接口实现是可选的,若没实现此接口,则SDK内部实现下载.
    // OTA方案1:下载并升级版本; childDevId为空表示父设备,否则表示子设备;
    int (*download_upgrade_version_callback)(char *childDevId, char *downloadurl, char *filetype, char *md5, unsigned int filesize);

    // 按需实现;平台接入的设备不必实现;若需要支持andlink OTA,此接口必须实现.
    // OTA方案2:升级版本; childDevId为空表示父设备,否则表示子设备;
    int (*upgrade_version_callback)(char *childDevId, char *filename, char *filetype);

    // 必须实现;获取设备IP;sdk以此判断设备是否联网;SDK调用时,两个参数分配的缓存长度为46B
    int (*get_device_ipaddr)(char *ip, char *broadAddr);
    // 按需实现;复位设备IP;平台对接的设备不必实现;
    int (*reset_device_Ipaddr)(void);

    // 按需实现; 若不实现此接口,sdk使用内部实现的读配置项接口;
    // 读配置项的接口; 举例getCfg("deviceId", a, sizeof(a)-1);
    int (*getCfg_callback)(char *item, char *value, unsigned int bufsize);

    // 按需实现; 若不实现此接口,sdk使用内部实现的写配置项接口;
    // 写配置项的接口;举例SetCfg("deviceId","CMCC-10086-666677778888");
    int (*setCfg_callback)(char *item, char *value);

    // 必须实现;DM信息获取;add by 21.02.19
    int (*get_dmInfo_callback)(char *childDeviceId, cJSON *root);

    // 必须实现;Andlink动态扩展信息获取;add by 23.03.03;代替adl_dev_attr_t中的extInfo字段
    int (*get_extInfo_callback)(char *childDeviceId, cJSON *root);

    // 按需实现;通知设备语音提示
    int (*set_voice_notify_callback)(ADL_DEV_VOICE_NOTIFY_e type);

    // 按需实现;通知设备启动BLE配网
    int (*ble_start_server_callback)(adl_ble_gatt *gattServer);

    // 按需实现;通知设备停止BLE配网
    int (*ble_stop_server_callback)(void);

    // 按需实现;通知设备发送BLE数据
    int (*ble_send_callback)(char *data, unsigned int datalen);

} adl_dev_callback_t;

// 必须调用; sdk 启动(sdk中devAttr,devCbs增加字段,不支持向上兼容,故sdk此接口变化导致的升级,应用程序也需要跟着升级)
int andlink_init(adl_dev_attr_t *devAttr, adl_dev_callback_t *devCbs);

// 设备复位,有恢复出厂或强制复位机制时,必须调用;
int devReset(void);

// 用户按键开启SoftAP配网时,必须调用;
int openAPCfgNet();

// 查询设备相关属性的接口;按需调用;
char *getDeviceInfoStr(EXPORT_DEVICE_ATTRS_e attr);

// 按需调用,平台对接的设备不会调用;子设备DM数据上报接口;注意token的内容从childDevBootstrap接口的出参outAndlinkToken获取; add by 22.09.26
int childDevDmReport(char *childDeviceId, cJSON *root, char *token);

// 按需调用;记录日志
// fid:0表示SDK,1表示设备
// logLevel: 0,不输出日志;1：单次关键日志，2：错误日志，4：告警1级，8：告警2级，16：普通日志，32：周期1级；64:周期2级;
int printLog(int fid, int logLevel, const char *fmt, ...);

// 设置printLog日志接口记录日志的级别;
// logLevel=0-NO output; =1-ONCE; =2-ERROR;  =4-ALARM_1;  =8-ALARM_2; =16-INFO; =32-CYC_1;=64-CYC_2;
// if ONCE+ERROR+ALARM_1+ALARM_2+INFO, then logLevel=31;
// logTo="file" 表示记录到文件;="terminal"表示记录到控制台
// 强烈注意,此接口仅用于厂商调试阶段;正式版本使用,会影响andlink的诊断功能,导致入库验收通过不了.
int set_printLog_debug_level(int logLevel, char *logTo);

// 获取andlink SDK运行过程中的错误码,类似linux系统的errno机制,用于SDK使用者诊断andlink相关的问题,具体错误码的含义可以在FAQ中搜索.
int adlGetErrno(void);

// 测试接口,一般无需调用;设置工作环境，默认是线上环境，可以通过此接口设置为测试环境
int setTestEnvFlag(int flag);
int getTestEnvFlag();

// 测试接口,一般无需调用;用于设置测试环境云网关服务器地址和诊断服务器地址
int setAdlDgsTestServUrl(char *url);
int setAdlCgwTestServUrl(char *url);

// 获取产品模式,取值见:PRODUCT_MODE_e
int getProductMode();

// 设置配网确权等级,1表示免确权,2表示强确权
int setCfgNetAuthLevel(unsigned int level);

// 按需调用;设置andlink单个日志文件最大阈值,默认0x80000,即500KB
int setAndlinkLogMaxSize(unsigned int size);

// 按需调用;设置andlink日志存储路径,若不设置,默认存储在/tmp/andlink
int setAndlinkLogFilePath(char *filepath);

// 按需调用;平台接入获取DM版本号
char *ca_getAndDmVersion();

// 按需调用;获取Andlink SDK版本号
char *getAndlinkVersion();

// 按需调用;禁止andlink默认开启的某些功能,funcPos可以是ADL_FUNCS_e中元素的组合
int disableAdlFunc(ADL_DEF_ENABLED_FUNCS_e funcPos);

// 按需调用;使能andlink默认关闭的某些功能,funcPos可以是ADL_FUNCS_e中元素的组合
int enableAdlFunc(ADL_DEF_DISABLED_FUNCS_e funcPos);

// 按需调用;设置OTA升级文件存储路径及文件名;参数1举例/tmp;参数2举例xxx.bin
int setOtaStoragePathAndFilename(char *path, char *filename);

// 按需调用;设置OTA升级文件下载策略;mode,0是整包下载;1是分包下载;onePackageSize,分包下载时,设备支持的最大单包下载大小,单位字节
// 没有实现download_upgrade_version_callback时,需要设置此接口
int setOtaDownloadPolicy(char mode, unsigned int onePackageSize);

// 按需调用;设备接入,查询andlink SDK版本号,也是DM版本号;
char *getAndlinkVersion();

// 按需调用,平台对接的设备不会调用;数据异步上报(返回值无法说明消息是否发送成功); modify  by 22.09.21,不再支持子设备数据上报;
// 删除入参childDevId;添加入参timestamp毫秒时间戳,根据需要填事件发生的时间戳或事件上报的时间戳 modify by 22.10.19
int devDataReport(char *eventType, char *seqId, unsigned long long timestamp, char *data, unsigned int dataLen);

// 按需调用,平台对接的设备不会调用;数据同步上报(内部阻塞等待,返回数据上报成功或失败的结果);add by 23.08.07
// 若需要支持离线续传的事件,采用此接口进行数据上报.
int devDataSyncReport(char *eventType, char *seqId, unsigned long long timestamp, char *data, unsigned int dataLen);

// 按需调用,平台对接的设备不会调用;本地局域网数据上报,默认上报到中国移动生态的路由器; 新增childDevType参数,update by 22.09.21
int local_devDataReport(char *childDevId, char *childDevType, char *eventType, char *seqId, char *data, unsigned int dataLen);

// 按需调用;一般只有扫码绑定的设备对接场景才会使用;
// 若设备走扫码绑定,扫码成功后,调用此接口通知Andlink SDK 用户ID和云网关url(gwAddress2),后面两个参数都可以填NULL
int setScanCodeBindConfigInfo(char *userKey, char *gwAddress, char *gwAddress2);

// 按需调用;子设备注册 token最大长度:MAX_TOKEN_LEN
int childDevBootstrap(char *childDevId, char *childDevType, char *childPdtToken, char *outDeviceToken, char *outAndlinkToken);

// 按需调用;子设备上线
int childDevBoot(char *childDevId, char *childDevType, char *swVersion, char *fmVersion);

// 按需调用,平台对接的设备不会调用;子设备数据上报接口 add by 22.09.21
// modify by 22.10.19  ;添加入参timestamp毫秒时间戳,根据需要填事件发生的时间戳或事件上报的时间戳
int childDevDataReport(char *childDevId, char *childDevType, char *eventType, char *seqId, unsigned long long timestamp, char *data, unsigned int dataLen);

// 按需调用，根据设备的情况，设置wlan名称，比如wlan0，或其他名称。
int setWiFiName(char *WiFiName);

// 蓝牙数据接收处理器
int adlBleRecvHandler(unsigned char *data, unsigned int len);

#ifdef SDK_USING_DEVICE_FUNCS_ENABLE
// 按需调用;设置设备的功能点,funcPos可以是ADL_DEVICE_FUNCS_e元素中的组合
int adl_setDevFunc(ADL_DEVICE_FUNCS_e funcPos);
#endif

#ifdef AHM_DISCOVERY_ENABLE // 设备互联,not used

// 能力个数
#define MAX_CAPABILITY_OBJ 8

// 新增回调类型
typedef enum
{
    CB_GET_CS_PORT,  // 获取能力端口;
    CB_GET_CS_VALUE, // 获取能力值;
    CB_MAX
} ADL_CALLBACK_TYPE_e;

// 新增回调接口
typedef struct
{
    // 获取能力端口
    int (*cs_get_port_callback)(int csIndex, int *csPort);

    // 获取能力值
    int (*cs_get_value_callback)(int csIndex, char *csValue, int csValueBufSize);

} adl_dev_newcallback_t;

// 设置能力模型:Capability SETS
int adl_cs_setModel(int csIndex, char *csName, int csID);

// 注册回调接口
int set_andlink_callback(ADL_CALLBACK_TYPE_e cbType, void *cbHandler);

#endif // end AHM_DISCOVERY_ENABLE

#ifdef __cplusplus
}
#endif

#endif
