﻿DEMO_ROOT_DIR=$(shell pwd)
USER_ADAPT_DIR=$(DEMO_ROOT_DIR)/adapt
COMPILE_SERVER_OS_BITS=$(shell uname -m)

#*****************************************************#
#注意以下n个符号,厂商进行二次编译时,可在run.sh脚本中指定;也可以通过删掉下列符号前的#来指定;

#编译主程序时,是否静态链接外部库;y表示静态链接;n或空表示动态链接;
#MAIN_APP_LINK_EXTERN_LIBS_STATIC=n

#设备互联SDK使能
#AHM_DISCOVERY_ENABLE=y

#是否开启测试环境的诊断功能
#DEMO_TESTENV_DGS_ENABLE=y

#define ANDLINK_DEV_ACCESS_LINK_ENABLE   1   #设备接入必须定义. 定义此宏,表示是设备接入,否则是平台接入.
#define APP_USING_ANDLINK_CJSON   1
#*****************************************************#

HOST_ARCH := $(shell uname -m)

$(info Current system architecture: $(HOST_ARCH))

ifeq ($(HOST_ARCH), x86_64)
	DEMO_X86_ENABLE=y
# else ifeq ($(HOST_ARCH), armv7l)
# else ifeq ($(HOST_ARCH), aarch64)
endif


#目标程序名称
APP=dev_andlink
ifeq ($(strip $(ANDLINK_DEV_ACCESS_LINK_ENABLE)),n)
	APP=cloud_andlink
endif
#依赖库
EXTERN_LIBS =


ifeq ($(strip $(CMDC_DM_LIB_ENABLE)),y)
	DEMO_DEFINES += -DCMDC_DM_LIB_ENABLE
endif

ifeq ($(strip $(ANDLINK_DEV_ACCESS_LINK_ENABLE)),y)
	DEMO_DEFINES += -DDEMO_DEV_ACCESS_ENABLE
endif

ifeq ($(strip $(ANDLINK_DEV_ACCESS_LINK_ENABLE)),n)
	DEMO_DEFINES += -DDEMO_CLOUD_ACCESS_ENABLE
endif

ifeq ($(strip $(APP_USING_ANDLINK_CJSON)),y)
	DEMO_DEFINES += -DAPP_USING_ANDLINK_CJSON
endif

ifeq ($(strip $(AHM_DISCOVERY_ENABLE)),y)
	DEMO_DEFINES += -DAHM_DISCOVERY_ENABLE
endif

ifneq ($(strip $(ANDLINK_DEBUG_ENABLE)),)
	DEMO_DEFINES += -DANDLINK_DEBUG_ENABLE=$(ANDLINK_DEBUG_ENABLE)
else
	DEMO_DEFINES += -DANDLINK_DEBUG_ENABLE=0
endif

ifeq ($(strip $(DEMO_TESTENV_DGS_ENABLE)),y)
	DEMO_DEFINES += -DDEMO_TESTENV_DGS_ENABLE
endif

ifeq ($(strip $(SDK_USING_DEVICE_FUNCS_ENABLE)),y)
	DEMO_DEFINES += -DSDK_USING_DEVICE_FUNCS_ENABLE
endif

ifeq ($(strip $(ANDLINK_DEV_ACCESS_LINK_ENABLE)),y)
	DEMO_DEFINES += -DANDLINK_DEV_ACCESS_LINK_ENABLE
endif


#厂商需要适配的文件
ADAPT_TARGET_NAME = AndlinkAdapt
SUBDIRS	= adapt
ADAPT_OBJS = $(foreach dir, $(SUBDIRS), $(patsubst %.c, %.o, $(wildcard $(dir)/*.c)) )

#主函数所在文件
MAIN_SRC_OBJS := $(wildcard *.c)
MAIN_OBJS := $(MAIN_SRC_OBJS:.c=.o)

MAIN_CPP_SRC_OBJS := $(wildcard *.cpp)
MAIN_CPP_OBJS := $(MAIN_CPP_SRC_OBJS:.cpp=.o)

ifeq ($(strip $(DEMO_X86_ENABLE)),y)
DEMO_LIBPATH = $(DEMO_ROOT_DIR)/lib_x86
else
DEMO_LIBPATH = $(DEMO_ROOT_DIR)/lib
endif
DEMO_INCLUDE = -I$(DEMO_ROOT_DIR)/include #-I$(DEMO_ROOT_DIR)/include/andlink-sec
DEMO_BIN = $(DEMO_ROOT_DIR)/bin

# y,各个库分开链接;n 仅链接一个库
MAIN_APP_LINK_MULTI_LIBS=y

#外部库(包括andlink库)
ifeq ($(strip $(MAIN_APP_LINK_EXTERN_LIBS_STATIC)),y) #静态链接
	ifeq ($(strip $(MAIN_APP_LINK_MULTI_LIBS)),y) #各个库分开链接
		#plan 1
		ifneq ($(strip $(ANDLINK_DEBUG_ENABLE)),)
		EXTERN_LIBS += $(DEMO_LIBPATH)/libandlink-core.a
		else
		EXTERN_LIBS += $(DEMO_LIBPATH)/libandlink-core.a.$(VERSION_ALL)
		endif
		EXTERN_LIBS += $(DEMO_LIBPATH)/libjson.a
		
		ifeq ($(strip $(ANDLINK_CFG_NET_ENABLE)),y)# andlink配网
		EXTERN_LIBS += $(DEMO_LIBPATH)/libcoap.a
		endif

		ifeq ($(strip $(SEC_LIB_ENABLE)),y)# 一机一密
		EXTERN_LIBS += $(DEMO_LIBPATH)/libalsec.a
		endif

		ifeq ($(strip $(ANDLINK_DEBUG_ENABLE)),2)# 用于调试内存泄漏
		EXTERN_LIBS += $(DEMO_LIBPATH)/libdmalloc.a
		endif
		
		# 设备对接轻量版本
		ifeq ($(strip $(ANDLINK_DEV_ACCESS_LINK_ENABLE)_$(SDK_USING_MBEDTLS)),y_y)
		EXTERN_LIBS += $(DEMO_LIBPATH)/libcurl.a
		EXTERN_LIBS += $(DEMO_LIBPATH)/libmqttclient.a
		EXTERN_LIBS += $(DEMO_LIBPATH)/libmbedtls.a $(DEMO_LIBPATH)/libmbedcrypto.a $(DEMO_LIBPATH)/libmbedx509.a
		endif
		
		# 设备对接普通版本
		ifeq ($(strip $(ANDLINK_DEV_ACCESS_LINK_ENABLE)_$(SDK_USING_MBEDTLS)),y_n)
		EXTERN_LIBS += $(DEMO_LIBPATH)/libcurl.a
		EXTERN_LIBS += $(DEMO_LIBPATH)/libpaho-mqtt3as.a
		EXTERN_LIBS += $(DEMO_LIBPATH)/libssl.a $(DEMO_LIBPATH)/libcrypto.a
		endif	
		
		# 平台对接轻量版本
		ifeq ($(strip $(ANDLINK_DEV_ACCESS_LINK_ENABLE)_$(SDK_USING_MBEDTLS)),n_y)
		ifeq ($(strip $(ANDLINK_CLOUD_ACCESS_DM_ENABLE)),y)#DM启动
		EXTERN_LIBS += $(DEMO_LIBPATH)/libcurl.a
		EXTERN_LIBS += $(DEMO_LIBPATH)/libmbedtls.a $(DEMO_LIBPATH)/libmbedcrypto.a $(DEMO_LIBPATH)/libmbedx509.a
		endif
		endif
		
		# 平台对接普通版本
		ifeq ($(strip $(ANDLINK_DEV_ACCESS_LINK_ENABLE)_$(SDK_USING_MBEDTLS)),n_n)
		ifeq ($(strip $(ANDLINK_CLOUD_ACCESS_DM_ENABLE)),y)#DM启动 
		EXTERN_LIBS += $(DEMO_LIBPATH)/libcurl.a		
		EXTERN_LIBS += $(DEMO_LIBPATH)/libssl.a $(DEMO_LIBPATH)/libcrypto.a
		endif
		endif
	else #各个库打包为一个库进行链接
		#plan 2
		ifneq ($(strip $(ANDLINK_DEBUG_ENABLE)),)
		EXTERN_LIBS += $(DEMO_LIBPATH)/libandlink.a
		else
		EXTERN_LIBS += $(DEMO_LIBPATH)/libandlink.a.$(VERSION_ALL)
		endif
	endif

else #动态链接
	EXTERN_LIBS +=  -L$(DEMO_LIBPATH) -landlink
	ifeq ($(strip $(SEC_LIB_ENABLE)),y)# 一机一密
		EXTERN_LIBS += -lalsec
	endif
	#EXTERN_LIBS +=  -L$(DEMO_LIBPATH) -landlink
endif


ifeq ($(strip $(CMDC_DM_LIB_ENABLE)),y)
	ifneq ($(strip $(CMDC_DM_LIB_LINK_STATIC)),y)
		EXTERN_LIBS +=  -L$(DEMO_LIBPATH) -llwm2msdkL
	endif
endif

#编译和链接选项
DEMO_CFLAGS := $(DEMO_INCLUDE) -fPIC -Wno-system-headers -Wno-unused-result -rdynamic -funwind-tables -ffunction-sections $(DEMO_DEFINES) -g $(APP_CFLAGS) #-lssl -lcrypto -Wunused-result
DEMO_LDFLAGS := $(EXTERN_LIBS) -lpthread -lm -ldl -lrt -Wl,-rpath $(DEMO_ROOT_DIR)/lib -I./include

CXXFLAGS=-Wall  -g -fpermissive  -I./include

#添加特殊链接选项,有些选项有些工具链需要,有些工具链不需要,采用外部输入的方式设置;比如 -lz
DEMO_LDFLAGS += $(SPECIAL_LDFLAGS)#

export DEMO_CFLAGS


# Define the source and include directories
SRCDIR=src
SRCDIR_CPP=src_cpp
INCLUDEDIR=include

# Automatically gather all .c files in src and src_cpp directories
C_SRCS=$(wildcard $(SRCDIR)/*.c)
CPP_SRCS=$(wildcard $(SRCDIR_CPP)/*.cpp)
SRCS=$(C_SRCS) $(CPP_SRCS)

C_OBJS=$(C_SRCS:.c=.o)
CPP_OBJS=$(CPP_SRCS:.cpp=.o)
OBJS=$(CPP_OBJS)
# OBJS=$(C_OBJS) $(CPP_OBJS)


.PHONY: andlinkdaemon andlink_router all adaptlib

all: andlinkdaemon  install

andlink_router: all

adaptlib:
	$(foreach dir, $(SUBDIRS), $(MAKE) -C $(dir) ;)
	@$(AR) r lib$(ADAPT_TARGET_NAME).a $(ADAPT_OBJS)
	$(CC) $(DEMO_CFLAGS) -shared -Bsymbolic -o lib$(ADAPT_TARGET_NAME).so $(ADAPT_OBJS)

andlinkdaemon:adaptlib $(OBJS) main.o
	@echo "--------------------------1"
	#$(CXX) $(OBJS) $(DEMO_CFLAGS) -o $@  $(MAIN_CPP_OBJS) lib$(ADAPT_TARGET_NAME).a $(DEMO_LDFLAGS)
	@echo "--------------------------2"
	$(CXX) $(OBJS) $(DEMO_CFLAGS) -o $@  $(MAIN_CPP_OBJS)  $(ADAPT_OBJS) $(DEMO_LDFLAGS)
	@echo "--------------------------3"
	mkdir -p $(DEMO_BIN)
	cp andlinkdaemon $(DEMO_BIN)/
	cd $(DEMO_BIN) && ln -sf andlinkdaemon $(APP)

	rm  -f $(DEMO_ROOT_DIR)/*.o
	rm  -f $(DEMO_ROOT_DIR)/adapt/*.o

$(SRCDIR)/%.o: $(SRCDIR)/%.c $(DEPS)
	$(CC) -c $< -o $@ $(CFLAGS)

$(SRCDIR_CPP)/%.o: $(SRCDIR_CPP)/%.c $(DEPS)
	$(CXX) -c $< -o $@ $(CXXFLAGS)

main.o: main.cpp $(DEPS)
	$(CXX) -c $< -o $@ $(CXXFLAGS)


# install app + main files + header files + dependent libs + makefile to target
install: 
	@echo "====congratulations,compile andlinkdaemon success!===="
	
clean:
ifneq ($(strip $(DEMO_ROOT_DIR)),) 
	rm -rf *.o
	rm -rf *.a*
	rm -rf *.so*
	rm -rf andlinkdaemon
	rm  -f $(DEMO_ROOT_DIR)/bin/*
	rm  -f $(DEMO_ROOT_DIR)/adapt/*.o
	rm  -f $(DEMO_ROOT_DIR)/src_cpp/*.o
	#小工程需要提供给厂商使用,此处clean,不可删除库 文件
	#rm  -f $(DEMO_ROOT_DIR)/lib/*.so*
	#rm  -f $(DEMO_ROOT_DIR)/lib/*.a*
endif
distclean:
ifneq ($(strip $(DEMO_ROOT_DIR)),) 
	rm -rf *.o
	rm -rf *.a*
	rm -rf *.so*
	rm -rf andlinkdaemon
	rm  -f $(DEMO_ROOT_DIR)/bin/*
	rm  -f $(DEMO_ROOT_DIR)/adapt/*.o
	#rm  -f $(DEMO_ROOT_DIR)/lib/*.so*
	#rm  -f $(DEMO_ROOT_DIR)/lib/*.a*
	$(foreach dir, $(SUBDIRS), $(MAKE) -C $(dir) cleann ;)
endif
%.o: %.c
	@echo "CC $@"
	@$(CC) $(DEMO_CFLAGS) -o $@ -c $<
