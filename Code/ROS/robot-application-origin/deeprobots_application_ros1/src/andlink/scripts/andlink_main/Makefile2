# Define the compiler and flags for C++ files -D__cplusplus
CXX=g++
CXXFLAGS=-Wall  -g -fpermissive  -I./include -I/usr/include/glib-2.0

# Define the compiler and flags for C files
CC=gcc -std=c99
CFLAGS=-Wall -g -fpermissive -I./include -I/usr/include/glib-2.0 -I/usr/lib/aarch64-linux-gnu/glib-2.0/include

# Define the source and include directories
SRCDIR=src
SRCDIR_CPP=src_cpp
INCLUDEDIR=include

# Automatically gather all .c files in src and src_cpp directories
C_SRCS=$(wildcard $(SRCDIR)/*.c)
CPP_SRCS=$(wildcard $(SRCDIR_CPP)/*.cpp)
SRCS=$(C_SRCS) $(CPP_SRCS)

C_OBJS=$(C_SRCS:.c=.o)
CPP_OBJS=$(CPP_SRCS:.cpp=.o)
OBJS=$(C_OBJS) $(CPP_OBJS)

# Automatically include header files as dependencies
DEPS=$(wildcard $(INCLUDEDIR)/*.h)

# Define the libraries and their locations
LIBS=-L./lib -lstdc++ -l:libandlink.a.V1.7.2 `pkg-config --cflags glib-2.0` `pkg-config --libs glib-2.0` `pkg-config --cflags gio-2.0` `pkg-config --libs gio-2.0`

# Define the target executable name5
TARGET=ysc_andlink

# Default rule: builds the target
all: $(TARGET)

# Rule to build the target executable
$(TARGET): $(OBJS) main.o
	$(CXX) $(OBJS) main.o -o $(TARGET) $(LIBS)

# Rule to compile each C source file into an object file
$(SRCDIR)/%.o: $(SRCDIR)/%.c $(DEPS)
	$(CC) -c $< -o $@ $(CFLAGS)

# Rule to compile each C++ source file into an object file
$(SRCDIR_CPP)/%.o: $(SRCDIR_CPP)/%.c $(DEPS)
	$(CXX) -c $< -o $@ $(CXXFLAGS)

# Rule to compile main.cpp into an object file
main.o: main.cpp $(DEPS)
	$(CXX) -c $< -o $@ $(CXXFLAGS)

# Rule to clean up object files and the executable
clean:
	rm -f $(C_OBJS) $(CPP_OBJS) main.o $(TARGET)

# Rule to very clean, including the .dSYM folder if present
veryclean: clean
	rm -rf .dSYM

# Rule to rebuild everything after a clean
rebuild: clean all
