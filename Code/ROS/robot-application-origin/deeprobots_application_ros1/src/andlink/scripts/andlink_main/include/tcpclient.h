// TcpClient.h
#ifndef TCPCLIENT_H
#define TCPCLIENT_H

#include <string>

class TcpClient {
public:
    unsigned char buffer[1024];

    
    TcpClient(const char* ip, int port);
    ~TcpClient();

    bool connect_server();
    bool send2server(const char* message,int length);
    int receive();
    // std::string receive();
    void close();

private:
    const char* m_ip;
    int m_port;
    int m_clientSocket;
    

};

#endif // TCPCLIENT_H