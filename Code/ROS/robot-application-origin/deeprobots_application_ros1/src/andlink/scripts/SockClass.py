import socket
import time
import threading
import selectors
import types
from custom_logger import CustomLogger

class TcpClient:
    def __init__(self, ip="127.0.0.1", port=30000, timeout=5, logger=None):
        if logger == None:
            self.log_t = CustomLogger('SockClass', 'debug')
        else:
            self.log_t = logger
        
        self.ip = ip
        self.port = port
        self.timeout = timeout

        self.error_msg = None
        self.tcp_client = self.init_tcp_connection()
        


    def init_tcp_connection(self, max_retries=5, retry_delay=5):
        # 尝试关闭现有连接，如果有的话
        if hasattr(self, 'tcp_client') and self.tcp_client:
            try:
                self.tcp_client.shutdown(socket.SHUT_RDWR)
            # except OSError as e:
                # self.log_t.error(f"OSError: {e}")
            except Exception as e:
                self.error_msg = e
                self.log_t.error(f"TCP client: Init_tcp_connection Exception: {e}")
            finally:
                self.tcp_client.close()

        # 尝试重新连接，直到成功或达到最大重试次数
        retries = 0
        while retries < max_retries:
            try:
                self.tcp_client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.tcp_client.settimeout(self.timeout)
                self.tcp_client.connect((self.ip, self.port))
                self.log_t.info(f"TCP client: Connection established with {self.ip}:{self.port}")
                return self.tcp_client
            # except (socket.timeout, ConnectionRefusedError, OSError) as e:
            except Exception as e:
                # 连接失败，等待一段时间后重试
                retries += 1
                time.sleep(retry_delay)
                self.error_msg = e
                self.log_t.warning(f"TCP client: Initial reconnect fail reason-{e},time-{retries}")
                continue

        
        self.log_t.warning("TCP client: Failed to establish connection after multiple retries")
        self.error_msg = "Failed to establish connection after multiple retries"
        return None
        # # 如果达到最大重试次数仍未成功，抛出异常
        # raise Exception("Failed to establish connection after multiple retries")

    def is_connected(self):
        # 发送心跳包或尝试读取数据以检测连接是否正常
        try:
            self.tcp_client.send(b'\0')  # 发送空字节作为心跳包
            self.tcp_client.recv(1)  # 尝试接收回应
            return True
        except (socket.timeout, ConnectionResetError, OSError) as e:
            self.error_msg = f"Socket error:{e}"
            self.log_t.warning(f"TCP client: is_connected-{e}")
            return False
        except Exception as e:
            self.error_msg =f"Unexpected error during connection check: {e}"
            # print(f"Unexpected error during connection check: {e}")
            self.log_t.warning(f"TCP client: Unexpected error during connection check-{e}")
            return False

    def tcp_recv(self):
        try:
            data = self.tcp_client.recv(1024)
            return data
        except socket.error as e:
            self.error_msg = f"Socket error:{e}"
            # print(f"Socket error: {e}")
            self.log_t.warning(f"TCP client: tcp_recv-Socket error: {e}")
            return None
        except Exception as e:
            self.error_msg = f"Unexpected error: {e}"
            # print(f"Unexpected error: {e}")
            self.log_t.warning(f"TCP client: tcp_recv-Unexpected error: {e}")
            return None

    def tcp_send(self, send_data):
        try:
            # 尝试发送所有数据，不接受部分写入
            self.tcp_client.sendall(send_data)
        except socket.error as e:
            # 捕获socket.error，通常表示底层网络问题
            # print(f"Failed to send data: {e}")
            self.log_t.warning(f"TCP client: tcp_send - Failed to send data: {e}")
        except Exception as e:
            # 捕获其他未预期的异常
            # print(f"An unexpected error occurred while sending data: {e}")
            self.log_t.warning(f"TCP client: tcp_send - An unexpected error occurred : {e}")


    def close(self):
        self.tcp_client.close()

class UdpClient:
    def __init__(self, host='localhost', port=50051, logger=None):
        self.host = host
        self.port = port
        self.error_msg = None

        if logger == None:
            self.log_t = CustomLogger('SockClass', 'debug')
        else:
            self.log_t = logger

        try:
            self.client_socket = self.init_udp_connection()
        except Exception as e:
            self.error_msg = f"Failed to initialize UDP connection: {e}"
            self.log_t.error(f"UdpClient: Failed to initialize UDP connection - {e}")
            self.client_socket = None


    def init_udp_connection(self):
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # 设置超时时间（秒）
        client_socket.settimeout(5)
        return client_socket

    # 发送数据,传入的必须为被encoded的字符串
    def send_message(self, message="Hello from UDP Client"):
        try:
            self.client_socket.sendto(message, (self.host, self.port))
            return True
        except Exception as e:
            self.error_msg = f"UdpClient: Error occurred when sending message-{e}"
            self.log_t.error(f"UdpClient: Error occurred when sending message-{e}")
            return False

    def recv_message(self):
        try:
            response, server_addr = self.client_socket.recvfrom(1024)
            return response, server_addr
        except socket.timeout:
            self.error_msg = f"Receive timeout"
            self.log_t.warning(f"UdpClient: Receive timeout")
            return None, None
        except Exception as e:
            self.error_msg = f"Error occurred when receiving message: {e}"
            self.log_t.error(f"UdpClient: Error occurred when receiving message: {e}")
            return None, None

    def close(self):
        self.client_socket.close()

    def run(self, message="Hello from UDP Client"):
        if self.send_message(message):
            response, _ = self.recv_message()
            if response is not None:
                self.error_msg = f"Received response from the server: {response}"
                self.log_t.info(f"UdpClient: Received response from the server: {response}")
            else:
                self.error_msg = f"No response received from the server."
                print("No response received from the server.")
                self.log_t.info("UdpClient: No response received from the server.")


class TcpServer:
    def __init__(self, host='127.0.0.1', port=30000,logger=None,max_listen=5):
        self.host = host
        self.port = port
        self.message_error = None

        if logger == None:
            # print("TcpServer: logger is None")
            self.log_t = CustomLogger('SockClass', 'debug')
        else:
            self.log_t = logger

        self.server_socket = self.init_tcp_server(max_listen)

    def init_tcp_server(self,max_listen=5):
        try:
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.bind((self.host, self.port))
            server_socket.listen(max_listen)
            return server_socket
        except Exception as e:
            self.message_error = f"Failed to initialize TCP server: {e}"
            self.log_t.error(f"TcpServer: Failed to initialize TCP server: {e}")
            return None


    @property
    def query_error_msg(self):
        return self.message_error
    
    def accept_new_connection(self):
        client_socket, client_address = self.server_socket.accept()
        # print(f"New connection from {client_address[0]}:{client_address[1]}")
        self.log_t.info(f"TcpServer: New connection from {client_address[0]}:{client_address[1]}")
        return client_socket, client_address

    def send_to_client(self, client_socket, message):
        try:
            # encoded_message = message.encode()
            encoded_message = message
            client_socket.send(encoded_message)
        except Exception as e:
            self.message_error = f"Error sending data to client: {e}"
            self.log_t.error(f"TcpServer: Error sending data to client: {e}")
            return False
        return True

    def receive_from_client(self, client_socket):
        try:
            data = client_socket.recv(1024)
            if not data:  ##检测断联
                return None
            # return data.decode()
            return data
        except Exception as e:
            self.message_error = f"Error receiving data from client: {e}"
            # print(f"Error receiving data from client: {e}")
            self.log_t.error(f"TcpServer: Error receiving data from client: {e}")
            return None

    def handle_client(self, client_socket):
        while True:
            data = self.receive_from_client(client_socket)
            if not data:  # 如果客户端断开连接，退出循环
                break
            response = f"Received: {data}".encode()
            self.send_to_client(client_socket, response)
        client_socket.close()

    def start_server(self):
        while True:
            client_socket = self.accept_new_connection()
            client_thread = threading.Thread(target=self.handle_client, args=(client_socket,))
            client_thread.start()

    def close_server(self):
        self.server_socket.close()

class UdpServer:
    def __init__(self, host='localhost', port=50051, logger=None):
        self.host = host
        self.port = port
        self.message_error = None

        if logger == None:
            self.log_t = CustomLogger('SockClass', 'debug')
        else:
            self.log_t = logger

        self.server_socket = self.__init_udp_server()

    def __init_udp_server(self):
        try:
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            server_socket.bind((self.host, self.port))
            return server_socket
        except Exception as e:
            self.message_error = f"Failed to initialize UDP server: {e}"
            self.log_t.error(f"UdpServer: Failed to initialize UDP server: {e}")
            return None



    def send_to_client(self, message, address):
        try:
            encoded_message = message.encode()
            self.server_socket.sendto(encoded_message, address)
        except Exception as e:
            self.message_error = f"Error sending data to client: {e}"
            self.log_t.error(f"UdpServer: Error sending data to client - {e}")

    def receive_from_client(self):
        try:
            data, address = self.server_socket.recvfrom(1024)
            return data, address
        except Exception as e:
            self.message_error = f"Error receiving data from client: {e}"
            self.log_t.error(f"UdpServer: Error receiving data from client - {e}")
            return None

    def handle_message(self, data, address):
        response = f"Received: {data.decode()} from {address[0]}:{address[1]}".encode()
        self.send_to_client(response, address)

    def start_server(self):
        while True:
            data, address = self.receive_from_client()
            if data is not None:
                self.handle_message(data, address)

    def close_server(self):
        self.server_socket.close()




class MultiplexedTcpServer:
    def __init__(self, host='127.0.0.1', port=30000, request_handler=None, logger=None,max_listen=5):
        if logger == None:
            self.log_t = CustomLogger('SockClass', 'debug')
        else:
            self.log_t = logger

        self.host = host
        self.port = port
        self.request_handler = request_handler
        self.message_error = None
        self.selector = selectors.DefaultSelector()
        self.server_socket = self.init_tcp_server(max_listen)
        self.client_connections = {}

    @property
    def query_error_msg(self):
        return self.message_error

    def get_all_client_sockets(self):
        return list(self.client_connections.keys())
    def init_tcp_server(self,max_listen=5):
        try:
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_socket.bind((self.host, self.port))
            server_socket.listen(max_listen)
            server_socket.setblocking(False)
            self.selector.register(server_socket, selectors.EVENT_READ, data=None)
            self.log_t.info(f"MultiplexedTcpServer: Server started on {self.host}:{self.port}")
            return server_socket
        except Exception as e:
            # self.message_error = f"Failed to initialize multiplexed TCP server: {e}"
            # print(self.message_error)
            self.log_t.error( f"MultiplexedTcpServer: Failed to initialize multiplexed TCP server - {e}")
            return None

    def accept_new_connection(self, server_socket):
        try:
            conn, addr = server_socket.accept()
            conn.setblocking(False)
            data = types.SimpleNamespace(addr=addr, inb=b'', outb=b'')
            self.client_connections[conn] = data
            self.selector.register(conn, selectors.EVENT_READ | selectors.EVENT_WRITE, data=data)
            self.log_t.info(f"MultiplexedTcpServer: New connection from {addr[0]}:{addr[1]}")
        except Exception as e:
            self.log_t.warning(f"MultiplexedTcpServer: Error accepting new connection - {e}")

    def receive_data(self, key, mask):
        sock = key.fileobj
        data = key.data
        try:
            recv_data = sock.recv(1024)  # Should be ready to read
            if recv_data:
                data.inb += recv_data
                self.log_t.debug(f"MultiplexedTcpServer: Received data from {data.addr}: {recv_data}")
                
                # Use the request handler to process the data
                if self.request_handler:
                    response = self.request_handler(data.inb)
                    data.outb += response
                else:
                    # Default behavior: echo back the received data
                    data.outb += recv_data
            else:
                self.log_t.debug(f"MultiplexedTcpServer: Closing connection to {data.addr}")
                self.close_connection(sock)
        except Exception as e:
            self.log_t.error(f"MultiplexedTcpServer: Error receiving data: {e}")
            self.close_connection(sock)

    def send_data(self, key, mask):
        sock = key.fileobj
        data = key.data
        if data.outb:
            try:
                print(f"MultiplexedTcpServer: Sending {data.outb!r} to {data.addr}")
                sent = sock.send(data.outb)  # Should be ready to write
                data.outb = data.outb[sent:]
            except Exception as e:
                print(f"Error sending data: {e}")
                self.log_t.error(f"MultiplexedTcpServer: Error sending data - {e}")
                self.close_connection(sock)
    def send_to_client(self, client_socket, message):
        if client_socket in self.client_connections:
            self.client_connections[client_socket].outb += message.encode('utf-8')
            self.selector.modify(client_socket, selectors.EVENT_READ | selectors.EVENT_WRITE)
            self.log_t.info(f"MultiplexedTcpServer: Queued message for {self.client_connections[client_socket].addr}")
        else:
            self.log_t.warning("MultiplexedTcpServer: Client socket not found")

    def handle_client(self, key, mask):
        if mask & selectors.EVENT_READ:
            self.receive_data(key, mask)
        if mask & selectors.EVENT_WRITE:
            self.send_data(key, mask)

    def start_server(self):
        if not self.server_socket:
            print("Server socket is not initialized. Exiting.")
            self.log_t.error("MultiplexedTcpServer: Server socket is not initialized. Exiting.")
            return

        try:
            while True:
                events = self.selector.select(timeout=None)
                for key, mask in events:
                    if key.data is None:
                        self.accept_new_connection(key.fileobj)
                    else:
                        self.handle_client(key, mask)
        except KeyboardInterrupt:
            self.log_t.info("MultiplexedTcpServer: Caught keyboard interrupt, exiting")
        finally:
            self.close_server()

    def close_connection(self, sock):
        try:
            self.log_t.info(f"MultiplexedTcpServer: Closing connection to {self.client_connections[sock].addr}")
            self.selector.unregister(sock)
            sock.close()
            del self.client_connections[sock]
        except Exception as e:
            self.log_t.error(f"MultiplexedTcpServer: Error closing connection: {e}")

    def close_server(self):
        for sock in list(self.client_connections.keys()):
            self.close_connection(sock)
        if self.server_socket:
            self.selector.unregister(self.server_socket)
            self.server_socket.close()
        self.selector.close()
        self.log_t.info("MultiplexedTcpServer: Server shut down")


# Test 任务
def tcp_client_test():
    tcp_client = TcpClient("127.0.0.1",1502)
    if tcp_client.is_connected():
        print("TCP Client connected to the server.")
    else:
        print("TCP Client failed to connect to the server.")
    tcp_client.tcp_send("hello".encode())
    tcp_client.close()

def tcp_server_test():
    tcpserver=TcpServer("127.0.0.1",5670)
    client_socket, client_address=tcpserver.accept_new_connection()
    tcpserver.send_to_client(client_socket,"hello,tcp client".encode())
    data=tcpserver.receive_from_client(client_socket)
    print(f"data:{data}")
    print(f"error_msg:{tcpserver.query_error_msg}")


def udpclient_test():

    udpclient=UdpClient("dd5410a9f2629613.natapp.cc",3131)
    udpclient.send_message("hello,udpclient_test".encode())
    print(udpclient.error_msg)

    response, server_addr=udpclient.recv_message()
    print(f"response, server_addr:{response, server_addr}")


    
    

def udpserver_test():
    # udpserver=UdpServer("127.0.0.1",5670)
    udpserver=UdpServer("***********",5670)
    data,_=udpserver.receive_from_client()
    print(f"data:{data}")
    print(f"message_error:{udpserver.message_error}")

def test_multiplexed_tcp_server():
    server = MultiplexedTcpServer("127.0.0.1", 5670)
    
    # Start the server in a separate thread
    server_thread = threading.Thread(target=server.start_server)
    server_thread.start()

    # Allow some time for the server to start and clients to connect
    time.sleep(1)

    # Example of sending a message to all connected clients
    for client_socket in server.get_all_client_sockets():
        server.send_to_client(client_socket, "Hello, Client!")

    # Add any additional logic for testing or running the server
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Shutting down the server.")
        server.close_server()
        server_thread.join()

if __name__ == '__main__':
    # tcp_client_test()

    # tcp_server_test()

    # udpclient_test()

    udpserver_test()

    # test_multiplexed_tcp_server()








