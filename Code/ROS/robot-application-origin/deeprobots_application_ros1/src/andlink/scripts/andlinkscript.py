#!/usr/bin/env python3
import os
import subprocess
import argparse
import struct
import traceback
import threading
import rospy
from SockClass import TcpServer
from custom_logger import CustomLogger
from ble_manager import ble_start,shutdown, update_adv,Characteristic,Ble_manager,GL<PERSON>,dbus,GATT_CHRC_IFACE,randint
from ThreadPool import ThreadPool
from wifi_manager import MultiInterfaceWifiManager
import time
import dbus
import dbus.mainloop.glib
from gi.repository import GLib
import datetime
from read import DeviceConfig

from ble_data_process import DataProcessor

global log_t,tcp_server_t,client_socket,threadp_t
global reply,aldlink_data,aldlink_data_sign
global global_device_id, global_manuf_code, global_manuf_data, global_deviceId
global bluetooth_device_path

reply={"value":None}
log_t = None
tcp_server_t  = None
client_socket = None
threadp_t = None
aldlink_data = None
aldlink_data_sign = 0

def disconnect_bluetooth_device(device_path):
    bus = dbus.SystemBus()
    device_proxy = bus.get_object('org.bluez', device_path)
    device = dbus.Interface(device_proxy, 'org.bluez.Device1')
    
    try:
        device.Disconnect()
        print("Device disconnected successfully.")
    except dbus.exceptions.DBusException as e:
        print(f"Failed to disconnect device: {e}")

def start_demo_process():
    # 获得当前脚本文件的绝对路径
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # 设置环境变量
    env = dict(os.environ)  # 复制当前环境
    env['LD_LIBRARY_PATH'] = os.path.join(script_dir, 'andlink_main')

    # 构建完整的命令路径
    command = ['./andlinkdaemon', '4', 'wlan0']

    # 指定工作目录
    cwd = os.path.join(script_dir, 'andlink_main')

    log_t.info(f"command:{command}, cwd:{cwd}")

    # 执行命令
    process = subprocess.Popen(command, env=env, cwd=cwd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    return process

def parse_tcp_header(data):
    # 解析数据包头部
    header_length = 4
    header_data = data[:header_length]
    data_valid = data[header_length:]


    # 解析头部信息
    header, = struct.unpack(">I", header_data)

   
    # 返回解析后的信息
    return header,data_valid


def send2adllink_ok():
    binary_array = bytearray()
    binary_array += bytes([0x55,0x55,0xff,0xff]) 
    tcp_server_t.send_to_client(client_socket,binary_array)
    log_t.debug("send ok")

def send2adllink_fail():
    binary_array = bytearray()
    binary_array += bytes([0x55,0x55,0xff,0xfe]) 
    tcp_server_t.send_to_client(client_socket,binary_array)
    log_t.debug("send failed")

def handle_ble01_first(data_valid, args1):
    global client_socket
    global global_device_id, global_manuf_code, global_manuf_data, global_deviceId
    global bluetooth_device_path
    log_t.info("enter 0x55550001")
    log_t.info(data_valid)
    print(len(data_valid))

    adl_ble_gatt_format1 = ">16sB16sB31sB"
    localName, localNameLen, scanRespData, scanRespDataLen, advData, advDataLen = struct.unpack(adl_ble_gatt_format1, data_valid[:-12])

    adl_ble_gatt_format2 = "<HHHHHH"
    serviceUUID, characteristicUUID_Down_Write, characteristicUUID_Up_Notify, characteristicUUID_Up_Indicate, duration, timeout = struct.unpack(adl_ble_gatt_format2, data_valid[-12:])

    # 处理解包后的值
    localName = localName.rstrip(b'\x00').decode('utf-8')
    scanRespData = scanRespData.rstrip(b'\x00')
    advData = advData.rstrip(b'\x00')

    serviceUUID_str = format(serviceUUID, '04X')

    log_t.debug(f"Local Name: {localName}")
    log_t.debug(f"Local Name Length: {localNameLen}")
    log_t.debug(f"Scan Response Data: {scanRespData}")
    log_t.debug(f"Scan Response Data Length: {scanRespDataLen}")
    log_t.debug(f"Advertisement Data: {' '.join(hex(byte)[2:].zfill(2) for byte in advData)}")
    log_t.debug(f"Advertisement Data Length: {advDataLen}")
    log_t.debug(f"Service UUID: {serviceUUID_str}")
    log_t.debug(f"Characteristic UUID Down Write: 0x{characteristicUUID_Down_Write:04X}")
    log_t.debug(f"Characteristic UUID Up Notify: 0x{characteristicUUID_Up_Notify:04X}")
    log_t.debug(f"Characteristic UUID Up Indicate: 0x{characteristicUUID_Up_Indicate:04X}")
    log_t.debug(f"Duration: {duration}")
    log_t.debug(f"Timeout: {timeout}")

    global_manuf_code, global_manuf_data = struct.unpack('>H5s', advData[-7:])
    config2_path = '/etc/andlink/facDevinfo.conf'
    device_config2 = DeviceConfig(config2_path)
    global_device_id = device_config2.get_config_value('deviceMac')
    mac_name = device_config2.get_config_value('mac')
    global_deviceId = 'CMCC-' + device_config2.get_config_value('deviceType') + '-' + device_config2.get_config_value('sn')

    mac_bytes = mac_to_bytes(mac_name)
    print(list(mac_bytes))  # 打印字节列表

    global_manuf_code, = struct.unpack('>H', mac_bytes[1::-1])  # 反转前两个字节
    global_manuf_data = list(mac_bytes[2:])  # 剩余字节作为列表
    global_manuf_data.append(0x01)

    args1[0].set_ble_adv_localname('CMB2320647-' + global_device_id[-4:])
    args1[0].set_ble_adv_manuf_data(global_manuf_code, global_manuf_data)
    args1[0].set_ble_json_deviceId(global_deviceId)

    ble_thread = threading.Thread(target=args1[1].custom_advertisement_register)
    ble_thread.start()

    send2adllink_ok()


def handle_ble02(data_valid, args1):
    global client_socket
    global global_device_id, global_manuf_code, global_manuf_data, global_deviceId
    global bluetooth_device_path
    reply['value'] = data_valid
    if data_valid[0] == 0x02 and data_valid[1] == 0x08:
        print("广播蓝牙登录02")
        config2_path = '/etc/andlink/facDevinfo.conf'
        device_config2 = DeviceConfig(config2_path)

        device_id = device_config2.get_config_value('deviceMac')
        mac_name = device_config2.get_config_value('mac')
        deviceId = 'CMCC-' + device_config2.get_config_value('deviceType') + '-' + device_config2.get_config_value('sn')

        mac_bytes = mac_to_bytes(mac_name)
        print(list(mac_bytes))  # 打印字节列表

        manuf_code, = struct.unpack('>H', mac_bytes[1::-1])  # 反转前两个字节
        manuf_data = list(mac_bytes[2:])  # 剩余字节作为列表
        manuf_data.append(0x02)

        time.sleep(3)
        args1[0].set_ble_adv_localname('CMB2320647-' + device_id[-4:])
        args1[0].set_ble_adv_manuf_data(manuf_code, manuf_data)
        args1[0].set_ble_json_deviceId(deviceId)

        ble_thread = threading.Thread(target=args1[1].custom_advertisement_register)
        ble_thread.start()


def handle_ble01_second(data_valid, args1):
    global client_socket
    global global_device_id, global_manuf_code, global_manuf_data, global_deviceId
    global bluetooth_device_path
    args1[0].set_ble_adv_localname('CMB2320647-' + global_device_id[-4:])
    args1[0].set_ble_adv_manuf_data(global_manuf_code, global_manuf_data)
    args1[0].set_ble_json_deviceId(global_deviceId)
    print("蓝牙断开")
    print("广播蓝牙绑定01")
    ble_thread = threading.Thread(target=args1[1].custom_advertisement_register)
    ble_thread.start()
def restart_andlink_service():
    try:
        # 执行 systemctl restart andlink.service 命令
        subprocess.run(['systemctl', 'restart', 'andlink.service'], check=True)
        print("andlink.service 重启成功")
    except subprocess.CalledProcessError as e:
        print(f"重启 andlink.service 失败: {e}")
        
def aldlink_tcprecv(*args1):
    global client_socket
    global global_device_id, global_manuf_code, global_manuf_data, global_deviceId
    global bluetooth_device_path
    # 获取结果
    while True:
        try:
            if client_socket == None:
                continue
            data = tcp_server_t.receive_from_client(client_socket)
            if data != None:
                hex_data = ' '.join(hex(byte)[2:].zfill(2) for byte in data)
                # log_t.debug(f"recv data :{data}")
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"{timestamp} recv data :{hex_data}")
            else:
                continue


            if not data:  # 如果客户端断开连接，退出循环
                log_t.info("Wait connect...")
                client_socket, client_address=tcp_server_t.accept_new_connection()
                log_t.info(f"client_socket:{client_socket}, client_address:{client_address}")
                continue
                
            header, data_valid = parse_tcp_header(data)

            if header == 0x55550001:  # 蓝牙开启
                print("广播蓝牙绑定01")
                handle_ble01_first(data_valid, args1)
            elif header == 0x55550002:  # 蓝牙登录
                handle_ble02(data_valid, args1)
            elif header == 0x55550003:  # 蓝牙解绑
                print("设备解绑，广播蓝牙01")
                handle_ble01_second(data_valid, args1)
                
            elif header == 0x55550006: #wifi连接失败
                print("wif连接失败，重新广播蓝牙01")
                #restart_andlink_service()
                handle_ble01_second(data_valid, args1)

            elif data == 0x55550004:  #wifi start
                wifi_cfg_fmt = ">32s64s16sii32s8s"
                (ssid,password,encrypt,channel,
                type,mac,RSSI) = struct.unpack(wifi_cfg_fmt, data_valid)
                wifi_mgu =MultiInterfaceWifiManager(logger=log_t)
                if wifi_mgu.wifi_manager.connect_to_network(ssid, password):
                    # log_t.debug("connect successfully")
                    send2adllink_ok()
                else:
                    send2adllink_fail()
            elif data == 0x55550005: #
                send2adllink_ok()                              

                
        except Exception as e:
            log_t.error(f"main quit Exception :{e}")
            error_traceback = traceback.format_exc()
            log_t.error(f"异常堆栈回溯: {error_traceback}")

def mac_to_byte_list(mac):
    # 确保 MAC 地址是正确的长度
    if len(mac) != 12:
        raise ValueError("MAC address should be exactly 12 characters long")
    
    # 将字符串每两个字符分割并转换为十六进制字节
    byte_list = [int(mac[i:i+2], 16) for i in range(0, len(mac), 2)]
    return byte_list

def mac_to_bytes(mac):
    if len(mac) != 12:
        raise ValueError("MAC address should be exactly 12 characters long")
    # 将 MAC 地址转换为字节对象
    return bytes(int(mac[i:i+2], 16) for i in range(0, len(mac), 2))


def property_changed(interface, changed, invalidated, path):
    global bluetooth_device_path
    #print(changed)
    try:
        if bluetooth_device_path is None and path is not None:
            bluetooth_device_path = path
            print("设定全局设备路径:", bluetooth_device_path)
    except Exception as e:
        print("设置bluetooth_device_path时发生错误:", e)
    if interface == "org.bluez.Device1":
        for prop in ("Connected", "ServicesResolved"):
            if prop in changed:
                status = changed[prop]
                print(f"Bluez状态 at {path} connected: {status}")
                # 设备状态改变时可以在这里加入相应的处理逻辑

def start_bluetooth_listener():
    bus = dbus.SystemBus()
    dbus.mainloop.glib.DBusGMainLoop(set_as_default=True)

    bus.add_signal_receiver(
        property_changed,
        bus_name="org.bluez",
        dbus_interface="org.freedesktop.DBus.Properties",
        signal_name="PropertiesChanged",
        path_keyword="path"
    )

    loop = GLib.MainLoop()
    print("Listening for Bluetooth connection changes...")
    loop.run()

if __name__ == '__main__':

    bluetooth_device_path = None
    parser = argparse.ArgumentParser()
    parser.add_argument('--timeout', default=0, type=int, help="advertise " +
                        "for this many seconds then stop, 0=run forever " +
                        "(default: 0)")
    args = parser.parse_args()

    log_t= CustomLogger(__name__, set_level="debug", log_path = "./logs")

    max_attempts = 5  # 设定最大尝试次数
    attempt = 0

    while attempt < max_attempts:
        try:
            tcp_server_t = TcpServer(host='0.0.0.0', port=31426, logger=log_t)
            log_t.info("TCP服务器已成功初始化并绑定到端口31426")
            break  # 成功后退出循环
        except Exception as e:
            log_t.error(f"第 {attempt+1} 次尝试TCP服务器初始化或绑定失败: {e}")
            attempt += 1
            if attempt < max_attempts:
                time.sleep(10)  # 等待10秒
            else:
                log_t.error("达到最大尝试次数，停止尝试")
                raise  # 在最后一次尝试后仍然失败，抛出异常以停止程序

    log_t.info("server start")

    config_path = '/etc/andlink/andlinkSdk.conf'
    device_config = DeviceConfig(config_path)

    user_bind = device_config.get_config_value('userBind')
    usrkey = device_config.get_config_value('usrkey')

    config2_path = '/etc/andlink/facDevinfo.conf'
    device_config2 = DeviceConfig(config2_path)

    device_id = device_config2.get_config_value('deviceMac')
    mac_name = device_config2.get_config_value('mac')
    deviceId = 'CMCC-'+device_config2.get_config_value('deviceType')+'-'+device_config2.get_config_value('sn')
    # mac_name_list = mac_to_byte_list(mac_name)
    # mac_hex_bytes = ['0x{:02x}'.format(b) for b in mac_name_list]
    print("userBind:", device_config.get_config_value('userBind'))
    print("deviceId:", device_config.get_config_value('deviceId'))
    print("deviceId_fa:"+deviceId)
    mac_bytes = mac_to_bytes(mac_name)
    print(list(mac_bytes))  # 打印字节列表

# 使用 struct.unpack 来解析前6个字节
# 由于大端格式，需要调整顺序为 ef54
    manuf_code, = struct.unpack('>H', mac_bytes[1::-1])  # 反转前两个字节
    manuf_data = list(mac_bytes[2:])  # 剩余字节作为列表
    
    # mac_name = list(str.encode(mac_name))

    # manuf_code,manuf_data = struct.unpack('>H4s', mac_hex_bytes)

    if user_bind is not None and user_bind == '1' and usrkey is not None:
        manuf_data.append(0x02)
    else:
        manuf_data.append(0x01)

        # ble_thread = threading.Thread(target=ble_start,args=(data_processor_t,))
    data_processor_t=DataProcessor('CMB2320647-'+device_id[-4:],manuf_code,manuf_data,deviceId)

    ble_t = Ble_manager(data_processor_t)
    ble_t.custom_app_register()
    # ble_t.custom_app_addsrv(service_t)
    ble_t.custom_advertisement_register()

    ble_thread = threading.Thread(target=ble_t.ble_manager_start,args=())

    # ble_thread = threading.Thread(target=ble_start,args=(data_processor_t,))
    ble_thread.daemon = True
    ble_thread.start()


    demo_process = start_demo_process()
    time.sleep(3)

    # 启动蓝牙监听器线程
    listener_thread = threading.Thread(target=start_bluetooth_listener)
    listener_thread.daemon = True
    listener_thread.start()

    try:
        log_t.info("等待新的客户端连接...")
        client_socket, client_address = tcp_server_t.accept_new_connection()
        if client_socket is None:
            log_t.warning("没有新的客户端连接，client_socket 为 None")
        else:
            log_t.info(f"客户端已连接，地址: {client_address}")
    except Exception as e:
        log_t.error(f"接受客户端连接时发生错误: {e}")
        client_socket = None  # 确保出错时不会继续使用 client_socket
    print("000")
    data_processor_t.set_tcp_socket(tcp_server_t,client_socket,client_address)
    log_t.info(f"client_address:{client_address}")
    print("111")
    # 使用线程池处理 aldlink 的 TCP 接收
    threadp_t = ThreadPool(logger=log_t)
    threadp_t.submit_task(aldlink_tcprecv,"aldlink_tcprecv",data_processor_t,ble_t)
    
