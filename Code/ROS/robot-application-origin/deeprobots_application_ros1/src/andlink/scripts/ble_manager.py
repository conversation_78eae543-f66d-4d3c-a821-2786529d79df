#!/usr/bin/env python3
# SPDX-License-Identifier: LGPL-2.1-or-later

from __future__ import print_function
import time
import argparse
from random import randint

import threading

import dbus
import dbus.exceptions
import dbus.service
from gi.repository import GLib
import dbus.mainloop.glib

import array

try:
    from gi.repository import GObject  # python3
except ImportError:
    import gobject as GObject  # python2


from custom_logger import CustomLogger
from ble_data_process import DataProcessor

mainloop = None

# Interfaces and Constants
BLUEZ_SERVICE_NAME = 'org.bluez'
LE_ADVERTISING_MANAGER_IFACE = 'org.bluez.LEAdvertisingManager1'
DBUS_OM_IFACE = 'org.freedesktop.DBus.ObjectManager'
DBUS_PROP_IFACE = 'org.freedesktop.DBus.Properties'

LE_ADVERTISEMENT_IFACE = 'org.bluez.LEAdvertisement1'

GATT_SERVICE_IFACE = 'org.bluez.GattService1'
GATT_CHRC_IFACE = 'org.bluez.GattCharacteristic1'
GATT_DESC_IFACE = 'org.bluez.GattDescriptor1'

GATT_MANAGER_IFACE = 'org.bluez.GattManager1'
DEVICE_INTERFACE = 'org.bluez.Device1'


# D-bus Exception Deal
class InvalidArgsException(dbus.exceptions.DBusException):
    _dbus_error_name = 'org.freedesktop.DBus.Error.InvalidArgs'


class NotSupportedException(dbus.exceptions.DBusException):
    _dbus_error_name = 'org.bluez.Error.NotSupported'


class NotPermittedException(dbus.exceptions.DBusException):
    _dbus_error_name = 'org.bluez.Error.NotPermitted'


class InvalidValueLengthException(dbus.exceptions.DBusException):
    _dbus_error_name = 'org.bluez.Error.InvalidValueLength'


class FailedException(dbus.exceptions.DBusException):
    _dbus_error_name = 'org.bluez.Error.Failed'

## 广播包设置
class Advertisement(dbus.service.Object):
    PATH_BASE = '/org/bluez/example/advertisement'

    def __init__(self, bus, index, advertising_type):
        self.path = self.PATH_BASE + str(index)
        self.bus = bus
        self.ad_type = advertising_type
        self.service_uuids = None
        self.manufacturer_data = None
        self.solicit_uuids = None
        self.service_data = None
        self.local_name = None
        self.include_tx_power = False
        self.data = None
        dbus.service.Object.__init__(self, bus, self.path)

    def get_properties(self):
        properties = dict()
        properties['Type'] = self.ad_type
        if self.service_uuids is not None:
            properties['ServiceUUIDs'] = dbus.Array(self.service_uuids,
                                                    signature='s')
        if self.solicit_uuids is not None:
            properties['SolicitUUIDs'] = dbus.Array(self.solicit_uuids,
                                                    signature='s')
        if self.manufacturer_data is not None:
            properties['ManufacturerData'] = dbus.Dictionary(
                self.manufacturer_data, signature='qv')
        if self.service_data is not None:
            properties['ServiceData'] = dbus.Dictionary(self.service_data,
                                                        signature='sv')
        if self.local_name is not None:
            properties['LocalName'] = dbus.String(self.local_name)
        if self.include_tx_power:
            properties['Includes'] = dbus.Array(["tx-power"], signature='s')

        if self.data is not None:
            properties['Data'] = dbus.Dictionary(
                self.data, signature='yv')
        return {LE_ADVERTISEMENT_IFACE: properties}

    def get_path(self):
        return dbus.ObjectPath(self.path)

    def add_service_uuid(self, uuid):
        if not self.service_uuids:
            self.service_uuids = []
        self.service_uuids.append(uuid)

    def add_solicit_uuid(self, uuid):
        if not self.solicit_uuids:
            self.solicit_uuids = []
        self.solicit_uuids.append(uuid)

    def add_manufacturer_data(self, manuf_code, data):
        if not self.manufacturer_data:
            self.manufacturer_data = dbus.Dictionary({}, signature='qv')
        self.manufacturer_data[manuf_code] = dbus.Array(data, signature='y')

    def add_service_data(self, uuid, data):
        if not self.service_data:
            self.service_data = dbus.Dictionary({}, signature='sv')
        self.service_data[uuid] = dbus.Array(data, signature='y')

    def add_local_name(self, name):
        if not self.local_name:
            self.local_name = ""
        self.local_name = dbus.String(name)

    def add_data(self, ad_type, data):
        if not self.data:
            self.data = dbus.Dictionary({}, signature='yv')
        self.data[ad_type] = dbus.Array(data, signature='y')

    @dbus.service.method(DBUS_PROP_IFACE,
                         in_signature='s',
                         out_signature='a{sv}')
    def GetAll(self, interface):
        #print('GetAll')
        if interface != LE_ADVERTISEMENT_IFACE:
            raise InvalidArgsException()
        #print('returning props')
        return self.get_properties()[LE_ADVERTISEMENT_IFACE]

    @dbus.service.method(LE_ADVERTISEMENT_IFACE,
                         in_signature='',
                         out_signature='')
    def Release(self):
        print('%s: Released!' % self.path)


class Service(dbus.service.Object):
    """
    org.bluez.GattService1 interface implementation
    """
    PATH_BASE = '/org/bluez/example/service'

    def __init__(self, bus, index, uuid, primary):
        self.path = self.PATH_BASE + str(index)
        self.bus = bus
        self.uuid = uuid
        self.primary = primary
        self.characteristics = []
        dbus.service.Object.__init__(self, bus, self.path)

    def get_properties(self):
        return {
                GATT_SERVICE_IFACE: {
                        'UUID': self.uuid,
                        'Primary': self.primary,
                        'Characteristics': dbus.Array(
                                self.get_characteristic_paths(),
                                signature='o')
                }
        }

    def get_path(self):
        return dbus.ObjectPath(self.path)

    def add_characteristic(self, characteristic):
        self.characteristics.append(characteristic)

    def get_characteristic_paths(self):
        result = []
        for chrc in self.characteristics:
            result.append(chrc.get_path())
        return result

    def get_characteristics(self):
        return self.characteristics

    @dbus.service.method(DBUS_PROP_IFACE,
                         in_signature='s',
                         out_signature='a{sv}')
    def GetAll(self, interface):
        if interface != GATT_SERVICE_IFACE:
            raise InvalidArgsException()

        return self.get_properties()[GATT_SERVICE_IFACE]


class Characteristic(dbus.service.Object):
    """
    org.bluez.GattCharacteristic1 interface implementation
    """
    def __init__(self, bus, index, uuid, flags, service):
        self.path = service.path + '/char' + str(index)
        self.bus = bus
        self.uuid = uuid
        self.service = service
        self.flags = flags
        self.descriptors = []
        dbus.service.Object.__init__(self, bus, self.path)

    def get_properties(self):
        return {
                GATT_CHRC_IFACE: {
                        'Service': self.service.get_path(),
                        'UUID': self.uuid,
                        'Flags': self.flags,
                        'Descriptors': dbus.Array(
                                self.get_descriptor_paths(),
                                signature='o')
                }
        }

    def get_path(self):
        return dbus.ObjectPath(self.path)

    def add_descriptor(self, descriptor):
        self.descriptors.append(descriptor)

    def get_descriptor_paths(self):
        result = []
        for desc in self.descriptors:
            result.append(desc.get_path())
        return result

    def get_descriptors(self):
        return self.descriptors

    @dbus.service.method(DBUS_PROP_IFACE,
                         in_signature='s',
                         out_signature='a{sv}')
    def GetAll(self, interface):
        if interface != GATT_CHRC_IFACE:
            raise InvalidArgsException()

        return self.get_properties()[GATT_CHRC_IFACE]

    @dbus.service.method(GATT_CHRC_IFACE,
                        in_signature='a{sv}',
                        out_signature='ay')
    def ReadValue(self, options):
        print('Default ReadValue called, returning error')
        raise NotSupportedException()

    @dbus.service.method(GATT_CHRC_IFACE, in_signature='aya{sv}')
    def WriteValue(self, value, options):
        print('Default WriteValue called, returning error')
        raise NotSupportedException()

    @dbus.service.method(GATT_CHRC_IFACE)
    def StartNotify(self):
        print('Default StartNotify called, returning error')
        raise NotSupportedException()

    @dbus.service.method(GATT_CHRC_IFACE)
    def StopNotify(self):
        print('Default StopNotify called, returning error')
        raise NotSupportedException()

    @dbus.service.signal(DBUS_PROP_IFACE,
                         signature='sa{sv}as')
    def PropertiesChanged(self, interface, changed, invalidated):
        pass


class Descriptor(dbus.service.Object):
    """
    org.bluez.GattDescriptor1 interface implementation
    """
    def __init__(self, bus, index, uuid, flags, characteristic):
        self.path = characteristic.path + '/desc' + str(index)
        self.bus = bus
        self.uuid = uuid
        self.flags = flags
        self.chrc = characteristic
        dbus.service.Object.__init__(self, bus, self.path)

    def get_properties(self):
        return {
                GATT_DESC_IFACE: {
                        'Characteristic': self.chrc.get_path(),
                        'UUID': self.uuid,
                        'Flags': self.flags,
                }
        }

    def get_path(self):
        return dbus.ObjectPath(self.path)

    @dbus.service.method(DBUS_PROP_IFACE,
                         in_signature='s',
                         out_signature='a{sv}')
    def GetAll(self, interface):
        if interface != GATT_DESC_IFACE:
            raise InvalidArgsException()

        return self.get_properties()[GATT_DESC_IFACE]

    @dbus.service.method(GATT_DESC_IFACE,
                        in_signature='a{sv}',
                        out_signature='ay')
    def ReadValue(self, options):
        print ('Default ReadValue called, returning error')
        raise NotSupportedException()

    @dbus.service.method(GATT_DESC_IFACE, in_signature='aya{sv}')
    def WriteValue(self, value, options):
        print('Default WriteValue called, returning error')
        raise NotSupportedException()


class Application(dbus.service.Object):
    """
    org.bluez.GattApplication1 interface implementation
    """
    def __init__(self, bus,data_process):
        self.path = '/'
        self.services = []
        self.data_process = data_process
        dbus.service.Object.__init__(self, bus, self.path)
        self.add_service(TestService(bus, 0,self.data_process))

    def get_path(self):
        return dbus.ObjectPath(self.path)

    def add_service(self, service):
        self.services.append(service)

    @dbus.service.method(DBUS_OM_IFACE, out_signature='a{oa{sa{sv}}}')
    def GetManagedObjects(self):
        response = {}
        print('GetManagedObjects')

        for service in self.services:
            response[service.get_path()] = service.get_properties()
            chrcs = service.get_characteristics()
            for chrc in chrcs:
                response[chrc.get_path()] = chrc.get_properties()
                descs = chrc.get_descriptors()
                for desc in descs:
                    response[desc.get_path()] = desc.get_properties()

        return response


class TestAdvertisement(Advertisement):

    def __init__(self, bus, index,data_process):
        Advertisement.__init__(self, bus, index, 'peripheral')
        # self.add_service_uuid('180D')
        # self.add_service_uuid('180F')
        self.add_manufacturer_data(data_process.manuf_code, data_process.manuf_data)
        self.add_service_data('9999', [0x00, 0x01, 0x02])
        self.add_local_name(data_process.ble_adv_localname)
        self.include_tx_power = True
        self.add_data(0x26, [0x01, 0x01, 0x00])
        

    def set_manufacturer_data(self, manuf_code,data):
        self.add_manufacturer_data(manuf_code,data)
    def set_local_name(self, name):
        self.add_local_name(name)

class TestService(Service):
    """
    Dummy test service that provides characteristics and descriptors that
    exercise various API functionality.

    """
    TEST_SVC_UUID = '0000D459-0000-1000-8000-00805f9b34fb'

    def __init__(self, bus, index,data_process):
        Service.__init__(self, bus, index, self.TEST_SVC_UUID, True)
        self.data_process = data_process
        self.add_characteristic(WriteCharacteristic(bus, 0, self,self.data_process))
        self.add_characteristic(NotifyCharacteristic(bus, 1, self,self.data_process))
        self.add_characteristic(IndicateCharacteristic(bus, 2, self,self.data_process))
        # self.add_characteristic(TestEncryptCharacteristic(bus, 1, self))
        # self.add_characteristic(TestSecureCharacteristic(bus, 2, self))

class WriteCharacteristic(Characteristic):
    """
    Dummy test characteristic. Allows writing arbitrary bytes to its value, and
    contains "extended properties", as well as a test descriptor.

    """
    TEST_CHRC_UUID = '00000013-0000-1000-8000-00805f9b34fb'

    def __init__(self, bus, index, service,data_process):
        Characteristic.__init__(
                self, bus, index,
                self.TEST_CHRC_UUID,
                ['write'],
                service)
        self.data_process = data_process
        self.value = []
        self.add_descriptor(WriteDescriptor(bus, 0, self))
        self.add_descriptor(
                CharacteristicUserDescriptionDescriptor(bus, 1, self))

    def ReadValue(self, options):
        print('TestCharacteristic Read: ' + repr(self.value))
        return self.value

    def WriteValue(self, value, options):
        self.data_process.ble_write_characteristic_data_process(value)
        # print('TestCharacteristic Write: ' + repr(value))
        # self.value = value

class WriteDescriptor(Descriptor):
    """
    Dummy test descriptor. Returns a static value.

    """
    TEST_DESC_UUID = '12345678-1234-5678-1234-56789abcdef2'

    def __init__(self, bus, index, characteristic):
        Descriptor.__init__(
                self, bus, index,
                self.TEST_DESC_UUID,
                ['read', 'write'],
                characteristic)

    def ReadValue(self, options):
        return [
                dbus.Byte('T'), dbus.Byte('e'), dbus.Byte('s'), dbus.Byte('t')
        ]


class NotifyCharacteristic(Characteristic):
    """
    Dummy test characteristic. Allows writing arbitrary bytes to its value, and
    contains "extended properties", as well as a test descriptor.

    """
    TEST_CHRC_UUID = '00000014-0000-1000-8000-00805f9b34fb'

    def __init__(self, bus, index, service,data_process):
        Characteristic.__init__(
                self, bus, index,
                self.TEST_CHRC_UUID,
                ['read', 'notify'],
                service)
        self.data_process = data_process
        self.data_process.set_notify_characteristic_data(self)
        self.value = []
        self.battery_lvl = 100
        self.notifying = False
        # GLib.timeout_add(5000, self.drain_battery)
        self.add_descriptor(NotifyDescriptor(bus, 0, self))
        self.add_descriptor(
                CharacteristicUserDescriptionDescriptor(bus, 1, self))
    def notify_battery_level(self):
        if not self.notifying:
            return
        self.PropertiesChanged(
                GATT_CHRC_IFACE,
                { 'Value': dbus.ByteArray(b'11223344')}, [])

    def drain_battery(self):
        if not self.notifying:
            return True
        if self.battery_lvl > 0:
            self.battery_lvl -= 2
            if self.battery_lvl < 0:
                self.battery_lvl = 0
        print('Battery Level drained: ' + repr(self.battery_lvl))
        self.notify_battery_level()
        return True

    def ReadValue(self, options):
        print('TestCharacteristic Read: ' + repr(self.value))
        return self.value

    def WriteValue(self, value, options):
        print('TestCharacteristic Write: ' + repr(value))
        self.value = value

    def StartNotify(self):
        if self.notifying:
            print('Already notifying, nothing to do')
            return

        self.notifying = True
        print('Notifying')
        # self.notify_battery_level()

    def StopNotify(self):
        if not self.notifying:
            print('Not notifying, nothing to do')
            return
        print('stop notification')
        self.notifying = False
    def notify_update(self, value):
        if not self.notifying:
            return
        self.PropertiesChanged(
                GATT_CHRC_IFACE,
                { 'Value': dbus.ByteArray(value) }, [])

class NotifyDescriptor(Descriptor):
    """
    Dummy test descriptor. Returns a static value.

    """
    TEST_DESC_UUID = '12345678-1234-5678-1234-56789abcdef2'

    def __init__(self, bus, index, characteristic):
        Descriptor.__init__(
                self, bus, index,
                self.TEST_DESC_UUID,
                ['read', 'write'],
                characteristic)

    def ReadValue(self, options):
        return [
                dbus.Byte('T'), dbus.Byte('e'), dbus.Byte('s'), dbus.Byte('t')
        ]


class IndicateCharacteristic(Characteristic):
    """
    Dummy test characteristic. Allows writing arbitrary bytes to its value, and
    contains "extended properties", as well as a test descriptor.

    """
    TEST_CHRC_UUID = '00000015-0000-1000-8000-00805f9b34fb'

    def __init__(self, bus, index, service,data_process):
        Characteristic.__init__(
                self, bus, index,
                self.TEST_CHRC_UUID,
                ['indicate'],
                service)
        self.data_process = data_process
        self.value = []
        self.add_descriptor(IndicateDescriptor(bus, 0, self))
        self.add_descriptor(
                CharacteristicUserDescriptionDescriptor(bus, 1, self))

    def ReadValue(self, options):
        print('TestCharacteristic Read: ' + repr(self.value))
        return self.value

    def WriteValue(self, value, options):
        print('TestCharacteristic Write: ' + repr(value))
        self.value = value

class IndicateDescriptor(Descriptor):
    """
    Dummy test descriptor. Returns a static value.

    """
    TEST_DESC_UUID = '12345678-1234-5678-1234-56789abcdef2'

    def __init__(self, bus, index, characteristic):
        Descriptor.__init__(
                self, bus, index,
                self.TEST_DESC_UUID,
                ['read', 'write'],
                characteristic)

    def ReadValue(self, options):
        return [
                dbus.Byte('T'), dbus.Byte('e'), dbus.Byte('s'), dbus.Byte('t')
        ]

class CharacteristicUserDescriptionDescriptor(Descriptor):
    """
    Writable CUD descriptor.

    """
    CUD_UUID = '2901'

    def __init__(self, bus, index, characteristic):
        self.writable = 'writable-auxiliaries' in characteristic.flags
        self.value = array.array('B', b'This is a characteristic for testing')
        self.value = self.value.tolist()
        Descriptor.__init__(
                self, bus, index,
                self.CUD_UUID,
                ['read', 'write'],
                characteristic)

    def ReadValue(self, options):
        return self.value

    def WriteValue(self, value, options):
        if not self.writable:
            raise NotPermittedException()
        self.value = value



class Ble_manager:
    def __init__(self,data_processor_t,logger = None,is_custom = False) -> None:

        if logger == None:
            self.log_t = CustomLogger('Ble_manager', 'debug')
        else:
            self.log_t = logger

        dbus.mainloop.glib.DBusGMainLoop(set_as_default=True)

        self.bus = dbus.SystemBus()

        self.adapter = self.find_adapter(self.bus)
        if not self.adapter:
            self.log_t.warning('LEAdvertisingManager1 interface not found')
            raise 'LEAdvertisingManager1 interface not found'

        self.adapter2 = self.find_adapter2(self.bus)
        if not self.adapter:
            self.log_t.warning('GATT_MANAGER_IFACE interface not found')
            raise 'LEAdvertisingManager1 interface not found'
        
        # adapter_props = dbus.Interface(self.bus.get_object(BLUEZ_SERVICE_NAME, self.adapter),
        #                            "org.freedesktop.DBus.Properties")

        # adapter_props.Set("org.bluez.Adapter1", "Powered", dbus.Boolean(0))
        # time.sleep(0.2)
        # adapter_props.Set("org.bluez.Adapter1", "Powered", dbus.Boolean(1))

        self.ad_manager = None
        self.app = None
        self.data_processor = data_processor_t
        
        self.test_advertisement = None



    def register_ad_cb(self):
        self.log_t.info('Advertisement registered')
        # print('Advertisement registered')


    def register_ad_error_cb(self,error):
        self.log_t.info('Failed to register advertisement: ' + str(error))
        # print('Failed to register advertisement: ' + str(error))
        mainloop.quit()

    
    def register_app_cb(self):
        print('GATT application registered')


    def register_app_error_cb(self,error):
        print('Failed to register application: ' + str(error))
        self.mainloop.quit()

    def find_adapter(self,bus):
        remote_om = dbus.Interface(bus.get_object(BLUEZ_SERVICE_NAME, '/'),
                                DBUS_OM_IFACE)
        objects = remote_om.GetManagedObjects()

        for o, props in objects.items():
            if LE_ADVERTISING_MANAGER_IFACE in props:
                return o
        return None
    
    def find_adapter2(self,bus):
        remote_om = dbus.Interface(bus.get_object(BLUEZ_SERVICE_NAME, '/'),
                               DBUS_OM_IFACE)
        objects = remote_om.GetManagedObjects()

        for o, props in objects.items():
            if GATT_MANAGER_IFACE in props.keys():
                return o
        return None
    

    def shutdown(self,timeout):
        print('Advertising for {} seconds...'.format(timeout))
        time.sleep(timeout)
        self.mainloop.quit()
    

    def ble_manager_start(self):
        self.mainloop = GLib.MainLoop()
        self.mainloop.run()

    def set_adapter_power(self, value, retries=5, delay=3):
        """
        Set the power state of the Bluetooth adapter with retries on failure using the DBus.Properties interface.
        """
        try:
            adapter_props = dbus.Interface(self.bus.get_object(BLUEZ_SERVICE_NAME, self.adapter),
                                        "org.freedesktop.DBus.Properties")
            adapter_props.Set("org.bluez.Adapter1", "Powered", dbus.Boolean(value))
            self.log_t.info(f"Adapter power set to:{value}")
        except dbus.exceptions.DBusException as e:
            print(f"Failed to set adapter power due to: {e.get_dbus_message()} at {e.get_dbus_name()}")
            if retries > 0:
                print("Retrying in", delay, "seconds...")
                time.sleep(delay)
                self.set_adapter_power(value, retries-1, delay)
            else:
                print("Failed to set adapter power after retries:", e)

    def custom_advertisement_register(self, index=0, adv_type='peripheral',
                                      localName='TestAdvertisement',
                                      manuf_code=0xffff, manuf_data=[0x00, 0x01, 0x02, 0x03],
                                      serviceUUID='180D', is_include_tx_power=True):
        # Set adapter power off and on with retries
        self.set_adapter_power(False)
        time.sleep(3)
        self.set_adapter_power(True)
        time.sleep(3)

        # 注销之前的广告，防止崩溃
        try:
            if self.test_advertisement is not None:
                self.unregister_adv(self.test_advertisement)
        except dbus.exceptions.DBusException as e:
            log_t.error(f"unregister_adv 发生 D-Bus 异常: {e}")
        except Exception as e:
            log_t.error(f"unregister_adv 发生未知的异常: {e}")

        # 注册新的蓝牙广告
        try:
            self.test_advertisement = TestAdvertisement(self.bus, 0, self.data_processor)
            self.ad_manager = dbus.Interface(self.bus.get_object(BLUEZ_SERVICE_NAME, self.adapter),
                                            LE_ADVERTISING_MANAGER_IFACE)
            self.ad_manager.RegisterAdvertisement(self.test_advertisement.get_path(), {},
                                                reply_handler=self.register_ad_cb,
                                                error_handler=self.register_ad_error_cb)
        except dbus.exceptions.DBusException as e:
            log_t.error(f"RegisterAdvertisement 发生 D-Bus 异常: {e}")
        except Exception as e:
            log_t.error(f"RegisterAdvertisement 发生未知的异常: {e}")

    def custom_app_register(self):    
        self.app = Application(self.bus,self.data_processor)
        service_manager = dbus.Interface(self.bus.get_object(BLUEZ_SERVICE_NAME, self.adapter2),
                                GATT_MANAGER_IFACE)
        service_manager.RegisterApplication(self.app.get_path(), {},
                                        reply_handler=self.register_app_cb,
                                        error_handler=self.register_app_error_cb) 

    def custom_app_addsrv(self,service):
        self.app.add_service(service)


    def create_service(self,index = 0, UUID = '0000D459-0000-1000-8000-00805f9b34fb',primary = True):
        return  Service(self.bus, index, UUID, primary)

    def add_charct(self,service,charact):   
        service.add_characteristic(charact)


    def unregister_adv(self,adv):
        self.ad_manager.UnregisterAdvertisement(adv)
        print('Advertisement unregistered')
        dbus.service.Object.remove_from_connection(adv)


class DRYiDongService(Service):

    UUID = '0000D459-0000-1000-8000-00805f9b34fb'

    def __init__(self, bus, index):
        Service.__init__(self, bus, index, self.UUID, True)
        self.add_characteristic(DRYiDongWriteChrc(bus, 0, self))
        self.add_characteristic(DRYiDongNotifyChrc(bus, 1, self))
        self.add_characteristic(DRYiDongIndicateyChrc(bus, 2, self))
        self.energy_expended = 0


class DRYiDongNotifyChrc(Characteristic):
    LOC_UUID = '00000014-0000-1000-8000-00805f9b34fb'

    def __init__(self, bus, index, service):
        Characteristic.__init__(self, bus, index,self.LOC_UUID,['notify'],service)
        self.notifying = False
        self.hr_ee_count = 0
        self.test = 0
        
    def hr_msrmt_cb(self):
        value = []
        if self.test == 0:
            self.test = self.test + 1
            return self.notifying
        if self.test == 1:
            time.sleep(5)
        self.test = self.test + 1

        #time.sleep(0)
        if self.test < 3:
            value.append(dbus.Byte(0x02))
            value.append(dbus.Byte(0x01))
        elif 3 <= self.test < 4: 
            time.sleep(3)
            value.append(dbus.Byte(0x02))
            value.append(dbus.Byte(0x01))
        elif 4 <= self.test < 5:
            value.append(dbus.Byte(0x02))
            value.append(dbus.Byte(0x02))
        elif 5 <= self.test < 6:
            value.append(dbus.Byte(0x02))
            value.append(dbus.Byte(0x04))
        elif 6 <= self.test < 7:
            value.append(dbus.Byte(0x02))
            value.append(dbus.Byte(0x06))
        elif 7 <= self.test:
            value.append(dbus.Byte(0x02))
            value.append(dbus.Byte(0x08))
        #value.append(dbus.Byte(randint(90, 130)))
        # log_t.debug(f"recv bluetooth data:{self.test}")
        print(self.test)
        #if self.hr_ee_count % 10 == 0:
        #    value[0] = dbus.Byte(value[0] | 0x08)
        #    value.append(dbus.Byte(self.service.energy_expended & 0xff))
        #    value.append(dbus.Byte((self.service.energy_expended >> 8) & 0xff))



        # self.service.energy_expended = \
        #         min(0xffff, self.service.energy_expended + 1)
        self.hr_ee_count += 1

        print('DRYiDongNotifyChrc Updating value: ' + repr(value))

        self.PropertiesChanged(GATT_CHRC_IFACE, { 'Value': value }, [])

        return self.notifying

    def _update_hr_msrmt_simulation(self):
        print('Update  Simulation')

        if not self.notifying:
            return

        # GLib.timeout_add(1000, self.hr_msrmt_cb)  #开启定时器任务

    def StartNotify(self):
        if self.notifying:
            print('Already notifying, nothing to do')
            return

        self.notifying = True
        print(f'DRYiDongNotifyChrc:StartNotify')
        self._update_hr_msrmt_simulation()
        

    def StopNotify(self):
        if not self.notifying:
            print('Not notifying, nothing to do')
            return

        self.notifying = False
        print(f'DRYiDongNotifyChrc:StopNotify')
        self._update_hr_msrmt_simulation()


class DRYiDongIndicateyChrc(Characteristic):
    LOC_UUID = '00000015-0000-1000-8000-00805f9b34fb'

    def __init__(self, bus, index, service):
        Characteristic.__init__(self, bus, index, self.LOC_UUID,['indicate'],service)
        self.notifying = False
        self.hr_ee_count = 0

    def hr_msrmt_cb(self):
        value = []
        value.append(dbus.Byte(0x06))

        value.append(dbus.Byte(randint(90, 130)))

        if self.hr_ee_count % 10 == 0:
            value[0] = dbus.Byte(value[0] | 0x08)
            value.append(dbus.Byte(self.service.energy_expended & 0xff))
            value.append(dbus.Byte((self.service.energy_expended >> 8) & 0xff))

        self.service.energy_expended = \
                min(0xffff, self.service.energy_expended + 1)
        self.hr_ee_count += 1

        print('DRYiDongIndicateyChrc Updating value: ' + repr(value))

        self.PropertiesChanged(GATT_CHRC_IFACE, { 'Value': value }, [])

        return self.notifying

    def _update_hr_msrmt_simulation(self):
        print('Update HR Measurement Simulation')

        if not self.notifying:
            return

        GLib.timeout_add(1000, self.hr_msrmt_cb)

    def StartIndicate(self):
        if self.notifying:
            print('Already notifying, nothing to do')
            return

        self.notifying = True
        self._update_hr_msrmt_simulation()

    def StopIndicate(self):
        if not self.notifying:
            print('Not notifying, nothing to do')
            return

        self.notifying = False
        self._update_hr_msrmt_simulation()


    def StartNotify(self):
        if self.notifying:
            print('Already notifying, nothing to do')
            return

        print("DRYiDongIndicateyChrc : StartNotify")
        # self.notifying = True
        # print(f'DRYiDongNotifyChrc:StartNotify')
        # self._update_hr_msrmt_simulation()
        

    def StopNotify(self):
        if not self.notifying:
            print('Not notifying, nothing to do')
            return

        print("DRYiDongIndicateyChrc : StopNotify")
        # self.notifying = False
        # print(f'DRYiDongNotifyChrc:StopNotify')
        # self._update_hr_msrmt_simulation()

##信息传输回调
class DRYiDongWriteChrc(Characteristic):
    UUID = '00000013-0000-1000-8000-00805f9b34fb'

    def __init__(self, bus, index, service):
        Characteristic.__init__(self, bus, index,self.UUID,['write'],service)
        self.test = 0
        self.shuju = 0
        self.jiami = 0
        self.chongfa = 0
        self.zhenshu = 0
        self.length = 0
        self.CRC32 = []
        self.value = []

    def set_op(self, value):
        self.shuju = int(value[0])
        self.zhenshu = int(value[4])
        self.length = int(value[5]) * 256 + int(value[6])
        self.CRC32.append(value[7])
        self.CRC32.append(value[8])
        self.CRC32.append(value[9])
        self.CRC32.append(value[10])
        if int(value[2]) == 0:
            self.jiami = 0
            self.chongfa = 0
        if int(value[2]) == 16:
            self.jiami = 0
            self.chongfa = 1
        if int(value[2]) == 32:
            self.jiami = 0
            self.chongfa = 2
        if int(value[2]) == 64:
            self.jiami = 1
            self.chongfa = 0
        if int(value[2]) == 80:
            self.jiami = 1 
            self.chongfa = 1
        if int(value[2]) == 96:
            self.jiami = 1
            self.chongfa = 2
        if int(value[2]) == 128:
            self.jiami = 2
            self.chongfa = 0
        if int(value[2]) == 144:
            self.jiami = 2
            self.chongfa = 1
        if int(value[2]) == 160:   
            self.jiami = 2
            self.chongfa = 2 
        print(self.shuju)
        print(self.jiami)
        print(self.chongfa)
        print(self.zhenshu)
        print(self.length)


    def add_values(self, value):
        for i, byte in enumerate(value):
            if i >= 2:
                self.value.append(int(value[i]))
                print(chr(int(value[i])))

    # def configure_wifi(self, ssid, password):
    #     try:
    #         subprocess.run(['nmcli', 'device', 'wifi', 'connect', ssid, 'password', password], check=True)
    #         print(f"Successfully connected to {ssid}")
    #     except subprocess.CalledProcessError as e:
    #         print(f"Failed to connect to {ssid}: {e}")

    def WriteValue(self, value, options):
        # print('got values :')
        print(f"WriteValue:{value}")
        # 提取每个字节的值
        byte_values = [byte for byte in value]
        # 转换为 Python 字节数组
        byte_array = bytes(byte_values)
        # 解码为字符串
        string_value = byte_array.decode('utf-8')
        print(f"value parse:{string_value}")
        print('#################')

    def StartIndicate(self):
        if self.notifying:
            print('DRYiDongWriteChrc: Already notifying, nothing to do')
            return

        self.notifying = True
        print('DRYiDongWriteChrc StartIndicate')
        # self._update_hr_msrmt_simulation()

    def StopIndicate(self):
        if not self.notifying:
            print('DRYiDongWriteChrc: Not notifying, nothing to do')
            return

        self.notifying = False
        print('DRYiDongWriteChrc StopIndicate')
        # self._update_hr_msrmt_simulation()


    def StartNotify(self):
        if self.notifying:
            print('Already notifying, nothing to do')
            return

        self.notifying = True
        print(f'DRYiDongWriteChrc:StartNotify')
        # self._update_hr_msrmt_simulation()
        

    def StopNotify(self):
        if not self.notifying:
            print('Not notifying, nothing to do')
            return

        self.notifying = False
        print(f'DRYiDongWriteChrc:StopNotify')
        # self._update_hr_msrmt_simulation()

def ble_test_task():
    ble_t = Ble_manager()
    service_t = ble_t.create_service(index = 0, UUID = '0000D459-0000-1000-8000-00805f9b34fb',primary = True )
    ble_t.add_charct(index =0,uuid='00000014-0000-1000-8000-00805f9b34fb',flags=['notify'],service = service_t)
    ble_t.add_charct(index =1,uuid='00000015-0000-1000-8000-00805f9b34fb',flags=['indicate'],service = service_t)
    ble_t.add_charct(index =2,uuid='00000013-0000-1000-8000-00805f9b34fb',flags=['write'],service = service_t)
    
    ble_t.custom_app_register()
    ble_t.custom_app_addsrv(service_t)
    ble_t.custom_advertisement_register()
    ble_t.ble_manager_start()


def zgyd_ble_test_task():
    ble_t = Ble_manager()
    service_t = ble_t.create_service(index = 0, UUID = '0000D459-0000-1000-8000-00805f9b34fb',primary = True )
    ble_t.add_charct(service = service_t,charact= DRYiDongWriteChrc(ble_t.bus, 0, service_t))
    ble_t.add_charct(service = service_t,charact= DRYiDongNotifyChrc(ble_t.bus, 1, service_t))
    ble_t.add_charct(service = service_t,charact= DRYiDongIndicateyChrc(ble_t.bus, 2, service_t))

    ble_t.custom_app_register()
    ble_t.custom_app_addsrv(service_t) #'CMB2320647-625A'
    ble_t.custom_advertisement_register(index=0,localName='CMB30135-625A',
                                        manuf_code=0xC32C,manuf_data=[0xE6, 0xE3, 0x62, 0x5A, 0x01],
                                        serviceUUID='D459')
    ble_t.ble_manager_start()

# if __name__ == '__main__':
#     parser = argparse.ArgumentParser()
#     parser.add_argument('--timeout', default=0, type=int, help="advertise " +
#                         "for this many seconds then stop, 0=run forever " +
#                         "(default: 0)")
#     args = parser.parse_args()


#     time_out = args.timeout

#     # ble_test_task()

#     zgyd_ble_test_task()
#     # main(args.timeout)


def register_ad_cb():
    print('Advertisement registered')


def register_ad_error_cb(error):
    print('Failed to register advertisement: ' + str(error))
    mainloop.quit()

def register_app_cb():
    print('GATT application registered')


def register_app_error_cb(error):
    print('Failed to register application: ' + str(error))
    mainloop.quit()



def find_adapter(bus):
    remote_om = dbus.Interface(bus.get_object(BLUEZ_SERVICE_NAME, '/'),
                               DBUS_OM_IFACE)
    objects = remote_om.GetManagedObjects()

    for o, props in objects.items():
        if LE_ADVERTISING_MANAGER_IFACE in props:
            return o

    return None


def find_adapter2(bus):
    remote_om = dbus.Interface(bus.get_object(BLUEZ_SERVICE_NAME, '/'),
                               DBUS_OM_IFACE)
    objects = remote_om.GetManagedObjects()

    for o, props in objects.items():
        if GATT_MANAGER_IFACE in props.keys():
            return o

    return None


def shutdown(timeout):
    print('Advertising for {} seconds...'.format(timeout))
    time.sleep(timeout)
    mainloop.quit()


def ble_start(data_process,timeout=0):
    global mainloop

    dbus.mainloop.glib.DBusGMainLoop(set_as_default=True)

    bus = dbus.SystemBus()

    adapter = find_adapter(bus)
    if not adapter:
        print('LEAdvertisingManager1 interface not found')
        return

    adapter_props = dbus.Interface(bus.get_object(BLUEZ_SERVICE_NAME, adapter),
                                   "org.freedesktop.DBus.Properties")


    adapter_props.Set("org.bluez.Adapter1", "Powered", dbus.Boolean(0))
    time.sleep(0.2)
    adapter_props.Set("org.bluez.Adapter1", "Powered", dbus.Boolean(1))

    # adapter_props.Set("org.bluez.Adapter1", "Powered", dbus.Boolean(1))

    ad_manager = dbus.Interface(bus.get_object(BLUEZ_SERVICE_NAME, adapter),
                                LE_ADVERTISING_MANAGER_IFACE)

    # if test_advertisement != None:
    #     ad_manager.UnregisterAdvertisement(test_advertisement)
    test_advertisement = TestAdvertisement(bus, 0,data_process)

    # mainloop = GObject.MainLoop()

    ad_manager.RegisterAdvertisement(test_advertisement.get_path(), {},
                                     reply_handler=register_ad_cb,
                                     error_handler=register_ad_error_cb)

    if timeout > 0:
        threading.Thread(target=shutdown, args=(timeout,)).start()
    else:
        print('Advertising forever...')

    adapter2 = find_adapter2(bus)
    if not adapter2:
        print('GattManager1 interface not found')
        return

    service_manager = dbus.Interface(
            bus.get_object(BLUEZ_SERVICE_NAME, adapter2),
            GATT_MANAGER_IFACE)

    app = Application(bus,data_process)

    mainloop = GLib.MainLoop()

    print('Registering GATT application...')

    service_manager.RegisterApplication(app.get_path(), {},
                                    reply_handler=register_app_cb,
                                    error_handler=register_app_error_cb)

    mainloop.run()  # blocks until mainloop.quit() is called

    ad_manager.UnregisterAdvertisement(test_advertisement)
    print('Advertisement unregistered')
    dbus.service.Object.remove_from_connection(test_advertisement)

    
def update_adv(data_process):
    bus = dbus.SystemBus()

    adapter = find_adapter(bus)
    if not adapter:
        print('LEAdvertisingManager1 interface not found')
        return

    adapter_props = dbus.Interface(bus.get_object(BLUEZ_SERVICE_NAME, adapter),
                                   "org.freedesktop.DBus.Properties")


    adapter_props.Set("org.bluez.Adapter1", "Powered", dbus.Boolean(0))
    time.sleep(0.2)
    adapter_props.Set("org.bluez.Adapter1", "Powered", dbus.Boolean(1))

    # adapter_props.Set("org.bluez.Adapter1", "Powered", dbus.Boolean(1))

    ad_manager = dbus.Interface(bus.get_object(BLUEZ_SERVICE_NAME, adapter),
                                LE_ADVERTISING_MANAGER_IFACE)

    test_advertisement = TestAdvertisement(bus, 0,data_process)

    # mainloop = GObject.MainLoop()

    ad_manager.RegisterAdvertisement(test_advertisement.get_path(), {},
                                     reply_handler=register_ad_cb,
                                     error_handler=register_ad_error_cb)
