import rospy
import json
from std_msgs.msg import String
import sys
from SockClass import TcpServer
import math
import dbus
from andlink.srv import SIGCData, SIGCDataResponse
import binascii
import zlib 
import time
import struct
import datetime
import subprocess

from andlink.msg  import SIGCEvent

from custom_logger import CustomLogger

def list_to_hex_string(list_data):
    list_str = '[ '
    for x in list_data:
        list_str += '0x{:02X},'.format(x)
    list_str += ' ]'
    return list_str

def get_timestamp_bytes():
    # 获取当前的时间戳
    current_timestamp = int(time.time())
    
    # 将整数时间戳转换为大端字节流
    bytes_stream = struct.pack('>I', current_timestamp)
    
    # 将字节流转换为十六进制列表
    return list(bytes_stream)



class DataProcessor:
    def __init__(self,ble_adv_localname,manuf_code,manuf_data,deviceId,ble_manager=None):

        # ROS节点初始化
        rospy.init_node('rob_dog_commander', anonymous=True)

        self.log_t = CustomLogger('DataProcessor', 'debug')
        # 创建一个发布者
        self.publisher = rospy.Publisher('/homi_speech/sigc_event_topic_APP', SIGCEvent, queue_size=10)

        # 创建服务服务器，等待对面节点发送的服务请求
        service = rospy.Service('/homi_speech/sigc_data_service_APP', SIGCData, self.handle_service_request)

        self.tcp_socket = None
        self.client_socket = None
        self.client_address = None
        self.deviceId = deviceId
        self.device_login_status = 0
        self.bind_data_frame_num = -1
        self.control_data_frame_num = -1
        self.control_data_mult_frame = 0
        self.control_first_frame_data = None
        self.control_data_frame_data = []

        self.notify_characteristic_data = None
        self.ble_adv_localname = ble_adv_localname
        self.manuf_code = manuf_code
        self.manuf_data = manuf_data

        self.andlink_plugin_connection = 0

        self.ble_manager = ble_manager

        self.sigcevent_msg=SIGCEvent()

    def set_ble_adv_localname(self,localname):
        self.ble_adv_localname = localname

    def set_ble_adv_manuf_data(self,manuf_code,manuf_data):
        self.manuf_code = manuf_code
        self.manuf_data = manuf_data

    def set_ble_json_deviceId(self,deviceId):
        self.deviceId = deviceId

    def set_tcp_socket(self, socket,client_socket,client_address):
        self.tcp_socket = socket
        self.client_socket = client_socket
        self.client_address = client_address
    def get_tcp_socket(self):
        return self.tcp_socket

    def set_notify_characteristic_data(self, characteristic_data): 
        self.notify_characteristic_data = characteristic_data


    def ble_write_characteristic_data_process(self, data):

        # 获取当前时间，精确到毫秒
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        
        # 使用获取的时间来格式化日志信息
        print(f"{current_time} - receive a frame: {list_to_hex_string(data)}")

        if data[0] == 0 and data[1] == 0:   #配网相关命令
            self.bind_data_frame_num = int(data[4])+1
        else:
            if data[0] == 0 and data[2] == 0xaa and data[3] == 0x02:
                self.control_first_frame_data = data
                self.control_data_frame_num = data[1]
                self.control_data_frame_data = data[9:]
            elif data[0] == 0 and data[2] == 0xc1 and data[3] == 0x02:     #时间同步更新
                self.control_first_frame_data = data
                self.control_data_frame_num = data[1]
                self.control_data_frame_data = data[9:]
            elif data[0] == 0 and data[2] == 0xc1 and data[3] == 0x06:     #控制命令更新
                self.control_first_frame_data = data
                self.control_data_frame_num = data[1]
                self.control_data_frame_data = data[9:]
            elif data[0] == 0 and data[2] == 0xc2 and data[3] == 0x05:     #状态获取命令更新
                self.control_first_frame_data = data
                self.control_data_frame_num = data[1]
                self.control_data_frame_data = data[9:]
            else:
                self.control_data_frame_data.extend(data[1:])

        if self.bind_data_frame_num > 0:
        # 提取每个字节的值
            if self.client_socket != None:
                byte_values = [byte for byte in data]
        # 转换为 Python 字节数组
                byte_array = bytes(byte_values)
                self.tcp_socket.send_to_client(self.client_socket,byte_array)
            else:
                print("Client socket is None,can't send package to andlink")

            if self.bind_data_frame_num == 0:
                self.ble_notify_characteristic_data_process(b'00')
            self.bind_data_frame_num -= 1

        if self.control_data_frame_num > 0:
            self.control_data_frame_num -= 1
            if self.control_data_frame_num == 0:
                if self.control_first_frame_data[2] == 0xaa and self.control_first_frame_data[3] == 0x02:
                #TODO
                    self.ble_notify_characteristic_data_login_status_response()  #登陆状态上传
                #TODO
                # elif self.device_login_status == 0:
                    #self.ble_notify_characteristic_data_control_error_nologin_response()
                else:
                    if self.control_first_frame_data[2] == 0xc1:
                        if self.control_first_frame_data[3] == 0x02:      #时间同步
                        #TODO
                            self.ble_notify_characteristic_data_settime_response()
                        elif self.control_first_frame_data[3] == 0x06:    #控制命令
                            self.ble_publish_data_process()
                            self.ble_notify_characteristic_data_control_response()
                    elif self.control_first_frame_data[2] == 0xc2:    #设备查询
                        if self.control_first_frame_data[3] == 0x05:
                            self.ble_publish_data_process()
                            # self.ble_notify_characteristic_data_status_response()

    def ble_notify_characteristic_data_login_status_response(self):
        print(sys._getframe().f_code.co_name)
        if len(self.control_first_frame_data) > 9:
            response_all_list = self.control_first_frame_data[:9]
            #TODO
            #response_result_list = [0x01,0x88,0x12,0x34,0x12,0x34]
            #response_all_list.extend(response_result_list)
            response_all_list[1] = 0x01
            response_all_list.append(0x01)
            response_all_list[5:9] = get_timestamp_bytes()
            self.ble_notify_characteristic_data_process(response_all_list)
            self.device_login_status = 1
        else:
            print("receice frame format is error")

    def ble_notify_characteristic_data_status_response(self, response_all_data_string):
        print(sys._getframe().f_code.co_name)
        if len(self.control_first_frame_data) > 9:
            #response_all_data_string='{\"eventId\": \"12345678\",\"body\": {}}'

            response_all_data_bytes=response_all_data_string.encode("utf-8")

            response_all_list = self.control_first_frame_data[:9]

            response_all_data_bytes_len = len(response_all_data_bytes)

            frame_num = (response_all_data_bytes_len-9)/19

            frame_num = math.ceil(frame_num)

            response_all_list[1]= frame_num + 1

            response_all_list[5:9] = get_timestamp_bytes()
            response_all_list[9:11] = list(response_all_data_bytes_len.to_bytes(2, byteorder='little'))
            response_all_list[11:20] = response_all_data_bytes[0:9]
            self.ble_notify_characteristic_data_process(response_all_list)
            i = 1
            while frame_num > 0:
                # time.sleep(1)
                frame_num -= 1
                response_all_list=[i]
                response_all_list.extend(response_all_data_bytes[9+19*(i-1):9+19*(i-1)+19])
                i += 1
                self.ble_notify_characteristic_data_process(response_all_list)
        else:
            print("receice frame format is error")
    
    def ble_notify_characteristic_data_settime_response(self):
        #TODO  数据获取
        print(sys._getframe().f_code.co_name)
        if len(self.control_first_frame_data) > 9:
            response_all_list = self.control_first_frame_data[:9]
            #TODO
            response_result_list = [0x01]
            response_all_list.extend(response_result_list)
            response_all_list[5:9] = get_timestamp_bytes()
            self.ble_notify_characteristic_data_process(response_all_list)
        else:
            print("receice frame format is error")

    def ble_notify_characteristic_data_control_response(self):
        #TODO  数据获取
        print(sys._getframe().f_code.co_name)
        if len(self.control_first_frame_data) > 9:
            response_all_list = self.control_first_frame_data[:9]
            #TODO
            response_result_list = [0x01]
            response_all_list.extend(response_result_list)
            response_all_list[1] = 0x01
            response_all_list[5:9] = get_timestamp_bytes()
            self.ble_notify_characteristic_data_process(response_all_list)
        else:
            print("receice frame format is error")


    def ble_notify_characteristic_data_control_error_nologin_response(self):
        print(sys._getframe().f_code.co_name)
        if len(self.control_first_frame_data) > 9:
            response_all_list = [0x00,0x01,0x00,0x02]
            response_all_list[5:9] = get_timestamp_bytes()
            self.ble_notify_characteristic_data_process(response_all_list)
        else:
            print("receice frame format is error")

    def ble_notify_characteristic_data_control_ack_response(self):
        print(sys._getframe().f_code.co_name)
        if len(self.control_first_frame_data) > 9:
            response_all_list = [0x00,0x01,0x00,0x00]
            response_all_list[5:9] = get_timestamp_bytes()
            self.ble_notify_characteristic_data_process(response_all_list)
        else:
            print("receice frame format is error")

    def ble_notify_characteristic_data_control_nack_response(self):
        print(sys._getframe().f_code.co_name)
        if len(self.control_first_frame_data) > 9:
            response_all_list = [0x00,0x01,0x00,0x01]
            response_all_list[5:9] = get_timestamp_bytes()
            self.ble_notify_characteristic_data_process(response_all_list)
        else:
            print("receice frame format is error")

    def ble_notify_characteristic_data_process(self, data):
        self.notify_characteristic_data.notify_update(data)
        json_string_from_bytes = bytes(self.control_data_frame_data).decode('utf-8')
        print("receive json from app:"+json_string_from_bytes)
        print(f'response a frame:{list_to_hex_string(data)}')

    def handle_service_request(self, req):
        self.log_t.info(f"Received request::{req.data}")
        # rospy.loginfo("Received request: %s", req.data)
        # 将接收到的JSON字符串解析为字典
        data = json.loads(req.data)

        if data.get("event") == "unbind_notify":
            self.log_t.info(f"Received unbind_notify")
            
            subprocess.run("rm /etc/andlink/andlinkSdk.conf", shell=True, check=True)
            subprocess.run("systemctl restart andlink.service", shell=True, check=True)

        #     print("设备解绑")
        #         args1[0].set_ble_adv_localname('CMB2320647-'+global_device_id[-4:])
        #         args1[0].set_ble_adv_manuf_data(global_manuf_code,global_manuf_data)
        #         args1[0].set_ble_json_deviceId(global_deviceId)
        #         #disconnect_bluetooth_device(bluetooth_device_path)
        #         print("蓝牙断开")
        #         # shutdown(0)
        #         print("广播蓝牙绑定01")
        #         ble_thread = threading.Thread(target=args1[1].custom_advertisement_register)
        #         ble_thread.start()
            return SIGCDataResponse(0)
        # 创建一个新字典，只包含需要的两个属性
        filtered_data = {
            "eventId": data.get("eventId"),
            "body": data.get("body")
        }

        # 将过滤后的数据转换回JSON字符串
        filtered_json_string = json.dumps(filtered_data)        
        # 往上回消息给app
        print("get json: "+filtered_json_string)
        self.ble_notify_characteristic_data_status_response(filtered_json_string)
        retcode = 0
        return SIGCDataResponse(retcode)

    def ble_publish_data_process(self):    
        try:
            # print(self.control_data_frame_data)
            json_string_from_bytes = bytes(self.control_data_frame_data).decode('utf-8')
            json_dict = json.loads(json_string_from_bytes)
            json_dict["deviceId"] = self.deviceId
            json_string_from_bytes = json.dumps(json_dict)
            print("publish json: "+json_string_from_bytes)
            # 发布消息到ROS
            self.sigcevent_msg.event=json_string_from_bytes
            self.publisher.publish(self.sigcevent_msg)
        except Exception as e:
            rospy.logerr("Failed to process or publish data: {}".format(e))
