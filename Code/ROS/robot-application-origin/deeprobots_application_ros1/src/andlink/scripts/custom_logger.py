import os
import logging
import datetime
from logging.handlers import TimedRotatingFileHandler

class CustomFilter(logging.Filter):
    def __init__(self, name='', level=None, exclude=None):
        super().__init__(name)
        self.level = getattr(logging, level.upper()) if level else None
        self.exclude = exclude if exclude is not None else []

    def filter(self, record):
        if self.level and record.levelno < self.level:
            return False
        if any(ex in record.getMessage() for ex in self.exclude):
            return False
        return True

class CustomLogger:
    def __init__(self, name, set_level='info', log_path=None, log_file=None, module=None):
        """
        初始化日志器，支持自定义日志级别、日志路径、日志文件名。
        如果未指定log_file，则按日期自动创建日志文件。
        增加module参数用于区分不同模块或层次的日志。
        """
        self.module = module
        self.logger = logging.getLogger(f"{name}.{module}" if module else name)
        self.set_level = set_level
        self.set_logging_level(set_level)

        # 设置日志格式
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # 控制台处理器
        self.console_handler = self.add_stream_handler(formatter, self.logger.level)

        # 文件处理器
        if log_file:
            self.add_file_handler(log_file, formatter, self.logger.level)
        elif log_path:
            self.add_daily_rotating_file_handler(log_path, formatter, self.logger.level)
        else:
            print("Warning: Neither log_file nor log_path is specified. Logging to console only.")

        # 初始化自定义过滤器
        self.custom_filter = None

    def set_logging_level(self, level):
        """
        设置日志级别，支持字符串或logging模块的内置级别常量。
        """
        try:
            self.logger.setLevel(getattr(logging, level.upper()))
        except AttributeError:
            print(f"Invalid log level: '{level}'. Defaulting to INFO.")
            self.logger.setLevel(logging.INFO)

    def add_stream_handler(self, formatter, level):
        """添加控制台处理器"""
        ch = logging.StreamHandler()
        ch.setFormatter(formatter)
        ch.setLevel(level)
        self.logger.addHandler(ch)
        return ch

    def add_file_handler(self, filename, formatter, level):
        """添加指定文件的文件处理器"""
        directory = os.path.dirname(filename)
        os.makedirs(directory, exist_ok=True)  # 确保目录存在，避免异常
        fh = logging.FileHandler(filename)
        fh.setFormatter(formatter)
        fh.setLevel(level)
        self.logger.addHandler(fh)

    def add_daily_rotating_file_handler(self, path, formatter, level):
        """添加按日期滚动的日志文件处理器"""

        # 检查目录是否存在，如果不存在则创建
        if not os.path.exists(path):
            os.makedirs(path)       
        filename = os.path.join(path, datetime.datetime.now().strftime('%Y-%m-%d') + '.log')
        rh = TimedRotatingFileHandler(filename, when='midnight', backupCount=50)
        rh.setFormatter(formatter)
        rh.setLevel(level)
        self.logger.addHandler(rh)

    def get_current_log_level(self):
        """
        查询并返回当前日志记录器的有效日志级别名称。
        """
        current_level = self.logger.getEffectiveLevel()
        return logging.getLevelName(current_level)

    def set_level(self, new_level):
        """动态更改日志级别"""
        self.set_logging_level(new_level)
        for handler in self.logger.handlers:
            handler.setLevel(new_level)

    def add_filter(self, level= None, exclude=None):
        """动态添加日志过滤器"""
        if level == None:
            level_t = self.set_level
        else:
            level_t = level

        if self.custom_filter:
            self.logger.removeFilter(self.custom_filter)
        self.custom_filter = CustomFilter(level=level_t, exclude=exclude)
        self.logger.addFilter(self.custom_filter)

    def remove_filter(self):
        """移除日志过滤器"""
        if self.custom_filter:
            self.logger.removeFilter(self.custom_filter)
            self.custom_filter = None

    # 保留原有的日志记录方法
    def debug(self, msg):
        self.logger.debug(msg)

    def info(self, msg):
        self.logger.info(msg)

    def warning(self, msg):
        self.logger.warning(msg)

    def error(self, msg):
        self.logger.error(msg)

    def critical(self, msg):
        self.logger.critical(msg)

# 使用示例
if __name__ == "__main__":
    logger = CustomLogger('TestLogger', 'debug', log_path='./')

    logger.debug("This is a debug message.")
    logger.info("This is an info message.")
    logger.warning("This is a warning message.")
    logger.error("This is an error message.")

    # 动态添加过滤器，屏蔽debug级别以下的日志和包含特定词语的日志
    logger.add_filter(level='info', exclude=['specific word'])

    logger.debug("This debug message will be filtered out.")
    logger.info("This info message contains a specific word and will be filtered out.")
    logger.warning("This is a warning message and will be logged.")
    logger.error("This is an error message and will be logged.")

    # 动态移除过滤器
    logger.remove_filter()

    logger.debug("This debug message will be logged again.")
    logger.info("This info message contains a specific word and will be logged again.")
