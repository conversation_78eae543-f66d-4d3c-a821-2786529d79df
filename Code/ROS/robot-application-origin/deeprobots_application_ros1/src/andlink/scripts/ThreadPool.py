import concurrent.futures
import threading
from typing import Callable, Dict, Any, Optional, Tuple
import time
import sys
import signal

from custom_logger import CustomLogger
# 定义一个简单的任务函数
def simple_task(task_id):
    print(f"Running task {task_id}")
    return f"Task {task_id} completed"


class LockClass:
    def __init__(self):
        self.readers_lock = threading.Lock()
        self.writer_lock = threading.Lock()
        self.reader_count = 0
        self.writer_count = 0
        self.reader_condition = threading.Condition(self.readers_lock)
        self.writer_condition = threading.Condition(self.writer_lock)

    def acquire_read(self):
        with self.readers_lock:
            while self.writer_count > 0:  # 等待直到没有写者
                self.reader_condition.wait()
            self.reader_count += 1

    def release_read(self):
        with self.readers_lock:
            self.reader_count -= 1
            if self.reader_count == 0:
                self.writer_condition.notify()  # 无读者时，唤醒等待的写者

    def acquire_write(self):
        with self.writer_lock:
            while self.reader_count > 0 or self.writer_count > 0:  # 等待直到没有读者和写者
                self.writer_condition.wait()
            self.writer_count += 1

    def release_write(self):
        with self.writer_lock:
            self.writer_count -= 1
            self.writer_condition.notify()  # 通知可能在等待的读者和写者



class ThreadPool:
    def __init__(self, max_workers: int = 10, logger = None):

        if logger == None:
            self.log_t = CustomLogger('ThreadPool', 'debug')
        else:
            self.log_t = logger

        signal.signal(signal.SIGINT, self._signal_handler)
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self.tasks_by_descriptor: Dict[str, concurrent.futures.Future] = {}
        self.stop_events: Dict[str, threading.Event] = {}

    # 捕获KeyboardInterrupt信号
    def _signal_handler(self,sig, frame):
        print('You pressed Ctrl+C!')
        sys.exit(0)


    # # 验证过，可以传入参数
    # def submit_task(self, func: Callable, descriptor: str, *args: Any, **kwargs: Any) -> concurrent.futures.Future:
    #     """提交任务到线程池，并记录其与描述符的关联"""
    #     stop_event = threading.Event()
    #     self.stop_events[descriptor] = stop_event
        
    #     wrapped_func = lambda: func(*args, **kwargs)
    #     future = self.executor.submit(wrapped_func)
    #     self.tasks_by_descriptor[descriptor] = future
    #     return future


    def submit_task(self, func: Callable, descriptor: str, *args: Any, **kwargs: Any) -> concurrent.futures.Future:
        """提交任务到线程池，并记录其与描述符的关联"""
        stop_event = threading.Event()
        self.stop_events[descriptor] = stop_event

        def wrapped_func():
            try:
                return func(*args, **kwargs)
            except Exception as e:
                self.log_t.error(f"Error in task {descriptor}: {e}") 
                # print(f"Error in task {descriptor}: {e}")      
                raise  # 重新抛出异常以便 future 能捕获到

        future = self.executor.submit(wrapped_func)
        self.tasks_by_descriptor[descriptor] = future
        return future
    
    
    def as_completed_with_descriptors(self):
        """按完成顺序返回带有描述符的任务Future"""
        return ((descriptor, future) for descriptor, future in self.tasks_by_descriptor.items() if future.done())

    def task_done(self, descriptor: str) -> bool:
        """检查指定描述符的任务是否已完成"""
        future = self.tasks_by_descriptor.get(descriptor)
        return future is not None and future.done()

    def task_exception(self, descriptor: str) -> Any:
        """获取指定描述符的任务是否引发了异常"""
        future = self.tasks_by_descriptor.get(descriptor)
        if future is not None and future.done():
            return future.exception()
        return None

    def get_result(self, descriptor: str) -> Any:
        """获取指定描述符的任务结果"""
        future = self.tasks_by_descriptor.get(descriptor)
        if future is not None:
            return future.result()
        raise KeyError(f"No result for descriptor: {descriptor}")

    def cancel_all_tasks(self) -> None:
        """取消所有未完成的任务"""
        for descriptor, future in self.tasks_by_descriptor.items():
            if not future.done():
                future.cancel()

    def shutdown_and_wait(self) -> None:
        """安全地关闭线程池并等待所有任务完成"""
        for event in self.stop_events.values():
            event.set()  # 通知所有线程停止
        self.executor.shutdown(wait=True)

    def force_shutdown(self) -> None:
        """强制关闭线程池并清理所有资源"""
        self.shutdown_and_wait()
        # 等待所有任务完成
        for descriptor, future in self.tasks_by_descriptor.items():
            if future.running():
                self.log_t.info(f"Waiting for task {descriptor} to finish.") 
                # print(f"Waiting for task {descriptor} to finish.")
                future.result()  # 等待任务完成




def test_submit_tasks():
    pool = ThreadPool(max_workers=2)

    # 带参数的任务
    def task_with_args(x, y):
        return x + y

    # 不带参数的任务
    def task_no_args():
        return "No args task completed"

    # 会引发错误的任务
    def faulty_task(x):
        raise ValueError(f"An error occurred with input {x}")

    # 提交带参数的任务
    descriptor_with_args = "task_with_args"
    pool.submit_task(task_with_args, descriptor_with_args, 5, 10)

    # 提交不带参数的任务
    descriptor_no_args = "task_no_args"
    pool.submit_task(task_no_args, descriptor_no_args)

    # 提交会引发错误的任务
    descriptor_faulty = "faulty_task"
    pool.submit_task(faulty_task, descriptor_faulty, 10)

    # 等待所有任务完成
    pool.executor.shutdown(wait=True)

    # 检查带参数的任务结果
    result_with_args = pool.get_result(descriptor_with_args)
    print(f"Result of task_with_args: {result_with_args}")
    assert result_with_args == 15, "Result should be 15"

    # 检查不带参数的任务结果
    result_no_args = pool.get_result(descriptor_no_args)
    print(f"Result of task_no_args: {result_no_args}")
    assert result_no_args == "No args task completed", "Result should be 'No args task completed'"

    # 检查错误任务是否捕获异常
    exception = pool.task_exception(descriptor_faulty)
    assert exception is not None, "Exception should have been captured"
    assert isinstance(exception, ValueError), "Exception should be of type ValueError"
    assert str(exception) == "An error occurred with input 10", "Exception message should match"

    print("All tests passed!")




# 使用示例
if __name__ == "__main__":
    # 运行测试
    test_submit_tasks()