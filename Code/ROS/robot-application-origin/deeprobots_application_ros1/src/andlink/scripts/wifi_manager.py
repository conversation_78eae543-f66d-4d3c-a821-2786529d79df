from pywifi import PyWiFi, const, Profile
import time
from custom_logger import CustomLogger

class MultiInterfaceWifiManager:
    def __init__(self, logger=None):
        self.wifi = PyWiFi()
        self.interfaces = self.wifi.interfaces()
        self.current_interface = None
        if logger is None:
            self.log_t = CustomLogger('MultiInterfaceWifiManager', 'debug')
        else:
            self.log_t = logger

    def list_interfaces(self):
        """列出所有可用的无线接口，并返回一个包含接口信息的字典列表。"""
        interfaces_info = [{"index": i, "name": interface.name()} for i, interface in enumerate(self.interfaces)]
        return interfaces_info

    def select_interface(self, index):
        """选择指定索引的无线接口。"""
        if 0 <= index < len(self.interfaces):
            self.current_interface = self.interfaces[index]
            return True
        else:
            self.log_t.warning("Invalid interface index.")
            return False

    def get_all_rssi(self):
        """获取当前选择的无线接口的 RSSI 值。"""
        if not self.current_interface:
            self.log_t.warning("No interface selected.")
            return None
        self.current_interface.scan()
        time.sleep(1)  # 等待扫描完成
        scan_results = self.current_interface.scan_results()
        for iface in scan_results:
            self.log_t.debug(f"SSID: {iface.ssid}, RSSI: {iface.signal}")
            print(f"SSID: {iface.ssid}, RSSI: {iface.signal}")

    def get_special_rssi(self, ssid):
        """获取指定 SSID 的 RSSI 值。"""
        if not self.current_interface:
            self.log_t.warning("No interface selected.")
            return None
        self.current_interface.scan()
        time.sleep(1)  # 等待扫描完成
        scan_results = self.current_interface.scan_results()
        for result in scan_results:
            if result.ssid == ssid:
                self.log_t.debug(f"RSSI of {ssid}: {result.signal}")
                return result.signal
        self.log_t.warning(f"SSID '{ssid}' not found.")
        return None

    def connect_to_network(self, ssid, password=None):
        """连接到指定 SSID 的 Wi-Fi 网络。"""
        if not self.current_interface:
            self.log_t.warning("No interface selected.")
            return

        profile = self._create_wifi_profile(ssid, password)
        self.current_interface.remove_all_network_profiles()
        tmp_profile = self.current_interface.add_network_profile(profile)

        self.current_interface.connect(tmp_profile)
        time.sleep(10)  # Wait for the connection to establish

        if self.current_interface.status() == const.IFACE_CONNECTED:
            self.log_t.info(f"Connected to {ssid}")
            return True
        else:
            self.log_t.info("Failed to connect.")
            return False

    def _create_wifi_profile(self, ssid, password):
        """创建一个新的 Wi-Fi 连接配置文件。"""
        profile = Profile()
        profile.ssid = ssid
        if password:
            profile.auth = const.AUTH_ALG_OPEN
            profile.akm.append(const.AKM_TYPE_WPA2PSK)
            profile.cipher = const.CIPHER_TYPE_CCMP
            profile.key = password
        else:
            profile.auth = const.AUTH_ALG_OPEN
            profile.akm.append(const.AKM_TYPE_UNENCRYPTED)
        return profile

# 使用示例
if __name__ == "__main__":
    log_t = CustomLogger('MultiInterfaceWifiManager', 'debug','./')
    wifi_manager = MultiInterfaceWifiManager(logger=log_t)
    dict=wifi_manager.list_interfaces()
    print(f"interfaces : {dict}")


    # 假设你的设备上有两张网卡，这里选择第一张
    if wifi_manager.select_interface(0): 
        # 获取指定 SSID 的 RSSI 值
        ssid = "iQOO10"
        # ssid =
        rssi = wifi_manager.get_special_rssi(ssid)
        if rssi is not None:
            print(f"RSSI of {ssid}: {rssi}")
        else:
            print(f"SSID {ssid} not found.")


        # wifi_manager.get_rssi()
        
        # # 假设你想连接到一个已知的 Wi-Fi 网络
        # ssid = "YourSSID"
        password = "1234567890"
        wifi_manager.connect_to_network(ssid, password)