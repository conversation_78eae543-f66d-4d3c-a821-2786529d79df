<?xml version="1.0"?>
<package format="2">
  <name>andlink</name>
  <version>0.0.0</version>
  <description>The andlink package</description>

  <maintainer email="<EMAIL>">yuwei</maintainer>
  <license>TODO</license>

  <!-- Url tags are optional -->
  <!-- Example: -->
  <!-- <url type="website">http://wiki.ros.org/andlink</url> -->

  <!-- Author tags are optional -->
  <!-- Example: -->
  <!-- <author email="<EMAIL>">Jane <PERSON></author> -->

  <!-- Build tool dependencies -->
  <buildtool_depend>catkin</buildtool_depend>

  <!-- Build dependencies -->
  <build_depend>rospy</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>message_generation</build_depend>  <!-- Added for message and service generation -->

  <!-- Build export dependencies -->
  <build_export_depend>rospy</build_export_depend>
  <build_export_depend>std_msgs</build_export_depend>
  <build_export_depend>message_runtime</build_export_depend>  <!-- Added for message and service generation -->

  <!-- Execution dependencies -->
  <exec_depend>rospy</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>message_runtime</exec_depend>  <!-- Added for message and service generation -->

  <!-- The export tag contains other, unspecified, tags -->
  <export>
    <!-- Other tools can request additional information be placed here -->
  </export>
</package>
