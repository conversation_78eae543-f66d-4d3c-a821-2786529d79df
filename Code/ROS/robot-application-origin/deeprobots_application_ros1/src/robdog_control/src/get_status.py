#!/usr/bin/env python3

import socket
import struct
import threading
import time

class CommandHead:
    def __init__(self, code, parameters_size, type):
        self.code = code
        self.parameters_size = parameters_size
        self.type = type

    def pack(self):
        return struct.pack('III', self.code, self.parameters_size, self.type)

class RobotState:
    def __init__(self, data):
        self.robot_basic_state, self.robot_gait_state = struct.unpack('ii', data[:8])
        self.rpy = struct.unpack('ddd', data[8:32])
        self.rpy_vel = struct.unpack('ddd', data[32:56])
        self.xyz_acc = struct.unpack('ddd', data[56:80])
        self.pos_world = struct.unpack('ddd', data[80:104])
        self.vel_world = struct.unpack('ddd', data[104:128])
        self.vel_body = struct.unpack('ddd', data[128:152])
        self.touch_down_and_stair_trot, = struct.unpack('I', data[152:156])
        self.is_charging, = struct.unpack('?', data[156:157])
        self.error_state, = struct.unpack('I', data[157:161])
        self.robot_motion_state, = struct.unpack('i', data[161:165])
        self.battery_level, = struct.unpack('d', data[165:173])
        self.task_state, = struct.unpack('i', data[173:177])
        self.is_robot_need_move, = struct.unpack('?', data[177:178])
        self.zero_position_flag, = struct.unpack('?', data[178:179])
        self.ultrasound = struct.unpack('dd', data[179:195])

class RobotStateReceived:
    def __init__(self, data):
        self.code, self.size, self.cons_code = struct.unpack('iii', data[:12])
        self.robot_state = RobotState(data[12:])

def send_command():
    server_ip = "**************" #"127.0.0.1"
    server_port = 43893
    sockfd = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    server_addr = (server_ip, server_port)
    command_head = CommandHead(0x0901, 0, 1)
    while True:
        try:
            sockfd.sendto(command_head.pack(), server_addr)
            # print("Command sent")
        except socket.error as e:
            print(f"Error sending command: {e}")
        time.sleep(1/50)

def receive_data():
    local_port = 43897
    sockfd = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    # sockfd.bind(("127.0.0.1", local_port))
    sockfd.bind(("**************", local_port))
    while True:
        data, _ = sockfd.recvfrom(4096)
        print("Received data:", data)
        print("From address:", _)
        if len(data) >= 195:
            robot_state_received = RobotStateReceived(data)
            if robot_state_received.code == 2305:
                print(f"Received data with code {robot_state_received.code}")
                print(f"Original data: {data}")
                print(f"Robot State Data:\n{robot_state_received.robot_state.__dict__}")

def main():
    send_thread = threading.Thread(target=send_command)
    recv_thread = threading.Thread(target=receive_data)
    send_thread.start()
    recv_thread.start()
    send_thread.join()
    recv_thread.join()

if __name__ == "__main__":
    main()
