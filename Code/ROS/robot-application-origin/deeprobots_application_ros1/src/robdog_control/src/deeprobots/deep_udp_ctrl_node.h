// #include <rclcpp/rclcpp.hpp>
#include <ros/ros.h>
#include <netinet/in.h>
#include <stdio.h>
#include <unistd.h>
#include <geometry_msgs/Twist.h>
#include <homi_speech_interface/RobdogAction.h>
#include <homi_speech_interface/Wakeup.h>
#include <homi_speech_interface/ProprietySet.h>
#include <homi_speech_interface/RobdogState.h>
#include <homi_speech_interface/ContinueMove.h>
#include <homi_speech_interface/AssistantAbort.h>
// #include <homi_speech_interface/AssistantQuiet.h>
#include <homi_speech_interface/SetWakeEvent.h>
#include <homi_speech_interface/NewUdpConnect.h>

#include <map>
#include <string>
#include "deep_cmd.h"
#include <tuple>
#include <unordered_map>

// #include <geometry_msgs/msg/twist.hpp>

static const uint16_t LOCAL_PORT_NUMBER = 2368;   // default data port
static const uint16_t REMOTE_PORT_NUMBER = 8308;  // default position port
enum cmdFromNode{
    POWER_LEVEL_FROM_NODE=1,
    POWER_CHARGE_FROM_NODE,
    WIFI_NAME_FROM_NODE,
    IS_WIFI_CONNECT,
};

// 机器狗状态[基础状态]
#define STATE_PRADOWN 1        // 趴下状态
#define STATE_READY_TO_STAND 4 // 准备起立状态
#define STATE_STANDING 5       // 正在起立状态
#define STATE_FORCE_CONTROL 6   // 力控状态
#define STATE_DOWNING 7        // 正在趴下状态
#define STATE_PROTECTION 8     // 失控保护状态
#define STATE_ADJUSTMENT 9     // 姿态调整状态
#define STATE_TURN_OVER 11     // 执行翻身动作
#define STATE_ZEROING 17       // 回零状态
#define STATE_BACKFLIP 18      // 执行后空翻动作
#define STATE_GREET 20          // 执行打招呼动作


// 从机器狗接收到的数据包
// 定义 RobotStateUpload 结构体
struct RobotStateUpload {
    int robot_basic_state;         // 机器人基本运动状态
    int robot_gait_state;          // 机器人当前步态
    double rpy[3];                 // IMU 角度信息 (Roll, Pitch, Yaw)
    double rpy_vel[3];             // IMU 角速度信息 (Roll, Pitch, Yaw)
    double xyz_acc[3];             // IMU 加速度信息 (X, Y, Z)
    double pos_world[3];           // 机器人在世界坐标系下的位姿信息 (X, Y, Z)
    double vel_world[3];           // 机器人在世界坐标系下的速度信息 (X, Y, Z)
    double vel_body[3];            // 机器人在身体坐标系下的速度信息 (X, Y, Z)
    // 无效数据，占位用
    unsigned touch_down_and_stair_trot; // 触地状态和楼梯步态（占位用）
    bool is_charging;              // 机器人是否正在充电
    unsigned error_state;              // 错误状态
    int task_state;               // 任务状态

    int robot_motion_state;       // 机器人动作状态
    unsigned zero_position_flag;  // 回零标志位（占位用）
    double battery_level;         // 电池电量百分比（小数形式）
    bool is_robot_need_move;      // 机器人是否需要移动
    double ultrasound[2];         // 超声波数据（用于检测障碍物或距离）
};

// class deepUdpCtrl : public rclcpp::Node {
class DeepUdpCtrl{
  public:
    DeepUdpCtrl(ros::NodeHandle& nh);
    ~DeepUdpCtrl(); 
    void initSocket(std::string devip_str_, int port_, int remote_port);
    void updateSocket(const homi_speech_interface::NewUdpConnect& msg) ;
    int getPacket(char *pkt, size_t packet_size);
    int sendPacket(uint8_t *pkt, size_t packet_size);
    void StandUp();
    // void HeartBeatCallback();
    void HeartBeatCallback(const ros::TimerEvent& event) ;
    // void callRobotStatus(RobotStateUpload *pkt, size_t packet_size) ;
    void send_command();
    void publishRobdagStateToQt(std::string robot_state_details);
    void iflyCallback(const homi_speech_interface::WakeupPtr& msg);
    void handle_UDP_data(char *data,size_t length);
    void deep_ctl(int cmdID,int cmdValue,int cmdValueEx);
    void userDefinedCtrlCallback(const homi_speech_interface::ProprietySetPtr& msg);
    void timerCallbackForRobotMove(const ros::TimerEvent&);
    void positionCtrl(float x, float y,float radian);
    // 机器人的状态信息
    // Getters
    int getRobotBasicState() const;
    int getRobotGaitState() const;
    std::array<double, 3> getRPY() const;
    std::array<double, 3> getRPYVel() const;
    std::array<double, 3> getXYZAcc() const;
    std::array<double, 3> getPosWorld() const;
    std::array<double, 3> getVelWorld() const;
    std::array<double, 3> getVelBody() const;
    unsigned getTouchDownAndStairTrot() const;
    bool getIsCharging() const;
    unsigned getErrorState() const;
    unsigned getRobotMotionState() const;
    bool getZeroPositionFlag() const;
    double getBatteryLevel() const;
    unsigned getTaskState() const;
    bool getIsRobotNeedMove() const;
    // unsigned getInvalidData() const;
    std::array<double, 2> getUltrasound() const;

    // Setters
    void setRobotBasicState(int state);
    void setRobotGaitState(int state);
    void setRPY(const std::array<double, 3>& angles);
    void setRPYVel(const std::array<double, 3>& velocities);
    void setXYZAcc(const std::array<double, 3>& acc);
    void setPosWorld(const std::array<double, 3>& pos);
    void setVelWorld(const std::array<double, 3>& vel);
    void setVelBody(const std::array<double, 3>& vel);

    void setTouchDownAndStairTrot(unsigned status);
    void setIsCharging(bool charging);
    void setErrorState(unsigned state);
    void setRobotMotionState(int state);
    void setZeroPositionFlag(bool flag);
    void setBatteryLevel(double level);
    void setTaskState(unsigned state);
    void setIsRobotNeedMove(bool needMove);
    // void setInvalidData(unsigned data);
    void setUltrasound(const std::array<double, 2>& data);
    int sendPacketUserDefine(uint8_t *pkt, size_t packet_size) ;
  private:
    void velCmdCallback(const geometry_msgs::TwistConstPtr& msg); 
    void MoveSkillscallback(const homi_speech_interface::RobdogAction& msg);
    void continueMovecallback(const homi_speech_interface::ContinueMove& msg);

    ros::NodeHandle nh_; // 节点
    ros::Subscriber ifly_sub_; //接收讯飞声源定位的节点
    

    // 机器人的状态信息
    int robot_basic_state = 17;         // 机器人基本运动状态
    int robot_gait_state = 0;          // 机器人当前步态
    double rpy[3] = {0.0, 0.0, 0.0};                 // IMU 角度信息 (Roll, Pitch, Yaw)
    double rpy_vel[3] = {0.0, 0.0, 0.0};             // IMU 角速度信息 (Roll, Pitch, Yaw)
    double xyz_acc[3] = {0.0, 0.0, 0.0};             // IMU 加速度信息 (X, Y, Z)
    double pos_world[3] = {0.0, 0.0, 0.0};           // 机器人在世界坐标系下的位姿信息 (X, Y, Z)
    double vel_world[3] = {0.0, 0.0, 0.0};           // 机器人在世界坐标系下的速度信息 (X, Y, Z)
    double vel_body[3] = {0.0, 0.0, 0.0};            // 机器人在身体坐标系下的速度信息 (X, Y, Z)

    // 无效数据，占位用
    unsigned touch_down_and_stair_trot = 0; // 触地状态和楼梯步态（占位用）
    bool is_charging = false;              // 机器人是否正在充电
    unsigned error_state = 0;              // 错误状态
    int task_state = 0;               // 任务状态

    int robot_motion_state = 0;       // 机器人动作状态
    bool zero_position_flag = false;  // 回零标志位（占位用）
    double battery_level = 0.0;         // 电池电量百分比（小数形式）
    
    bool is_robot_need_move = false;      // 机器人是否需要移动
    double ultrasound[2] = {0.0, 0.0};         // 超声波数据（用于检测障碍物或距离）


    std::map<int, std::string> robotStateMap = {
        {1, "趴下状态"},
        {4, "准备起立状态"},
        {5, "正在起立状态"},
        {6, "力控状态"},
        {7, "正在趴下状态"},
        {8, "失控保护状态"},
        {9, "姿态调整状态"},
        {11, "执行翻身动作"},
        {17, "回零状态"},
        {18, "执行后空翻动作"},
        {20, "执行打招呼动作"}
    };

    std::map<int, std::string> gaitStateMap = {
        {0, "平地低速步态"},
        {2, "通用越障步态"},
        {4, "平地中速步态"},
        {5, "平地高速步态"},
        {6, "抓地越障步态"},
        {13, "高踏步越障步态"},
        {12, "太空步步态"}
    };

    // 当前的运动模式
    // std::string SportMode = "walk";    // 初始是walk模式

  protected:
    int port_;
    int remote_port_;
    std::string devip_str_;
    
    int sockfd_;
    int sockfd_customized;//与外设控制进程通信的socket
    in_addr devip_;
    sockaddr_in client_addr;

    // rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr velCmd_  = nullptr;
    // rclcpp::TimerBase::SharedPtr timer_ = nullptr;
    ros::Subscriber velCmd_; // 速度命令订阅者
    ros::Subscriber UdpConnectCmd_; // 新建UDP连接
    ros::Subscriber actionCmd_; // 特定运动消息订阅者
    ros::Subscriber continueMoveCmd_; // 持续运动消息订阅
    ros::Publisher statusReportPub;//发布者，发布通过UDP接收的上报状态
    ros::Subscriber userDefinedSub_;//自定义消息（部分通过自定义命令实现，部分通过开发手册中接口实现）
    ros::Timer timer_; // 定时器
    ros::Publisher robotStatusPub_;
};
