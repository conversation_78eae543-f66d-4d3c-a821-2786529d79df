#include "deep_udp_ctrl_node.h"
#include <arpa/inet.h>
#include <errno.h>
#include <fcntl.h>
#include <poll.h>
#include <sys/file.h>
#include <sys/socket.h>
#include <unistd.h>
#include <sstream>
#include <string>
#include <filesystem>
#include <vector>
#include <random> 
#include <arpa/inet.h> 
#include <cmath> 
#include <thread>
#include <stdexcept>
#include <array>
#include <regex>
#include <tinyxml2.h>
#pragma pack(4)
// 发给机器狗的数据包
struct CommandHead {
  uint32_t code;
  uint32_t size;
  uint32_t type;
};
uint32_t hton_int(int value) {
    return htonl(value);
}
const uint32_t kDataSize = 256;
struct Command{
  CommandHead head;
  uint32_t data[kDataSize];
};
ros::Timer timer;
bool timer_active = false;
homi_speech_interface::ContinueMove g_robot_motion;
CommandHead command;   
Command data;    
using namespace tinyxml2;
class FloatRandomGenerator {
public:
    FloatRandomGenerator(float min, float max, size_t num_samples)
        : min(min), max(max), current_index(0) {
        float step = (max - min) / static_cast<float>(num_samples - 1);
        for (size_t i = 0; i < num_samples; ++i) {
            float value = min + i * step;
            numbers.push_back(value);
        }
        std::shuffle(numbers.begin(), numbers.end(), rng);
    }
    float next() {
        if (current_index >= numbers.size()) {
            std::cout<<"No more numbers available.";
        }
        return numbers[current_index++];
    }
private:
    float min, max;
    std::vector<float> numbers;
    size_t current_index;
    std::default_random_engine rng{static_cast<unsigned int>(std::time(nullptr))};
};

long getVideoDurationInMilliseconds(const std::string& filePath) {
    if (filePath.empty()) {
        ROS_INFO("File path cannot be empty.");
        throw std::invalid_argument("File path cannot be empty.");
    }
    std::string command = "ffmpeg -i \"" + filePath + "\" 2>&1 | grep \"Duration\"";
    std::array<char, 128> buffer;
    std::string result;
    FILE* pipe = popen(command.c_str(), "r");
    if (!pipe) {
        ROS_INFO("popen() failed!");
    }
    while (fgets(buffer.data(), buffer.size(), pipe) != nullptr) {
        result += buffer.data();
    }
    pclose(pipe);
    std::regex durationRegex(R"(Duration: (\d+):(\d+):(\d+)\.(\d+))");
    std::smatch match;
    if (std::regex_search(result, match, durationRegex)) {
        int hours = std::stoi(match[1]);
        int minutes = std::stoi(match[2]);
        int seconds = std::stoi(match[3]);
        int milliseconds = std::stoi(match[4]);
        long totalMilliseconds = (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds * 10;
        return totalMilliseconds;
    } else {
        throw std::runtime_error("Could not extract duration from ffmpeg output.");
    }
}

void playAudio (const std::string& filePath) {

  FILE* fp;
  char buffer[50];
  fp = popen(R"(aplay -l | grep "USB Audio Devic" -A 2 | grep "card" | awk '{print $2}' | tr -d ':')", "r");
  if(!fp) {
    printf("ThirdpartyAudioDevice Search audio device failed\n");
    return;
  }
  fgets(buffer,sizeof(buffer),fp);
  std::string device_name = "plughw:" + std::string(1, buffer[0]) + ",0";
	if (pclose(fp) == -1) {
			ROS_ERROR_STREAM( "Error closing the pipe." << std::flush);
	}
  ROS_INFO("device_name:%s",device_name.c_str());

    // 关闭语音助手
    homi_speech_interface::AssistantAbort srv; 
    bool response = ros::service::call("/homi_speech/helper_assistant_abort_service", srv); 
    // 关闭语音唤醒
    homi_speech_interface::SetWakeEvent wkevent;
    wkevent.request.target = false;
    bool response1 = ros::service::call("/audio_node/set_wake_event_service", wkevent);
    // 播放音频
    std::string command = "aplay -D "+device_name + " \"" + filePath + "\"";
    int result = std::system(command.c_str());
    // 开启语音唤醒事件
    homi_speech_interface::SetWakeEvent wkevent2;
    wkevent.request.target = true;
    bool response2 = ros::service::call("/audio_node/set_wake_event_service", wkevent);
}
void getAndPlayRandomFilePath(const std::string& directory) {
    std::vector<std::string> files;
    try {
        if (!std::filesystem::exists(directory) || !std::filesystem::is_directory(directory)) {
            std::cerr << "Directory does not exist or is not a directory: " << directory << std::endl;
            return;
        }
        for (const auto &entry : std::filesystem::directory_iterator(directory)) {
            if (entry.is_regular_file()) {
                files.push_back(entry.path().string());
            }
        }
    } catch (const std::filesystem::filesystem_error &e) {
        std::cerr << "Filesystem error: " << e.what() << std::endl;
        return; // 处理完错误后正常返回
    } catch (const std::exception &e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return; // 处理其他异常
    }    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, files.size() - 1);
    playAudio(files[dis(gen)]);
    // std::string command = "aplay -D plughw:4,0 " + files[dis(gen)];
    // int result = std::system(command.c_str());
    // if (result != 0) 
    //     std::cerr << "Error playing audio file:"<<command<<"Command returned: " << result << std::endl;
}

void execute_script(const std::string& directory) {
    std::vector<std::string> files;    long duration;
    try {
        if (!std::filesystem::exists(directory) || !std::filesystem::is_directory(directory)) {
            std::cerr << "Directory does not exist or is not a directory: " << directory << std::endl;
            return;
        }
        for (const auto &entry : std::filesystem::directory_iterator(directory)) {
            if (entry.is_regular_file()) {
                files.push_back(entry.path().string());
            }
        }
    } catch (const std::filesystem::filesystem_error &e) {
        std::cerr << "Filesystem error: " << e.what() << std::endl;
        return; // 处理完错误后正常返回
    } catch (const std::exception &e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return; // 处理其他异常
    }    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, files.size() - 1);
    try {
        duration = getVideoDurationInMilliseconds(files[dis(gen)]);
    } 
    catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return ;
    }
    std::string command = "/home/<USER>/updateexpression.sh " + files[dis(gen)];
    system(command.c_str());
    sleep(duration/1000);
    command = "/home/<USER>/updateexpression.sh /home/<USER>/resource/vedio/default.mp4";
    system(command.c_str());
}

void specific_expression(const std::string& path) {
    if (path.empty()) {
        ROS_INFO("File path cannot be empty.");
        throw std::invalid_argument("File path cannot be empty.");
    }
    std::string command = "/home/<USER>/updateexpression.sh " + path;
    system(command.c_str());
}
// ******************************************************************************************************************8
// 类名：：构造函数：初始化列表（成员变量【用于访问ros节点句柄】）
DeepUdpCtrl::DeepUdpCtrl(ros::NodeHandle& nh) : nh_(nh){
    XMLDocument doc;
    XMLError error = doc.LoadFile("/home/<USER>/.config/config_robotdog.xml");
    ROS_INFO("Loading configuration file from: /home/<USER>/.config/config_robotdog.xml");
    if (error != XML_SUCCESS)
        ROS_ERROR("Failed to load configuration file");
    XMLElement* root = doc.FirstChildElement("robot_state");
    if (!root) {
        ROS_ERROR_STREAM ("Root element not found in configuration file." << std::endl);
        return;
    }

    XMLElement* elem = root->FirstChildElement("sound_source_localization");
    if (elem && nullptr != elem->GetText()) {
        std::string ssl = elem->GetText();
        ROS_INFO("sound_source_localization value is %s",ssl.c_str());
        if (ssl.c_str()=="on"){
            ifly_sub_ = nh.subscribe("/audio_node/wakeup_event", 1,&DeepUdpCtrl::iflyCallback, this);      // 接收到讯飞节点的消息后回调，用于声源定位
            ROS_INFO("ifly_sub_ subscribe topic /audio_node/wakeup_event");
            std::cout << std::flush;
        }
    }
    // 订阅速度命令话题
    velCmd_ = nh.subscribe<geometry_msgs::Twist>("/catch_turtle/ctrl_instruct", 1,
        &DeepUdpCtrl::velCmdCallback, this);
    // 订阅新的UDP连接消息
    UdpConnectCmd_ = nh.subscribe("/catch_turtle/ctrl_udpconnect", 1, &DeepUdpCtrl::updateSocket, this);
    
    // 订阅特定运动消息
    actionCmd_ = nh.subscribe("/catch_turtle/action_type", 1, &DeepUdpCtrl::MoveSkillscallback, this);
    // 订阅持续运动消息
    continueMoveCmd_ = nh.subscribe("/catch_turtle/continue_move", 1000, &DeepUdpCtrl::continueMovecallback, this);

    timer_ = nh.createTimer(ros::Duration(0.1), &DeepUdpCtrl::HeartBeatCallback, this);

    userDefinedSub_ = nh.subscribe("/deep_udp_ctrl/status_ctrl", 1,&DeepUdpCtrl::userDefinedCtrlCallback, this);      // 接收处理平台消息的robdog_plat节点的消息，实现自定义命令控制和直接控制
    ROS_INFO("userDefinedSub_ subscribe topic /deep_udp_ctrl/status_ctrl");
    std::cout << std::flush;
    statusReportPub =nh.advertise<homi_speech_interface::ProprietySet>("/deep_udp_ctrl/status_report", 1); 
    
    robotStatusPub_ = nh.advertise<homi_speech_interface::RobdogState>("/robdog_control/robdog_state", 1); // 发布机器人状态信息
    sockfd_ = -1;
}

DeepUdpCtrl::~DeepUdpCtrl() {
    if (sockfd_ != -1) {
        close(sockfd_);
    }
    int robdog_status = getRobotBasicState();// 判断当前机器人的状态
    // 程序结束的时候趴下
    if(robdog_status != 1){ // 非趴下状态
        command.code = 0x21010202;
        command.type = 0;
        command.size = 0;
        sendPacket((uint8_t*)&command, sizeof(command));
    }
}
void DeepUdpCtrl::userDefinedCtrlCallback(const homi_speech_interface::ProprietySetPtr& msg){
    ROS_INFO("Received msg form Platintera Node: cmd:%x,value:%d", msg->cmd,msg->value);
    switch (msg->cmd)
    {
    case DEEP_CMD_ACTION:
        command={0};
        command.code = msg->value; //指令码
        command.size = 0;// 指令值
        command.type = 0; // 指令类型,复杂/简单
        sendPacket((uint8_t*)&command, sizeof(command));
        break;
    case DEEP_CMD_FLASHLIGHT:
        deep_ctl(DEEP_CMD_FLASHLIGHT,msg->value,msg->exvalue);
        break;
    case DEEP_CMD_LIGHT:
        deep_ctl(DEEP_CMD_LIGHT,msg->value,msg->exvalue);

    default:
        break;
    }
}
void DeepUdpCtrl::positionCtrl(float x, float y,float radian)
{
    Command cmd;
    memset(&cmd,0,sizeof(Command));
    cmd.head.type = 1;
    cmd.head.size = sizeof(int)+3*sizeof(float);
    cmd.head.code = POSITION_CMD;
    memset(cmd.data, 0, sizeof(cmd.data));
    cmd.data[0] = 1; 
    unsigned seed = std::chrono::system_clock::now().time_since_epoch().count();
    std::default_random_engine generator(seed);
    std::uniform_real_distribution<double> distribution(0.01, 0.1);
    double randomNumber = distribution(generator);
    radian=radian+randomNumber;
    float position[3]={x,y,radian};
    memcpy(&cmd.data[1],position,sizeof(position));
    sendPacket((uint8_t*)&cmd, sizeof(CommandHead) + sizeof(int)+3*sizeof(float));          
}

void DeepUdpCtrl::iflyCallback(const homi_speech_interface::WakeupPtr& msg){
    ROS_INFO("Received msg form IFly Node: event:%s,angle:%d", msg->ivwWord.c_str(),msg->angle);
    float real_angle=(msg->angle+30)*static_cast<float>(M_PI) / 180.0f;
    CommandHead cmd;
    memset(&cmd,0,sizeof(CommandHead));
    cmd.code = POSITION_ANG_VEL;
    cmd.type = 0;
    cmd.size = 790;
    sendPacket((uint8_t*)&cmd, sizeof(CommandHead));
    positionCtrl(0.0f,0.0f,(msg->angle+30)<180?real_angle:-(2*M_PI-real_angle));
}

void DeepUdpCtrl::initSocket(std::string devip_str, int port, int remote_port) {
    devip_str_ = devip_str;
    port_ = port;
    remote_port_ = remote_port;

    ROS_INFO("Initializing socket: %s , %d , %d " , devip_str.c_str(), port ,remote_port);
    std::cout << std::flush;
    sockfd_ = -1;
    if (!devip_str_.empty()) {
        inet_aton(devip_str_.c_str(), &client_addr.sin_addr);
    }

    // 创建UDP套接字
    sockfd_ = socket(PF_INET, SOCK_DGRAM, 0);
    if (sockfd_ == -1) {
        perror("socket");
        return;
    }
		sockfd_customized=socket(PF_INET, SOCK_DGRAM, 0);
		if (sockfd_customized == -1) {
        perror("socket");
        return;
    }

    sockaddr_in my_addr,my_addr_pro;
    memset(&my_addr, 0, sizeof(my_addr));
    my_addr.sin_family = AF_INET;
    my_addr.sin_port = htons(port_);
    my_addr.sin_addr.s_addr = INADDR_ANY;

    memset(&my_addr_pro, 0, sizeof(my_addr_pro));
    my_addr_pro.sin_family = AF_INET;
    my_addr_pro.sin_port = htons(1234);
    my_addr_pro.sin_addr.s_addr = INADDR_ANY;

    if (bind(sockfd_, (sockaddr *)&my_addr, sizeof(my_addr)) == -1) {  
        perror("bind");
        return;
    }
    if (bind(sockfd_customized, (sockaddr *)&my_addr_pro, sizeof(my_addr)) == -1) {  
        perror("bind");
        return;
    }

    if (fcntl(sockfd_, F_SETFL, O_NONBLOCK | FASYNC) < 0) {
        perror("fcntl");
        return;
    }
    if (fcntl(sockfd_customized, F_SETFL, O_NONBLOCK | FASYNC) < 0) {
        perror("fcntl");
        return;
    }
    client_addr.sin_family = AF_INET;
    client_addr.sin_port = htons(remote_port_);

    ROS_INFO("Socket fd is %d",sockfd_);
    std::cout << std::flush;
}

// 根据QT界面传入的IP地址，更新新的UDP连接
void DeepUdpCtrl::updateSocket(const homi_speech_interface::NewUdpConnect& msg) {
    // std::string new_devip_str, int new_remote_port, int new_local_port
    std::string new_devip_str = msg.new_devip_str;
    int new_remote_port = msg.new_remote_port;
    int new_local_port = msg.new_local_port;
    // Check if the new IP address or local port is different from the current ones
    if (new_devip_str != devip_str_ || new_remote_port != remote_port_ || new_local_port != port_) {
        // Close the existing sockets
        if (sockfd_ != -1) {
            close(sockfd_);
            sockfd_ = -1;
        }
        if (sockfd_customized != -1) {
            close(sockfd_customized);
            sockfd_customized = -1;
        }

        // Update the device IP, remote port, and local port
        devip_str_ = new_devip_str;
        remote_port_ = new_remote_port;
        port_ = new_local_port; // Update local port

        // Reinitialize the socket with the new parameters
        initSocket(devip_str_, port_, remote_port_);
    } else {
        ROS_INFO_STREAM("No change in IP address, remote port, or local port. No update needed.");
    }
}

int DeepUdpCtrl::getPacket(char *pkt, size_t packet_size) {

  struct pollfd fds[2];
  fds[0].fd = sockfd_;
  fds[0].events = POLLIN;
	fds[1].fd = sockfd_customized;
  fds[1].events = POLLIN;
  const int POLL_TIMEOUT = 1000; // 毫秒
  sockaddr_in sender_address;
  socklen_t sender_address_len = sizeof(sender_address);
	int retval = poll(fds, 2, POLL_TIMEOUT);
	if (retval < 0) {
			if (errno != EINTR) {
					ROS_ERROR_STREAM("poll() error: " << strerror(errno));
			}
			return 1;
	}
	if (retval == 0) {
			return 1;
	}
	if (fds[0].revents & (POLLERR | POLLHUP | POLLNVAL)) {
			ROS_ERROR("poll() reports error");
			return 1;
	}
  // 接收数据
  if (fds[0].revents & POLLIN){
      memset(pkt, 0, packet_size);
      ssize_t nbytes = recvfrom(sockfd_, pkt, packet_size, 0,
                                (sockaddr *)&sender_address, &sender_address_len);

      if (nbytes < 0) {
          if (errno != EWOULDBLOCK) {
              perror("recvfail");
              ROS_ERROR("recvfail");
              return 1;
              // continue;
          }
      } 
      else if (nbytes >= 195){   // 按照云深处给的例程
      // 收到了数据
      // else if (static_cast<size_t>(nbytes) == packet_size) {
          if (!devip_str_.empty() && sender_address.sin_addr.s_addr != devip_.s_addr){
              // ROS_INFO("devip_str_: %s", devip_str_.c_str());
              // Convert the IP addresses to strings
              std::string sender_ip = inet_ntoa(sender_address.sin_addr);
              std::string dev_ip = inet_ntoa(devip_);
              // ROS_INFO("sender_address.sin_addr: %s, devip_.s_addr: %s", sender_ip.c_str(), dev_ip.c_str());
              return 0;
          }
      }
  }
	if (fds[1].revents & POLLIN) {
			memset(pkt, 0, packet_size);
			int num = recvfrom(sockfd_customized, pkt, packet_size, 0,
												(struct sockaddr*)&sender_address, &sender_address_len);
			if (num < 0) {
					perror("recvfrom");
					return 1;
			}
			char ip_str[INET_ADDRSTRLEN];
			inet_ntop(AF_INET, &sender_address.sin_addr, ip_str, sizeof(ip_str));
			std::cout << "Received from " << ip_str << ":" 
								<< ntohs(sender_address.sin_port) 
								<< " on socket " << sockfd_customized <<std::endl;
			handle_UDP_data(pkt,num);
  }   
  return 1;
}

int DeepUdpCtrl::sendPacket(uint8_t *pkt, size_t packet_size) {
    ssize_t nbytes = sendto(sockfd_, pkt, packet_size, 0,
                            (struct sockaddr *)&client_addr, sizeof(client_addr));
    if (nbytes < 0) {
        ROS_ERROR("Failed to send packet: %s", strerror(errno)); // 使用ROS_ERROR记录错误
        return 1;
    } else if ((size_t)nbytes == packet_size) {
        //ROS_INFO("Packet sent successfully"); // 你可以取消注释这行来记录成功发送的日志
    }
    return 0;
}

int DeepUdpCtrl::sendPacketUserDefine(uint8_t *pkt, size_t packet_size) {
    sockaddr_in userClient_addr;
    userClient_addr.sin_family = AF_INET;
    userClient_addr.sin_port = htons(12345);
    userClient_addr.sin_addr.s_addr = inet_addr("127.0.0.1"); 
    ssize_t nbytes = sendto(sockfd_, pkt, packet_size, 0,
                            (struct sockaddr *)&userClient_addr, sizeof(userClient_addr));
    if (nbytes < 0) {
        ROS_ERROR("Failed to send packet: %s", strerror(errno)); 
        return 1;
    } else if ((size_t)nbytes == packet_size) {
        ROS_INFO("Packet sent successfully"); 
    }
    return 0;
}

void DeepUdpCtrl::StandUp() {

    ROS_INFO_STREAM("StandUp StandUp StandUp StandUp StandUp"<<std::endl);
    command.code = 0x21010202;
    command.type = 0;
    command.size = 0;
    sendPacket((uint8_t*)&command, sizeof(command));
    ros::Duration(0.2).sleep(); // 替换rclcpp::sleep_for

    command.code = 0x21010D06; // 移动模式
    command.type = 0;
    command.size = 0;
    sendPacket((uint8_t*)&command, sizeof(command));
    ros::Duration(0.2).sleep();

    command.code = 0x21010C03; //自主模式
    command.type = 0;
    command.size = 0;
    sendPacket((uint8_t*)&command, sizeof(command));
	
	ros::Duration(0.2).sleep();

    command.code = 0x21012109; //开启停障
    command.type = 0;
    command.size = 0x20;
    sendPacket((uint8_t*)&command, sizeof(command));
}

void DeepUdpCtrl::HeartBeatCallback(const ros::TimerEvent& event) { // 新增了一个参数输入
    command.code = 0x21040001;
    command.type = 0;
    command.size = 0;
    sendPacket((uint8_t*)&command, sizeof(command));
}

// 运动功能
void DeepUdpCtrl::MoveSkillscallback(const homi_speech_interface::RobdogAction& msg) { // 新增了一个参数输入
    ROS_INFO("actiontype: %s, actionargument: %s", msg.actiontype.c_str(), msg.actionargument.c_str()); 
    std::string actiontype = msg.actiontype;
    // 跟随或避障
    if (actiontype == "followMe"){
      std::string follome = msg.actionargument; // 详细的运动技能
      if (follome == "on") {
        command.code = 0x21012109;
        command.type = 0;
        command.size = 0xC0;
        sendPacket((uint8_t*)&command, sizeof(command));
      }else if (follome == "off") {
        command.code = 0x21012109;
        command.type = 0;
        command.size = 0x00;
        sendPacket((uint8_t*)&command, sizeof(command));
      }

    }
    // 运动模式
    if (actiontype == "sportMode"){
      std::string SportMode = msg.actionargument; 
      // SportMode = msg.actionargument; 
      if (SportMode == "walk") {
          command.code = 0x21010300; // 平地低速步态
          command.type = 0;
          command.size = 0;
          sendPacket((uint8_t*)&command, sizeof(command));
      }
      else if (SportMode == "run") {              
          command.code = 0x21010303; // 平地高速步态
          command.type = 0;
          command.size = 0;
          sendPacket((uint8_t*)&command, sizeof(command));
      }
      else if (SportMode == "stairClimbe") {
          command.code = 0x21010407; // 高踏步越障步态
          command.type = 0;
          command.size = 0;
          sendPacket((uint8_t*)&command, sizeof(command));
      }
      else if (SportMode == "climbe") {
          command.code = 0x21010402; // 抓地越障步态
          command.type = 0;
          command.size = 0;
          sendPacket((uint8_t*)&command, sizeof(command));
      }
    // 牵引模式后续会提供接口
    //   else if (SportMode == "traction") {
    //       command.code = 0x21010300; // 平地低速步态
    //       command.type = 0;
    //       command.size = 0;
    //       sendPacket((uint8_t*)&command, sizeof(command));
    //   }
    }
    // 运动技能
    if (actiontype == "motorSkill"){
        /*
        扭身体 0x21010204
        翻身 0x21010205
        太空步 0x2101030C
        后空翻 0x21010502
        打招呼 0x21010507
        向前跳 0x2101050B
        扭身跳 0x2101020D
        
        还没实现的指令：
            比心：fingerHeart 
            作yi:makeBow
        */
        std::string MoveSkill = msg.actionargument;
        // 原本：在站立和趴下之间切换【如果在站立情况下再喊站立会导致趴下】
        int robdog_status = getRobotBasicState();// 判断当前机器人的状态
        if(MoveSkill == "standUp"){// 趴下状态 && robdog_status == 1
//            if(robot_basic_state!=1 || robot_gait_state!=0 || robot_motion_state!=0) return;
            // system("/home/<USER>/updateexpression.sh /home/<USER>/resource/眨眼困.mp4"); 
            deep_ctl(DEEP_CMD_LIGHT, DEEP_CMD_LIGHT_8, 0);
            command.code = 0x21010202;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
        }

        else if(MoveSkill == "getDown"){ // && robdog_status != 1){ 
//            if(robot_basic_state!=6 || robot_motion_state!=0) return;
            system("/home/<USER>/updateexpression.sh /home/<USER>/resource/眨眼困.mp4"); 
            deep_ctl(DEEP_CMD_LIGHT, DEEP_CMD_LIGHT_9, 0); // 白色半亮
            command.code = 0x21010202;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
        }

        else if(MoveSkill == "twistBody"){
//            if(robot_basic_state!=6 || robot_motion_state!=0) return;
            deep_ctl(DEEP_CMD_LIGHT, DEEP_CMD_LIGHT_8, 0);
            command.code = 0x21010204;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
            // system("/home/<USER>/updateexpression2.sh /home/<USER>/resource/vedio/twist/stareyes.mp4 8 /home/<USER>/resource/default.mp4"); 
            // playAudio("/home/<USER>/resource/audio/twist/151695984076845056.wav");
            std::thread audioTwist(getAndPlayRandomFilePath, "/home/<USER>/resource/audio/twist/");
            audioTwist.detach();
            std::thread expressTwist(execute_script, "/home/<USER>/resource/vedio/twist/");
            expressTwist.detach();
        }
        else if(MoveSkill == "twistBody_emergency"){ // 合大演示需求
//            if(robot_basic_state!=6 || robot_motion_state!=0) return;
            system("/home/<USER>/updateexpression.sh /home/<USER>/resource/眨眼+开心+星星眼.mp4"); 
            std::thread t_audio(getAndPlayRandomFilePath, "/home/<USER>/resource/audio/touch/");
            t_audio.detach();
            deep_ctl(DEEP_CMD_LIGHT, DEEP_CMD_LIGHT_8, 0);
            command.code = 0x21010204;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));

        }
        else if(MoveSkill == "turnOver"){
//            if(robot_basic_state!=1 || robot_gait_state!=0 || robot_motion_state!=0) return;
            system("/home/<USER>/updateexpression.sh /home/<USER>/resource/眨眼+开心+星星眼.mp4"); 
            command.code = 0x21010205;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
        }
        
        else if(MoveSkill == "backflip"){
//            if(robot_basic_state!=1 || robot_gait_state!=0 || robot_motion_state!=0) return;
            system("/home/<USER>/updateexpression.sh /home/<USER>/resource/眨眼+开心+星星眼.mp4"); 
            command.code = 0x21010502;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
        }
        else if(MoveSkill == "greeting"){
  //          if(robot_basic_state!=1 || robot_gait_state!=0 || robot_motion_state!=0) return;
            deep_ctl(DEEP_CMD_LIGHT, DEEP_CMD_LIGHT_8, 0);
            command.code = 0x21010507;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
            // system("/home/<USER>/updateexpression2.sh /home/<USER>/resource/vedio/hello/heart.mp4 8 /home/<USER>/resource/default.mp4"); 
            // playAudio("/home/<USER>/resource/audio/hello/151363243288358912.wav");
            std::thread expressGreet(execute_script, "/home/<USER>/resource/vedio/hello/");
            expressGreet.detach();
            std::thread audioGreet(getAndPlayRandomFilePath, "/home/<USER>/resource/audio/hello/");
            audioGreet.detach();
        }
        else if(MoveSkill == "greeting_emergency"){ // 合大演示需求
//            if(robot_basic_state!=1 || robot_gait_state!=0 || robot_motion_state!=0) return;
            // system("/home/<USER>/updateexpression.sh /home/<USER>/resource/眨眼+开心+星星眼.mp4"); 
            deep_ctl(DEEP_CMD_LIGHT, DEEP_CMD_LIGHT_8, 0);
            command.code = 0x21010507;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
            std::thread expressGreet(execute_script, "/home/<USER>/resource/vedio/hello/");
            expressGreet.detach();
            std::thread audioGreet(getAndPlayRandomFilePath, "/home/<USER>/resource/audio/hello/");
            audioGreet.detach();
        }
        else if(MoveSkill == "jumpForward"){
//            if(robot_basic_state!=1 || robot_gait_state!=0 || robot_motion_state!=0) return;
            system("/home/<USER>/updateexpression.sh /home/<USER>/resource/step1_戴墨镜.mp4"); 
            deep_ctl(DEEP_CMD_LIGHT, DEEP_CMD_LIGHT_8, 0);
            command.code = 0x2101050B;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
        }
        else if(MoveSkill == "twistJump"){
//            if(robot_basic_state!=6 || robot_motion_state!=0) return;
            system("/home/<USER>/updateexpression.sh /home/<USER>/resource/step1_戴墨镜.mp4"); 
            command.code = 0x2101020D;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
        }
        else if(MoveSkill == "sitDown"){
//            if(robot_basic_state!=6 || robot_motion_state!=0) return;
            command.code = 0x21010202;
            command.type = 0;
            command.size = 0;  // 指令值
            sendPacket((uint8_t*)&command, sizeof(command));
        }
        else if(MoveSkill == "twistAss"){ // 蹦达 twistAss
//            if(robot_basic_state!=6 || robot_motion_state!=0) return;
            command.code = 0x21010508;
            command.type = 0;
            command.size = 0;  // 指令值
            sendPacket((uint8_t*)&command, sizeof(command));
            std::thread expressGreet(execute_script, "/home/<USER>/resource/vedio/jumping/");
            expressGreet.detach();
            std::thread audioGreet(getAndPlayRandomFilePath, "/home/<USER>/resource/audio/jumping/");
            audioGreet.detach();
        }
        else if(MoveSkill == "shakeBody"){ // 摇摆 shakeBody
//		if(robot_basic_state!=6 || robot_motion_state!=0) return;
            command.code = 0x21010509;
            command.type = 0;
            command.size = 0;  // 指令值
            sendPacket((uint8_t*)&command, sizeof(command));
            std::thread expressGreet(execute_script, "/home/<USER>/resource/vedio/shake/");
            expressGreet.detach();
            std::thread audioGreet(getAndPlayRandomFilePath, "/home/<USER>/resource/audio/shake/");
            audioGreet.detach();
        }
        else if(MoveSkill == "dance"){
//            if(robot_basic_state!=6 || robot_motion_state!=0) return;
            command.code = 0x2101030C;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
            std::thread expressGreet(execute_script, "/home/<USER>/resource/vedio/dance/");
            expressGreet.detach();
            std::thread audioGreet(getAndPlayRandomFilePath, "/home/<USER>/resource/audio/dance/");
            audioGreet.detach();
        }
    }
    if (actiontype == "emergencyStop"){
        command.code = 0x21020C0E;
        command.type = 0;
        command.size = 2;  // 指令值
        sendPacket((uint8_t*)&command, sizeof(command));
    }
    if (actiontype == "resetZero"){ 
        command.code = 0x21010C05;
        command.type = 0;
        command.size = 2;  // 指令值
        sendPacket((uint8_t*)&command, sizeof(command));
    }
    // 和强化学习有关的
    if (actiontype == "ObstaclesRL"){ 
        command.code = 0x21010529;
        command.type = 0;
        command.size = 2;  // 指令值
        sendPacket((uint8_t*)&command, sizeof(command));
    }
    if (actiontype == "FlatRL"){ 
        command.code = 0x2101052a;
        command.type = 0;
        command.size = 2;  // 指令值
        sendPacket((uint8_t*)&command, sizeof(command));
    }
    if (actiontype == "ExitRL"){ 
        command.code = 0x2101052b;
        command.type = 0;
        command.size = 2;  // 指令值
        sendPacket((uint8_t*)&command, sizeof(command));
    }
    if (actiontype == "NavCtrl"){
        std::string MoveSkill = msg.actionargument;
        if(MoveSkill == "AutoMode"){ 
            command.code = 0x21010C03; //自主模式
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
        }
    }
    // 和强化学习有关的
    if (actiontype == "gaitControl"){ 
        std::string MoveSkill = msg.actionargument;
        if(MoveSkill == "obstacleCross"){ 
            command.code = 0x21010529;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
        }
        if(MoveSkill == "flatGround"){ 
            command.code = 0x2101052a;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
        }
        if(MoveSkill == "exit"){ 
            command.code = 0x2101052b;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
        }
    }
    if (actiontype == "video_record"){
        std::string angle = msg.actionargument;
        float real_angle=std::stoi(angle)*static_cast<float>(M_PI) / 180.0f;
        positionCtrl(0.0f,0.0f,real_angle);
    }
}
void DeepUdpCtrl::timerCallbackForRobotMove(const ros::TimerEvent&) {
    if(g_robot_motion.event == "robot_move"){
        if(g_robot_motion.x != 0){
            command.code = 0x21010130;
            command.type = 0;
            command.size = (g_robot_motion.x>0)?15000:-15000;  
            sendPacket((uint8_t*)&command, sizeof(CommandHead));
        }
        if(g_robot_motion.y != 0){
            command.code = 0x21010131;
            command.type = 0;
            command.size =  (g_robot_motion.y>0)?-20000:20000;  
            sendPacket((uint8_t*)&command, sizeof(CommandHead));
        }
        if (g_robot_motion.x==0&&g_robot_motion.y==0){
            command.code = 0x21010130;
            command.type = 0;
            command.size = 0;  
            sendPacket((uint8_t*)&command, sizeof(CommandHead));
            command.code = 0x21010131;
            command.type = 0;
            command.size = 0;  
            sendPacket((uint8_t*)&command, sizeof(CommandHead));
            ROS_INFO_STREAM("send robot_move packet and value is 0"<<std::flush);
        }
    }
    else if(g_robot_motion.event == "robot_view"){
        if(g_robot_motion.yaw != 0){
            command.code = 0x21010135;
            command.type = 0;
            command.size = (g_robot_motion.yaw>0)?15000:-15000;
            sendPacket((uint8_t*)&command, sizeof(command));
        }
        if(g_robot_motion.pitch != 0){
            command.code = 0x21010102; // 移动模式下调整俯仰角(死区是20000)
            command.type = 0;
            command.size = (g_robot_motion.pitch>0)?25000:-25000;
            sendPacket((uint8_t*)&command, sizeof(command));
        }
        if(g_robot_motion.yaw == 0&&g_robot_motion.pitch == 0){
            command.code = 0x21010135;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
            command.code = 0x21010102;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
            ROS_INFO_STREAM("send robot_view packet and value is 0"<<std::flush);
        }
    }
}


void DeepUdpCtrl::continueMovecallback(const homi_speech_interface::ContinueMove& robot_motion){
    ROS_INFO("Recieve Remote Ctrl robot_motion.event=%s,x=%d,y=%d,z=%d,yaw=%d,pitch=%d,roll=%d",robot_motion.event.c_str(),robot_motion.x,robot_motion.y,robot_motion.z,robot_motion.yaw,robot_motion.pitch,robot_motion.roll);
    g_robot_motion=robot_motion;
    if(robot_motion.event == "robot_move"){
        if (robot_motion.x != 0||robot_motion.y!= 0||robot_motion.z!=0) {
            if (!timer_active) {
                command.code = 0x21010D06; // 移动模式
                command.type = 0;
                command.size = 0;
                sendPacket((uint8_t*)&command, sizeof(CommandHead));
                command.code = 0x21010C02; // 手动模式
                command.type = 0;
                command.size = 0;
                sendPacket((uint8_t*)&command, sizeof(command));
                timer = ros::NodeHandle().createTimer(ros::Duration(0.03), &DeepUdpCtrl::timerCallbackForRobotMove,this);
                timer_active = true;
                ROS_INFO("Timer is running,Robot move started.");
            }
        }
        else
        {
            ROS_INFO_STREAM("Recieve robot move cmd.And value is 0"<<std::flush);
            command.code = 0x21010130;
            command.type = 0;
            command.size = 0;  
            sendPacket((uint8_t*)&command, sizeof(CommandHead));
            command.code = 0x21010131;
            command.type = 0;
            command.size = 0;  
            sendPacket((uint8_t*)&command, sizeof(CommandHead));
            ROS_INFO_STREAM("send robot_move packet and value is 0"<<std::flush);
        }
    }
    else if (robot_motion.event == "robot_view"){
        if (robot_motion.pitch != 0||robot_motion.yaw!= 0||robot_motion.roll!=0) {
            if (!timer_active) {
                command.code = 0x21010D06; // 移动模式
                command.type = 0;
                command.size = 0;
                sendPacket((uint8_t*)&command, sizeof(CommandHead));
                command.code = 0x21010C02; // 手动模式
                command.type = 0;
                command.size = 0;
                sendPacket((uint8_t*)&command, sizeof(command));
                command.code = 0x21012109; //开启停障
                command.type = 0;
                command.size = 0;
                sendPacket((uint8_t*)&command, sizeof(command));
                timer = ros::NodeHandle().createTimer(ros::Duration(0.03), &DeepUdpCtrl::timerCallbackForRobotMove,this);
                timer_active = true;
                ROS_INFO("Timer is running,Robot view started.");
            }
        }
        else{
            ROS_INFO_STREAM("Recieve robot view cmd.And value is 0"<<std::flush);
            command.code = 0x21010135;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
            command.code = 0x21010102;
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
            ROS_INFO_STREAM("send robot_view packet and value is 0"<<std::flush);
        }
    }
    // 移动固定距离和转向
    else if(robot_motion.event == "stepMode"){
        // ROS_INFO("Recieve Remote Ctrl robot_motion.event=%s,x=%d,y=%d,z=%d,yaw=%d,pitch=%d,roll=%d",robot_motion.event.c_str(),robot_motion.x,robot_motion.y,robot_motion.z,robot_motion.yaw,robot_motion.pitch,robot_motion.roll);
        ROS_INFO("stepMode: x = %d, y = %d, yaw = %d", robot_motion.x, robot_motion.y, robot_motion.yaw);
        // robot_motion.x表示向前走的步数，一步表示20cm
        // robot_motion.y表示向左走的步数，一步表示20cm
        // robot_motion.yaw表示向右转的角度(角度制)
        float stepx = robot_motion.x * 0.2;
        float stepy = robot_motion.y * 0.2;
        positionCtrl(stepx,stepy,((robot_motion.yaw)*static_cast<float>(M_PI) / 180.0f));
    }
    else{
        ROS_INFO_STREAM("Recieve robot STOP cmd"<<std::flush);
        command.code = 0x21010135;
        command.type = 0;
        command.size = 0;
        sendPacket((uint8_t*)&command, sizeof(command));
        command.code = 0x21010102;
        command.type = 0;
        command.size = 0;
        sendPacket((uint8_t*)&command, sizeof(command));
        command.code = 0x21010130;
        command.type = 0;
        command.size = 0;  
        sendPacket((uint8_t*)&command, sizeof(CommandHead));
        command.code = 0x21010131;
        command.type = 0;
        command.size = 0;  
        sendPacket((uint8_t*)&command, sizeof(CommandHead));
        ROS_INFO_STREAM("send robot_view and robot_move packet and  ALL value is 0 AS recv STOP cmd"<<std::flush);
        if (timer_active) {
            command.code = 0x21010C03; // 自主模式
            command.type = 0;
            command.size = 0;
            sendPacket((uint8_t*)&command, sizeof(command));
            timer.stop();
            timer_active = false;
            ROS_INFO("Timer is stopped,Robot move or view stopped.");
        }else{
            ROS_INFO("Timer has already stopped.");
        }
    }
}

// 收到速度之后的回调函数(把指令发给机器狗)
void DeepUdpCtrl::velCmdCallback(const geometry_msgs::TwistConstPtr& msg) {
    // ROS_INFO_STREAM("recv robot cmd x: " << msg->linear.x << ", y: " << msg->linear.y << ", z: " << msg->angular.z);
    ROS_INFO("Published velocity: linear.x = %f, linear.y = %f, linear.z = %f, angular.x = %f, angular.y = %f, angular.z = %f", msg->linear.x, msg->linear.y, msg->linear.z, msg->angular.x, msg->angular.y, msg->angular.z);

    
    data.head.code = 0x140;
    data.head.type = 1;
    data.head.size = sizeof(double);
    memcpy(data.data, &msg->linear.x, sizeof(double));
    sendPacket((uint8_t*)&data, sizeof(CommandHead) + sizeof(double)); 

    data.head.code = 0x145;
    memcpy(data.data, &msg->linear.y, sizeof(double));
    sendPacket((uint8_t*)&data, sizeof(CommandHead) + sizeof(double));

    data.head.code = 0x141;
    memcpy(data.data, &msg->angular.z, sizeof(double));
    sendPacket((uint8_t*)&data, sizeof(CommandHead) + sizeof(double));          

}

// 向机器狗请求机器人状态信息【在线程里面去执行】
void DeepUdpCtrl::send_command() {
    // CommandHead command_head(0x0901, 0, 1);
    data.head.code = 0x0901;
    data.head.type = 1;
    data.head.size = 0;
    sendPacket((uint8_t*)&data, sizeof(CommandHead));
    // std::this_thread::sleep_for(std::chrono::milliseconds(20)); // 1/50 second
}

// 在这里将机器人状态信息发布出去【应该在界面按键触发之后再去发布还是定时发布？】
void DeepUdpCtrl::publishRobdagStateToQt(std::string robot_state_details) {
    homi_speech_interface::RobdogState stateMsg;
    // int robot_state_int = getRobotBasicState();
    // int robot_step_state_int = getRobotGaitState();
    // std::array<double, 3> robot_vel_array = getVelBody();

    // auto robotStateIt = robotStateMap.find(robot_state_int);
    // if (robotStateIt != robotStateMap.end()) {
    //     // std::cout << "状态描述: " << it->second << std::endl;
    //     stateMsg.robot_state = robotStateIt->second;
    // } else {
    //     // std::cout << "未知状态码" << std::endl;
    //     stateMsg.robot_state = "未知状态码";
    // }

    // auto gaitStateIt = gaitStateMap.find(robot_step_state_int);
    // if (gaitStateIt != gaitStateMap.end()) {
    //     // std::cout << "步态描述: " << it->second << std::endl;
    //     stateMsg.robot_step_state = gaitStateIt->second;
    // } else {
    //     // std::cout << "未知步态码" << std::endl;
    //     stateMsg.robot_step_state = "未知步态码";
    // }
    // stateMsg.robot_vel = "(" + std::to_string(robot_vel_array[0]) + ", " + std::to_string(robot_vel_array[1]) + ", " + std::to_string(robot_vel_array[2])+ ")";
    // // double vel_body[3] = {x_vel,y_vel,yaw_vel} = getVelBody();
    
    stateMsg.robot_state_details = robot_state_details;
    robotStatusPub_.publish(stateMsg);
}

void DeepUdpCtrl:: handle_UDP_data(char *data,size_t length)
{
    head_t* header = reinterpret_cast<head_t*>(data);
    rsp_body_t body;
    pk_t *pkg=(pk_t*)data;
    ROS_INFO("pkg->rsp_body.sensor.sensor_id=%d",ntohl(pkg->rsp_body.sensor.sensor_id));
    ROS_INFO("pkg->rsp_body.sensor.value=%d",ntohl(pkg->rsp_body.sensor.value));
    if (length >= sizeof(head_t)) {
        switch (ntohl(header->cmd)) {
            case DEEP_CMD_SENSOR:
            ROS_INFO("Recieve UDP data which is sensor res");
            //语音、动作、表情、灯光
                if (ntohl(pkg->rsp_body.sensor.value)==0x30){
                    ROS_INFO("HEAD has been touched");
                    command={0};
                    command.code = DEEP_CMD_MOTION_1; //指令码扭一扭
                    command.size = 0;// 指令值
                    command.type = 0; // 指令类型,复杂/简单
                    sendPacket((uint8_t*)&command, sizeof(command));
                    std::thread t_expression(execute_script, "/home/<USER>/resource/vedio/touch/");
                    t_expression.detach();
                    std::thread t_audio(getAndPlayRandomFilePath, "/home/<USER>/resource/audio/touch/");
                    t_audio.detach();
                    deep_ctl(DEEP_CMD_LIGHT, DEEP_CMD_LIGHT_8, 0);
                }
                // else if (ntohl(pkg->rsp_body.sensor.value)==0x31)
                // {
                //     ROS_INFO("HEAD has been not touched");
                //     std::thread t_detouch(specific_expression, "/home/<USER>/resource/vedio/默认_眨眼睛.mp4");
                //     t_detouch.detach();
                // }
                break;
            case DEEP_CMD_POWER:
                if (length >= sizeof(head_t) + sizeof(power_status)) {
                    homi_speech_interface::ProprietySet repMsg;
                    memcpy(&body.powerAttribute, data + sizeof(head_t), sizeof(power_status));
                    repMsg.cmd=POWER_LEVEL_FROM_NODE;
                    repMsg.value=body.powerAttribute.power_level;
                    repMsg.exvalue=body.powerAttribute.chargeStatus;
                    statusReportPub.publish(repMsg);
                    ROS_INFO("Recieve battery info,going to pubulish.Battery level is[%d],Battery charging state is [%d]",repMsg.value,repMsg.exvalue);
                }
                else
                    ROS_ERROR_STREAM("Battery's data is wrong");

            default:
                ROS_ERROR_STREAM( "Unknown command code: " << std::hex << static_cast<int>(header->cmd) << std::dec << std::endl);
                break;
        }
    } else {
        ROS_ERROR_STREAM( "Invalid packet length: " << length << std::endl);
    }
}

void DeepUdpCtrl::deep_ctl(int cmdID,int cmdValue,int cmdValueEx)
{
    pk_t pkg_deep;
    memset(&pkg_deep,0,sizeof(pkg_deep));
    // ROS_INFO_STREAM("Start to control deep robot,cmd id is "<<std::hex<<cmdID);
    ROS_INFO("Start to control deep robot,cmd id is 0x%x,cmdValue=0x%x,cmdValueEx=0x%x",cmdID,cmdValue,cmdValueEx);
    // 设置头部信息
    pkg_deep.head.len=hton_int(sizeof(pk_t));
    pkg_deep.head.stat = hton_int(DEEP_STATUS_RUN); 
    pkg_deep.head.cmd=hton_int(cmdID);
    std::string directory_head = "/data/test/audio/head/";
    std::string directory_back = "/data/test/audio/back/";
    std::string randomFile ;

    switch (cmdID)
    {
    case DEEP_CMD_LIGHT:
      pkg_deep.rqt_body.light.color = hton_int(cmdValue); 
      pkg_deep.rqt_body.light.duration = 5000;          // 假设持续时间为5秒
      break;
    case DEEP_CMD_EXPRESSION:
      pkg_deep.rqt_body.expression.type = cmdValue; 
      break;
    case DEEP_CMD_MOTION:
      pkg_deep.rqt_body.motion.type = cmdValue; 
      break;
    case DEEP_CMD_NET:
      pkg_deep.rqt_body.net.netswitch = cmdValue; 
      break;
    case DEEP_CMD_MOVEANDRO:
      pkg_deep.rqt_body.attitude.angle=cmdValue;
      pkg_deep.rqt_body.attitude.angle=cmdValueEx;
    case DEEP_CMD_FLASHLIGHT:
      if (cmdValue==0)
        pkg_deep.rqt_body.flashlight.brightness=0;
      else
        pkg_deep.rqt_body.flashlight.brightness=hton_int(cmdValue);
        break;
    case DEEP_CMD_AI_MOTION:
      pkg_deep.rqt_body.intelligentSwitch.type =cmdValue;
      break;
    case DEEP_CMD_AUDIO:
        // if (cmdValue==DEEP_CMD_AUDIO_HEAD)
        //   randomFile=getRandomFilePath(directory_head);
        // else 
        //   randomFile=getRandomFilePath(directory_back);
        if (!randomFile.empty()) {
            ROS_INFO_STREAM("Play audio file: " << randomFile << std::endl);
        } else {
            ROS_ERROR_STREAM("No files found in the directory" << std::endl);
        }
      memcpy(pkg_deep.rqt_body.audio.path,randomFile.c_str(),sizeof(pkg_deep.rqt_body.audio.path));
      break;
    default:
      break;
    }
    sendPacketUserDefine((uint8_t*)&pkg_deep, sizeof(pkg_deep));
}


// Getters
int DeepUdpCtrl::getRobotBasicState() const { return robot_basic_state; }
int DeepUdpCtrl::getRobotGaitState() const { return robot_gait_state; }
std::array<double, 3> DeepUdpCtrl::getRPY() const { return {rpy[0], rpy[1], rpy[2]}; }
std::array<double, 3> DeepUdpCtrl::getRPYVel() const { return {rpy_vel[0], rpy_vel[1], rpy_vel[2]}; }
std::array<double, 3> DeepUdpCtrl::getXYZAcc() const { return {xyz_acc[0], xyz_acc[1], xyz_acc[2]}; }
std::array<double, 3> DeepUdpCtrl::getPosWorld() const { return {pos_world[0], pos_world[1], pos_world[2]}; }
std::array<double, 3> DeepUdpCtrl::getVelWorld() const { return {vel_world[0], vel_world[1], vel_world[2]}; }
std::array<double, 3> DeepUdpCtrl::getVelBody() const { return {vel_body[0], vel_body[1], vel_body[2]}; }
unsigned DeepUdpCtrl::getTouchDownAndStairTrot() const { return touch_down_and_stair_trot; }
bool DeepUdpCtrl::getIsCharging() const { return is_charging; }
unsigned DeepUdpCtrl::getErrorState() const { return error_state; }
unsigned DeepUdpCtrl::getRobotMotionState() const { return robot_motion_state; }
bool DeepUdpCtrl::getZeroPositionFlag() const { return zero_position_flag; }
double DeepUdpCtrl::getBatteryLevel() const { return battery_level; }
unsigned DeepUdpCtrl::getTaskState() const { return task_state; }
bool DeepUdpCtrl::getIsRobotNeedMove() const { return is_robot_need_move; }
// unsigned DeepUdpCtrl::getInvalidData() const { return invalid_data; }
std::array<double, 2> DeepUdpCtrl::getUltrasound() const { return {ultrasound[0], ultrasound[1]}; }

// Setters
void DeepUdpCtrl::setRobotBasicState(int state) { robot_basic_state = state; }
void DeepUdpCtrl::setRobotGaitState(int state) { robot_gait_state = state; }
void DeepUdpCtrl::setRPY(const std::array<double, 3>& angles) { rpy[0] = angles[0]; rpy[1] = angles[1]; rpy[2] = angles[2]; }
void DeepUdpCtrl::setRPYVel(const std::array<double, 3>& velocities) { rpy_vel[0] = velocities[0]; rpy_vel[1] = velocities[1]; rpy_vel[2] = velocities[2]; }
void DeepUdpCtrl::setXYZAcc(const std::array<double, 3>& acc) { xyz_acc[0] = acc[0]; xyz_acc[1] = acc[1]; xyz_acc[2] = acc[2]; }
void DeepUdpCtrl::setPosWorld(const std::array<double, 3>& pos) { pos_world[0] = pos[0]; pos_world[1] = pos[1]; pos_world[2] = pos[2]; }
void DeepUdpCtrl::setVelWorld(const std::array<double, 3>& vel) { vel_world[0] = vel[0]; vel_world[1] = vel[1]; vel_world[2] = vel[2]; }
void DeepUdpCtrl::setVelBody(const std::array<double, 3>& vel) { vel_body[0] = vel[0]; vel_body[1] = vel[1]; vel_body[2] = vel[2]; }
void DeepUdpCtrl::setTouchDownAndStairTrot(unsigned status) { touch_down_and_stair_trot = status; }
void DeepUdpCtrl::setIsCharging(bool charging) { is_charging = charging; }
void DeepUdpCtrl::setErrorState(unsigned state) { error_state = state; }
void DeepUdpCtrl::setRobotMotionState(int state) { robot_motion_state = state; }
void DeepUdpCtrl::setZeroPositionFlag(bool flag) { zero_position_flag = flag; }
void DeepUdpCtrl::setBatteryLevel(double level) { battery_level = level; }
void DeepUdpCtrl::setTaskState(unsigned state) { task_state = state; }
void DeepUdpCtrl::setIsRobotNeedMove(bool needMove) { is_robot_need_move = needMove; }
// void DeepUdpCtrl::setInvalidData(unsigned data) { invalid_data = data; }
void DeepUdpCtrl::setUltrasound(const std::array<double, 2>& data) { ultrasound[0] = data[0]; ultrasound[1] = data[1]; }
