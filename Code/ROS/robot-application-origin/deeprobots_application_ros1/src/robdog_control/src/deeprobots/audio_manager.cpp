#include <iostream>
#include <ros/ros.h>
#include <homi_speech_interface/AssistantAbort.h>
#include <homi_speech_interface/SetWakeEvent.h>
#include <cstdlib>
class SpeechServiceManager {
public:
    SpeechServiceManager(const std::string& device_name, const std::string& filePath)
        : device_name(device_name), filePath(filePath) {
        // 关闭语音助手
        homi_speech_interface::AssistantAbort srv;
        if (!ros::service::call("/homi_speech/helper_assistant_abort_service", srv)) {
            throw std::runtime_error("Failed to call /homi_speech/helper_assistant_abort_service");
        }

        // 关闭语音唤醒
        homi_speech_interface::SetWakeEvent wkevent;
        wkevent.request.target = false;
        if (!ros::service::call("/audio_node/set_wake_event_service", wkevent)) {
            throw std::runtime_error("Failed to call /audio_node/set_wake_event_service to disable wake event");
        }
    }

    ~SpeechServiceManager() {
        // 开启语音唤醒事件
        homi_speech_interface::SetWakeEvent wkevent;
        wkevent.request.target = true;
        if (!ros::service::call("/audio_node/set_wake_event_service", wkevent)) {
            std::cerr << "Failed to call /audio_node/set_wake_event_service to enable wake event" << std::endl;
        }
    }

    void playAudio() {
        std::string command = "aplay -D " + device_name + " \"" + filePath + "\"";
        int result = std::system(command.c_str());
        if (result != 0) {
            throw std::runtime_error("Failed to play audio");
        }
    }

private:
    std::string device_name;
    std::string filePath;
};


/*int main(int argc, char** argv) {
    ros::init(argc, argv, "speech_service_manager");
    ros::NodeHandle nh;

    if (argc < 3) {
        std::cerr << "Usage: " << argv[0] << " <device_name> <file_path>" << std::endl;
        return 1;
    }

    std::string device_name = argv[1];
    std::string filePath = argv[2];

    try {
        SpeechServiceManager manager(device_name, filePath);
        manager.playAudio();
    } catch (const std::exception& e) {
        std::cerr << "Exception caught: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}*/