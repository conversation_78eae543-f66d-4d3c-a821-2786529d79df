#include <iostream>
#include <fstream>
#include <alsa/asoundlib.h>
#define CHANNELS 2
#define BIT_DEPTH 16
class AudioPlayer {
public:
    AudioPlayer(const std::string& deviceName, int cardNumber);
    ~AudioPlayer();

    bool play(const std::string& fileName);
    bool setVolume(long volume);

private:
    snd_pcm_t *pcmHandle;
    snd_mixer_t *mixerHandle;
    snd_mixer_elem_t *mixerElem;
    std::string deviceName;
    int cardNumber;
    snd_pcm_hw_params_t *hwParams;
    long volume;  // Volume level (0-100)

    bool openPcmDevice();
    bool openMixerDevice();
    void closePcmDevice();
    void closeMixerDevice();
};
