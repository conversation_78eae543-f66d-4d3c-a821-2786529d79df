#include "audio_ctrl.h"
AudioPlayer::AudioPlayer(const std::string& deviceName, int cardNumber)
    : deviceName(deviceName), cardNumber(cardNumber), pcmHandle(nullptr), mixer<PERSON>andle(nullptr), mixer<PERSON><PERSON>(nullptr), hwParams(nullptr), volume(50) {
    if (!openPcmDevice()) {
        throw std::runtime_error("Failed to open PCM device");
    }
    if (!openMixerDevice()) {
        throw std::runtime_error("Failed to open mixer device");
    }
}

AudioPlayer::~AudioPlayer() {
    closePcmDevice();
    closeMixerDevice();
}

bool AudioPlayer::openPcmDevice() {
    int err;
    if ((err = snd_pcm_open(&pcmHandle, deviceName.c_str(), SND_PCM_STREAM_PLAYBACK, 0)) < 0) {
        std::cerr << "Failed to open PCM device: " << snd_strerror(err) << std::endl;
        return false;
    }

    snd_pcm_hw_params_alloca(&hwParams);
    snd_pcm_hw_params_any(pcmHandle, hwParams);
    snd_pcm_hw_params_set_access(pcmHandle, hwParams, SND_PCM_ACCESS_RW_INTERLEAVED);
    snd_pcm_hw_params_set_format(pcmHandle, hwParams, SND_PCM_FORMAT_S16_LE);
    snd_pcm_hw_params_set_channels(pcmHandle, hwParams, 2);
    snd_pcm_hw_params_set_rate(pcmHandle, hwParams, 44100, 0);

    if ((err = snd_pcm_hw_params(pcmHandle, hwParams)) < 0) {
        std::cerr << "Failed to set hardware parameters: " << snd_strerror(err) << std::endl;
        return false;
    }
    return true;
}

bool AudioPlayer::openMixerDevice() {
    int err;
    std::string mixerDeviceName = "hw:" + std::to_string(cardNumber);
    if ((err = snd_mixer_open(&mixerHandle, 0)) < 0) {
        std::cerr << "Failed to open mixer: " << snd_strerror(err) << std::endl;
        return false;
    }

    if ((err = snd_mixer_attach(mixerHandle, mixerDeviceName.c_str())) < 0) {
        std::cerr << "Failed to attach mixer: " << snd_strerror(err) << std::endl;
        return false;
    }

    if ((err = snd_mixer_selem_register(mixerHandle, nullptr, nullptr)) < 0) {
        std::cerr << "Failed to register mixer element: " << snd_strerror(err) << std::endl;
        return false;
    }

    if ((err = snd_mixer_load(mixerHandle)) < 0) {
        std::cerr << "Failed to load mixer elements: " << snd_strerror(err) << std::endl;
        return false;
    }

    mixerElem = snd_mixer_first_elem(mixerHandle);
    while (mixerElem) {
        if (strcmp(snd_mixer_selem_get_name(mixerElem), "PCM") == 0) {
            break;
        }
        mixerElem = snd_mixer_elem_next(mixerElem);
    }

    if (!mixerElem) {
        std::cerr << "PCM mixer element not found" << std::endl;
        return false;
    }

    return true;
}

void AudioPlayer::closePcmDevice() {
    if (pcmHandle) {
        snd_pcm_close(pcmHandle);
        pcmHandle = nullptr;
    }
}

void AudioPlayer::closeMixerDevice() {
    if (mixerHandle) {
        snd_mixer_close(mixerHandle);
        mixerHandle = nullptr;
    }
}

bool AudioPlayer::play(const std::string& fileName) {
    FILE *wav_file;    
    size_t read_size;
    char buffer[4096];
    wav_file = fopen(fileName.c_str(), "rb");
    printf("filename[%s]",fileName.c_str());
    if (!wav_file) {
        fprintf(stderr, "Error opening WAV file.\n");
        snd_pcm_close(pcmHandle);
        return false;
    }
        // Read and play WAV file
    while ((read_size = fread(buffer, 1, sizeof(buffer), wav_file)) > 0) {
        if (snd_pcm_writei(pcmHandle, buffer, read_size / (BIT_DEPTH / 8 * CHANNELS)) < 0) {
            fprintf(stderr, "Error writing to PCM device.\n");
            break;
        }
    }
    fclose(wav_file);
    return true;
}

bool AudioPlayer::setVolume(long volume) {
    if (!mixerElem) {
        std::cerr << "Mixer element not initialized" << std::endl;
        return false;
    }

    long minVolume, maxVolume;
    snd_mixer_selem_get_playback_volume_range(mixerElem, &minVolume, &maxVolume);

    long volumeValue = minVolume + (volume * (maxVolume - minVolume) / 100);
    if (snd_mixer_selem_set_playback_volume_all(mixerElem, volumeValue) < 0) {
        std::cerr << "Failed to set volume" << std::endl;
        return false;
    }

    this->volume = volume;
    return true;
}

#ifdef TESTMAIN
int main(int argc, char *argv[]) {
    if (argc != 3) {
        fprintf(stderr, "Usage: %s <wav_file> <volume>\n", argv[0]);
        return 1;
    }
    long volume = strtol(argv[2], NULL, 10);
    try {
        AudioPlayer player("hw:3,0", 3);
        player.setVolume(volume);  
        if (player.play(argv[1])) {
            std::cout << "Playback successful" << std::endl;
        } else {
            std::cerr << "Playback failed" << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
#endif