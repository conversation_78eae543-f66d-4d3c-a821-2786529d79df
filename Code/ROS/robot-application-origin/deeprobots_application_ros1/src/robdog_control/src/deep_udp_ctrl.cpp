//
/*****websocket通讯服务端节点******/
/**********默认端口为19002********/
//

#include "deeprobots/deep_udp_ctrl_node.h"
// #include "rclcpp/rclcpp.hpp"
#include "ros/ros.h"
#include <ros/console.h>
#include <log4cxx/logger.h> 
#include <log4cxx/propertyconfigurator.h>
#include <iostream>
#include <fstream>
#include <tuple>
#include <unordered_map>
#include <map>
#include <chrono>
#include <string>
#include <csignal>  // 新增
#include <signal.h> // 新增
#include <jsoncpp/json/json.h>
#include <ros/package.h>
#include <cstdint> // For fixed-width integer types
#include <array>   // For std::array

#include <thread>       // 引入线程库
#include <atomic>       // 引入原子操作库
#include <chrono>   // 用于 std::chrono
using namespace std;

std::atomic<bool> stopRequested(false);
std::atomic<bool> pauseRequested(false);

 // 定义状态查询表
using StateKey = std::tuple<int32_t, int32_t, int32_t>;
std::map<StateKey, std::string> stateQueryTable = {
  {{1, 0, 0}, "趴下状态"},
  {{1, 0, 11}, "正在执行向前跳"},   // 
  {{4, 0, 0}, "准备起立状态"},
  {{5, 0, 0}, "正在起立状态"},
  {{6, 0, 0}, "力控状态（静止站立）且步态为平地低速步态"},
  {{6, 0, 1}, "正在以平地低速步态踏步或正在根据轴指令扭动身体"}, 
  {{6, 0, 2}, "正在执行扭身体"},   //  system("/home/<USER>/updateexpression2.sh  /home/<USER>/resource/vedio/hello/stareyes.mp4 8  /home/<USER>/resource/vedio/default.mp4");
  {{6, 0, 4}, "正在执行扭身跳"},
  {{6, 2, 0}, "力控状态（静止站立）且步态为通用越障步态"},
  {{6, 2, 1}, "正在以通用越障步态踏步"},
  {{6, 4, 0}, "力控状态（静止站立）且步态为平地中速步态"},
  {{6, 4, 1}, "正在以平地中速步态踏步"},
  {{6, 5, 0}, "力控状态（静止站立）且步态为平地高速步态"},
  {{6, 5, 1}, "正在以平地高速步态踏步"},
  {{6, 6, 0}, "力控状态（静止站立）且步态为抓地越障步态"},
  {{6, 6, 1}, "正在以抓地越障步态踏步"},
  {{6, 12, 1}, "正在执行太空步"},
  {{6, 13, 0}, "力控状态（静止站立）且步态为高踏步越障步态"},
  {{6, 13, 1}, "正在以高踏步越障步态踏步"},
  {{7, 0, 0}, "正在趴下状态"},
  {{8, 0, 0}, "失控保护状态"},
  {{9, 0, 0}, "姿态调整状态"},
  {{11, 0, 0}, "正在执行翻身"},
  {{17, 0, 0}, "回零状态"},
  {{18, 0, 0}, "正在执行后空翻"},
  {{20, 0, 0}, "正在执行打招呼"}  //  system("/home/<USER>/updateexpression2.sh  /home/<USER>/resource/vedio/hello/heart.mp4 8  /home/<USER>/resource/vedio/default.mp4");
    // 摇一摇 system("/home/<USER>/updateexpression2.sh  /home/<USER>/resource/vedio/hello/heart.mp4 8  /home/<USER>/resource/vedio/default.mp4");
    // 扭屁股 【戴墨镜】
};

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
            std::cout << "Caught Ctrl+C (SIGINT)!" << std::endl;
            stopRequested = true;
            break;
        case SIGTSTP:
            std::cout << "Caught Ctrl+Z!" << std::endl;
            pauseRequested = !pauseRequested; // 切换暂停状态
            break;
        default:
            std::cout << "Caught " << signal << std::endl;
            // 处理其他信号
            break;
    }
}
// 外部定义
std::shared_ptr<DeepUdpCtrl> udp_ctrl;
// 解析机器人上报的数据
class RobotState {
public:
    int32_t robot_basic_state = 0;
    int32_t robot_gait_state = 0;
    std::array<double, 3> rpy = {0.0};            // Roll, Pitch, Yaw
    std::array<double, 3> rpy_vel = {0.0};        // Angular velocities

    std::array<double, 3> xyz_acc = {0.0};        // Acceleration in XYZ
    std::array<double, 3> pos_world = {0.0};      // Position in world coordinates
    std::array<double, 3> vel_world = {0.0};      // Velocity in world coordinates
    std::array<double, 3> vel_body = {0.0};       // Velocity in body coordinates
    uint32_t touch_down_and_stair_trot = 0;
    bool is_charging = false;
    uint32_t error_state = 0;
    int32_t robot_motion_state = 0;
    double battery_level = 0.0;
    int32_t task_state = 0;
    bool is_robot_need_move = false;
    bool zero_position_flag = false;
    std::array<double, 2> ultrasound = {0.0};     // Ultrasound readings

    // 构造函数，用于初始化 RobotState 对象
    RobotState(const char* data) {
        if (!data) return;  // 检查数据指针

        std::memcpy(&robot_basic_state, data, sizeof(robot_basic_state));
        udp_ctrl->setRobotBasicState(robot_basic_state);  // 设置基本状态

        std::memcpy(&robot_gait_state, data + sizeof(robot_basic_state), sizeof(robot_gait_state));
        udp_ctrl->setRobotGaitState(robot_gait_state);    // 设置步态状态

        std::memcpy(rpy.data(), data + 8, sizeof(double) * 3);
        udp_ctrl->setRPY(rpy);                             // 设置姿态

        std::memcpy(rpy_vel.data(), data + 32, sizeof(double) * 3);
        udp_ctrl->setRPYVel(rpy_vel);                     // 设置角速度

        std::memcpy(xyz_acc.data(), data + 56, sizeof(double) * 3);
        udp_ctrl->setXYZAcc(xyz_acc);                     // 设置加速度

        std::memcpy(pos_world.data(), data + 80, sizeof(double) * 3);
        udp_ctrl->setPosWorld(pos_world);                 // 设置世界坐标位置

        std::memcpy(vel_world.data(), data + 104, sizeof(double) * 3);
        udp_ctrl->setVelWorld(vel_world);                 // 设置世界坐标速度

        std::memcpy(vel_body.data(), data + 128, sizeof(double) * 3);
        udp_ctrl->setVelBody(vel_body);                   // 设置机体坐标速度

        std::memcpy(&touch_down_and_stair_trot, data + 152, sizeof(touch_down_and_stair_trot));
        udp_ctrl->setTouchDownAndStairTrot(touch_down_and_stair_trot); // 设置触地和阶梯状态

        std::memcpy(&is_charging, data + 156, sizeof(is_charging));
        udp_ctrl->setIsCharging(is_charging);             // 设置充电状态

        std::memcpy(&error_state, data + 160, sizeof(error_state));   // 因为对齐要偏移四个字节
        udp_ctrl->setErrorState(error_state);             // 设置错误状态

        std::memcpy(&robot_motion_state, data + 164, sizeof(robot_motion_state));
        udp_ctrl->setRobotMotionState(robot_motion_state); // 设置运动状态

        std::memcpy(&battery_level, data + 168, sizeof(battery_level));
        udp_ctrl->setBatteryLevel(battery_level);         // 设置电池电量

        std::memcpy(&task_state, data + 176, sizeof(task_state));
        udp_ctrl->setTaskState(task_state);               // 设置任务状态

        std::memcpy(&is_robot_need_move, data + 180, sizeof(is_robot_need_move));
        udp_ctrl->setIsRobotNeedMove(is_robot_need_move); // 设置移动需求状态

        std::memcpy(&zero_position_flag, data + 184, sizeof(zero_position_flag));
        udp_ctrl->setZeroPositionFlag(zero_position_flag); // 设置零位标志

        std::memcpy(ultrasound.data(), data + 192, sizeof(double) * 2);  // 最后是偏移8个字节
        udp_ctrl->setUltrasound(ultrasound);             // 设置超声波读数
    }

};

// 用于处理机器人上报数据的类
class RobotStateReceived {
public:
    int code;
    int size;
    int cons_code;
    RobotState robot_state;

    RobotStateReceived(const char* data) : robot_state(data + 12) { // 这一行就包括了robot_state的初始化
        // 解析 RobotStateReceived 的数据
        std::memcpy(&code, data, sizeof(code));
        data += sizeof(code);
        std::memcpy(&size, data, sizeof(size));
        data += sizeof(size);
        std::memcpy(&cons_code, data, sizeof(cons_code));
    }
};

// 将结构体转换为 JSON
Json::Value to_json(const RobotState& state) {
    Json::Value j;

    j["robot_basic_state"] = state.robot_basic_state;
    j["robot_gait_state"] = state.robot_gait_state;

    j["rpy"] = Json::arrayValue;
    for (double val : state.rpy) {
        j["rpy"].append(val);
    }

    j["rpy_vel"] = Json::arrayValue;
    for (double val : state.rpy_vel) {
        j["rpy_vel"].append(val);
    }

    j["xyz_acc"] = Json::arrayValue;
    for (double val : state.xyz_acc) {
        j["xyz_acc"].append(val);
    }

    j["pos_world"] = Json::arrayValue;
    for (double val : state.pos_world) {
        j["pos_world"].append(val);
    }

    j["vel_world"] = Json::arrayValue;
    for (double val : state.vel_world) {
        j["vel_world"].append(val);
    }

    j["vel_body"] = Json::arrayValue;
    for (double val : state.vel_body) {
        j["vel_body"].append(val);
    }

    j["touch_down_and_stair_trot"] = state.touch_down_and_stair_trot;
    j["is_charging"] = state.is_charging;
    j["error_state"] = state.error_state;
    j["robot_motion_state"] = state.robot_motion_state;
    j["battery_level"] = state.battery_level;
    j["task_state"] = state.task_state;
    j["is_robot_need_move"] = state.is_robot_need_move;
    j["zero_position_flag"] = state.zero_position_flag;

    j["ultrasound"] = Json::arrayValue;
    for (double val : state.ultrasound) {
        j["ultrasound"].append(val);
    }

    return j;
}

// 持续发送指令
void sendrobdogcall(shared_ptr<DeepUdpCtrl> udp_ctrl) {
    // udp_ctrl->callRobotStatus(&packet, packet_size); // 发起请求
    while (!stopRequested) {
        // std::cout << stopRequested << std::endl;
        if (!pauseRequested) {   // 只会暂停发送
            // 执行实际工作，例如发送数据
            // udp_ctrl->sendData();
            udp_ctrl->send_command();
            // std::cout << "Sending data..." << std::endl;
        } else {
            std::cout << "Paused. Waiting for RESUME signal..." << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        // 适当的延时 ！！！！此处可以修改请求当前状态的频率，为了避免抵消掉其他请求，建议时间长一点
        std::this_thread::sleep_for(std::chrono::milliseconds(1000)); 
    }

    // while (true) {
    //     udp_ctrl->send_command();
    //     this_thread::sleep_for(chrono::milliseconds(20));
    // }
}

// 根据状态播放对应的表情视频
void udp_response_expression_function(Json::Value status_json, shared_ptr<DeepUdpCtrl> udp_ctrl)
{
    static int last_robot_basic_state = 0;
    static int last_robot_gait_state = 0;
    static int last_robot_motion_state = 0;
    int robot_basic_state = status_json["robot_basic_state"].asInt();
    int robot_gait_state = status_json["robot_gait_state"].asInt();
    int robot_motion_state = status_json["robot_motion_state"].asInt();
    // ROS_INFO("robot_basic_state :%d  robot_gait_state:%d robot_motion_state:%d",robot_basic_state,robot_gait_state,robot_motion_state);

    // 状态不改变的情况下不重新播放
    if(robot_basic_state == last_robot_basic_state && last_robot_gait_state == robot_gait_state && last_robot_motion_state == robot_motion_state){
        return;
    }

    // 查询当前状态
    auto key = std::make_tuple(robot_basic_state, robot_gait_state, robot_motion_state);
    auto it = stateQueryTable.find(key);
    if (it != stateQueryTable.end()) {
        std::cout << "Current Robot State: " << it->second << std::endl; // 返回对应的状态描述
        // system(it->second);   // 后续可以把stateQueryTable的值改为视频播放的地址
        udp_ctrl->publishRobdagStateToQt(it->second);
        
    } else {
        std::cout << "Current Robot State: " << "Unknown State" << std::endl; // 未知状态
    }

    

    // if(robot_basic_state == 19 && robot_gait_state == 0 && robot_motion_state == 0){
    //     system("/home/<USER>/updateexpression2.sh  /home/<USER>/resource/vedio/hello/heart.mp4 8  /home/<USER>/resource/vedio/default.mp4");
    //     ROS_INFO("robot_basic_state == 19 && robot_gait_state == 0 && robot_motion_state == 0");
    // }
    // if(robot_basic_state == 21 && robot_gait_state == 0 && robot_motion_state == 0){
	//     system("/home/<USER>/updateexpression2.sh  /home/<USER>/resource/vedio/hello/stareyes.mp4 8  /home/<USER>/resource/vedio/default.mp4");
	// }	
    // else if(robot_basic_state == 6 && robot_gait_state == 0 && robot_motion_state == 33554432){
    //     system("/home/<USER>/updateexpression2.sh  /home/<USER>/resource/left_right_look.mp4 10  /home/<USER>/resource/vedio/default.mp4");
    //     ROS_INFO("robot_basic_state == 6 && robot_gait_state == 0 && robot_motion_state == 33554432");
    // }
    last_robot_basic_state = robot_basic_state;
    last_robot_gait_state = robot_gait_state;
    last_robot_motion_state = robot_motion_state;
    return;

}

// 持续接收指令
void receiveAndProcessData(shared_ptr<DeepUdpCtrl> udp_ctrl) {
    // RobotStateUpload packet; // 不能以结构体直接接收，需要后续处理
    char packet[4096];
    size_t packet_size = sizeof(packet);
    // while (true) {
    //     // 调用 getPacket 方法接收数据包
    //     udp_ctrl->getPacket(&packet, packet_size);
    //     // this_thread::sleep_for(chrono::milliseconds(20));
        
    // }
    while (!stopRequested) {
        // std::cout << stopRequested << std::endl;
        if (!pauseRequested) {   
            int result = udp_ctrl->getPacket(packet, packet_size);
            // std::cout << "Receiving data..." << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(20)); // 适当的延时
            if(!result){
                // 处理接收到的数据包
                RobotStateReceived robot_state_received(packet);

                if (robot_state_received.code == 2305) {
                    // std::cout << "Received data with code " << robot_state_received.code << std::endl;
                    Json::Value status_json = to_json(robot_state_received.robot_state);
                    // cout << status_json << endl;
                    // cout << status_json["robot_basic_state"].asString() << endl;
                    udp_response_expression_function(status_json, udp_ctrl);
                    
                    // 在这里将机器人状态信息发布出去【应该在界面按键触发之后再去发布还是定时发布？】
                    // udp_ctrl->robotStatusPub_.publish(msg);
                }
            }
            // else cout << "no bytes" << endl;
        }
        else {
            std::cout << "Paused. Waiting for RESUME signal..." << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }
}

//local_port = 43894"
//remote_port = 43893
//remote_ip = "*************"

int main(int argc, char **argv)
{
    //  ------------------ 注册信号处理函数（ctrl+c） ------------------ 
    struct sigaction sa_ctrl_c;
    sa_ctrl_c.sa_handler = &signal_handler;
    sigemptyset(&sa_ctrl_c.sa_mask);
    sa_ctrl_c.sa_flags = 0;
    if (sigaction(SIGINT, &sa_ctrl_c, NULL) == -1) {
        std::cerr << "Failed to register SIGINT handler" << std::endl;
        return 1;
    }
    
    // ------------------ 注册信号处理函数（ctrl+z） ------------------ 
    struct sigaction sa_tstp;
    sa_tstp.sa_handler = &signal_handler;
    sigemptyset(&sa_tstp.sa_mask);
    sa_tstp.sa_flags = 0;
    if (sigaction(SIGTSTP, &sa_tstp, NULL) == -1) {
        std::cerr << "Failed to register SIGTSTP handler" << std::endl;
        return 1;
    }
    ros::init(argc, argv, "deep_udp_ctrl_node"); 
    setvbuf(stdout, NULL, _IOLBF, 4096);
    ros::NodeHandle nh;

    // auto udp_ctrl = make_shared<DeepUdpCtrl>(nh);    // 创建UDP控制类的实例
    udp_ctrl = make_shared<DeepUdpCtrl>(nh);    // 创建UDP控制类的实例
    std::string log4cxx_config;
    std::string package_path = ros::package::getPath("robdog_control");
    ROS_INFO("package path is %s",package_path.c_str());
    log4cxx_config=package_path+"/../launch_package/launch/deep_udp_log.yaml";
    ROS_INFO("log4cxx_config path is %s",log4cxx_config.c_str());
    log4cxx::PropertyConfigurator::configure(log4cxx_config);

    ROS_INFO_STREAM("deep_udp_ctrl init INFO" << std::flush); 
    ROS_DEBUG_STREAM("deep_udp_ctrl init DEBUG" << std::flush);
    ROS_WARN_STREAM("deep_udp_ctrl init WARN" << std::flush);
    ROS_ERROR_STREAM("deep_udp_ctrl init ERROR" << std::flush);
    ROS_FATAL_STREAM("deep_udp_ctrl init FATAL" << std::flush);

    udp_ctrl->initSocket("*************", 6688, 43893);

    ros::Duration(0.2).sleep();// 等待一段时间确保UDP通信初始化完成
    // udp_ctrl->StandUp();

    //  ------------------ 创建并启动线程（接收机器狗上报的数据） ------------------ 
    
    // std::thread worker_thread(receiveAndProcessData, udp_ctrl);
    
    // thread recv_thread(receiveAndProcessData, ref(udp_ctrl));
    // int robdog_status = udp_ctrl->getRobotBasicState();
    // if(robdog_status != STATE_STANDING) udp_ctrl->StandUp();
    thread send_thread(sendrobdogcall, ref(udp_ctrl));
    thread recv_thread(receiveAndProcessData, ref(udp_ctrl));
    udp_ctrl->StandUp();
    ros::spin();

    // 请求停止
    stopRequested = true;

    recv_thread.join();
    send_thread.join();
    // if (recv_thread.joinable()) {
    //     recv_thread.join();
    // }
    // if (send_thread.joinable()) {
    //     send_thread.join();
    // }
    // 等待线程完成
    // if (worker_thread.joinable()) {
    //     worker_thread.join();
    // }

    return 0;
}
