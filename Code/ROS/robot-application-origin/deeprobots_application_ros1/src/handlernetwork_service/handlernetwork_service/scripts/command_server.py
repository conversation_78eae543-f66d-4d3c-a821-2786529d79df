#!/usr/bin/env python3
import rospy
import json
import os
import time
import subprocess

from handlernetwork_service.srv import NetCtrl, NetCtrlResponse



# 定义一个函数来读取配置文件并解析JSON字符串
def read_and_parse_json_config(file_path):
    if not os.path.exists(file_path):
        print(f"Configuration file not found: {file_path}")
        return None
    
    # 读取整个文件内容作为字符串
    with open(file_path, 'r') as file:
        json_str = file.read()
    
    # 解析JSON字符串
    try:
        config_data = json.loads(json_str)
    except json.JSONDecodeError as e:
        print(f"Failed to decode JSON: {e}")
        return None
    
    # 返回解析后的数据
    return config_data


# 更新配置文件
def update_network_conf(file_path, new_config_data):
    with open(file_path, 'w') as file:
        json.dump(new_config_data, file, indent=5)
        
        # 读取并打印文件内容
    with open(file_path, 'r') as file:
        content = file.read()
        print(f"Updated configuration file content:\n{content}")
        

def get_wifi_info(config_data):

    wifi_info = {
    'wifiState': config_data.get('wifiState', 'unknown'),
    'mobileDataState': config_data.get('mobileDataState', 'unknown'),
    'wifiName': config_data.get('wifiName', ''),
    'isWifiConnect': config_data.get('isWifiConnect', 'false'),
    'isInternetConnect': config_data.get('isInternetConnect', 'false')
    }
    
    return wifi_info


# 处理 setNetworkStatus 消息
def handle_set_network_status(network_status, config_data,file_path):
    # 解析 network_status 消息中的 wifiState 和 mobileDataState
    wifi_state = network_status.get("wifiState")
    mobile_data_state = network_status.get("mobileDataState")
    # 检查是否得到了有效的状态值
    if wifi_state is None or mobile_data_state is None:
        return {"status_code": 400, "message": "Both wifiState and mobileDataState are required."}

    # if (wifi_state, mobile_data_state) == ("on", "on"):
    #     return NetCtrlResponse(400, "Cannot be on at the same time.")    
    if (wifi_state, mobile_data_state) == ("off", "off"):
        return NetCtrlResponse(400, "Cannot be on at the same time.")    

    # 获取配置文件中的 wifiState 和 mobileDataState
    current_wifi_state = config_data.get("wifiState")
    current_mobile_data_state = config_data.get("mobileDataState")
                
    # 比较并决定是否需要更新
    if (wifi_state != current_wifi_state) or (mobile_data_state != current_mobile_data_state):
        # 更新配置数据
        config_data["wifiState"] = wifi_state
        config_data["mobileDataState"] = mobile_data_state
        if wifi_state == 'off':
            config_data["wifiName"] = ''
            config_data["isWifiConnect"] = 'false'
        else:
            time.sleep(2)
            print("等待2s,重新读取network.conf配置文件")
            config_data = read_and_parse_json_config(file_path)
            config_data["wifiState"] = wifi_state
            config_data["mobileDataState"] = mobile_data_state
            config_data["isWifiConnect"] = 'true'
        
        # 写入更新后的配置到文件
        update_network_conf(file_path, config_data)
        print("Network configuration has been updated.")
    else:
        print("No changes detected, network configuration remains the same.")
    
    return NetCtrlResponse(200, "Network configuration updated successfully.")

def run_sudo_command(password, command):
    # 使用 -S 参数让 sudo 从标准输入读取密码
    sudo_command = ['sudo', '-S'] + command.split()
    process = subprocess.Popen(sudo_command, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    stdout, stderr = process.communicate(input=password + '\n')
    if process.returncode != 0:
        print(f"chmod 777 network.conf An error occurred: {stderr}")
    else:
        print(f"chmod 777 network.conf  executed successfully: {stdout}")



def handle_network_status(req):

    try:
            request_data = json.loads(req.data)
    except json.JSONDecodeError:
            return NetCtrlResponse(400, "Invalid JSON in request.")
    # 获取当前工作目录
    current_directory = os.getcwd()

    # 打印当前工作目录
    print("当前工作目录:", current_directory)
    # 配置文件路径
    config_file_path = '/mine/robot-application/deeprobots_application_ros1/service/board_resources/network.conf'
    
    sudo_password = "'"
    command_to_run = f'chmod 777 {config_file_path}'
    run_sudo_command(sudo_password, command_to_run) 
    
    # 读取并解析配置文件
    config_data = read_and_parse_json_config(config_file_path)

        # 检查请求是否是 "getNetworkStatus"
    if request_data.get("command") == "getNetworkStatus":        
        
            if config_data is not None:
                # 创建wifi_info结构体
                wifi_info = get_wifi_info(config_data)                
                # 打印wifi_info结构体
                print(wifi_info)
                json_response = json.dumps(wifi_info, indent=9)
                return NetCtrlResponse(200, json_response)                
            else:
                print("Failed to read or parse the configuration file.")
                return NetCtrlResponse(400,"Failed to read or parse the configuration file.")                

        # 如果请求是 "setNetworkStatus"
    elif request_data.get("command") == "setNetworkStatus":
            network_status = request_data.get("networkStatus")
             # 处理 setNetworkStatus 消息
            response=handle_set_network_status(network_status, config_data,config_file_path)
            
            if isinstance(response, NetCtrlResponse):
                return response
            else:
                return NetCtrlResponse(400, "Internal server error.")
          
    else:
            return {"status_code": 400, "message": "Unknown command."}

if __name__ == "__main__":
    rospy.init_node('command_server')
    net_service = rospy.Service('/homi_speech/network_service', NetCtrl, handle_network_status)
    rospy.loginfo("Ready to receive commands.")
    rospy.spin()




