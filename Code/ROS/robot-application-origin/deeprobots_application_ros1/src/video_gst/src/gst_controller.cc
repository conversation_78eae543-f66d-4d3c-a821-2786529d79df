#include "gst_controller.h"

VideoPipeline::VideoPipeline() : pipeline(nullptr), tee(nullptr) {}

VideoPipeline::~VideoPipeline()
{
    if(pipeline) {
        gst_element_set_state(pipeline, GST_STATE_NULL);
        gst_object_unref(pipeline);
    }
}

bool VideoPipeline::init()
{
    // 初始化GStreamer
    gst_init(nullptr, nullptr);

    // 设置日志等级
    gst_debug_set_threshold_for_name("*", GST_LEVEL_INFO);
    gst_debug_set_threshold_for_name("pipeline", GST_LEVEL_DEBUG);

    // 创建管道
    pipeline = gst_pipeline_new("video-pipeline");
    
    // 创建元素
    GstElement *source = gst_element_factory_make("v4l2src", "source");
    g_object_set(G_OBJECT(source), "device", "/dev/video0", nullptr);
    GstElement *jpeg_caps = gst_element_factory_make("capsfilter", "jpeg_caps");
    g_object_set(G_OBJECT(jpeg_caps), "caps", gst_caps_from_string("image/jpeg, width=1920, height=1080"), nullptr);
    GstElement *jpeg_dec = gst_element_factory_make("mppjpegdec", "jpeg_dec");
    GstElement *video_convert1 = gst_element_factory_make("videoconvert", "video_convert1");
    GstElement *raw_caps = gst_element_factory_make("capsfilter", "raw_caps");
    g_object_set(G_OBJECT(raw_caps), "caps", gst_caps_from_string("video/x-raw, format=NV12"), nullptr);
    tee = gst_element_factory_make("tee", "t");

    // 添加元素到管道
    gst_bin_add_many(GST_BIN(pipeline), source, jpeg_caps, jpeg_dec, video_convert1, raw_caps, tee, nullptr);

    // 链接元素
    if (!gst_element_link_many(source, jpeg_caps, jpeg_dec, video_convert1, raw_caps, tee, nullptr)) {
        ROS_ERROR("Failed to link elements");
        return false;
    }

    // 创建额外的队列
    GstElement *queue1 = gst_element_factory_make("queue", "queue1");
    GstElement *mpph264enc1 = gst_element_factory_make("mpph264enc", "encoder1");
    g_object_set(G_OBJECT(mpph264enc1), "header-mode", 1, "profile", 66, "level", 31, "rc-mode", "cbr", "bps-min", 3500000, "bps", 4000000, "bps-max", 4500000, "gop", 50, nullptr);
    GstElement *shmsink1 = gst_element_factory_make("shmsink", "shmsink1");
    g_object_set(G_OBJECT(shmsink1), "socket-path", SOCKET_SD, "sync", true, "wait-for-connection", false, "shm-size", 10000000, nullptr);
    
    GstElement *queue2 = gst_element_factory_make("queue", "queue2");
    GstElement *video_scale = gst_element_factory_make("videoscale", "video_scale");
    GstElement *scale_caps = gst_element_factory_make("capsfilter", "scale_caps");
    g_object_set(G_OBJECT(scale_caps), "caps", gst_caps_from_string("video/x-raw,width=1280,height=720"), NULL);
    GstElement *video_convert2 = gst_element_factory_make("videoconvert", "video_convert2");
    GstElement *mpph264enc2 = gst_element_factory_make("mpph264enc", "encoder2");
    g_object_set(G_OBJECT(mpph264enc2), "header-mode", 1, "profile", 66, "level", 31, "rc-mode", "cbr", "bps-min", 1500000, "bps", 2000000, "bps-max", 2500000, "gop", 50, nullptr);
    GstElement *shmsink2 = gst_element_factory_make("shmsink", "shmsink2");
    g_object_set(G_OBJECT(shmsink2), "socket-path", SOCKET_HD, "sync", true, "wait-for-connection", false, "shm-size", 10000000, nullptr);


    gst_bin_add_many(GST_BIN(pipeline), queue1, mpph264enc1, shmsink1, queue2, video_scale, scale_caps, video_convert2, mpph264enc2, shmsink2, nullptr);

    // 请求新的源垫
    GstPad *tee_src_pad1, *tee_src_pad2;
    tee_src_pad1 = gst_element_get_request_pad(tee, "src_%u");
    tee_src_pad2 = gst_element_get_request_pad(tee, "src_%u");

    // 获取队列的接收垫
    GstPad *queue_sink_pad1 = gst_element_get_static_pad(queue1, "sink");
    GstPad *queue_sink_pad2 = gst_element_get_static_pad(queue2, "sink");

    // 链接 tee 到队列
    if (!gst_pad_link(tee_src_pad1, queue_sink_pad1) == GST_PAD_LINK_OK ||
        !gst_pad_link(tee_src_pad2, queue_sink_pad2) == GST_PAD_LINK_OK) {
        ROS_ERROR("Failed to link tee to queue");
        return false;
    }

    // 链接队列到其他元素
    if (!gst_element_link_many(queue1, mpph264enc1, shmsink1, nullptr) ||
        !gst_element_link_many(queue2, video_scale, scale_caps, video_convert2, mpph264enc2, shmsink2, nullptr)) {
        ROS_ERROR("Failed to link queue to other elements ");
        return false;
    }

    // 设置管道状态为 PLAYING
    GstStateChangeReturn ret = gst_element_set_state(pipeline, GST_STATE_PLAYING);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        ROS_ERROR("Unable to set the pipeline to the playing state.");
        gst_object_unref(pipeline);
        return false;
    }

    return true;
}

int main(int argc, char *argv[]) {
    ros::init(argc, argv, "video_gst_node");
    ros::NodeHandle nh;
    
    VideoPipeline vp;
    if(!vp.init()) return 1;

    ros::spin();
    return 0;
}
