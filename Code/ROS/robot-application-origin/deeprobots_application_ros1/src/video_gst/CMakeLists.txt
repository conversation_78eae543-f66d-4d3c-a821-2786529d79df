cmake_minimum_required(VERSION 3.0.2)
project(video_gst)

## Compile as C++11, supported in ROS Kinetic and newer
# add_compile_options(-std=c++11)

## Find catkin macros and libraries
## if COMPONENTS list like find_package(catkin REQUIRED COMPONENTS xyz)
## is used, also find other catkin packages
find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
)

find_package(PkgConfig)
pkg_search_module(GSTREAMER REQUIRED gstreamer-1.0)

catkin_package(
#  INCLUDE_DIRS include
#  LIBRARIES video_gst
 CATKIN_DEPENDS roscpp rospy std_msgs
#  DEPENDS system_lib
)

include_directories(
# include
  ${catkin_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/include
  ${GSTREAMER_INCLUDE_DIRS}
)

add_executable(${PROJECT_NAME}_node
       src/gst_controller.cc
)

add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})

target_link_libraries(${PROJECT_NAME}_node
      ${catkin_LIBRARIES}
      ${GSTREAMER_LIBRARIES}
)

