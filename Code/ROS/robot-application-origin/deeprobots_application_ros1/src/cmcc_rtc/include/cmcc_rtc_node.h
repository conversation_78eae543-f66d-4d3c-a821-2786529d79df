#ifndef __CMCC_RTC_NODE_H__
#define __CMCC_RTC_NODE_H__

#include <ros/ros.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <iostream>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <time.h>
#include <unordered_map>
#include <sys/prctl.h>
#include "cmcc_rtc_api.h"
#include "rtc/SIGCEvent.h"
#include "rtc/PCMStream.h"
#include "rtc/AssistantAbort.h"
#include "xunfei_audio/SetNrMode.h"
#include "xunfei_audio/SetWakeEvent.h"
#include <jsoncpp/json/json.h>
#include "shmaudio.h"

extern "C" {
#include "vs_define.h"
}

#define SDK_INCLUDE_THIRD_PARTY_DEVICE
#define ENABLE_AUDIO_CODEC
    uint64_t get_cur_timestamp_us();

    void *send_audio_thread(void *arg);

    void create_send_audio_thread();

    void *send_video_thread(void *arg);

    void create_send_video_thread();

    void __on_recv_audio_packet(char *packet ,int size);

    void __on_login_success(const char *user);

    void __on_login_failed(const char *user, int err_code, const char *reason);

    void __on_recv_ring(int session, const char *from, const char *displayname, const char *to, int early_media);
    /*
        * @说明：
        * [1]early_media=1表示平台会播放回铃音，设备端不用自己播放回铃音；
        * [2]early_media=0表示平台不播放回铃音，设备需要自己播放一个本地回铃音；
        * [3]设备主动呼出后，此回调可能出现多次，如果early_media为0时，
        *    设备自己播放本地回铃音；early_media为1时，设备停止播放本地回铃音。
        * [4]播放本地本地回铃音时，如果收到recv_answer应答回调事件，需要停止播放本地回铃音，播放收到的对端音频流。
        * 
    */

    void __on_recv_answer(int session, const char *from, const char *displayname, const char *to, cmcc_call_type_t call_type);

    // void __on_recv_hangup(int session, int err_code, const char *reason, cmcc_call_type_t call_type);

    void __on_recv_notify(cmcc_notify_t notify_type, const char *content);

    void __on_recv_keyframe_request();

    void __on_recv_dtmf(int value);

    void __on_recv_audio_pcm_data(char *pcm_data, int size);

    void __on_send_audio_pcm_data(char *pcm_data, int size);

    extern std::unordered_map<std::string, cmcc_rtc_opt_> stringsToEnum;

    class CmccRtcNode{
    public:
        CmccRtcNode(cmcc_rtc_device_info_t& device_info, cmcc_rtc_log_config_t& log_config,
            cmcc_rtc_login_params_t& login_params, std::unordered_map<cmcc_rtc_opt_,
            const char*>& rtc_opts, ros::NodeHandle& nh);
        ~CmccRtcNode();
            
    private:
        void eventCallback(const rtc::SIGCEvent::ConstPtr& msg);
        void pcmCallback(const rtc::PCMStream::ConstPtr& msg);

        static void __on_recv_hangup(int session, int err_code, const char *reason, cmcc_call_type_t call_type);

        static void __on_recv_call(int session, const char *from, const char *displayname, const char *to, cmcc_call_type_t call_type, const char *json_call_control);
        /*
            * 说明：
            * [1] json_call_control中有"answer_auto"字段，表示需要自动接听的来电，走自动接听逻辑；
            * [2] json_call_control中没有"answer_auto"字段，表示非自动接听的来电，走手动接听逻辑；
            * [3] json_call_control中有"app_broadcast"字段，表示云广播来电
            * [4] json_call_control中有"app_talk"字段，表示安防对讲来电
            * 
            * 备注：
            * [1] 对于需要自动接听的来电，先调用cmcc_rtc_setopt接口，将自动接听的flag设置到sdk层，
            *     然后再调用cmcc_rtc_pickup接口，接听来电；
            * [2] 对于未带answer_auto的来电，需要手动接听；
            * 
        */

        int _writeShmId;
        int _writeIsAvaliableShmId;
        audio_buffer* _writeShared;
        is_avalibale* _writeIsAvaliableShared;

    protected:
        cmcc_rtc_device_info_t device_info_;        //设备信息
        cmcc_rtc_log_config_t log_config_;          //日志信息
        cmcc_rtc_login_params_t login_params_;      //线上环境
        cmcc_rtc_event_handler_t event_handler;     //事件处理
        std::unordered_map<cmcc_rtc_opt_, const char*> rtc_opts_;   //扩展属性
        ros::NodeHandle nh_;            //节点
        ros::Subscriber eventSuber_;    //声明事件订阅者
        ros::Subscriber audioSuber_;    //声明音频订阅者
#ifdef XUNFEI
        static std::shared_ptr<ros::ServiceClient> modeClient_;     //声明降噪模式客户端
#endif
        static std::shared_ptr<ros::ServiceClient> wakeClient_;     //声明唤醒事件客户端
        static std::shared_ptr<ros::ServiceClient> abortClient_;    //声明语音助手暂停客户端
        Json::Reader str2json;          //String转Json
    };

    // class AudioBuffer{
    // public:
    //     AudioBuffer(int size);
    //     ~AudioBuffer();

    //     void write(char* pcm, int size);
    //     bool read(char* pcm, int size);
    // private:
    //     char* m_Audio;
    //     int m_totsize;
    //     int m_Writeindex;
    //     int m_Readindex;

    // };

#endif
