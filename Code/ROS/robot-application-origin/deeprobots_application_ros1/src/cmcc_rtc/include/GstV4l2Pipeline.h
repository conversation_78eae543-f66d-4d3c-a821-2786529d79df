//
// Created by l<PERSON><PERSON><PERSON> on 24-6-4.
//

#ifndef CMCC_RTC_DEMO_GSTV4L2PIPELINE_H
#define CMCC_RTC_DEMO_GSTV4L2PIPELINE_H

// #include <gst/gst.h>
// #include <gst/app/app.h>
#include <gst/gst.h>
#include <gst/app/gstappsink.h>
#include <glib.h>
#include <functional>
#include <cstdint>

using namespace std;

typedef function<void(uint8_t *, unsigned long, int, int)> BufferCallback;

class GstV4l2Pipeline {
public:
    GstV4l2Pipeline(int argc, char *argv[]);

    ~GstV4l2Pipeline();

    void Create();

    void Create_1080p();

    void Create_720p();

    void Create265();

    bool Start();

    bool Pause();

    bool Resume();

    bool Stop();

    void Destroy();

    void SetVoipBufferCallback(BufferCallback callback);

    void SetCameraBufferCallback(BufferCallback callback);

    std::string GetVideoPath();

public:
    BufferCallback mVoipBufferCallback; 

    BufferCallback mCameraBufferCallback;

private:
    std::string video_path_;
    GstElement *mPipeline;
    // GstElement *source;
    // GstElement *decoder;
    // GstElement *convert;
    GstElement *mSink;
    // GstElement *mEnc;
    GstElement *mCameraSink;
    GstStateChangeReturn mRet;

    std::string resolution_;
};


#endif //CMCC_RTC_DEMO_GSTV4L2PIPELINE_H
