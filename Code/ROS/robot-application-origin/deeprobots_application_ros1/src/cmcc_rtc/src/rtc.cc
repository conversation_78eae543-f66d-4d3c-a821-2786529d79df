#include <ros/ros.h>
#include "cmcc_rtc_node.h"
#include <yaml-cpp/yaml.h>

// /**
//     [1]device_model 命名规则：
//     终端设备类型|厂家名称|系统类型
//     说明：
//     终端设备类型：SpeakerBox（音箱），Camera（摄像头），CameraCall（可主动发起呼叫的摄像头），
//                 ML（门铃拼英缩写），DoorLock（门锁），TV（电视），APP（手机应用）、ACS（门禁），
//                 Watch（手表），Robot（机器人），Lamp（灯），IPTelephone（IP电话）
//     厂家名称：厂家全拼，如Baidu，Xiaomi，Tianmao，Xunfei，Huawei等
//     系统类型：Linux，LiteOS，RTOS，Android等

//     其中第三方厂家填写的字符串中不能有'|'

//     举例：
//     SpeakerBox|Baidu|RTOS
//     Camera|Haikang|Linux

//     [2]芯片厂家接入时，chip_model、chip_uuid,chip_brand必须填写，设备厂家接入时，直接传入空字符串即可
//     [3]所填参数必须是对应芯片或设备的真实参数
// **/
// cmcc_rtc_device_info_t device_info = {
//         .os_version             = (char*)"Ubuntu-20.04",
//         .image_version          = (char*)"2.3.54",
//         .hardware_version       = (char*)"2.1",
//         .device_model           = (char*)"Robot|Xunfei|Linux",
//         // .sn                     = (char*)"6361752000000013",
//         // .cmei                   = (char*)"212345670000012",
//         // .sn                     = (char*)"6361752000000011",
//         // .cmei                   = (char*)"212345670000012",
//         .sn                     = (char*)"6361752000000014",
//         .cmei                   = (char*)"212345670000012",
//         .mac                    = (char*)"b6:5e:94:d4:2b:6a",
//         .chip_model             = (char*)"",
//         .chip_uuid              = (char*)"",
//         .chip_brand             = (char*)"",
// };

// cmcc_rtc_log_config_t log_config = {
//         .log_size           = 1024 * 1024,
//         .console_log_enable = 1,
//         /**
//          * 【1】日志文件必须是txt格式
//          * 【2】同时集成安防OVD和固话SDK的设备，需将固话日志、安防SDK日志以及设备相关日志放在同一个目录下，
//          *     并且该目录下只能存放日志文件，安防日志上传时需将该目录下的所有日志文件打包成一个压缩文件上传。
//          *
//          */
//         .log_path           = (char*)"log/call.txt",
// };

// cmcc_rtc_login_params_t login_params = {
//         //线上环境
//         // .app_key         = "t0xs87hl6o234nj5",           // 固话平台产品创建后自动生成的
//         // .app_secret  = "sgoeq50tmlfftzxc",               // 固话平台产品创建后自动生成的
//         // .device_id       = "CMCC-500801-F8A763E68262",   // 格式为cmcc-AndlinkID-序列号

//         // .app_key         = "za2hmz1w7679w5zy",           // 固话平台产品创建后自动生成的
//         // .app_secret  = "0f8k4rf9qp7bj6mg",               // 固话平台产品创建后自动生成的
//         // .device_id        = "xiaodu4795",                // 格式为cmcc-AndlinkID-序列号

//         //测试环境
//         // .app_key        = "za2hmz1w7679w5zy",               // 固话平台产品创建后自动生成的
//         // .app_secret     = "0f8k4rf9qp7bj6mg",               // 固话平台产品创建后自动生成的
//         // .device_id      = "cmcc-30560-xiaodu4795",          // 格式为cmcc-AndlinkID-序列号

//         .app_key        = (char*)"sqp9ci1qrptp1orl",               // 固话平台产品创建后自动生成的
//         .app_secret     = (char*)"2ezmzb9v27fkztj7",               // 固话平台产品创建后自动生成的
//         // .app_key        = (char*)"X25wg0GB",               // 固话平台产品创建后自动生成的
//         // .app_secret     = (char*)"X25wg0GB",               // 固话平台产品创建后自动生成的
//         .device_id      = (char*)"cmcc-2320647-6361752000000014",          // 格式为cmcc-AndlinkID-序列号
//         // .device_id      = (char*)"CMCC-2320647-1830004229212345670000020",          // 格式为cmcc-AndlinkID-序列号

//         //业务平台测试环境-cameracall通讯录
//         // .app_key        = "825neezyeig65t28_dev",
//         // .app_secret     = "nc7078dp5e9yq43y",
//         // .device_id      = "cmcc-30560-xiaodu8888",
// };

// std::unordered_map<cmcc_rtc_opt_, const char*> rtc_opts{
//     {CMCC_OPT_DISENABLE_CONTACTS, "1"},
//     {CMCC_OPT_ENABLE_H265, "0"},
//     {CMCC_OPT_VIDEO_RESOLUTION, "width=1280,height=720"}};


bool getParams(cmcc_rtc_device_info_t& device_info, cmcc_rtc_log_config_t& log_config, cmcc_rtc_login_params_t& login_params, std::unordered_map<cmcc_rtc_opt_, const char*>& rtc_opts, ros::NodeHandle& nh) {
    std::string config_file;
    if(!nh.getParam("config_file", config_file)) {
        ROS_ERROR("Failed to get parameter config_file");
        return false;
    }

    try {
        YAML::Node config = YAML::LoadFile(config_file);
        // 传递device_info参数
        device_info.os_version = strdup(config["device_info"]["os_version"].as<std::string>().c_str());
        device_info.image_version = strdup(config["device_info"]["image_version"].as<std::string>().c_str());
        device_info.hardware_version = strdup(config["device_info"]["hardware_version"].as<std::string>().c_str());
        device_info.device_model = strdup(config["device_info"]["device_model"].as<std::string>().c_str());
        device_info.sn = strdup(config["device_info"]["sn"].as<std::string>().c_str());
        device_info.cmei = strdup(config["device_info"]["cmei"].as<std::string>().c_str());
        device_info.mac = strdup(config["device_info"]["mac"].as<std::string>().c_str());
        device_info.chip_model = strdup(config["device_info"]["chip_model"].as<std::string>().c_str());
        device_info.chip_uuid = strdup(config["device_info"]["chip_uuid"].as<std::string>().c_str());
        device_info.chip_brand = strdup(config["device_info"]["chip_brand"].as<std::string>().c_str());

        // 传递log_config参数
        log_config.log_size = config["log_config"]["log_size"].as<int>();
        log_config.console_log_enable = config["log_config"]["console_log_enable"].as<int>();
        log_config.log_path = strdup(config["log_config"]["log_path"].as<std::string>().c_str());

        // login_param
        login_params.app_key = strdup(config["login_params"]["app_key"].as<std::string>().c_str());
        login_params.app_secret = strdup(config["login_params"]["app_secret"].as<std::string>().c_str());
        login_params.device_id = strdup(config["login_params"]["device_id"].as<std::string>().c_str());

        // 传递rtc_opts参数
        for(YAML::const_iterator it=config["rtc_opts"].begin(); it != config["rtc_opts"].end(); it++) {
            rtc_opts[stringsToEnum[it->first.as<std::string>()]] = strdup(it->second.as<std::string>().c_str());
        }

        return true;
    } catch (YAML::Exception& e) {
        ROS_ERROR_STREAM("Error while loading YAML file: " << e.what());
        return false;
    }
}

int main(int argc, char *argv[])
{
    char currentPath[256];
    if(getcwd(currentPath, 256) != nullptr) {
        ROS_INFO("Current working directory: %s\n", currentPath);
    }
    ros::init(argc, argv, "rtc_node");
    ros::NodeHandle nh;
    cmcc_rtc_device_info_t device_info;
    cmcc_rtc_log_config_t log_config;
    cmcc_rtc_login_params_t login_params;
    std::unordered_map<cmcc_rtc_opt_, const char*> rtc_opts;
    if(getParams(device_info, log_config, login_params, rtc_opts, nh)) {
        auto cmcc_rtc = std::make_shared<CmccRtcNode>(device_info, log_config, login_params, rtc_opts, nh);
        ros::spin();
    }

    // cmcc_rtc_init(&device_info, &log_config, &event_handler);

    // for(std::unordered_map<cmcc_rtc_opt_, const char*>::iterator it=rtc_opts.begin(); it!=rtc_opts.end(); it++) {
    //     cmcc_rtc_setopt(it->first, it->second);
    // }

    // int ret = cmcc_rtc_login(&login_params);

    // while(cmcc_rtc_login(&login_params) != 0) {
    //     ROS_INFO("======================= failed to invoke cmcc_rtc_login interface =======================\n");
    //     //SDK内部连接业务平台线程启动失败（正常不会），等待一段时间重试
    //     usleep(1000 * 1000* 10);
    // }

    // while(true){}

    return 0;

    // string msg = "{\"deviceId\": \"11200\", \"domain\": \"DEVICE_ABILITY\", \"event\": \"phone_call\", \"eventId\": \"唯一id\", \"seq\": \"时间戳\", \"body\":{ \"phoneNumber\": \"18867100331\" }}"

}
