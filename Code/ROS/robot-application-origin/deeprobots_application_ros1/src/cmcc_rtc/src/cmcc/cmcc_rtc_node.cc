#include "cmcc_rtc_node.h"
#include <sys/ipc.h>
#include <sys/shm.h>
#include <errno.h>
#include "GstV4l2Pipeline.h"
#include "alsa_helper.h"
#include "file_wrapper.h"
#include <thread>  

#define VIDEO_DATA_SIZE 1024 * 50

static int g_call_session                   = 0;
static int g_calling                        = 0;
static int g_login_success                  = 0;
static int g_enable_video                   = 0;
static int g_call_type                      = CMCC_CALL_TYPE_IMS_1V1_VIDEO;
static int g_h265                           = 0;
// static int g_480p                           = 0;
// static int g_720p                           = 0;
// static int g_1080p                           = 0;
static bool g_video_resolution_changed      = 0;
std::string FileFolderName;

std::string ringtoneFileName = "";
GstV4l2Pipeline *gstV4L2Pipeline;

void runCommand(const std::string& command) {  
    system(command.c_str());  
} 

/**
 * 【说明】
 * SDK_INCLUDE_THIRD_PARTY_DEVICE宏表示SDK内部已经包含third_party_device.cc等源文件，
 * 厂家编译demo时，makefile文件中不用再加入third_party_device.cc等文件，
 * 默认提供给厂家的SDK中不包含third_party_device.cc等源文件!!!!
 * 
 */

#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
const char *inputAudioFilename                    = "cmcc_audio/input16000.pcm";
const char *outputAudioFilename                   = "cmcc_audio/output16000.pcm";
FILE*      g_audio_pcm_fp_input                   = NULL;
FILE*      g_audio_pcm_fp_output                  = NULL;
// std::shared_ptr<AudioBuffer>  input_pcm_buffer    = std::make_shared<AudioBuffer>(4096);
// std::shared_ptr<AudioBuffer>  output_pcm_buffer    = std::make_shared<AudioBuffer>(4096);
#endif

/**
 * 【说明】
 * ENABLE_AUDIO_CODEC宏表示SDK启用了音频编解码功能
 * 默认提供给厂家的SDK是启用音频编解码的SDK，厂家提供原始的PCM音频数据流!!!!
 * 提供编码音频数据（如PCMA数据）的厂家，收发音频数据，参考ENABLE_AUDIO_CODEC宏下的代码流程；
 * 提供原始音频数据（如PCM数据）的厂家：
 * （1）如果提供给厂家的demo中包含third_party_device.cc等文件，收发音频数据时，请参考third_party_device.cc文件中相关的代码逻辑。
 * （2）果提供给厂家的demo中不包含third_party_device.cc等文件，收发音频数据时，请参考SDK_INCLUDE_THIRD_PARTY_DEVICE宏下的代码逻辑。
 * 
*/

#ifndef ENABLE_AUDIO_CODEC
#define AUDIO_DATA_SIZE 160
static int              g_enable_audio                    = 0;
FILE*                   g_audio_fp                        = NULL;
static  unsigned char   g_audio_buffer[AUDIO_DATA_SIZE]   = {0};
static  pthread_mutex_t g_send_audio_mutex;

static uint64_t get_cur_timestamp_us()
{
    return 0;
}

static void *send_audio_thread(void *arg)
{
    printf("================ start send audio packets ================\n");

    prctl(PR_SET_NAME, "Audio_send_th");
    g_audio_fp = fopen("output16000.alaw", "wb");
    if (!g_audio_fp) {
        printf("failed to open output16000 file!!!\n");
        return;
    }

    FILE *fp = NULL;
    fp = fopen("input16000.alaw", "r");
    if (!fp) {
        printf("failed to open input16000 file!!!\n");
        return;
    } 

    uint64_t cur_timestamp      = 0;
    size_t   read_data_len      = 0;

    while (g_enable_audio) {
        pthread_mutex_lock(&g_send_audio_mutex);
        uint64_t pre_timestamp = get_cur_timestamp_us();
        {
            memset(g_audio_buffer, 0, AUDIO_DATA_SIZE);
            read_data_len = fread(g_audio_buffer, 1, AUDIO_DATA_SIZE, fp);
            if (read_data_len <= 0) {
                //printf("================= fseek =================\n");
                fseek(fp, 0, SEEK_SET);
                pthread_mutex_unlock(&g_send_audio_mutex);
                continue;
            }

            cmcc_rtc_send_audio(g_call_session, g_audio_buffer, read_data_len);
        }
        pthread_mutex_unlock(&g_send_audio_mutex);

        cur_timestamp = get_cur_timestamp_us();
        uint64_t delta_time_us = cur_timestamp - pre_timestamp; 
        if (delta_time_us <= 20 * 1000) {
            //printf("2==============sleep_ms:%ld=========\n", (20 * 1000 - delta_time_us));
            usleep(20 * 1000 - delta_time_us); //sleep 20ms
        } else {
            //printf("3==============sleep_ms:%ld=========\n", (20 * 1000 - delta_time_us));
            usleep(1 * 1000); //sleep 1ms
        }
    }

    if (fp) {
        fclose(fp);
        fp = NULL;
    }
    if (g_audio_fp) {
        fclose(g_audio_fp);
        g_audio_fp = NULL;
    }

    printf("================ stop send audio packets ================\n");
    retrun NULL;
}

static void create_send_audio_thread()
{
    printf("================ create_send_audio_thread ================\n");
    pthread_t tid;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setstacksize(&attr, 1024*128);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    tid = pthread_create(&tid, &attr, &send_audio_thread, NULL);
    pthread_attr_destroy(&attr);
}
#endif

static void *send_video_thread(void *arg)
{
    printf("================ start send video packets ================\n");
    char *data = NULL;
    data = (char *)malloc(VIDEO_DATA_SIZE);
    unsigned int video_file_count = 1;
    char file_name[128];
    int send_key_frame_count = 3;
    const char* TmpFolderName = FileFolderName.c_str();
    prctl(PR_SET_NAME, "Video_send_th");
    //刚开始多发送几次关键帧
    if (1 == g_h265) {
        unsigned int file_count = 1;
        int send_count = 5;
        while (send_key_frame_count--) {
            while (send_count--) {
                FILE *fp = NULL;
                int read_len = 0;
                memset(file_name, 0, sizeof(file_name));
                sprintf(file_name, "../../send/H265/%s/%d.h265",TmpFolderName,file_count++);
                fp = fopen(file_name, "r");
                if (NULL == fp) {
                    printf("send_video_thread, open h265 file failed, please make sure the file exists!!!\n\n");
                    return NULL;
                }
                memset(data, 0, VIDEO_DATA_SIZE);
                read_len = fread(data, 1, VIDEO_DATA_SIZE, fp);
                ROS_INFO("cmcc_rtc_send_video+++++++\n");
                cmcc_rtc_send_video(g_call_session, data, read_len);
                usleep(1000 * 300); //sleep 300ms

                if (fp) {
                    fclose(fp);
                }
            }
            send_count = 5;
            file_count = 1;
        }
    } else {
        while (send_key_frame_count--) {
            FILE *fp = NULL;
            int read_len = 0;
            memset(file_name, 0, sizeof(file_name));
            sprintf(file_name, "send/H264/%s/1.h264", TmpFolderName);

            fp = fopen(file_name, "r");
            if (NULL == fp) {
                printf("send_video_thread, open h264 file failed, please make sure the file exists!!!\n\n");
                return NULL;
            }
            memset(data, 0, VIDEO_DATA_SIZE);
            read_len = fread(data, 1, VIDEO_DATA_SIZE, fp);
            cmcc_rtc_send_video(g_call_session, data, read_len);
            usleep(1000 * 50); //sleep 50ms ,1s发送20帧

            if (fp) {
                fclose(fp);
            }
        }
    }
    
    while (g_enable_video) {
        FILE *fp = NULL;
        int read_data_len = 0;
        memset(file_name, 0, sizeof(file_name));
        if (g_video_resolution_changed) {
            video_file_count = 1;
            TmpFolderName = FileFolderName.c_str();      
            g_video_resolution_changed = 0;
        }
        if (1 == g_h265) {
            sprintf(file_name, "send/H265/%s/%d.h265",TmpFolderName, video_file_count++);
        } else {
            sprintf(file_name, "send/H264/%s/%d.h264",TmpFolderName, video_file_count++);
        }
        fp = fopen(file_name, "r");
        if (fp == NULL) {
            printf("打开文件失败!\n");
            video_file_count = 1;
            continue;
        } else {
            fflush(stdout);
        }

        memset(data, 0, VIDEO_DATA_SIZE);
        read_data_len = fread(data, 1, VIDEO_DATA_SIZE, fp);
        cmcc_rtc_send_video(g_call_session, data, read_data_len);
        usleep(1000 * 50); //sleep 50ms

        if (fp) {
            fclose(fp);
        }
    }

    if (data) {
        free(data);
        data = NULL;
    }
    printf("================ stop send video packets ================\n");
    return NULL;
}

static void VideoVoipBufferCallback(uint8_t *data, unsigned long length, int width, int height) {
//    printf("================ VideoVoipBufferCallback: %d x %d ================\n", width, height);
    cmcc_rtc_send_video(g_call_session, reinterpret_cast<char *>(data), length);

}

static void VideoCameraBufferCallback(uint8_t *data, unsigned long length, int width, int height) {
    // printf("================ VideoCameraBufferCallback: %d x %d ================\n", width, height);
}

// TODO(WangJie): Change time use this funcation to solve the balkscreen problem.

static void create_send_video_thread()
{
    printf("================ create_send_video_thread ================\n");
    if (gstV4L2Pipeline) {
        gstV4L2Pipeline->Start();
    }
    // pthread_t tid;
    // pthread_attr_t attr;
    // pthread_attr_init(&attr);
    // pthread_attr_setstacksize(&attr, 1024*256);
    // pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    // tid = pthread_create(&tid, &attr, &send_video_thread, NULL);
    // pthread_attr_destroy(&attr);
}

#ifndef ENABLE_AUDIO_CODEC
static void __on_recv_audio_packet(char *packet ,int size)
{
    if (g_audio_fp) {
        size_t num_bytes = fwrite(packet, 1, size, g_audio_fp);
        fflush(g_audio_fp);
    }
}
#endif

static void __on_login_success(const char *user)
{
    printf("================ __on_login_success ================\n");
    g_login_success = 1;
}

static void __on_login_failed(const char *user, int err_code, const char *reason)
{
    printf("================ __on_login_failed ================\n");
    g_login_success = 0;
}

// static void __on_recv_call(int session, const char *from, const char *displayname, const char *to, cmcc_call_type_t call_type, const char *json_call_control)
// {
// // #ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
// //     g_audio_pcm_fp_input = fopen(inputAudioFilename, "w");
// //     if(!g_audio_pcm_fp_input){ 
// //         return;
// //     }
// // #endif
//     int ret = 0;
//     g_calling = 1;
//     printf("================ __on_recv_call(%d) json_call_control(%s)================\n",call_type, json_call_control);
//     const char *nick_name = cmcc_rtc_get_nickname_by_number(from);
//     printf("from number:%s,nickname:%s\n",from, nick_name);
//     printf("================================================\n");
//     g_call_type = call_type;
//     if (NULL != strstr(json_call_control, "answer_auto")) { //自动接听
//         if (NULL != strstr(json_call_control, "app_broadcast")) {
//             // 云广播来电业务
//         }else if (NULL != strstr(json_call_control, "app_talk")) {
//             // 安防对讲来电业务
//         }

//         //收到需要自动接听的来电，调用cmcc_rtc_pickup接听接口之前，需要先设置自动接听的flag到底层
//         cmcc_rtc_setopt(CMCC_OPT_AUTO_ANSWER_FLAG,"1");
//         ret = cmcc_rtc_pickup(session);

//         #ifndef ENABLE_AUDIO_CODEC
//         g_enable_audio = 1;
//         create_send_audio_thread();
//         #endif
//         if (CMCC_CALL_TYPE_IMS_1V1_VIDEO == g_call_type && 0 == ret) {
//             g_enable_video = 1;
//             create_send_video_thread();
//         }
//     } else {
//         //走手动接听逻辑，不能自动接听
//     }
// }

static void __on_recv_ring(int session, const char *from, const char *displayname, const char *to, int early_media)
{
#ifndef ENABLE_AUDIO_CODEC
    //非编解码版本的SDK，如果early_media为1时，需要先向平台发送媒体流，平台的媒体数据才能发送到终端
    if (1 == early_media) {
        g_enable_audio = 1;
        create_send_audio_thread();
    }
#endif
    printf("================ __on_recv_ring ================\n");
}

static void __on_recv_answer(int session, const char *from, const char *displayname, const char *to, cmcc_call_type_t call_type)
{
    std::string command = "/home/<USER>/updateexpression.sh /home/<USER>/resource/step2_通话中眨眼.mp4";  
    std::thread t(runCommand, command);  
    t.detach(); // 分离线程，使主线程继续执行 

    // system("/home/<USER>/updateexpression.sh /home/<USER>/resource/step2_通话中眨眼.mp4");

    printf("================ __on_recv_answer calltype[%d] ================\n",call_type);
// #ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
//     g_audio_pcm_fp_input = fopen(inputAudioFilename, "w");
//     if(!g_audio_pcm_fp_input){ 
//         return;
//     }
// #endif

#ifndef ENABLE_AUDIO_CODEC
    if (0 == g_enable_audio) {
        g_enable_audio = 1;
        create_send_audio_thread();
    }
#endif
    g_calling = 1;
    if (CMCC_CALL_TYPE_IMS_1V1_VIDEO == call_type) {
        g_enable_video = 1;
        create_send_video_thread();
    }
}

// static void __on_recv_hangup(int session, int err_code, const char *reason, cmcc_call_type_t call_type)
// {
//     printf("================ __on_recv_hangup ================\n");
//     g_calling = 0;
//     g_enable_video = 0;
//     gstV4L2Pipeline->Stop();
//     // pushEvent_->publish(callFinishedMsg);

// #ifndef ENABLE_AUDIO_CODEC
//     g_enable_audio = 0;
// #endif

// #ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
//     if ( NULL != g_audio_pcm_fp_input) {
//         fclose(g_audio_pcm_fp_input);
//         g_audio_pcm_fp_input = NULL;
//     }
    
//     if (NULL != g_audio_pcm_fp_output) {
//         fclose(g_audio_pcm_fp_output);
//         g_audio_pcm_fp_output = NULL;
//     }
// #endif
// }

static void __on_recv_notify(cmcc_notify_t notify_type, const char *content)
{
    printf("================ __on_recv_notify ================\n"); 
    printf("type:%d, content:%s\n", notify_type, content);  
    if (CMCC_NOTIFY_VIDEO_CODEC == notify_type) {
        /*
         * @说明：
         * 收到此回调事件后，厂家要根据对应的编码方式来发送流
         */
        if (NULL != strstr(content,"H265")) {
            if(!g_h265) {
                gstV4L2Pipeline->Destroy();
                gstV4L2Pipeline->Create265();
                g_h265 = 1;
            }
        } else {
            if(g_h265) {
                gstV4L2Pipeline->Destroy();
                gstV4L2Pipeline->Create();
                g_h265 = 0;
            }
        }
    } else if (CMCC_NOTIFY_VIDEO_RESOLUTION == notify_type) {
        /*
        * @说明：
        * 收到此回调事件后，厂家要根据此分辨率来发送视频数据
        * 
        * [1]若content中存在quality字段，说明当前通话为对讲通话，
        *    厂家需要判断当前对讲通话的quality值是否与安防直播的quality
        *    值一致，若一致且编码方式（H264/H265）也和安防一致，则复用安防视频流，
        *    否则按照当前quality字段和编码方式送流。
        * [2]特殊情况下，如果安防和固话的视频流不一致（即编码方式或分辨率不一致），且设备不能同时出两路高清（或超高清）视频流时，
        *    设备出的第二路流（固话流）的分辨率可以降到设备能够出的最高分辨率。
        *    如：安防直播流是H265、1080P的流，对讲是H264、1080P的流，但此时设备能出的第二路对讲流的最高分辨率是H264、720P，
        *    那么厂家可以推H264、720P的流给固话SDK。
        * [3]若content中不存在quality字段，则说明当前通话为实时通话，
        *    厂家按照content中实际的宽高和编码送流
        * [4]quality字段取值和安防一致，如sd表示标清，hd表示高清，fhd表示超清
        * 
        * 回调示例：
        * [1]content:quality=sd
        *    表明当前通话为对讲通话，发送视频流的分辨率根据quality字段，忽略宽高
        * [2]content:width=640,height=360
        *    表明当前通话为实时通话，发送视频流的分辨率根据width和height
        */

        //【注意!!!!】此处为demo代码逻辑，对接厂家需根据实际协商结果送流
        // FileFolderName = "";
        if (NULL != strstr(content,"quality=")){
            //对讲业务的分辨率回调
            if (NULL != strstr(content,"quality=sd")) {
                //如果视频编码方式和安防一致（如都使用H264或者都使用H265），复用安防标清视频流
                //否则依据实际编码方式和quality发送视频流
                // FileFolderName.append("480P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create();

            }else if( NULL != strstr(content,"quality=hd")){
                //如果视频编码方式和安防一致（如都使用H264或者都使用H265），复用安防高清视频流
                //否则依据实际编码方式和quality发送视频流
                // FileFolderName.append("720P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create_720p();

            }else if(NULL != strstr(content,"quality=fhd")){
                //如果视频编码方式和安防一致（如都使用H264或者都使用H265），复用安防超高清视频流
                //否则依据实际编码方式和quality发送视频流
                // FileFolderName.append("1080P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create_1080p();

            }
        }else{
            //实时通话业务分辨率回调，厂家按实际协商的分辨率发送视频流
            if(NULL != strstr(content,"640") || NULL != strstr(content,"360")){
                // FileFolderName.append("480P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create();


            }else if(NULL != strstr(content,"1280") || NULL != strstr(content,"720")) {
                // FileFolderName.append("720P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create_720p();

            }else if(NULL != strstr(content,"1920") || NULL != strstr(content,"1080")){
                // FileFolderName.append("1080P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create_1080p();

            }else{
                //其他
                // FileFolderName.append("480P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create();

            }
        }
        // printf("======the file folder name is %s=========\n\n", FileFolderName.c_str());
        if (g_enable_video) {
            g_video_resolution_changed =1;
        }

    }else if(CMCC_NOTIFY_VIDEO_CODEC_AND_RESOLUTION == notify_type){
        printf("======CMCC_NOTIFY_VIDEO_CODEC_AND_RESOLUTION=========\n\n");
        /**
         * @brief 出现此回调通知，说明厂家已启用合并视频编码方式和分辨率的回调方式，CMCC_NOTIFY_VIDEO_CODEC和CMCC_NOTIFY_VIDEO_RESOLUTION回调通知被禁用。
         * 默认情况下，视频编码方式和分辨率是分开通知给厂家，启用视频编码和分辨率同时回调给厂家的代码，参考下文的cmcc_rtc_setopt(CMCC_OPT_ENABLE_CODEC_AND_RESOLUTION_CALLBACK,"1");
         */

        /*
        * @说明：
        * 收到此回调事件后，厂家要根据此分辨率来发送视频数据
        * 
        * [1]若content中存在quality字段，说明当前通话为对讲通话，
        *    厂家需要判断当前对讲通话的quality值是否与安防直播的quality
        *    值一致，若一致且编码方式（H264/H265）也和安防一致，则复用安防视频流，
        *    否则按照当前quality字段和编码方式送流。
        * [2]特殊情况下，如果安防和固话的视频流不一致（即编码方式或分辨率不一致），且设备不能同时出两路高清（或超高清）视频流时，
        *    设备出的第二路流（固话流）的分辨率可以降到设备能够出的最高分辨率。
        *    如：安防直播流是H265、1080P的流，对讲是H264、1080P的流，但此时设备能出的第二路对讲流的最高分辨率是H264、720P，
        *    那么厂家可以推H264、720P的流给固话SDK。
        * [3]若content中不存在quality字段，则说明当前通话为实时通话，
        *    厂家按照content中实际的宽高和编码送流
        * [4]quality字段取值和安防一致，如sd表示标清，hd表示高清，fhd表示超清
        * 
        * 回调示例：
        * [1]content:quality=hd,codec=H265
        *    表明当前通话为对讲通话，发送视频流的分辨率根据quality字段，忽略宽高, 编码方式H265
        * [2]content:width=640,height=360,codec=H265
        *    表明当前通话为实时通话，发送视频流的分辨率根据width和height, 编码方式H265
        */

        // if (NULL != strstr(content,"H265")) {
        //     g_h265 = 1;
        // } else {
        //     g_h265 = 0;
        // }


        if (NULL != strstr(content,"H265")) {
            if(!g_h265) {
                gstV4L2Pipeline->Destroy();
                gstV4L2Pipeline->Create265();
                g_h265 = 1;
            }
        } else {
            if(g_h265) {
                gstV4L2Pipeline->Destroy();
                gstV4L2Pipeline->Create();
                g_h265 = 0;
            }
        }

        //【注意!!!!】此处为demo代码逻辑，对接厂家需根据实际协商结果送流
        // FileFolderName = "";
        if (NULL != strstr(content,"quality=")){
            //对讲业务的视频编码方式和分辨率回调
            if (NULL != strstr(content,"quality=sd")) {
                //如果视频编码方式和安防一致（如都使用H264或者都使用H265），复用安防标清视频流
                //否则依据实际编码方式和quality发送视频流
                // FileFolderName.append("480P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create();

            }else if( NULL != strstr(content,"quality=hd")){
                //如果视频编码方式和安防一致（如都使用H264或者都使用H265），复用安防高清视频流
                //否则依据实际编码方式和quality发送视频流
                // FileFolderName.append("720P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create_720p();

            }else if(NULL != strstr(content,"quality=fhd")){
                //如果视频编码方式和安防一致（如都使用H264或者都使用H265），复用安防超高清视频流
                //否则依据实际编码方式和quality发送视频流
                // FileFolderName.append("1080P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create_1080p();

            }
        }else{
            //实时通话业务分辨率回调，厂家按实际协商的分辨率发送视频流
            if(NULL != strstr(content,"640") || NULL != strstr(content,"360")){
                // FileFolderName.append("480P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create();


            }else if(NULL != strstr(content,"1280") || NULL != strstr(content,"720")) {
                // FileFolderName.append("720P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create_720p();

            }else if(NULL != strstr(content,"1920") || NULL != strstr(content,"1080")){
                // FileFolderName.append("1080P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create_1080p();

            }else{
                //其他
                // FileFolderName.append("480P");

                    gstV4L2Pipeline->Destroy();
                    gstV4L2Pipeline->Create();
            }
        }
        // printf("======the file folder name is %s=========\n\n", FileFolderName.c_str());
        if (g_enable_video) {
            g_video_resolution_changed =1;
        }

        
    }else if(CMCC_NOTIFY_REMOTE_CONTROL == notify_type) {
        printf("======CMCC_NOTIFY_REMOTE_CONTROL=========\n\n");
        /*
         * @说明：
         * 远程控制通知，content为json数组格式，厂家收到此回调通知后，解析json数组，实现对应控制ID的控制操作
         * 
         * =======================================================================================
         * 【1】控制台灯开关，content格式如下：
         * [{
         *      "id": "100100",
         *      "value": "1"
         * }]
         * 其中远程控制id 100100表示控制台灯开关；value为手机端对应的DTMF数字按键，1表示开灯，3表示关灯。
         * =======================================================================================
         * 【2】控制门锁开门，content格式如下：
         * [{
         *      "id": "100200",
         *      "value": "1234"
         * }]
         * 其中远程控制id 100200表示控制门锁开门；"value"为开门的密码（对应手机拨号盘上输入的密码），厂家取出该字段，
         * 和本地保存的密码就行比对，校验通过实现开门操作。
         * =======================================================================================
         * 【3】控制摄像头云台转动，content格式如下：
         * [{
         *      "id": "100300",
         *      "value": "2"
         * }]
         * 其中远程控制id 100300表示控制摄像头云台；value值对应手机拨号盘上的DTMF数字按键，为2表示向上转动，
         * 为4表示向左转动，为6表示向右转动，为8表示向下转动。
         * 
         * @注意：
         * 如果要实现设备的控制功能的话，需要将对应的控制ID设置到SDK内部
         * 参考下面的“cmcc_rtc_setopt(CMCC_OPT_REMOTE_CONTROL_ID,"100200");”
         * =======================================================================================
         * 
         */
    }
}

static void __on_recv_keyframe_request()
{
    printf("================ __on_recv_keyframe_request ================\n");
    //收到此回调事件后，对接厂家需要发送一个关键帧给对端，可解决对端视频出现花屏或者卡顿的问题
}

static void __on_recv_dtmf(int value)
{
    printf("================ __on_recv_dtmf ================\n");
    printf("value:%d\n",value);
}

#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
static void __on_recv_audio_pcm_data(char *pcm_data, int size)
{
    // 厂商通过此回调接口接收pcm音频数据 pcm_data为pcm数据，size为数据长度
    // printf("__on_recv_audio_pcm_data, test, data size=%d\n", size);
    if (NULL == g_audio_pcm_fp_output) {
        printf("__on_recv_audio_pcm_data, data size=%d\n", size);
        g_audio_pcm_fp_output = fopen("output16000.pcm", "w");
        if (!g_audio_pcm_fp_output) {
            printf("failed to open output16000 file!!!\n");
            return;
        }
    }
    int write_data_len = fwrite(pcm_data, 1, size, g_audio_pcm_fp_output);

}

static void __on_send_audio_pcm_data(char *pcm_data, int size)
{
    // 厂商通过此回调接口发送pcm音频数据，pcm_data为发送数据buffer，size为发送数据的长度
    // 特别注意：对接厂家一定要保证拷贝给pcm_data的数据为正常的有效数据，异常数据不能拷贝给pcm_data。
    printf("__on_send_audio_pcm_data, test, data size=%d\n", size);
    if (NULL == g_audio_pcm_fp_input) {
        printf("__on_send_audio_pcm_data, data size=%d\n", size);
        g_audio_pcm_fp_input = fopen("input16000.pcm", "r");
        if (!g_audio_pcm_fp_input) {
            printf("failed to open input16000 file!!!\n");
            return;
        }
    }
    int read_data_len = fread(pcm_data, 1, size, g_audio_pcm_fp_input);
    if (read_data_len <= 0) {
        printf("__on_send_audio_pcm_data,fseek!!!\n");
        fseek(g_audio_pcm_fp_input, 0, SEEK_SET);
    }
    // input_pcm_buffer->read(pcm_data, size);
}
#endif

std::unordered_map<std::string, cmcc_rtc_opt_> stringsToEnum = {
    {"CMCC_OPT_AUTO_ANSWER_FLAG", CMCC_OPT_AUTO_ANSWER_FLAG},
    {"CMCC_OPT_ENABLE_H265", CMCC_OPT_ENABLE_H265},
    {"CMCC_OPT_HANGUP_WITH_REASON", CMCC_OPT_HANGUP_WITH_REASON},
    {"CMCC_OPT_ENABLE_OPUS_NEGOTIATE", CMCC_OPT_ENABLE_OPUS_NEGOTIATE},
    {"CMCC_OPT_VIDEO_RESOLUTION", CMCC_OPT_VIDEO_RESOLUTION},
    {"CMCC_OPT_GOVERNMENT_ENTERPRISE", CMCC_OPT_GOVERNMENT_ENTERPRISE},
    {"CMCC_OPT_THREAD_MODE_LOOP", CMCC_OPT_THREAD_MODE_LOOP},
    {"CMCC_OPT_VIDEO_FEC", CMCC_OPT_VIDEO_FEC},
    {"CMCC_OPT_PORTRAIT", CMCC_OPT_PORTRAIT},
    {"CMCC_OPT_ENABLE_DTMF_TONE", CMCC_OPT_ENABLE_DTMF_TONE},
    {"CMCC_OPT_REMOTE_CONTROL_ID", CMCC_OPT_REMOTE_CONTROL_ID},
    {"CMCC_OPT_ENABLE_CODEC_AND_RESOLUTION_CALLBACK", CMCC_OPT_ENABLE_CODEC_AND_RESOLUTION_CALLBACK},
    {"CMCC_OPT_RECV_VIDEO", CMCC_OPT_RECV_VIDEO},
    {"CMCC_OPT_DISENABLE_CONTACTS", CMCC_OPT_DISENABLE_CONTACTS},
    {"CMCC_OPT_FLASH_DIR", CMCC_OPT_FLASH_DIR}
};

#ifdef XUNFEI
std::shared_ptr<ros::ServiceClient> CmccRtcNode::modeClient_ = nullptr;
#endif

std::shared_ptr<ros::ServiceClient> CmccRtcNode::wakeClient_ = nullptr;

std::shared_ptr<ros::ServiceClient> CmccRtcNode::abortClient_ = nullptr;


CmccRtcNode::CmccRtcNode(cmcc_rtc_device_info_t& device_info, cmcc_rtc_log_config_t& log_config,
    cmcc_rtc_login_params_t& login_params, std::unordered_map<cmcc_rtc_opt_,
    const char*>& rtc_opts, ros::NodeHandle& nh)
    : device_info_(device_info), log_config_(log_config), login_params_(login_params), rtc_opts_(rtc_opts), nh_(nh), _writeShmId(0), _writeShared(NULL)
{
    event_handler = {     //事件处理
        .on_login_success           = __on_login_success,
        .on_login_failed            = __on_login_failed,
        .on_recv_call               = __on_recv_call,
        .on_recv_ring               = __on_recv_ring,
        .on_recv_answer             = __on_recv_answer,
        .on_recv_hangup             = __on_recv_hangup,
        .on_recv_notify             = __on_recv_notify,
        .on_recv_keyframe_request   = __on_recv_keyframe_request,
        .on_recv_dtmf               = __on_recv_dtmf,
#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
        .on_recv_audio_pcm_data     = __on_recv_audio_pcm_data,
        .on_send_audio_pcm_data     = __on_send_audio_pcm_data,
#else
        .on_recv_audio_pcm_data     = NULL,
        .on_send_audio_pcm_data     = NULL,
#endif
#ifndef ENABLE_AUDIO_CODEC
        .on_recv_audio_packet       = __on_recv_audio_packet,
#else
        .on_recv_audio_packet       = NULL,
#endif
    };
    
    cmcc_rtc_init(&device_info_, &log_config_, &event_handler);

    gstV4L2Pipeline = new GstV4l2Pipeline(NULL, NULL);
    gstV4L2Pipeline->Create();
    gstV4L2Pipeline->SetVoipBufferCallback(
            std::bind(VideoVoipBufferCallback, placeholders::_1, placeholders::_2, placeholders::_3, placeholders::_4));
#ifdef ENABLE_MULTI_STREAMS
    gstV4L2Pipeline->SetCameraBufferCallback(
            std::bind(VideoCameraBufferCallback, placeholders::_1, placeholders::_2, placeholders::_3, placeholders::_4));
#endif

    for(std::unordered_map<cmcc_rtc_opt_, const char*>::iterator it=rtc_opts_.begin(); it!=rtc_opts_.end(); it++) {
        cmcc_rtc_setopt(it->first, it->second);
    }

    // cmcc_rtc_setopt(cmcc_rtc_opt_(1000), "dev");

    while(cmcc_rtc_login(&login_params_) != 0) {
        ROS_INFO("======================= failed to invoke cmcc_rtc_login interface =======================\n");
        //SDK内部连接业务平台线程启动失败（正常不会），等待一段时间重试
        usleep(1000 * 1000* 10);
    }

    _writeIsAvaliableShmId = shmget((key_t)2886, sizeof(is_avalibale), 0);
    while(_writeIsAvaliableShmId == -1) {
        _writeIsAvaliableShmId = shmget((key_t)2886, sizeof(is_avalibale), 0);
    }
    _writeIsAvaliableShared = (is_avalibale*)shmat(_writeIsAvaliableShmId, NULL, 0);
    while(_writeIsAvaliableShared == (is_avalibale*)-1) {
        _writeIsAvaliableShared = (is_avalibale*)shmat(_writeIsAvaliableShmId, NULL, 0);
    }
    
    eventSuber_ = nh.subscribe<rtc::SIGCEvent>("/homi_speech/sigc_event_topic", 10, &CmccRtcNode::eventCallback, this);
    audioSuber_ = nh.subscribe<rtc::PCMStream>("/audio_node/pcm_stream", 10, &CmccRtcNode::pcmCallback, this);

#ifdef XUNFEI
    modeClient_ = std::make_shared<ros::ServiceClient>(nh.serviceClient<xunfei_audio::SetNrMode>("/audio_node/set_nr_mode_service"));
    ROS_INFO("Waiting for service: /audio_node/set_nr_mode_service.\n");
    modeClient_->waitForExistence();
    ROS_INFO("/audio_node/set_nr_mode_service already exists.\n");
#endif

    wakeClient_ = std::make_shared<ros::ServiceClient>(nh.serviceClient<xunfei_audio::SetWakeEvent>("/audio_node/set_wake_event_service"));
    ROS_INFO("Waiting for service: /audio_node/set_wake_event_service.\n");
    wakeClient_->waitForExistence();
    ROS_INFO("/audio_node/set_wake_event_service exists.\n");

    abortClient_ = std::make_shared<ros::ServiceClient>(nh.serviceClient<rtc::AssistantAbort>("/homi_speech/helper_assistant_abort_service"));
    ROS_INFO("Waiting for service: /homi_speech/helper_assistant_abort_service.\n");
    abortClient_->waitForExistence();
    ROS_INFO("/homi_speech/helper_assistant_abort_service already exists.\n");
}

CmccRtcNode::~CmccRtcNode()
{
    cmcc_rtc_fini();

    gstV4L2Pipeline->Destroy();
    delete gstV4L2Pipeline;
}

void CmccRtcNode::__on_recv_hangup(int session, int err_code, const char *reason, cmcc_call_type_t call_type)
{
    printf("================ __on_recv_hangup ================\n");
    g_calling = 0;
    g_enable_video = 0;
    gstV4L2Pipeline->Stop();

    std::string command = "/home/<USER>/updateexpression.sh /home/<USER>/resource/默认_眨眼睛.mp4";  
    std::thread t(runCommand, command);  
    t.detach(); // 分离线程，使主线程继续执行 
    // system("/home/<USER>/updateexpression.sh /home/<USER>/resource/默认_眨眼睛.mp4");
    
#ifdef XUNFEI
    xunfei_audio::SetNrMode nrmode;
    nrmode.request.vs_nr_mode = nrModeToInt32(NR_MODE_INTERACT);
    while(!modeClient_->call(nrmode)) {
        ROS_ERROR("Change MIC NR mode false, target mode: %s, current mode: %s\n", int32ToChar(nrmode.request.vs_nr_mode), int32ToChar(nrmode.response.vs_nr_mode));
        //休眠10ms再次设置降噪模式
        usleep(10000);
    }
#endif

    xunfei_audio::SetWakeEvent wkevent;
    wkevent.request.target = true;
    wakeClient_->call(wkevent);

#ifndef ENABLE_AUDIO_CODEC
    g_enable_audio = 0;
#endif

#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
    if ( NULL != g_audio_pcm_fp_input) {
        fclose(g_audio_pcm_fp_input);
        g_audio_pcm_fp_input = NULL;
    }
    
    if (NULL != g_audio_pcm_fp_output) {
        fclose(g_audio_pcm_fp_output);
        g_audio_pcm_fp_output = NULL;
    }
#endif
}

void CmccRtcNode::__on_recv_call(int session, const char *from, const char *displayname, const char *to, cmcc_call_type_t call_type, const char *json_call_control)
{
// #ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
//     g_audio_pcm_fp_input = fopen(inputAudioFilename, "w");
//     if(!g_audio_pcm_fp_input){ 
//         return;
//     }
// #endif
    rtc::AssistantAbort abort;
    abortClient_->call(abort);
    
#ifdef XUNFEI
    xunfei_audio::SetNrMode nrmode;
    nrmode.request.vs_nr_mode = nrModeToInt32(NR_MODE_CALL);
    modeClient_->call(nrmode);
#endif

    xunfei_audio::SetWakeEvent wkevent;
    wkevent.request.target = false;
    wakeClient_->call(wkevent);

    int ret = 0;
    g_calling = 1;
    printf("================ __on_recv_call(%d) json_call_control(%s)================\n",call_type, json_call_control);
    const char *nick_name = cmcc_rtc_get_nickname_by_number(from);
    printf("from number:%s,nickname:%s\n",from, nick_name);
    printf("================================================\n");
    g_call_type = call_type;
    if (NULL != strstr(json_call_control, "answer_auto")) { //自动接听
        if (NULL != strstr(json_call_control, "app_broadcast")) {
            // 云广播来电业务
        }else if (NULL != strstr(json_call_control, "app_talk")) {
            // 安防对讲来电业务
        }

        //收到需要自动接听的来电，调用cmcc_rtc_pickup接听接口之前，需要先设置自动接听的flag到底层
        cmcc_rtc_setopt(CMCC_OPT_AUTO_ANSWER_FLAG,"1");
        ret = cmcc_rtc_pickup(session);

        #ifndef ENABLE_AUDIO_CODEC
        g_enable_audio = 1;
        create_send_audio_thread();
        #endif
        if (CMCC_CALL_TYPE_IMS_1V1_VIDEO == g_call_type && 0 == ret) {
            g_enable_video = 1;
            create_send_video_thread();
        }
    } else {
        //走手动接听逻辑，不能自动接听
        if(NULL != strstr(json_call_control, "called")) {
            std::string command = "/home/<USER>/updateexpression.sh /home/<USER>/resource/step1_通话.mp4";  
            std::thread t(runCommand, command);  
            t.detach(); // 分离线程，使主线程继续执行 
            // system("/home/<USER>/updateexpression.sh /home/<USER>/resource/step1_通话.mp4");

            FILE* fp;
            char buffer[50];
            fp = popen(R"(aplay -l | grep "USB Audio Devic" -A 2 | grep "card" | awk '{print $2}' | tr -d ':')", "r");
            if(fp) {
                fgets(buffer,sizeof(buffer),fp);
                std::string device_name = "plughw:" + std::string(1, buffer[0]) + ",0";

                snd_pcm_t* audioPlayer;
                AlsaHelperConfig audioPlayerConfig = AlsaHelperConfig();
                cmcc_webrtc::FileWrapper& ringFile = *cmcc_webrtc::FileWrapper::Create();

                if(snd_pcm_open(&audioPlayer, device_name.c_str(), SND_PCM_STREAM_PLAYBACK, 0) >= 0){
                    if(audioPlayerConfig.config(audioPlayer, "Signed 16 bit Little Endian", 1, 16000) >= 0){
                        snd_pcm_start(audioPlayer);
                        if(ringFile.OpenFile("/home/<USER>/resource/ring.wav", true, true, false) != -1){
                            int frameSize = audioPlayerConfig.sizeToFrame(320);
                            int8_t* recordingBuffer = new int8_t[320];

                            while (ringFile.Read(recordingBuffer, 320) > 0) {
                                if(snd_pcm_writei(audioPlayer, recordingBuffer, frameSize) == -EPIPE){
                                    snd_pcm_prepare(audioPlayer);
                                }
                            }

                            usleep(500000); //休眠1s无法接通电话
                            delete[] recordingBuffer;
                            ringFile.Flush();
                            ringFile.CloseFile();
                            delete &ringFile;
                        }
                    }
                    snd_pcm_close(audioPlayer);
                }
            }

            // if(g_720p) {
            //     gstV4L2Pipeline->Destroy();
            //     gstV4L2Pipeline->Create();
            //     g_720p = 0;
            // }
        } else {
            // if(!g_720p) {
            //     gstV4L2Pipeline->Destroy();
            //     gstV4L2Pipeline->Create_720p();
            //     g_720p = 1;
            // }
        }

        // 暂无手动语音接听功能，5s后自动接听
        // usleep(5000000);

        cmcc_rtc_setopt(CMCC_OPT_AUTO_ANSWER_FLAG,"1");
        ret = cmcc_rtc_pickup(session);

        std::string command = "/home/<USER>/updateexpression.sh /home/<USER>/resource/step2_通话中眨眼.mp4";  
        std::thread t(runCommand, command);
        t.detach(); // 分离线程，使主线程继续执行 

        // system("/home/<USER>/updateexpression.sh /home/<USER>/resource/step2_通话中眨眼.mp4");

        #ifndef ENABLE_AUDIO_CODEC
        g_enable_audio = 1;
        create_send_audio_thread();
        #endif
        if (CMCC_CALL_TYPE_IMS_1V1_VIDEO == g_call_type && 0 == ret) {
            g_enable_video = 1;
            create_send_video_thread();
        }
    }
}

void CmccRtcNode::eventCallback(const rtc::SIGCEvent::ConstPtr& msg)
{
    ROS_INFO("%s\n", msg->event.c_str());
    Json::Value eventMsg;
    if(str2json.parse(msg->event.c_str(), eventMsg)) {
        // ROS_INFO("%s\n", eventMsg["event"].asString().c_str());
        if (0 == strcmp(eventMsg["event"].asString().c_str(),"robot_action")) {
            if (0 == strcmp(eventMsg["body"]["actionType"].asString().c_str(),"phoneCallAction") &&
                0 == strcmp(eventMsg["body"]["actionArgument"].asString().c_str(),"off")) {
                //【挂断电话】
                if (1 == g_calling) {
                    /*
                    * 设置主动挂断原因到SDK内部
                    * 默认情况下，用户主动挂断时，不用调此接口；被安防业务打断当前正在进行的通话时，需要调用此接口
                    * "1"表示被安防告警打断后的挂断；"2"表示被安防对讲打断后的挂断
                    */
                    // cmcc_rtc_setopt(CMCC_OPT_HANGUP_WITH_REASON,"0");

                    cmcc_rtc_hangup(g_call_session);
                    g_calling = 0;
    #ifndef ENABLE_AUDIO_CODEC
                    g_enable_audio = 0;
    #endif
                    g_enable_video = 0;
                    gstV4L2Pipeline->Stop();

                    std::string command = "/home/<USER>/updateexpression.sh /home/<USER>/resource/默认_眨眼睛.mp4";  
                    std::thread t(runCommand, command);  
                    t.detach(); // 分离线程，使主线程继续执行 

                    // system("/home/<USER>/updateexpression.sh /home/<USER>/resource/默认_眨眼睛.mp4");

#ifdef XUNFEI
                    xunfei_audio::SetNrMode nrmode;
                    nrmode.request.vs_nr_mode = nrModeToInt32(NR_MODE_INTERACT);
                    while(!modeClient_->call(nrmode)) {
                        ROS_ERROR("Change MIC NR mode false, target mode: %s, current mode: %s\n", int32ToChar(nrmode.request.vs_nr_mode), int32ToChar(nrmode.response.vs_nr_mode));
                        //休眠10ms再次设置降噪模式
                        usleep(10000);
                    }
#endif

                    xunfei_audio::SetWakeEvent wkevent;
                    wkevent.request.target = true;
                    wakeClient_->call(wkevent);
                } else {
                    ROS_INFO("============ hangup error ============\n");
                }
    #ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
                if ( NULL != g_audio_pcm_fp_input) {
                    fclose(g_audio_pcm_fp_input);
                    g_audio_pcm_fp_input = NULL;
                }
                
                if (NULL != g_audio_pcm_fp_output) {
                    fclose(g_audio_pcm_fp_output);
                    g_audio_pcm_fp_output = NULL;
                }
    #endif
            } else if (0 == strcmp(eventMsg["body"]["actionType"].asString().c_str(),"phoneCallAction") && 
                    0 == strcmp(eventMsg["body"]["actionArgument"].asString().c_str(),"on")) { 
                int ret = 0;
                //【手动接听来电】
                if (1 == g_calling) {
                    ret = cmcc_rtc_pickup(g_call_session);
    #ifndef ENABLE_AUDIO_CODEC
                    g_enable_audio = 1;
                    create_send_audio_thread();
    #endif
                    if (CMCC_CALL_TYPE_IMS_1V1_VIDEO == g_call_type && 0 == ret) {
                        g_enable_video = 1;
                        create_send_video_thread();
                    }
                } else {
                    ROS_INFO("============ pickup error ============\n");
                }
            // } else if (0 == strcmp(eventMsg["event"].asString().c_str(),"l")) { 
            //     //【登出，对接方不用调用此接口】
            //     //g_calling = 0;
            //     //cmcc_rtc_logout();
            // } else if (0 == strcmp(eventMsg["event"].asString().c_str(),"g")) {  
            //     // 【获取通信录昵称】
            //     const char *name = cmcc_rtc_get_nickname_by_number("18867122970");
            //     ROS_INFO("cmcc_rtc_get_nickname_by_number,name:%s\n", name);
            // } else if (0 == strcmp(eventMsg["event"].asString().c_str(),"n")) {
            // // 【根据昵称获取camera call设备通讯录手机号码，并调用cmcc_rtc_callout接口】
            //     const char *phone_number = cmcc_rtc_get_number_by_nickname("测试");
            //     if (strlen(phone_number) == 0) {
            //         ROS_INFO("cmcc_rtc_get_number_by_nickname, phoneNumber is NULL\n");
            //     }else {
            //         ROS_INFO("cmcc_rtc_get_number_by_nickname, nickname:测试, phoneNumber:%s\n", phone_number);
            //         cmcc_rtc_callout(phone_number,CMCC_CALL_TYPE_IMS_1V1_VIDEO);
            //         g_calling = 1;          
            //     }
            } else {
                ROS_INFO("======================= No robot action type =======================\n");
            }
        } else if (0 == strcmp(eventMsg["event"].asString().c_str(),"phone_call")) {
                //【发起呼叫】
                if (1 == g_login_success) {
                    if (0 == g_calling) {
                        if (eventMsg["body"]["phoneNumber"].asString().length() == 0) {
                            ROS_INFO("============ expected number ============\n");
                        } else {
// #ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
                            // ROS_INFO("============ start open write file ============\n");
                            // g_audio_pcm_fp_input = fopen(inputAudioFilename, "w");
                            // if(g_audio_pcm_fp_input){ 
                            //     ROS_INFO("============ successed to open write file ============\n");
                            //     cmcc_rtc_callout(eventMsg["body"]["phoneNumber"].asString().c_str(),CMCC_CALL_TYPE_IMS_1V1_VIDEO);
                            //     g_calling = 1;
                            // } else {
                            //     ROS_INFO("============ failed to open write file ============\n");
                            // }
// #else
                            rtc::AssistantAbort abort;
                            abortClient_->call(abort);
                            
#ifdef XUNFEI
                            xunfei_audio::SetNrMode nrmode;
                            nrmode.request.vs_nr_mode = nrModeToInt32(NR_MODE_CALL);
                            modeClient_->call(nrmode);
#endif

                            xunfei_audio::SetWakeEvent wkevent;
                            wkevent.request.target = false;
                            wakeClient_->call(wkevent);

                            if(g_h265) {
                                gstV4L2Pipeline->Destroy();
                                gstV4L2Pipeline->Create();
                                g_h265 = 0;
                            }

                            ROS_INFO("============ cmcc_rtc_callout ============\n");
                            cmcc_rtc_callout(eventMsg["body"]["phoneNumber"].asString().c_str(),CMCC_CALL_TYPE_IMS_1V1_VIDEO);
                            g_calling = 1;

                            std::string command = "/home/<USER>/updateexpression.sh /home/<USER>/resource/step1_通话.mp4";  
                            std::thread t(runCommand, command);  
                            t.detach(); // 分离线程，使主线程继续执行 

                            // system("/home/<USER>/updateexpression.sh /home/<USER>/resource/step1_通话.mp4");
// #endif
                        }
                    } else {
                        ROS_INFO("============ be calling ============\n");
                    }

                } else {
                    ROS_INFO("============ login failed ============\n");
                }
        } else {
            ROS_INFO("======================= No event action =======================\n");
        }
    } else {
        ROS_INFO("======================= SIGEvent parsing failed =======================\n");
    }
}

void CmccRtcNode::pcmCallback(const rtc::PCMStream::ConstPtr& msg)
{
#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
    // 文件作为缓冲区，有回音，2min后文件指针跳转到头
    // if(g_audio_pcm_fp_input && g_calling == 1) {
    //     ROS_INFO("======================= receviced pcm stream =======================\n");
    //     int write_data_len = fwrite(&msg->data[0], 1, msg->data.size(), g_audio_pcm_fp_input);
    //     fflush(g_audio_pcm_fp_output);
    // }

    // 共享内存
    if(_writeIsAvaliableShared->avaliable) {
        if(_writeShared==NULL) {
            _writeShmId = shmget((key_t)2887, sizeof(audio_buffer), 0);
            if(_writeShmId == -1) {
                ROS_INFO("======================= connect share memory failed, code: %d =======================\n", errno);
                return;
            }
            _writeShared = (audio_buffer*)shmat(_writeShmId, NULL, 0);
            if(_writeShared == (audio_buffer*)-1) {
                ROS_INFO("======================= connect share memory failed, code: %d =======================\n", errno);
                _writeShared = NULL;
                return;
            }
        }
        int curlen = M_TOTSIZE - (_writeShared->m_Writeindex + M_TOTSIZE - _writeShared->m_Readindex) % M_TOTSIZE;  //当前缓冲区空余长度
        if(msg->data.size() >= curlen){   
            ROS_INFO("======================= share memory not enough =======================\n");                                               //当前的缓冲区仅剩一帧可以写，此时要等待读取，暂时抛弃一部分数据
            return;
        }
        if((_writeShared->m_Writeindex + msg->data.size()) > M_TOTSIZE)                               //大于总长度
        {
            int overlen = _writeShared->m_Writeindex + msg->data.size() - M_TOTSIZE;                   //超出长度部分
            int curlen = msg->data.size() - overlen;						                //可容纳长度
            memcpy(_writeShared->m_Audio + _writeShared->m_Writeindex, &msg->data[0], curlen);	                    //写入缓冲区	  
            memcpy(_writeShared->m_Audio, &msg->data[0] + curlen, overlen);			                //写入缓冲区	 //覆盖原来
            _writeShared->m_Writeindex = (_writeShared->m_Writeindex + msg->data.size()) % M_TOTSIZE;	            //取余 写指针位置
        }else                                                               //长度不超出
        {
            memcpy(_writeShared->m_Audio + _writeShared->m_Writeindex, &msg->data[0], msg->data.size());                       //写入缓冲区
            _writeShared->m_Writeindex += msg->data.size();
        }
        // ROS_INFO("======================= receviced pcm stream =======================\n");
    } else {
        if(_writeShared)
        {
            shmdt(_writeShared);
            _writeShared = NULL;
        }
    }
#else
    if(g_calling == 1) {
        // ROS_INFO("======================= receviced pcm stream =======================\n");
        // input_pcm_buffer->write(&msg->data[0], msg->data.size());
    }
#endif
}


// AudioBuffer::AudioBuffer(int size):m_totsize(size), m_Writeindex(0), m_Readindex(0)
// {
//     m_Audio = new char[m_totsize];
// }

// AudioBuffer::~AudioBuffer()
// {
//     delete m_Audio;
// }

// void AudioBuffer::write(char* pcm, int size)
// {
// 	int curlen = (m_Readindex + m_totsize - m_Writeindex) % m_totsize;  //当前缓冲区空余长度
// 	if(size > curlen){                                                  //当前的缓冲区仅剩一帧可以写，此时要等待读取，暂时抛弃一部分数据
// 	    return;
//     }
// 	if((m_Writeindex + size) > m_totsize)                               //大于总长度
// 	{
// 	   int overlen = m_Writeindex + size - m_totsize;                   //超出长度部分
// 	   int curlen = size-overlen;						                //可容纳长度
// 	   memcpy(m_Audio + m_Writeindex, pcm, curlen);	                    //写入缓冲区	  
// 	   memcpy(m_Audio, pcm + curlen, overlen);			                //写入缓冲区	 //覆盖原来
// 	   m_Writeindex = (m_Writeindex + size) % m_totsize;	            //取余 写指针位置
// 	}else                                                               //长度不超出
// 	{
// 	   memcpy(m_Audio + m_Writeindex, pcm, size);                       //写入缓冲区
// 	   m_Writeindex += size;
// 	}
// 	return;
// }

// bool AudioBuffer::read(char* pcm, int size)
// {
//     int curlen = (m_Writeindex + m_totsize - m_Readindex) % m_totsize;  //当前缓冲区数据长度
// 	if(size > curlen){	                                                //当前的缓冲区不足一帧可以读，此时要等待写入数据
// 	    return false; 
//     }
// 	if((m_Readindex + size) > m_totsize)                                //取数据大于长度
// 	{
// 	   int overlen =  m_Readindex + size - m_totsize;	                //超出部分长度
// 	   int curlen = size - overlen;						                //可用长度
// 	   memcpy(pcm, m_Audio+m_Readindex, curlen);
// 	   memcpy(pcm+curlen, m_Audio,overlen);
// 	   m_Readindex = (m_Readindex + size)%m_totsize;	                //取余 读指针位置
// 	}else
// 	{
// 		memcpy(pcm, m_Audio+m_Readindex, size);
// 		m_Readindex += size;
// 	}
// 	return true;
// }
