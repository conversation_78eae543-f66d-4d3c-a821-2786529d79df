//
// Created by li<PERSON>g on 24-6-4.
//

#include "GstV4l2Pipeline.h"
#include <fstream>

static GstFlowReturn cb_call_sink_new_sample(GstElement *appsink,
                                           gpointer user_data) {

    GstV4l2Pipeline *gstV4L2Pipeline =
            reinterpret_cast<GstV4l2Pipeline *>(user_data);

    GstSample *sample = NULL;
    GstBuffer *buffer = NULL;
    GstMapInfo map;
    const GstStructure *info = NULL;
    GstCaps *caps = NULL;
    GstFlowReturn ret = GST_FLOW_OK;
    int sample_width = 0;
    int sample_height = 0;

    // equals to gst_app_sink_pull_sample (GST_APP_SINK_CAST (appsink), sample);
    g_signal_emit_by_name(appsink, "pull-sample", &sample, &ret);
    if (ret != GST_FLOW_OK) {
        g_printerr("can't pull GstSample.\n");
        return ret;
    }

    if (sample) {
        buffer = gst_sample_get_buffer(sample);
        if (buffer == NULL) {
            g_printerr("get buffer is null\n");
            goto exit;
        }

        gst_buffer_map(buffer, &map, GST_MAP_READ);

        caps = gst_sample_get_caps(sample);
        if (caps == NULL) {
            g_printerr("get caps is null\n");
            goto exit;
        }

        info = gst_caps_get_structure(caps, 0);
        if (info == NULL) {
            g_printerr("get info is null\n");
            goto exit;
        }

        // ---- Read frame and convert to opencv format ---------------
        // convert gstreamer data to OpenCV Mat, you could actually
        // resolve height / width from caps...
        gst_structure_get_int(info, "width", &sample_width);
        gst_structure_get_int(info, "height", &sample_height);

        //        g_print("sample w x h: %d x %d\n", sample_width, sample_height);

        // appsink product queue produce
        {
            // init a cv::Mat with gst buffer address: deep copy
            if (map.data == NULL) {
                g_printerr("appsink buffer data empty\n");
                return GST_FLOW_NOT_LINKED;
            }

//                        g_printerr("stream xxx: %d %d\n", map.data[0], map.data[4]);
//            std::ofstream file("output.h264",
//                               std::ios_base::app |
//                               std::ios_base::binary); // 以追加模式打开文件
//            file.write(reinterpret_cast<const char *>(map.data),
//                       map.size); // 写入数据
//            file.close();
            if (gstV4L2Pipeline->mVoipBufferCallback) { 
                gstV4L2Pipeline->mVoipBufferCallback(map.data, map.size,
                                                 sample_width, sample_height);
            }
        }
    }

    exit:
    if (buffer) {
        gst_buffer_unmap(buffer, &map);
    }
    if (sample) {
        gst_sample_unref(sample);
    }
    return GST_FLOW_OK;
}

static GstFlowReturn cb_camera_sink_new_sample(GstElement *appsink,
                                             gpointer user_data) {

    GstV4l2Pipeline *gstV4L2Pipeline =
            reinterpret_cast<GstV4l2Pipeline *>(user_data);

    GstSample *sample = NULL;
    GstBuffer *buffer = NULL;
    GstMapInfo map;
    const GstStructure *info = NULL;
    GstCaps *caps = NULL;
    GstFlowReturn ret = GST_FLOW_OK;
    int sample_width = 0;
    int sample_height = 0;

    // equals to gst_app_sink_pull_sample (GST_APP_SINK_CAST (appsink), sample);
    g_signal_emit_by_name(appsink, "pull-sample", &sample, &ret);
    if (ret != GST_FLOW_OK) {
        g_printerr("can't pull GstSample.\n");
        return ret;
    }

    if (sample) {
        buffer = gst_sample_get_buffer(sample);
        if (buffer == NULL) {
            g_printerr("get buffer is null\n");
            goto exit;
        }

        gst_buffer_map(buffer, &map, GST_MAP_READ);

        caps = gst_sample_get_caps(sample);
        if (caps == NULL) {
            g_printerr("get caps is null\n");
            goto exit;
        }

        info = gst_caps_get_structure(caps, 0);
        if (info == NULL) {
            g_printerr("get info is null\n");
            goto exit;
        }

        // ---- Read frame and convert to opencv format ---------------
        // convert gstreamer data to OpenCV Mat, you could actually
        // resolve height / width from caps...
        gst_structure_get_int(info, "width", &sample_width);
        gst_structure_get_int(info, "height", &sample_height);

        //        g_print("sample w x h: %d x %d\n", sample_width, sample_height);

        // appsink product queue produce
        {
            // init a cv::Mat with gst buffer address: deep copy
            if (map.data == NULL) {
                g_printerr("appsink buffer data empty\n");
                return GST_FLOW_OK;
            }

//                        g_printerr("stream xxx: %d %d\n", map.data[0], map.data[4]);
//            std::ofstream file("output.h264",
//                               std::ios_base::app |
//                               std::ios_base::binary); // 以追加模式打开文件
//            file.write(reinterpret_cast<const char *>(map.data),
//                       map.size); // 写入数据
//            file.close();
            if (gstV4L2Pipeline->mCameraBufferCallback) {
                gstV4L2Pipeline->mCameraBufferCallback(map.data, map.size,
                                                 sample_width, sample_height);
            }
        }
    }

    exit:
    if (buffer) {
        gst_buffer_unmap(buffer, &map);
    }
    if (sample) {
        gst_sample_unref(sample);
    }
    return GST_FLOW_OK;
}
GstV4l2Pipeline::GstV4l2Pipeline(int argc, char **argv) : video_path_(""), resolution_("") {
    gst_init(&argc, &argv);
    this->video_path_ = this->GetVideoPath();
}

GstV4l2Pipeline::~GstV4l2Pipeline() {}

std::string GstV4l2Pipeline::GetVideoPath() {
    std::string video_path = "/dev/";
    FILE* fp;
    char buffer[50];
    fp = popen("ls /dev/ | grep video[0-9] | head -n 1", "r");
    if(!fp) {
        g_printerr("Failed to get_parameter: video_path\n");
        return;
    }
    if(fgets(buffer,sizeof(buffer),fp) !=NULL) {
        // 移除尾部的换行符
        buffer[strcspn(buffer, "\n")] = 0;

        // 使用 std::string 进行字符串拼接
        video_path += std::string(buffer);

        // 输出视频设备路径
        g_print("Video device path: %s\n", video_path.c_str());
    } else {
        g_printerr("No video device found or failed to read command output.\n");
        pclose(fp);
        return;
    }
    pclose(fp);

    return video_path;
}

void GstV4l2Pipeline::Create() {
    this->resolution_ = "sd";

#ifndef ENABLE_MULTI_STREAMS
    // mPipeline = gst_parse_launch("rtspsrc location=rtsp://127.0.0.1:8554/test latency=10 ! decodebin ! queue ! videoconvert ! mpph264enc header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=500000 bps= 600000 bps-max=700000 gop=50 ! appsink name=callSink", NULL);
    // mPipeline = gst_parse_launch("rtspsrc location=rtsp://127.0.0.1:8554/test latency=10 ! "videoconvert ! queue ! videoscale ! video/x-raw,width=640,height=480 ! 
    //     "decodebin ! videoconvert ! videoscale ! video/x-raw,width=640,height=480 ! "
    //     "x264enc tune=zerolatency bitrate=600000 key-int-max=50 name=264enc ! "
    //     "appsink name=callSink", NULL);
    std::string pipeline_str = "v4l2src device=" + video_path_ + " ! image/jpeg,width=640,height=480,framerate=25/1 ! "
            "jpegdec ! videoconvert ! mpph264enc header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=500000 bps= 600000 bps-max=700000 gop=50 ! "
            "appsink name=callSink";

    mPipeline = gst_parse_launch(pipeline_str.c_str(), NULL);

#else
    //通话加拉流测试代码
    std::string pipeline_str = "v4l2src device=" + video_path_ + " ! "
            "image/jpeg,width=1920,height=1080,framerate=25/1 ! jpegdec ! videoconvert ! "
            "tee name=t ! queue ! mpph264enc width=640 height=480 header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=500000 bps= 600000 bps-max=700000 gop=50 ! "
            "appsink name=callSink "
            //"t. ! queue ! mpph264enc header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=100000 bps=110000 bps-max=120000 gop=50 ! "
            //"appsink name=cameraSink "
            "t. ! queue ! mpph264enc ! rtspclientsink location=rtsp://127.0.0.1:8554/test latency=10";

    mPipeline = gst_parse_launch(pipeline_str.c_str(), NULL);
#endif

    // mEnc = gst_bin_get_by_name(GST_BIN(mPipeline), "264enc");
    // g_object_set(mEnc,"cabac", false, NULL);
    // g_object_set(mEnc,"dct8x8", false, NULL);
    // g_object_set(mEnc,"bframes", 0, NULL);
    mSink = gst_bin_get_by_name(GST_BIN(mPipeline), "callSink");
    g_object_set(mSink, "emit-signals", TRUE, NULL);
    g_signal_connect(mSink, "new-sample", G_CALLBACK(cb_call_sink_new_sample),
                     reinterpret_cast<void *>(this));

#ifdef ENABLE_MULTI_STREAMS
    mCameraSink = gst_bin_get_by_name(GST_BIN(mPipeline), "cameraSink");
    g_object_set(mCameraSink, "emit-signals", TRUE, NULL);
    g_signal_connect(mCameraSink, "new-sample", G_CALLBACK(cb_camera_sink_new_sample),
                    reinterpret_cast<void *>(this));
#endif

    gst_pipeline_set_auto_flush_bus(GST_PIPELINE(mPipeline), true);
}

void GstV4l2Pipeline::Create_1080p() {
    this->resolution_ = "fhd";

#ifndef ENABLE_MULTI_STREAMS
    std::string pipeline_str = "v4l2src device=" + video_path_ + " ! image/jpeg,width=1920,height=1080,framerate=25/1 ! "
            "jpegdec ! videoconvert ! mpph264enc header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=1500000 bps= 4000000 bps-max=5000000 gop=50 ! "
            "appsink name=callSink";

    mPipeline = gst_parse_launch(pipeline_str.c_str(), NULL);

#else
    //通话加拉流测试代码
    std::string pipeline_str = "v4l2src device=" + video_path_ + " ! "
            "image/jpeg,width=1920,height=1080,framerate=25/1 ! jpegdec ! videoconvert ! "
            "tee name=t ! queue ! mpph265enc width=640 height=480 header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=500000 bps= 600000 bps-max=700000 gop=50 ! "
            "appsink name=callSink "
            //"t. ! queue ! mpph265enc header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=100000 bps=110000 bps-max=120000 gop=50 ! "
            //"appsink name=cameraSink "
            "t. ! queue ! mpph265enc ! rtspclientsink location=rtsp://127.0.0.1:8554/test latency=10";

    mPipeline = gst_parse_launch(pipeline_str.c_str(), NULL);
#endif

    mSink = gst_bin_get_by_name(GST_BIN(mPipeline), "callSink");
    g_object_set(mSink, "emit-signals", TRUE, NULL);
    g_signal_connect(mSink, "new-sample", G_CALLBACK(cb_call_sink_new_sample),
                     reinterpret_cast<void *>(this));

#ifdef ENABLE_MULTI_STREAMS
    mCameraSink = gst_bin_get_by_name(GST_BIN(mPipeline), "cameraSink");
    g_object_set(mCameraSink, "emit-signals", TRUE, NULL);
    g_signal_connect(mCameraSink, "new-sample", G_CALLBACK(cb_camera_sink_new_sample),
                    reinterpret_cast<void *>(this));
#endif

    gst_pipeline_set_auto_flush_bus(GST_PIPELINE(mPipeline), true);
}

void GstV4l2Pipeline::Create_720p() {
    this->resolution_ = "hd";

#ifndef ENABLE_MULTI_STREAMS
    std::string pipeline_str = "v4l2src device=" + video_path_ + " ! image/jpeg,width=1280,height=720,framerate=25/1 ! "
            "jpegdec ! videoconvert ! mpph264enc header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=1500000 bps= 2000000 bps-max=2500000 gop=50 ! "
            "appsink name=callSink";

    mPipeline = gst_parse_launch(pipeline_str.c_str(), NULL);

#else
    //通话加拉流测试代码
    std::string pipeline_str = "v4l2src device=" + video_path_ + " ! "
            "image/jpeg,width=1920,height=1080,framerate=25/1 ! jpegdec ! videoconvert ! "
            "tee name=t ! queue ! mpph265enc width=640 height=480 header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=500000 bps= 600000 bps-max=700000 gop=50 ! "
            "appsink name=callSink "
            //"t. ! queue ! mpph265enc header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=100000 bps=110000 bps-max=120000 gop=50 ! "
            //"appsink name=cameraSink "
            "t. ! queue ! mpph265enc ! rtspclientsink location=rtsp://127.0.0.1:8554/test latency=10";

    mPipeline = gst_parse_launch(pipeline_str.c_str(), NULL);
#endif

    mSink = gst_bin_get_by_name(GST_BIN(mPipeline), "callSink");
    g_object_set(mSink, "emit-signals", TRUE, NULL);
    g_signal_connect(mSink, "new-sample", G_CALLBACK(cb_call_sink_new_sample),
                     reinterpret_cast<void *>(this));

#ifdef ENABLE_MULTI_STREAMS
    mCameraSink = gst_bin_get_by_name(GST_BIN(mPipeline), "cameraSink");
    g_object_set(mCameraSink, "emit-signals", TRUE, NULL);
    g_signal_connect(mCameraSink, "new-sample", G_CALLBACK(cb_camera_sink_new_sample),
                    reinterpret_cast<void *>(this));
#endif

    gst_pipeline_set_auto_flush_bus(GST_PIPELINE(mPipeline), true);
}

void GstV4l2Pipeline::Create265() {
    this->resolution_ = "h265";

#ifndef ENABLE_MULTI_STREAMS
    std::string pipeline_str = "v4l2src device=" + video_path_ + " ! image/jpeg,width=640,height=480,framerate=25/1 ! "
            "jpegdec ! videoconvert ! mpph265enc header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=500000 bps= 600000 bps-max=700000 gop=50 ! "
            "appsink name=callSink";

    mPipeline = gst_parse_launch(pipeline_str.c_str(), NULL);

#else
    //通话加拉流测试代码
    std::string pipeline_str = "v4l2src device=" + video_path_ + " ! "
            "image/jpeg,width=1920,height=1080,framerate=25/1 ! jpegdec ! videoconvert ! "
            "tee name=t ! queue ! mpph265enc width=640 height=480 header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=500000 bps= 600000 bps-max=700000 gop=50 ! "
            "appsink name=callSink "
            //"t. ! queue ! mpph265enc header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=100000 bps=110000 bps-max=120000 gop=50 ! "
            //"appsink name=cameraSink "
            "t. ! queue ! mpph265enc ! rtspclientsink location=rtsp://127.0.0.1:8554/test latency=10";
    mPipeline = gst_parse_launch(pipeline_str.c_str(), NULL);
#endif

    mSink = gst_bin_get_by_name(GST_BIN(mPipeline), "callSink");
    g_object_set(mSink, "emit-signals", TRUE, NULL);
    g_signal_connect(mSink, "new-sample", G_CALLBACK(cb_call_sink_new_sample),
                     reinterpret_cast<void *>(this));

#ifdef ENABLE_MULTI_STREAMS
    mCameraSink = gst_bin_get_by_name(GST_BIN(mPipeline), "cameraSink");
    g_object_set(mCameraSink, "emit-signals", TRUE, NULL);
    g_signal_connect(mCameraSink, "new-sample", G_CALLBACK(cb_camera_sink_new_sample),
                    reinterpret_cast<void *>(this));
#endif

    gst_pipeline_set_auto_flush_bus(GST_PIPELINE(mPipeline), true);
}

bool GstV4l2Pipeline::Start() {
    std::string video_path = this->GetVideoPath();
    if(video_path != this->video_path_) {
        this->video_path_ = video_path;
        this->Destroy();
        if(this->resolution_ == "sd") {
            this->Create();
        } else if(this->resolution_ == "hd") {
            this->Create_720p();
        } else if(this->resolution_ == "fhd") {
            this->Create_1080p();
        } else if(this->resolution_ == "h265") {
            this->Create265();
        }
    }

    mRet = gst_element_set_state(mPipeline, GST_STATE_PLAYING);
    if (mRet == GST_STATE_CHANGE_FAILURE) {
        g_printerr("Unable to set the pipeline to the playing state "
                   "(GST_STATE_PLAYING).\n");
        return false;
    }
    return true;
}

bool GstV4l2Pipeline::Pause() {
    GstState state, pending;
    g_print("StopPipeline called\n");

    if (GST_STATE_CHANGE_ASYNC == gst_element_get_state(mPipeline, &state,
                                                        &pending,
                                                        5 * GST_SECOND / 1000)) {
        g_printerr("Failed to get state of pipeline");
        return false;
    }

    if (state == GST_STATE_PAUSED) {
        return true;
    } else if (state == GST_STATE_PLAYING) {
        gst_element_set_state(mPipeline, GST_STATE_PAUSED);
        gst_element_get_state(mPipeline, &state, &pending, GST_CLOCK_TIME_NONE);
        return true;
    } else {
        g_printerr("Invalid state of pipeline(%d)", GST_STATE_CHANGE_ASYNC);
        return false;
    }
}

bool GstV4l2Pipeline::Resume() {
    GstState state, pending;
    g_print("StartPipeline called\n");

    if (GST_STATE_CHANGE_ASYNC == gst_element_get_state(mPipeline, &state,
                                                        &pending,
                                                        5 * GST_SECOND / 1000)) {
        g_printerr("Failed to get state of pipeline");
        return false;
    }

    if (state == GST_STATE_PLAYING) {
        return true;
    } else if (state == GST_STATE_PAUSED) {
        gst_element_set_state(mPipeline, GST_STATE_PLAYING);
        gst_element_get_state(mPipeline, &state, &pending, GST_CLOCK_TIME_NONE);
        return true;
    } else {
        g_printerr("Invalid state of pipeline(%d)", GST_STATE_CHANGE_ASYNC);
        return false;
    }
}

bool GstV4l2Pipeline::Stop() {
    GstState state, pending;
    g_print("StopPipeline called\n");

    if (GST_STATE_CHANGE_ASYNC == gst_element_get_state(mPipeline, &state,
                                                        &pending,
                                                        5 * GST_SECOND / 1000)) {
        g_printerr("Failed to get state of pipeline");
        return false;
    }
    if (state != GST_STATE_NULL) {
        gst_element_set_state(mPipeline, GST_STATE_NULL);
    }
    return true;
}

void GstV4l2Pipeline::Destroy() {
    if (mPipeline) {
        Stop();
//        gst_element_set_state(mPipeline, GST_STATE_NULL);
        gst_object_unref(mPipeline);
        mPipeline = nullptr;
    }
}

void GstV4l2Pipeline::SetVoipBufferCallback(BufferCallback callback) {
    mVoipBufferCallback = std::move(callback);
}

void GstV4l2Pipeline::SetCameraBufferCallback(BufferCallback callback) {
    mCameraBufferCallback = std::move(callback);
}
