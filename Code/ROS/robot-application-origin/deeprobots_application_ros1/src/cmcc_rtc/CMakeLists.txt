cmake_minimum_required(VERSION 3.0.2)
# SET(CMAKE_C_COMPILER "/usr/bin/gcc")
# SET(CMAKE_CXX_COMPILER "/usr/bin/g++")
project(rtc)

set(SPACE "cn")
set(<PERSON><PERSON><PERSON>Y "xunfei")

add_compile_options(-lc -g -lstdc++ -fPIC -fpermissive -lgcc -Wall 
                    -DWEBRTC_CLOCK_TYPE_REALTIME -DWEBRTC_THREAD_RR 
                    -DWEBRTC_LINUX -DWEBRTC_POSIX -lc -std=c++11 
                    -lgcc_s -lpthread)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
  message_generation
  xunfei_audio
)

find_package(ALSA REQUIRED)
find_package(YAML-cpp REQUIRED)
find_package(PkgConfig)
pkg_search_module(GSTREAMER REQUIRED gstreamer-1.0)

add_message_files(
  FILES
  SIGCEvent.msg
  PCMStream.msg
)

add_service_files(
  FILES
  AssistantAbort.srv
)

generate_messages(
  DEPENDENCIES
  std_msgs  # Or other packages containing msgs
)

catkin_package(
#  INCLUDE_DIRS include
#  LIBRARIES cmcc_rtc_node
  CATKIN_DEPENDS roscpp std_msgs message_runtime
#  DEPENDS system_lib
)

include_directories(
# include
  ${catkin_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/include
  ${CMAKE_CURRENT_SOURCE_DIR}/../xunfei_audio/include
  ${GSTREAMER_INCLUDE_DIRS}
  ${YAML_CPP_INCLUDE_DIRS}
)

if(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64|i386")
    message(STATUS "Detected x86 architecture")
    # 添加 x86 相关的c文件

    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib/x86_64/${SPACE}/)

    if(COMPANY MATCHES "xunfei")
      add_executable(${PROJECT_NAME}_node
          src/rtc.cc
          src/cmcc/cmcc_rtc_node.cc
          src/cmcc/third_party_device.cc
          src/cmcc/third_party_device_factory.cc
          src/cmcc/GstV4l2Pipeline.cpp
          ../xunfei_audio/src/audio/vs_define_trans.cc)
      target_compile_definitions(${PROJECT_NAME}_node PUBLIC XUNFEI)
    elseif(COMPANY MATCHES "baidu")
      add_executable(${PROJECT_NAME}_node
          src/rtc.cc
          src/cmcc/cmcc_rtc_node.cc
          src/cmcc/third_party_device.cc
          src/cmcc/third_party_device_factory.cc
          src/cmcc/GstV4l2Pipeline.cpp)
    endif()

    add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
    # 添加 x86 相关的依赖库
    target_link_libraries(${PROJECT_NAME}_node
      ${catkin_LIBRARIES}
      ${ALSA_LIBRARIES}
      ${GSTREAMER_LIBRARIES}
      ${YAML_CPP_LIBRARIES}
      # ${STATIC_LIBS}
      voipengine_not_include_mbedtls
      mbedtls
      mbedx509
      mbedcrypto
      dl
      pthread
      jsoncpp
    )
    install(TARGETS ${PROJECT_NAME}_node 
      ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
      LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
      RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
    )

elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
    message(STATUS "Detected ARM architecture")
    # 添加 arm 相关的c文件

    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib/aarch64/${SPACE}/)

    if(COMPANY MATCHES "xunfei")
      add_executable(${PROJECT_NAME}_node
          src/rtc.cc
          src/cmcc/cmcc_rtc_node.cc
          src/cmcc/third_party_device.cc
          src/cmcc/third_party_device_factory.cc
          src/cmcc/GstV4l2Pipeline.cpp
          ../xunfei_audio/src/audio/vs_define_trans.cc)
      target_compile_definitions(${PROJECT_NAME}_node PUBLIC XUNFEI)
    elseif(COMPANY MATCHES "baidu")
      add_executable(${PROJECT_NAME}_node
          src/rtc.cc
          src/cmcc/cmcc_rtc_node.cc
          src/cmcc/third_party_device.cc
          src/cmcc/third_party_device_factory.cc
          src/cmcc/GstV4l2Pipeline.cpp)
    endif()

    add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
    # 添加 arm 相关的依赖库
    target_link_libraries(${PROJECT_NAME}_node
      ${catkin_LIBRARIES}
      ${ALSA_LIBRARIES}
      ${GSTREAMER_LIBRARIES}
      ${YAML_CPP_LIBRARIES}
      # ${STATIC_LIBS}
      voipengine_not_include_mbedtls
      mbedtls
      mbedx509
      mbedcrypto
      dl
      pthread
      jsoncpp
    )
    install(TARGETS ${PROJECT_NAME}_node 
    ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
    )
endif()
