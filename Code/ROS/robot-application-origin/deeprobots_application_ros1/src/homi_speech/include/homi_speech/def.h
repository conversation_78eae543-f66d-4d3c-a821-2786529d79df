#ifndef __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_DEF_H_
#define __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_DEF_H_

//capture
#define PARAM_ALSA_CAPTURE_SH_NAME       "alsa_capture_sh_name"
#define PARAM_ALSA_CAPTURE_SH_PARAM      "alsa_capture_sh_param"
#define PARAM_ALSACAPTURE_CHANNEL        "alsa_capture_channel"

//playback
#define PARAM_ALSA_PLAYBACK_SH_NAME       "alsa_playback_sh_name"
#define PARAM_ALSA_PLAYBACK_SH_PARAM      "alsa_playback_sh_param"

//topic
#define PARAM_PCM_STREAM_TOPIC      "pcm_stream_topic"
#define PARAM_PCM_STREAM_WAKEUP_TOPIC  "pcm_stream_wakeup_topic"
#define PARAM_SPEECH_SIGC_EVENT_TOPIC  "sigc_event_topic"
#define PARAM_SPEECH_SIGC_DATA_SERVICE  "sigc_data_service"
#define PARAM_SPEECH_ASSISTANT_CTRL_SERVICE  "speech_assistant_ctrl_service"
#define PARAM_SPEECH_ASSISTANT_STATUS_TOPIC  "speech_assistant_status_topic"
#define PARAM_SPEECH_UPLOAD_IMAGE_URL_SERVICE  "speech_upload_image_url_service"
#define PARAM_HELPER_ASSISTANT_ABORT_SERVICE  "helper_assistant_abort_service"
#define PARAM_HELPER_ASSISTANT_QUIET_SERVICE  "helper_assistant_quiet_service"
#define PARAM_HELPER_ASSISTANT_TAKEPHOTO_SERVICE  "helper_assistant_takephoto_service"
#define PARAM_HELPER_ASSISTANT_SPEECH_TEXT_SERVICE  "helper_assistant_speech_text_service"
#define PARAM_SPEECH_IOT_CONTROL_SERVICE  "speech_iot_control_service"

#define PARAM_SPEECH_ROUND1WAIT_MS   "sppech_round1_waitMs"
#define PARAM_SPEECH_ROUNDnWAIT_MS   "sppech_roundn_waitMs"

#define PARAM_SPEECH_SDK_INITPARAM       "sdk_init_param"
#define PARAM_SPEECH_SDK_LOGFILE         "sdk_log_file"
#define PARAM_SPPECH_SDK_LOGLEVEL        "sdk_log_level"

#define PARAM_RES_XF_MLP_PATH  "resXF_mlp_path"
#define PARAM_RES_XF_KEYWORD_MAIN_PATH "resXF_keyword_main_path"
#define PARAM_RES_XF_KEYWORD_MINGLINGCI_PATH "resXF_keyword_minglingci_path"

#define PARAM_OFFLINE_ASR_ENGINE_CONFIG "offline_asr_engine_config"
#define PARAM_OFFLINE_PARSE_RULE_CONFIG "offline_parse_rule_config"
#define PARAM_SPEECH_RESSOURCE_FILE_CONFIG "speech_resourse_file_config"

#define PARAM_SPEECH_DISPLAY_WAKEUP_SH_FILE "speech_display_wakeup_sh_file"
#define PARAM_SPEECH_DISPLAY_IDLE_SH_FILE   "speech_display_idle_sh_file"





#define DIR_SEPARATOR       '/'
#define DIR_SEPARATOR_STR   "/"
#define __FILENAME__  (strrchr(DIR_SEPARATOR_STR __FILE__, DIR_SEPARATOR) + 1)

#endif  // __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_DEF_H_
