#ifndef __ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_HOMI_EXCEPTION_H_
#define __ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_HOMI_EXCEPTION_H_

#include <exception>
#include <string>
#include <cstring>
#include <system_error>
#define __HE_FILENAME__  (strrchr("/" __FILE__, '/') + 1)
namespace homi_speech
{

    // 自定义异常类
    class exception : public std::exception {
    public:
        virtual ~exception()
        {
            
        }
        // 构造函数接受一个错误消息字符串作为参数
        exception(const std::string &msg,const std::error_code &__ec)
            :  _code(__ec),_msg(msg) 
        {
            _msg = _msg + " " + _code.message() + "\n";
        }

        // 重写what()方法，返回错误消息
        virtual const char* what() const noexcept override {
            return _msg.c_str();
        }
        const std::error_code&
        code() const noexcept { return _code; }
        private:
        const std::error_code _code;
        std::string _msg; // 存储错误消息
        
    };
    
}

#define THROW_HOMI_E(err) THROW_HOMI_EE(err,"")
#define THROW_HOMI_EE(err,ss) do { std::string s(ss); s =s+ "\t"+ __HE_FILENAME__+":"+std::to_string(__LINE__);\
     throw homi_speech::exception(s,err); }while(0)
#define THROW_HOMI_ES(err,format,...) THROW_HOMI_E(err)

#endif  // __ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_HOMI_EXCEPTION_H_
