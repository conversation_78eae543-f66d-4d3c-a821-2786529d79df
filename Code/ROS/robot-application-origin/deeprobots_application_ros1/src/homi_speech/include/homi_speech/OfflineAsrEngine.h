#ifndef __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_SRC_OFFLINEASRENGINE_H_
#define __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_SRC_OFFLINEASRENGINE_H_

#include "homi_sdk/SimpleSpeechApp.h"
#include <mutex>
#include <condition_variable>
namespace homi::app 
{
    class IOffineAsrEngine
    {
    public:
        virtual std::shared_ptr<IAudioStreamOutput> createAudioStreamOutput(const std::string &eventId,std::function<void(const std::string & eventId, const int ResSubType, const std::string & instruction)> callback) = 0;
        virtual ~IOffineAsrEngine() = default;
    };
    std::shared_ptr<IOffineAsrEngine> createOfflineAsrEngine(const std::string &aiserJson,const std::string &parseRule);
}


#endif  // __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_SRC_OFFLINEASRENGINE_H_
