#ifndef __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_INPUTSTREAM_H_
#define __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_INPUTSTREAM_H_
#include <queue>
#include <vector>
#include <memory>
#include <chrono>
#include <condition_variable>
#include <mutex>
namespace homi_speech
{
    struct InputStreamData
    {
        uint64_t ms;
        std::vector<unsigned char> data;
    };
    class InputStream
    {
    private:
        // 禁止拷贝构造函数和赋值运算符重载，防止复制对象
        InputStream()
        {

        }
        InputStream(const InputStream&) = delete;
        InputStream& operator=(const InputStream&) = delete;
        std::queue<std::shared_ptr<InputStreamData>> _queue;
        std::mutex _mtx;
        std::condition_variable _cv;
    public:
        static InputStream& getInstance() 
        {
            // 在首次调用时初始化静态局部变量，确保线程安全
            static InputStream _instance;
            return _instance;
        }
        void push(const std::shared_ptr<InputStreamData> &e)
        {
            std::unique_lock<std::mutex> lock(_mtx);
            if(e==nullptr) return ;
            _queue.push(e);
            if(_queue.size()>20)
            {
                _queue.pop();
            }
            _cv.notify_all();
        }
        template<typename _Rep, typename _Period>
        std::shared_ptr<InputStreamData> pop(const std::chrono::duration<_Rep, _Period>& rtime)
        {
            std::unique_lock<std::mutex> lock(_mtx);
            _cv.wait_for(lock,rtime,[this](){ return !_queue.empty();});
            if(_queue.empty())
                return nullptr;
            auto tmp = _queue.front();
            _queue.pop();
            return tmp;
        }
    };
}
#endif  // __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_INPUTSTREAM_H_
