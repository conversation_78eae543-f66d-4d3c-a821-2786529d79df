<launch>
    <param name="offline_asr_engine_config" value='
            {
                "aiesr": {
                    "MLP_XN_0": "$(find homi_speech)/launch/res/res_cn/esr_xn.bin",
                    "MLP_VAD_0": "$(find homi_speech)/launch/res/res_cn/esr_evad.bin",
                    "WFST_0": "$(find homi_speech)/launch/res/res_cn/esr_wfst.bin",
                    "FSA_0": "$(find homi_speech)/launch/res/AnswerCall.txt"
                }
            }' />
    <param name="offline_parse_rule_config" value='{
    "jumpforward": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "jumpForward"
            }
        },
        "match": [
            "jumpforward"
        ],
        "resSubType": 1
    },
    "roll": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "turnOver"
            }
        },
        "match": [
            "roll"
        ],
        "resSubType": 1
    },
    "twistjump": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "twistJump"
            }
        },
        "match": [
            "twistjump"
        ],
        "resSubType": 1
    },
    "standup": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "standUp"
            }
        },
        "match": [
            "standup"
        ],
        "resSubType": 1
    },
    "sitdown": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "sitDown"
            }
        },
        "match": [
            "sitdown"
        ],
        "resSubType": 1
    },
    "bixin": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "fingerHeart"
            }
        },
        "match": [
            "bixin"
        ],
        "resSubType": 1
    },
    "compilation": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "makeBow"
            }
        },
        "match": [
            "compilation"
        ],
        "resSubType": 1
    },
    "openfollow": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "followMe",
                "actionArgument": "on"
            }
        },
        "match": [
            "openfollow"
        ],
        "resSubType": 1
    },
    "closefollow": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "followMe",
                "actionArgument": "off"
            }
        },
        "match": [
            "closefollow"
        ],
        "resSubType": 1
    },
    "openterrain": {
        "online": {
            "domain": "DEVICE_PROPERTIES",
            "event": "properties_write",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "properties": {
                    "intelligent": "on"
                }
            }
        },
        "match": [
            "openterrain"
        ],
        "resSubType": 1
    },
    "closeterrain": {
        "online": {
            "domain": "DEVICE_PROPERTIES",
            "event": "properties_write",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "properties": {
                    "intelligent": "off"
                }
            }
        },
        "match": [
            "closeterrain"
        ],
        "resSubType": 1
    },
    "takeCourier":{
        "online":{
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "move_points",
            "eventId": "xxx",
            "seq": "xxx",
            "body": {                   
                "event":"fetchExpress",   
                "points":[
                    {   
                        "x":1.2,  
                        "y":2.3,
                        "angle":1
                    }
                ]
            }   
        },
        "match":[
            "takeCourier"
        ],
        "resSubType": 4
    },
    "courier": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "move_points",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "event": "deliverExpress",
                "points": [
                    {
                        "x": 1.2,
                        "y": 2.3,
                        "angle": 1
                    }
                ]
            }
        },
        "match": [
            "courier"
        ],
        "resSubType": 2
    },
    "takepictures": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "event": "takePhotos",
                "points": [
                    {
                        "x": 1.2,
                        "y": 2.3,
                        "angle": 1
                    }
                ]
            }
        },
        "match": [
            "takepictures"
        ],
        "resSubType": 3
    },
    "liedown": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "getDown"
            }
        },
        "match": [
            "liedown"
        ],
        "resSubType": 1
    },
    "sayHello": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "greeting"
            }
        },
        "match": [
            "sayHello"
        ],
        "resSubType": 1
    },
    "greet": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "greeting"
            }
        },
        "match": [
            "greet"
        ],
        "resSubType": 1
    },    
    "twistbody": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "twistBody"
            }
        },
        "match": [
            "twistbody"
        ],
        "resSubType": 1
    },
    "backflip": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "backflip"
            }
        },
        "match": [
            "backflip"
        ],
        "resSubType": 1
    },
    "shakeit": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "shakebody"
            }
        },
        "match": [
            "shakeit"
        ],
        "resSubType": 1
    },
    "twistedhisbuttoc": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_action",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "actionType": "motorSkill",
                "actionArgument": "twistAss"
            }
        },
        "match": [
            "twistedhisbuttoc"
        ],
        "resSubType": 1
    },
    "moveFront": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_move",
            "eventId": "speech_1727581212519",
            "seq": 0,
            "response": false,
            "body": {
                "direction": {
                    "x": 5
                },
                "actionType": 2
            }
        },
        "match": [
            [
                "moveFront",
                "number",
                "unit"
            ],
            [
                "moveFront"
            ],
            [
                "moveFront",
                "little"
            ]
        ],
        "resSubType": 1
    },
    "moveBack": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_move",
            "eventId": "speech_1727581212519",
            "seq": 0,
            "response": false,
            "body": {
                "direction": {
                    "x": -5
                },
                "actionType": 2
            }
        },
        "match": [
            [
                "moveBack",
                "number",
                "unit"
            ],
            [
                "moveBack"
            ],
            [
                "moveBack",
                "little"
            ]
        ],
        "resSubType": 1
    },
    "movewLeft": {
        "online": {
            "deviceId": "6361752000000011",
            "domain": "DEVICE_ABILITY",
            "event": "robot_move",
            "eventId": "speech_1727581212535",
            "seq": 0,
            "response": false,
            "body": {
                "direction": {
                    "y": 5
                },
                "actionType": 2
            }
        },
        "match": [
            [
                "movewLeft",
                "number",
                "unit"
            ],
            [
                "movewLeft"
            ],
            [
                "movewLeft",
                "little"
            ]
        ],
        "resSubType": 1
    },
    "moveRight": {
        "online": {
            "deviceId": "6361752000000011",
            "domain": "DEVICE_ABILITY",
            "event": "robot_move",
            "eventId": "speech_1727581212535",
            "seq": 0,
            "response": false,
            "body": {
                "direction": {
                    "y": -5
                },
                "actionType": 2
            }
        },
        "match": [
            [
                "moveRight",
                "number",
                "unit"
            ],
            [
                "moveRight"
            ],
            [
                "moveRight",
                "little"
            ]
        ],
        "resSubType": 1
    },
    "turnBack": {
        "online": {
            "deviceId": "6361752000000011",
            "domain": "DEVICE_ABILITY",
            "event": "robot_move",
            "eventId": "speech_1727581212565",
            "seq": 0,
            "response": false,
            "body": {
                "direction": {
                    "yaw": -180
                },
                "actionType": 2
            }
        },
        "match": [
            [
                "turnBack",
                "number",
                "unit"
            ],
            [
                "turnBack"
            ],
            [
                "turnBack",
                "little"
            ]
        ],
        "resSubType": 1
    },
    "turnFront": {
        "online": {
            "deviceId": "6361752000000011",
            "domain": "DEVICE_ABILITY",
            "event": "robot_move",
            "eventId": "speech_1727581212565",
            "seq": 0,
            "response": false,
            "body": {
                "direction": {
                    "yaw": 180
                },
                "actionType": 2
            }
        },
        "match": [
            [
                "turnFront",
                "number",
                "unit"
            ],
            [
                "turnFront"
            ],
            [
                "turnFront",
                "little"
            ]
        ],
        "resSubType": 1
    },
    "turnLeft": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_move",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "direction": {
                    "yaw": -90
                },
                "actionType": 2
            }
        },
        "match": [
            [
                "turnLeft",
                "number",
                "unit"
            ],
            [
                "turnLeft"
            ],
            [
                "turnLeft",
                "little"
            ]
        ],
        "resSubType": 1
    },
    "turnRight": {
        "online": {
            "deviceId": "xxxx",
            "domain": "DEVICE_ABILITY",
            "event": "robot_move",
            "eventId": "随意",
            "seq": 0,
            "body": {
                "direction": {
                    "yaw": 90
                },
                "actionType": 2
            }
        },
        "match": [
            [
                "turnRight",
                "number",
                "unit"
            ],
            [
                "turnRight"
            ],
            [
                "turnRight",
                "little"
            ]
        ],
        "resSubType": 1
    }
            }' />
</launch>