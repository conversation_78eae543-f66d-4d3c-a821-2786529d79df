<launch>
    <param name="speech_resourse_file_config" value='{
                "Wakeup":[
                 "$(find homi_speech)/launch/res/wakeup01.wav"
                 ,"$(find homi_speech)/launch/res/wakeup02.wav"   
                 ,"$(find homi_speech)/launch/res/wakeup03.wav"                
                ]
                ,"NetworkError":[
                 "$(find homi_speech)/launch/res/noresponse01.wav"    
                ,"$(find homi_speech)/launch/res/noresponse02.wav"               
                ]
                ,"DeviceInputError":[
                 "$(find homi_speech)/launch/res/noresponse01.wav"    
                ,"$(find homi_speech)/launch/res/noresponse02.wav"                
                ]
                ,"offlineInstructionError":[
                 "$(find homi_speech)/launch/res/noresponse01.wav"    
                ,"$(find homi_speech)/launch/res/noresponse02.wav"               
                ]
                ,"TimeOut":[
                 "$(find homi_speech)/launch/res/noresponse01.wav"    
                ,"$(find homi_speech)/launch/res/noresponse02.wav"                 
                ]
                ,"Inquiry":[
                 "$(find homi_speech)/launch/res/takephoto_isready.wav"                
                ]
                ,"offlineInstruction":[
                 "$(find homi_speech)/launch/res/instruction_ok.wav"
                 ,"$(find homi_speech)/launch/res/instruction_express_delivery.wav"
                 ,"$(find homi_speech)/launch/res/instruction_takephoto.wav"
                 ,"$(find homi_speech)/launch/res/instruction_fetch_express.wav"               
                ]
            }' />
</launch>