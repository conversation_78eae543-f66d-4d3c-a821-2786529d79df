<launch>
        <param name="sdk_init_param" value='
                        {
                        "url":"https://business.homibot.komect.com:9443/robot/business/api/device/client/connect/url",
                        "config":{
                        "sn":"6361752000000011",
                        "deviceId":"6361752000000011",
                        "macId":"55:EF:45:97:73:2E",	
                        "deviceType":"2320647",	
                        "firmwareVersion":"1.0.1",
                        "innerType":1
                        },
                        "speech":{
                        "sampleRate":"16000",
                        "voiceId":"x4_lingxiaowan_boy",
                        "domain":"VOICE_INTERACTION_DOG",
                        "textDomain":"VOICE_INTERACTION_XF_SEPARATE_TTS",
                        "encodingConfig":{
                            "up":{
                            "type":"pcm",
                            "sampleRate":"16000"
                            },
                            "down":{
                            "type":"pcm",
                            "sampleRate":"16000"
                            }
                        }
                        },
                        "authorization":"A4Ijr2089ai4drWGxZoVYRENuEXWC1Ry6E4MgmwLfDcu1OE+FZpqZFFdsomuxcCIcA7i0DXupzUlR7zMIF8bFFXoBIO7xYvHbKYRZcyXBwvxKw3WGBuXjSYZtqh++r+h1Htwb+UCJhRRVSeQ+8xZmN+FnC3WKgVjBasowvrKSvh9IN2PsuqhKZJzzjFwq3QbaI134qpgxqb193ei2WAZyBWbvcoB4LHs7M1SCOyuvXefcPHLHUZZfnatbmMFOQ9HKVg3c+jtsczVPnup6vMXD3U3iv0PvW/S2sT/fmV7A3dHnT7T1cY58BZ4C+Fpvp6893lgcDllv5PWNkzhR75++Q=="
                        }
                        '/>
        <param name="sdk_log_level" value="debug"/>
        <param name="sdk_log_file" value="/var/tmp/speechcore"/>

        <param name="sppech_round1_waitMs" value= "10000" />
        <param name="sppech_roundn_waitMs" value= "10000"/>

        <param name="pcm_stream_topic" value="/audio_node/pcm_stream" />
        <param name="pcm_stream_wakeup_topic" value="/audio_node/wakeup_event" />
        <param name="alsa_playback_sh_param" value='
                {
                        "name":[
                        "USB Audio Device",
                        "default"
                        ]
                }' />
        <param name="alsa_playback_sh_name" value="$(find homi_speech)/scripts/get_playback_card.sh" /> 
        <param name="speech_display_wakeup_sh_file" value="$(find homi_speech)/scripts/display_assistant_wakeup.sh" /> 
        <param name="speech_display_idle_sh_file" value="$(find homi_speech)/scripts/display_assistant_idle.sh" /> 

        <include file="$(find homi_speech)/launch/config/offline_asr.launch"/>
        <include file="$(find homi_speech)/launch/config/speech_res.launch"/>
</launch>