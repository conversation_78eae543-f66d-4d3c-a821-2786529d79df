<launch>
        <!-- <env name="ROSCONSOLE_CONFIG_FILE" value='$(find homi_speech)/launch/config/rosconsole.config'/> -->
        <env name="ROSCONSOLE_FORMAT" value='[${severity}][${time:%Y-%m-%d %H:%M:%S:%s}][${node}][${thread}][${logger}:${function}:${line}] ${message}'/>
        <arg name="group_name" default="homi_speech"/>
        <group ns="$(arg group_name)">
                <include file="$(find homi_speech)/launch/config/param.launch"/>
                <node name="speech_core" pkg="homi_speech" type="speech_core" output="screen" respawn="true" respawn_delay="2"/>
                <node name="upload_image" pkg="homi_speech" type="upload_image.py" output="screen" respawn="true" respawn_delay="2"/>
                <node name="helper" pkg="homi_speech" type="helper" output="screen" respawn="true" respawn_delay="2"/>
        </group>
</launch>