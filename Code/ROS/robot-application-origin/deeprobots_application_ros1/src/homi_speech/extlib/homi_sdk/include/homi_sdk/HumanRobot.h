#ifndef __SRC_HOMI_INCLUDE_HOMI_HUMANROBOT_H_
#define __SRC_HOMI_INCLUDE_HOMI_HUMANROBOT_H_

#include <string>
#include <functional>
namespace homi
{
    int registerHumanRotbotTask(const std::string &event,const std::function<void(const std::string &json)> &func);
    int sendHumanRotbotMsgOOB(const std::string &event,const std::string &body,bool response=false,unsigned int milliseconds=0);
    int sendHumanRotbotMsg(const std::string &event,const std::string &body,bool response=false);
}
#endif  // __SRC_HOMI_INCLUDE_HOMI_HUMANROBOT_H_
