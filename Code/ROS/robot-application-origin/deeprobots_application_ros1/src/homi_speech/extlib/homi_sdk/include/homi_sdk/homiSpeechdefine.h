#ifndef Homi_DEFINE_H
#define Homi_DEFINE_H

#define MAX_LEN_24  24
#define MAX_LEN_32  32
#define MAX_LEN_64  64
#define MAX_LEN_128  128
#define MAX_LEN_256  256
#define MAX_LEN_1024  1024


//基础类型定义
#if !defined(__cplusplus)
#define bool char
#define true 1
#define false 0
#endif


typedef char Homi_bool;
typedef char Homi_int8;
typedef unsigned char Homi_uint8;
typedef unsigned int Homi_uint32;
typedef int Homi_int32;
typedef unsigned long long  Homi_uint64;
typedef long long Homi_int64;
typedef char  Homi_char;
typedef unsigned char  Homi_uchar;
typedef void Homi_void;
typedef float  Homi_float;
typedef unsigned short Homi_ushort;


typedef enum
{
    Homi_PCM          = 0,
} HomiAUDIOIN_TYPE;

typedef struct
{
    Homi_uint32 codec;         //HomiAUDIOIN_TYPE
    Homi_uint32 samplesRate;
    Homi_uint32 bitWidth;
    Homi_uint32 volume;
    Homi_uint32 priority;      //the biger ,the higher
} HomiAudioINDataFormat;

typedef enum
{
    Homi_WAKE_UP          = 0,  //唤醒
	Homi_START_RECORD     ,	    //开始录音
	Homi_RECORDING        ,     //录音中
	Homi_STOP_RECORD      ,     //录音结束
	Homi_RECOGNIZING      ,     //识别中
    Homi_MEDIUM_DISCONNECT,     //媒体关闭
    Homi_MEDIUM_VAD       ,     //端侧VAD上报
    Homi_MUSIC_REPAET_NEXT,     //顺序播放下一首
} HomiSPEECH_STATE;

enum
{
	/* Generic Error defines */
    Homi_RET_COMMON_ERROR                = -1,    //通用错误，初始化失败
	Homi_RET_SUCCESS					 = 0,     //初始化成功

    Homi_RET_CMCCINIT_ERROR              = 3100,  //CMCC初始化失败
    Homi_RET_CALLBACK_ERROR              ,        //注册回调函数失败
    Homi_RET_BADPARAMETER                ,        //入参校验失败
    Homi_RET_CMD_SEND_ERROR              ,        //信令通道发送失败
 
    Homi_RET_TOKEN_NULL                  = 3200,  //TOKEN为空
    Homi_RET_MEIDUM_CONMECT_ERROR        ,        //媒体通道连接失败
    Homi_RET_MEIDUM_DISCONMECT_ERROR     ,        //媒体通道关闭失败
    Homi_RET_MUSIC_REPAET_NEXT_ERROR     ,        //顺序播放下一首失败
    Homi_RET_SET_STATUS_ERROR            ,        //暂无该状态选项

    Homi_RET_PUSH_MEIDUM_ERROR           = 3300,  //推音频流到云端失败

    Homi_RET_GET_VERSION_ERROR           = 3400,  //获取版本号失败
};


#endif
