#ifndef __SRC_HOMI_INCLUDE_HOMI_OPENAPI_H_
#define __SRC_HOMI_INCLUDE_HOMI_OPENAPI_H_

#include <string>
#include <memory>
#include <functional>
#include "Logger.h"
namespace homi{
    
    class IOpenAPI
    {
    public:
        virtual int Init(const std::string &params,homi::LogLevel level=LogLevel::LOG_LEVEL_INFO,const std::string &filepath="") = 0;
        virtual std::string getVersionInfo(void) =0;
        virtual int registerOTACallback(std::function<void(const std::string &fromVersion,
                                                           const std::string &toVersion,
                                                           const std::string &url,
                                                           const std::string &MD5)> func) = 0;
        virtual int registerLogCallback(std::function<void(const std::string &url)> func) = 0;
        virtual int triggerLog(long long logSize) = 0;
    };
    IOpenAPI * getOpenAPI(void);
}
#endif  // __SRC_HOMI_INCLUDE_HOMI_OPENAPI_H_
