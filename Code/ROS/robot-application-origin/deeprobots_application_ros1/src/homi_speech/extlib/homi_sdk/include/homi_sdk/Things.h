#ifndef __SRC_HOMI_INCLUDE_HOMI_THINGS_H_
#define __SRC_HOMI_INCLUDE_HOMI_THINGS_H_
#include <string>
#include <map>
#include <functional>
namespace homi{
using ThingsCallback_t = std::function<void(const std::string &input,std::string &output)>;
int registerPropertiesReadCallback(const std::map<std::string,ThingsCallback_t> &conf);
int registerPropertiesWriteCallback(const std::map<std::string,ThingsCallback_t> &conf);
int registerServiceCallback(const std::map<std::string,ThingsCallback_t> &conf);
int sendEvent(const std::string &event,const std::string &output);
}
#endif  // __SRC_HOMI_INCLUDE_HOMI_THINGS_H_
