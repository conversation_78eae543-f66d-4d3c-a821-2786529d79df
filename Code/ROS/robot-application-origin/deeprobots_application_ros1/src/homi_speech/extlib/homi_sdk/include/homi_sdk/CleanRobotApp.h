#ifndef __SRC_HOMI_INCLUDE_HOMI_CLEANROBOTAPP_H_
#define __SRC_HOMI_INCLUDE_HOMI_CLEANROBOTAPP_H_
#include <map>
#include <string>
#include <functional>
namespace homi{
    
    class ICleanRobot
    {
    public:
        using ThingsCallback_t = std::function<void(const std::string &input,std::string &output)>;
        virtual int registerPropertiesReadCallback(const std::map<std::string,ThingsCallback_t> &conf)=0;
        virtual int registerPropertiesWriteCallback(const std::map<std::string,ThingsCallback_t> &conf)=0;
        virtual int registerServiceCallback(const std::map<std::string,ThingsCallback_t> &conf)=0;
        virtual int sendEvent(const std::string &event,const std::string &output)=0;
        virtual void updateOSS(const std::string &url,const std::string &data,const std::string &remark="") = 0;
        virtual ~ICleanRobot(){};
    };
    ICleanRobot * getCleanRobot(void);
}

#endif  // __SRC_HOMI_INCLUDE_HOMI_CLEANROBOTAPP_H_
