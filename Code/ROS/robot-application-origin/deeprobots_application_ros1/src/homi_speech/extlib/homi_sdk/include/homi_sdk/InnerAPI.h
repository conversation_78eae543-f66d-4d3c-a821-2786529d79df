#ifndef __SRC_HOMI_INCLUDE_EXT_INNERAPI_H_
#define __SRC_HOMI_INCLUDE_EXT_INNERAPI_H_
#include <functional>
#include <memory>
namespace homi::inner
{
    int regeisterUserEvent(const std::string &domain,const std::string &event, const std::function<void(const std::string &json)> &func);
    int registerDefaultEvent(const std::function<void(const std::string &json)> &func);
    int sendEventOOB(const std::shared_ptr<std::string> &dataPtr,const bool isBinary=false,unsigned int milliseconds = 0);
    int sendEventOOB(const std::string &domain,const std::string &event,const bool response,const nlohmann::json &body,unsigned int milliseconds =0);
    int sendEvent(const std::shared_ptr<std::string> &dataPtr,const bool isBinary=false);
    int sendEvent(const std::string &domain,const std::string &event,const bool response,const nlohmann::json &body);
}

#endif  // __SRC_HOMI_INCLUDE_EXT_INNERAPI_H_
