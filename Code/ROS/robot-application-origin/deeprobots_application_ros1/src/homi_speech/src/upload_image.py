#!/usr/bin/env python3
import rospy
import requests
from homi_speech.srv import UploadImageUrl, UploadImageUrlResponse
from homi_speech_interface.srv import UploadImage, UploadImageResponse
import os
def upload_image(fileName,url):
    with open(fileName, 'rb') as f:
        files = {'file': (fileName, f)}
        r = requests.post(url, files=files)
        return r.text

def upload_image_service(req):
    rospy.loginfo("Upload image service called")
    try:
        filesize = os.path.getsize(req.fileName)
        rospy.loginfo("Image fileName: " + req.fileName )
        rospy.wait_for_service('/homi_speech/speech_upload_image_url_service')  
        speech_upload_image_url_request = rospy.ServiceProxy('/homi_speech/speech_upload_image_url_service', UploadImageUrl)
        response = speech_upload_image_url_request(filesize)
        print("ErrorCode:", response.errorCode)
        print("URL:", response.url)
        if response.errorCode != 0 or response.url == "":
            return UploadImageResponse(-2)
        rospy.loginfo("Image url: " + response.url)
        res = upload_image(req.fileName,response.url)
        rospy.loginfo("Image res: " + res)
    except rospy.ServiceException as e:
        rospy.loginfo("Service call failed: %s"%e)
        return UploadImageResponse(-1)
    except Exception as e:
        rospy.loginfo("Upload image failed: %s"%e)
        return UploadImageResponse(-1)
    return UploadImageResponse(0)
def main():
    rospy.init_node('upload_image')
    rospy.loginfo("Upload image service started")
    rospy.Service('/homi_speech/upload_image_service', UploadImage, upload_image_service)
    rospy.spin()

if __name__ == '__main__':
    main()