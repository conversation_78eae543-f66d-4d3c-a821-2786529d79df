#include <ros/ros.h>
#include <homi_speech_interface/PCMStream.h>
#include <homi_speech_interface/AssistantCtrl.h>
#include <homi_speech/InputStream.h>
#include <homi_speech/def.h>
#include <homi_sdk/homiWakeOpenAPI.h>
#include <string>
#include <thread>
#include <vector>

#define SPEECH_SIZE     (320*16)
ros::Publisher _gWakeupPUB;
int Homi_WakeUpResult(char *param, char *pUserInfo)
{
    ROS_INFO_NAMED(__FILENAME__,"homiWake: wakeup:ivw param=%s user_param=%s \n", param, pUserInfo);
    homi_speech_interface::AssistantCtrl msg;
    msg.request.isNormal = true;
    msg.request.start = true;
    msg.request.stop = true;
    msg.request.startWaitMS = 5000;
    msg.request.stopWaitMS = 5000;
    msg.request.mutipleWheels = true;
    msg.request.inquiryText = "";
    msg.request.notifyUserAbort = true;
    ros::NodeHandle nh;
    std::string serviceName;
    ros::ServiceClient client = nh.serviceClient<homi_speech_interface::AssistantCtrl>(PARAM_SPEECH_ASSISTANT_CTRL_SERVICE);
    if(client.call(msg))
    {
        ROS_INFO_NAMED(__FILENAME__,"homiWake: wakeup:ivw call assistant service success %d", msg.response.errorCode);
    } 
    else
    {
        ROS_INFO_NAMED(__FILENAME__,"homiWake: wakeup:ivw call assistant service failed");
    }
    return 0;
}
void pcmStreamCallback(const homi_speech_interface::PCMStreamConstPtr & ptr)
{
    if(ptr==nullptr) return ;
    homi_speech::InputStreamData data;
    data.ms = ptr->ts;
    data.data = ptr->data;
    homi_speech::InputStream::getInstance().push(std::make_shared<homi_speech::InputStreamData>(data));
}
int main(int argc, char *argv[])
{
    setlocale(LC_ALL, "");
    ros::init(argc, argv, "wake_up_xf");
    ros::NodeHandle nh;
    ROS_INFO_NAMED(__FILENAME__,"wake_up_xf start up");
    homiWakeCallBackFunList callBackFunList;
    callBackFunList.Homi_WakeUpResult = Homi_WakeUpResult;
    std::string mlpPath;
    std::string keywordMainPath;
    std::string keywordMinglingci;
    std::string pcm_stream_topic;
    if(!nh.getParam(PARAM_RES_XF_MLP_PATH,mlpPath)
        ||!nh.getParam(PARAM_RES_XF_KEYWORD_MAIN_PATH,keywordMainPath)
        ||!nh.getParam(PARAM_RES_XF_KEYWORD_MINGLINGCI_PATH,keywordMinglingci)
        ||!nh.getParam(PARAM_PCM_STREAM_TOPIC,pcm_stream_topic))
    {
        ROS_ERROR_NAMED(__FILENAME__,"read param error ,exit");
        return -1;
    }
    ROS_INFO_NAMED(__FILENAME__,"mlpPath=%s keywordMainPath=%s keywordMinglingci=%s",
                        mlpPath.c_str(),keywordMainPath.c_str(),keywordMinglingci.c_str());
    auto ret = homiWakeInit(&callBackFunList, (char *)mlpPath.c_str(), (char *)keywordMainPath.c_str(),(char *)keywordMinglingci.c_str()); 
    if(ret)
    {
        ROS_ERROR_NAMED(__FILENAME__,"homiWakeInit failed , ret = %d\n",ret);
        return -1;
    }

    ret = homiStartWake();
    if(ret)
    {
        ROS_ERROR_NAMED(__FILENAME__,"homiStartWake failed , ret = %d\n",ret);
        return -1;
    }
    long long lcount = 0;
    ros::Timer timer = nh.createTimer(ros::Duration(5),[&lcount](const ros::TimerEvent & e){
        lcount++;
        ROS_INFO_NAMED(__FILENAME__,"idle ,run count =%lld s ...",(lcount*5));
    });
    bool stop=false;
    auto mythread = std::thread([&stop](){
        unsigned int count=0;
        std::vector<unsigned char> speechBuf;
        while(!stop)
        {
            auto dataPtr = homi_speech::InputStream::getInstance().pop(std::chrono::seconds(2));
            if(dataPtr==nullptr)
            {
                continue;
            }
            auto readSize = dataPtr->data.size();
            auto buffer = dataPtr->data.data();
            int ret=0;
            count++;
            auto now = std::chrono::system_clock::now();
            if(readSize>0)
            {
                speechBuf.insert(speechBuf.end(),buffer,buffer+readSize);
                // if(readSize%2==1)
                // {
                //     readSize-=1;
                // }
                // ret = homiWriteWakeUpPcm((char *)buffer,readSize);
            }
            while(speechBuf.size()>=SPEECH_SIZE)
            {
                homiWriteWakeUpPcm((char *)speechBuf.data(),SPEECH_SIZE);
                speechBuf.erase(speechBuf.begin(), speechBuf.begin() + SPEECH_SIZE);
            }
            if(count%(50*5)==0)
            {
                ROS_INFO_NAMED(__FILENAME__,"update wake_up_xf");
            }
            
        }
        

    });
    ros::Subscriber sub2 = nh.subscribe(pcm_stream_topic,50,&pcmStreamCallback);
    ros::spin();
    stop = true;
    if(mythread.joinable())
    {
        mythread.join();
    }
    ret = homiStopWake();
    if(ret)
    {
        ROS_ERROR_NAMED(__FILENAME__,"homiStopWake failed , ret = %d\n",ret);
    }

    ret = homiUnInitWake();
    if(ret)
    {
        ROS_ERROR_NAMED(__FILENAME__,"homiUnInitWake failed , ret = %d\n",ret);
    }
    ROS_INFO_NAMED(__FILENAME__,"wake_up_xf end");
    return 0;
}