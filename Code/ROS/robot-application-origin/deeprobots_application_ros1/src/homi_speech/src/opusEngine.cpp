#include <ros/ros.h>
#include <homi_speech/def.h>
#include <homi_speech/opusEngine.h>
#include <atomic>
#include <opus/opus.h>
#include <deque>
#include <vector>
#define MAX_PACKET (1500)
#define MAX_FRAME_SAMP (5760)
namespace homi::app {
    class pcmPacketSplitter
    {
    public:
        // 输入数据进行分包
        void inputData(const std::vector<unsigned char>& data)
        {
            // 将新数据添加到剩余数据中
            remaining_data.insert(remaining_data.end(), data.begin(), data.end());
            // 处理数据，直到剩下的数据不足640字节
            while (remaining_data.size() >= 640) {
                std::vector<unsigned char> packet(remaining_data.begin(), remaining_data.begin() + 640);
                packets.push_back(packet);
                remaining_data.erase(remaining_data.begin(), remaining_data.begin() + 640);
            }
        }
        // 获取所有已分包的数据
        std::vector<std::vector<unsigned char>> getPackets()
        {    
            std::vector<std::vector<unsigned char>> result;
            
            for (auto& item : packets) {
                result.push_back(item);
            }      
            packets.clear(); // 清空已获取的包        
            return result;
        }
    private:
        std::deque<std::vector<unsigned char>> packets; // 存储分包数据
        std::vector<unsigned char> remaining_data;      // 存储多余数据
    }; 
    class ImplOpusEncode : public IAudioEncode
    {
    public:
        bool onInit()
        {
            int err;
            auto enc = opus_encoder_create(16000, 1, OPUS_APPLICATION_VOIP, &err);
            if(err!=OPUS_OK || enc==NULL)return false;
            if(opus_encoder_ctl(enc, OPUS_SET_BITRATE(16000)) != OPUS_OK) return false;
            if(opus_encoder_ctl(enc, OPUS_SET_FORCE_CHANNELS(1)) != OPUS_OK) return false;
            if(opus_encoder_ctl(enc, OPUS_SET_VBR(0)) != OPUS_OK) return false;
            if(opus_encoder_ctl(enc, OPUS_SET_VBR_CONSTRAINT(0)) != OPUS_OK) return false;
            if(opus_encoder_ctl(enc, OPUS_SET_COMPLEXITY(0)) != OPUS_OK) return false;
            if(opus_encoder_ctl(enc, OPUS_SET_MAX_BANDWIDTH(OPUS_BANDWIDTH_NARROWBAND)) != OPUS_OK) return false;
            if(opus_encoder_ctl(enc, OPUS_SET_SIGNAL(OPUS_AUTO)) != OPUS_OK) return false;
            if(opus_encoder_ctl(enc, OPUS_SET_INBAND_FEC(0)) != OPUS_OK) return false;
            if(opus_encoder_ctl(enc, OPUS_SET_PACKET_LOSS_PERC(0)) != OPUS_OK) return false;
            if(opus_encoder_ctl(enc, OPUS_SET_LSB_DEPTH(8)) != OPUS_OK) return false;
            if(opus_encoder_ctl(enc, OPUS_SET_PREDICTION_DISABLED(0)) != OPUS_OK) return false;
            if(opus_encoder_ctl(enc, OPUS_SET_DTX(0)) != OPUS_OK) return false;
            if(opus_encoder_ctl(enc, OPUS_SET_EXPERT_FRAME_DURATION(OPUS_FRAMESIZE_20_MS)) != OPUS_OK) return false;
            _enc = enc;
            return true;
        }
        ImplOpusEncode(const AudioCodeConfig &config):_config(config)
        {
            s_count.fetch_add(1);
            _enc = nullptr;
        }
        virtual bool encode(std::shared_ptr<AudioFrame> &inDataPtr,std::vector<std::shared_ptr<AudioCodeFrame>> &outData) override
        {
            if(inDataPtr==nullptr) return false;
            try
            {
                outData.clear();
                m_pcmPacketSplitter.inputData(inDataPtr->data);
                std::vector<std::vector<unsigned char>> packets = m_pcmPacketSplitter.getPackets();
                for(const auto& elem : packets) {
                unsigned char packet[MAX_PACKET+257];
                opus_int16 *inbuf = (opus_int16*)elem.data();
                int frame_size = elem.size()/2;
                auto len = opus_encode(_enc, inbuf, frame_size, packet, MAX_PACKET);
                if(len<0 || len>MAX_PACKET) {
                    ROS_ERROR_NAMED(__FILENAME__,"opus_encode(), returned =  %d",len);
                    continue;
                }
                auto frame = std::make_shared<AudioCodeFrame>();
                frame->config = inDataPtr->config;
                frame->data = std::vector<unsigned char>(packet,packet+len);
                frame->ts = inDataPtr->ts;
                outData.insert(outData.end(), frame);
                }
                return true;
            }
            catch(const std::exception& e)
            {
                ROS_ERROR_NAMED(__FILENAME__,"encode: %s",e.what()); 
            }
            return false;
        }
        virtual ~ImplOpusEncode()
        {
            auto k = s_count.fetch_sub(1);
            ROS_ERROR_NAMED(__FILENAME__,"ImplOpusEncode: %d",k-1);
            if(_enc)
            {
                opus_encoder_destroy(_enc);
                _enc=nullptr;
            }
        }
    private:
        const AudioCodeConfig _config;
        OpusEncoder *_enc;
        static std::atomic<int> s_count;
        pcmPacketSplitter m_pcmPacketSplitter;
    };
    std::atomic<int> ImplOpusEncode::s_count(0);
    class ImplOpusDecode : public IAudioDecode
    {
    public:
        bool onInit()
        {
            int err;
            auto dec = opus_decoder_create(16000, 1, &err);
            if(err!=OPUS_OK || dec==NULL) return false;
            _dec = dec;
            return true;
        }
        ImplOpusDecode(const AudioCodeConfig &config):_config(config)
        {
            s_count.fetch_add(1);
            _dec = nullptr;
        }
        virtual bool decode(std::shared_ptr<AudioCodeFrame> &inDataPtr,std::vector<std::shared_ptr<AudioFrame>> &outData) override
        {
            if(inDataPtr==nullptr) return false;
            try
            {
                outData.clear();
                auto frame = std::make_shared<homi::app::AudioFrame>();
                frame->config = inDataPtr->config;
                frame->data = inDataPtr->data;
                frame->ts = inDataPtr->ts;
                outData.insert(outData.end(), frame);
                return true;
            }
            catch(const std::exception& e)
            {
                ROS_ERROR_NAMED(__FILENAME__,"decode: %s",e.what());   
            }
            return false;
        }
        ~ImplOpusDecode()
        {
            auto k = s_count.fetch_sub(1);
            ROS_ERROR_NAMED(__FILENAME__,"ImplOpusDecode: %d",k-1);
            if(_dec)
            {
                opus_decoder_destroy(_dec);
            }
        }
    private:
        const AudioCodeConfig _config;
        OpusDecoder *_dec;
        static std::atomic<int> s_count;
    };
    std::atomic<int> ImplOpusDecode::s_count(0);
    class ImplOpusEng: public  IOpusEngine
    {
    public:
        bool onInit()
        {
            return true;
        }
        virtual std::shared_ptr<IAudioDecode> createAudioDecode(const AudioCodeConfig &config) override
        {
            auto ptr = std::make_shared<ImplOpusDecode>(config);
            if(ptr&&ptr->onInit())
            {
                return ptr;
            }
            return nullptr;
        }
        virtual std::shared_ptr<IAudioEncode> createAudioEncode(const AudioCodeConfig &config) override
        {
            auto ptr = std::make_shared<ImplOpusEncode>(config);
            if(ptr&&ptr->onInit())
            {
                return ptr;
            }
            return nullptr;
        }
        virtual ~ImplOpusEng(){

        };
    };
    std::shared_ptr<IOpusEngine> createOpusEngine()
    {
        auto ptr =  std::make_shared<ImplOpusEng>();
        if(ptr&&ptr->onInit())
        {
            return ptr;
        }
        return nullptr;
    }
}