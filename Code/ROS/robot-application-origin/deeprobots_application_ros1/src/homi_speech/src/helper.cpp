#include <ros/ros.h>
#include <homi_speech/def.h>
#include <homi_speech_interface/AssistantAbort.h>
#include <homi_speech_interface/AssistantCtrl.h>
#include <homi_speech_interface/AssistantTakePhoto.h>
//#include <homi_speech_interface/AssistantQuiet.h>
#include <homi_speech_interface/AssistantSpeechText.h>
#include <homi_speech_interface/Wakeup.h>
#include <atomic>

std::atomic<bool> g_assistantQuiet(false);
static int AssistantCallService(homi_speech_interface::AssistantCtrl &srv)
{
    ros::NodeHandle nh;
    auto assistantClient = nh.serviceClient<homi_speech_interface::AssistantCtrl>(PARAM_SPEECH_ASSISTANT_CTRL_SERVICE);
    auto ret = assistantClient.waitForExistence(ros::Duration(1.0));
    if(ret==false)
    {
        ROS_WARN_NAMED(__FILENAME__,"Failed to waitForExistence service assistant");
        return -11;
    }
    ret = assistantClient.call(srv);
    if(ret==false)
    {
        ROS_WARN_NAMED(__FILENAME__,"Failed to call service assistant");
        return -12;
    }
    else
    {
        ROS_WARN_NAMED(__FILENAME__,"service assistant return error code %d",srv.response.errorCode);
        return srv.response.errorCode;        
    }
}
bool AssistantTakePhotoCallback(homi_speech_interface::AssistantTakePhoto::Request &req,
                             homi_speech_interface::AssistantTakePhoto::Response &res)
{
    homi_speech_interface::AssistantCtrl srv;
    ros::NodeHandle nh;
    srv.request.isNormal = false;
    srv.request.start = true;
    srv.request.stop = true;
    srv.request.startWaitMS = 5000;
    srv.request.stopWaitMS = 5000;
    srv.request.mutipleWheels = true;
    srv.request.inquiryText = "准备好拍照了吗";
    srv.request.inquirySubType = 0;
    srv.request.notifyUserAbort = false;
    res.errorCode = AssistantCallService(srv);
    return true;
}
bool AssistantAbortCallback(homi_speech_interface::AssistantAbort::Request &req,
                             homi_speech_interface::AssistantAbort::Response &res)
{    
    homi_speech_interface::AssistantCtrl srv;
    ros::NodeHandle nh;
    srv.request.isNormal = false;
    srv.request.start = false;
    srv.request.stop = true;
    srv.request.startWaitMS = 5000;
    srv.request.stopWaitMS = 5000;
    srv.request.mutipleWheels = false;
    srv.request.inquiryText = "";
    srv.request.inquirySubType = -1;
    srv.request.notifyUserAbort = false;
    res.errorCode = AssistantCallService(srv);
    return true;
}

bool AssistantSpeechTextCallback(homi_speech_interface::AssistantSpeechText::Request &req,
                             homi_speech_interface::AssistantSpeechText::Response &res)
{    
    homi_speech_interface::AssistantCtrl srv;
    ros::NodeHandle nh;
    srv.request.isNormal = false;
    srv.request.start = true;
    srv.request.stop = true;
    srv.request.startWaitMS = 5000;
    srv.request.stopWaitMS = 5000;
    srv.request.mutipleWheels = false;
    srv.request.inquiryText = req.msg;
    srv.request.inquirySubType = -1;
    srv.request.notifyUserAbort = false;
    res.errorCode = AssistantCallService(srv);
    res.sectionId = srv.response.eventId;
    return true;
}

// bool AssistantQuietCallback(homi_speech_interface::AssistantQuiet::Request &req,
//                              homi_speech_interface::AssistantQuiet::Response &res)
// {
//     if(req.enable==true)
//     {
//         g_assistantQuiet.store(true);
//         ROS_WARN_NAMED(__FILENAME__,"call service assistant: enable quiet");
//     }
//     else
//     {
//         g_assistantQuiet.store(false);
//         ROS_WARN_NAMED(__FILENAME__,"call service assistant: disable quiet");
//     }
//     res.errorCode = 0;
//     return true;
// }

void WakeupCallback(const homi_speech_interface::WakeupConstPtr &msg)
{
    ROS_WARN_NAMED(__FILENAME__,"WakeupCallback: %s quiet:%d",msg->ivwWord.c_str(),g_assistantQuiet.load());
    if(g_assistantQuiet.load()==true)
    {
        ROS_WARN_NAMED(__FILENAME__,"assistant quiet is enabled, ignore wakeup");
        return ;
    }
    homi_speech_interface::AssistantCtrl srv;
    ros::NodeHandle nh;
    srv.request.isNormal = true;
    srv.request.start = true;
    srv.request.stop = true;
    srv.request.startWaitMS = 5000;
    srv.request.stopWaitMS = 5000;
    srv.request.mutipleWheels = true;
    srv.request.inquiryText = "";
    srv.request.inquirySubType = -1;
    srv.request.notifyUserAbort = true;
    AssistantCallService(srv);
}

int main(int argc, char *argv[])
{
    setlocale(LC_ALL, "");
    ros::init(argc, argv, "helper");
    ros::NodeHandle nh;
    ROS_INFO_NAMED(__FILENAME__,"helper start up");
    g_assistantQuiet.store(false);
    std::string assistantTopic;
    if(!nh.getParam(PARAM_PCM_STREAM_WAKEUP_TOPIC, assistantTopic))
    {
        ROS_ERROR_NAMED(__FILENAME__,"Failed to get param assistantTopic");
        return -1;
    }
    auto tWakeup = nh.subscribe(assistantTopic,10,&WakeupCallback);
    auto sAssistantAbort = nh.advertiseService(PARAM_HELPER_ASSISTANT_ABORT_SERVICE, &AssistantAbortCallback);
    //auto sAssistantQuiet = nh.advertiseService(PARAM_HELPER_ASSISTANT_QUIET_SERVICE, &AssistantQuietCallback);
    auto sAssistantTakePhoto = nh.advertiseService(PARAM_HELPER_ASSISTANT_TAKEPHOTO_SERVICE, &AssistantTakePhotoCallback);
    auto sAssistantSpeechText = nh.advertiseService(PARAM_HELPER_ASSISTANT_SPEECH_TEXT_SERVICE, &AssistantSpeechTextCallback);
    ros::spin();
    return 0;
}
