#include <ros/ros.h>
#include <homi_speech_interface/PCMStream.h>
#include <homi_speech_interface/SIGCEvent.h>
#include <homi_speech_interface/SIGCData.h>
#include <homi_speech_interface/AssistantCtrl.h>
#include <homi_speech_interface/AssistantEvent.h>
#include <homi_speech_interface/IotControl.h>
#include <homi_speech/UploadImageUrl.h>
#include <homi_speech/def.h>
#include <homi_speech/AlsaHelper.h>
#include <homi_speech/WavHelper.h>
#include <homi_sdk/OpenAPI.h>
#include <homi_speech/json.hpp>
#include <homi_sdk/InnerAPI.h>
#include <homi_sdk/AudioStream.h>
#include <homi_sdk/SimpleSpeechApp.h>
#include <homi_sdk/ThreadPool.h>
#include <fstream>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <string>
#include <chrono>
#include <homi_speech/OfflineAsrEngine.h>
#include <homi_speech/opusEngine.h>
namespace happ =  homi::app;
std::shared_ptr<homi::app::IOpusEngine> g_opusEngine;
std::shared_ptr<homi::app::IOffineAsrEngine> g_offlineAsrEngine;
std::shared_ptr<homi::app::ISimpleSpeechApp> g_speechApp; 
struct UploadImageUrl
{
    std::mutex _mutex;
    std::condition_variable _cv;
    std::string _url;
}g_UploadImageUrlOp;
bool checkFolderExists(const std::string& filepath) {
    size_t found = filepath.find_last_of("/\\");
    if (found == std::string::npos) {
        return false;  // 没有找到路径分隔符
    }
    std::string folder = filepath.substr(0, found);
    std::ifstream file(folder);
    return file.good();
}

class INotifyInputStream
{
public:
    virtual void notifyInputStream(const std::shared_ptr<happ::AudioFrame> &frame) = 0;
};
class InputStreamPublisher
{
public:
    void registerSubscriber(const std::weak_ptr<INotifyInputStream> & subscriber)
    {
        std::lock_guard<std::mutex> lock(_mutex);
        _subscribersTmp.push_back(subscriber);
    }
    void notifySubscribers(const std::shared_ptr<happ::AudioFrame> &frame)
    {
        {
            std::lock_guard<std::mutex> lock(_mutex);
            _subscribers.insert(_subscribers.end(),_subscribersTmp.begin(),_subscribersTmp.end());
            _subscribersTmp.clear();
        }
        _subscribers.remove_if([frame](auto& callback){
            if (auto ptr = callback.lock())
            {
                ptr->notifyInputStream(frame);
                return false;
            }
            return true;
        });
    }
private:
    std::list<std::weak_ptr<INotifyInputStream>> _subscribers;
    std::list<std::weak_ptr<INotifyInputStream>> _subscribersTmp;
    std::mutex _mutex;
} g_NetStatusListener;

void pcmStreamCallback(const homi_speech_interface::PCMStreamConstPtr & ptr)
{
    if(ptr==nullptr) return ;
    auto frame = std::make_shared<happ::AudioFrame>();
    if(frame==nullptr) return ;
    happ::AudioConfig config{happ::SampleFormat::PCM_S16LE,16000,1};
    frame->config = config;
    frame->data = ptr->data;
    frame->ts = std::chrono::milliseconds(ptr->ts);
    g_NetStatusListener.notifySubscribers(frame);
}

bool SGICDataCallback(homi_speech_interface::SIGCData::Request &req,
					homi_speech_interface::SIGCData::Response &res)
{
    ROS_INFO_NAMED(__FILENAME__,"SGICDataCallback %s",req.data.c_str());
    auto ret = homi::inner::sendEventOOB(std::make_shared<std::string>(req.data),false,2000);
    ROS_INFO_NAMED(__FILENAME__,"SGICDataCallback ret=%d",ret);
    if(ret<0)
    {
        res.errorCode = -1;
    }
    else
    {
        res.errorCode = 0;
    }
    return true;
}
bool UploadImageUrlCallback(homi_speech::UploadImageUrl::Request &req,
					homi_speech::UploadImageUrl::Response &res)
{
    ROS_INFO_NAMED(__FILENAME__,"UploadImageUrl fileSize = %d",req.fileSize);
    {
        try
        {
            nlohmann::json obj;
            obj["skillName"] = "photo_upload";
            obj["argumentV2"]["fileSize"] = req.fileSize;
            auto ret = homi::inner::sendEvent("SKILL_EXECUTE","skill_photo_upload",true,obj);
            if(ret<0)
            {
                res.errorCode = -1;
                return true;
            }
        }
        catch(const std::exception& e)
        {
            ROS_WARN_NAMED(__FILENAME__,"%s",e.what());
        }
        {
            std::unique_lock<std::mutex> lock(g_UploadImageUrlOp._mutex);
            g_UploadImageUrlOp._url.clear();
            g_UploadImageUrlOp._cv.wait_for(lock,std::chrono::milliseconds(10000),[](){ return !g_UploadImageUrlOp._url.empty();});
            if(g_UploadImageUrlOp._url.empty())
            {
                res.errorCode = -2;
            }
            else
            {
                res.url = g_UploadImageUrlOp._url;
                res.errorCode = 0;
            }
        }
    }
    return true;
}

bool IotControlCallback(homi_speech_interface::IotControl::Request &req,
					homi_speech_interface::IotControl::Response &res)
{
    
    nlohmann::json obj;
    // nlohmann::json controlParams;
    using json = nlohmann::json;
    obj["skillName"] = "iot_control";
    nlohmann::json argumentV2;
    argumentV2["targetDeviceId"] = "CMCC-591022-34A6EF828EA8";
    json parsedJson = json::parse(req.param);
    int outletStatusValue = parsedJson.value("outletStatus", 1);
    std::vector<std::unordered_map<std::string, std::string>> controlParams;
    controlParams.push_back({{"name", "outletStatus"}, {"value", std::to_string(outletStatusValue)}});
    // controlParams.push_back({{"name", "outletStatus"}, {"value", "1"}});
    argumentV2["controlParams"] = controlParams;
    obj["argumentV2"] = argumentV2;
    std::cout << obj<< std::endl;

    // 按照srv.request.param处理逻辑
    // nlohmann::json obj;
    // nlohmann::json parsedJson = nlohmann::json::parse(req.param);
    // obj.update(parsedJson);
    auto ret = homi::inner::sendEvent("SKILL_EXECUTE","skill_iot_control",true,obj);
    if(ret<0)
    {
        res.errorCode = -1;
        return true;
    }
    return true;
}
static int assistantCtrl(std::string &eventId,bool isNormal, bool multipleWheel,bool notifyUserAbort
        ,const std::string &inquiryText,const int inquirySubType
        ,bool run,const std::chrono::milliseconds& runWait
        ,bool stop,const std::chrono::milliseconds& stopWait) 
{
    ros::NodeHandle nh; 
    int waitrun;
    std::chrono::milliseconds wait1{10000},wait2{0};
    homi::app::SimpleSpeechAppErrorCode error;
    if(nh.getParam(PARAM_SPEECH_ROUND1WAIT_MS,waitrun))
    {
        wait1 = std::chrono::milliseconds(waitrun);
    }
    if(multipleWheel)
    {
        if(nh.getParam(PARAM_SPEECH_ROUNDnWAIT_MS,waitrun))
        {
            wait2 = std::chrono::milliseconds(waitrun);
        }
        else
        {
            wait2 = std::chrono::milliseconds(10000);
        }
    }
    if(stop)
    {
        error = g_speechApp->stop(notifyUserAbort,stopWait);
        if(error != homi::app::SimpleSpeechAppErrorCode::Success)
        {
            if(error==happ::SimpleSpeechAppErrorCode::EAgain)
            {
                return 1;
            }
            else
            {
                return -1;
            }
        }
    } 
    if(run)
    {
        if(isNormal)
        {
            error = g_speechApp->runNormal(eventId,std::chrono::milliseconds(runWait),wait1,wait2);
        }
        else
        {
            error = g_speechApp->runInquiry(eventId,inquiryText,inquirySubType,std::chrono::milliseconds(runWait),wait1,wait2);
        }
        
        if(error != homi::app::SimpleSpeechAppErrorCode::Success)
        {
            if(error==happ::SimpleSpeechAppErrorCode::EAgain)
            {
                return 2;
            }
            else
            {
                return -2;
            }
        }
    } 
    return 0;  
}
bool AssistantCtrlCallback(homi_speech_interface::AssistantCtrl::Request &req,
					homi_speech_interface::AssistantCtrl::Response &res)
{
    ROS_INFO_NAMED(__FILENAME__,"Assistant isNormal(%d) start(%d) stop(%d) startWaitMS(%d) stopWaitMS(%d) inquiryText(%s) inquirySubType(%d) mutipleWheels(%d) notifyUserAbort(%d)",req.isNormal,req.start,req.stop,req.startWaitMS,req.stopWaitMS,req.inquiryText.c_str(),req.inquirySubType,req.mutipleWheels,req.notifyUserAbort);
    std::string eventId;
    res.errorCode = assistantCtrl(eventId,req.isNormal,req.mutipleWheels,req.notifyUserAbort,req.inquiryText,req.inquirySubType,req.start,std::chrono::milliseconds(req.startWaitMS),req.stop,std::chrono::milliseconds(req.stopWaitMS));
    res.eventId = eventId;
    return true;
}
const char * homiAppResourceTypehelperToString(homi::app::ResourceType type) {
            switch (type) {
                case homi::app::ResourceType::Wakeup:          return "Wakeup";
                case homi::app::ResourceType::NetworkError:       return "NetworkError";
                case homi::app::ResourceType::DeviceInputError:  return "DeviceInputError";
                case homi::app::ResourceType::offlineInstruction:    return "offlineInstruction";
                case homi::app::ResourceType::Inquiry:        return "Inquiry";
                case homi::app::ResourceType::TimeOut:      return "TimeOut";
                case homi::app::ResourceType::offlineInstructionError:    return "offlineInstructionError";
                case homi::app::ResourceType::End:    return "End";
                default:                                  return "Unknown";
            }
}
static nlohmann::json g_ResourceFiles;
class ResStream : public homi::app::IAudioStreamInput
{
public:
    ResStream(happ::ResourceType rt,int subType)
    {
        s_count.fetch_add(1);
        _rt = rt;
        _subType = subType;
    }
    bool onInit()
    {
        std::string filePath;
        if(_subType<0) return false;
        try
        {
            auto key = g_ResourceFiles[homiAppResourceTypehelperToString(_rt)];
            if(key.size()<_subType) return false;
            auto index = _subType==0?rand()%key.size():_subType-1;
            filePath = key[index].get<std::string>();
        }
        catch(const std::exception& e)
        {
            ROS_WARN_NAMED(__FILENAME__,"parse speech resource file error:%s",e.what());
        }
        if(_wf.openFile(filePath)<0)
        {
            ROS_WARN_NAMED(__FILENAME__,"failed to open file %s", filePath.c_str());
            return false;
        }        
        _config.sampleRate = _wf.getRate();
        _config.channelCount = _wf.getChannels();
        _config.format = _wf.getFormat();
        if(_config.format==happ::SampleFormat::End)
        {
            ROS_WARN_NAMED(__FILENAME__,"unknown sample format %d", (int)_wf.getFormat());
            return false;
        }
        return true;
    }
    virtual happ::StreamErrorCode read(std::shared_ptr<happ::AudioFrame> &InDataPtr,const std::chrono::milliseconds &wait) override
    {
        
        InDataPtr = std::make_shared<happ::AudioFrame>();
        InDataPtr->config = _config;
        InDataPtr->data = _wf.readData(-1,1024);
        InDataPtr->ts = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch());
        if(InDataPtr->data.size() == 0) return happ::StreamErrorCode::EEOF;
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode getAudioConfig(happ::AudioConfig & config) override
    {
        config = _config;
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode close(const std::chrono::milliseconds &wait) override
    {

        return happ::StreamErrorCode::Success;
    }
    virtual ~ResStream() override
    {
        _wf.close();
        auto k = s_count.fetch_sub(1);
        ROS_INFO_NAMED(__FILENAME__,"~ResStream %d",k-1);
    };
private:
    static std::atomic<int> s_count;
    happ::AudioConfig _config;
    homi_speech::WavHelperFromFile _wf;
    happ::ResourceType _rt;
    int _subType;
};
std::atomic<int> ResStream::s_count{0};
class InStream : public std::enable_shared_from_this<InStream>
    , public homi::app::IAudioStreamInput ,public INotifyInputStream
{
public:
    InStream(const happ::AudioConfig &config):_config(config)
    {
        s_count.fetch_add(1);  
        _inputClosed = false;  
        _isRead = false;    
    }
    bool onInit()
    {
        auto ptr = std::static_pointer_cast<INotifyInputStream>(shared_from_this());
        if(ptr==nullptr) 
        {
            ROS_WARN_NAMED(__FILENAME__,"onInit std::static_pointer_cast<INotifyInputStream>(shared_from_this())==nullptr");
            return false;
        }
        g_NetStatusListener.registerSubscriber(ptr);
        return true;
    }
    virtual void notifyInputStream(const std::shared_ptr<happ::AudioFrame> &frame) override
    {
        std::unique_lock<std::mutex> lock(_mutex);
        if(_inputClosed) return;
        _frames.push(frame);
        if(_frames.size()>20) _frames.pop();
        _cv.notify_one();
    }
    void readSyn()
    {
        if(!_isRead)
        {
            _isRead = true;
            _firstReadTS=std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch());
        }
        while(_frames.size() > 0)
        {
            auto frame = _frames.front();
            if(frame->ts > _firstReadTS) 
            {
                break;
            }
            _frames.pop();
        }
    }
    virtual happ::StreamErrorCode read(std::shared_ptr<happ::AudioFrame> &InDataPtr,const std::chrono::milliseconds &wait) override
    {
        auto endTime = std::chrono::system_clock::now();
        if(wait>=std::chrono::milliseconds::max()/2) endTime = std::chrono::system_clock::time_point::max();
        else endTime += wait;
        std::unique_lock<std::mutex> lock(_mutex);
        while(endTime > std::chrono::system_clock::now())
        {
            _cv.wait_until(lock,endTime,[this](){return _inputClosed || _frames.size()>0;});
            if(_inputClosed) return happ::StreamErrorCode::Failure;
            readSyn();
            if(_frames.size()>0)
            {
                InDataPtr = _frames.front();
                _frames.pop();
                return happ::StreamErrorCode::Success;
            }
        }
        return happ::StreamErrorCode::EAgain;
    }
    virtual happ::StreamErrorCode getAudioConfig(happ::AudioConfig & config) override
    {
        config = _config;
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode close(const std::chrono::milliseconds &wait) override
    {
        std::unique_lock<std::mutex> lock(_mutex);
        _inputClosed = true;
        _cv.notify_all();
        return happ::StreamErrorCode::Success;
    }
    virtual ~InStream() override
    {
        {
            std::unique_lock<std::mutex> lock(_mutex);
            _inputClosed = true;
            _cv.notify_all();
        }
        auto k = s_count.fetch_sub(1);
        ROS_INFO_NAMED(__FILENAME__,"InStream::~InStream %d",k-1);
    };
private:
    static std::atomic<int> s_count;
    const happ::AudioConfig _config;
    bool _inputClosed;
    bool _isRead;
    std::chrono::milliseconds _firstReadTS;
    std::mutex _mutex;
    std::condition_variable _cv;
    std::queue<std::shared_ptr<happ::AudioFrame>> _frames;
};
std::atomic<int> InStream::s_count{0};
class OutStream : public happ::IAudioStreamOutput
{
public:
    OutStream(const happ::AudioConfig &config):_config(config)
    {
        s_count.fetch_add(1);
    }
    std::string getPlaybackDeviceName()
    {
        ros::NodeHandle nh;
        std::string playbackShPath;
        std::string playbackShParam;
        if(!nh.getParam(PARAM_ALSA_PLAYBACK_SH_NAME, playbackShPath) 
                ||!nh.getParam(PARAM_ALSA_PLAYBACK_SH_PARAM, playbackShParam))
        {
            ROS_ERROR_NAMED(__FILENAME__,"getparam (alsa_playback_sh_name or alsa_playback_sh_param) fail");
            return "default";
        }
        try
        {
            auto paramJson = nlohmann::json::parse(playbackShParam);
            auto names = paramJson["name"];
            int cardId=-1;
            for(auto e:names)
            {
                auto nameTmp = e.get<std::string>();
                ROS_INFO_NAMED(__FILENAME__,"playbackDeviceName=%s",nameTmp.c_str());
                if(nameTmp=="default")
                {
                    return "default";
                }
                cardId = homi_speech::getCardId(playbackShPath,nameTmp);
                if(cardId>=0)
                {
                    return ("plughw:" + std::to_string(cardId) + ",0");
                }
            }
        }
        catch(const std::exception& e)
        {
            ROS_ERROR_NAMED(__FILENAME__,"%s",e.what());
        }
        ROS_ERROR_NAMED(__FILENAME__,"no mathch playbackDeviceName");
        return "";
    }
    bool onInit()
    {
        auto name = getPlaybackDeviceName();
        if(name.empty())
        {
            ROS_ERROR_NAMED(__FILENAME__,"getPlaybackDeviceName fail");
            return false;
        }
        try
        {
            ROS_INFO_NAMED(__FILENAME__,"playbackDeviceName=%s",name.c_str());
            playback.openAndConfig(name.c_str(),"S16_LE",_config.channelCount,_config.sampleRate);
        }
        catch(const std::exception& e)
        {
            ROS_ERROR_NAMED(__FILENAME__,"%s",e.what());
            return false;
        }
        return true;
        
    }
    virtual happ::StreamErrorCode write(std::shared_ptr<happ::AudioFrame> OutDataPtr,const std::chrono::milliseconds &wait) override
    {        
        try
        {
            playback.writei((const unsigned char *)OutDataPtr->data.data(), OutDataPtr->data.size());
        }
        catch(const std::exception& e)
        {
            ROS_ERROR_NAMED(__FILENAME__,"%s",e.what());
            return happ::StreamErrorCode::Failure;
        }
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode getAudioConfig(happ::AudioConfig &config) override
    {
        config = _config;
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode close(bool flush,const std::chrono::milliseconds &wait) override
    {
        try
        {
            if(flush)
            {
                playback.drain();
            }
            playback.close();
            
        }
        catch(const std::exception& e)
        {
            ROS_ERROR_NAMED(__FILENAME__,"%s",e.what());
            return happ::StreamErrorCode::Failure;
        }
        return happ::StreamErrorCode::Success;
    }
    virtual ~OutStream() override
    {
        playback.close();
        auto k = s_count.fetch_sub(1);
        ROS_INFO_NAMED(__FILENAME__,"OutStream::~OutStream %d",k-1);
    };
private:
    static std::atomic<int> s_count;
    const happ::AudioConfig _config;
    homi_speech::AlsaHelperPlayback playback;
};
std::atomic<int> OutStream::s_count{0};


class StreamFactory : public happ::IStreamsFactory
{
public:
    virtual std::shared_ptr<happ::IAudioStreamInput> createAudioStreamInput(const happ::AudioConfig & config) override
    {
        auto ptr = std::make_shared<InStream>(config);
        if(ptr->onInit())
            return ptr;
        else
            return nullptr;
    }
    virtual std::shared_ptr<happ::IAudioStreamOutput> createAudioStreamOutput(const happ::AudioConfig & config) override
    {
        auto ptr = std::make_shared<OutStream>(config);
        if(ptr->onInit())
            return ptr;
        else
            return nullptr;
    }
    virtual std::shared_ptr<happ::IAudioStreamInput> createResource(const happ::ResourceType rt,const int subType) override
    {
        auto ptr = std::make_shared<ResStream>(rt,subType);
        if(ptr->onInit())
            return ptr;
        else
            return nullptr;
    }
    virtual std::shared_ptr<happ::IAudioStreamOutput> createOfflineInstruction(const std::string &eventId,const std::function<void(const std::string & eventId, const int ResSubType, const std::string & instruction)> &callback) override
    {
        if(g_offlineAsrEngine==nullptr) return nullptr;
        return g_offlineAsrEngine->createAudioStreamOutput(eventId,callback);
    }
    virtual std::shared_ptr<happ::IAudioDecode> createAudioDecode(const happ::AudioCodeConfig &config) override
    {
        ROS_WARN_NAMED(__FILENAME__,"createAudioDecode");
        if(g_opusEngine==nullptr)
        {
            ROS_ERROR_NAMED(__FILENAME__,"createAudioDecode:g_opusEngine==nullptr");
            return nullptr;
        } 
        return g_opusEngine->createAudioDecode(config);
    }
    virtual std::shared_ptr<happ::IAudioEncode> createAudioEncode(const happ::AudioCodeConfig &config) override
    {
        ROS_WARN_NAMED(__FILENAME__,"createAudioEncode");
        if(g_opusEngine==nullptr)
        {
            ROS_ERROR_NAMED(__FILENAME__,"createAudioEncode:g_opusEngine==nullptr");
            return nullptr;
        } 
        return g_opusEngine->createAudioEncode(config);
    }
    virtual ~StreamFactory() override
    {}
};



int main(int argc, char *argv[])
{
    setlocale(LC_ALL, "");
    ros::init(argc, argv, "speech_core");
    ROS_INFO_NAMED(__FILENAME__,"speech_core start up");
    ros::NodeHandle nh; 
    std::string initParam;
    std::string logfile="";
    homi::LogLevel loglevel=homi::LogLevel::LOG_LEVEL_DEBUG;
    std::string pcmStreamTopic;
    std::string playbackShPath;
    std::string playbackShParam;
    std::string speechResourceFiles;
    std::string displayAssistantWakeupShPath;
    std::string displayAssistantIdleShPath;
    if(!nh.getParam(PARAM_SPEECH_DISPLAY_WAKEUP_SH_FILE, displayAssistantWakeupShPath)
        ||!nh.getParam(PARAM_SPEECH_DISPLAY_IDLE_SH_FILE, displayAssistantIdleShPath))
    {
        ROS_ERROR_NAMED(__FILENAME__,"getparam (%s , %s) fail",PARAM_SPEECH_DISPLAY_WAKEUP_SH_FILE,PARAM_SPEECH_DISPLAY_IDLE_SH_FILE);
        return -1;        
    }
    if(!nh.getParam(PARAM_SPEECH_RESSOURCE_FILE_CONFIG, speechResourceFiles))
    {
        ROS_ERROR_NAMED(__FILENAME__,"getparam (%s) fail",PARAM_SPEECH_RESSOURCE_FILE_CONFIG);
        return -1;
    }
    try
    {
        g_ResourceFiles=nlohmann::json::parse(speechResourceFiles);
    }
    catch(const std::exception& e)
    {
        ROS_ERROR_NAMED(__FILENAME__,"parse (%s) fail:%s",PARAM_SPEECH_RESSOURCE_FILE_CONFIG,e.what() );
        return -1;
    }
    
    if(!nh.getParam(PARAM_SPEECH_SDK_INITPARAM, initParam))
    {
        ROS_ERROR_NAMED(__FILENAME__,"getparam (%s) fail",PARAM_SPEECH_SDK_INITPARAM);
        return -1;
    }
    std::string sTmp;
    if(nh.getParam(PARAM_SPEECH_SDK_LOGFILE, sTmp))
    {
        if(checkFolderExists(sTmp))
        {
            logfile = sTmp;
        }
    }
    sTmp.clear();
    if(nh.getParam(PARAM_SPPECH_SDK_LOGLEVEL, sTmp))
    {
        if (sTmp=="info")
        {
            homi::LogLevel::LOG_LEVEL_INFO;
        }
    }
    if(!nh.getParam(PARAM_PCM_STREAM_TOPIC,pcmStreamTopic))
    {
        ROS_ERROR_NAMED(__FILENAME__,"getparam (%s) fail",PARAM_PCM_STREAM_TOPIC);
        return -1;
    }
    std::string aiserJson=R"({})";;
    if(!nh.getParam(PARAM_OFFLINE_ASR_ENGINE_CONFIG,aiserJson))
    {
        ROS_ERROR_NAMED(__FILENAME__,"getparam (%s) fail",PARAM_OFFLINE_ASR_ENGINE_CONFIG);
        return -1;
    }
    std::string offlineParseRule=R"({})";
    if(!nh.getParam(PARAM_OFFLINE_PARSE_RULE_CONFIG,offlineParseRule))
    {
        ROS_ERROR_NAMED(__FILENAME__,"getparam (%s) fail",PARAM_OFFLINE_PARSE_RULE_CONFIG);
        return -1;
    } 
    ROS_INFO_NAMED(__FILENAME__,"get offline asr config:\n%s \nrule=%s",aiserJson.c_str(),offlineParseRule.c_str());
    ROS_INFO_NAMED(__FILENAME__,"OpenAPI init param:\n%s\nloglevel=%d\nlogfile=%s",initParam.c_str(),(int)loglevel,logfile.c_str());
    std::string myDeviceId;
    try
    {
        auto json = nlohmann::json::parse(initParam);
        myDeviceId = json["config"]["deviceId"].get<std::string>();
        ROS_INFO_NAMED(__FILENAME__,"deviceId=%s",myDeviceId.c_str());
    }
    catch(const std::exception& e)
    {
        ROS_ERROR_NAMED(__FILENAME__,"parse (myDeviceId) fail:%s",e.what() );
        return -1;
    }
    
    auto h = homi::getOpenAPI();
    int ret; 
    ret = h->Init(initParam,loglevel,logfile);
    if(ret<0)
    {
        ROS_ERROR_NAMED(__FILENAME__,"OpenAPI init fail");
        return -1;
    }
    g_offlineAsrEngine = homi::app::createOfflineAsrEngine(aiserJson,offlineParseRule);
    if(g_offlineAsrEngine==nullptr)
    {
        ROS_ERROR_NAMED(__FILENAME__,"createOfflineAsrEngine fail");
        return -1;
    }
    g_opusEngine = homi::app::createOpusEngine();
    if(g_opusEngine==nullptr)
    {
        ROS_ERROR_NAMED(__FILENAME__,"createOpusEngine fail");
    }
    ret = homi::inner::regeisterUserEvent("DEVICE_ABILITY","photo_upload",[](const std::string &json){
        ROS_INFO_NAMED(__FILENAME__,"upload json=%s",json.c_str());
        try
        {
            auto obj = nlohmann::json::parse(json);
            std::unique_lock<std::mutex> lock(g_UploadImageUrlOp._mutex);
            g_UploadImageUrlOp._url = obj["url"];
            g_UploadImageUrlOp._cv.notify_one();
        }
        catch(const std::exception& e)
        {
            ROS_ERROR_NAMED(__FILENAME__,"%s",e.what());
        }
    });
    if(ret<0)
    {
        ROS_ERROR_NAMED(__FILENAME__,"regeisterUserEvent fail");
        return -1;
    }
    ros::Subscriber sub2 = nh.subscribe(pcmStreamTopic,50,&pcmStreamCallback);
    ros::ServiceServer sSIGCDataService = nh.advertiseService(PARAM_SPEECH_SIGC_DATA_SERVICE,&SGICDataCallback);
    ros::ServiceServer sAssistantService = nh.advertiseService(PARAM_SPEECH_ASSISTANT_CTRL_SERVICE,&AssistantCtrlCallback);
    ros::ServiceServer sUploadImageUrlService = nh.advertiseService(PARAM_SPEECH_UPLOAD_IMAGE_URL_SERVICE,&UploadImageUrlCallback);
    ros::ServiceServer sIotControlService = nh.advertiseService(PARAM_SPEECH_IOT_CONTROL_SERVICE,&IotControlCallback);
    ros::Publisher tSIGCEvent = nh.advertise<homi_speech_interface::SIGCEvent>(PARAM_SPEECH_SIGC_EVENT_TOPIC,1000);
    homi::inner::registerDefaultEvent([&tSIGCEvent](const std::string &json){
        ROS_WARN_NAMED(__FILENAME__,"inner event: %s",json.c_str());
        homi_speech_interface::SIGCEvent sigcEvent;
        sigcEvent.event = json;
        tSIGCEvent.publish(sigcEvent);
    });
    ros::Publisher tAssistantEvent = nh.advertise<homi_speech_interface::AssistantEvent>(PARAM_SPEECH_ASSISTANT_STATUS_TOPIC,10);
    std::shared_ptr<homi::framework::ThreadPool> threadPoolPtr = std::make_shared<homi::framework::ThreadPool>(1);
    if(threadPoolPtr==nullptr)
    {
        ROS_ERROR_NAMED(__FILENAME__,"create thread pool failed");
        return -1;
    }
    g_speechApp = happ::SimpleSpeechAppFactory(std::make_shared<StreamFactory>()
        ,[&tAssistantEvent,&tSIGCEvent,myDeviceId,threadPoolPtr,displayAssistantWakeupShPath,displayAssistantIdleShPath](const happ::SimpleSpeechAppEvent event,const std::string & msg,const std::string & eventId){
        ROS_WARN_NAMED(__FILENAME__,"event: %s, msg: %s, id: %s",happ::SimpleSpeechAppEventHelper::to_string(event),msg.c_str(),eventId.c_str());
        homi_speech_interface::AssistantEvent assistantEvent;
        static int s_seq = 0;
        assistantEvent.status = (int)event;
        assistantEvent.description = happ::SimpleSpeechAppEventHelper::to_string(event);
        assistantEvent.msg = msg;
        assistantEvent.sectionId = eventId;
        tAssistantEvent.publish(assistantEvent);
        if(event==happ::SimpleSpeechAppEvent::OfflineInstructionMatched)
        {
            ROS_WARN_NAMED(__FILENAME__,"OfflineInstructionMatched: %s",msg.c_str());
            homi_speech_interface::SIGCEvent sigcEvent;
            try
            {
                auto json = nlohmann::json::parse(msg);
                json["deviceId"] = myDeviceId;
                json["eventId"] = eventId;
                json["seq"] = s_seq++;
                sigcEvent.event = json.dump();
            }
            catch(const std::exception& e)
            {
                ROS_WARN_NAMED(__FILENAME__,"OfflineInstructionMatched %s",e.what());
            }
            tSIGCEvent.publish(sigcEvent);
        }else if(event==happ::SimpleSpeechAppEvent::Running)
        {
            ROS_INFO_NAMED(__FILENAME__,"Running");
            threadPoolPtr->enqueue([displayAssistantWakeupShPath](){
                try
                {
                    std::system(displayAssistantWakeupShPath.c_str());
                }
                catch(const std::exception& e)
                {
                    ROS_ERROR_NAMED(__FILENAME__,"%s",e.what());
                }
            });
            
        }else if (event==happ::SimpleSpeechAppEvent::Idle)
        {
            ROS_INFO_NAMED(__FILENAME__,"Idle");
            threadPoolPtr->enqueue([displayAssistantIdleShPath](){
                try
                {
                    std::system(displayAssistantIdleShPath.c_str());
                }
                catch(const std::exception& e)
                {
                    ROS_ERROR_NAMED(__FILENAME__,"%s",e.what());
                }
            });
        }
        else{}
    });
    if(g_speechApp==nullptr)
    {
        ROS_ERROR_NAMED(__FILENAME__,"SimpleSpeechAppFactory fail");
        return -1;
    }
    ros::spin();//回旋函数
    return 0;
}