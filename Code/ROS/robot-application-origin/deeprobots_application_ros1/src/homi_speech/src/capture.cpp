#include <ros/ros.h>
#include <homi_speech_interface/PCMStream.h>
#include <homi_speech/def.h>
#include <homi_speech/AlsaHelper.h>
#include <homi_speech/json.hpp> 
#include <string>
#include <thread>
#include <atomic>
std::atomic<bool> _needrestart;
int main(int argc, char *argv[])
{
    setlocale(LC_ALL, "");
    ros::init(argc, argv, "capture");
    ros::NodeHandle nh;
    ROS_INFO_NAMED(__FILENAME__,"capture start up");
    // std::string alsaCaptureDeviceName;
    std::string usbShPath;
    std::string usbName;
    std::string pcm_stream_topic;
    int channelNumb = 1;
    _needrestart = false;
    if(!nh.getParam(PARAM_ALSA_CAPTURE_SH_NAME,usbShPath)
        ||!nh.getParam(PARAM_ALSA_CAPTURE_SH_PARAM,usbName)
        ||!nh.getParam(PARAM_ALSACAPTURE_CHANNEL,channelNumb)
        ||!nh.getParam(PARAM_PCM_STREAM_TOPIC,pcm_stream_topic))
    {
        ROS_ERROR_NAMED(__FILENAME__,"read param error ,exit");
        return -1;
    }
    long long lcount = 0;
    ros::Timer timer = nh.createTimer(ros::Duration(5),[&lcount](const ros::TimerEvent & e){
        lcount++;
        ROS_INFO_NAMED(__FILENAME__,"idle ,run count =%lld s ...",(lcount*5));
        if(_needrestart)
        {
            ROS_WARN_NAMED(__FILENAME__,"restart ");
            exit(-1);
        }
        _needrestart = true;
    });
    ros::Publisher pushStream = nh.advertise<homi_speech_interface::PCMStream>(pcm_stream_topic,50);
    bool stop=false;
    auto mythread = std::thread([&stop,&pushStream,usbShPath,usbName,channelNumb](){
        homi_speech::AlsaHelperCapture capture;
        int cardId=-1;
        std::string alsaCaptureDeviceName="default";
        try
        {
            auto usbjson = nlohmann::json::parse(usbName);
            auto usb_name = usbjson["name"];
            for(auto e:usb_name)
            {
                auto nameTmp = e.get<std::string>();
                ROS_INFO_NAMED(__FILENAME__,"usbname=%s",nameTmp.c_str());
                if(nameTmp=="default")
                {
                    cardId = 0;
                    break;
                }
                cardId = homi_speech::getCardId(usbShPath,nameTmp);
                if(cardId>=0)
                {
                    alsaCaptureDeviceName = "plughw:" + std::to_string(cardId) + ",0";
                    break;
                }
            }
        }
        catch(const std::exception& e)
        {
            ROS_ERROR_NAMED(__FILENAME__,"%s",e.what());
            exit(-1);
        }
        if(cardId<0)
        {
            ROS_ERROR_NAMED(__FILENAME__,"cardId<0");
            exit(-1);
        }
        ROS_INFO_NAMED(__FILENAME__,"alsaCaptureDeviceName=%s channelNumb=%d",alsaCaptureDeviceName.c_str(),channelNumb);
        ROS_INFO_NAMED(__FILENAME__,"open1");
        capture.openAndConfig(alsaCaptureDeviceName,"S16_LE",channelNumb,16000);
        std::this_thread::sleep_for(std::chrono::milliseconds(250));
        capture.close();
        std::this_thread::sleep_for(std::chrono::milliseconds(250));
        capture.openAndConfig(alsaCaptureDeviceName,"S16_LE",channelNumb,16000);
        ROS_INFO_NAMED(__FILENAME__,"open2");
        auto periodSize = capture.getPeriodSize();
        auto periodBytes = capture.getPeriodBytes();
        unsigned int count=0;
        ROS_INFO_NAMED(__FILENAME__,"periodSize=%lu periodBytes=%u",periodSize,periodBytes);
        while(!stop)
        {
            try
            {
                count++;
                auto now = std::chrono::system_clock::now();
                auto ts = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
                _needrestart = false;
                auto captureDataPtr = std::make_shared<std::vector<unsigned char>>(periodBytes);
                auto readSize = capture.readi(captureDataPtr->data(),periodSize);               
                readSize = periodSize *2;
                std::vector<unsigned char> channelData(readSize);
                unsigned char *uiBuffer = captureDataPtr->data();
                auto buffer = channelData.data();
                for(auto i=0;i<periodSize;i++)
                {
                    buffer[2*i] = uiBuffer[2*i*channelNumb];
                    buffer[2*i+1] = uiBuffer[2*i*channelNumb+1];
                }
                
                
                homi_speech_interface::PCMStream msg;
                msg.ts = ts;
                msg.data = std::vector<unsigned char>(buffer,buffer+readSize);
                pushStream.publish(msg);

                if(count%(50*5)==0)
                {
                    ROS_INFO_NAMED(__FILENAME__,"update capture %d alsaCaptureDeviceName=%s",readSize,alsaCaptureDeviceName.c_str());
                }
            }
            catch(...)
            {
                ROS_WARN_NAMED(__FILENAME__,"capture reset");
                capture.close();
                capture.openAndConfig(alsaCaptureDeviceName,"S16_LE",channelNumb,16000);
                ROS_INFO_NAMED(__FILENAME__,"alsaCaptureDeviceName=%s",alsaCaptureDeviceName.c_str());
                periodSize = capture.getPeriodSize();
                periodBytes = capture.getPeriodBytes();                
            }
        }
    });
    ros::spin();
    stop = true;
    if(mythread.joinable())
    {
        mythread.join();
    }
    ROS_INFO_NAMED(__FILENAME__,"capture end");
    return 0;
}