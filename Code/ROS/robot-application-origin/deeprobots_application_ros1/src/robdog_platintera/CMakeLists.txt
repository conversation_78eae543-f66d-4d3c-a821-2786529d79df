cmake_minimum_required(VERSION 3.0.2)
project(robdog_platintera)
set(CMAKE_CXX_STANDARD 17)
#set(CMAKE_PREFIX_PATH "/usr/local/lib/cmake/TinyXML2")

find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  geometry_msgs
  image_transport
  std_msgs
  homi_speech_interface  
)

catkin_package()


if (${CMAKE_HOST_WIN32})
    if(CMAKE_CXX_FLAGS MATCHES "-m32")
        set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/win/x86)
    else()
        set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/win/x64)
    endif()
elseif(${CMAKE_HOST_UNIX})
    message("linux system")
    # Linux 平台特定处理逻辑
    if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "x86_64")
        message("support x86-64/AMD64")
        if(CMAKE_CXX_FLAGS MATCHES "-m32")
            message("m32")
            set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/x86)
        else()
            message("x64")
            set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/x86_64)
        endif()
    elseif(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "aarch64")
        message("no support x86-64/AMD64")
        set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/aarch64)
    endif()
elseif (${CMAKE_SYSTEM} MATCHES "FreeBSD|OpenBSD")
endif()


############ robdog_subpub_node ############
include_directories(
  ${PROJECT_INIT_PATH}/include
  include
  ${catkin_INCLUDE_DIRS}
  #${TINYXML2_INCLUDE_DIRS}
)

add_executable(
  robdog_subpub_node
  src/robdog_subpub.cpp
  src/robdog_subpub/robdog_subpub_node.cpp
  src/robdog_subpub/robdog_subpub_node.h
  src/robdog_subpub/RobotState.cpp
  src/robdog_subpub/RobotState.h
  src/robdog_subpub/audio_ctrl.cpp
  src/robdog_subpub/audio_ctrl.h
  src/robdog_subpub/litedb.cpp
  src/robdog_subpub/litedb.h
  src/robdog_subpub/read_map_point_config/read_map_point_cfg.cpp
  src/robdog_subpub/read_map_point_config/read_map_point_cfg.h
)

target_link_libraries(
  robdog_subpub_node 
  ${catkin_LIBRARIES}
  jsoncpp
  tinyxml2
  asound
  WebSocket
  pthread
  sqlite3
)

add_dependencies(
  robdog_subpub_node 
  ${${PROJECT_NAME}_EXPORTED_TARGETS}
  ${catkin_EXPORTED_TARGETS}
)

install(
  TARGETS robdog_subpub_node
  RUNTIME DESTINATION 
  ${CATKIN_PACKAGE_BIN_DESTINATION}
)