//
/*****websocket通讯服务端节点******/
/**********默认端口为19002********/
//

#include "robdog_subpub/robdog_subpub_node.h"
// #include "rclcpp/rclcpp.hpp"
#include "ros/ros.h"
#include <ros/package.h>
#include <log4cxx/logger.h> 
#include <ros/console.h>
#include <log4cxx/propertyconfigurator.h>
#include <iostream>
#include <chrono>
#include <string>
#include <csignal>  // 新增
#include <signal.h> // 新增
using namespace std;

void signal_handler(int signal) {
    switch (signal) {
        case SIGTSTP:
            std::cout << "Caught Ctrl+Z!" << std::endl;
            // 可以在这里执行一些操作，比如保存状态或暂停程序
            break;
        default:
            std::cout << "Caught " << signal << std::endl;
            // 处理其他信号
            break;
    }
}

//local_port = 43894"
//remote_port = 43893
//remote_ip = "*************"

int main(int argc, char **argv)
{
    // 注册信号处理函数
    struct sigaction sa_ctrl_c;
    //ctrl + c
    sa_ctrl_c.sa_handler = &signal_handler;
    sigemptyset(&sa_ctrl_c.sa_mask);
    sa_ctrl_c.sa_flags = 0;
    if (sigaction(SIGINT, &sa_ctrl_c, NULL) == -1) {
        std::cerr << "Failed to register signal handler" << std::endl;
        return 1;
    }
    ros::init(argc, argv, "robdog_subpub_node"); 
    ros::NodeHandle nh;
    auto node = std::make_shared<RobdogSubPub>(nh,"/home/<USER>/.config/config_robotdog.xml"); 

    std::string log4cxx_config;
    std::string package_path = ros::package::getPath("robdog_platintera");
    ROS_INFO("package path is %s",package_path.c_str());
    log4cxx_config=package_path+"/../launch_package/launch/sub_node_log.yaml";
    ROS_INFO("log4cxx_config path is %s",log4cxx_config.c_str());
    log4cxx::PropertyConfigurator::configure(log4cxx_config);
    setvbuf(stdout, NULL, _IOLBF, 4096);

    ROS_DEBUG("robdog_subpub init DEBUG"); // 输出初始化日志
    ROS_INFO("robdog_subpub init INFO"); // 输出初始化日志
    ROS_WARN("robdog_subpub init WARN"); // 输出初始化日志
    ROS_ERROR("robdog_subpub init ERROR"); // 输出初始化日志
    ROS_FATAL("robdog_subpub init FATAL"); // 输出初始化日志

    ros::Rate loop_rate(10); // 10 Hz
    /*while (ros::ok())
    {
        // 在这里可以添加其他ROS相关的操作
        // 例如，订阅话题、发布话题、处理服务等
        ros::spinOnce(); // 处理回调
        loop_rate.sleep(); // 等待直到下一个循环
    }
    */
    ros::spin();
    return 0;
}