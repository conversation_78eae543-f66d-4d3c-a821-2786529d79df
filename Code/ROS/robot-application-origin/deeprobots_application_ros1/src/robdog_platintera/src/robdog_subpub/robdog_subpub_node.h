#include <ros/ros.h>
#include <geometry_msgs/Twist.h> 
#include <geometry_msgs/Pose.h>
#include <std_msgs/String.h>
#include <tf/transform_broadcaster.h>
#include <tf/transform_listener.h>
#include <tf/LinearMath/Quaternion.h>

#include <homi_speech_interface/SIGCEvent.h>
#include <homi_speech_interface/RobdogAction.h>
#include <homi_speech_interface/SIGCData.h>
#include <homi_speech_interface/NetCtrl.h>
#include <homi_speech_interface/ProprietySet.h>
#include <homi_speech_interface/ProperToApp.h>
#include <homi_speech_interface/ContinueMove.h>

#include <homi_speech_interface/AssistantEvent.h>
#include "homi_speech_interface/AssistantSpeechText.h" 
#include "homi_speech_interface/AssistantAbort.h" 

#include "deep_cmd.h"
#include <jsoncpp/json/json.h>
#include <fstream>
#include "RobotState.h"
#include "audio_ctrl.h"
#include <boost/asio.hpp>
#include <functional>
#include <thread>
#include "litedb.h"
enum HomiRobotStatus {
  ROBDOG_STATUS_STATE                            = 0X00000000,  //机器狗的状态开始标志位
  ROBDOG_STATUS_SITDOWM,                                        //趴下状态
  ROBDOG_STATUS_FORWARD_JUMPPING,                               //正在执行向前跳
  ROBDOG_STATUS_READYTOSTAND,                                   //准备起立状态
  ROBDOG_STATUS_STANDING,                                       //正在起立状态
  ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT,                //力控状态（静止站立）且步态为平地低速步态
  ROBDOG_STATUS_BODYROTWISTING_BY_AXISCOMMAND,                  //正在以平地低速步态踏步或正在根据轴指令扭动身体
  ROBDOG_STATUS_BODYROTWISTING,                                 //正在执行扭身体
  ROBDOG_STATUS_BODYJUMPPING,                                   //正在执行扭身跳
  ROBDOG_STATUS_FORCE_CTRL_GEN_OBS_GAIT,                        //力控状态（静止站立）且步态为通用越障步态
  ROBDOG_STATUS_GEN_OBS_GAIT_STEPPING,                          //正在以通用越障步态踏步
  ROBDOG_STATUS_FORCE_CTRL_LEVEL_MIDSPEED_GAIT,                 //力控状态（静止站立）且步态为平地中速步态
  ROBDOG_STATUS_LEVEL_MIDSPEED_GAIT_STEPPING,                   //正在以平地中速步态踏步
  ROBDOG_STATUS_FORCE_CTRL_LEVEL_HIGHSPEED_GAIT,                //力控状态（静止站立）且步态为平地高速步态
  ROBDOG_STATUS_LEVEL_HIGHSPEED_GAIT_STEPPING,                  //正在以平地高速步态踏步
  ROBDOG_STATUS_FORCE_CTRL_GROUND_GRIPPING_GAIT,                //力控状态（静止站立）且步态为抓地越障步态
  ROBDOG_STATUS_GROUND_GRIPPING_GAIT_STEPPING,                  //正在以抓地越障步态踏步
  ROBDOG_STATUS_MOONWALKING,                                    //正在执行太空步
  ROBDOG_STATUS_FORCE_CTRL_HIGHSTEP_OBS_GAIT,                   //力控状态（静止站立）且步态为高踏步越障步态
  ROBDOG_STATUS_HIGHSTEP_OBS_GAIT_STEPPING,                     //正在以高踏步越障步态踏步
  ROBDOG_STATUS_PRONEING,                                       //正在趴下状态
  ROBDOG_STATUS_LOSS_CTRL,                                      //失控保护状态
  ROBDOG_STATUS_ATTITUDE_ADJUST_MODE,                           //姿态调整状态
  ROBDOG_STATUS_ROLL_OVERING,                                   //正在执行翻身
  ROBDOG_STATUS_RESET,                                          //回零状态
  ROBDOG_STATUS_BACKFLIP,                                       //正在执行后空翻
  ROBDOG_STATUS_HELLO                                           //正在执行打招呼
};

using StateKey = std::tuple<int32_t, int32_t, int32_t>;
using StateValue = std::tuple<std::string, HomiRobotStatus>;
static std::map<StateKey, StateValue> stateQueryTableString = {
    {{1, 0, 0}, {"趴下状态", ROBDOG_STATUS_SITDOWM}},
    {{1, 0, 11}, {"正在执行向前跳", ROBDOG_STATUS_FORWARD_JUMPPING}},   
    {{4, 0, 0}, {"准备起立状态", ROBDOG_STATUS_READYTOSTAND}},
    {{5, 0, 0}, {"正在起立状态", ROBDOG_STATUS_STANDING}},
    {{6, 0, 0}, {"力控状态（静止站立）且步态为平地低速步态", ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT}},
    {{6, 0, 1}, {"正在以平地低速步态踏步或正在根据轴指令扭动身体", ROBDOG_STATUS_BODYROTWISTING_BY_AXISCOMMAND}}, 
    {{6, 0, 2}, {"正在执行扭身体", ROBDOG_STATUS_BODYROTWISTING}},   
    {{6, 0, 4}, {"正在执行扭身跳", ROBDOG_STATUS_BODYJUMPPING}},
    {{6, 2, 0}, {"力控状态（静止站立）且步态为通用越障步态", ROBDOG_STATUS_FORCE_CTRL_GEN_OBS_GAIT}},
    {{6, 2, 1}, {"正在以通用越障步态踏步", ROBDOG_STATUS_GEN_OBS_GAIT_STEPPING}},
    {{6, 4, 0}, {"力控状态（静止站立）且步态为平地中速步态", ROBDOG_STATUS_FORCE_CTRL_LEVEL_MIDSPEED_GAIT}},
    {{6, 4, 1}, {"正在以平地中速步态踏步", ROBDOG_STATUS_LEVEL_MIDSPEED_GAIT_STEPPING}},
    {{6, 5, 0}, {"力控状态（静止站立）且步态为平地高速步态", ROBDOG_STATUS_FORCE_CTRL_LEVEL_HIGHSPEED_GAIT}},
    {{6, 5, 1}, {"正在以平地高速步态踏步", ROBDOG_STATUS_LEVEL_HIGHSPEED_GAIT_STEPPING}},
    {{6, 6, 0}, {"力控状态（静止站立）且步态为抓地越障步态", ROBDOG_STATUS_FORCE_CTRL_GROUND_GRIPPING_GAIT}},
    {{6, 6, 1}, {"正在以抓地越障步态踏步", ROBDOG_STATUS_GROUND_GRIPPING_GAIT_STEPPING}},
    {{6, 12, 1}, {"正在执行太空步", ROBDOG_STATUS_MOONWALKING}},
    {{6, 13, 0}, {"力控状态（静止站立）且步态为高踏步越障步态", ROBDOG_STATUS_FORCE_CTRL_HIGHSTEP_OBS_GAIT}},
    {{6, 13, 1}, {"正在以高踏步越障步态踏步", ROBDOG_STATUS_HIGHSTEP_OBS_GAIT_STEPPING}},
    {{7, 0, 0}, {"正在趴下状态", ROBDOG_STATUS_PRONEING}},
    {{8, 0, 0}, {"失控保护状态", ROBDOG_STATUS_LOSS_CTRL}},
    {{9, 0, 0}, {"姿态调整状态", ROBDOG_STATUS_ATTITUDE_ADJUST_MODE}},
    {{11, 0, 0}, {"正在执行翻身", ROBDOG_STATUS_ROLL_OVERING}},
    {{17, 0, 0}, {"回零状态", ROBDOG_STATUS_RESET}},
    {{18, 0, 0}, {"正在执行后空翻", ROBDOG_STATUS_BACKFLIP}},
    {{20, 0, 0}, {"正在执行打招呼", ROBDOG_STATUS_HELLO}}  
};

//定义来自CONTROL_NODE的命令
enum cmdFromNode{
    POWER_LEVEL_FROM_NODE=1,
    POWER_CHARGE_FROM_NODE,
    WIFI_NAME_FROM_NODE,
    IS_WIFI_CONNECT,
};
using boost::asio::ip::udp;
struct CommandHead {
  uint32_t code;
  uint32_t size;
  uint32_t type;
};
const uint32_t kDataSize = 256;
struct Command{
  CommandHead head;
  uint32_t data[kDataSize];
};

class UDPServer {
public:
    using CallbackType = std::function<void(const std::string&, std::size_t)>;
    UDPServer(int port, CallbackType callback): io_context_(), socket_(io_context_, udp::endpoint(udp::v4(), port)), callback_(callback) {start_receive();}
    void run() {io_context_.run();}
    void send(const char* data, std::size_t length, const std::string& host, int port) {
        udp::endpoint remote_endpoint(boost::asio::ip::address::from_string(host), port);
        socket_.async_send_to(boost::asio::buffer(data, length), remote_endpoint,
            [](boost::system::error_code ec, std::size_t bytes_sent) {
                if (ec) 
                    std::cerr << "Error sending data: " << ec.message() << std::endl;
            });
    }
private:
    void start_receive() {
        socket_.async_receive_from(
            boost::asio::buffer(recv_buffer_), remote_endpoint_,
            [this](boost::system::error_code ec, std::size_t bytes_recvd) {
                if (!ec && bytes_recvd > 0) {
                    std::string data(recv_buffer_.data(), bytes_recvd);
                    callback_(data, bytes_recvd);
                }
                start_receive();
            });
    }
    boost::asio::io_context io_context_;
    udp::socket socket_;
    udp::endpoint remote_endpoint_;
    std::array<char, 4096> recv_buffer_;
    CallbackType callback_;
};


struct EthCommand{
  uint32_t code;
  union{
    uint32_t value;
    uint32_t paramters_size;
  };
  struct {
    uint32_t type  : 8;
    uint32_t count : 24;
  };
};
class RobdogSubPub {
public:
    RobdogSubPub(ros::NodeHandle& nh,const std::string& configPath);
    ~RobdogSubPub();

    void readMappingPoints();

    void sleepForDuration(double seconds);  // 暂停函数
    // void loadConfig(const std::string& configFilePath); 
    // void loadConfig(); 

    // 回调函数，当订阅的消息到达时被调用
    void robctrlCallback(const homi_speech_interface::SIGCEventPtr& msg);
    // 发布者功能：发布速度命令
    void publishVelocity(const geometry_msgs::Twist& velocity);
    // 发布特定运动信息
    void publishAction(const homi_speech_interface::RobdogAction& robdogAction);

    void deepStatusCallback(const homi_speech_interface::ProprietySetPtr& msg);
    void appCtrlCallBack(const homi_speech_interface::SIGCEventPtr& msg);

    // void startTimer();
    // 设置发送次数的函数
    void setTotalCount(int count);
    // 公开的函数用于手动调用定时器回调
    void triggerTimerCallback();
    void timerCallback(const ros::TimerEvent&);
    void timerRobotPoseCallback(const ros::TimerEvent&);
    void Proceationtype(int actiontype, int step);
    void deep_ctl(int cmdID,int cmdValue); //灯光、语音、动作等交互
    void _handle_UDP_data(char *data,size_t length);

    std::string get_robot_properties(Json::Value &inValue);
    std::string get_connect_info_request(Json::Value &inValue);
    void setProperties(const Json::Value& request);
    void publishStatusCtrl(int cmd,int value,int exvalue,std::string& exmsg);
    void publishStatusCtrl(int cmd,int value,int exvalue);
    void publishProperties2APP(const ros::TimerEvent&);
    void navPositionCallback(const geometry_msgs::Pose::ConstPtr& msg);
    void navStatusCallback(const std_msgs::String::ConstPtr& msg);
    void navStatusNotifyCallback(const std_msgs::String::ConstPtr& msg);
    bool isAtTarget (const homi_speech_interface::SIGCEvent& msg);
    void playAudio (const std::string& filePath);
    void moveToTarget (const homi_speech_interface::SIGCEvent& msg);
    void moveToTargetAndPlayAudio (const homi_speech_interface::SIGCEvent& msg);
    void callHelperPhoto();
    void takePhotoService();
    void checkTargetStatus(const ros::TimerEvent& e);
    void actionFollow(int status); //0-停止跟随 1-开启跟随 2-开启UWB跟随
    void timerRobotBroadcastCallback(const ros::TimerEvent&); 
    // 智能播报相关
    void BrocastIfAbortCallBack(const homi_speech_interface::AssistantEventPtr& msg);
    void SendBrocastCallback(const ros::TimerEvent&);
    void sendStringToBrocast(const std::string& message);
    void RobotBroadcastStatusToPlat(int status);
    void checkStatusWatchdog(const ros::TimerEvent&);
    void server_run() {server_.run();}
    void sendMsgforFetch(const char* data, std::size_t length, const std::string& host, int port) {server_.send(data, length, host, port);}
    void timerSendMsgCallback(const ros::TimerEvent&);
    void update_emergency_contacts(int updateType,const std::string& entityId, const Json::Value& emergencyContact) ;

    void devAlarmReportCallback(const std_msgs::String::ConstPtr& msg);


private:
    ros::NodeHandle nh_;  // ROS节点句柄
    ros::Publisher actionCmd_pub;   // 发布机器狗特定运动控制消息
    ros::Publisher continueMoveCmd_pub; // 发布持续运动信息
    ros::Subscriber app_sub;
    ros::Subscriber velCmd_sub_;  // 订阅者对象订阅平台消息   /homi_speech/sigc_event_topic
    ros::Subscriber deepCtrl_sub_;      //订阅者对象，订阅本体状态的消息，来自于UDP数据包。
    ros::Publisher velCmd_pub;   // 发布者对象
    ros::Publisher status_pub_;   // 发布者对象，通过自定义命令，控制本体运动、模式等。
    ros::Publisher prope2app_pub; // 向APP发送设备状态消息
    UDPServer server_;
    HomiRobotStatus curState_;
    void handle_received_data(const std::string& data, std::size_t bytes_received) {
        if (bytes_received < sizeof(CommandHead)) {return;}
        CommandHead cmdh;
        std::memcpy(&cmdh, data.data(), sizeof(CommandHead));
        if(cmdh.code==0x11050F01){robotSt.setBatteryLevel(cmdh.size);}
        else if (cmdh.code == 0x11050F02 && cmdh.type == 1) {
            // std::cout << "Received robot status data." << std::endl;
            if (bytes_received < sizeof(CommandHead) + cmdh.size) {
                std::cerr << "Received Command packet is shorter than expected" << std::endl;
                return;
            }
            Command cmd;
            std::memcpy(&cmd, data.data(), sizeof(CommandHead) + cmdh.size);
	    // std::cout << "cmd.head.code: " << cmd.head.code  << std::endl;
	    // std::cout << "cmd.head.type: " << cmd.head.type  << std::endl;
	    // std::cout << "cmd.head.size: " << cmd.head.size  << std::endl;
            for (uint32_t i = 0; i < cmdh.size / sizeof(uint32_t); ++i) {
                cmd.data[i] = cmd.data[i];
		// std::cout << "cmd.data Index: " << i << ", Value: " << cmd.data[i] << std::endl;
            }
            if (cmdh.size == 12) {
                auto cur_state_tuple = std::make_tuple(
                static_cast<int32_t>(cmd.data[0]),
                static_cast<int32_t>(cmd.data[1]),
                static_cast<int32_t>(cmd.data[2])
            );
            auto it = stateQueryTableString.find(cur_state_tuple);
            if (it != stateQueryTableString.end()) {
		// std::cout << "Current Robot State: "<< std::get<0>(it->second)  << std::get<1>(it->second) << std::endl;
                curState_=std::get<1>(it->second);
		// std::cout << "curState_ is ."<<curState_ << std::endl;
            } else {
                std::cout << "Current Robot State: " << "Unknown State" << std::endl;
            }
        } else {
            std::cout << "Unknown packet format" << std::endl;
        }
      }
    }
    std::thread server_thread_;
    ros::Timer timer_2; 
    ros::Timer timer_; // 设置定时器
    ros::Timer robPoseStatusTimer_;
    ros::Timer robMoveStatusTimer_;
    ros::Timer robActionCheckTimer_;
    ros::Timer timerDog ;
    ros::Timer timerFetchMsg;
    /************智能播报相关********************* */
    ros::Subscriber brocast_sub;
    ros::Timer timer_brocast;
    ros::ServiceClient brocast_client;
    ros::ServiceClient brocast_abort_client; // 向语音发送停止播报请求
    ros::Time lastMoveMessageTime; // 更新最后收到action消息的时间
    bool watchDogMonitor=false; // 启动监测
    /************************************************ */

    // 设置初始状态为未到达目标
    bool at_target_ = false;
    // 创建定时器，每1秒检查一次
    // timer_ = nh.createTimer(ros::Duration(1), &RobdogSubPub::checkTargetStatus, this);

    /************感知主机算法模块********************* */
    ros::Publisher actionPlanningMove_pub;    // 发布机器狗固定点位坐标到感知主机
    ros::Subscriber navPosition_sub_;         // 从感知主机订阅者机器人实时位置
    ros::Publisher mappingControl_pub;        // 地图更新给感知主机
    ros::Subscriber navStatus_sub_;        // 地图更新给感知主机
    ros::Subscriber devAlarmRep_sub_;        // 地图更新给感知主机
    ros::Publisher publishVirtualWall;
    /************************************************ */

    Json::Value config;
    ros::ServiceClient platform_client; //平台请求者
    ros::ServiceClient app_client;      //Andlink的Server节点
    ros::ServiceClient net_client;      //网络设置客户端
    RobotState robotSt;
    // 步进次数
    int total_count_ = 0;  // 总共发送的次数
    int send_count_ = 0;   // 已发送的次数
    // 智能播报次数
    int brocast_send_count_ = 0;
    int brocast_total_count_ = 0;
    std::string brocast_text; // 当前智能播报的文本

    // 点位信息
    //std::string PHOTO_POINT = "{\"points\": [{\"x\": 127.0, \"y\": 133.0, \"angle\": 98.0}]}";
    //std::string PRESS_POINT = "{\"points\": [{\"x\": 103.0, \"y\": 97.0, \"angle\": 84.0}]}";

    geometry_msgs::Twist current_twist_msg_;  // 保存的消息（要传给控制节点的速度【语音控制】）
    homi_speech_interface::ContinueMove current_continue_msg_; // 要传给控制节点的持续移动信息【摇杆控制】
    ros::Time last_heartbeat_time;  // 上次接收心跳的时间
    std::shared_ptr<AudioPlayer> audioCtrl;
    // 用到的yaml的数据
    double fspeed_x = 0.6;
    double fspeed_y = 0.6;
    double fspeed_z = 0.5; // 大约是15度
    int timer_interval = 1;
    double resting_time = 0.5;
    bool  move_status_flag = false;
    int  expresstion_count = 0;

    std::string execId="";

    std::map<std::string,int> sportModeMap={
        {"walk",DEEP_CMD_ACTION_WALK},
        {"run",DEEP_CMD_ACTION_RUN},
        {"stairClimbe",DEEP_CMD_ACTION_STAIR},
        {"climbe",DEEP_CMD_ACTION_CLIMBE},
        {"traction",DEEP_CMD_ACTION_WALK},
        {"emergencyStop",DEEP_CMD_ACTION_SOFTSTOP}
    };  //平台运动模式字段

};
