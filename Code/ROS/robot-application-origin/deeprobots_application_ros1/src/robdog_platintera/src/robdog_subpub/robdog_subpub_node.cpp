#include "libWebSocket.h"
#include "robdog_subpub_node.h"
#include "ros/ros.h"
#include "std_msgs/String.h"
#include <cmath> // For std::abs and std::fabs
#include <filesystem>
#include <fstream>
#include <geometry_msgs/Twist.h>
#include <homi_speech_interface/AssistantAbort.h>
#include <homi_speech_interface/AssistantTakePhoto.h>
#include <homi_speech_interface/IotControl.h>
#include <iostream>
#include <jsoncpp/json/json.h>
#include <random>
#include <string>
#include <tf/transform_datatypes.h>
#include <thread>
#include <vector>
// #include <homi_speech_interface/AssistantQuiet.h>
#include <atomic>
#include <chrono>
#include <homi_speech_interface/SetWakeEvent.h>
#include <iostream>
#include <thread>

#include "read_map_point_config/read_map_point_cfg.h"

using namespace std;
using namespace WS;

#define WEBSOCKET_CON_TIMEOUT 30 // webSocket 连接超时时间 30s

/*自主导航的步态，下发多点任务时带上
"option": {
    "even_low_speed": False,      // 平地低速
    "even_medium_speed": False,   // 平地中速
    "uneven_high_step": False,    // 越障高速
    "even_rl": False,             // 平地学习
    "uneven_rl": False            // 越障学习
}*/

#define PHOTO_POINT                                                            \
  "{\"points\": [{\"x\": 471.0, \"y\": 191.0, \"angle\": 105.5}]}"
#define PRESS_POINT                                                            \
  "{\"points\": [{\"x\": 457.0, \"y\": 242.0, \"angle\": -72.0}, {\"x\": "     \
  "481.0, \"y\": 314.0, \"angle\":19.3}, {\"x\": 542.0, \"y\": 295.0, "        \
  "\"angle\": 102.0}, {\"x\": 519.0, \"y\": 231.0, \"angle\": 111.8}]}"

#define FAMILY_MOVE_POINT                                                            \
  "{\"points\": [{\"x\": 471.0, \"y\": 191.0, \"angle\": 105.5}]}"


#define CANCEL_POINT                                                           \
  "{\"points\": [{\"x\": 124.0, \"y\": 160.0, \"angle\": -86.0}]}"

/************04狗子公司建图环境点位信息***********
# 基准点 "{\"points\": [{\"x\": 457.0, \"y\": 242.0, \"angle\": -72.0}]}"
# 取快递 "{\"points\": [{\"x\": 457.0, \"y\": 242.0, \"angle\": -72.0}, {\"x\":
481.0, \"y\": 314.0, \"angle\":19.3, \"option\": \"uneven_high_step\"}, {\"x\":
542.0, \"y\": 295.0, \"angle\": 102.0, \"option\": \"uneven_rl\"}]}" # 拍照点
"{\"points\": [{\"x\": 471.0, \"y\": 191.0, \"angle\": 105.5}]}"
************04狗子公司建图环境点位信息***********/

unsigned long getCurrentTimeStramp() {
    auto now = std::chrono::high_resolution_clock::now();
    auto time_now = std::chrono::high_resolution_clock::to_time_t(now);
    std::tm now_tm = *std::localtime(&time_now);
    return std::mktime(&now_tm);
}
unsigned long currentTimeStramp_ = getCurrentTimeStramp();
bool bConnected_ = false;
string strConnectUrl_ = "ws://192.168.1.110:19002";
int nConnectIndex_ = 0;

void notifyWsMsgCallback(void *handle, const char *msg, int index) {
    nConnectIndex_ = index;
    std::cout << "notifyWsMsgCallback: " << msg << std::endl;
    currentTimeStramp_ = getCurrentTimeStramp();

    RobdogSubPub *parent = (RobdogSubPub *)handle;
    Json::Reader reader;
    Json::Value value;
    if (false == reader.parse(msg, value)) {
        return;
    }
    if (!value["type"].isNull()) {
        string strType = value["type"].asString();
        if ("connect_success" == strType) {
            bConnected_ = true;
            std::this_thread::sleep_for(std::chrono::milliseconds(20));
            Json::Value value;
            Json::Value params;
            value["client_type"] = "launcher";
            value["action"] = "success";
            WS_Send(value.toStyledString().c_str(), nConnectIndex_);
        }
    }
    if (!value["action"].isNull()) {
        string action = value["action"].asString();
        //感知主机下发的机器人移动的角速度和线速度
        if (action == "motionArcWithObstacles") {
            Json::Value params = value["params"];
            double lineSpeed = params["lineSpeed"].asDouble();
            double angularSpeed = params["angularSpeed"].asDouble();
            geometry_msgs::Twist twist;
            twist.linear.x = lineSpeed;
            twist.angular.z = angularSpeed;
            if (parent) {
                parent->publishVelocity(twist);
            }
        }
    }
}

std::string execute_result_response(Json::Value &inValue, std::string execid, bool success,Json::Value &data) {
    Json::Value response = inValue;
    Json::Value body;
    response["domain"] = "SKILL_EXECUTE";
    response["event"] = "skill_execute_callback";
    body["execId"] = execid;
    body["success"] = success;
    body["data"] = data;
    response["body"] = body;
    // 将 Json::Value 转换成字符串
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    return jsonString;
}

void videoRecordThreadFunction(int len,const std::string& videoWriteUrl,const std::string& videoReadUrl,const std::string& execId,ros::ServiceClient platform_client,std::string strMsg) {

    Json::Reader reader;
    Json::Value inValue;
    reader.parse(strMsg, inValue);
                
    std::string command = "/mine/robot-application/deeprobots_application_ros1/startVideo.sh "+to_string(len)+" /tmp/video.mp4 \"" + videoWriteUrl + "\"" ;
    ROS_INFO("videoRecord command:%s",command.c_str());
    int exit_code = std::system(command.c_str());

    homi_speech_interface::SIGCData resMsg;
    Json::Value data;
    // Json::Value inValue;
    data["videoReadUrl"]=videoReadUrl;
    bool success = false;
    if (exit_code == -1) {
        ROS_ERROR("Error executing command");
    } else {
        int status = WEXITSTATUS(exit_code);
        ROS_INFO("Command exited with status: %d",status);
        if(status == 0){
            success = true;
        }
        else {
            success = false;
        }
    }
    resMsg.request.data = execute_result_response(inValue,execId,success,data);
    ROS_INFO_STREAM("Send res to Server33333, res is " << resMsg.request.data);
    if (platform_client.call(resMsg))
        ROS_INFO("Response from server: %d", resMsg.response.errorCode);
    else
        ROS_ERROR("Failed to call service Service_demo errorCode:%d",
        resMsg.response.errorCode);
}

RobdogSubPub::RobdogSubPub(ros::NodeHandle &nh, const std::string &configPath) : nh_(nh), robotSt(configPath), server_(8080, std::bind(&RobdogSubPub::handle_received_data, this, std::placeholders::_1, std::placeholders::_2))
 {
    // WS服务启动
    WS_Init(EN_WS_ClIENT, 19002);
    //设置接受msg的回调函数
    WS_SetMsgCallback(notifyWsMsgCallback, this);
    WS_Connect(strConnectUrl_.c_str());
    // 订阅平台命令话题
    velCmd_sub_ = nh.subscribe("/homi_speech/sigc_event_topic", 1000, &RobdogSubPub::robctrlCallback, this); // 接收到平台发来的事件之后调用robctrlCallback函数
    // ROS_INFO("velCmd_sub subscribe topic /homi_speech/sigc_event_topic");
    app_sub = nh.subscribe("/homi_speech/sigc_event_topic_APP", 1000, &RobdogSubPub::appCtrlCallBack, this); // 接收到app发来的事件之后调用appCtrlCallBack函数
    // ROS_INFO("app_sub subscribe topic /homi_speech/sigc_event_topic_APP");
    deepCtrl_sub_ = nh.subscribe("/deep_udp_ctrl/status_report", 1, &RobdogSubPub::deepStatusCallback, this); // 接收到控制节点的消息后回调，写状态（主要是电量,充电状态等主动获取的状态）
    // ROS_INFO("deepCtrl_sub_ subscribe topic /deep_udp_ctrl/status_report");
    // 向狗子发布速度命令话题（robot_move）
    velCmd_pub = nh.advertise<geometry_msgs::Twist>("/catch_turtle/ctrl_instruct", 1);
    // 向狗子发送特定运动指令消息（robot_action）
    actionCmd_pub = nh.advertise<homi_speech_interface::RobdogAction>("/catch_turtle/action_type", 1);
    // 向狗子发送持续运动信息（robot_move）
    continueMoveCmd_pub = nh.advertise<homi_speech_interface::ContinueMove>("/catch_turtle/continue_move", 1000);

    /**********感知算法交互模块************************* */
    // 向感知主机下发移动点位
    actionPlanningMove_pub = nh.advertise<std_msgs::String>("/navigation_control", 10); // 发布机器狗固定点位坐标到感知主机
    mappingControl_pub = nh.advertise<std_msgs::String>("/mapping_control", 10); // 地图更新给感知主机
    navPosition_sub_ = nh.subscribe("/navigation_position", 10, &RobdogSubPub::navPositionCallback, this); // 从感知主机订阅者机器人实时位置
    navStatus_sub_ = nh.subscribe("/navigation_status", 10, &RobdogSubPub::navStatusCallback, this); // 感知主机上报的地图
    // 虚拟墙
    publishVirtualWall = nh.advertise<std_msgs::String>("/virtual_wall_control", 10);
    /*********************************** */

    // 平台服务的客户端
    platform_client = nh.serviceClient<homi_speech_interface::SIGCData>("/homi_speech/sigc_data_service"); // 上发给平台的消息
    app_client = nh.serviceClient<homi_speech_interface::SIGCData>("/homi_speech/sigc_data_service_APP");
    net_client = nh.serviceClient<homi_speech_interface::NetCtrl>("/homi_speech/network_service");
    // 向APP发送设备状态消息
    prope2app_pub = nh.advertise<homi_speech_interface::ProperToApp>("/catch_turtle/prope_toapp", 1);
    // 定时器，1秒触发一次
    timer_2 = nh_.createTimer(ros::Duration(1), &RobdogSubPub::publishProperties2APP, this, false);
    // 定时器，一秒触发一次
    timer_ = nh_.createTimer(ros::Duration(timer_interval), &RobdogSubPub::timerCallback, this, false);
    robPoseStatusTimer_ = nh_.createTimer(ros::Duration(1), &RobdogSubPub::timerRobotPoseCallback, this, false);
    timerFetchMsg=nh_.createTimer(ros::Duration(0.1), &RobdogSubPub::timerSendMsgCallback, this, false);
    server_thread_=std::thread(&RobdogSubPub::server_run, this);
    // *********************************************/
    // 智能播报服务的客户端【上传播报文本】
    brocast_client = nh.serviceClient<homi_speech_interface::AssistantSpeechText>("/homi_speech/helper_assistant_speech_text_service");
    // 请求语音助手打断当前正在播放的内容
    brocast_abort_client = nh.serviceClient<homi_speech_interface::AssistantAbort>("/homi_speech/helper_assistant_abort_service");
    timer_brocast = nh.createTimer(ros::Duration(40.0), &RobdogSubPub::SendBrocastCallback,this);  // 40s触发一次
    bool timer_broadcast_running = false; // 定时器的开关状态
    // 语音助手下发的是否播报被打断指令
    brocast_sub = nh.subscribe("/homi_speech/speech_assistant_status_topic", 100, &RobdogSubPub::BrocastIfAbortCallBack, this); // homi_speech_interface::AssistantEvent

    // 发布状态信息话题
    status_pub_ = nh.advertise<homi_speech_interface::ProprietySet>("/deep_udp_ctrl/status_ctrl", 1);
    // 发布状态信息话题【暂时还没用到】
    // status_pub_ = nh.advertise<std_msgs::String>("/deep_udp_ctrl/status", 1);
    // // 发布一个话题
    timerDog = nh.createTimer(ros::Duration(0.1), &RobdogSubPub::checkStatusWatchdog, this);
    lastMoveMessageTime = ros::Time::now();
    readMappingPoints();
    //订阅告警消息
    devAlarmRep_sub_ = nh.subscribe("/device_alarm_report", 10, &RobdogSubPub::devAlarmReportCallback, this); // 感知主机上报的地图

}

RobdogSubPub::~RobdogSubPub() {
    server_thread_.join();
}

void RobdogSubPub::readMappingPoints() {
    std::string strMappingPath = "/home/<USER>/.config/map_points.json";
    ReadMapCfg::getInstance().loadConfig(strMappingPath);

    // std::string strPhonePoints =
    // ReadMapCfg::getInstance().getPhonePoints("6361752000000014"); std::string
    // strPressPoints =
    // ReadMapCfg::getInstance().getPressPoints("6361752000000014");

    // ROS_INFO("strPhonePoints: %s", strPhonePoints.c_str());
    // ROS_INFO("strPressPoints: %s", strPressPoints.c_str());
}

// 暂停函数
void RobdogSubPub::sleepForDuration(double seconds) {
    ros::Duration(seconds).sleep();
}

// void RobdogSubPub::loadConfig(){
//      // 读取json里的数据
//     std::ifstream configFile("src/robdog_platintera/src/config.json",
//     std::ifstream::binary); if (!configFile.is_open()) {
//         ROS_ERROR(".");
//     }
//     // Json::Value config;
//     Json::CharReaderBuilder readerBuilder;
//     std::string errs;

//     if (!Json::parseFromStream(readerBuilder, configFile, &config, &errs)) {
//         ROS_ERROR("Failed to parse configuration file: %s", errs.c_str());
//     }
// }

void RobdogSubPub::setTotalCount(int count) {
    total_count_ = count;
    send_count_ = 0; // 重置已发送次数
}

void RobdogSubPub::triggerTimerCallback() {
    // 直接调用定时器回调函数
    ROS_INFO("Timer Start");
    timer_.start();
    ros::TimerEvent event;
    timerCallback(event);
}

void RobdogSubPub::timerCallback(const ros::TimerEvent &) {
    if (send_count_ < total_count_) {
        publishVelocity(current_twist_msg_);
        ++send_count_; // 增加了定时器次数
        ROS_INFO("Pub times: %d, Total Count_: %d", send_count_, total_count_);
    } else {
        // 完成发送后停止定时器
        timer_.stop();
        send_count_ = 0;
    }
}
void RobdogSubPub::timerSendMsgCallback(const ros::TimerEvent &) {
    char buffer[sizeof(CommandHead)];
    CommandHead cmd = {0x0901, 0, 1};
    std::memcpy(buffer, &cmd, sizeof(CommandHead));
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    sendMsgforFetch(buffer, sizeof(CommandHead),"192.168.1.120", 43893);
}
void RobdogSubPub::timerRobotPoseCallback(const ros::TimerEvent &) {
    Json::Value body = robotSt.getCurRobotPose();
    if (body.isNull()) {
        // ROS_INFO("timerRobotPoseCallback: no position"); //【发的太频繁先注释】
        return;
    }
    ROS_INFO("timerRobotPoseCallback: has position");

    Json::Value response;
    response["deviceId"] = robotSt.getDeviceId();
    response["domain"] = "DEVICE_INTERACTION";
    response["event"] = "point_report";
    response["eventId"] = "robdog_plat_" + to_string(getCurrentTimeStramp());
    response["seq"] = 0;
    response["body"] = body;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    ROS_INFO("cmd : %s", jsonString.c_str());
    homi_speech_interface::SIGCData resMsg;
    resMsg.request.data = jsonString;
    ROS_INFO_STREAM("Send res to Server, res is " << resMsg.request.data);
    if (platform_client.call(resMsg)) {
        ROS_INFO("Response from server: %d", resMsg.response.errorCode);
    } else {
        ROS_ERROR("Failed to call service Service_demo errorCode:%d", resMsg.response.errorCode);
    }
}

void RobdogSubPub::deepStatusCallback(
    const homi_speech_interface::ProprietySetPtr &msg) {
    ROS_INFO("Received msg form robdog_ctrl Node.cmd is :%d,value is:%d", msg->cmd, msg->value);
    switch (msg->cmd) {
        case POWER_LEVEL_FROM_NODE:
            robotSt.setBatteryLevel(msg->value);
            robotSt.setBatteryStatus(msg->exvalue);
            break;
        case WIFI_NAME_FROM_NODE:
            robotSt.setWifiName(msg->exmsg);
            break;
        default:
            break;
    }
    return;
}

std::string RobdogSubPub::get_connect_info_request(Json::Value &inValue) {
    Json::Value response = inValue;
    Json::Value body;
    Json::Value data;
    response["event"] = "connect_info_response";
    data["status"] = robotSt.getUserConnectStatus();
    data["phone"] = robotSt.getUserPhoneNumber();
    data["lastConnectTs"] = Json::Int64(robotSt.getTimeStamp());
    ROS_INFO("robotSt.getTimeStamp=%lld", robotSt.getTimeStamp());
    body["data"] = data;
    body["code"] = 0;
    body["msg"] = "";
    response["body"] = body;
    // 将 Json::Value 转换成字符串
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    return jsonString;
}

std::string RobdogSubPub::get_robot_properties(Json::Value &inValue) {
    Json::Value response = inValue;
    const Json::Value &properties = inValue["body"]["properties"];
    Json::Value newBody(Json::objectValue);
    for (const auto &property : properties) {
        std::string propertyName = property.asString();
        if (propertyName == "networkStatus") {
            /*newBody["properties"][propertyName]["wifiState"] = "on";
            newBody["properties"][propertyName]["mobileDataState"] = "on";
            newBody["properties"][propertyName]["wifiName"] = "robot_wifi";
            newBody["properties"][propertyName]["isWifiConnect"] = true;*/
            
                      Json::FastWriter writer;
                      Json::Value requestJs;
                      requestJs["command"]="getNetworkStatus";

                      std::string jsonString = writer.write(requestJs);
                      homi_speech_interface::NetCtrl reqMsg;
                      Json::Reader reader;
                      Json::Value value;
                      ROS_INFO("Request to net_ctrl_srv is %s",jsonString.c_str());
                      reqMsg.request.data = jsonString;
                      if (net_client.call(reqMsg)){
                          ROS_INFO("Response errorCode from net_ctrl_srv: %d",reqMsg.response.errorCode); 
                          ROS_INFO("Response result from net_ctrl_srv: %s", reqMsg.response.result.c_str());
                          reader.parse(reqMsg.response.result, value);
                          if (value.isNull())
                              ROS_ERROR("json parse error");
                          else
                              newBody["properties"][propertyName]=value;
                      }
                      else
                          ROS_ERROR("Failed to call net_ctrl_srv errorCode:%d",reqMsg.response.errorCode);
        } else if (propertyName == "battery") {
            newBody["properties"][propertyName]["power"] = robotSt.getBatteryLevel();
            newBody["properties"][propertyName]["status"] = robotSt.getBatteryChargeStatus();
        } else if (propertyName == "intelligent") {
            newBody["properties"][propertyName] = robotSt.getIntelligentSwitch();
        } else if (propertyName == "flashlight") {
            newBody["properties"][propertyName]["status"] = robotSt.getFlashStatus();
            newBody["properties"][propertyName]["brightness"] = robotSt.getFlashBrightness();
        } else if (propertyName == "volume") {
            newBody["properties"][propertyName] = robotSt.getVolume();
        } else if (propertyName == "connect_info") {
            newBody["properties"][propertyName]["changeType"] = robotSt.getUserConnectStatus();
            newBody["properties"][propertyName]["phone"] = robotSt.getUserPhoneNumber();
        }
    }
    response["body"] = newBody;
    // 将 Json::Value 转换成字符串
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    return jsonString;
}

void RobdogSubPub::setProperties(const Json::Value &request) {
    const Json::Value &properties = request["body"]["properties"];
    if (properties.isMember("flashlight")) {
        const Json::Value &flahlight = properties["flashlight"];
        if (flahlight.isMember("status"))
            robotSt.setFlashStatus(flahlight["status"].asString());
        if (flahlight.isMember("brightness"))
            robotSt.setFlashBrightness(flahlight["brightness"].asInt());
        int valueStatus = (robotSt.getFlashStatus() == "on") ? 1 : 0;
        publishStatusCtrl(DEEP_CMD_FLASHLIGHT, valueStatus, robotSt.getFlashBrightness());
    }
    if (properties.isMember("volume")) {
        robotSt.setVolume(properties["volume"].asInt());
        int volume = properties["volume"].asInt();
        ROS_INFO_STREAM( "Volume is set to " <<volume<< std::flush);
        if (volume < 0 || volume > 100) {
            ROS_ERROR_STREAM( "Volume must be between 0 and 100." << std::flush);
            return;
        }
        FILE* fp;
        char buffer[50]={0};
        fp = popen(R"(aplay -l | grep "USB Audio Device" -A 2 | grep "card" | awk '{print $2}' | tr -d ':')", "r");
        if(!fp) {
            ROS_INFO ("ThirdpartyAudioDevice Search audio device failed");
            return;
        }
        if (fgets(buffer, sizeof(buffer), fp) == nullptr) {
            ROS_ERROR_STREAM( "No audio device found." << std::flush);
        }
        if (pclose(fp) == -1) {
            ROS_ERROR_STREAM( "Error closing the pipe." << std::flush);
        }
        std::string cardNumber(buffer);
        cardNumber.erase(std::remove(cardNumber.begin(), cardNumber.end(), '\n'), cardNumber.end());
        cardNumber.erase(std::remove(cardNumber.begin(), cardNumber.end(), ' '), cardNumber.end());
        std::string command = "amixer -c   " + (cardNumber) + "   sset PCM " + std::to_string(volume) + "%";
        ROS_INFO("cmd is %s",command.c_str());
        int result = std::system(command.c_str());
        if (result != 0) {
          ROS_ERROR_STREAM( "Error setting PCM volume. Command returned: " << result<<std::flush);
        }
    }
    if (properties.isMember("networkStatus")) {
        const Json::Value &networkStatus = properties["networkStatus"];
        Json::FastWriter writer;
        Json::Value requestJs;
        requestJs["command"] = "setNetworkStatus";
        requestJs["networkStatus"] = networkStatus;

        std::string jsonString = writer.write(requestJs);
        homi_speech_interface::NetCtrl reqMsg;
        ROS_INFO("Request to net_ctrl_srv is %s", jsonString.c_str());
        reqMsg.request.data = jsonString;
        // if (networkStatus.isMember("wifiState")){
        // robotSt.setWifiSwitch(networkStatus["wifiState"].asString());
        if (net_client.call(reqMsg))
          ROS_INFO("Request to net_ctrl_srv: %d", reqMsg.response.errorCode);
        else
          ROS_ERROR("Failed to call net_ctrl_srv errorCode:%d",
                    reqMsg.response.errorCode);
        // }
        // if (networkStatus.isMember("mobileDataState")){
        //     robotSt.setMobileDataSwitch(networkStatus["mobileDataState"].asString());
        // }
        // int value = (robotSt.getWifiSwitch() == "on") ? 1 : 0;
        // int exvalue=(robotSt.getMobileDataSwitch() == "on") ? 1 : 0;
        // publishStatusCtrl(DEEP_CMD_NET,value,exvalue);
    }
    if (properties.isMember("intelligent")) {
        robotSt.setIntelligentSwitch(properties["intelligent"].asString());
        int value = (properties["intelligent"].asString() == "on") ? 1 : 0;
        publishStatusCtrl(DEEP_CMD_AI_MOTION, value, 0);
    }
}

/*{
    x: 1.0,
    y: 6.0,
    angle: 0.0
}*/
void RobdogSubPub::navPositionCallback(
    const geometry_msgs::Pose::ConstPtr &pos) {
    // std::string strMsg = msg->data;
    Json::Value value;
    value["x"] = pos->position.x;
    value["y"] = pos->position.y;
    tf::Quaternion quaternion;
    tf::quaternionMsgToTF(pos->orientation, quaternion);
    double roll, pitch, yaw;
    tf::Matrix3x3(quaternion).getRPY(roll, pitch, yaw);
    yaw = yaw * 180.0 / M_PI;
    value["angle"] = yaw;
    // ROS_INFO("Received robot position %s",value.toStyledString().c_str());

    robotSt.setCurRobotPose(value);
}

/*
    接收感知主机上报的信息：
    1 moveTaskFinished：移动任务完成；
    2 pointUnreachable：目标点不可达；
    0 reLocationFinished：重定位成功
*/
void RobdogSubPub::navStatusCallback(const std_msgs::String::ConstPtr &msg) {
    ROS_INFO("Received msg form nav: %s", msg->data.c_str());
    std::string strMsg = msg->data;
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    if (value.isNull()) {
        ROS_INFO("json parse error");
        return;
    }
    int status = value["status"].asInt();
    Json::Value response;
    Json::Value body;
    body["status"] = status;
    response["deviceId"] = robotSt.getDeviceId();
    response["domain"] = "DEVICE_INTERACTION";
    response["event"] = "navigation_report";
    response["eventId"] = "robdog_plat_" + to_string(getCurrentTimeStramp());
    response["seq"] = 0;
    response["body"] = body;
    printf("status:%d\n", status);
    if (status == 1) {
        expresstion_count++;
        if(expresstion_count == 8)
        {
          playAudio("/home/<USER>/robot-application/deeprobots_application_ros1/resource/audio/fetchExpress2.wav");
          expresstion_count = 0;
        }
        at_target_ = true;
        std::cout << "at_target_: " << at_target_ << std::endl;
    }

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    ROS_INFO("cmd : %s", jsonString.c_str());
    homi_speech_interface::SIGCData resMsg;
    resMsg.request.data = jsonString;
    ROS_INFO_STREAM("Send res to Server, res is " << resMsg.request.data);
    if (platform_client.call(resMsg)) {
        ROS_INFO("Response from server: %d", resMsg.response.errorCode);
    } else {
        ROS_ERROR("Failed to call service Service_demo errorCode:%d", resMsg.response.errorCode);
    }
}

// 导航状态通知【由感知主机上报后，转发给平台】 合并到状态上报里面了
// void RobdogSubPub::navStatusNotifyCallback(const std_msgs::String::ConstPtr&
// msg) {
//     ROS_INFO("Received msg from nav: %s", msg->data.c_str());
//     std::string strMsg = msg->data;
//     Json::Reader reader;
//     Json::Value value;
//     reader.parse(strMsg, value);
//     if (value.isNull()) {
//         ROS_INFO("json parse error");
//         return;
//     }

//     int type = value["body"]["type"].asInt(); // 从原 JSON 中获取类型
//     Json::Value response;
//     Json::Value body;
//     body["type"] = type; // 0- 重定位请求
//     response["deviceId"] = "1008613"; // 固定设备 ID
//     response["domain"] = "DEVICE_INTERACTION";
//     response["event"] = "navigation_notify"; // 修改事件类型
//     response["eventId"] = "随意"; // 固定事件 ID
//     response["seq"] = "时间戳"; // 固定序列号
//     response["body"] = body;

//     Json::FastWriter writer;
//     std::string jsonString = writer.write(response);
//     ROS_INFO("cmd : %s", jsonString.c_str());
//     homi_speech_interface::SIGCData resMsg;
//     resMsg.request.data = jsonString;
//     ROS_INFO_STREAM("Send res to Server, res is " << resMsg.request.data);

//     if (platform_client.call(resMsg)) {
//         ROS_INFO("Response from server: %d", resMsg.response.errorCode);
//     } else {
//         ROS_ERROR("Failed to call service Service_demo errorCode:%d",
//         resMsg.response.errorCode);
//     }
// }

// 0-停止跟随 1-开启跟随 2-开启UWB跟随
void RobdogSubPub::actionFollow(int status) {
    string strAction = 0 == status ? "endFollow"
                     : 1 == status ? "startFollow"
                     : 2 == status ? "startUwbFollow" : "endFollow";
    Json::Value value;
    Json::Value params;
    value["client_type"] = "launcher";
    value["action"] = strAction;
    WS_Send(value.toStyledString().c_str(), nConnectIndex_);
}

bool RobdogSubPub::isAtTarget(const homi_speech_interface::SIGCEvent &msg) {
    std::string strMsg = msg.event; // JSON字段
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    Json::Value jBody = value["body"];
    // 获取当前机器人位置
    Json::Value currentPose = robotSt.getCurRobotPose();
    double curX = currentPose["x"].asDouble();
    double curY = currentPose["y"].asDouble();
    double curAngle = currentPose["angle"].asDouble();
    ROS_INFO("current pos x: %f, y: %f, theta: %f", curX, curY, curAngle);

    // 目标位置
    double targetX = 0.0;
    double targetY = 0.0;
    double targetTheta = 0.0;

    Json::Value arrayPoints = value["points"];
    if (arrayPoints.isArray()) {
        for (auto it : arrayPoints) {
            Json::Value jPoint = it;
            targetX = jPoint["x"].asDouble();
            targetY = jPoint["y"].asDouble();
            targetTheta = jPoint["angle"].asDouble();
        }
    }
    ROS_INFO("planning move pos x: %f, y: %f, theta: %f", targetX, targetY, targetTheta);
    // 比较当前位置与目标位置
    return (fabs(targetX - curX) < 0.01 && fabs(targetY - curY) < 0.01 &&
            fabs(targetTheta - curAngle) < 0.01);
}

void RobdogSubPub::playAudio(const std::string &filePath) {
    FILE* fp;
    char buffer[50];
    fp = popen(R"(aplay -l | grep "USB Audio Device" -A 2 | grep "card" | awk '{print $2}' | tr -d ':')", "r");
    if(!fp) {
      printf("ThirdpartyAudioDevice Search audio device failed\n");
      return;
    }
    fgets(buffer,sizeof(buffer),fp);
    std::string device_name = "plughw:" + std::string(1, buffer[0]) + ",0";

    ROS_INFO("device_name:%s",device_name.c_str());

    // 关闭语音助手
    homi_speech_interface::AssistantAbort srv;
    bool response = ros::service::call("/homi_speech/helper_assistant_abort_service", srv);
    // 关闭语音唤醒
    homi_speech_interface::SetWakeEvent wkevent;
    wkevent.request.target = false;
    bool response1 = ros::service::call("/audio_node/set_wake_event_service", wkevent);
    // 播放音频
    std::string command = "aplay -D " + device_name +" \"" + filePath + "\"";
    int result = std::system(command.c_str());
    // 开启语音唤醒事件
    homi_speech_interface::SetWakeEvent wkevent2;
    wkevent.request.target = true;
    bool response2 = ros::service::call("/audio_node/set_wake_event_service", wkevent);
}

void RobdogSubPub::checkTargetStatus(const ros::TimerEvent &) {
    // std::cout << "111111111at_target_: " << at_target_ << std::endl;
    if (at_target_ /* &&  "takePhotos" == robotSt.getMoveTaskType()*/) {
        std::string file_path = "/home/<USER>/resource/audio/到位打开氛围灯.wav";
        playAudio(file_path);
        // 调用iot控制打开氛围灯
        ros::service::waitForService("/homi_speech/speech_iot_control_service");
        homi_speech_interface::IotControl srv1;
        srv1.request.param = "{\"outletStatus\":1}";
        bool response1 = ros::service::call("/homi_speech/speech_iot_control_service", srv1);
        printf("response: %d\n", response1);
        // 第四步，调用语音助手
        ros::service::waitForService("/homi_speech/helper_assistant_takephoto_service");
        homi_speech_interface::AssistantTakePhoto srv2;
        bool response2 = ros::service::call("/homi_speech/helper_assistant_takephoto_service", srv2);
        printf("472---response: %d %d\n", response2, srv2.response.errorCode);
        at_target_ = false;
        // 停止定时器
        robMoveStatusTimer_.stop();
    }
}

void RobdogSubPub::moveToTarget(const homi_speech_interface::SIGCEvent &msg) {
    std::string strMsg = msg.event; // JSON字段
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    Json::Value jBody = value["body"];
    string strEvent = jBody["event"].asString();
    robotSt.setMoveTaskType(strEvent);
    if (strEvent == "cancelMovement") { // 取消移动
      jBody["point"] = Json::Value(Json::arrayValue);
    }

    // ROS_INFO("planning move pos x: %f, y: %f, theta: %f", targetX, targetY,
    // targetTheta);
    Json::StreamWriterBuilder writerBuilder;
    std::string navCtrlMsgs = msg.event;
    // 打印 navCtrlMsgs
    std::cout << "发送的 navCtrlMsgs: " << navCtrlMsgs << std::endl;
    std_msgs::String nav_ctrl_msg;
    nav_ctrl_msg.data = navCtrlMsgs;
    ROS_INFO("moveToTarge111111111111111111111111111111111111111111111111111");
    actionPlanningMove_pub.publish(nav_ctrl_msg);

    Json::Value jMsgvalue;
    jMsgvalue["client_type"] = "launcher";
    jMsgvalue["action"] = "navigation_control";
    jMsgvalue["params"] = value;
    WS_Send(jMsgvalue.toStyledString().c_str(), nConnectIndex_);   
}

void RobdogSubPub::moveToTargetAndPlayAudio(
    const homi_speech_interface::SIGCEvent &msg) {

    // 如果已在指定地点，则直接播放音频
    if (isAtTarget(msg)) {
        std::string file_path = "/home/<USER>/resource/audio/1312直接打开氛围灯.wav";
        playAudio(file_path);
        // 调用iot控制打开氛围灯
        ros::service::waitForService("/homi_speech/speech_iot_control_service");
        homi_speech_interface::IotControl srv1;
        srv1.request.param = "{\"outletStatus\":1}";
        bool response1 = ros::service::call("/homi_speech/speech_iot_control_service", srv1);
        printf("response: %d\n", response1);

        // 第四步，调用语音助手
        ros::service::waitForService("/homi_speech/helper_assistant_takephoto_service");
        homi_speech_interface::AssistantTakePhoto srv2;
        bool response2 = ros::service::call("/homi_speech/helper_assistant_takephoto_service", srv2);
        printf("502---response: %d %d\n", response2, srv2.response.errorCode);
    }

    // 移动到指定地点
    else {
        moveToTarget(msg);
        std::string file_path = "/home/<USER>/resource/audio/去玄关.wav";
        playAudio(file_path);
        // ROS_INFO("plat send point to nvidia: %s", msg); 
        // 平台下发给狗子的等待移动点位信息
        // 等待并监听机器人到达状态，此处采用非阻塞方式
        robMoveStatusTimer_ = nh_.createTimer(ros::Duration(1), &RobdogSubPub::checkTargetStatus, this, false);
    }
    // ROS_INFO("moveToTargetAndPlayAudio 389723897239823798223923\n");
    // moveToTarget(msg);
}

// 调用语音助手
void RobdogSubPub::callHelperPhoto() {
    ros::service::waitForService("/homi_speech/helper_assistant_takephoto_service");
    homi_speech_interface::AssistantTakePhoto srv;
    bool response2 = ros::service::call("/homi_speech/helper_assistant_takephoto_service", srv);
    printf("594---response: %d %d\n", response2, srv.response.errorCode);
    // sleep(15);
}

// 调用拍照
void RobdogSubPub::takePhotoService() {
    // 启动拍照打印服务
    ros::Publisher pub = nh_.advertise<std_msgs::String>("/take_photo", 10);
    std_msgs::String msg;
    msg.data = "take_photo";
    sleep(2.0);
    pub.publish(msg);
    // 输出日志
    ROS_INFO("Published message: [%s]", msg.data.c_str());
    /*
        std::string file_path = "/home/<USER>/resource/audio/upload_cloud.wav";
        playAudio(file_path);

        // 调用iot控制打开氛围灯
        ros::service::waitForService("/homi_speech/speech_iot_control_service");
        homi_speech_interface::IotControl srv1;
        srv1.request.param = "{\"outletStatus\":0}";
        bool response1 = ros::service::call("/homi_speech/speech_iot_control_service",
        srv1); printf("response: %d\n", response1);
    */
}

// ********************************************* 智能播报 **************************************************************

// 接受语音助手上报的信息（是否终止）
void RobdogSubPub::BrocastIfAbortCallBack(
    const homi_speech_interface::AssistantEventPtr &msg) {
    std::string sectionId_past = robotSt.getSectionId(); // 语音助手之前上报的sectionId
    std::string sectionId_cur = msg->sectionId;
    std::string aborting = msg->description;
    // ROS_INFO("aborting: %s, sectionId_past: %s, sectionId_cur: %s",
    // aborting.c_str(), sectionId_past.c_str(), sectionId_cur.c_str());
    // if(sectionId_past == sectionId_cur)
    // ROS_INFO("6666666666666666666666666666666666666666");
    if (sectionId_past == sectionId_cur &&
        aborting == "UserAbort") {   // 被语音词唤醒
        RobotBroadcastStatusToPlat(0); // 被打断
        timer_brocast.stop();
        brocast_send_count_ = 0;
        ROS_INFO("interrupted by the wake word!!!");
    }
}

// 定时发送播报文本
void RobdogSubPub::SendBrocastCallback(const ros::TimerEvent &) {
    ROS_INFO("Brocast times: %d, Total Count_: %d", brocast_send_count_, brocast_total_count_);
    if (brocast_send_count_ < brocast_total_count_) { // 人为定义发送次数
        sendStringToBrocast(brocast_text); // 向语音助手发送播报文本
        // ROS_INFO("Start Brocasting: %s.", brocast_text);
        ++brocast_send_count_; // 增加了定时器次数
        // ROS_INFO("Pub times: %d, Total Count_: %d", send_count_, total_count_);
    } else {
        // 完成发送后停止定时器
        timer_brocast.stop();
        brocast_send_count_ = 0;
    }
}

// 调用服务向语音助手发送智能播报文本（收到的sectionId要保存起来）
void RobdogSubPub::sendStringToBrocast(const std::string &message) {
    // ROS_INFO("Sending to brocast: %s", message.c_str()); // 播报文本
    // 创建请求消息
    homi_speech_interface::AssistantSpeechText resMsg;
    resMsg.request.msg = message;

    // 调用服务并处理响应
    if (brocast_client.call(resMsg)) {
        std::string sectionId = resMsg.response.sectionId; // 假设服务响应中有 sectionId
        robotSt.setSectionId(sectionId);
        ROS_INFO("Received sectionId from server: %s", sectionId.c_str());
    } else {
        ROS_ERROR("Failed to call service, errorCode: %d", resMsg.response.errorCode);
    }
}

// 向平台上传智能播报状态【不要定时,被打断了才会上报】
void RobdogSubPub::RobotBroadcastStatusToPlat(int status) {
    // 构建状态报告的 JSON
    Json::Value response;

    response["deviceId"] = robotSt.getDeviceId();

    response["domain"] = "ROBOT_BUSINESS_DEVICE";        // 域
    response["event"] = "broadcast_report";              // 事件类型
    response["eventId"] = "111111111";                   // 事件 ID
    response["seq"] = to_string(getCurrentTimeStramp()); // 时间戳作为序列号

    // 以定时器的开关作为播报任务的状态
    // int status = timer_broadcast_running ? 1 : 0; // 1 - 正常运行, 0 - 被打断
    response["body"]["status"] = status; // 状态字段：0-打断，1-正常运行
    long id = robotSt.getRemindId();
    // ROS_INFO("RemindId: %ld", id);
    response["body"]["remindId"] = Json::Int64(id); // 设备 ID【直接读取】
    // ROS_INFO("RemindId: %ld", response["body"]["remindId"].asInt64());
    // 将 JSON 转换为字符串
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    // ROS_INFO("cmd : %s", jsonString.c_str());

    homi_speech_interface::SIGCData resMsg;
    resMsg.request.data = jsonString; // 设置请求数据
    ROS_INFO_STREAM("Send res to plat, res is " << resMsg.request.data);

    if (platform_client.call(resMsg)) {
        ROS_INFO("Response from server: %d", resMsg.response.errorCode); // 打印服务器响应
    } else {
        ROS_ERROR("Failed to call service Service_demo errorCode:%d", resMsg.response.errorCode); // 错误处理
    }
}
void RobdogSubPub::update_emergency_contacts(int updateType,const std::string& entityId, const Json::Value& emergencyContact) {
    std::unique_ptr<SQLiteDB> db=std::make_unique<SQLiteDB>("robdog.db");
    if (!db->open()) {
        ROS_ERROR("robdog.db open or create failed");
        return ;
    }
    switch (updateType)
    {
    case 1:{
        db->createTable("emergency_contacts", "id TEXT PRIMARY KEY, nickName TEXT, phone TEXT");
        std::map<std::string, std::string> values={{"id", entityId},{"nickName", emergencyContact["nickName"].asString()},{"phone", emergencyContact["phone"].asString()}};
        if (db->insert("emergency_contacts", values)) {
            ROS_INFO_STREAM("Emergency contact created successfully." << std::endl);
        } else {
            ROS_ERROR_STREAM("Failed to create emergency contact." << std::endl);
        }
        break;
    }
    case 2:{
        std::string condition = "id = '" + entityId + "'";
        std::map<std::string, std::string> values = {{"nickName", emergencyContact["nickName"].asString()},{"phone", emergencyContact["phone"].asString()}};
        if (db->update("emergency_contacts", values, condition)) {
            ROS_INFO_STREAM("Emergency contact update successfully." << std::endl);
        } else {
            ROS_ERROR_STREAM("Failed to update emergency contact." << std::endl);
        }
        break;
    }
    case 3:{
        std::string condition = "id = '" + entityId + "'";
        if (db->remove("emergency_contacts", condition)) {
            ROS_INFO_STREAM("Emergency contact delete successfully." << std::endl);
        } else {
            ROS_ERROR_STREAM("Failed to delete emergency contact." << std::endl);
        }        
        break;
    }
    default:
        break;
    }
    db->close();
}


/* devAlarmReportCallback 暂时用字符串，后续改成ros的msg自定义类型
"body":{
        "alarmCode":10001,			//告警代码	10001：遇到障碍无法通过；10002：跟随目标丢失
        "alarName":"告警名称",
        "alarmLevel":1, 		    //告警级别，1：紧急告警；2：重要告警；3：一般告警；4：提示        
        "alarmType":"告警类别",
        "notifiedParty":0,	    //1:app，2：用户，3：运维
        "notifyWay":0,		      //通知方式，1:短信通知；2:电话通知（notifyUserType为2或3的时候有效）
        "alarmDesc":"告警描述",
        "launcherModel":"告警模块"
}*/
void RobdogSubPub::devAlarmReportCallback(const std_msgs::String::ConstPtr& msg) {
    Json::Value response;
    response["deviceId"] = robotSt.getDeviceId();
    response["domain"] = "DEVICE_ALARM";
    response["event"] = "device_alarm_report";
    response["eventId"] = "robdog_plat_" + to_string(getCurrentTimeStramp());
    response["seq"] = to_string(getCurrentTimeStramp());

    Json::Value body;
    Json::CharReaderBuilder readerBuilder;
    std::istringstream s(msg->data.c_str());
    std::string errs;
    // 使用 Json::CharReaderBuilder 解析 JSON 字符串
    if (!Json::parseFromStream(readerBuilder, s, &body, &errs)) {
        ROS_ERROR("Failed to parse JSON: %s", msg->data.c_str());
        return;
    }
    response["body"] = body;
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    ROS_INFO("cmd : %s", jsonString.c_str());
    homi_speech_interface::SIGCData resMsg;
    resMsg.request.data = jsonString;
    ROS_INFO_STREAM("Send res to Server, res is " << resMsg.request.data);
    if (platform_client.call(resMsg)) {
        ROS_INFO("Response from server: %d", resMsg.response.errorCode);
    } else {
        ROS_ERROR("Failed to call service Service_demo errorCode:%d", resMsg.response.errorCode);
    }
}

// *********************************************************************************************************************

void RobdogSubPub::robctrlCallback(const homi_speech_interface::SIGCEventPtr &msg) {
    // 处理接收到的速度消息
    ROS_INFO("Received msg form platform: %s", msg->event.c_str());

    // 解析平台发送过来的控制指令
    std::string strMsg = msg->event; // JSON字段
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    if (value.isNull()) {
        ROS_INFO("json parse error");
        return;
    }
    try {
        // 检查必须的字段
        if (!value["deviceId"].isNull() && !value["event"].isNull()) {
            // ROS_INFO("094282882038");
            // 开始处理消息
            robotSt.setDeviceId(value["deviceId"].asString());
            string strType = value["event"].asString();
            Json::Value jBody = value["body"];
            // 和特定的运动指令（如主动跟随及运动模式等）有关
            if (strType == "robot_action") {
                move_status_flag = false;
                if (jBody["actionType"].isNull()) {
                    expresstion_count = 0;
                    // ROS_INFO("8959379483739289398");
                    //导航任务需要切换到自主模式
                    homi_speech_interface::RobdogAction zzmsg;
                    zzmsg.actiontype = "NavCtrl";
                    zzmsg.actionargument = "AutoMode";
                    publishAction(zzmsg);

                    // 定点移动
                    // event：throwGarbage：丢垃圾；deliverExpress：寄快递；takePhotos：拍照；welcomeHome：欢迎回家
                    system("/home/<USER>/updateexpression.sh  "
                            "/home/<USER>/resource/left_right_look.mp4");
                    std::string taskType;
                    if (!jBody["event"].isNull()) {
                        taskType = jBody["event"].asString();
                        robotSt.setMoveTaskType(taskType);
                    } else {
                        robotSt.setMoveTaskType("");
                    }
                    if (taskType == "takePhotos") {
                        readMappingPoints();
                        std::string navCtrlMsgs = msg->event;
                        // 打印 navCtrlMsgs
                        std::cout << "发过来的 navCtrlMsgs: " << navCtrlMsgs << std::endl;
                        homi_speech_interface::SIGCEvent photo_msg;
                        photo_msg.event =
                            ReadMapCfg::getInstance().getPhonePoints(robotSt.getDeviceId());
                        if (photo_msg.event.empty()) {
                        photo_msg.event = PHOTO_POINT;
                        } else {
                        ROS_INFO("get PHOTO_POINT, %s", photo_msg.event.c_str());
                        }
                        ROS_INFO("PHOTO_POINT:%s", PHOTO_POINT);
                        moveToTargetAndPlayAudio(photo_msg);
                    } else if (taskType == "deliverExpress") {
                        readMappingPoints();
                        std::string file_path = "/home/<USER>/resource/audio/出发去快递站.wav";
                        playAudio(file_path);
                        std::string navCtrlMsgs = msg->event;
                        // 打印 navCtrlMsgs
                        std::cout << "发过来的 navCtrlMsgs: " << navCtrlMsgs << std::endl;
                        homi_speech_interface::SIGCEvent press_msg;

                        press_msg.event = ReadMapCfg::getInstance().getPressPoints(robotSt.getDeviceId());
                        if (press_msg.event.empty()) {
                          press_msg.event = PRESS_POINT;
                        } else {
                            ROS_INFO("get PRESS_POINT, %s", press_msg.event.c_str());
                        }
                        moveToTarget(press_msg);
                    }else if (taskType == "fetchExpress") {
                        readMappingPoints();
                        std::string file_path = "/home/<USER>/resource/audio/fetchExpress.wav";
                        playAudio(file_path);
                        std::string navCtrlMsgs = msg->event;
                        // 打印 navCtrlMsgs
                        std::cout << "发过来的 navCtrlMsgs: " << navCtrlMsgs << std::endl;
                        homi_speech_interface::SIGCEvent press_msg;

                        press_msg.event = ReadMapCfg::getInstance().getPressPoints(robotSt.getDeviceId());
                        if (press_msg.event.empty()) {
                            press_msg.event = PRESS_POINT;
                        } else {
                            ROS_INFO("get PRESS_POINT, %s", press_msg.event.c_str());
                        }
                        moveToTarget(press_msg);
                    }else if (taskType == "cancelMovement") {
                        homi_speech_interface::SIGCEvent cancel_msg;
                        cancel_msg.event = CANCEL_POINT;
                        moveToTarget(cancel_msg);
                    } else {
                        homi_speech_interface::SIGCEvent move_msg;
                        Json::StreamWriterBuilder writerBuilder;
                        std::string moveMsgs = Json::writeString(writerBuilder, jBody);
                        move_msg.event = moveMsgs;
                        moveToTarget(move_msg);
                    }
                } else {
                    std::string actionType = jBody["actionType"].asString();
                    // 主动跟随【跟随的消息应该不是直接发给机器狗的，应该是websocket跟算法通信】
                    if (actionType == "followMe") {
                        string actionArgument = "";
                        if (!jBody["actionArguement"].isNull()) {
                            actionArgument = jBody["actionArguement"].asString();
                        } else if (!jBody["actionArgument"].isNull()) {
                            actionArgument = jBody["actionArgument"].asString();
                        }
                        // 开启跟随
                        if (actionArgument == "on") {
                            ROS_INFO_STREAM("Robot is following the user.");
                            actionFollow(1);
                        }
                        // UWB跟随
                        else if (actionArgument == "on_uwb") {
                            ROS_INFO_STREAM("Robot is not following the user.");
                            actionFollow(2);
                        }
                        // 关闭跟随
                        else if (actionArgument == "off") {
                            ROS_INFO_STREAM("Robot is not following the user.");
                            actionFollow(0);
                        } else if (actionType == "takePhoto") {
                            robActionCheckTimer_.stop();
                            system("/home/<USER>/updateexpression.sh  /home/<USER>/resource/happy.mp4");
                            // 播放321茄子
                            std::string filePath = "/home/<USER>/resource/audio/321茄子.wav";
                            playAudio(filePath);

                            // 启动拍照打印服务
                            takePhotoService();
                            } else if (actionType == "preparedConfirm") {
                            std::string filePath = "/home/<USER>/resource/audio/等着急了.wav";
                            playAudio(filePath);
                            // 创建定时器回调函数
                            auto checkActionType = [this, actionType](const ros::TimerEvent &event) -> void {
                                // 检查新的actionType是否为"takePhoto"
                                callHelperPhoto();
                                printf("checkActionType: %s\n", actionType.c_str());
                            };
                            // 创建定时器，每5秒检查一次actionType
                            robActionCheckTimer_ = nh_.createTimer(ros::Duration(10), checkActionType);
                            }
                    }
                    // 运动模式设置
                    else if (actionType == "sportMode") {
                        try {
                            std::string actionArgument;
                            if (!jBody["actionArguement"].isNull()) {
                            actionArgument = jBody["actionArguement"].asString();
                            } else if (!jBody["actionArgument"].isNull()) {
                            actionArgument = jBody["actionArgument"].asString();
                            }
                            auto it = sportModeMap.find(actionArgument);
                            if (it != sportModeMap.end()) {
                            // publishStatusCtrl(DEEP_CMD_ACTION,sportModeMap[actionArgument],0);
                            // 发送给控制节点
                            homi_speech_interface::RobdogAction msg;
                            msg.actiontype = "sportMode";
                            msg.actionargument = actionArgument;
                            publishAction(msg);
                            } else
                            throw std::invalid_argument("Unknown actionArgument name: " +
                                                        actionArgument);
                        } catch (const std::exception &e) {
                            std::cerr << e.what() << '\n';
                        }
                    }
                    // 运动技能设定
                    else if (actionType == "motorSkill") {
                        // ROS_INFO("3998947983274398");
                        std::string actionArgument;
                        if (!jBody["actionArguement"].isNull()) {
                            actionArgument = jBody["actionArguement"].asString();
                        } else if (!jBody["actionArgument"].isNull()) {
                            actionArgument = jBody["actionArgument"].asString();
                        }
                        homi_speech_interface::RobdogAction msg;
                        msg.actiontype = "motorSkill";
                        // 站立
                        if (actionArgument == "standUp") {
                            if(curState_==ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT){
                                ROS_ERROR("Current state is %d already,robot DO NOT need stand",curState_);
                                return;
                            }
                            msg.actionargument = "standUp";
                            publishAction(msg);
                            // ROS_INFO_STREAM("Recv standUp.");
                        } else if (actionArgument == "getDown") {
                            if(curState_==ROBDOG_STATUS_SITDOWM){
                                ROS_ERROR("Current state is %d already,robot DO NOT need getDown",curState_);
                                return;
                            }
                            msg.actionargument = "getDown";
                            publishAction(msg);
                            // ROS_INFO_STREAM("Recv getDown.");
                        } else if (actionArgument == "greeting") {
                            msg.actionargument = "greeting";
                            publishAction(msg);
                            // ROS_INFO_STREAM("Recv greeting.");
                        } else if (actionArgument == "twistBody") {
                            msg.actionargument = "twistBody";
                            publishAction(msg);
                            // ROS_INFO_STREAM("Recv twistBody.");
                        } else if (actionArgument == "backflip") {
                            msg.actionargument = "backflip";
                            publishAction(msg);
                            // ROS_INFO_STREAM("Recv backflip.");
                        } else if (actionArgument == "jumpForward") {
                            msg.actionargument = "jumpForward";
                            publishAction(msg);
                            // ROS_INFO_STREAM("Recv jumpForward.");
                        } else if (actionArgument == "turnOver") {
                            msg.actionargument = "turnOver";
                            publishAction(msg);
                            // ROS_INFO_STREAM("Recv turnOver.");
                        } else if (actionArgument == "twistJump") {
                            msg.actionargument = "twistJump";
                            publishAction(msg);
                            // ROS_INFO_STREAM("Recv twistJump.");
                        } else if (actionArgument == "sitDown") {
                            msg.actionargument = "sitDown";
                            publishAction(msg);
                            // ROS_INFO_STREAM("Recv sitDown.");
                        } else if (actionArgument == "fingerHeart") {
                            msg.actionargument = "fingerHeart";
                            publishAction(msg);
                            // ROS_INFO_STREAM("Recv fingerHeart.");
                        } else if (actionArgument == "makeBow") {
                            msg.actionargument = "makeBow";
                            publishAction(msg);
                            // ROS_INFO_STREAM("Recv makeBow.");
                        } else if (actionArgument == "dance") {
                            msg.actionargument = "dance";
                            publishAction(msg);
                        } else if (actionArgument == "shakeBody") {
                            msg.actionargument = "shakeBody";
                            publishAction(msg);
                        } else if (actionArgument == "twistAss") {
                            msg.actionargument = "twistAss";
                            publishAction(msg);
                        } else if (actionArgument == "twistBody_emergency") {
                            msg.actionargument = "twistBody_emergency";
                            publishAction(msg);
                        } else if (actionArgument == "greeting_emergency") {
                            msg.actionargument = "greeting_emergency";
                            publishAction(msg);
                        } else {
                            ROS_INFO_STREAM(
                                "Unhandled action argument: " << actionArgument);
                        }
                    }
                    //急停
                    else if (actionType == "emergencyStop") {
                        homi_speech_interface::RobdogAction msg;
                        msg.actiontype = "emergencyStop";
                        msg.actionargument = "emergencyStop";
                        publishAction(msg);
                    }
                    // 关节回零
                    else if (actionType == "resetZero") {
                        homi_speech_interface::RobdogAction msg;
                        msg.actiontype = "resetZero";
                        msg.actionargument = "resetZero";
                        publishAction(msg);
                    } 
                    // 和强化学习有关的
                    else if (actionType=="gaitControl"){
                        std::string actionArgument;
                        if (!jBody["actionArguement"].isNull()) {
                            actionArgument = jBody["actionArguement"].asString();
                        } else if (!jBody["actionArgument"].isNull()) {
                            actionArgument = jBody["actionArgument"].asString();
                        }
                        homi_speech_interface::RobdogAction msg;
                        msg.actiontype = "gaitControl";
                        if (actionArgument == "obstacleCross") {
                            msg.actionargument = "obstacleCross";
                            publishAction(msg);
                        } else if (actionArgument == "flatGround") {
                            msg.actionargument = "flatGround";
                            publishAction(msg);
                        } else if (actionArgument == "exit") {
                            msg.actionargument = "exit";
                            publishAction(msg);
                        } 
                    }  
                    else {
                        ROS_INFO_STREAM("Unhandled action type: " << actionType);
                    }
                }
            }
            // 平台的控制指令（移动模式）
            else if (strType == "robot_move" || strType == "robot_view") {
                // ROS_INFO("actionType: %s", jBody["actionType"].asString().c_str());
                // 初始化 current_twist_msg_ 为全0
                current_twist_msg_.linear.x = 0;
                current_twist_msg_.linear.y = 0;
                current_twist_msg_.linear.z = 0;
                current_twist_msg_.angular.x = 0;
                current_twist_msg_.angular.y = 0;
                current_twist_msg_.angular.z = 0;

                // 初始化current_continue_msg_全为0
                current_continue_msg_.event = strType; // 移动模式或者原地模式
                current_continue_msg_.x = 0;
                current_continue_msg_.y = 0;
                current_continue_msg_.z = 0;
                current_continue_msg_.yaw = 0;
                current_continue_msg_.pitch = 0;
                current_continue_msg_.roll = 0;

                // float resting_time = config.get("resting_time", 0.0f).asFloat();
                // 处理direction的信息
                Json::Value direction = jBody["direction"];
                // 是否是连续移动
                if (!jBody["actionType"].isNull()) {
                    int actionType = jBody["actionType"].asInt();
                    if (actionType == 1 && move_status_flag == false) {
                        move_status_flag = true;
                        string command =
                            "/home/<USER>/updateexpression2.sh "
                            "/home/<USER>/resource/vedio/look_right_left_step1.mp4 2 "
                            "/home/<USER>/resource/vedio/look_right_left_step2.mp4";
                        system(command.c_str());
                    } else if (actionType == 0 && move_status_flag == true) {
                        move_status_flag = false;
                        string command = "/home/<USER>/updateexpression.sh "
                                        "/home/<USER>/resource/vedio/default.mp4";
                        system(command.c_str());
                    }

                    if (actionType == 2) {
                        // 每次执行一个动作之前先静止一会【只有步进模式下才需要】
                        timer_.stop();
                        velCmd_pub.publish(current_twist_msg_);
                    }
                    // float fspeed_x = config.get("fspeed_x", 0.0f).asFloat();
                    // float fspeed_y = config.get("fspeed_y", 0.0f).asFloat();
                    // float fspeed_z = config.get("fspeed_z", 0.0f).asFloat();

                    ROS_INFO("fspeed_x: %f, fspeed_ydirection: %f, fspeed_z: %f", fspeed_x, fspeed_y, fspeed_z);

                    int count = 0; // 要走的步数

                    if (!direction["x"].isNull() && direction["x"].asInt() != 0) {
                        int step_x = direction["x"].asInt();
                        count = std::abs(step_x);
                        current_continue_msg_.x = step_x;
                        ROS_INFO("Continue Move,CMD FROM PLATFORM OR APP. STEP_X: %d, "
                                "fspeed_x: %f",
                                step_x, fspeed_x);
                        current_twist_msg_.linear.x = (step_x > 0) ? fspeed_x : -fspeed_x;
                    }
                    if (!direction["y"].isNull() && direction["y"].asInt() != 0) {
                        int step_y = direction["y"].asInt();
                        count = std::abs(step_y);
                        current_continue_msg_.y = step_y;
                        ROS_INFO("Continue Move,CMD FROM PLATFORM OR APP. STEP_Y: %d, "  "fspeed_y: %f",  step_y, fspeed_y);
                        current_twist_msg_.linear.y = (step_y > 0) ? fspeed_y : -fspeed_y;
                    }
                    if (!direction["z"].isNull() && direction["z"].asInt() != 0) {
                        int step_z = direction["z"].asInt();
                        current_continue_msg_.z = step_z;
                    }
                    if (!direction["yaw"].isNull() && direction["yaw"].asInt() != 0) {
                        int yaw = direction["yaw"].asInt();
                        count = std::ceil(static_cast<double>(yaw) / 15);
                        current_continue_msg_.yaw = yaw;
                        current_twist_msg_.angular.z =
                            (yaw > 0) ? fspeed_z : -fspeed_z; //转一次的角速度
                                                            // if(actionType == 2){
                                                            //     count = 0;
                        //     current_continue_msg_.event = "stepMode";
                        //     continueMoveCmd_pub.publish(current_continue_msg_); //
                        //     用云深处的转向【现在暂不可用】
                        // }
                    }
                    if (!direction["pitch"].isNull() && direction["pitch"].asInt() != 0) {
                        float pitch = direction["pitch"].asInt();
                        current_continue_msg_.pitch = pitch;
                    }
                    if (!direction["roll"].isNull() && direction["roll"].asInt() != 0) {
                        float roll = direction["roll"].asInt();
                        current_continue_msg_.roll = roll;
                    }
                    Proceationtype(actionType, count);
                }
            }
            // 机器狗模式
            else if (strType == "mode_set") {
                Json::Value deviceMode = jBody["deviceMode"];
                // 机器狗模式：0-宅家模式 1-遛狗模式 2-外出模式 3-遥控模式
                // 一期暂无外出模式
            } else if (strType == "properties_write") {
                ROS_INFO("platform set properties");
                setProperties(value);
            } else if (strType == "properties_read") {
                ROS_INFO("platform get properties");
                homi_speech_interface::SIGCData resMsg;
                resMsg.request.data = get_robot_properties(value);
                ROS_INFO_STREAM("Send res to Server, res is " << resMsg.request.data);
                if (platform_client.call(resMsg))
                    ROS_INFO("Response from server: %d", resMsg.response.errorCode);
                else
                    ROS_ERROR("Failed to call service Service_demo errorCode:%d",
                            resMsg.response.errorCode);
            } else if (strType == "connect_info_request") {
                ROS_INFO_STREAM("CMD : connect_info_request");
                homi_speech_interface::SIGCData resMsg;
                resMsg.request.data = get_connect_info_request(value);
                ROS_INFO_STREAM("Send res to Server, res is " << resMsg.request.data);
                if (platform_client.call(resMsg))
                    ROS_INFO("Response from server: %d", resMsg.response.errorCode);
                else
                    ROS_ERROR("Failed to call service Service_demo errorCode:%d",
                resMsg.response.errorCode);
            } else if (strType == "user_connect_change") {
                ROS_INFO("user_connect_change");
                ROS_INFO("set operateTs %lld", jBody["operateTs"].asInt64());
                robotSt.setTimeStamp(jBody["operateTs"].asInt64());
                if (jBody["changeType"].asInt() == 1) // 1代表建联
                {
                    robotSt.setUserConnectStatus(1); // 1代表有用户连接
                    robotSt.setUserPhoneNumber(jBody["phone"].asString());
                } else if (jBody["changeType"].asInt() == 2) // 2代表取消建联
                {
                    robotSt.setUserConnectStatus(0); // 0代表无用户连接
                    robotSt.setUserPhoneNumber("13933333333");
                } else
                    ROS_ERROR("Undefined changeType %d", jBody["changeType"].asInt());
            }
            // 家庭建图功能
            else if (strType == "map_draw") {
                ROS_INFO("map_draw");
                jBody["action"];
                Json::StreamWriterBuilder writerBuilder;
                std::string mapCtrlMsgs = Json::writeString(writerBuilder, jBody);
                std_msgs::String map_ctrl_msg;
                map_ctrl_msg.data = mapCtrlMsgs;
                mappingControl_pub.publish(map_ctrl_msg);
                
                Json::Value jMsgvalue;
                jMsgvalue["client_type"] = "launcher";
                jMsgvalue["action"] = "mapping_control";
                jMsgvalue["params"] = jBody;
                WS_Send(jMsgvalue.toStyledString().c_str(), nConnectIndex_);
            }
            // 修改建图或者创建智能播报
            else if (strType == "data_update") {
                if (!jBody["changeType"].isNull()) // 1代表创建,2修改，3删除                               // 【逻辑在下面都实现了，所以先不用判断】
                {
                    int entityType = jBody["entityType"].asInt(); // 业务数据类型
                    string entityId = jBody["entityId"].asString(); // 发生变更的数据id
                    if (entityType == 10001) { // 智能播报【创建和更新提醒】
                        std::string data_string = jBody["data"].asString();
                        // 创建 JSON 对象
                        Json::CharReaderBuilder readerBuilder;
                        Json::Value data;
                        std::string errs;
                        // 解析 JSON
                        std::istringstream stream(data_string);
                        if (Json::parseFromStream(readerBuilder, stream, &data, &errs)) {
                            // 输出格式化后的 JSON
                            Json::StreamWriterBuilder writerBuilder;
                            std::string output = Json::writeString(writerBuilder, data);
                            // std::cout << output << std::endl;
                        } else {
                            std::cerr << "JSON 解析错误: " << errs << std::endl;
                        }
                        // const Json::Value& data = jBody["data"]; // data是一个string
                        long id = data["id"].asInt64(); // 数据 ID // 存起来
                        robotSt.setRemindId(id);
                        std::string deviceId = data["deviceId"].asString(); // 设备 ID
                        std::string title = data["title"].asString();       // 标题
                        int remindType = data["remindType"].asInt(); // 提醒类型：1-吃药提醒 2-日程提醒
                        bool enabled = data["enabled"].asBool(); // 启用状态

                        // int secondOfDay = data["time"]["secondOfDay"].asInt(); // 开始时间（一天中的秒数）
                        // int endSecondOfDay = data["time"]["endSecondOfDay"].asInt(); // 结束时间（一天中的秒数）
                        std::vector<std::string> weekDays;
                        for (const auto &day : data["time"]["weekDays"]) {
                            weekDays.push_back(day.asString()); // 取值集合"Mon","Tue","Wed","Thu","Fri","Sat","Sun"
                        }

                        int repeatType = data["time"]["repeatType"].asInt(); // 重复类型：1:每周; 2:单次
                        int dayOfMonth = data["time"]["dayOfMonth"].asInt(); // 一个月中的第几天
                        int month = data["time"]["month"].asInt(); // 月份
                        int year = data["time"]["year"].asInt();   // 年份

                        std::vector<std::string> contents;
                        // std::string text = ""; // 提醒文本
                        // for (const auto &content : data["contents"]) {
                        //     std::string contentType = content["contentType"].asString(); // 内容类型：1-播报内容 2-天气预报
                        //     text = content["text"].asString();          // 提醒文本
                        //     int location = content["location"].asInt(); // 所在城市
                        //     std::string locationStr = content["locationStr"].asString(); // 所在城市，天气提醒必填(冗余)

                        //     // 可以根据需要对内容进行处理或存储
                        //     contents.push_back(text);
                        // }
                        std::string text_single = ""; // 提醒文本
                        std::string text = ""; // 所有提醒文本
                        for (const auto& content : data["contents"]) {
                            text_single = content["text"].asString();
                            contents.push_back(text_single);
                            text += text_single;
                            text += "          ";
                        }
                        std::string remindLocationUid = data["remindLocation"]["uid"].asString(); // 提醒位置 uid
                        std::string remindLocationName = data["remindLocation"]["name"].asString(); // 提醒位置名称

                        int familyMemberId = data["familyMember"]["familyMemberId"].asInt(); // 关联家庭成员 id
                        std::string nickname = data["familyMember"]["nickname"].asString(); // 家庭成员昵称

                        bool running = data["running"].asBool();
                        if (!enabled && jBody["changeType"].asInt() != 1) {
                            // ROS_INFO_STREAM("no brocasting!");
                            // RobotBroadcastStatusToPlat(0);
                            timer_brocast.stop();
                            brocast_send_count_ = 0;
                            // ROS_INFO("APP Stop Brocast!");
                            homi_speech_interface::AssistantAbort resMsg;
                            brocast_abort_client.call(resMsg);
                        }
                        if (running) {
                            ROS_INFO("restart brocast!!");
                            brocast_text = text;
                            // sendStringToBrocast(text); // 向语音助手发送播报文本
                            // brocast_total_count_ = (endSecondOfDay - secondOfDay) / 40;
                            int secondOfDay = 0;
                            int endSecondOfDay = 0;
                            Json::Value time_broc = data["time"];
                            if (!time_broc["secondOfDay"].isNull()){  
                                secondOfDay = time_broc["secondOfDay"].asInt();
                            }
                            
                            if (!time_broc["endSecondOfDay"].isNull()) {
                                endSecondOfDay = time_broc["endSecondOfDay"].asInt();
                                if (endSecondOfDay != 0 && endSecondOfDay > secondOfDay) { // 如果是时间段的提醒
                                    brocast_total_count_ = (endSecondOfDay - secondOfDay) / 40;}
                                else{
                                    brocast_total_count_ = 1;// 说明是单点的提醒 (endSecondOfDay等于0也可能是单点提醒)
                                }
                            } else {
                                // endSecondOfDay 字段不存在，默认处理为单点提醒
                                brocast_total_count_ = 1;
                            }

                            timer_brocast.stop(); // 把上一次的定时器先关闭
                            brocast_send_count_ = 0;
                            timer_brocast.start();
                            ros::TimerEvent event;
                            SendBrocastCallback(event);
                            // ROS_INFO("Start Brocasting.");
                        }
                    } else if (entityType == 10006) {  // 10006表示家庭建图
                        int entityType = jBody["entityType"].asInt();  
                        std::string entityId = jBody["entityId"].asString(); //发生变更的数据id,填的是地图id，用于后续有多张地图的时候，更新指定地图的数据

                        // 读取 virtualwall 数据（注：data是一个string） && !jBody["data"]["virtualwall"].isNull()
                        if (!jBody["data"].isNull()){

                            // -------------------- 把data转为JSON -------------------- 
                            std::string data_string = jBody["data"].asString();
                            // 创建 JSON 对象
                            Json::CharReaderBuilder readerBuilder;
                            Json::Value data;
                            std::string errs;
                            // 解析 JSON
                            std::istringstream stream(data_string);
                            if (Json::parseFromStream(readerBuilder, stream, &data, &errs)) {
                                // 输出格式化后的 JSON
                                Json::StreamWriterBuilder writerBuilder;
                                std::string output = Json::writeString(writerBuilder, data);
                                // std::cout << output << std::endl;
                            } else {
                                std::cerr << "JSON 解析错误: " << errs << std::endl;
                            }
                            //  -------------------- 加入entityid -------------------- 
                            if (!jBody["data"].isNull())
                                data["entityId"] = jBody["entityId"];    // 感知那边定义的是entityID，平台定义的是entityId

                            //  -------------------- 再把JSON转为string发送出去 -------------------- 
                            Json::StreamWriterBuilder writerBuilder;
                            std::string navCtrlMsgs = Json::writeString(writerBuilder, data);
                            std_msgs::String virtualwall_msg;
                            virtualwall_msg.data = navCtrlMsgs; // 如果不转成JSON，也可以直接把data发送出去jBody["data"].asString();
                            publishVirtualWall.publish(virtualwall_msg);
                            ROS_INFO("publish virtualwall_msg, %s", virtualwall_msg.data.c_str());
                        }
                    }
                    else if (entityType == 11200)
                    {
                        ROS_INFO("Update emergency contacts");
                        std::string entityId = jBody["entityId"].asString();
                        int changeType = jBody["changeType"].asInt();
                        update_emergency_contacts(changeType,entityId,jBody["emergencyContact"]);
                    }
                    
                } else
                    ROS_ERROR("Undefined changeType %d", jBody["changeType"].asInt());
            } else if (strType == "move_points") {
                //导航任务需要切换到自主模式
                homi_speech_interface::RobdogAction zzmsg;
                zzmsg.actiontype = "NavCtrl";
                zzmsg.actionargument = "AutoMode";
                publishAction(zzmsg);
                expresstion_count = 0;
                // 定点移动
                // event：throwGarbage：丢垃圾；deliverExpress：寄快递；takePhotos：拍照；welcomeHome：欢迎回家
                system("/home/<USER>/updateexpression.sh  /home/<USER>/resource/left_right_look.mp4");
                std::string taskType;
                if (!jBody["event"].isNull()) {
                    taskType = jBody["event"].asString();
                    robotSt.setMoveTaskType(taskType);
                } else {
                    robotSt.setMoveTaskType("");
                }
                if (taskType == "takePhotos") {
                    readMappingPoints();
                    std::string navCtrlMsgs = msg->event;
                    // 打印 navCtrlMsgs
                    std::cout << "发过来的 navCtrlMsgs: " << navCtrlMsgs << std::endl;
                    homi_speech_interface::SIGCEvent photo_msg;
                    photo_msg.event = ReadMapCfg::getInstance().getPhonePoints(robotSt.getDeviceId());
                    if (photo_msg.event.empty()) {
                        photo_msg.event = PHOTO_POINT;
                    } else {
                        ROS_INFO("get PHOTO_POINT, %s", photo_msg.event.c_str());
                    }
                    ROS_INFO("PHOTO_POINT:%s", PHOTO_POINT);
                    moveToTargetAndPlayAudio(photo_msg);
                } else if (taskType == "deliverExpress") {
                    readMappingPoints();
                    std::string file_path = "/home/<USER>/resource/audio/出发去快递站.wav";
                    playAudio(file_path);
                    std::string navCtrlMsgs = msg->event;
                    // 打印 navCtrlMsgs
                    std::cout << "发过来的 navCtrlMsgs: " << navCtrlMsgs << std::endl;
                    homi_speech_interface::SIGCEvent press_msg;

                    press_msg.event = ReadMapCfg::getInstance().getPressPoints(robotSt.getDeviceId());
                    if (press_msg.event.empty()) {
                        press_msg.event = PRESS_POINT;
                    } else {
                        ROS_INFO("get PRESS_POINT, %s", press_msg.event.c_str());
                    }
                    moveToTarget(press_msg);
                } else if (taskType == "fetchExpress") {
                    readMappingPoints();
                    std::string file_path = "/home/<USER>/resource/audio/fetchExpress.wav";
                    playAudio(file_path);
                    std::string navCtrlMsgs = msg->event;
                    // 打印 navCtrlMsgs
                    std::cout << "发过来的 navCtrlMsgs: " << navCtrlMsgs << std::endl;
                    homi_speech_interface::SIGCEvent press_msg;

                    press_msg.event = ReadMapCfg::getInstance().getPressPoints(robotSt.getDeviceId());
                    if (press_msg.event.empty()) {
                        press_msg.event = PRESS_POINT;
                    } else {
                        ROS_INFO("get PRESS_POINT, %s", press_msg.event.c_str());
                    }
                    moveToTarget(press_msg);
                }
                else if (taskType == "familyMovePoint") {
                    ROS_INFO_STREAM("taskType == familyMovePoint");
                    homi_speech_interface::SIGCEvent family_move_point_msg;

                    Json::Value points = jBody["points"];

                    // family_move_point_msg.event = points.asString();
                    // if (family_move_point_msg.event.empty()) {
                    //     family_move_point_msg.event = FAMILY_MOVE_POINT;
                    // } else {
                    //     ROS_INFO("get PRESS_POINT, %s", family_move_point_msg.event.c_str());
                    // }
                    // moveToTarget(family_move_point_msg);

                    execId = jBody["execId"].asString();

                    homi_speech_interface::SIGCData resMsg;
                    Json::Value body;
                    resMsg.request.data = execute_result_response(value,execId,true,body);
                    ROS_INFO_STREAM("Send res to Server222222, res is " << resMsg.request.data);
                    if (platform_client.call(resMsg))
                        ROS_INFO("Response from server: %d", resMsg.response.errorCode);
                    else
                        ROS_ERROR("Failed to call service Service_demo errorCode:%d",
                        resMsg.response.errorCode);
                    
                }
                else if (taskType == "cancelMovement") {
                    homi_speech_interface::SIGCEvent cancel_msg;
                    cancel_msg.event = CANCEL_POINT;
                    moveToTarget(cancel_msg);
                } else {
                    homi_speech_interface::SIGCEvent move_msg;
                    Json::StreamWriterBuilder writerBuilder;
                    std::string moveMsgs = Json::writeString(writerBuilder, jBody);
                    move_msg.event = moveMsgs;
                    moveToTarget(move_msg);
                }
            } else if (strType == "point_report") {
                ROS_INFO_STREAM("cmd: point_report");
                int type = jBody["type"].asInt();
                int interval = jBody["interval"].asInt();
                if (1 == type) {
                    robPoseStatusTimer_.stop();
                    ros::Duration new_duration = ros::Duration((double)interval / 1000.0);
                    robPoseStatusTimer_ = nh_.createTimer(new_duration, &RobdogSubPub::timerRobotPoseCallback, this);
                }
            }

            // 播报立即执行
            else if (strType == "remind_ontime") { // 触发执行提醒【智能播报】
                long id = jBody["id"].asInt64();     // 数据 ID // 存起来
                robotSt.setRemindId(id);
                std::string deviceId = jBody["deviceId"].asString(); // 设备 ID
                std::string title = jBody["title"].asString();       // 标题
                int remindType = jBody["remindType"].asInt(); // 1-吃药提醒 2-日程提醒
                bool enabled = jBody["enabled"].asBool(); // 启用状态

                // int secondOfDay = jBody["time"]["secondOfDay"].asInt(); // 开始时间（一天中的秒数）
                // int endSecondOfDay = jBody["time"]["endSecondOfDay"].asInt(); // 结束时间（一天中的秒数）

                std::vector<std::string> weekDays;
                for (const auto &day : jBody["time"]["weekDays"]) {
                    weekDays.push_back(day.asString()); // 取值集合"Mon","Tue","Wed","Thu","Fri","Sat","Sun"
                }

                int repeatType = jBody["time"]["repeatType"].asInt(); // 重复类型：1:每周; 2:单次
                int dayOfMonth = jBody["time"]["dayOfMonth"].asInt();    // 一个月中的第几天
                int month = jBody["time"]["month"].asInt(); // 月份
                int year = jBody["time"]["year"].asInt();   // 年份

                std::vector<std::string> contents;
                std::string text_single = ""; // 提醒文本
                std::string text = ""; // 所有提醒文本
                for (const auto &content : jBody["contents"]) {
                    std::string contentType = content["contentType"].asString(); // 1-播报内容  2-天气预报
                    text_single = content["text"].asString();     // 提醒文本
                    int location = content["location"].asInt(); // 所在城市
                    std::string locationStr = content["locationStr"].asString(); // 所在城市，天气提醒必填(冗余)

                    // 可以根据需要对内容进行处理或存储
                    contents.push_back(text_single); // 示例：将文本内容存入 vector
                    // 要把文本拼成一个长文本播放
                    // text += "         "; // 给空字符串在语音播放上会停顿吗？
                    text += text_single;
                    text += "          ";
                }

                std::string remindLocationUid = jBody["remindLocation"]["uid"].asString(); // 提醒位置 uid
                std::string remindLocationName = jBody["remindLocation"]["name"].asString(); // 提醒位置名称

                int familyMemberId = jBody["familyMember"]["familyMemberId"].asInt(); // 关联家庭成员 id
                std::string nickname = jBody["familyMember"]["nickname"].asString(); // 家庭成员昵称
                if (!enabled) {
                    // ROS_INFO_STREAM("no brocasting!");
                    // RobotBroadcastStatusToPlat(0);
                    timer_brocast.stop();
                    brocast_send_count_ = 0;
                    // ROS_INFO("APP Stop Brocast!");
                    homi_speech_interface::AssistantAbort resMsg;
                    brocast_abort_client.call(resMsg); // 立即打断播放
                } else {
                    // sendStringToBrocast(text); // 向语音助手发送播报文本
                    // brocast_total_count_ = (endSecondOfDay - secondOfDay) / 40; // 人为设置了播放次数
                    int secondOfDay = 0;
                    int endSecondOfDay = 0;
                    if (!jBody["time"]["secondOfDay"].isNull()){  
                        secondOfDay = jBody["time"]["secondOfDay"].asInt();
                    }
            
                    if (!jBody["time"]["endSecondOfDay"].isNull()) {
                        endSecondOfDay = jBody["time"]["endSecondOfDay"].asInt();
                        if (endSecondOfDay != 0 && endSecondOfDay > secondOfDay) { // 如果是时间段的提醒
                            brocast_total_count_ = (endSecondOfDay - secondOfDay) / 40;}
                        else{
                            brocast_total_count_ = 1;// 说明是单点的提醒 (endSecondOfDay等于0也可能是单点提醒)
                        }
                    } else {
                        // endSecondOfDay 字段不存在，默认处理为单点提醒
                        brocast_total_count_ = 1;
                    }
                    brocast_text = text;
                    timer_brocast.stop();
                    brocast_send_count_ = 0; // 重新播放
                    timer_brocast.start();
                    ros::TimerEvent event;
                    SendBrocastCallback(event);
                }
            } else if (strType == "navigation_notify") { // 重定位请求
                int type = jBody["type"].asInt();
                if (type == 0) {
                    std::string strMsg = msg->event; // JSON字段
                    Json::Reader reader;
                    Json::Value value;
                    reader.parse(strMsg, value);
                    Json::Value jBody = value["body"];
                    Json::Value pointsArray = Json::Value(Json::arrayValue);
                    pointsArray.append(-1);
                    jBody["points"] = pointsArray;
                    Json::StreamWriterBuilder writerBuilder;
                    std::string navCtrlMsgs = Json::writeString(writerBuilder, jBody);
                    std_msgs::String nav_ctrl_msg;
                    nav_ctrl_msg.data = navCtrlMsgs;
                    actionPlanningMove_pub.publish(nav_ctrl_msg);
                }
            }
            else if (strType == "video_record")
            {
                std::string strMsg = msg->event; // JSON字段
                // Json::Value inValue;
                // reader.parse(strMsg, &inValue);

                homi_speech_interface::RobdogAction msg2;
                msg2.actiontype = "video_record";
                int len = jBody["len"].asInt();
                int yaw = jBody["yaw"].asInt();
                msg2.actionargument = to_string(yaw);
                string videoWriteUrl = jBody["videoWriteUrl"].asString();
                string videoReadUrl = jBody["videoReadUrl"].asString();
                execId = jBody["execId"].asString();
                publishAction(msg2);
                
                std::thread t(videoRecordThreadFunction,len,videoWriteUrl,videoReadUrl,execId,platform_client,strMsg);
                t.detach();
            
            }
            else if (strType == "unbind_notify"){
                ROS_INFO_STREAM("unbind_notify");
                homi_speech_interface::SIGCData resMsg;
                Json::FastWriter writer;
                std::string jsonString = writer.write(value);
                resMsg.request.data=jsonString;
                ROS_INFO_STREAM("Send res to APPServer, res is "<< resMsg.request.data);
                if (app_client.call(resMsg))
                    ROS_INFO("Response from APPserver: %d", resMsg.response.errorCode);
                else
                    ROS_ERROR("Failed to call APPservice Service_demo errorCode:%d",resMsg.response.errorCode);
            }
        } else {
            ROS_INFO_STREAM("NO ACTION!");
        }
    } catch (const Json::LogicError &e) {
        ROS_ERROR("Logic error: %s", e.what());
    } catch (const Json::RuntimeError &e) {
        ROS_ERROR("Runtime error: %s", e.what());
    }
}

// ********************************************* 处理APP消息的回调函数 ****************************************************

void RobdogSubPub::appCtrlCallBack(
    const homi_speech_interface::SIGCEventPtr &msg) {
  // 处理接收到的速度消息
  ROS_INFO("Received msg form APP: %s", msg->event.c_str());

  // 解析平台发送过来的控制指令
  std::string strMsg = msg->event; // JSON字段
  Json::Reader reader;
  Json::Value value;
  reader.parse(strMsg, value);
  if (value.isNull()) {
    ROS_INFO("json parse error");
    return;
  }
  try {
    // 检查必须的字段
    if (!value["deviceId"].isNull() && !value["event"].isNull()) {
      // 开始处理消息
      string strType = value["event"].asString();
      Json::Value jBody = value["body"];
      // 和特定的运动指令（如主动跟随及运动模式等）有关
      if (strType == "robot_action") {

        move_status_flag = false;

        std::string actionType = jBody["actionType"].asString();
        // 主动跟随【跟随的消息应该不是直接发给机器狗的，应该是websocket跟算法通信】
        if (actionType == "followMe") {
          std::string actionArgument;
          if (!jBody["actionArguement"].isNull()) {
            actionArgument = jBody["actionArguement"].asString();
          } else if (!jBody["actionArgument"].isNull()) {
            actionArgument = jBody["actionArgument"].asString();
          }
          // 开启跟随
          if (actionArgument == "on") {
            ROS_INFO_STREAM("Robot is following the user.");
            actionFollow(true);
          }
          // 关闭跟随
          else if (actionArgument == "off") {
            ROS_INFO_STREAM("Robot is not following the user.");
            actionFollow(false);
          }
        }
        // 运动模式设置
        else if (actionType == "sportMode") {
          try {
            std::string actionArgument;
            if (!jBody["actionArguement"].isNull()) {
              actionArgument = value["body"]["actionArguement"].asString();
            } else if (!jBody["actionArgument"].isNull()) {
              actionArgument = value["body"]["actionArgument"].asString();
            }
            auto it = sportModeMap.find(actionArgument);
            if (it != sportModeMap.end())
              publishStatusCtrl(DEEP_CMD_ACTION, sportModeMap[actionArgument],
                                0);
            else
              throw std::invalid_argument("Unknown actionArgument name: " +
                                          actionArgument);
          } catch (const std::exception &e) {
            std::cerr << e.what() << '\n';
          }
        }
        // 运动技能设定
        else if (actionType == "motorSkill") {
          std::string actionArgument;
          if (!jBody["actionArguement"].isNull()) {
            actionArgument = value["body"]["actionArguement"].asString();
          } else if (!jBody["actionArgument"].isNull()) {
            actionArgument = value["body"]["actionArgument"].asString();
          }
          homi_speech_interface::RobdogAction msg;
          msg.actiontype = "motorSkill";
          // 站立
            if (actionArgument == "standUp") {
                if(curState_==ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT){
                    ROS_ERROR("Current state is %d already,robot DO NOT need stand",curState_);
                    return;
                }
                msg.actionargument = "standUp";
                publishAction(msg);
                // ROS_INFO_STREAM("Recv standUp.");
            } else if (actionArgument == "getDown") {
                if(curState_==ROBDOG_STATUS_SITDOWM){
                    ROS_ERROR("Current state is %d already,robot DO NOT need getDown",curState_);
                    return;
                }
                msg.actionargument = "getDown";
                publishAction(msg);
                // ROS_INFO_STREAM("Recv getDown.");
            } else if (actionArgument == "greeting") {
                msg.actionargument = "greeting";
                publishAction(msg);
                // ROS_INFO_STREAM("Recv greeting.");
            } else if (actionArgument == "twistBody") {
                msg.actionargument = "twistBody";
                publishAction(msg);
                // ROS_INFO_STREAM("Recv twistBody.");
            } else if (actionArgument == "backflip") {
                msg.actionargument = "backflip";
                publishAction(msg);
                // ROS_INFO_STREAM("Recv backflip.");
            } else if (actionArgument == "jumpForward") {
                msg.actionargument = "jumpForward";
                publishAction(msg);
                // ROS_INFO_STREAM("Recv jumpForward.");
            } else if (actionArgument == "turnOver") {
                msg.actionargument = "turnOver";
                publishAction(msg);
                // ROS_INFO_STREAM("Recv turnOver.");
            } else if (actionArgument == "twistJump") {
                msg.actionargument = "twistJump";
                publishAction(msg);
                // ROS_INFO_STREAM("Recv twistJump.");
            } else if (actionArgument == "sitDown") {
                msg.actionargument = "sitDown";
                publishAction(msg);
                // ROS_INFO_STREAM("Recv sitDown.");
            } else if (actionArgument == "fingerHeart") {
                msg.actionargument = "fingerHeart";
                publishAction(msg);
                // ROS_INFO_STREAM("Recv fingerHeart.");
            } else if (actionArgument == "makeBow") {
                msg.actionargument = "makeBow";
                publishAction(msg);
                // ROS_INFO_STREAM("Recv makeBow.");
            } else if (actionArgument == "dance") {
                msg.actionargument = "dance";
                publishAction(msg);
            } else if (actionArgument == "shakeBody") {
                msg.actionargument = "shakeBody";
                publishAction(msg);
            } else if (actionArgument == "twistAss") {
                msg.actionargument = "twistAss";
                publishAction(msg);
            } else if (actionArgument == "twistBody_emergency") {
                msg.actionargument = "twistBody_emergency";
                publishAction(msg);
            } else if (actionArgument == "greeting_emergency") {
                msg.actionargument = "greeting_emergency";
                publishAction(msg);
            } else {
                ROS_INFO_STREAM(
                    "Unhandled action argument: " << actionArgument);
            }
        }
        else if (actionType == "resetZero") {
            homi_speech_interface::RobdogAction msg;
            msg.actiontype = "resetZero";
            msg.actionargument = "resetZero";
            publishAction(msg);
        } 
        //急停
        else if (actionType == "emergencyStop") {
          homi_speech_interface::RobdogAction msg;
          msg.actiontype = "emergencyStop";
          msg.actionargument = "emergencyStop";
          publishAction(msg);
        } else {
          ROS_INFO_STREAM("Unhandled action type: " << actionType);
        }
      }
      // 平台的控制指令（移动模式）
      else if (strType == "robot_move" || strType == "robot_view") {
        current_twist_msg_.linear.x = 0;
        current_twist_msg_.linear.y = 0;
        current_twist_msg_.linear.z = 0;
        current_twist_msg_.angular.x = 0;
        current_twist_msg_.angular.y = 0;
        current_twist_msg_.angular.z = 0;

        // 初始化current_continue_msg_全为0
        current_continue_msg_.event = strType; // 移动模式或者原地模式
        current_continue_msg_.x = 0;
        current_continue_msg_.y = 0;
        current_continue_msg_.z = 0;
        current_continue_msg_.yaw = 0;
        current_continue_msg_.pitch = 0;
        current_continue_msg_.roll = 0;

        // float resting_time = config.get("resting_time", 0.0f).asFloat();
        // 处理direction的信息
        Json::Value direction = jBody["direction"];
        // 是否是连续移动
        if (!jBody["actionType"].isNull()) {
            int actionType = jBody["actionType"].asInt();
            if (actionType == 1 && move_status_flag == false) {
                move_status_flag = true;
                string command =
                    "/home/<USER>/updateexpression2.sh "
                    "/home/<USER>/resource/vedio/look_right_left_step1.mp4 2 "
                    "/home/<USER>/resource/vedio/look_right_left_step2.mp4";
                system(command.c_str());
            } else if (actionType == 0 && move_status_flag == true) {
                move_status_flag = false;
                string command = "/home/<USER>/updateexpression.sh "
                                "/home/<USER>/resource/vedio/default.mp4";
                system(command.c_str());
            }

            if (actionType == 2) {
                // 每次执行一个动作之前先静止一会【只有步进模式下才需要】
                timer_.stop();
                velCmd_pub.publish(current_twist_msg_);
            }
            // float fspeed_x = config.get("fspeed_x", 0.0f).asFloat();
            // float fspeed_y = config.get("fspeed_y", 0.0f).asFloat();
            // float fspeed_z = config.get("fspeed_z", 0.0f).asFloat();

            ROS_INFO("fspeed_x: %f, fspeed_ydirection: %f, fspeed_z: %f", fspeed_x, fspeed_y, fspeed_z);

            int count = 0; // 要走的步数

            if (!direction["x"].isNull() && direction["x"].asInt() != 0) {
                int step_x = direction["x"].asInt();
                count = std::abs(step_x);
                current_continue_msg_.x = step_x;
                ROS_INFO("Continue Move,CMD FROM PLATFORM OR APP. STEP_X: %d, "
                        "fspeed_x: %f",
                        step_x, fspeed_x);
                current_twist_msg_.linear.x = (step_x > 0) ? fspeed_x : -fspeed_x;
            }
            if (!direction["y"].isNull() && direction["y"].asInt() != 0) {
                int step_y = direction["y"].asInt();
                count = std::abs(step_y);
                current_continue_msg_.y = step_y;
                ROS_INFO("Continue Move,CMD FROM PLATFORM OR APP. STEP_Y: %d, "  "fspeed_y: %f",  step_y, fspeed_y);
                current_twist_msg_.linear.y = (step_y > 0) ? fspeed_y : -fspeed_y;
            }
            if (!direction["z"].isNull() && direction["z"].asInt() != 0) {
                int step_z = direction["z"].asInt();
                current_continue_msg_.z = step_z;
            }
            if (!direction["yaw"].isNull() && direction["yaw"].asInt() != 0) {
                int yaw = direction["yaw"].asInt();
                count = std::ceil(static_cast<double>(yaw) / 15);
                current_continue_msg_.yaw = yaw;
                current_twist_msg_.angular.z =
                    (yaw > 0) ? fspeed_z : -fspeed_z; //转一次的角速度
                                                    // if(actionType == 2){
                                                    //     count = 0;
                //     current_continue_msg_.event = "stepMode";
                //     continueMoveCmd_pub.publish(current_continue_msg_); //
                //     用云深处的转向【现在暂不可用】
                // }
            }
            if (!direction["pitch"].isNull() && direction["pitch"].asInt() != 0) {
                float pitch = direction["pitch"].asInt();
                current_continue_msg_.pitch = pitch;
            }
            if (!direction["roll"].isNull() && direction["roll"].asInt() != 0) {
                float roll = direction["roll"].asInt();
                current_continue_msg_.roll = roll;
            }
            Proceationtype(actionType, count);
        }
      }
      // 机器狗模式
      else if (strType == "mode_set") {
        Json::Value deviceMode = jBody["deviceMode"];
        // 机器狗模式：0-宅家模式 1-遛狗模式 2-外出模式 3-遥控模式
        // 一期暂无外出模式
      }

      else if (strType == "properties_write") {
        ROS_INFO("APP set properties");
        setProperties(value);
      } else if (strType == "properties_read") {
        ROS_INFO("APP get properties");
        homi_speech_interface::SIGCData resMsg;
        resMsg.request.data = get_robot_properties(value);
        ROS_INFO_STREAM("Send res to APPServer, res is "
                        << resMsg.request.data);
        if (app_client.call(resMsg))
          ROS_INFO("Response from APPserver: %d", resMsg.response.errorCode);
        else
          ROS_ERROR("Failed to call APPservice Service_demo errorCode:%d",
                    resMsg.response.errorCode);
      } else if (strType == "connect_info_request") {
        ROS_INFO_STREAM("CMD : APPconnect_info_request");
        homi_speech_interface::SIGCData resMsg;
        resMsg.request.data = get_connect_info_request(value);
        ROS_INFO_STREAM("Send res to APPServer, res is "
                        << resMsg.request.data);
        if (app_client.call(resMsg))
          ROS_INFO("Response from APPserver: %d", resMsg.response.errorCode);
        else
          ROS_ERROR("Failed to call APPservice Service_demo errorCode:%d",
                    resMsg.response.errorCode);
      } else if (strType == "user_connect_change") {
        ROS_INFO("user_connect_change");
        if (jBody["changeType"].asInt() == 1) // 1代表建联
        {
          robotSt.setUserConnectStatus(1); // 1代表有用户连接
          robotSt.setUserPhoneNumber(jBody["phone"].asString());
        } else if (jBody["changeType"].asInt() == 2) // 2代表取消建联
        {
          robotSt.setUserConnectStatus(0); // 0代表无用户连接
          robotSt.setUserPhoneNumber("1399999999");
        } else
          ROS_ERROR("Undefined changeType %d", jBody["changeType"].asInt());
      }
      // 家庭建图功能
      else if (strType == "map_draw") {
        ROS_INFO("map_draw");
        if (jBody["action"].asString() == "start") {
          string url = jBody["url"].asString(); // 地图可写url
        } else if (jBody["action"].asString() == "suspend") {

        } else if (jBody["action"].asString() == "resume") {

        } else if (jBody["action"].asString() == "complete") {

        } else
          ROS_ERROR("Undefined map_draw");
      }
      // 修改建图
      else if (strType == "data_update") {
        ROS_INFO("data_update");
        if (jBody["changeType"].asInt() == 1) // 1代表创建
        {
          int entityType = jBody["entityType"].asInt(); // 业务数据类型，填10006
          string entityId = jBody["entityId"].asString(); // 发生变更的数据id

        } else if (jBody["changeType"].asInt() == 2) // 2代表修改
        {

        } else if (jBody["changeType"].asInt() == 3) // 3代表删除
        {

        } else
          ROS_ERROR("Undefined changeType %d", jBody["changeType"].asInt());
      }
    } else {
      ROS_INFO_STREAM("NO ACTION!");
    }
  } catch (const Json::LogicError &e) {
    ROS_ERROR("Logic error: %s", e.what());
  } catch (const Json::RuntimeError &e) {
    ROS_ERROR("Runtime error: %s", e.what());
  }
}

// 发给机器狗控制节点
void RobdogSubPub::publishAction(const homi_speech_interface::RobdogAction &robdogAction) {
    actionCmd_pub.publish(robdogAction);
    ROS_INFO("Published ActionType: %s, ActionArgument: %s",
            robdogAction.actiontype.c_str(),
            robdogAction.actionargument.c_str());
    // ROS_INFO("Published ActionType: %f, ActionArgument: %f",
    // robdogAction.actiontype, robdogAction.actionargument);
}

void RobdogSubPub::publishVelocity(const geometry_msgs::Twist &velocity) {
    velCmd_pub.publish(velocity);
    ROS_INFO("Published velocity: linear.x = %f, linear.y = %f, angular.z = "
            "%f, angular.x = %f, angular.y = %f, angular.z = %f",
            velocity.linear.x, velocity.linear.y, velocity.linear.z,
            velocity.angular.x, velocity.angular.y, velocity.angular.z);
}

void RobdogSubPub::checkStatusWatchdog(const ros::TimerEvent &) {
    if (watchDogMonitor) {
        // 检查是否超过2秒未收到消息
        if ((ros::Time::now() - lastMoveMessageTime).toSec() > 2.0) {
            ROS_WARN("No move message for more than two seconds. Stop Move.");
            watchDogMonitor = false; // 停止监测
            current_continue_msg_.event = "stopAction";
            continueMoveCmd_pub.publish(current_continue_msg_);
        }
    }
}

// 移动方式（步进还是持续移动）
void RobdogSubPub::Proceationtype(int actiontype, int step) {
    // 步进【基本上是语音发送的指令，直接把twist传到ctrl执行即可】
    if (actiontype == 2) {
        // ROS_INFO("Published velocity: linear.x = %f, linear.y = %f, angular.z =
        // %f, angular.x = %f, angular.y = %f, angular.z = %f",
        // current_twist_msg_.linear.x, current_twist_msg_.linear.y,
        // current_twist_msg_.linear.z, current_twist_msg_.angular.x,
        // current_twist_msg_.angular.y, current_twist_msg_.angular.z);
        // ROS_INFO("count = %d", step);
        sleepForDuration(resting_time); // 暂停1秒
        // 方式一：直接发送速度【配置在qt里面的方法】
        int count = std::abs(step); // 假设肯定不会发送0值
        setTotalCount(count);
        triggerTimerCallback();

        // 方式二：利用云深处新提供的接口【移动固定距离经常不灵，而且向左右移动固定距离是小碎步，不是明显的一步】
        // current_continue_msg_.event = "stepMode";
        // continueMoveCmd_pub.publish(current_continue_msg_);
    }
    // 连续移动【摇杆的指令，要通过轴指令控制狗】
    else if (actiontype == 1) {
        // 把current_continue_msg_传给控制节点
        watchDogMonitor = true;
        lastMoveMessageTime = ros::Time::now();
        continueMoveCmd_pub.publish(current_continue_msg_);
    } else {
        current_continue_msg_.event = "stopAction";
        continueMoveCmd_pub.publish(current_continue_msg_);
    }
}

// void RobdogSubPub::publishStatusCtrl(int cmd,int value,int
// exvalue,std::string& exmsg)
// {}

void RobdogSubPub::publishStatusCtrl(int cmd, int value, int exvalue) {
    homi_speech_interface::ProprietySet msg;
    msg.cmd = cmd;
    msg.value = value;
    msg.exvalue = exvalue;
    status_pub_.publish(msg);
    ROS_INFO("cmd [0x%x] value[%d] exvalu[%d]", cmd, value, exvalue);
}

// 持续发送（向APP）发送设备信息
void RobdogSubPub::publishProperties2APP(const ros::TimerEvent &) {
    /***********webSocket的超时处理逻辑**********/
    if ((getCurrentTimeStramp() - currentTimeStramp_) > WEBSOCKET_CON_TIMEOUT) {
        bConnected_ = false;
        std::cerr << "websocket reconnect" << std::endl;
        WS_Connect(strConnectUrl_.c_str());
    }
    /********************************************/

    homi_speech_interface::ProperToApp resMsg;
    Json::Value response(Json::objectValue);
    Json::Value newBody(Json::objectValue);

    newBody["properties"]["networkStatus"]["wifiState"] = robotSt.getWifiSwitch();
    newBody["properties"]["networkStatus"]["mobileDataState"] = robotSt.getMobileDataSwitch();
    newBody["properties"]["networkStatus"]["wifiName"] = robotSt.getWifiName();
    newBody["properties"]["networkStatus"]["isWifiConnect"] = (robotSt.getWifiSwitch() == "on");
    newBody["properties"]["battery"]["power"] = robotSt.getBatteryLevel();
    newBody["properties"]["battery"]["status"] = robotSt.getBatteryChargeStatus();
    newBody["properties"]["connect_info"]["changeType"] = robotSt.getUserConnectStatus();
    newBody["properties"]["connect_info"]["phone"] = robotSt.getUserPhoneNumber();

    // geometry_msgs::Pose pos = robotSt.getCurRobotPose();

    //将 geometry_msgs::Quaternion 转换为 tf::Quaternion
    // tf::Quaternion quat;
    // tf::quaternionMsgToTF(pos.orientation, quat);
    // 从四元数计算出欧拉角 (roll, pitch, yaw)
    // tf::Matrix3x3 mat(quat);
    // double roll, pitch, yaw;
    // mat.getRPY(roll, pitch, yaw);
    // newBody["properties"]["robot_pos"]["x"] = pos.position.x;
    // newBody["properties"]["robot_pos"]["y"] = pos.position.y;
    // newBody["properties"]["robot_pos"]["angle"] = (yaw * 180.0 / M_PI);

    response["body"] = newBody;
    // 将 Json::Value 转换成字符串
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    resMsg.devicePrope = jsonString;
    prope2app_pub.publish(resMsg);
}
