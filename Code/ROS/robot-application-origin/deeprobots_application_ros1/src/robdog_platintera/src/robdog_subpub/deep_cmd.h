/****命令码类型定义（灯光、动作、表情、语音、切网）****/
//调节灯光
#define DEEP_CMD_LIGHT                                   0x00000100  //命令，下同
#define DEEP_CMD_LIGHT_RES                               0x80000100  //响应，下同
//显示表情     
#define DEEP_CMD_EXPRESSION                              0x00000110
#define DEEP_CMD_EXPRESSION_RES                          0x80000110
//执行动作（运动技能）
#define DEEP_CMD_MOTION                                  0x00000120
#define DEEP_CMD_MOTION_RES                              0x80000120
//播放语音（命令值为具体语音文件路径）
#define DEEP_CMD_AUDIO                                   0x00000130
#define DEEP_CMD_AUDIO_RES                               0x80000130
//传感器
#define DEEP_CMD_SENSOR                                  0x00000140
#define DEEP_CMD_SENSOR_RES                              0x80000140//通过该CMD发送传感器数据
//切换网络命令
#define DEEP_CMD_NET                                     0x00000150
#define DEEP_CMD_NET_RES                                 0x80000150
//手电筒
#define DEEP_CMD_FLASHLIGHT                              0x00000160
#define DEEP_CMD_FLASHLIGHT_RES                          0x80000160
//音量
#define DEEP_CMD_VOLUME                                  0x00000170
#define DEEP_CMD_VOLUME_RES                              0x80000170
//电量
#define DEEP_CMD_POWER                                   0x00000180
#define DEEP_CMD_POWER_RES                               0x80000180
//动作模式
#define DEEP_CMD_ACTION                                  0x00000190
#define DEEP_CMD_ACRION_RES                              0x80000190
//自适应地形状态
#define DEEP_CMD_AI_MOTION                                0x00000200
#define DEEP_CMD_AI_MOTION_RES                            0x80000200
//移动和转动
#define DEEP_CMD_MOVEANDRO                               0x00000201
#define DEEP_CMD_MOVEANDRO_RES                               0x80000201


/****状态码定义****/
#define DEEP_STATUS_RUN                                   0x00000001 //运动状态
#define DEEP_STATUS_STOP                                  0x00000001 //停止状态
#define DEEP_STATUS_ERROR                                 0x00000002 //错误状态
#define DEEP_STATUS_OFFLINE                               0x00000003 //离线状态

/****命令值定义****/
//灯光类型
#define DEEP_CMD_LIGHT_1                                   0x00000101//蓝灯常亮（正常运行，联网正常）
#define DEEP_CMD_LIGHT_2                                   0x00000102//蓝灯闪烁（开机自检）
#define DEEP_CMD_LIGHT_3                                   0x00000103//蓝色呼吸灯（状态正常、等待联网中）
#define DEEP_CMD_LIGHT_4                                   0x00000104//黄灯常亮（机器人建联成功，APP可开启远程控制）
#define DEEP_CMD_LIGHT_5                                   0x00000105//黄灯闪烁（机器人互动建联中）
#define DEEP_CMD_LIGHT_6                                   0x00000106//红灯常亮（低电量）
#define DEEP_CMD_LIGHT_7                                   0x00000106//红灯闪烁（故障，需要重启）
#define DEEP_CMD_LIGHT_8                                   0x00000107//白色常亮
#define DEEP_CMD_LIGHT_9                                   0x00000108//白色呼吸
#define DEEP_CMD_LIGHT_A                                   0x00000109//黄色呼吸
#define DEEP_CMD_LIGHT_B                                   0x0000010A//橙色常亮

//表情类型
#define DEEP_CMD_EXPRESSION_1                              0x00000111//害羞
#define DEEP_CMD_EXPRESSION_2                              0x00000112//高兴
#define DEEP_CMD_EXPRESSION_3                              0x00000113//星星眼
#define DEEP_CMD_EXPRESSION_4                              0x00000114//墨镜酷
#define DEEP_CMD_EXPRESSION_5                              0x00000115//困
#define DEEP_CMD_EXPRESSION_6                              0x00000116//瞪眼凶
#define DEEP_CMD_EXPRESSION_7                              0x00000117//难过
#define DEEP_CMD_EXPRESSION_8                              0x00000118//眨眼睛

//动作类型(运动技能)
#define DEEP_CMD_MOTION_1                                   0x00000121//扭一扭
#define DEEP_CMD_MOTION_2                                   0x00000122//比心
#define DEEP_CMD_MOTION_3                                   0x00000123//站立
#define DEEP_CMD_MOTION_4                                   0x00000124//坐下
#define DEEP_CMD_MOTION_5                                   0x00000125//趴下
#define DEEP_CMD_MOTION_6                                   0x00000126//握手
#define DEEP_CMD_MOTION_7                                   0x00000127//空翻
#define DEEP_CMD_MOTION_8                                   0x00000128//俯卧撑
#define DEEP_CMD_MOTION_9                                   0x00000129//跳跃
#define DEEP_CMD_MOTION_A                                   0x0000012A//伸懒腰
#define DEEP_CMD_MOTION_B                                   0x0000012B//扑人
#define DEEP_CMD_MOTION_C                                   0x0000012C//拜年
#define DEEP_CMD_MOTION_D                                   0x0000012D//打滚
#define DEEP_CMD_MOTION_E                                   0x0000012E//侧边步
#define DEEP_CMD_MOTION_F                                   0x0000012F//交叉步
#define DEEP_CMD_MOTION_G                                   0x00000131//倒立走
#define DEEP_CMD_MOTION_H                                   0x00000132//舞蹈1
#define DEEP_CMD_MOTION_I                                   0x00000133//舞蹈2
#define DEEP_CMD_MOTION_J                                   0x00000134//急停

//网络类型
#define DEEP_CMD_NET_WIFION                                   0x00000151    //wifi  on
#define DEEP_CMD_NET_WIFIOFF                                   0x00000152    //wifi  off
#define DEEP_CMD_NET_5GON                                   0x00000153    //5G on 
#define DEEP_CMD_NET_5GOFF                                0x00000154    //5G off 

//传感器类型
#define DEEP_CMD_SENSOR_RES_BACK                         0x80000141     //背部传感器
#define DEEP_CMD_SENSOR_RES_HEAD                         0x80000142     //头部传感器
#define DEEP_CMD_SENSOR_RES_LOAD                         0x80000143     //载物传感器

//语音类型
#define DEEP_CMD_AUDIO_HEAD                              0x0000013E     //摸头语音
#define DEEP_CMD_AUDIO_BACK                              0x0000013F     //摸背语音

//动作模式
#define DEEP_CMD_ACTION_1                                   0x00000191//跟随
#define DEEP_CMD_ACTION_WALK                                0x21010300//步行/牵引
#define DEEP_CMD_ACTION_RUN                                 0x21010307//奔跑
#define DEEP_CMD_ACTION_STAIR                               0x21010407//爬楼
#define DEEP_CMD_ACTION_CLIMBE                              0x21010402//爬坡
#define DEEP_CMD_ACTION_SOFTSTOP                            0x21020C0E//软急停


struct head_t{
    int len; // 消息长度
    int cmd; // 命令码
    int stat;// 状态码
};//协议头

struct light_t {
    int color; // 颜色
    int duration; // 持续时间（待定）
};//指示灯

struct flashlight_t
{
    int flashswitch;
    int brightness;
};//手电筒


struct expression_t {
    int type; // 表情类型
};

struct motion_t {
    int type; // 动作（站立、趴下、作揖、比心等）
};

struct actionMode_t
{
    int type;       //动作模式选择（跟随followMe、步行walk、奔跑run、 爬楼stairClimbe、爬坡climbe、牵引traction）
};

struct intelligentMode_t
{
    int type;       //1开启自适应地形，0关闭自适应地形
};

struct sceneMode_t
{
    int type;       //场景模式选择：0-宅家模式 1-遛狗模式 2-外出模式 3-遥控模式
};

struct audio_t {
    char path[256]; // 音频文件路径
};

struct sensor_t {
    int sensor_id; // 传感器 ID
    int value; // 传感器值（状态）
    int timestamp; // 时间戳
};

struct net_t {
    int netswitch; // 网络选择
};

struct wifi_status{
    char wifi_name[256];
    int wifiSwitch; //on or off
    int isWifiConnect; //whether use wifi to access internet
};

struct power_status{
    int chargeStatus;   //0未在充电，1充电中 2 充满
    int power_level;
};

struct rotateAndMove_t{
    int angle;   //转动角度(弧度)
    int distance; //移动距离(米)
};

struct rqt_body_t {
    union {
        light_t light;
        expression_t expression;
        motion_t motion;
        audio_t audio;
        net_t net;
        actionMode_t actionType;
        intelligentMode_t intelligentSwitch;
        rotateAndMove_t attitude;
    };
};

struct rsp_body_t {
    union {
        sensor_t sensor;
        wifi_status wifiAttribute;
        power_status powerAttribute;
    };
};

typedef struct pk_t {
    head_t head;
    union {
        rqt_body_t rqt_body;
        rsp_body_t rsp_body;
    };
}pk_t;
