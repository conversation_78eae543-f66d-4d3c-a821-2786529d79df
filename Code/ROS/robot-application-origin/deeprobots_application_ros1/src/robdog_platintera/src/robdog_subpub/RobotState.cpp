#include "RobotState.h"
#include <iostream>
#include <ros/ros.h>

RobotState::RobotState(const std::string& configPath) : configPath(configPath) {
    loadConfig();
}

RobotState::~RobotState() {
    saveConfig();
}

void RobotState::loadConfig() {
    XMLDocument doc;
    XMLError error = doc.LoadFile(configPath.c_str());
    ROS_INFO_STREAM("Loading configuration file from: " << configPath.c_str());
    if (error != XML_SUCCESS) {
        // ROS_ERROR_STREAM ("Failed to load configuration file: " << configPath << std::endl);
        ROS_ERROR_STREAM("Failed to load configuration file: " << configPath << ", Error: " << doc.ErrorID() << ", Message: " << doc.ErrorStr());
        return;
    }

    XMLElement* root = doc.FirstChildElement("robot_state");
    if (!root) {
        ROS_ERROR_STREAM ("Root element not found in configuration file." << std::endl);
        return;
    }

    XMLElement* elem = root->FirstChildElement("network_status");
    if (elem && nullptr != elem->GetText()) {
        networkStatus = elem->GetText();
        ROS_INFO("robot networkStatus is  %s",networkStatus.c_str());
    }

    elem = root->FirstChildElement("wifi_name");
    if (elem && nullptr != elem->GetText()) {
        wifiName = elem->GetText();
        ROS_INFO("robot wifi_name is  %s",wifiName.c_str());
    }

    elem = root->FirstChildElement("scene_mode");
    if (elem && nullptr != elem->GetText()) {
        sceneMode = atoi(elem->GetText());
        ROS_INFO("robot scene_mode is  %d",sceneMode);
    }

    elem = root->FirstChildElement("user_connect_status");
    if (elem && nullptr != elem->GetText()) {
        userConnectStatus = atoi(elem->GetText());
        ROS_INFO("robot user_connect_status is  %d",userConnectStatus);
    } 

    elem = root->FirstChildElement("user_phone_number");
    if (elem && nullptr != elem->GetText()) {
        userPhoneNumber = elem->GetText();
        ROS_INFO("robot user_phone_number is  %s",userPhoneNumber.c_str());
    }

    elem = root->FirstChildElement("action_type");
    if (elem && nullptr != elem->GetText()) {
        actionType = elem->GetText();
        ROS_INFO("robot action_type is  %s",actionType.c_str());
    }

    elem = root->FirstChildElement("flash_status");
    if (elem && nullptr != elem->GetText()) {
        flashStatus = elem->GetText();
        ROS_INFO("robot flash_status is  %s",flashStatus.c_str());
    }

    elem = root->FirstChildElement("flash_brightness");
    if (elem && nullptr != elem->GetText()) {
        flashBrightness = atoi(elem->GetText());
        ROS_INFO("robot flash_brightness is  %d",flashBrightness);
    }

    elem = root->FirstChildElement("volume");
    if (elem && nullptr != elem->GetText()) {
        volume = atoi(elem->GetText());
        ROS_INFO("robot volume is  %d",volume);
    }

    elem = root->FirstChildElement("battery_charge_status");
    if (elem && nullptr != elem->GetText()) {
        batteryChargeStatus = atoi(elem->GetText());
        ROS_INFO("robot battery_charge_status is  %d",batteryChargeStatus);
    }

    elem = root->FirstChildElement("battery_level");
    if (elem && nullptr != elem->GetText()) {
        batteryLevel = atoi(elem->GetText());
        ROS_INFO("robot battery_level is  %d",batteryLevel);
    }

    elem = root->FirstChildElement("wifi_switch");
    if (elem && nullptr != elem->GetText()) {
        wifiSwitch = elem->GetText();
        ROS_INFO("robot wifi_switch is  %s",wifiSwitch.c_str());
    }

    elem = root->FirstChildElement("mobile_data_switch");
    if (elem && nullptr != elem->GetText()) {
        mobileDataSwitch = elem->GetText();
        ROS_INFO("robot mobile_data_switch is  %s",mobileDataSwitch.c_str());
    }

    elem = root->FirstChildElement("intelligent");
    if (elem && nullptr != elem->GetText()) {
        intelligentSwitch = elem->GetText();
        ROS_INFO("robot intelligent is  %s",intelligentSwitch.c_str());
    }

    elem= root->FirstChildElement("timestamp");
    if (elem && nullptr != elem->GetText()) {
        timestamp = std::stoll(elem->GetText());
        ROS_INFO("robot timestamp is  %lld",timestamp);
    }
}

void RobotState::saveConfig() {
    XMLDocument doc;
    XMLElement* root = doc.NewElement("robot_state");
    doc.InsertEndChild(root);

    XMLElement* elem = doc.NewElement("network_status");
    elem->SetText(networkStatus.c_str());
    root->InsertEndChild(elem);

    elem = doc.NewElement("user_connect_status");
    elem->SetText(userConnectStatus ? "1" : "0");
    root->InsertEndChild(elem);

    elem = doc.NewElement("user_phone_number");
    elem->SetText(userPhoneNumber.c_str());
    root->InsertEndChild(elem);

    elem = doc.NewElement("wifi_name");
    elem->SetText(wifiName.c_str());
    root->InsertEndChild(elem);

    elem = doc.NewElement("action_type");
    elem->SetText(actionType.c_str());
    root->InsertEndChild(elem);

    elem = doc.NewElement("flash_status");
    elem->SetText(flashStatus.c_str());
    root->InsertEndChild(elem);

    elem = doc.NewElement("flash_brightness");
    char buf[16];
    sprintf(buf, "%d", flashBrightness);
    elem->SetText(buf);
    root->InsertEndChild(elem);

    elem = doc.NewElement("volume");
    sprintf(buf, "%d", volume);
    elem->SetText(buf);
    root->InsertEndChild(elem);

    elem = doc.NewElement("scene_mode");
    sprintf(buf, "%d", sceneMode);
    elem->SetText(buf);
    root->InsertEndChild(elem);

    elem = doc.NewElement("battery_charge_status");
    sprintf(buf, "%d", batteryChargeStatus);
    elem->SetText(buf);
    root->InsertEndChild(elem);

    elem = doc.NewElement("battery_level");
    sprintf(buf, "%d", batteryLevel);
    elem->SetText(buf);
    root->InsertEndChild(elem);

    elem = doc.NewElement("wifi_switch");
    elem->SetText(wifiSwitch.c_str());
    root->InsertEndChild(elem);

    elem = doc.NewElement("mobile_data_switch");
    elem->SetText(mobileDataSwitch.c_str());
    root->InsertEndChild(elem);

    elem = doc.NewElement("intelligent");
    elem->SetText(intelligentSwitch.c_str());
    root->InsertEndChild(elem);

    elem = doc.NewElement("timestamp");
    char buftest[32];
    sprintf(buf, "%lld", timestamp);
    elem->SetText(buf);
    root->InsertEndChild(elem);

    // 移动的点位信息
    elem = doc.NewElement("photo_point");
    elem->SetText(PhotoPoint.c_str());
    root->InsertEndChild(elem);

    elem = doc.NewElement("press_point");
    elem->SetText(PressPoint.c_str());
    root->InsertEndChild(elem);

    XMLError error = doc.SaveFile(configPath.c_str());
    if (error != XML_SUCCESS) {
        ROS_ERROR_STREAM ("Failed to save configuration file: " << configPath<<"error code :" <<error << std::endl);
    }
}

// Getters
std::string RobotState::getNetworkStatus() const { return networkStatus; }
int RobotState::getUserConnectStatus() const { return userConnectStatus; }
std::string RobotState::getUserPhoneNumber() const { return userPhoneNumber; }
std::string RobotState::getActionType() const { return actionType; }
std::string RobotState::getFlashStatus() const { return flashStatus; }
std::string RobotState::getWifiName()const{return wifiName;}
int RobotState::getFlashBrightness() const { return flashBrightness; }
int RobotState::getVolume() const { return volume; }
int RobotState::getBatteryChargeStatus() const { return batteryChargeStatus; }
int RobotState::getBatteryLevel() const { return batteryLevel; }
int RobotState::getSceneMode()const{return sceneMode;}
long long RobotState::getTimeStamp()const{return timestamp;}
std::string RobotState::getWifiSwitch()const{return wifiSwitch;}
std::string RobotState::getMobileDataSwitch()const{return mobileDataSwitch;}
std::string RobotState::getIntelligentSwitch()const{return intelligentSwitch;}
std::string RobotState::getMoveTaskType()const{return strMoveTaskType;}
Json::Value RobotState::getCurRobotPose()const{return jsonCurRobotPose;}
std::string RobotState::getDeviceId()const{return strDeviceId;}

long RobotState::getRemindId()const{return remindId;}
std::string RobotState::getSectionId()const{return sectionId;}

std::string RobotState::getPhotoPoint()const{return PhotoPoint;}
std::string RobotState::getPressPoint()const{return PressPoint;}

// Setters
void RobotState::setNetworkStatus(const std::string& status) {
    networkStatus = status;
    saveConfig();
}
void RobotState::setUserConnectStatus(int status) {
    userConnectStatus = status;
    saveConfig();
}
void RobotState::setUserPhoneNumber(const std::string& number) {
    userPhoneNumber = number;
    saveConfig();
}
void RobotState::setActionType(const std::string& type) {
    actionType = type;
    saveConfig();
}
void RobotState::setFlashStatus(const std::string& status) {
    flashStatus = status;
    saveConfig();
}
void RobotState::setFlashBrightness(int brightness) {
    flashBrightness = brightness;
    saveConfig();
}
void RobotState::setVolume(int volume) {
    this->volume = volume;
    saveConfig();
}
void RobotState::setSceneMode(int sceneMode){
    this->sceneMode=sceneMode;
    saveConfig();
}

void RobotState::setWifiSwitch(const std::string& wifiS) {
    this->wifiSwitch=wifiS;
    saveConfig();
}

void RobotState::setIntelligentSwitch(const std::string& intelligentS) {
    this->intelligentSwitch=intelligentS;
    saveConfig();
}

void RobotState::setMobileDataSwitch(const std::string& mobileDataS){
    this->mobileDataSwitch=mobileDataS;
    saveConfig();
}

void RobotState::setBatteryLevel(int batteryLevel) {
    this->batteryLevel=batteryLevel;
    saveConfig();
}

void RobotState::setBatteryStatus(int chargeStatus) {
    this->batteryChargeStatus=chargeStatus;
    saveConfig();
}

void RobotState::setWifiName(const std::string& wifiName){
    this->wifiName=wifiName;
    saveConfig();
}

void RobotState::setMoveTaskType(const std::string& moveTaskType) {
    this->strMoveTaskType = moveTaskType;
}

void RobotState::setCurRobotPose(const Json::Value& pose) {
    this->jsonCurRobotPose = pose;
}

void RobotState::setDeviceId(const std::string& msg) {
    this->strDeviceId = msg;
}

void RobotState::setTimeStamp(long long  times) {
    this->timestamp = times;
    saveConfig();
}

void RobotState::setRemindId(long remindId) {
    this->remindId = remindId;
}
void RobotState::setSectionId(const std::string& sectionId) {
    this->sectionId = sectionId;
}
void RobotState::setPhotoPoint(const std::string& PhotoPoint) {
    this->PhotoPoint = PhotoPoint;
}
void RobotState::setPressPoint(const std::string& PressPoint) {
    this->PressPoint = PressPoint;
}
