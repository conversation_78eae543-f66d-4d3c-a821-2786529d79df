/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-10-01 10:04:29
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-10-02 21:02:13
 * @FilePath: \robot-application\deeprobots_application_ros1\src\robdog_platintera\src\robdog_subpub\read_map_point_config\read_map_point_cfg.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "read_map_point_cfg.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <jsoncpp/json/json.h>

ReadMapCfg::ReadMapCfg() {
}

ReadMapCfg::~ReadMapCfg() {
}

void ReadMapCfg::loadConfig(std::string& configPath) {
    std::ifstream file(configPath);
    if (!file.is_open()) {
        std::cerr << "Error opening file." << std::endl;
        return;
    }
    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string jsonString = buffer.str();
    file.close();
    std::cout << "json: " << configPath << "    " << jsonString << std::endl;
    Json::Value root;
    Json::CharReaderBuilder readerBuilder;
    std::string errs;
    std::istringstream s(jsonString);
    if (!Json::parseFromStream(readerBuilder, s, &root, &errs)) {
        std::cerr << "Error parsing JSON: " << errs << std::endl;
        return;
    }
    // 解析 JSON 数据
    for (const auto& item : root["mapping_point"]) {
        MapDeviceInfo device;
        device.deviceId = item["deviceId"].asString();

        Json::Value jPressPoint= item["press_point"];
        Json::StreamWriterBuilder writerPressPt;
        device.strPressPoints  = Json::writeString(writerPressPt, jPressPoint);

        Json::Value jPhonePoint= item["photo_point"];
        Json::StreamWriterBuilder writerPhotePt;
        device.strPhonePoints  = Json::writeString(writerPressPt, jPhonePoint);
        mpMapDeviceInfo[device.deviceId] = device;
    }
    // 打印解析结果
    for (const auto& it : mpMapDeviceInfo) {
        std::cout << "Device ID: " << it.first << std::endl;
        std::cout << "Press Points: " << it.second.strPressPoints << std::endl;
        std::cout << "Photo Points: " << it.second.strPhonePoints << std::endl;
        std::cout << std::endl;
    }
}

std::string ReadMapCfg::getPressPoints(string deviceId) {
    auto it = mpMapDeviceInfo.find(deviceId);
    if (it != mpMapDeviceInfo.end()){
        return it->second.strPressPoints;
    }
    return "";
}

std::string ReadMapCfg::getPhonePoints(string deviceId) {
    auto it = mpMapDeviceInfo.find(deviceId);
    if (it != mpMapDeviceInfo.end()){
        return it->second.strPhonePoints;
    }
    return "";
}

