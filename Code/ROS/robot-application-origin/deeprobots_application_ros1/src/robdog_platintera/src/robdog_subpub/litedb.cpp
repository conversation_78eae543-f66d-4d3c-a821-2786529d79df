#include "litedb.h"
#include <iostream>
#include <ros/ros.h>

SQLiteDB::SQLiteDB(const std::string& dbPath) : dbPath(dbPath), db(nullptr) {}

SQLiteDB::~SQLiteDB() {
    close();
}

bool SQLiteDB::open() {
    if (db != nullptr) {
        ROS_ERROR_STREAM("Database already open." << std::flush);
        return false;
    }
    int rc = sqlite3_open(dbPath.c_str(), &db);
    if (rc != SQLITE_OK) {
        ROS_ERROR_STREAM("Can't open database: " << sqlite3_errmsg(db) << std::flush);
        return false;
    }
    return true;
}

void SQLiteDB::close() {
    if (db != nullptr) {
        sqlite3_close(db);
        db = nullptr;
    }
}

bool SQLiteDB::execute(const std::string& sql) {
    if (db == nullptr) {
        ROS_ERROR_STREAM( "Database not open." << std::flush);
        return false;
    }
    char* errMsg = nullptr;
    int rc = sqlite3_exec(db, sql.c_str(), nullptr, nullptr, &errMsg);
    if (rc != SQLITE_OK) {
        ROS_ERROR_STREAM("SQL error: " << errMsg << std::flush);
        sqlite3_free(errMsg);
        return false;
    }
    return true;
}

bool SQLiteDB::createTable(const std::string& tableName, const std::string& columns) {
    std::string sql = "CREATE TABLE IF NOT EXISTS " + tableName + " (" + columns + ");";
    return execute(sql);
}

bool SQLiteDB::insert(const std::string& tableName, const std::map<std::string, std::string>& values) {
    std::string sql = "INSERT INTO " + tableName + " (";
    for (const auto& kv : values) {
        sql += kv.first + ", ";
    }
    sql.pop_back();
    sql.pop_back();
    sql += ") VALUES (";

    for (const auto& kv : values) {
        sql += "'" + kv.second + "', ";
    }
    sql.pop_back();
    sql.pop_back();
    sql += ");";

    return execute(sql);
}

bool SQLiteDB::update(const std::string& tableName, const std::map<std::string, std::string>& values, const std::string& condition) {
    std::string sql = "UPDATE " + tableName + " SET ";
    for (const auto& kv : values) {
        sql += kv.first + " = '" + kv.second + "', ";
    }
    sql.pop_back();
    sql.pop_back();
    sql += " WHERE " + condition + ";";

    return execute(sql);
}

bool SQLiteDB::remove(const std::string& tableName, const std::string& condition) {
    std::string sql = "DELETE FROM " + tableName + " WHERE " + condition + ";";
    return execute(sql);
}

std::vector<std::vector<std::string>> SQLiteDB::query(const std::string& sql) {
    if (db == nullptr) {
        std::cerr << "Database not open." << std::flush;
        return {};
    }

    results.clear();
    char* errMsg = nullptr;
    int rc = sqlite3_exec(db, sql.c_str(), staticCallback, this, &errMsg);
    if (rc != SQLITE_OK) {
        ROS_ERROR_STREAM("SQL error: " << errMsg << std::flush);
        sqlite3_free(errMsg);
        return {};
    }
    return results;
}

int SQLiteDB::staticCallback(void* data, int argc, char** argv, char** azColName) {
    SQLiteDB* dbInstance = static_cast<SQLiteDB*>(data);
    return dbInstance->callback(argc, argv, azColName);
}

int SQLiteDB::callback(int argc, char** argv, char** azColName) {
    std::vector<std::string> row;
    for (int i = 0; i < argc; ++i) {
        row.push_back(argv[i] ? argv[i] : "NULL");
    }
    results.push_back(row);
    return 0;
}


// 回调函数
static int callback(void *data, int argc, char **argv, char **azColName) {
    int i;
    fprintf(stderr, "%s:", (const char *)data);
    for (i = 0; i < argc; i++) {
        printf("%s = %s\n", azColName[i], argv[i] ? argv[i] : "NULL");
    }
    printf("\n");
    return 0;
}

/*int main() {
    SQLiteDB db("example.db");

    if (!db.open()) {
        return 1;
    }

    // 创建表
    db.createTable("users", "id INTEGER PRIMARY KEY, name TEXT, email TEXT");

    // 插入数据
    std::map<std::string, std::string> values = {
        {"name", "Alice"},
        {"email", "<EMAIL>"}
    };
    db.insert("users", values);

    // 查询数据
    std::vector<std::vector<std::string>> results = db.query("SELECT * FROM users");
    for (const auto& row : results) {
        for (const auto& col : row) {
            std::cout << col << " ";
        }
        std::cout << std::flush;
    }

    // 更新数据
    std::map<std::string, std::string> updateValues = {
        {"email", "<EMAIL>"}
    };
    db.update("users", updateValues, "name = 'Alice'");

    // 删除数据
    db.remove("users", "name = 'Alice'");

    db.close();
    return 0;
}*/