/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-10-01 10:05:05
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-10-01 10:26:34
 * @FilePath: \robot-application\deeprobots_application_ros1\src\robdog_platintera\src\robdog_subpub\read_map_point_config\read_map_point_config.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include <string>
#include <vector>
#include <map>

using namespace std;

struct Point {
    double x;
    double y;
    double angle;
    std::string option; // Optional, may be empty
};

struct MapDeviceInfo {
    std::string deviceId;
    std::string strPressPoints;
    std::string strPhonePoints;
};


template <typename T>
class Singleton {
public:
    static T& getInstance() {
        static T instance; 
        return instance;
    }

    Singleton(const Singleton&) = delete;
    Singleton& operator=(const Singleton&) = delete;

protected:
    Singleton() {} 
};



class ReadMapCfg: public Singleton<ReadMapCfg>  {
public:
    ReadMapCfg();
    ~ReadMapCfg();

    void loadConfig(std::string& configPath);

    std::string getPressPoints(string deviceId);
    std::string getPhonePoints(string deviceId);

public:
    map<string, MapDeviceInfo> mpMapDeviceInfo;

};