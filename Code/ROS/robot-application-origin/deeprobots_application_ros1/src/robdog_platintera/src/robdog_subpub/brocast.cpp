#include <homi_speech_interface/AssistantEvent.h>
#include "homi_speech_interface/AssistantSpeechText.h" 

// 智能播报服务的客户端【上传播报文本】
brocast_client=nh.serviceClient<homi_speech_interface::AssistantSpeechText>("/homi_speech/helper_assistant_speech_text_service");
timer_brocast = nh.createTimer(ros::Duration(60.0), &RobdogSubPub::SendBrocastCallback, this); 
bool timer_broadcast_running = false;// 定时器的开关状态
// 语音助手下发的是否播报被打断指令
brocast_sub=nh.subscribe("/homi_speech/speech_assistant_status_topic ", 100,&RobdogSubPub::BrocastIfAbortCallBack, this);   // homi_speech_interface::AssistantEvent 

// 接受语音助手上报的信息（是否终止）
void RobdogSubPub::BrocastIfAbortCallBack(const homi_speech_interface::AssistantEventPtr& msg) {
    std::string sectionId_past = robotSt.getSectionId(); // 语音助手之前上报的sectionId
    std::string sectionId_cur = msg->sectionId;
    std::string aborting = msg->description; 
    if(sectionId_past == sectionId_cur && aborting == "Aborting"){
        RobotBroadcastStatusToPlat(0); // 被打断

        timer_brocast.stop();
        brocast_send_count_ = 0;
    }
}

// 定时发送播报文本
void RobdogSubPub::SendBrocastCallback(const ros::TimerEvent&) {
    if (brocast_send_count_ < brocast_total_count_) { // 人为定义发送次数
        sendStringToBrocast(text); // 向语音助手发送播报文本
        ++brocast_send_count_; // 增加了定时器次数
        // ROS_INFO("Pub times: %d, Total Count_: %d", send_count_, total_count_); 
    } else {
        // 完成发送后停止定时器
        timer_brocast.stop();
        brocast_send_count_ = 0;
    }
}

// 调用服务向语音助手发送智能播报文本（收到的sectionId要保存起来）
void RobdogSubPub::sendStringToBrocast(const std::string& message) {
    ROS_INFO("Sending to brocast: %s", message.c_str()); // 播报文本
    // 创建请求消息
    homi_speech_interface::AssistantSpeechText resMsg; 
    resMsg.request.msg = message;

    // 调用服务并处理响应
    if (platform_client.call(resMsg)) {
        std::string sectionId = resMsg.response.sectionId; // 假设服务响应中有 sectionId
        robotSt.setSectionId(sectionId);
        ROS_INFO("Received sectionId from server: %s", sectionId.c_str());
    } else {
        ROS_ERROR("Failed to call service, errorCode: %d", resMsg.response.errorCode);
    }
}

// 向平台上传智能播报状态【不要定时,被打断了才会上报】
void RobdogSubPub::RobotBroadcastStatusToPlat(int status) {
    // 构建状态报告的 JSON
    Json::Value response;
    
    response["remindId"] = robotSt.getRemindId(); // 设备 ID【直接读取】
    response["domain"] = "ROBOT_BUSINESS_DEVICE"; // 域
    response["event"] = "broadcast_report"; // 事件类型
    response["eventId"] = "111111111"; // 事件 ID
    response["seq"] = to_string(getCurrentTimeStramp()); // 时间戳作为序列号

    // 以定时器的开关作为播报任务的状态
    // int status = timer_broadcast_running ? 1 : 0; // 1 - 正常运行, 0 - 被打断  
    response["body"]["status"] = status; // 状态字段：0-打断，1-正常运行

    // 将 JSON 转换为字符串
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    ROS_INFO("cmd : %s", jsonString.c_str());
    
    homi_speech_interface::SIGCData resMsg;
    resMsg.request.data = jsonString; // 设置请求数据
    ROS_INFO_STREAM("Send res to Server, res is "<< resMsg.request.data);
    
    if (platform_client.call(resMsg)) {
        ROS_INFO("Response from server: %d", resMsg.response.errorCode); // 打印服务器响应
    } else {
        ROS_ERROR("Failed to call service Service_demo errorCode:%d", resMsg.response.errorCode); // 错误处理
    }
}

// 接收平台消息
if(entityType == 10001){ // 智能播报【创建和更新提醒】
    const Json::Value& data = jBody["data"];
    long long id = data["id"].asInt64(); // 数据 ID // 存起来
    std::string deviceId = data["deviceId"].asString(); // 设备 ID
    std::string title = data["title"].asString(); // 标题
    int remindType = data["remindType"].asInt(); // 提醒类型：1-吃药提醒 2-日程提醒
    bool enabled = data["enabled"].asBool(); // 启用状态

    if(enabled == false){
        RobotBroadcastStatusToPlat(0);
        timer_brocast.stop();
        brocast_send_count_ = 0;
        ROS_INFO("APP Stop Brocast!"); 
    }

    int secondOfDay = data["time"]["secondOfDay"].asInt(); // 开始时间（一天中的秒数）
    int endSecondOfDay = data["time"]["endSecondOfDay"].asInt(); // 结束时间（一天中的秒数）
    std::vector<std::string> weekDays;
    for (const auto& day : data["time"]["weekDays"]) {
        weekDays.push_back(day.asString()); // 取值集合 "Mon","Tue","Wed","Thu","Fri","Sat","Sun"
    }

    int repeatType = data["time"]["repeatType"].asInt(); // 重复类型：1:每周; 2:单次
    int dayOfMonth = data["time"]["dayOfMonth"].asInt(); // 一个月中的第几天
    int month = data["time"]["month"].asInt(); // 月份
    int year = data["time"]["year"].asInt(); // 年份

    std::vector<std::string> contents;
    std::string text = ""; // 提醒文本
    for (const auto& content : data["contents"]) {
        std::string contentType = content["contentType"].asString(); // 内容类型：1-播报内容 2-天气预报
        text = content["text"].asString(); // 提醒文本
        int location = content["location"].asInt(); // 所在城市
        std::string locationStr = content["locationStr"].asString(); // 所在城市，天气提醒必填(冗余)

        // 可以根据需要对内容进行处理或存储
        contents.push_back(text); 
    }

    std::string remindLocationUid = data["remindLocation"]["uid"].asString(); // 提醒位置 uid
    std::string remindLocationName = data["remindLocation"]["name"].asString(); // 提醒位置名称

    int familyMemberId = data["familyMember"]["familyMemberId"].asInt(); // 关联家庭成员 id
    std::string nickname = data["familyMember"]["nickname"].asString(); // 家庭成员昵称

    bool running = data["running"].asBool(); 
    if(running){
        // sendStringToBrocast(text); // 向语音助手发送播报文本
        brocast_total_count_ = (endSecondOfDay - secondOfDay) / 60;
        timer_brocast.start();
        ros::TimerEvent event;
        SendBrocastCallback(event);
        ROS_INFO("Start Brocasting.");
    }
}

// 立即执行
else if(strType == "remind_ontime"){ // 触发执行提醒【智能播报】
    int remindType = jBody["remindType"].asInt();    // 1-吃药提醒 2-日程提醒
    bool enabled = jBody["enabled"].asBool(); // 启用状态
    if(enabled == false){
        RobotBroadcastStatusToPlat(0);
        timer_brocast.stop();
        brocast_send_count_ = 0;
        ROS_INFO("APP Stop Brocast!"); 
    }
    int secondOfDay = jBody["time"]["secondOfDay"].asInt(); // 开始时间（一天中的秒数）
    int endSecondOfDay = jBody["time"]["endSecondOfDay"].asInt(); // 结束时间（一天中的秒数）

    std::vector<std::string> weekDays;
    for (const auto& day : jBody["time"]["weekDays"]) {
        weekDays.push_back(day.asString()); // 取值集合"Mon","Tue","Wed","Thu","Fri","Sat","Sun"
    }

    int repeatType = jBody["time"]["repeatType"].asInt(); // 重复类型：1:每周; 2:单次
    int dayOfMonth = jBody["time"]["dayOfMonth"].asInt(); // 一个月中的第几天
    int month = jBody["time"]["month"].asInt(); // 月份
    int year = jBody["time"]["year"].asInt(); // 年份

    std::vector<std::string> contents;
    std::string text = ""; // 提醒文本
    for (const auto& content : jBody["contents"]) {
        std::string contentType = content["contentType"].asString(); // 1-播报内容  2-天气预报
        text = content["text"].asString(); // 提醒文本
        int location = content["location"].asInt(); // 所在城市
        std::string locationStr = content["locationStr"].asString(); // 所在城市，天气提醒必填(冗余)
        
        // 可以根据需要对内容进行处理或存储
        contents.push_back(text); // 示例：将文本内容存入 vector
    }

    std::string remindLocationUid = jBody["remindLocation"]["uid"].asString(); // 提醒位置 uid
    std::string remindLocationName = jBody["remindLocation"]["name"].asString(); // 提醒位置名称

    int familyMemberId = jBody["familyMember"]["familyMemberId"].asInt(); // 关联家庭成员 id
    std::string nickname = jBody["familyMember"]["nickname"].asString(); // 家庭成员昵称
    // sendStringToBrocast(text); // 向语音助手发送播报文本
    timer_brocast.start();
    ros::TimerEvent event;
    SendBrocastCallback(event);
    brocast_total_count_ = (endSecondOfDay - secondOfDay) / 60;
    ROS_INFO("Start Brocasting.");
    // 开启定时器
    // timer = ros::NodeHandle().createTimer(ros::Duration(0.03), &DeepUdpCtrl::timerCallbackForRobotMove,this);
    // timer_broadcast_running = true;
    // ROS_INFO("Timer is running,Robot move started.");
}