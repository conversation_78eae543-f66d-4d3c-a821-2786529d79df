/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-09-10 14:22:33
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-09-11 19:19:17
 * @FilePath: \robot-application\deeprobots_application_ros1\src\robdog_platintera\src\robdog_subpub\RobotState.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#pragma once

#include <string>
#include <fstream>
#include <tinyxml2.h>
#include <geometry_msgs/Pose.h>
#include <jsoncpp/json/json.h>


using namespace tinyxml2;

class RobotState {
public:
    RobotState(const std::string& configPath);
    ~RobotState();

    // Getters
    std::string getNetworkStatus() const;//获取当前连接哪个wifi or data
    int getUserConnectStatus() const;
    std::string getUserPhoneNumber() const;
    std::string getActionType() const;
    std::string getFlashStatus() const;
    std::string getWifiName() const;
    std::string getWifiSwitch() const;
    std::string getMobileDataSwitch() const;
    int getSceneMode() const;
    int getFlashBrightness() const;
    int getVolume() const;
    int getBatteryChargeStatus() const;
    int getBatteryLevel() const;
    long long getTimeStamp() const;
    std::string  getIntelligentSwitch() const;
    std::string getMoveTaskType() const;
    Json::Value getCurRobotPose() const;
    std::string getDeviceId() const;
    long getRemindId()const;
    std::string getSectionId()const;

    std::string getPhotoPoint()const;
    std::string getPressPoint()const;


    // Setters
    void setNetworkStatus(const std::string& status);
    void setUserConnectStatus(int status);
    void setUserPhoneNumber(const std::string& number);
    void setActionType(const std::string& type);
    void setFlashStatus(const std::string& status);
    void setFlashBrightness(int brightness);
    void setSceneMode(int sceneMode);
    void setVolume(int volume);
    void setWifiSwitch(const std::string& wifiS) ;
    void setMobileDataSwitch(const std::string& mobileDataS) ;
    void setBatteryLevel(int batteryLevel) ;
    void setBatteryStatus(int chargeStatus) ;
    void setIntelligentSwitch(const std::string& intelligentS);
    void setWifiName(const std::string& wifiName);
    void setMoveTaskType(const std::string& moveTaskType);
    void setCurRobotPose(const Json::Value& pose);
    void setDeviceId(const std::string& msg);
    void setTimeStamp(long long times);

    void setRemindId(long remindId);
    void setSectionId(const std::string& sectionId);

    void setPhotoPoint(const std::string& PhotoPoint);
    void setPressPoint(const std::string& PressPoint);

private:
    std::string configPath;
    std::string networkStatus;
    std::string wifiName;
    std::string wifiSwitch;         //wifi页面开关是否打开
    std::string mobileDataSwitch;   //流量页面开关是否打开
    std::string intelligentSwitch;   //自适应地形模式开关，保存状态。
    long long timestamp;         //下发的时间戳
    int userConnectStatus;
    std::string userPhoneNumber;
    int sceneMode;  //0-宅家模式 1-遛狗模式 2-外出模式 3-遥控模式  
    std::string actionType;//followMe、sportMode、intelligent
    std::string flashStatus;
    int flashBrightness;
    int volume;
    int batteryChargeStatus;    //0未在充电，1充电中 2 充满
    int batteryLevel;
    void loadConfig();
    void saveConfig();

    //定点移动任务类型
    // "throwGarbage" 丢垃圾
    // "fetchExpress" 取快递 
    // "takePhotos" 拍照
    // "welcomeHome" 欢迎回家
    std::string strMoveTaskType;

    //机器人实时位置
    Json::Value jsonCurRobotPose;

    std::string strDeviceId;

    // 智能播报相关
    long remindId;
    std::string sectionId;

    // 点位相关
    std::string PhotoPoint;
    std::string PressPoint;
};