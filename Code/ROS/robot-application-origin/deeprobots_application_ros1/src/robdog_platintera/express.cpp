// 播放狗子的表情
#include <iostream>
#include <cstdlib>
#include <string>

void loadVideo(const std::string& path, int flag) {
    std::string videoPath;
    if (flag == 1) {
        videoPath = path;  // 使用传入的路径
    } else {
        videoPath = "/path/to/default/video";  // 使用默认路径
    }
    std::string command = "echo '{ \"command\": [\"loadfile\", \"" + videoPath + "\", \"replace\"] }' | socat - /tmp/mpv-socket";
    system(command.c_str());
}

int main() {
    loadVideo("/path/to/new/video", 1);  //切换
    sleep(5);
    loadVideo("", 0);  
    return 0;
}
