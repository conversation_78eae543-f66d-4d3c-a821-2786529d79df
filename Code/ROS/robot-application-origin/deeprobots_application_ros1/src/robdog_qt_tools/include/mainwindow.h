#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include "rclcomm.h"
// #include "codeinfo2qt.h" // 新增
// #include "follow_me_qt_tools/include/rclcomm.h" // ck:修改
#include <iostream>
// #include "follow_msgs/msg/target_vel_info.hpp"  // 新增

// #include "homi_speech_interface/msg/SIGCEvent.hpp" 
#include <homi_speech_interface/SIGCEvent.h>
// #include <Qstring> // 新增
QT_BEGIN_NAMESPACE
namespace Ui { class mainWindow; }
QT_END_NAMESPACE

enum WS_SEN_MSG_TYPE_E{
    
    EN_WS_SEND_LAUNCHER_CON_SUCEESS = 0,        // 连接成功
    EN_WS_SEND_LAUNCHER_START_FOLLOW,           // 开启跟随
    EN_WS_SEND_LAUNCHER_END_FOLLOW,             // 停止跟随
};

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();
    void sendWsMsg(WS_SEN_MSG_TYPE_E enType);
    void sendActionMsg(const string& msgs);

private:
    Ui::mainWindow *ui;
    std::shared_ptr<rclcomm> m_pCommonNode = nullptr;
    MOVE_TYPE_E m_type = EN_STOP;

    //std::shared_ptr<InfoToQt> InfoToQt_ = nullptr; //新增：调用codeinfo2qt里的函数

signals:
    void signalParseWsMsgCallback(QString);
    void signalSendTakephotoCmd();
    // void signalSendRobotCmd(int, double);
    // void signalSendSubpubCmd(int, QString);

public slots:
    // void onLogCallback(QString);
    // void onParseWsMsgCallback(QString);
    void onCurrentTabChange(int index);
    void onRecvServiceMsg(QString);

    // void onBtnUpdateLauncherParam();
    // void onBtnUpdateFollowParam(); // 新建声明
    
    // void updateUiTargetVel(follow_msgs::msg::TargetVelInfo targetVel_msg); // 新建声明
    void onMoveClicked();
    void onUpdateUDPClicked();
    QString createJsonString(int x, int y, int z, int pitch, int roll, int yaw);
    QString createPlanningMoveJsonString();
    void onMoveStepClicked();
    void onBtnTakephoto();
};
#endif // MAINWINDOW_H
