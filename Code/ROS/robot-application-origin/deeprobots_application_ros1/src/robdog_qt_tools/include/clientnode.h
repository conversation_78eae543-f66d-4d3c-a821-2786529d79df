#ifndef CLIENTNODE_H
#define CLIENTNODE_H
// #include <actionlib/client/simple_action_client.h>
#include <functional>
#include <future>
#include <memory>
#include <string>
#include <sstream>
#include <QObject>

#include <geometry_msgs/Twist.h>
// #include <follow_msgs/TargetVelInfo.h>
// #include <follow_msgs/FollowcfgAction.h>  // 假设动作名为 FollowcfgAction
// #include "homi_speech_interface/msg/SIGCEvent.hpp" 
#include <ros/ros.h>
// #include <std_msgs/bool.h>
#include <homi_speech_interface/SIGCEvent.h>
#include <homi_speech_interface/RobdogState.h>

#include <homi_speech_interface/ProprietySet.h>
#include <homi_speech_interface/RobdogAction.h>
#include <homi_speech_interface/ContinueMove.h>
#include <homi_speech_interface/SIGCEvent.h>
#include <homi_speech_interface/AssistantEvent.h>
#include <homi_speech_interface/NewUdpConnect.h>
#include <std_msgs/String.h>
// #include <homi_speech_interface/Robotcfg.h>
#include <geometry_msgs/Pose.h>
#include <tf/transform_broadcaster.h>
#include <tf/transform_listener.h>
#include <tf/LinearMath/Quaternion.h>

// 接收的消息回调
typedef void(*ServeMsgCallback)(void*, const char* msg);

class FollowCfgClient {
public:
    FollowCfgClient(ros::NodeHandle& nh);
    ~FollowCfgClient(); 
  // typedef follow_msgs::FollowcfgAction CfgClient;
  // typedef actionlib::SimpleActionClient<follow_msgs::FollowcfgAction> CfgClientActionClient;
  // action应该暂时用不到
  // typedef homi_speech_interface::Robotcfg CfgClient;
  // typedef actionlib::SimpleActionClient<homi_speech_interface::Robotcfg> CfgClientActionClient;

  // explicit FollowCfgClient(ros::NodeHandle& nh);
  // void ServeMsgCallback (callback, void* pHandle);
  // void sendGoalInternal(const std::string& strMsgs);
  
  void sendRobotCmd(const geometry_msgs::Twist& twist);
  void sendUdpConnectCmd(const homi_speech_interface::NewUdpConnect& msg);
  // void pubRobotTargetVel(const follow_msgs::TargetVelInfo& info);
  void sendSubpubCmd(const homi_speech_interface::SIGCEvent& info);
  
  // void targetVelInfoCallback(const follow_msgs::TargetVelInfo::ConstPtr& info);
  
  void pubGetStragyParams();
  void sendTakephotoCmd(const std_msgs::String& info);

  void setServeMsgCallback(ServeMsgCallback callback, void* pHandle);
  // 把感知主机上报的信息显示到界面上
  void navPositionCallback(const geometry_msgs::Pose::ConstPtr& pos);
  void navStatusCallback(const std_msgs::String::ConstPtr& msg);
  
  void robdog_state_info_callback(const homi_speech_interface::RobdogState::ConstPtr& info);

  // ***************************** 日志更新到界面 *****************************
  void robctrlCallback(const homi_speech_interface::SIGCEventPtr& msg); 
  void deepStatusCallback(const homi_speech_interface::ProprietySetPtr& msg);
  void userDefinedCtrlCallback(const homi_speech_interface::ProprietySetPtr& msg);
  void velCmdCallback(const geometry_msgs::TwistPtr& msg);
  void MoveSkillscallback(const homi_speech_interface::RobdogActionPtr& msg);
  void continueMovecallback(const homi_speech_interface::ContinueMovePtr& msg);
  void BrocastIfAbortCallBack(const homi_speech_interface::AssistantEventPtr& recmsg);

private:
  ros::NodeHandle nh_;  // ROS节点句柄
  // Action client
  // CfgClientActionClient ac_;

  // // Callback functions for action client
  // void goalResponseCallback(const actionlib::SimpleClientGoalState& state);
  // void feedbackCallback(const homi_speech_interface::RobotcfgFeedback::ConstPtr& feedback);
  // void resultCallback(const homi_speech_interface::RobotcfgResult::ConstPtr& result);

  // 接受消息回调函数
  ServeMsgCallback servMsgCallback_;
  void* handle_ = nullptr;
  // 设置初始状态为未到达目标
  bool at_target_ = false;
  // Publishers
  ros::Publisher pubRobotTwist_;
  ros::Publisher pubRobotUdpConnect_;
  ros::Publisher pubTargetVel_;
  ros::Publisher pubTargetStragyParam_;
  ros::Publisher pubPlatMsg; // 模拟平台发布的消息
  ros::Publisher pubtakephoto_;



  // Subscribers
  ros::Subscriber subTargetVel_;
  ros::Subscriber subNavPosition_;
  ros::Subscriber subNavStatus_;

  ros::Subscriber velCmd_sub_;// 平台下发的数据
  ros::Subscriber deepCtrl_sub_;
  ros::Subscriber subRobotState_;// 狗子的状态
  ros::Subscriber userDefinedSub_;
  ros::Subscriber velCmd_;// 狗子收到的指令信息
  ros::Subscriber actionCmd_;
  ros::Subscriber continueMoveCmd_;
  ros::Subscriber brocast_sub;

};
#endif // CLIENTNODE_H