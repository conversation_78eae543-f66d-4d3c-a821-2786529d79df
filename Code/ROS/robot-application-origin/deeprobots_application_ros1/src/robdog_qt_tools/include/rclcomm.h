#ifndef RCCOMM_H
#define RCCOMM_H
// 此头文件主要用于定义信号和槽
#include <QObject>
#include <QThread>
#include <iostream>
#include <ros/ros.h>
// #include <rclcpp/rclcpp.hpp>
// #include <std_msgs/msg/int32.hpp>
#include <string>
#include <QString>
#include "clientnode.h"  
// #include "mainwindow.h"  // 新增：能让rclcomm调用到mainwindow里的函数
// #include "follow_msgs/msg/target_vel_info.hpp" // 新增：在install文件夹下

enum MOVE_TYPE_E { // 枚举不同的动作类型
    EN_ADD_X = 0,
    EN_REDUCE_X,
    EN_ADD_Y,
    EN_REDUCE_Y,
    EN_ADD_RZ,
    EN_REDUCE_RZ,
    EN_STOP,
    
    // 摇杆模式
    EN_CONTINUE, // 持续运动
    EN_YDCONTINUE,  // m_pBtnyuandiContinue
    EN_CONSTOP, //停止持续运动

    // 运动模式
    EN_WALK, // m_pBtnwalk
    EN_RUN,// m_pBtnrun
    EN_TRACTION,// m_pEdttraction
    EN_STAIRCLI,// m_pBtnstairClimbe
    EN_CLIMBE,// m_pBtnclimbe
    // 运动技能
    EN_STAND,// m_pBtnstandUp
    EN_GETDOWN,// m_pBtngetDown
    EN_TWISTBODY,// m_pBtntwistBody
    EN_TWISTBODY_EMR,
    EN_TWISTJUMP,// m_pBtntwistJump
    EN_GREET,// m_pBtngreeting
    EN_GREET_EMR,
    EN_JUMPFORWARD,// m_pBtnjumpForward
    EN_MAKEBOW,// m_pBtnmakeBow
    EN_BACKFLIP,// m_pBtnbackflip
    EN_SITDOWN,// m_pEdtsitDown
    EN_FINGERHEART,// m_pBtnfingerHeart
    EN_TURNOVER,// m_pBtnturnOver

    EN_DANCE,
    EN_YAOBAI,
    EN_BENGDA,

    EN_PLANNINGMOVE,// m_pBtnPlanningMove  定位移动任务（依赖建图定位下发点位）
    EN_STOPPLANNINGMOVE, // 取消移动 m_pBtStopPlan
    EN_RELOCATION,  // 重定位 m_pBtReLocation

    EN_STARTUWBFOLLOW,
    EN_STARTFOLLOW,
    EN_ENDFOLLOW,

    EN_ESTOP,
    EN_GOZERO,
    
    EN_OBSTACLESRL,
    EN_FLATRL,
    EN_EXITRL
};

using namespace std;
class rclcomm : public QThread 
{
    Q_OBJECT
public:
    explicit rclcomm(QObject *parent = nullptr);
    // rclcomm();

protected:
    void run();

public slots:

    void onTimer();
    // void onSendActionMsg(QString msgs);
    void onSendRobotCmd(int type, double fSpeed);
    void onSendUdpConnectCmd(QString IpAddress, int LocalPort, int RemotePort);   
    void onSendSubpubCmd(int type, QString fStepOrArgument);
    void onsendTakephotoCmd();
    // void onSendRobotTargetVel(follow_msgs::msg::TargetVelInfo info); // 新加
    // void onRecvRobotTargetVel(follow_msgs::msg::TargetVelInfo info); // 新加
    void pubGetStragyParams();
    
private:
    std::shared_ptr<FollowCfgClient> followCfgClientNode_ = nullptr;
    // std::shared_ptr<MaisignalSendRobotCmdnWindow> MainWindow_ = nullptr; // 新增：去调用mainwindow.cpp里的函数

signals:
    // void signalLogCallback(QString);
    void signalRecvServiceMsg(QString);
    // void signalSendActionMsg(QString);
    void signalSendRobotCmd(int, double);
    void signalSendUdpConnectCmd(QString, int, int); // 新的UDP连接消息
    void signalSendSubpubCmd(int, QString);
    void signalSendTakephotoCmd();
    // void signalTargetVelInfo(follow_msgs::msg::TargetVelInfo info); // 新增

};
#endif // RCCOMM_H