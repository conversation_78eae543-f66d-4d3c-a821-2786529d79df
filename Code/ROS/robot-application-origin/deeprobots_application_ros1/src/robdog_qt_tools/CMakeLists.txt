cmake_minimum_required(VERSION 3.0.2)
project(robdog_qt_tools)

set(CMAKE_CXX_STANDARD 17)

# 查找 catkin 包
find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  geometry_msgs
  image_transport
  std_msgs
  homi_speech_interface
)
# 查找 Qt5 包
find_package(Qt5 REQUIRED COMPONENTS Core Widgets)
find_package(Qt5 REQUIRED COMPONENTS Widgets )
# 查找其他包
find_package(Boost 1.71.0 REQUIRED COMPONENTS thread)
find_package(jsoncpp REQUIRED)

# find_package(tinyxml2 REQUIRED)  # Assuming tinyxml2 is installed and required
find_package(Boost 1.71.0 REQUIRED COMPONENTS thread)

catkin_package()


set(CMAKE_INCLUDE_CURRENT_DIR ON)
file(GLOB PRO_FORM_DIR RELATIVE ${CMAKE_CURRENT_SOURCE_DIR} ui/*.ui)
file(GLOB PRO_RESOURCES_DIR RELATIVE ${CMAKE_CURRENT_SOURCE_DIR} resource/*.qrc)
file(GLOB_RECURSE PRO_INCLUDE_DIR RELATIVE ${CMAKE_CURRENT_SOURCE_DIR} FOLLOW_SYMLINKS include/*.hpp *.h )
file(GLOB_RECURSE PRO_SOURCES_DIR RELATIVE ${CMAKE_CURRENT_SOURCE_DIR} FOLLOW_SYMLINKS src/*.cpp)

# set(CMAKE_AUTOMOC ON)#设置自动生成moc文件，一定要设置
# set(CMAKE_AUTOUIC ON)

# # 自动处理 Qt 的 uic 和 moc 文件
# qt5_wrap_ui(UI_HEADERS
#   ui/mainwindow.ui
# )

# qt5_wrap_cpp(MOC_SOURCES
#   include/mainwindow.h
# )

include_directories(
  include   # 表示包中的include/目录也是路径的一部分
  ${catkin_INCLUDE_DIRS}
  ${Qt5Core_INCLUDE_DIRS}
  ${Qt5Gui_INCLUDE_DIRS}
  ${Qt5Widgets_INCLUDE_DIRS}
  ${Boost_INCLUDE_DIRS}
  ${jsoncpp_INCLUDE_DIRS}
  # ${tinyxml2_INCLUDE_DIRS}
)

qt5_wrap_ui(QT_UI_HPP ui/mainwindow.ui) # ${PRO_FORM_DIR})
qt5_wrap_cpp(QT_MOC_HPP ${PRO_INCLUDE_DIR})
qt5_add_resources(QT_RESOURCES_CPP ${PRO_RESOURCES_DIR})

add_executable(robdog_qt_tools_node
    ${PRO_SOURCES_DIR}
    ${QT_RESOURCES_CPP}
    ${PRO_INCLUDE_DIR}
    ${QT_MOC_HPP}
    ${PRO_FORM_DIR}

    ${QT_UI_HPP}
    src/clientnode.cpp
    src/main.cpp
    src/mainwindow.cpp
    src/rclcomm.cpp
    include/clientnode.h
    include/mainwindow.h
    include/rclcomm.h
)

# add_executable(
#   robdog_qt_tools_node
#   src/clientnode.cpp
#   src/main.cpp
#   src/mainwindow.cpp
#   src/rclcomm.cpp
#   ${UI_HEADERS}
#   ${MOC_SOURCES}
#   include/clientnode.h
#   include/mainwindow.h
#   include/rclcomm.h
# )
add_dependencies(
  robdog_qt_tools_node 
  ${${PROJECT_NAME}_EXPORTED_TARGETS}
  ${catkin_EXPORTED_TARGETS}
)

target_link_libraries(
  robdog_qt_tools_node 
  Qt5::Core 
  Qt5::Widgets
  ${catkin_LIBRARIES}
  ${Qt5Core_LIBRARIES}
  ${Qt5Gui_LIBRARIES}
  ${Qt5Widgets_LIBRARIES}
  jsoncpp
  # tinyxml2
  asound
)

install(
  TARGETS robdog_qt_tools_node
  RUNTIME DESTINATION 
  ${CATKIN_PACKAGE_BIN_DESTINATION}
)



# cmake_minimum_required(VERSION 3.0.2)
# project(robdog_qt_tools)
# set(CMAKE_CXX_STANDARD 17)
# find_package(catkin REQUIRED COMPONENTS
#   roscpp
#   rospy
#   geometry_msgs
#   image_transport
#   std_msgs
#   homi_speech_interface 
# )

# catkin_package()

# ############ robdog_subpub_node ############
# include_directories(
#   include
#   ${catkin_INCLUDE_DIRS}
# )
# add_executable(
#   robdog_qt_tools_node
#   src/clientnode.cpp
#   src/main.cpp
#   src/mainwindow.cpp
#   src/rclcomm.cpp
#   include/clientnode.h
#   include/mainwindow.h
#   include/rclcomm.h
# )

# target_link_libraries(
#   robdog_qt_tools_node 
#   ${catkin_LIBRARIES}
#   jsoncpp
#   tinyxml2
#   asound
# )

# add_dependencies(
#   robdog_qt_tools_node 
#   ${${PROJECT_NAME}_EXPORTED_TARGETS}
#   ${catkin_EXPORTED_TARGETS}
# )

# install(
#   TARGETS robdog_qt_tools_node
#   RUNTIME DESTINATION 
#   ${CATKIN_PACKAGE_BIN_DESTINATION}
# )



# catkin_package(
#   CATKIN_DEPENDS roscpp std_msgs geometry_msgs homi_speech_interface
#   INCLUDE_DIRS include
#   LIBRARIES ${PROJECT_NAME}
# )

## System dependencies are found with CMake's conventions
# find_package(Boost REQUIRED COMPONENTS system)


## Uncomment this if the package has a setup.py. This macro ensures
## modules and global scripts declared therein get installed
## See http://ros.org/doc/api/catkin/html/user_guide/setup_dot_py.html
# catkin_python_setup()

################################################
## Declare ROS messages, services and actions ##
################################################

## To declare and build messages, services or actions from within this
## package, follow these steps:
## * Let MSG_DEP_SET be the set of packages whose message types you use in
##   your messages/services/actions (e.g. std_msgs, actionlib_msgs, ...).
## * In the file package.xml:
##   * add a build_depend tag for "message_generation"
##   * add a build_depend and a exec_depend tag for each package in MSG_DEP_SET
##   * If MSG_DEP_SET isn't empty the following dependency has been pulled in
##     but can be declared for certainty nonetheless:
##     * add a exec_depend tag for "message_runtime"
## * In this file (CMakeLists.txt):
##   * add "message_generation" and every package in MSG_DEP_SET to
##     find_package(catkin REQUIRED COMPONENTS ...)
##   * add "message_runtime" and every package in MSG_DEP_SET to
##     catkin_package(CATKIN_DEPENDS ...)
##   * uncomment the add_*_files sections below as needed
##     and list every .msg/.srv/.action file to be processed
##   * uncomment the generate_messages entry below
##   * add every package in MSG_DEP_SET to generate_messages(DEPENDENCIES ...)

## Generate messages in the 'msg' folder
# add_message_files(
#   FILES
#   Message1.msg
#   Message2.msg
# )

## Generate services in the 'srv' folder
# add_service_files(
#   FILES
#   Service1.srv
#   Service2.srv
# )

## Generate actions in the 'action' folder
# add_action_files(
#   FILES
#   Action1.action
#   Action2.action
# )

## Generate added messages and services with any dependencies listed here
# generate_messages(
#   DEPENDENCIES
#   geometry_msgs#   nav_msgs#   sensor_msgs#   std_msgs
# )

################################################
## Declare ROS dynamic reconfigure parameters ##
################################################

## To declare and build dynamic reconfigure parameters within this
## package, follow these steps:
## * In the file package.xml:
##   * add a build_depend and a exec_depend tag for "dynamic_reconfigure"
## * In this file (CMakeLists.txt):
##   * add "dynamic_reconfigure" to
##     find_package(catkin REQUIRED COMPONENTS ...)
##   * uncomment the "generate_dynamic_reconfigure_options" section below
##     and list every .cfg file to be processed

## Generate dynamic reconfigure parameters in the 'cfg' folder
# generate_dynamic_reconfigure_options(
#   cfg/DynReconf1.cfg
#   cfg/DynReconf2.cfg
# )

###################################
## catkin specific configuration ##
###################################
## The catkin_package macro generates cmake config files for your package
## Declare things to be passed to dependent projects
## INCLUDE_DIRS: uncomment this if your package contains header files
## LIBRARIES: libraries you create in this project that dependent projects also need
## CATKIN_DEPENDS: catkin_packages dependent projects also need
## DEPENDS: system dependencies of this project that dependent projects also need
## catkin_package(
#  INCLUDE_DIRS include
#  LIBRARIES robdog_qt_tools
#  CATKIN_DEPENDS cv_bridge geometry_msgs image_transport nav_msgs roscpp rospy sensor_msgs std_msgs tf
#  DEPENDS system_lib
## )

###########
## Build ##
###########

## Specify additional locations of header files
## Your package locations should be listed before other locations

## Declare a C++ library
# add_library(${PROJECT_NAME}
#   src/${PROJECT_NAME}/robdog_qt_tools.cpp
# )

## Add cmake target dependencies of the library
## as an example, code may need to be generated before libraries
## either from message generation or dynamic reconfigure
# add_dependencies(${PROJECT_NAME} ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})

## Declare a C++ executable
## With catkin_make all packages are built within a single CMake context
## The recommended prefix ensures that target names across packages don't collide
# add_executable(${PROJECT_NAME}_node src/robdog_qt_tools_node.cpp)

## Rename C++ executable without prefix
## The above recommended prefix causes long target names, the following renames the
## target back to the shorter version for ease of user use
## e.g. "rosrun someones_pkg node" instead of "rosrun someones_pkg someones_pkg_node"
# set_target_properties(${PROJECT_NAME}_node PROPERTIES OUTPUT_NAME node PREFIX "")

## Add cmake target dependencies of the executable
## same as for the library above
# add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})

## Specify libraries to link a library or executable target against
# target_link_libraries(${PROJECT_NAME}_node
#   ${catkin_LIBRARIES}
# )

#############
## Install ##
#############

# all install targets should use catkin DESTINATION variables
# See http://ros.org/doc/api/catkin/html/adv_user_guide/variables.html

## Mark executable scripts (Python etc.) for installation
## in contrast to setup.py, you can choose the destination
# catkin_install_python(PROGRAMS
#   scripts/my_python_script
#   DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
# )

## Mark executables for installation
## See http://docs.ros.org/melodic/api/catkin/html/howto/format1/building_executables.html
# install(TARGETS ${PROJECT_NAME}_node
#   RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
# )

## Mark libraries for installation
## See http://docs.ros.org/melodic/api/catkin/html/howto/format1/building_libraries.html
# install(TARGETS ${PROJECT_NAME}
#   ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
#   LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
#   RUNTIME DESTINATION ${CATKIN_GLOBAL_BIN_DESTINATION}
# )

## Mark cpp header files for installation
# install(DIRECTORY include/${PROJECT_NAME}/
#   DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
#   FILES_MATCHING PATTERN "*.h"
#   PATTERN ".svn" EXCLUDE
# )

## Mark other files for installation (e.g. launch and bag files, etc.)
# install(FILES
#   # myfile1
#   # myfile2
#   DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
# )

#############
## Testing ##
#############

## Add gtest based cpp test target and link libraries
# catkin_add_gtest(${PROJECT_NAME}-test test/test_robdog_qt_tools.cpp)
# if(TARGET ${PROJECT_NAME}-test)
#   target_link_libraries(${PROJECT_NAME}-test ${PROJECT_NAME})
# endif()

## Add folders to be run by python nosetests
# catkin_add_nosetests(test)
