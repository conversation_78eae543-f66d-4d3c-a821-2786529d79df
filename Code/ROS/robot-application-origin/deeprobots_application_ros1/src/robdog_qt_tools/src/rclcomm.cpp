/*实现信号与槽*/

#include "rclcomm.h"
// #include "libWebSocket.h" // 原本是用于和跟随算法通信的
#include <QTimer>
#include <jsoncpp/json/json.h>
// #include <rclcpp/logging.hpp>
// #include "follow_msgs/msg/target_vel_info.hpp" // 在install文件夹下
// #include "homi_speech_interface/msg/SIGCEvent.hpp" 
#include <homi_speech_interface/SIGCEvent.h>
#include <homi_speech_interface/NewUdpConnect.h>
#include <geometry_msgs/Twist.h>
// using namespace WS;
rclcomm* rclcomm_ =  nullptr;

// 把数据更新到QT界面上
void serveMsgCallback(void* pHandle, const char* msg) {
    // RCLCPP_INFO(rclcpp::get_logger("follow_me_qt_tools"), "serveMsgCallback: %s", msg);
    // ROS_INFO("serveMsgCallback: %s", msg);
    //if (rclcomm_ != pHandle || rclcomm_ == nullptr){
    //    return;
    //}
    // 使用Qt的信号和槽机制，发出一个信号，将接收到的消息传递给其他对象
    emit rclcomm_->signalRecvServiceMsg(QString::fromStdString(msg));   
}

// rclcomm::rclcomm(){
//     rclcomm_ = this;
//     this->start();
//     QTimer::singleShot(30, this, &rclcomm::onTimer);
// }
rclcomm::rclcomm(QObject *parent) /*: QObject(parent)*/
{
    rclcomm_ = this;
    this->start();
    QTimer::singleShot(30, this, &rclcomm::onTimer);
}

void rclcomm::onTimer(){
    QTimer::singleShot(30, this, &rclcomm::onTimer);
}

void rclcomm::run(){   // int argc, char** argv
    // ros::init(argc, argv, "rclcomm_node");
    ros::NodeHandle nh;
    followCfgClientNode_ = std::make_shared<FollowCfgClient>(nh);
    // followCfgClientNode_ = std::make_shared<FollowCfgClient>();

    // 直接把action去掉
    followCfgClientNode_->setServeMsgCallback(serveMsgCallback, this);
    // connect(this, &rclcomm::signalSendActionMsg, this, &rclcomm::onSendActionMsg); 
    connect(this, &rclcomm::signalSendRobotCmd, this, &rclcomm::onSendRobotCmd);
    connect(this, &rclcomm::signalSendUdpConnectCmd, this, &rclcomm::onSendUdpConnectCmd);  // 新的UDP连接消息
    connect(this, &rclcomm::signalSendSubpubCmd, this, &rclcomm::onSendSubpubCmd); // 向subpub节点模拟平台侧发送的消息
    connect(this, &rclcomm::signalSendTakephotoCmd, this, &rclcomm::onsendTakephotoCmd); 

    // rclcpp::spin(followCfgClientNode_);
    // rclcpp::shutdown();
    ros::spin();
}

// void rclcomm::onSendActionMsg(QString msgs) {
//     if (!followCfgClientNode_.get()){
//         return;
//     }
//     followCfgClientNode_->send_goal_internal(msgs.toStdString().c_str());
// }

void rclcomm::onSendRobotCmd(int type, double fSpeed) {
    if (!followCfgClientNode_.get()){
        return;
    }
    geometry_msgs::Twist twist;
    twist.linear.x = 0.0;
    twist.linear.y = 0.0;
    twist.angular.z = 0.0;
    MOVE_TYPE_E enType = (MOVE_TYPE_E)type;
    switch (enType)
    {
    case EN_ADD_X:
        twist.linear.x = fSpeed;
        break;
    case EN_REDUCE_X:
        twist.linear.x = -fSpeed;
        break;
    case EN_ADD_Y:
        twist.linear.y = fSpeed;
        break;
    case EN_REDUCE_Y:
        twist.linear.y = -fSpeed;
        break;
    case EN_ADD_RZ:
        twist.angular.z = fSpeed;
        break;
    case EN_REDUCE_RZ:
        twist.angular.z = -fSpeed;
        break;
    case EN_STOP:
    default:
        break;
    }
    followCfgClientNode_->sendRobotCmd(twist);
}

void rclcomm::onSendUdpConnectCmd(QString IpAddress, int LocalPort, int RemotePort){
    homi_speech_interface::NewUdpConnect msg;
    msg.new_devip_str = IpAddress.toStdString();
    msg.new_remote_port = LocalPort;
    msg.new_local_port = RemotePort;
    followCfgClientNode_->sendUdpConnectCmd(msg);
}

void rclcomm::onSendSubpubCmd(int type, QString stepOrArgument) {
    if (!followCfgClientNode_.get()) {
        return;
    }

    MOVE_TYPE_E enType = static_cast<MOVE_TYPE_E>(type);
    homi_speech_interface::SIGCEvent plat_msg;
    Json::Value sigc_event;
    Json::Value bodyValue;
    sigc_event["deviceId"] = "1111111"; // 必须要有的字段
    std::string strStepOrArgument = stepOrArgument.toStdString();

    auto setActionTypeInt = [&](const std::string& event, int actionType) {
        sigc_event["event"] = event;
        bodyValue["actionType"] = actionType;
    };
    auto setActionTypeString = [&](const std::string& event, const std::string& actionType) {
        sigc_event["event"] = event;
        bodyValue["actionType"] = actionType;
    };
    // Helper function to parse JSON
    auto parseJson = [&](const std::string& input) {
        Json::CharReaderBuilder readerBuilder;
        Json::Value jsonData;
        std::istringstream s(input);
        std::string errs;
        if (!Json::parseFromStream(readerBuilder, s, &jsonData, &errs)) {
            ROS_INFO("Failed to parse JSON: [%s]", input.c_str());
        }
        return jsonData;
    };

    switch (enType) {
        case EN_ADD_X: case EN_REDUCE_X: {
            setActionTypeInt("robot_move", 2);
            bodyValue["direction"]["x"] = (enType == EN_ADD_X) ? std::stoi(strStepOrArgument) : -std::stoi(strStepOrArgument);
            break;
        }
        case EN_ADD_Y: case EN_REDUCE_Y: {
            setActionTypeInt("robot_move", 2);
            bodyValue["direction"]["y"] = (enType == EN_ADD_Y) ? std::stoi(strStepOrArgument) : -std::stoi(strStepOrArgument);
            break;
        }
        case EN_ADD_RZ: case EN_REDUCE_RZ: {
            setActionTypeInt("robot_move", 2);
            bodyValue["direction"]["yaw"] = (enType == EN_ADD_RZ) ? std::stoi(strStepOrArgument) : -std::stoi(strStepOrArgument);
            break;
        }
        case EN_CONTINUE: case EN_YDCONTINUE: case EN_CONSTOP: {
            setActionTypeInt((enType == EN_YDCONTINUE) ? "robot_view" : "robot_move", (enType == EN_CONTINUE) ? 1 : 0);
            bodyValue["direction"] = parseJson(strStepOrArgument);
            break;
        }
        case EN_WALK: case EN_RUN: case EN_TRACTION: case EN_STAIRCLI: case EN_CLIMBE: {
            setActionTypeString("robot_action", "sportMode");
            bodyValue["actionArguement"] = strStepOrArgument;
            break;
        }
        case EN_STAND: case EN_GETDOWN: case EN_TWISTBODY: case EN_TWISTBODY_EMR:
        case EN_TWISTJUMP: case EN_GREET: case EN_GREET_EMR: case EN_DANCE:
        case EN_YAOBAI: case EN_BENGDA: case EN_JUMPFORWARD: case EN_MAKEBOW:
        case EN_BACKFLIP: case EN_SITDOWN: case EN_FINGERHEART: case EN_TURNOVER: {
            setActionTypeString("robot_action", "motorSkill");
            bodyValue["actionArguement"] = strStepOrArgument;
            break;
        }
        case EN_PLANNINGMOVE: case EN_STOPPLANNINGMOVE: {
            setActionTypeString("move_points", (enType == EN_PLANNINGMOVE) ? "deliverExpress" : "cancelMovement");
            bodyValue["points"].append(parseJson(strStepOrArgument));
            break;
        }
        case EN_RELOCATION: {
            setActionTypeInt("navigation_notify", 0);
            break;
        }
        case EN_STARTUWBFOLLOW: case EN_STARTFOLLOW: case EN_ENDFOLLOW: {
            setActionTypeString("robot_action", "followMe");
            bodyValue["actionArguement"] = (enType == EN_STARTUWBFOLLOW) ? "on_uwb" : (enType == EN_ENDFOLLOW) ? "off" : "on";
            break;
        }
        case EN_GOZERO: {
            setActionTypeString("robot_action", "resetZero");
            break;
        }
        case EN_ESTOP: {
            setActionTypeString("robot_action", "emergencyStop");
            break;
        }
        case EN_OBSTACLESRL: case EN_FLATRL: case EN_EXITRL: {
            setActionTypeString("robot_action", "gaitControl");
            bodyValue["actionArguement"] = (enType == EN_OBSTACLESRL) ? "obstacleCross" : (enType == EN_FLATRL) ? "flatGround" : "exit";
            break;
        }
        default:
            break;
    }

    sigc_event["body"] = bodyValue;
    Json::StreamWriterBuilder writer;
    std::string jsonString = Json::writeString(writer, sigc_event);
    plat_msg.event = jsonString;
    followCfgClientNode_->sendSubpubCmd(plat_msg);
    ROS_INFO("Published ctrl msg: [%s]", jsonString.c_str());
}

// void rclcomm::onSendRobotTargetVel(follow_msgs::msg::TargetVelInfo info) {
//     // 消息被传输到了这里（没做任何处理，中介的作用）
//     followCfgClientNode_->pubRobotTargetVel(info); // 用的是followActionClientNode.cpp里面的函数
// }
void rclcomm::onsendTakephotoCmd() {
    std_msgs::String msg;
    msg.data = "take_photo";
    followCfgClientNode_->sendTakephotoCmd(msg);
    ROS_INFO("Published message: [%s]", msg.data.c_str());
}

void rclcomm::pubGetStragyParams() {
    followCfgClientNode_->pubGetStragyParams();
}
