#include "clientnode.h"
// #include "follow_msgs/TargetVelInfo.h"
#include <jsoncpp/json/json.h>
#include <ros/ros.h>
#include <geometry_msgs/Twist.h>
#include <homi_speech_interface/SIGCEvent.h>
#include <std_msgs/Bool.h>
 
FollowCfgClient::FollowCfgClient(ros::NodeHandle& nh) : nh_(nh){
// FollowCfgClient::FollowCfgClient() : nh_("~") {
    ROS_INFO("FollowCfgClient init");

    // client_ptr_ = nh_.serviceClient<follow_msgs::CfgAction>("follow_me/cfgAction");
    pubRobotTwist_ = nh_.advertise<geometry_msgs::Twist>("/catch_turtle/ctrl_instruct", 10);
    pubRobotUdpConnect_ = nh_.advertise<homi_speech_interface::NewUdpConnect>("/catch_turtle/ctrl_udpconnect", 10); // 把要建立的新的UDP连接发布到控制节点

    // pubTargetVel_ = nh_.advertise<follow_msgs::TargetVelInfo>("/follow_me/pub_TargetVel", 1);
    pubTargetStragyParam_ = nh_.advertise<std_msgs::Bool>("/follow_me/get_params", 1);
    pubPlatMsg = nh_.advertise<homi_speech_interface::SIGCEvent>("/homi_speech/sigc_event_topic", 1); // 模拟平台发布的消息
    // subTargetVel_ = nh_.subscribe("/follow_me/targetVel_info", 10, &FollowCfgClient::target_vel_info_callback, this);
    subRobotState_ = nh_.subscribe("/robdog_control/robdog_state", 10, &FollowCfgClient::robdog_state_info_callback, this); // 需要的：订阅机器人状态信息
    pubtakephoto_ = nh.advertise<std_msgs::String>("/take_photo", 10);

    // 从感知主机获得的信息
    subNavPosition_ = nh.subscribe("/navigation_position", 10, &FollowCfgClient::navPositionCallback, this);  // 从感知主机订阅者机器人实时位置    
    subNavStatus_ = nh.subscribe("/navigation_status", 10, &FollowCfgClient::navStatusCallback, this);      // 感知主机上报的地图(是否建图完毕)

    //  *********************************** 日志界面需要接收的数据 ********************************************8
    // 平台下发的数据
    velCmd_sub_ = nh.subscribe("/homi_speech/sigc_event_topic", 1, &FollowCfgClient::robctrlCallback, this); 
    // 狗子的状态
    deepCtrl_sub_ = nh.subscribe("/deep_udp_ctrl/status_report", 1,&FollowCfgClient::deepStatusCallback, this); 
    userDefinedSub_ = nh.subscribe("/deep_udp_ctrl/status_ctrl", 1,&FollowCfgClient::userDefinedCtrlCallback, this);      // 接收处理平台消息的robdog_plat节点的消息，实现自定义命令控制和直接控制
    // robotStatusPub_ = nh.subscribe("/robdog_control/robdog_state", 1,&FollowCfgClient::robdog_state_info_callback, this);
    // 狗子收到的指令信息
    velCmd_ = nh.subscribe("/catch_turtle/ctrl_instruct", 1,&FollowCfgClient::velCmdCallback, this); // 订阅速度命令话题
    actionCmd_ = nh.subscribe("/catch_turtle/action_type", 1, &FollowCfgClient::MoveSkillscallback, this); // 订阅特定运动消息
    continueMoveCmd_ = nh.subscribe("/catch_turtle/continue_move", 1, &FollowCfgClient::continueMovecallback, this); // 订阅持续运动消息
    // 智能播报
    brocast_sub=nh.subscribe("/homi_speech/speech_assistant_status_topic", 100,&FollowCfgClient::BrocastIfAbortCallBack, this);   // 语音助手下发的是否播报被打断指令 
}

void FollowCfgClient::setServeMsgCallback(ServeMsgCallback callback, void* pHandle) {
    servMsgCallback_ = callback;
    handle_ = pHandle;
}

FollowCfgClient::~FollowCfgClient(){

}

// ********************************* 日志更新到界面需要发送的内容 ************************************
void FollowCfgClient::robctrlCallback(const homi_speech_interface::SIGCEventPtr& msg) {
   
    Json::Value root;
    root["actionType"] = "sigcEventTopic";
    Json::Value value;  
    value["event"] = msg->event;
    root["params"] = value;        
    // ROS_INFO("Received msg form platform: %s", msg->event.c_str());

    Json::StreamWriterBuilder builder;
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    std::ostringstream out;
    writer->write(root, &out);
    std::string jsonStr = out.str();

    void* ptr = nullptr; 
    const char* c_str = jsonStr.c_str();
    ServeMsgCallback callback = servMsgCallback_;
    callback(ptr, c_str);
}

void FollowCfgClient::deepStatusCallback(const homi_speech_interface::ProprietySetPtr& msg) {
   
    Json::Value root;
    root["actionType"] = "ProprietySet_report";
    Json::Value value;  
    value["cmd"] = msg->cmd;
    value["value"] = msg->value;
    value["exvalue"] = msg->exvalue;
    value["exmsg"] = msg->exmsg;

    root["params"] = value;
    // ROS_INFO("Received msg form robdog_ctrl Node.cmd is :%d,value is:%d", msg->cmd,msg->value);

    Json::StreamWriterBuilder builder;
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    std::ostringstream out;
    writer->write(root, &out);
    std::string jsonStr = out.str();

    void* ptr = nullptr; 
    const char* c_str = jsonStr.c_str();
    ServeMsgCallback callback = servMsgCallback_;
    callback(ptr, c_str);
}

void FollowCfgClient::userDefinedCtrlCallback(const homi_speech_interface::ProprietySetPtr& msg) {
   
    Json::Value root;
    root["actionType"] = "ProprietySet_ctrl";
    Json::Value value;  
    value["cmd"] = msg->cmd;
    value["value"] = msg->value;
    value["exvalue"] = msg->exvalue;
    value["exmsg"] = msg->exmsg;

    root["params"] = value;        
    // ROS_INFO("Received msg form robdog_ctrl Node.cmd is :%d,value is:%d", msg->cmd,msg->value);

    Json::StreamWriterBuilder builder;
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    std::ostringstream out;
    writer->write(root, &out);
    std::string jsonStr = out.str();

    void* ptr = nullptr; 
    const char* c_str = jsonStr.c_str();
    ServeMsgCallback callback = servMsgCallback_;
    callback(ptr, c_str);
}

void FollowCfgClient::velCmdCallback(const geometry_msgs::TwistPtr& msg) {
   
    Json::Value root;
    root["actionType"] = "Twist";
    Json::Value value;  
    value["linearx"] = msg->linear.x;
    value["lineary"] = msg->linear.y;
    value["linearz"] = msg->linear.z;
    value["angularx"] = msg->angular.x;
    value["angulary"] = msg->angular.y;
    value["angularz"] = msg->angular.z;

    root["params"] = value;        
    // ROS_INFO("Published velocity: linear.x = %f, linear.y = %f, linear.z = %f, angular.x = %f, angular.y = %f, angular.z = %f", msg->linear.x, msg->linear.y, msg->linear.z, msg->angular.x, msg->angular.y, msg->angular.z);


    Json::StreamWriterBuilder builder;
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    std::ostringstream out;
    writer->write(root, &out);
    std::string jsonStr = out.str();

    void* ptr = nullptr; 
    const char* c_str = jsonStr.c_str();
    ServeMsgCallback callback = servMsgCallback_;
    callback(ptr, c_str);
}

void FollowCfgClient::MoveSkillscallback(const homi_speech_interface::RobdogActionPtr& msg) {
   
    Json::Value root;
    root["actionType"] = "RobdogAction";
    Json::Value value;  
    value["actiontype"] = msg->actiontype;
    value["actionargument"] = msg->actionargument;

    root["params"] = value;        
    ROS_INFO("Published ActionType: %s, ActionArgument: %s", msg->actiontype.c_str(), msg->actionargument.c_str());

    Json::StreamWriterBuilder builder;
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    std::ostringstream out;
    writer->write(root, &out);
    std::string jsonStr = out.str();

    void* ptr = nullptr; 
    const char* c_str = jsonStr.c_str();
    ServeMsgCallback callback = servMsgCallback_;
    callback(ptr, c_str);
}


void FollowCfgClient::continueMovecallback(const homi_speech_interface::ContinueMovePtr& msg) {
   
    Json::Value root;
    root["actionType"] = "ContinueMove";
    Json::Value value;  
    value["event"] = msg->event;
    value["x"] = msg->x;
    value["y"] = msg->y;
    value["z"] = msg->z;

    value["pitch"] = msg->pitch;
    value["yaw"] = msg->yaw;
    value["roll"] = msg->roll;

    root["params"] = value;        
    ROS_INFO("Recieve Remote Ctrl msg.event=%s,x=%d,y=%d,z=%d,yaw=%d,pitch=%d,roll=%d",msg->event.c_str(),msg->x,msg->y,msg->z,msg->yaw,msg->pitch,msg->roll);

    Json::StreamWriterBuilder builder;
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    std::ostringstream out;
    writer->write(root, &out);
    std::string jsonStr = out.str();

    void* ptr = nullptr; 
    const char* c_str = jsonStr.c_str();
    ServeMsgCallback callback = servMsgCallback_;
    callback(ptr, c_str);
}

      
void FollowCfgClient::BrocastIfAbortCallBack(const homi_speech_interface::AssistantEventPtr& recmsg) {
   
    Json::Value root;
    root["actionType"] = "AssistantEvent";
    Json::Value value;  
    value["status"] = recmsg->status;
    value["description"] = recmsg->description;
    value["msg"] = recmsg->msg;
    value["sectionId"] = recmsg->sectionId;

    root["params"] = value;        
    ROS_INFO("Published description: %s, msg: %s, sectionId: %s", recmsg->description.c_str(), recmsg->msg.c_str(), recmsg->sectionId.c_str());

    Json::StreamWriterBuilder builder;
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    std::ostringstream out;
    writer->write(root, &out);
    std::string jsonStr = out.str();

    void* ptr = nullptr; 
    const char* c_str = jsonStr.c_str();
    ServeMsgCallback callback = servMsgCallback_;
    callback(ptr, c_str);
}

// *************************************************************************************

void FollowCfgClient::sendRobotCmd(const geometry_msgs::Twist& twist) {
    ROS_INFO("sendRobotCmd x: %f, %f, %f", twist.linear.x, twist.linear.y, twist.angular.z);
    pubRobotTwist_.publish(twist);
}

void FollowCfgClient::sendUdpConnectCmd(const homi_speech_interface::NewUdpConnect& msg){
    ROS_INFO("UpdateUdpConnect x: %s, %d, %d", msg.new_devip_str.c_str(), msg.new_remote_port, msg.new_local_port);
    pubRobotUdpConnect_.publish(msg);
}
// void FollowCfgClient::send_goal_internal(const std::string& strMsgs) {
//     follow_msgs::CfgAction srv;
//     srv.request.clear();
//     srv.request = strMsgs;

//     if (client_ptr_.call(srv)) {
//         ROS_INFO("Goal accepted by server, waiting for result");
//     } else {
//         ROS_ERROR("Action server not available");
//     }
// }

// void FollowCfgClient::goal_response_callback(const follow_msgs::CfgAction::Response & response) {
//     if (!response.success) {
//         ROS_ERROR("Goal was rejected by server");
//     } else {
//         ROS_INFO("Goal accepted by server, waiting for result");
//     }
// }

// void FollowCfgClient::feedback_callback(const follow_msgs::CfgAction::FeedbackConstPtr & feedback) {
//     std::string strRevFeedbackMsg = feedback->feedback;
//     servMsgCallback_(handle_, strRevFeedbackMsg.c_str());
//     ROS_INFO("rec action msg : %s", strRevFeedbackMsg.c_str());
// }

// void FollowCfgClient::result_callback(const follow_msgs::CfgAction::ResultConstPtr & result) {
//     switch (result->result_code) {
//         case follow_msgs::CfgAction::ResultCode::SUCCEEDED:
//             break;
//         case follow_msgs::CfgAction::ResultCode::ABORTED:
//             ROS_ERROR("Goal was aborted");
//             return;
//         case follow_msgs::CfgAction::ResultCode::CANCELED:
//             ROS_ERROR("Goal was canceled");
//             return;
//         default:
//             ROS_ERROR("Unknown result code");
//             return;
//     }
//     std::string strRevResultMsg = result->result;
//     servMsgCallback_(handle_, strRevResultMsg.c_str());
//     ROS_INFO("recv result msg: %s", strRevResultMsg.c_str());
// }

void FollowCfgClient::pubGetStragyParams() {
    std_msgs::Bool bState;
    pubTargetStragyParam_.publish(bState);
}

// void FollowCfgClient::pubRobotTargetVel(const follow_msgs::TargetVelInfo& info) {
//     pubTargetVel_.publish(info);
// }

void FollowCfgClient::sendSubpubCmd(const homi_speech_interface::SIGCEvent& info) {
    pubPlatMsg.publish(info);
}

void FollowCfgClient::sendTakephotoCmd(const std_msgs::String& info) {
    pubtakephoto_.publish(info);
}

// ********************************* 感知主机关于导航上报的信息 *****************************************
void FollowCfgClient::navStatusCallback(const std_msgs::String::ConstPtr& msg) {
    ROS_INFO("Received msg form nav: %s", msg->data.c_str());
    std::string strMsg = msg->data;
    Json::Reader reader;
    Json::Value value;  
    reader.parse(strMsg, value);
    if (value.isNull()){
        ROS_INFO("json parse error");
        return;
    }
    Json::Value root;
    root["actionType"] = "update_nav_Status";

    int status = value["status"].asInt();
    Json::Value response; 
    Json::Value body; 
    body["status"] = status;
    // response["deviceId"] = robotSt.getDeviceId();
    // response["domain"]="DEVICE_INTERACTION";
    // response["event"] = "navigation_report";
    // response["eventId"] = "robdog_plat_" + to_string(getCurrentTimeStramp());
    // response["seq"] = 0; 
    response["body"] = body;
    printf("status:%d\n", status);
    if (status == 1){
        at_target_ = true;
        std::cout << "at_target_: " << at_target_ << std::endl;;
    } 
    root["params"] = response;
    // 下发给平台
    // Json::FastWriter writer;
    // std::string jsonString = writer.write(response);
    // ROS_INFO("cmd : %s", jsonString.c_str());
    // homi_speech_interface::SIGCData resMsg;
    // resMsg.request.data = jsonString;
    // ROS_INFO_STREAM("Send res to Server, res is "<<resMsg.request.data);
    // if (platform_client.call(resMsg)){
    //     ROS_INFO("Response from server: %d", resMsg.response.errorCode);
    // }
    // else{
    //     ROS_ERROR("Failed to call service Service_demo errorCode:%d",resMsg.response.errorCode);
    // }

    Json::StreamWriterBuilder builder;
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    std::ostringstream out;
    writer->write(root, &out);
    std::string jsonStr = out.str();

    void* ptr = nullptr; 
    const char* c_str = jsonStr.c_str();
    ServeMsgCallback callback = servMsgCallback_;
    callback(ptr, c_str);

}

void FollowCfgClient::navPositionCallback(const geometry_msgs::Pose::ConstPtr& pos) {
   
    Json::Value root;
    root["actionType"] = "update_nav_Position";
    Json::Value value;  
    value["x"] = pos->position.x;
    value["y"] = pos->position.y;

    tf::Quaternion quaternion;
    tf::quaternionMsgToTF(pos->orientation, quaternion);
    double roll, pitch, yaw;
    tf::Matrix3x3(quaternion).getRPY(roll, pitch, yaw);
    yaw = yaw * 180.0 / M_PI;
    value["angle"] = yaw;
    root["params"] = value;
    ROS_INFO("Received robot position %s",value.toStyledString().c_str());

    Json::StreamWriterBuilder builder;
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    std::ostringstream out;
    writer->write(root, &out);
    std::string jsonStr = out.str();

    void* ptr = nullptr; 
    const char* c_str = jsonStr.c_str();
    ServeMsgCallback callback = servMsgCallback_;
    callback(ptr, c_str);
}

// ********************************* 跟随算法的期望速度信息（针对ROS2的） *****************************************
// void FollowCfgClient::target_vel_info_callback(const follow_msgs::TargetVelInfo::ConstPtr& info) {
//     ROS_INFO("Received target max linear vel: '%f'", info->max_linear_vel);

//     Json::Value root;
//     root["actionType"] = "update_target_vel";
//     Json::Value params;
//     params["max_linear_vel"] = info->max_linear_vel;
//     params["max_angular_vel"] = info->max_angular_vel;
//     params["target_linear_vel"] = info->target_linear_vel;
//     params["target_angular_vel"] = info->target_angular_vel;
//     params["goal_dist_tol"] = info->goal_dist_tol;
//     root["params"] = params;

//     Json::StreamWriterBuilder builder;
//     std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
//     std::ostringstream out;
//     writer->write(root, &out);
//     std::string jsonStr = out.str();

//     void* ptr = nullptr; 
//     const char* c_str = jsonStr.c_str();
//     ServeMsgCallback callback = servMsgCallback_;
//     callback(ptr, c_str);
// }

// ********************************* 把机器人状态信息传输到界面 *****************************************
void FollowCfgClient::robdog_state_info_callback(const homi_speech_interface::RobdogState::ConstPtr& info) {
    ROS_INFO("Received robot state: '%s'", info->robot_state_details.c_str());

    Json::Value root;
    root["actionType"] = "update_robot_state";
    Json::Value params;
    params["robot_state_details"] = info->robot_state_details;
    // params["max_angular_vel"] = info->robot_vel;
    // params["target_linear_vel"] = info->robot_step_state;
    root["params"] = params;

    Json::StreamWriterBuilder builder;
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    std::ostringstream out;
    writer->write(root, &out);
    std::string jsonStr = out.str();

    void* ptr = nullptr; 
    const char* c_str = jsonStr.c_str();
    ServeMsgCallback callback = servMsgCallback_;
    callback(ptr, c_str);
}