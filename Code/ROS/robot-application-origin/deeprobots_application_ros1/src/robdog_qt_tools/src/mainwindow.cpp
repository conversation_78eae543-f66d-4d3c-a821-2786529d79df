// 设置用户界面（这个函数只做界面交互）
#include "mainwindow.h"
#include "./ui_mainwindow.h"
// #include "rclcomm.h" // 新增
// #include "libWebSocket.h"

#include <QDebug>
#include <QButtonGroup>
#include <QTimer>
#include <jsoncpp/json/json.h>    
// using namespace WS; // 和websocket有关的
// #include "follow_msgs/msg/target_vel_info.hpp" // ck：创建消息实例需要用到 

// void notifyWsMsgCallback(void *handle, const char *msg, int index){
//     MainWindow* pThis = (MainWindow*)(handle);
//     if (!pThis){
//         return;
//     }
//     emit pThis->signalParseWsMsgCallback(QString::fromStdString(msg));
// }

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::mainWindow) {

    ui->setupUi(this);
    m_pCommonNode = std::make_shared<rclcomm>(); // 指针的初始化
    // 把信号（rclcomm定义）与槽（mainwindow定义）相匹配
    // connect(m_pCommonNode.get(),  &rclcomm::signalLogCallback, this, &MainWindow::onLogCallback);
    connect(m_pCommonNode.get(),  &rclcomm::signalRecvServiceMsg, this, &MainWindow::onRecvServiceMsg); // 可以去接收函数（其他节点）里面发来的数据

    // 是否上线设备
    // QButtonGroup* group1 = new QButtonGroup(this);
    // group1->addButton(ui->m_pdevup);
    // group1->addButton(ui->m_pdevdown);

    // 是否开启跟随
    QButtonGroup* group2 = new QButtonGroup(this);
    group2->addButton(ui->m_pstartfollow);
    group2->addButton(ui->m_pstopfollow);

    // 是否开启避障
    QButtonGroup* group3 = new QButtonGroup(this);
    group2->addButton(ui->m_pstartbz);
    group2->addButton(ui->m_pstopbz);

    // group1->addButton(ui->m_pRadioStatic);
    // group1->addButton(ui->m_pRadioNoStatic);
    // group2->addButton(ui->m_pRadioConnectCtrl);
    // group2->addButton(ui->m_pRadioDisConnectCtrl);
    
    //g_windowPtr_ = this;
    // ui->m_pBtnFollowEnable->setCheckable(true); 
    // ui->m_pBtnFollowEnable->setChecked(false);
    // ui->m_pEdtWsAddress->setText("ws://127.0.0.1:19002"); 

    ui->m_pEdtSpeedX->setText("0.5");
    ui->m_pEdtSpeedY->setText("0.2");
    ui->m_pEdtSpeedRZ->setText("0.8");

    // 走的步数和转的次数
    ui->m_pEdtX_step->setText("0");
    ui->m_pEdtY_step->setText("0");
    ui->m_pEdtZ_step->setText("0");
    
    // 和websocket有关的
    // WS_Init(EN_WS_ClIENT, 19002);
    // //设置接受msg的回调函数
    // WS_SetMsgCallback(notifyWsMsgCallback, this);

    // connect(this, &MainWindow::signalParseWsMsgCallback, this, &MainWindow::onParseWsMsgCallback);

    // 点击连接按钮
    // connect(ui->m_pBtnConnectWebSocket, &QPushButton::clicked, [&]() {
    //     QString strIp = ui->m_pEdtWsAddress->text();
    //     WS_Connect(strIp.toStdString().c_str());
    //     ROS_INFO("%s connecting...", strIp.toStdString().c_str());
    //     // RCLCPP_INFO(rclcpp::get_logger("follow_me_qt_tools"), "%s connecting...", strIp.toStdString().c_str());
    // });

    // 点击开启跟随按钮
    // connect(ui->m_pBtnFollowEnable, &QPushButton::toggled, [&](bool checked) {
    //     if (checked) {
    //         // RCLCPP_INFO(rclcpp::get_logger("follow_me_qt_tools"), "Button is ON");
    //         ROS_INFO("Button is ON");
    //         ui->m_pBtnFollowEnable->setText(u8"关闭跟随");
    //         //send success msg
    //         sendWsMsg(EN_WS_SEND_LAUNCHER_START_FOLLOW);
    //     } else {
    //         // RCLCPP_INFO(rclcpp::get_logger("follow_me_qt_tools"), "Button is OFF");
    //         ROS_INFO("Button is OFF");
    //         ui->m_pBtnFollowEnable->setText(u8"开启跟随");
    //         //send success msg
    //         sendWsMsg(EN_WS_SEND_LAUNCHER_END_FOLLOW);
    //     }
    // });

    // 信号与槽的连接
    // connect(ui->m_pBtnUpdateLauncherParam, &QPushButton::clicked, this, &MainWindow::onBtnUpdateLauncherParam);

    // 需要的：按下更新按钮后再去开启上线功能或者跟随和避障功能
    // connect(ui->m_pBtnUpdateUpService, &QPushButton::clicked, this, &MainWindow::onBtnUpdateUpService);
    // connect(ui->m_pBtnUpdateFollowOrBz, &QPushButton::clicked, this, &MainWindow::onBtnUpdateFollowOrBz);

    // 切换页面 
    connect(ui->tabWidget, &QTabWidget::currentChanged, this, &MainWindow::onCurrentTabChange);
    ui->tabWidget->setCurrentIndex(0);

    // 按下速度增减的按钮
    connect(ui->m_pBtnAddX, &QPushButton::clicked, this, &MainWindow::onMoveClicked);
    connect(ui->m_pBtnReduceX, &QPushButton::clicked, this, &MainWindow::onMoveClicked);
    connect(ui->m_pBtnAddY, &QPushButton::clicked, this, &MainWindow::onMoveClicked);
    connect(ui->m_pBtnReduceY, &QPushButton::clicked, this, &MainWindow::onMoveClicked);
    connect(ui->m_pBtnAddRZ, &QPushButton::clicked, this, &MainWindow::onMoveClicked);
    connect(ui->m_pBtnReduceRZ, &QPushButton::clicked, this, &MainWindow::onMoveClicked);
    // 停止功能【逻辑是直接向机器人发送速度为0】
    connect(ui->m_pBtnStop, &QPushButton::clicked, this, &MainWindow::onMoveClicked);
    // 按下步数增减的按钮 
    connect(ui->m_pBtnAddX_step, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnReduceX_step, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnAddY_step, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnReduceY_step, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    // 转向
    connect(ui->m_pBtnAddRZ_step, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnReduceRZ_step, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);

    // 关于运动模式和运动技能
    connect(ui->m_pBtnContinue, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);     // 持续运动
    connect(ui->m_pBtnyuandiContinue, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);     // 原地模式
    connect(ui->m_pBtnContinueStop, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked); // 停止持续运动
    
    connect(ui->m_pBtnwalk, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnrun, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pEdttraction, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnstairClimbe, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnclimbe, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
 
    connect(ui->m_pBtnstandUp, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtngetDown, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtntwistBody, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtntwistJump, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtngreeting, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnjumpForward, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnmakeBow, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnbackflip, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnsitDown, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnfingerHeart, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnturnOver, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnPlanningMove, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);

    connect(ui->m_pBtndance, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnyaobai, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnbengda, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);

    // 拍照功能
    // m_pBtntakephoto onsendTakephotoCmd
    connect(ui->m_pBtntakephoto, &QPushButton::clicked, this, &MainWindow::onBtnTakephoto);

    connect(ui->m_pBtnStartUwbFollow, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnStartFollow, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnEndFollow, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);

    // 取消移动和重定位
    connect(ui->m_pBtStopPlan, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtReLocation, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);

    // 合大需求应急
    connect(ui->m_pBtnGreetEmergency, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnMotouEmergency, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);

    // 回零和急停
    connect(ui->m_pBtnGoZero, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnEStop, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    
    // 和强化学习有关的
    connect(ui->m_pBtnObstaclesRL, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnFlatRL, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);
    connect(ui->m_pBtnExitRL, &QPushButton::clicked, this, &MainWindow::onMoveStepClicked);

    // 更新UDP连接
    connect(ui->m_pBtnUpdateUDP, &QPushButton::clicked, this, &MainWindow::onUpdateUDPClicked);

    // ck: 更新按键与函数的连接 【把界面上的数据更新到代码里】
    // connect(ui->m_pBtnUpdateStaregyParam, &QPushButton::clicked, this, &MainWindow::onBtnUpdateFollowParam);
}

// void MainWindow::onLogCallback(QString data){
//     ui->m_pTextEditConsole->append(data);
// }

// websocket 连接
// void MainWindow::onParseWsMsgCallback(QString strMsg){
//     ui->m_pTextEditConsole->append(strMsg);
//     Json::Reader reader;
//     Json::Value value;  
//     reader.parse(strMsg.toStdString().c_str(), value);
//     if (value.isNull()){
//         return;
//     }
//     if (!value["type"].isNull()){
//         string strType = value["type"].asString();
//         if ("connect_falied" == strType){
//             string strContents = value["content"].asString();
//             ui->m_EdtConnectTips->setText(QString::fromLocal8Bit(strContents.c_str()));
//             // RCLCPP_INFO(rclcpp::get_logger("follow_me_qt_tools"), "connect_falied");
//             ROS_INFO("recv msg: %s", strMsg.toStdString().c_str());
//             ui->m_EdtConnectTips->setStyleSheet("QLabel { color: red; }");
//         }
//         else if ("connect_success" == strType){
//             ui->m_EdtConnectTips->setText(u8"连接成功");
//             // RCLCPP_INFO(rclcpp::get_logger("follow_me_qt_tools"), "connect_success");
//             ROS_INFO("send msg: %s", value.toStyledString().c_str());
//             ui->m_EdtConnectTips->setStyleSheet("QLabel { color: blue; }");
//             // send success msg
//             sendWsMsg(EN_WS_SEND_LAUNCHER_CON_SUCEESS);
//         }
//     }
//     // RCLCPP_INFO(rclcpp::get_logger("follow_me_qt_tools"), "recv msg: %s", strMsg.toStdString().c_str());
//     ROS_INFO("recv msg: %s", strMsg.toStdString().c_str());
// }

// void MainWindow::sendWsMsg(WS_SEN_MSG_TYPE_E enType){
//     Json::Value value;
//     Json::Value params;
//     switch (enType)
//     {
//     case EN_WS_SEND_LAUNCHER_CON_SUCEESS:
//         value["client_type"] = "launcher";
//         value["action"] = "success";
//         break;
//     case EN_WS_SEND_LAUNCHER_START_FOLLOW:
//         value["client_type"] = "launcher";
//         value["action"] = "startFollow";
//         break;
//     case EN_WS_SEND_LAUNCHER_END_FOLLOW:
//         value["client_type"] = "launcher";
//         value["action"] = "endFollow";
//         break;
//     default:
//         break;
//     }
//     WS_Send(value.toStyledString().c_str());
//     // RCLCPP_INFO(rclcpp::get_logger("follow_me_qt_tools"), "send msg: %s", value.toStyledString().c_str());
//     ROS_INFO("send msg: %s", value.toStyledString().c_str());
// }

// 发送一个消息出去
// void MainWindow::sendActionMsg(const string& msgs){
//     emit m_pCommonNode->signalSendActionMsg(QString::fromStdString(msgs));
// }

// 切换到不同的子页面的按键的时候，也同时实现不同功能
void MainWindow::onCurrentTabChange(int index) {
    // RCLCPP_INFO(rclcpp::get_logger("follow_me_qt_tools"), "change tab: %d", index);
    ROS_INFO("change tab: %d", index);
    QWidget* pCurWgt = ui->tabWidget->currentWidget();
    // if (ui->m_tabWgtRcsSetting == pCurWgt){
    //     Json::Value value;
    //     value["actionType"] = "rcs_param_req";
    //     sendActionMsg(value.toStyledString().c_str());
    if (ui->m_tabWgtNodeTest == pCurWgt){

    }
    // 这个页面删除了
    // else if (ui->m_tabWgtStrategySetting == pCurWgt){
    //     //Json::Value value;
    //     //value["actionType"] = "strategy_param_req";
    //     //sendActionMsg(value.toStyledString().c_str());
    //     if (m_pCommonNode) {
    //         m_pCommonNode->pubGetStragyParams();
    //         // RCLCPP_INFO(rclcpp::get_logger("pubGetStragyParams"), "pubGetStragyParams");
    //         ROS_INFO("pubGetStragyParams");
    //     }
        
    // } 
    else if (ui->m_tabRosInfo == pCurWgt){
        
    } 
}

// 把参数更新到界面
void MainWindow::onRecvServiceMsg(QString msg) {
    // RCLCPP_INFO(rclcpp::get_logger("follow_me_qt_tools"), "onRecvServiceMsg: %s", msg.toStdString().c_str());
    // ROS_INFO("onRecvServiceMsg: %s", msg.toStdString().c_str());
    Json::Reader reader;
    Json::Value value;
    if(!reader.parse(msg.toStdString(), value)){
        cout << "error" << endl;
        return; 
    }
    string strActionType = value["actionType"].asString();
    if(strActionType == "rcs_param_resp") {
        // 这个页面未保留
        // Json::Value params = value["params"];
        // if (params.isNull()) {
        //     return;
        // }
        // bool b_static_state_follow = params["static_state_follow"].asBool();
        // ui->m_pRadioStatic->setChecked(b_static_state_follow);
        // ui->m_pRadioNoStatic->setChecked(!b_static_state_follow);

        // bool b_ctrl_temi_connected = params["ctrl_temi_connected"].asBool();
        // ui->m_pRadioConnectCtrl->setChecked(b_ctrl_temi_connected);
        // ui->m_pRadioDisConnectCtrl->setChecked(!b_ctrl_temi_connected);

        // float f_max_tracking_distanse = params["max_tracking_distanse"].asFloat();
        // float f_min_tracking_distanse = params["min_tracking_distanse"].asFloat();

        // ui->m_pEditMaxTrackingDistanse->setText(QString::number(f_max_tracking_distanse, 'f', 3)); 
        // ui->m_pEditMinTrackingDistanse->setText(QString::number(f_min_tracking_distanse, 'f', 3)); 

    } 
    else if (strActionType == "strategy_param_resp") {

    }
    else if(strActionType == "update_rcs_param") {

    } 
    else if (strActionType == "update_strategy_param") {

    }
    // 把跟随参数更新到界面上
    // else if (strActionType == "update_target_vel") {        
    //     Json::Value params = value["params"];
    //     if (params.isNull()) {
    //         return;
    //     }
    //     float f_max_linear_vel = params["max_linear_vel"].asFloat();
    //     float f_max_angular_vel = params["max_angular_vel"].asFloat();
    //     float f_target_linear_vel = params["target_linear_vel"].asFloat();
    //     float f_target_angular_vel = params["target_angular_vel"].asFloat();
    //     float f_goal_dist_tol = params["goal_dist_tol"].asFloat();

    //     ui->m_pEdtMaxLinearVel->setText(QString::number(f_max_linear_vel, 'f', 3));
    //     ui->m_pEdtMaxAngularVel->setText(QString::number(f_max_angular_vel, 'f', 3));
    //     ui->m_pEdtTarLinearVel->setText(QString::number(f_target_linear_vel, 'f', 3));
    //     ui->m_pEdtTarAngularVel->setText(QString::number(f_target_angular_vel, 'f', 3));
    //     ui->m_pEdtTarDist->setText(QString::number(f_goal_dist_tol, 'f', 5));
    // }
    // 把机器人状态信息更新到界面上【待修改】
    else if (strActionType == "update_robot_state") {        
        Json::Value params = value["params"];
        if (params.isNull()) {
            return;
        }
        
        QString f_robot_state_details = QString::fromStdString(params.get("robot_state_details", "").asString());
        ui->m_pEditrobotStateDetails->setText(f_robot_state_details);
        // QString f_robot_state = QString::fromStdString(params.get("robot_state", "").asString());
        // QString f_robot_vel = QString::fromStdString(params.get("robot_vel", "").asString());
        // QString f_robot_step_state = QString::fromStdString(params.get("robot_step_state", "").asString());
        // ui->m_pEditrobotstate->setText(f_robot_state);
        // ui->m_pEditrobotvelstate->setText(f_robot_vel);
        // ui->m_pEditrobotstepstate->setText(f_robot_step_state);
    }
    
    else if (strActionType == "update_nav_Status") { // 感知主机实时上报地图（放到链接里）
        Json::Value params = value["params"];
        if (params.isNull()) {
            return;
        }
        Json::Value body = params["body"]; 
        // QString f_nav_state = QString::fromStdString(body.get("status", "").asString());
        if(body["status"].asInt() == 1)
            ui->m_pEdtIfGetMap->setText("是");
        else
            ui->m_pEdtIfGetMap->setText("否");
    }
    else if (strActionType == "update_nav_Position") { // 感知主机上报的点位信息
        Json::Value params = value["params"];
        if (params.isNull()) {
            return;
        }
        QString f_x = QString::fromStdString(params.get("x", "").asString());
        QString f_y = QString::fromStdString(params.get("y", "").asString());
        QString f_angle = QString::fromStdString(params.get("angle", "").asString());
        ui->m_pEdtNowPlanningPos->setText(f_x + "," + f_y + "," + f_angle);
    }
    // ************************ 日志系统 *************************
    else if (strActionType == "sigcEventTopic") { // 平台下发的信息
        Json::Value params = value["params"];
        if (params.isNull()) {
            return;
        }
        QString f_event = QString::fromStdString(params.get("event", "").asString());
        f_event.remove('\n');
        f_event.remove('\r');
        f_event.remove('\t');

        ui->m_pEdtSigcEventTopic->setText("Platmsg：" + f_event);
    }
    else if (strActionType == "ProprietySet_report") { // 写狗子的状态
        Json::Value params = value["params"];
        if (params.isNull()) {
            return;
        }
        QString f_cmd = QString::fromStdString(params.get("cmd", "").asString());
        QString f_value = QString::fromStdString(params.get("value", "").asString());
        ui->m_pEdtStatusReport->setText(f_cmd + "," + f_value);
    }
    else if (strActionType == "ProprietySet_ctrl") { // 发布状态信息
        Json::Value params = value["params"];
        if (params.isNull()) {
            return;
        }
        QString f_cmd = QString::fromStdString(params.get("cmd", "").asString());
        QString f_value = QString::fromStdString(params.get("value", "").asString());
        ui->m_pEdtStatusCtrl->setText(f_cmd + "," + f_value);
    }
    else if (strActionType == "Twist") { // 向狗子发布的速度命令话题
        Json::Value params = value["params"];
        if (params.isNull()) {
            return;
        }
        QString f_linearx = QString::fromStdString(params.get("linearx", "").asString());
        QString f_lineary = QString::fromStdString(params.get("lineary", "").asString());
        QString f_linearz = QString::fromStdString(params.get("linearz", "").asString());

        QString f_angularx = QString::fromStdString(params.get("angularx", "").asString());
        QString f_angulary = QString::fromStdString(params.get("angulary", "").asString());
        QString f_angularz = QString::fromStdString(params.get("angularz", "").asString());

        ui->m_pEdtCtrlInstruct->setText(f_linearx + "," + f_lineary + "," + f_linearz + "," + f_angularx + "," + f_angulary + "," + f_angularz);
    }
    else if (strActionType == "RobdogAction") { // 向狗子发送特定运动指令消息
        Json::Value params = value["params"];
        if (params.isNull()) {
            return;
        }
        QString f_actiontype = QString::fromStdString(params.get("actiontype", "").asString());
        QString f_actionargument = QString::fromStdString(params.get("actionargument", "").asString());
        ui->m_pEdtSActionType->setText(f_actiontype + "," + f_actionargument);
    }
    else if (strActionType == "ContinueMove") { // 向狗子发送持续运动信息
        Json::Value params = value["params"];
        if (params.isNull()) {
            return;
        }
        QString f_event = QString::fromStdString(params.get("event", "").asString());
        QString f_x = QString::fromStdString(params.get("x", "").asString());
        QString f_y = QString::fromStdString(params.get("y", "").asString());
        QString f_z = QString::fromStdString(params.get("z", "").asString());
        QString f_pitch = QString::fromStdString(params.get("pitch", "").asString());
        QString f_yaw = QString::fromStdString(params.get("yaw", "").asString());
        QString f_roll = QString::fromStdString(params.get("roll", "").asString());

        ui->m_pEdtContinueMove->setText(f_event + "," + f_x + "," + f_y + "," + f_z + "," + f_pitch + "," + f_yaw + "," + f_roll);
    }
    else if (strActionType == "AssistantEvent") { // 语音助手下发的是否播报被打断指令
        Json::Value params = value["params"];
        if (params.isNull()) {
            return;
        }
        QString f_status = QString::fromStdString(params.get("status", "").asString());
        QString f_description = QString::fromStdString(params.get("description", "").asString());
        QString f_msg = QString::fromStdString(params.get("msg", "").asString());
        QString f_sectionId = QString::fromStdString(params.get("sectionId", "").asString());

        ui->m_pEdtSpeechAssistantStatusTopic->setText(f_status + "," + f_description + "," + f_msg + "," + f_sectionId);
    }
}

// 可以设定参数【此界面已删除】
// void MainWindow::onBtnUpdateLauncherParam() {
//     Json::Value value;
//     value["actionType"] = "update_rcs_param";
//     Json::Value params;
//     params["static_state_follow"] = ui->m_pRadioStatic->isChecked();
//     params["ctrl_temi_connected"] = ui->m_pRadioConnectCtrl->isChecked();
//     params["max_tracking_distanse"] = ui->m_pEditMaxTrackingDistanse->text().toFloat();
//     params["min_tracking_distanse"] = ui->m_pEditMinTrackingDistanse->text().toFloat();
//     value["params"] = params;
//     sendActionMsg(value.toStyledString().c_str());
// }

// 需要的：开启跟随和避障的按钮
// void MainWindow::onBtnUpdateFollowOrBz() {
//     // 问题：跟随和避障是一起关闭的
//     QString strrStepOrArgument = "";
//     // 是否开启跟随
//     if(ui->m_pstartfollow->isChecked()){
//         m_type = EN_FOLLOW;
//         strrStepOrArgument = "on";
//     }else if(ui->m_pstopfollow->isChecked()){
//         m_type = EN_STOPFOLLOW;
//         strrStepOrArgument = "off";
//     }
//     // 是否开启避障【问题：平台端目前还没有接口】
//     if(ui->m_pstartbz->isChecked()){
//         m_type = EN_BZ;
//         strrStepOrArgument = "on";
//     }else if(ui->m_pstartbz->isChecked()){
//         m_type = EN_STOPBZ;
//         strrStepOrArgument = "off";
//     }
    
//     // 还要在结构体加type
//     emit m_pCommonNode->signalSendSubpubCmd((int)m_type, strrStepOrArgument);
// }

// 按下速度修改按键
void MainWindow::onMoveClicked() {
    // ROS_INFO("");
    QPushButton *button = qobject_cast<QPushButton*>(sender());
    double fSpeed = 0.2;
    if (button == ui->m_pBtnAddX) {
        m_type = EN_ADD_X;
        fSpeed = ui->m_pEdtSpeedX->text().toFloat();
    }
    else if(button == ui->m_pBtnReduceX) {
        m_type = EN_REDUCE_X;
        fSpeed = ui->m_pEdtSpeedX->text().toFloat();
    }
    else if(button == ui->m_pBtnAddY) {
        m_type = EN_ADD_Y;
        fSpeed = ui->m_pEdtSpeedY->text().toFloat();
    }
    else if(button == ui->m_pBtnReduceY) {
        m_type = EN_REDUCE_Y;
        fSpeed = ui->m_pEdtSpeedY->text().toFloat();
    }
    else if(button == ui->m_pBtnAddRZ) {
        m_type = EN_ADD_RZ;
        fSpeed = ui->m_pEdtSpeedRZ->text().toFloat();
    }
    else if(button == ui->m_pBtnReduceRZ) {
        m_type = EN_REDUCE_RZ;
        fSpeed = ui->m_pEdtSpeedRZ->text().toFloat();
    }
    else if(button == ui->m_pBtnStop) {
        m_type = EN_STOP;
        fSpeed = ui->m_pEdtSpeedX->text().toFloat();
    }
    emit m_pCommonNode->signalSendRobotCmd((int)m_type, fSpeed);
}

// 更新和狗子的UDP连接
void MainWindow::onUpdateUDPClicked(){
    QString IpAddress = ui->m_pEdtUdpIpAddress->text();
    int LocalPort = ui->m_pEdtUdpLocalPort->text().toInt();
    int RemotePort = ui->m_pEdtUdpRemotePort->text().toInt();
    // RCLCPP_INFO(rclcpp::get_logger("follow_me_qt_tools"), "recv msg: %s", strMsg.toStdString().c_str());
    // ROS_INFO("recv msg: %s", strMsg.toStdString().c_str());
    emit m_pCommonNode->signalSendUdpConnectCmd(IpAddress, LocalPort, RemotePort);
}

// 创建 JSON 字符串的辅助函数
QString MainWindow::createJsonString(int x, int y, int z, int pitch, int roll, int yaw) {
    Json::Value jsonObject;
    jsonObject["x"] = x;
    jsonObject["y"] = y;
    jsonObject["z"] = z;
    jsonObject["pitch"] = pitch;
    jsonObject["roll"] = roll;
    jsonObject["yaw"] = yaw;

    Json::StreamWriterBuilder writer;
    return QString::fromStdString(Json::writeString(writer, jsonObject));
}

// 创建定点移动 JSON 字符串的辅助函数
QString MainWindow::createPlanningMoveJsonString() {
    Json::Value bodyValue;
    bodyValue["x"] = ui->m_pEdtPlanningMoveX->text().toDouble();
    bodyValue["y"] = ui->m_pEdtPlanningMoveY->text().toDouble();
    bodyValue["angle"] = ui->m_pEdtPlanningMoveT->text().toDouble();

    Json::StreamWriterBuilder writer;
    return QString::fromStdString(Json::writeString(writer, bodyValue));
}

// 运动控制（向subpub节点发送JSON）
void MainWindow::onMoveStepClicked() {
    QPushButton *button = qobject_cast<QPushButton*>(sender());
    QString strrStepOrArgument;
    
    // 定义按钮与类型的映射
    QMap<QPushButton*, std::pair<MOVE_TYPE_E, QString>> buttonMap = {
        {ui->m_pBtnAddX_step, {EN_ADD_X, ui->m_pEdtX_step->text()}},
        {ui->m_pBtnReduceX_step, {EN_REDUCE_X, ui->m_pEdtX_step->text()}},
        {ui->m_pBtnAddY_step, {EN_ADD_Y, ui->m_pEdtY_step->text()}},
        {ui->m_pBtnReduceY_step, {EN_REDUCE_Y, ui->m_pEdtY_step->text()}},
        {ui->m_pBtnAddRZ_step, {EN_ADD_RZ, ui->m_pEdtZ_step->text()}},
        {ui->m_pBtnReduceRZ_step, {EN_REDUCE_RZ, ui->m_pEdtZ_step->text()}},
        {ui->m_pBtnwalk, {EN_WALK, "walk"}},
        {ui->m_pBtnrun, {EN_RUN, "run"}},
        {ui->m_pEdttraction, {EN_TRACTION, "traction"}},
        {ui->m_pBtnstairClimbe, {EN_STAIRCLI, "stairClimbe"}},
        {ui->m_pBtnclimbe, {EN_CLIMBE, "climbe"}},
        {ui->m_pBtnstandUp, {EN_STAND, "standUp"}},
        {ui->m_pBtngetDown, {EN_GETDOWN, "getDown"}},
        {ui->m_pBtntwistBody, {EN_TWISTBODY, "twistBody"}},
        {ui->m_pBtnMotouEmergency, {EN_TWISTBODY_EMR, "twistBody_emergency"}},
        {ui->m_pBtntwistJump, {EN_TWISTJUMP, "twistJump"}},
        {ui->m_pBtngreeting, {EN_GREET, "greeting"}},
        {ui->m_pBtnGreetEmergency, {EN_GREET_EMR, "greeting_emergency"}},
        {ui->m_pBtnjumpForward, {EN_JUMPFORWARD, "jumpForward"}},
        {ui->m_pBtnmakeBow, {EN_MAKEBOW, "makeBow"}},
        {ui->m_pBtnbackflip, {EN_BACKFLIP, "backflip"}},
        {ui->m_pBtnsitDown, {EN_SITDOWN, "sitDown"}},
        {ui->m_pBtnfingerHeart, {EN_FINGERHEART, "fingerHeart"}},
        {ui->m_pBtnturnOver, {EN_TURNOVER, "turnOver"}},
        {ui->m_pBtndance, {EN_DANCE, "dance"}},
        {ui->m_pBtnyaobai, {EN_YAOBAI, "shakeBody"}},
        {ui->m_pBtnbengda, {EN_BENGDA, "twistAss"}},
        {ui->m_pBtnGoZero, {EN_GOZERO, "resetZero"}},
        {ui->m_pBtnEStop, {EN_ESTOP, "emergencyStop"}},
        {ui->m_pBtnContinue, {EN_CONTINUE, createJsonString(ui->m_pEdtx->text().toInt(), ui->m_pEdty->text().toInt(), ui->m_pEdtz->text().toInt(),ui->m_pEdtpitch->text().toInt(), ui->m_pEdtroll->text().toInt(),ui->m_pEdtyaw->text().toInt())}},
        {ui->m_pBtnyuandiContinue, {EN_YDCONTINUE, createJsonString(ui->m_pEdtx->text().toInt(), ui->m_pEdty->text().toInt(), ui->m_pEdtz->text().toInt(),ui->m_pEdtpitch->text().toInt(), ui->m_pEdtroll->text().toInt(),ui->m_pEdtyaw->text().toInt())}},        
        {ui->m_pBtnContinueStop, {EN_CONSTOP, createJsonString(0, 0, 0, 0, 0, 0)}},
        {ui->m_pBtnPlanningMove, {EN_PLANNINGMOVE, createPlanningMoveJsonString()}},     
        {ui->m_pBtnStartFollow, {EN_STARTFOLLOW, ""}},
        {ui->m_pBtnEndFollow, {EN_ENDFOLLOW, ""}},
        {ui->m_pBtnStartUwbFollow, {EN_STARTUWBFOLLOW, ""}},
        {ui->m_pBtStopPlan, {EN_STOPPLANNINGMOVE, ""}},
        {ui->m_pBtReLocation, {EN_RELOCATION, ""}},
        {ui->m_pBtnObstaclesRL, {EN_OBSTACLESRL, ""}},
        {ui->m_pBtnFlatRL, {EN_FLATRL, ""}},
        {ui->m_pBtnExitRL, {EN_EXITRL, ""}}
    };

    // 查找按钮对应的类型和参数
    auto it = buttonMap.find(button);
    if (it != buttonMap.end()) {
        m_type = it.value().first;
        strrStepOrArgument = it.value().second;
    }
    emit m_pCommonNode->signalSendSubpubCmd((int)m_type, strrStepOrArgument);
    // emit m_pCommonNode->signalSendSubpubCmd(static_cast<int>(m_type), strrStepOrArgument);
}

void MainWindow::onBtnTakephoto() {
    // ROS_INFO("click btn: take photo");
    emit m_pCommonNode->signalSendTakephotoCmd();
}

// ck：跟随参数设置：
// void MainWindow::onBtnUpdateFollowParam() {
//     // 创建消息实例
//     follow_msgs::msg::TargetVelInfo target_vel_msg;
//     target_vel_msg.max_linear_vel = ui->m_pEdtMaxLinearVel->text().toFloat();
//     target_vel_msg.max_angular_vel = ui->m_pEdtMaxAngularVel->text().toFloat();
//     target_vel_msg.target_linear_vel = ui->m_pEdtTarLinearVel->text().toFloat();
//     target_vel_msg.target_angular_vel = ui->m_pEdtTarAngularVel->text().toFloat();
//     target_vel_msg.goal_dist_tol = ui->m_pEdtTarDist->text().toFloat();

//     m_pCommonNode->onSendRobotTargetVel(target_vel_msg); // 传递到rclcomm函数（实现与机器的通信）
// }

// 析构
MainWindow::~MainWindow()
{
    delete ui;
}