<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>mainWindow</class>
 <widget class="QMainWindow" name="mainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1261</width>
    <height>671</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>机器狗交互控制平台</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout" stretch="0,1">
    <item>
     <widget class="QTabWidget" name="tabWidget">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>1200</width>
        <height>409</height>
       </size>
      </property>
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="m_tabWgtNodeTest">
       <attribute name="title">
        <string>机器狗控制器</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <widget class="Line" name="line_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_13">
          <property name="leftMargin">
           <number>5</number>
          </property>
          <property name="topMargin">
           <number>5</number>
          </property>
          <property name="rightMargin">
           <number>5</number>
          </property>
          <property name="bottomMargin">
           <number>5</number>
          </property>
          <item>
           <widget class="QLabel" name="label_37">
            <property name="text">
             <string>建立新的UDP连接：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_40">
            <property name="text">
             <string>新的IP地址</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtUdpIpAddress"/>
          </item>
          <item>
           <widget class="QLabel" name="label_39">
            <property name="text">
             <string>新的本地端口号</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtUdpLocalPort"/>
          </item>
          <item>
           <widget class="QLabel" name="label_41">
            <property name="text">
             <string>新的远程端口号</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtUdpRemotePort"/>
          </item>
          <item>
           <spacer name="horizontalSpacer_12">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnUpdateUDP">
            <property name="text">
             <string>更新</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_42">
            <property name="text">
             <string>注：点击更新前，三项都必填</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="Line" name="line_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="0,0,0,0,0,0,0,0,0,0,0,0,0,0">
          <property name="leftMargin">
           <number>10</number>
          </property>
          <property name="rightMargin">
           <number>10</number>
          </property>
          <item>
           <widget class="QLineEdit" name="m_pEdtSpeedX">
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>80</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="placeholderText">
             <string>0~1.0</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_17">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>50</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>m/s</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnAddX">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>X+</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnReduceX">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>X-</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtSpeedY">
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>80</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="placeholderText">
             <string>0~0.5</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_19">
            <property name="minimumSize">
             <size>
              <width>30</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>30</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>m/s</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnAddY">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>Y+</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnReduceY">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>Y-</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtSpeedRZ">
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>80</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="placeholderText">
             <string>0~1.5</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_18">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>50</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>/rad/s</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnAddRZ">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>RZ+</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnReduceRZ">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>RZ-</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_95">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnStop">
            <property name="text">
             <string>Stop</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="Line" name="line_7">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_14">
          <property name="leftMargin">
           <number>5</number>
          </property>
          <property name="topMargin">
           <number>5</number>
          </property>
          <property name="rightMargin">
           <number>5</number>
          </property>
          <property name="bottomMargin">
           <number>5</number>
          </property>
          <item>
           <widget class="QLabel" name="label_10">
            <property name="text">
             <string>定点移动任务：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_7">
            <property name="text">
             <string>x</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtPlanningMoveX">
            <property name="maximumSize">
             <size>
              <width>80</width>
              <height>16777215</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_8">
            <property name="text">
             <string>y</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtPlanningMoveY">
            <property name="maximumSize">
             <size>
              <width>80</width>
              <height>16777215</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_9">
            <property name="text">
             <string>theta</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtPlanningMoveT">
            <property name="maximumSize">
             <size>
              <width>80</width>
              <height>16777215</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnPlanningMove">
            <property name="text">
             <string>移动</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_7">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="label_14">
            <property name="text">
             <string>和感知主机的通信：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_15">
            <property name="text">
             <string>取消移动：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtStopPlan">
            <property name="text">
             <string>stopPlan</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_16">
            <property name="text">
             <string>下发重定位：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtReLocation">
            <property name="text">
             <string>reLocation</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_12">
            <property name="text">
             <string>是否收到建图信息：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtIfGetMap">
            <property name="maximumSize">
             <size>
              <width>50</width>
              <height>16777215</height>
             </size>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="Line" name="line_8">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_7">
          <item>
           <widget class="QLabel" name="label_27">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>运动模式：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnwalk">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>步行</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnrun">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>跑步</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pEdttraction">
            <property name="text">
             <string>牵引</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnstairClimbe">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>爬楼</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnclimbe">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>爬坡</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <widget class="Line" name="line_18">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <widget class="QLabel" name="label_20">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>向前/后走：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtX_step">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>80</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="placeholderText">
             <string>1~10</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_22">
            <property name="minimumSize">
             <size>
              <width>30</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>30</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>步</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnAddX_step">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>X+</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnReduceX_step">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>X-</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_24">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="layoutDirection">
             <enum>Qt::LeftToRight</enum>
            </property>
            <property name="text">
             <string>向左/右走：</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtY_step">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>80</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="placeholderText">
             <string>1~10</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_182">
            <property name="minimumSize">
             <size>
              <width>30</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>30</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>步</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnAddY_step">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>Y+</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnReduceY_step">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>Y-</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_25">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>80</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>向右/左转：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtZ_step">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>80</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="placeholderText">
             <string>1~∞</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_23">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>20</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>度</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnAddRZ_step">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>RZ+</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnReduceRZ_step">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>RZ-</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_10">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <item>
           <widget class="QLabel" name="label">
            <property name="text">
             <string>x</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtx">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>50</width>
              <height>16777215</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>y</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdty">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>50</width>
              <height>16777215</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_3">
            <property name="text">
             <string>z</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtz">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>50</width>
              <height>16777215</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_5">
            <property name="text">
             <string>pitch</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtpitch">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>50</width>
              <height>16777215</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_6">
            <property name="text">
             <string>roll</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtroll">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>50</width>
              <height>16777215</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_4">
            <property name="text">
             <string>yaw</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="m_pEdtyaw">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>50</width>
              <height>16777215</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnContinue">
            <property name="text">
             <string>移动</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnyuandiContinue">
            <property name="text">
             <string>原地</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnContinueStop">
            <property name="text">
             <string>停止移动</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_5">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="label_11">
            <property name="text">
             <string>注：点击后会持续执行，结束请按“停止移动”</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="Line" name="line_9">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_6">
          <item>
           <widget class="QLabel" name="label_26">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>运动技能：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnstandUp">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>起立</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtngetDown">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>趴下</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtntwistBody">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>扭身体</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtngreeting">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>打招呼</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnsitDown">
            <property name="text">
             <string>坐下</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtndance">
            <property name="text">
             <string>跳舞</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnyaobai">
            <property name="text">
             <string>摇一摇</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnbengda">
            <property name="text">
             <string>扭屁股</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_32">
            <property name="text">
             <string>已去掉：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtntwistJump">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>扭身跳</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnbackflip">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>后空翻</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnjumpForward">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>向前跳</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnfingerHeart">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>比心</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnmakeBow">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>作揖</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnturnOver">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>翻身</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_6">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QGridLayout" name="gridLayout_10">
          <item row="0" column="12">
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="0" column="1">
           <widget class="QPushButton" name="m_pBtnGoZero">
            <property name="text">
             <string>GO ZERO</string>
            </property>
           </widget>
          </item>
          <item row="0" column="10">
           <widget class="QPushButton" name="m_pBtnStartUwbFollow">
            <property name="text">
             <string>开启UWB跟随</string>
            </property>
           </widget>
          </item>
          <item row="0" column="11">
           <widget class="QPushButton" name="m_pBtnEndFollow">
            <property name="text">
             <string>结束跟随</string>
            </property>
           </widget>
          </item>
          <item row="0" column="7">
           <spacer name="horizontalSpacer_11">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="0" column="0">
           <widget class="QPushButton" name="m_pBtnEStop">
            <property name="text">
             <string>EStop</string>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QPushButton" name="m_pBtnObstaclesRL">
            <property name="text">
             <string>越障+RL</string>
            </property>
           </widget>
          </item>
          <item row="0" column="8">
           <widget class="QPushButton" name="m_pBtntakephoto">
            <property name="text">
             <string>拍照</string>
            </property>
           </widget>
          </item>
          <item row="0" column="5">
           <widget class="QPushButton" name="m_pBtnFlatRL">
            <property name="text">
             <string>平地+RL</string>
            </property>
           </widget>
          </item>
          <item row="0" column="6">
           <widget class="QPushButton" name="m_pBtnExitRL">
            <property name="text">
             <string>退出RL</string>
            </property>
           </widget>
          </item>
          <item row="0" column="9">
           <widget class="QPushButton" name="m_pBtnStartFollow">
            <property name="text">
             <string>开启跟随</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <spacer name="horizontalSpacer_9">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="0" column="3">
           <widget class="QLabel" name="label_21">
            <property name="text">
             <string>和强化学习有关：</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="m_tabRosInfo">
       <attribute name="title">
        <string>日志检查</string>
       </attribute>
       <layout class="QFormLayout" name="formLayout">
        <item row="0" column="0" colspan="2">
         <layout class="QHBoxLayout" name="horizontalLayout_81">
          <item>
           <widget class="QLabel" name="label_179">
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>200</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>是否上线设备</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="m_pdevdown">
            <property name="text">
             <string>是</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="m_pdevup">
            <property name="text">
             <string>否</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton">
            <property name="text">
             <string>更新</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="1" column="0">
         <widget class="QFrame" name="frame">
          <property name="frameShape">
           <enum>QFrame::StyledPanel</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="Line" name="line">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QLabel" name="label_13">
          <property name="enabled">
           <bool>true</bool>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>250</width>
            <height>20</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>100000</width>
            <height>100000</height>
           </size>
          </property>
          <property name="text">
           <string>平台下发的信息：</string>
          </property>
         </widget>
        </item>
        <item row="4" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout_9">
          <item>
           <widget class="QLabel" name="m_pEdtSigcEventTopic">
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="5" column="1">
         <widget class="Line" name="line_6">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item row="6" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout_10">
          <item>
           <widget class="QLabel" name="label_30">
            <property name="text">
             <string>狗子的状态信息：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="m_pEdtStatusReport">
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="7" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout_11">
          <item>
           <widget class="QLabel" name="label_31">
            <property name="text">
             <string>状态信息：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="m_pEdtStatusCtrl">
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="8" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout_12">
          <item>
           <widget class="QLabel" name="label_33">
            <property name="text">
             <string>状态信息：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="m_pEdtRobdogState">
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="9" column="1">
         <widget class="Line" name="line_10">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item row="11" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout_15">
          <item>
           <widget class="QLabel" name="label_34">
            <property name="text">
             <string>速度指令消息：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="m_pEdtCtrlInstruct">
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="14" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout_16">
          <item>
           <widget class="QLabel" name="label_35">
            <property name="text">
             <string>特定运动指令消息：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="m_pEdtSActionType">
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="16" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout_18">
          <item>
           <widget class="QLabel" name="label_36">
            <property name="text">
             <string>持续运动信息：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="m_pEdtContinueMove">
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="17" column="1">
         <widget class="Line" name="line_11">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item row="18" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout_21">
          <item>
           <widget class="QLabel" name="label_38">
            <property name="text">
             <string>智能播报是否被打断：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="m_pEdtSpeechAssistantStatusTopic">
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="m_followbz">
       <attribute name="title">
        <string>跟随避障模块</string>
       </attribute>
       <layout class="QFormLayout" name="formLayout_2">
        <item row="0" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_82">
          <item>
           <widget class="QLabel" name="label_181">
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>200</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>是否开启静态跟随</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="m_pstartfollow">
            <property name="text">
             <string>是</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="m_pstopfollow">
            <property name="text">
             <string>否</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="1" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_83">
          <item>
           <widget class="QLabel" name="label_180">
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>200</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>是否开启停障功能</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="m_pstartbz">
            <property name="text">
             <string>是</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="m_pstopbz">
            <property name="text">
             <string>否</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="2" column="0" colspan="2">
         <layout class="QGridLayout" name="gridLayout_8">
          <property name="bottomMargin">
           <number>30</number>
          </property>
          <item row="0" column="1">
           <widget class="QPushButton" name="pushButton_2">
            <property name="text">
             <string>更新</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>3</number>
      </property>
      <property name="topMargin">
       <number>3</number>
      </property>
      <property name="rightMargin">
       <number>3</number>
      </property>
      <property name="bottomMargin">
       <number>3</number>
      </property>
      <item>
       <layout class="QGridLayout" name="gridLayout">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item row="0" column="1">
         <widget class="QLabel" name="m_pEditrobotStateDetails">
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label_43">
          <property name="text">
           <string>机器人运动状态</string>
          </property>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="Line" name="line_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item row="5" column="0">
         <widget class="QLabel" name="label_29">
          <property name="text">
           <string>感知主机上报的点位信息：</string>
          </property>
         </widget>
        </item>
        <item row="5" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout_8">
          <item>
           <widget class="QLabel" name="m_pEdtNowPlanningPos">
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="6" column="0">
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="3" column="0">
         <widget class="QLabel" name="label_28">
          <property name="text">
           <string>合大演示紧急按钮（动作、语音、LED）</string>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <item>
           <widget class="QPushButton" name="m_pBtnGreetEmergency">
            <property name="text">
             <string>打招呼</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="m_pBtnMotouEmergency">
            <property name="text">
             <string>摸头</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_8">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item row="2" column="0">
         <widget class="Line" name="line_5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item row="4" column="1">
         <widget class="Line" name="line_12">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="Line" name="line_13">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout_5">
        <property name="leftMargin">
         <number>0</number>
        </property>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1261</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
