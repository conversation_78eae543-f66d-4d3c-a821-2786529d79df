import rospy
from std_msgs.msg import String

class ChatterSubscriber:

    def __init__(self):
        rospy.init_node('chatter_subscriber', anonymous=True)
        self.subscriber = rospy.Subscriber('chatter', String, self.listener_callback)

    def listener_callback(self, msg):
        rospy.loginfo('I heard: "%s"' % msg.data)

    def run(self):
        pass

if __name__ == '__main__':
    try:
        chatter_subscriber = ChatterSubscriber()
        rospy.spin()  # 保持节点运行，直到它被Ctrl+C或其他方式关闭
    except rospy.ROSInterruptException:
        pass