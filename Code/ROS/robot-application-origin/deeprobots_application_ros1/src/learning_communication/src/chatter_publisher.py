
import rospy
from std_msgs.msg import String

class ChatterPublisher:

    def __init__(self):
        rospy.init_node('chatter_publisher', anonymous=True)
        self.publisher = rospy.Publisher('chatter', String, queue_size=10)
        self.rate = rospy.Rate(1)  # 1hz

    def timer_callback(self, event):
        msg = String()
        msg.data = 'Hello, ROS 1 World! %s' % rospy.Time.now().secs
        self.publisher.publish(msg)

    def run(self):
        while not rospy.is_shutdown():
            self.timer_callback(None)  # 传递None作为事件参数，因为这里不需要
            self.rate.sleep()

if __name__ == '__main__':
    try:
        chatter_publisher = ChatterPublisher()
        chatter_publisher.run()
    except rospy.ROSInterruptException:
        pass