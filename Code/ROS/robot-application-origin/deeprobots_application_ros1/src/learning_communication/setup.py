from setuptools import find_packages, setup

package_name = 'learning_communication'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    # ROS 1不需要ament_index或resource_index，所以这部分被移除了
    # data_files部分也被简化，只包含必要的文件
    data_files=[
        ('share/' + package_name, ['package.xml']),  # 假设package.xml是ROS 1的包描述文件
    ],
    install_requires=['setuptools'],  # ROS 1通常不需要这个，但保留也无妨
    zip_safe=False,  # ROS 1包通常不应该被压缩
    maintainer='hb',
    maintainer_email='<EMAIL>',
    description='A ROS 1 package for learning communication',
    license='TODO: License declaration',
    # tests_require 和 entry_points 在ROS 1中通常不这样使用
    # ROS 1的测试和节点启动通常通过其他方式处理
    # 但如果你确实想通过这种方式提供入口点，可以保留并修改entry_points
    # 但请注意，ROS 1用户不会期望通过这种方式启动节点
    # entry_points={
    #     # 这里的入口点需要修改以符合ROS 1的习惯，但通常不推荐这样做
    # },
)
