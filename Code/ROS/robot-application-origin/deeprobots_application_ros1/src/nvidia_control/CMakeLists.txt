cmake_minimum_required(VERSION 3.0.2)
project(nvidia_control)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  geometry_msgs
)

# add_message_files(
#   FILES
#   Message1.msg
#   Message2.msg
# )

# add_service_files(
#   FILES
#   Service1.srv
#   Service2.srv
# )

# add_action_files(
#   FILES
#   Action1.action
#   Action2.action
# )

# generate_messages(
#   DEPENDENCIES
#   std_msgs
# )

# generate_dynamic_reconfigure_options(
#   cfg/DynReconf1.cfg
#   cfg/DynReconf2.cfg
# )

catkin_package()

include_directories(
# include
  ${catkin_INCLUDE_DIRS}
)

# add_library(${PROJECT_NAME}
#   src/${PROJECT_NAME}/nvidia_control.cpp
# )

# add_dependencies(${PROJECT_NAME} ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})

add_executable(
  nvidia_control_node
  src/nvidia_control.cpp
  src/nvidia_control_node.cpp
  src/nvidia_control_node.h)

# set_target_properties(${PROJECT_NAME}_node PROPERTIES OUTPUT_NAME node PREFIX "")

add_dependencies(
  nvidia_control_node
  ${${PROJECT_NAME}_EXPORTED_TARGETS} 
  ${catkin_EXPORTED_TARGETS})

target_link_libraries(
  nvidia_control_node 
  ${catkin_LIBRARIES}
  jsoncpp
  WebSocket
  pthread
)

install(
  TARGETS nvidia_control_node
  RUNTIME DESTINATION 
  ${CATKIN_PACKAGE_BIN_DESTINATION}
)
