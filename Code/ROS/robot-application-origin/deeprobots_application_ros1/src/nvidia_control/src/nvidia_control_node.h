
#include <ros/ros.h>
#include <iostream>
#include <std_msgs/String.h>
#include <geometry_msgs/Twist.h> 
#include <geometry_msgs/Pose.h>

#include <chrono>
#include <string>
#include <map>

#include "libWebSocket.h"
#include <jsoncpp/json/json.h>

class NvidiaCtrlNode
{

public:
    NvidiaCtrlNode(ros::NodeHandle& nh);
    ~NvidiaCtrlNode();

    void timerCallback(const ros::TimerEvent&);
    void navPositionCallback(const std_msgs::String::ConstPtr& msg);
    void navStatusCallback(const std_msgs::String::ConstPtr& msg);

    void actionPlanningMove(const string msg);
    void actionMappingControl(const string msg);

private: 
    ros::NodeHandle nh_; // 节点
    ros::Timer timer_; 
    /************感知主机算法模块********************* */
    ros::Publisher actionPlanningMove_pub;    // 发布机器狗固定点位坐标到感知主机
    ros::Subscriber navPosition_sub_;         // 从感知主机订阅者机器人实时位置
    ros::Publisher mappingControl_pub;        // 地图更新给感知主机
    ros::Subscriber navStatus_sub_;        // 地图更新给感知主机
    ros::Publisher publishVirtualWall;
    /************************************************ */

};
