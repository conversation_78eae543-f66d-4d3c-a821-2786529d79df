#include "nvidia_control_node.h"
#include <tf/transform_datatypes.h>
#include <tf/transform_broadcaster.h>
#include <tf/transform_listener.h>
#include <tf/LinearMath/Quaternion.h>
#include <thread>

using namespace WS;

#define WEBSOCKET_CON_TIMEOUT      30         // webSocket 连接超时时间 30s
#define CLIENT_NVIDIA             "nvidia"    //云深处主控的连接

unsigned long getCurrentTimeStramp() {
    auto now = std::chrono::high_resolution_clock::now();
    auto time_now = std::chrono::high_resolution_clock::to_time_t(now);
    std::tm now_tm = *std::localtime(&time_now);
    return std::mktime(&now_tm);
}
unsigned long currentTimeStramp_ = getCurrentTimeStramp();
bool bConnected_ = false;
string strConnectUrl_ = "ws://192.168.1.110:19002";
int nConnectIndex_ = 0;

void notifyWsMsgCallback(void *handle, const char *msg, int index) {
    nConnectIndex_ = index;
    currentTimeStramp_ = getCurrentTimeStramp();
    Json::Reader reader;
    Json::Value value;
    if (false == reader.parse(msg, value)) {
        return;
    }
    if (!value["type"].isNull()) {
        string strType = value["type"].asString();
        if ("connect_success" == strType) {
            bConnected_ = true;
            std::this_thread::sleep_for(std::chrono::milliseconds(20));
            Json::Value reqValue;
            reqValue["client_type"] = CLIENT_NVIDIA;
            reqValue["action"] = "success";
            WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);
        } 
    }
    if (value["client_type"].isNull()) {
        return;
    }
    string strClientType = value["client_type"].asString();
    if(CLIENT_NVIDIA != strClientType) {
        return;
    }
    if (value["action"].isNull()) {
        return;
    }
    string action = value["action"].asString();
    NvidiaCtrlNode *parent = (NvidiaCtrlNode *)handle;
    if(nullptr == parent) {
        return;
    }
    ROS_INFO("Received msg form nav ctrl: %s", msg);
    Json::StreamWriterBuilder writer;
    std::string strParams = Json::writeString(writer, value["params"]);
    if (action == "navigation_control") {
        parent->actionPlanningMove(strParams);
    }
    else if (action == "mapping_control") {
        parent->actionMappingControl(strParams);
    }
}

NvidiaCtrlNode::NvidiaCtrlNode(ros::NodeHandle& nh) : nh_(nh){
    // WS服务启动
    WS_Init(EN_WS_ClIENT, 19002);
    //设置接受msg的回调函数
    WS_SetMsgCallback(notifyWsMsgCallback, this);
    WS_Connect(strConnectUrl_.c_str());
    
    
        // 定时器，一秒触发一次
    timer_ = nh_.createTimer(ros::Duration(10), &NvidiaCtrlNode::timerCallback, this, false);
    /**********感知算法交互模块************************* */
    // 向感知主机下发移动点位
    actionPlanningMove_pub = nh.advertise<std_msgs::String>("/navigation_control", 10); // 发布机器狗固定点位坐标到感知主机
    mappingControl_pub = nh.advertise<std_msgs::String>("/mapping_control", 10); // 地图更新给感知主机
    navPosition_sub_ = nh.subscribe("/navigation_position", 10, &NvidiaCtrlNode::navPositionCallback, this); // 从感知主机订阅者机器人实时位置
    navStatus_sub_ = nh.subscribe("/task_status", 10, &NvidiaCtrlNode::navStatusCallback, this); // 感知主机上报的地图
    // 虚拟墙
    publishVirtualWall = nh.advertise<std_msgs::String>("/virtual_wall_control", 10);
    /*********************************** */

}

NvidiaCtrlNode::~NvidiaCtrlNode() {
}

void NvidiaCtrlNode::timerCallback(const ros::TimerEvent&) {
        /***********webSocket的超时处理逻辑**********/
    if ((getCurrentTimeStramp() - currentTimeStramp_) > WEBSOCKET_CON_TIMEOUT) {
        bConnected_ = false;
        std::cerr << "websocket reconnect" << std::endl;
        WS_Connect(strConnectUrl_.c_str());
    }
    /********************************************/
}

void NvidiaCtrlNode::actionPlanningMove(const string msg) {
    std_msgs::String nav_ctrl_msg;
    nav_ctrl_msg.data = msg;
    actionPlanningMove_pub.publish(nav_ctrl_msg);
}

void NvidiaCtrlNode::actionMappingControl(const string msg) {
    std_msgs::String map_ctrl_msg;
    map_ctrl_msg.data = msg;
    mappingControl_pub.publish(map_ctrl_msg);
}


void NvidiaCtrlNode::navPositionCallback(const std_msgs::String::ConstPtr& msg) {
    ROS_INFO("Received msg form nav: %s", msg->data.c_str());
    std::string strMsg = msg->data;
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    if (value.isNull()) {
        ROS_INFO("json parse error");
        return;
    }
    Json::Value jMsgValue;
    jMsgValue["client_type"] = CLIENT_NVIDIA;
    jMsgValue["action"] = "navigation_position";
    jMsgValue["params"] = value; 
    WS_Send(jMsgValue.toStyledString().c_str(), nConnectIndex_);
}


void NvidiaCtrlNode::navStatusCallback(const std_msgs::String::ConstPtr& msg) {
    ROS_INFO("Received msg form nav: %s", msg->data.c_str());
    std::string strMsg = msg->data;
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    if (value.isNull()) {
        ROS_INFO("json parse error");
        return;
    }
    Json::Value jMsgValue;
    jMsgValue["client_type"] = CLIENT_NVIDIA;
    jMsgValue["action"] = "task_status";
    jMsgValue["params"] = value; 
    WS_Send(jMsgValue.toStyledString().c_str(), nConnectIndex_);
}

