//
/*****websocket通讯服务端节点******/
/**********默认端口为19002********/
//

#include "nvidia_control_node.h"
#include <iostream>
#include <chrono>
#include <string>
#include <csignal>  
#include <signal.h>

using namespace std;

void signal_handler(int signal) {
    switch (signal) {
        case SIGTSTP:
            std::cout << "Caught Ctrl+Z!" << std::endl;
            // 可以在这里执行一些操作，比如保存状态或暂停程序
            break;
        default:
            std::cout << "Caught " << signal << std::endl;
            // 处理其他信号
            break;
    }
}

//local_port = 43894"
//remote_port = 43893
//remote_ip = "*************"

int main(int argc, char **argv)
{
    // 注册信号处理函数
    struct sigaction sa_ctrl_c;
    sa_ctrl_c.sa_handler = &signal_handler;
    sigemptyset(&sa_ctrl_c.sa_mask);
    sa_ctrl_c.sa_flags = 0;
    if (sigaction(SIGINT, &sa_ctrl_c, NULL) == -1) {
        std::cerr << "Failed to register signal handler" << std::endl;
        return 1;
    }
    ROS_INFO("nvidia_control_node init"); // 输出初始化日志
    ros::init(argc, argv, "nvidia_control_node"); 
    ros::NodeHandle nh;
    auto node = std::make_shared<NvidiaCtrlNode>(nh); 
    // 10 Hz
    ros::Rate loop_rate(10); 
    ros::spin();
    return 0;
}
