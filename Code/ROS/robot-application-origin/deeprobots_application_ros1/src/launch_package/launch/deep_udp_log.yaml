log4j.rootLogger=INFO, ROLLINGFILE
log4j.appender.ROLLINGFILE=org.apache.log4j.RollingFileAppender
log4j.appender.ROLLINGFILE.Append=true
log4j.appender.ROLLINGFILE.File=/home/<USER>/robot-application/deeprobots_application_ros1/log/deep_udp_ctrl-stdout.log
log4j.appender.ROLLINGFILE.RollingPolicy=org.apache.log4j.rolling.SizeAndTimeBasedFNATP
log4j.appender.ROLLINGFILE.MaxFileSize=10240KB
log4j.appender.ROLLINGFILE.MaxBackupIndex=10
log4j.appender.ROLLINGFILE.RollingPolicy.FileNamePattern=/home/<USER>/robot-application/deeprobots_application_ros1/log/deep_udp_ctrl-stdout.log.%d{yyyyMMdd}.%i.log
log4j.appender.ROLLINGFILE.layout=org.apache.log4j.PatternLayout
log4j.appender.ROLLINGFILE.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5p %c{1}:%L - %m%n
log4j.logger.ros.robdog_platintera=DEBUG
