<launch>
    <!-- 定义 config_file 参数 -->
    <!-- arg name="config_file" default="$(find robdog_platintera)/src/config.yaml"/ -->
    
    <!-- 加载 ROS 参数 -->
    <!--rosparam file="$(arg config_file)" command="load"/-->
    
    <!-- 启动语音节点前需先启动讯飞语音服务 -->
    <node pkg="xunfei_audio" type="xunfei_audio_node" name="xunfei_audio_node" respawn="true" output="screen"/>

    <!-- 启动第二个 launch 文件 -->
    <include file="$(find homi_speech)/launch/speech_helper_near.launch"/>
    
    <!-- 启动 robdog_subpub_node 节点 -->
    <node pkg="robdog_platintera" type="robdog_subpub_node" name="robdog_subpub_node" output="log"/>
        <param name="config_log_subnode" value="$(find launch_package)/launch/sub_node_log.yaml"/>
    <!-- 启动 rtsp_image_capturer 节点 -->
    <node pkg="rtsp_image_capturer" type="rtsp_image_capturer" name="rtsp_image_capturer" output="screen"/>

    <!-- 启动 deep_udp_ctrl 节点 -->
    <node pkg="robdog_control" type="deep_udp_ctrl" name="deep_udp_ctrl" output="log"/>
        <param name="config_log_control" value="$(find launch_package)/launch/deep_udp_log.yaml"/>
    <!-- 使用视频通话前需先开启视频推流服务 -->
    <param name="config_file" value="$(find rtc)/launch/config/param.yaml" />
    <node pkg="rtc" type="rtc_node" name="rtc_node" respawn="true" output="screen"/>
    
</launch>
