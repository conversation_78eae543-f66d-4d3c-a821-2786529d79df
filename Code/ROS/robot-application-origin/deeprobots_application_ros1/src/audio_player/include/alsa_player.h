#ifndef __ALSA_PLAYER_H__
#define __ALSA_PLAYER_H__
#include<stdio.h>
#include<string.h>
#include<thread>
#include"alsa_helper.h"

typedef struct SHARED_AUDIO
{
    char* m_Audio;
    int m_Totsize;
    int m_Writeindex;
    int m_Readindex;
}audio_buffer;

class AlsaPlayer{
public:
    AlsaPlayer(std::string device, std::string format, int channels, int rate, int bufferFrame, audio_buffer* audioBuffer);
    ~AlsaPlayer();

    bool init();
    void start();
    
    std::string getPlayerStatus();

private:
    void playFunction();
    
    AlsaHelperConfig _audioPlayerConfig;
    int _err;
    std::string _device;            //播放设备号
    snd_pcm_t* _audioPlayer;        //播放设备句柄
    std::string _format;            //编码格式
    int _channels;                  //通道数
    int _rate;                      //频率
    snd_pcm_uframes_t _frameSize;   //播放设备帧

    int _bufferFrame;               //缓冲区帧
    audio_buffer* _audioBuffer;     //环形缓冲区
    int8_t* _playoutBuffer;         //播放缓冲区

    bool _isPlay;                   //播放线程标志位
    std::thread _playThread;        //播放线程指针

protected:
    // ros::NodeHandle nh_;            //ros节点
    // ros::Subscriber audioSuber_;    //声明音频订阅者
};

#endif
