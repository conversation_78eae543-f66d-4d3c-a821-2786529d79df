#ifndef __ALSA_CONTROLLER_H__
#define __ALSA_CONTROLLER_H__
#include <ros/ros.h>
#include <fstream>
#include "alsa_player.h"
#include "audio_player/PCMStream.h"
#include "audio_player/EndPcmPlayer.h"
#include "audio_player/EndWavPlayer.h"
#include "audio_player/GetPcmPlayer.h"
#include "audio_player/GetWavPlayer.h"
#include "audio_player/PlayWav.h"
#include "audio_player/GetPlayerStatus.h"

struct WaveHeader {
    char riff[4];           // "RIFF"
    int32_t length;         // Length of the file excluding the first 8 bytes
    char file_type[4];      // "WAVE"
    char fmt[4];            // "fmt "
    int32_t fmt_length;     // Length of the format data
    short audio_format;     // PCM = 1
    short channels;         // Number of channels (1 for mono, 2 for stereo)
    int32_t sample_rate;    // Sample rate in Hz
    int32_t byte_rate;      // Sample rate * channels * bits per sample / 8
    short sample_alignment; // channels * bits per sample / 8
    short bits_per_sample;  // Bits per sample
    char data[4];           // "data"
    int32_t data_length;    // Length of the data section
};

class AlsaController
{
public:
    AlsaController(std::string device, std::string format, int channels, int rate,
        int bufferFrame, int bufferSize, int topics, ros::NodeHandle& nh);
    ~AlsaController();
    
    bool init();

private:
    void audioCallback(const audio_player::PCMStream::ConstPtr& msg, std::string topic);
    bool getPcmPlayer(audio_player::GetPcmPlayer::Request& req, audio_player::GetPcmPlayer::Response& resp);
    bool endPcmPlayer(audio_player::EndPcmPlayer::Request& req, audio_player::EndPcmPlayer::Response& resp);
    bool getWavPlayer(audio_player::GetWavPlayer::Request& req, audio_player::GetWavPlayer::Response& resp);
    bool playWav(audio_player::PlayWav::Request& req, audio_player::PlayWav::Response& resp);
    bool endWavPlayer(audio_player::EndWavPlayer::Request& req, audio_player::EndWavPlayer::Response& resp);
    bool getPlayerStatus(audio_player::GetPlayerStatus::Request& req, audio_player::GetPlayerStatus::Response& resp);

    AlsaPlayer* _player;
    audio_buffer* _audioBuffer;
    
protected:
    ros::NodeHandle nh_;                        //ros节点
    std::list<ros::Subscriber> audioSubers_;    //声明音频订阅者列表
    std::vector<bool> audioTopicVector_;        //音频订阅者启用状态
    ros::ServiceServer getPcmServer_;           //声明获取pcm播放权限服务
    ros::ServiceServer endPcmServer_;           //声明释放pcm播放权限服务
    ros::ServiceServer getWavServer_;           //声明获取wav文件播放权限服务
    ros::ServiceServer playWavServer_;          //声明wav文件播放服务
    ros::ServiceServer endWavServer_;           //声明释放wav文件播放权限服务
    ros::ServiceServer getStatusServer_;        //声明获取播放器状态服务
    
};

#endif
