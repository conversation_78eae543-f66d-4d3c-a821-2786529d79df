#include "alsa_controller.h"

#define CHUNKSIZE 1024

AlsaController::AlsaController(std::string device, std::string format, int channels, int rate,
    int bufferFrame, int bufferSize, int topics, ros::NodeHandle& nh) : audioTopicVector_(topics, false), nh_(nh), audioSubers_()
{
    _audioBuffer = new audio_buffer();
    _audioBuffer->m_Totsize = bufferSize;
    _audioBuffer->m_Writeindex = 0;
    _audioBuffer->m_Readindex = 0;
    _audioBuffer->m_Audio = new char[_audioBuffer->m_Totsize];
    memset(_audioBuffer->m_Audio, 0, _audioBuffer->m_Totsize);
    
    _player = new AlsaPlayer(device, format, channels, rate, bufferFrame, _audioBuffer);
    
    for(int i = 0; i < topics; i++)
    {
        std::stringstream ss;
        ss << "/audio_player/audio_topic_" << i;
        audioSubers_.push_back(nh_.subscribe<audio_player::PCMStream>(ss.str(), 10, boost::bind(&AlsaController::audioCallback, this, _1, ss.str())));
    }

    getPcmServer_ = nh.advertiseService("/audio_player/get_pcm_player", &AlsaController::getPcmPlayer, this);
    endPcmServer_ = nh.advertiseService("/audio_player/end_pcm_player", &AlsaController::endPcmPlayer, this);
    getWavServer_ = nh.advertiseService("/audio_player/get_wav_player", &AlsaController::getWavPlayer, this);
    playWavServer_ = nh.advertiseService("/audio_player/play_wav", &AlsaController::playWav, this);
    endWavServer_ = nh.advertiseService("/audio_player/end_wav_player", &AlsaController::endWavPlayer, this);
    getStatusServer_ = nh.advertiseService("/audio_player/get_player_status", &AlsaController::getPlayerStatus, this);
}

AlsaController::~AlsaController() {
    // 确保释放 _audioBuffer 内部的 m_Audio 数组
    if (_audioBuffer != nullptr && _audioBuffer->m_Audio != nullptr) {
        delete[] _audioBuffer->m_Audio;
        _audioBuffer->m_Audio = nullptr; // 防止悬挂指针
    }

    // 释放 _audioBuffer 本身
    if (_audioBuffer != nullptr) {
        delete _audioBuffer;
        _audioBuffer = nullptr; // 防止悬挂指针
    }

    // 假设 AlsaPlayer 有一个析构函数来清理其资源
    if (_player != nullptr) {
        delete _player;
        _player = nullptr; // 防止悬挂指针
    }
}

bool AlsaController::init()
{
    if(!_player->init()) return false;
    _player->start();
    return true;
}

bool AlsaController::getPcmPlayer(audio_player::GetPcmPlayer::Request& req, audio_player::GetPcmPlayer::Response& resp)
{
    if(req.str.find("/audio_player/audio_topic_") != 0) {
        ROS_ERROR("AlsaController::getPcmPlayer get param (req.str) not a topic");
        resp.status = false;
        return false;
    }
    
    std::stringstream ss(req.str);
    std::string temp;
    int num;
    
    while (std::getline(ss, temp, '_')) {}
    try {
        num = std::stoi(temp);
    } catch (const std::invalid_argument& e) {
        ROS_ERROR("AlsaController::getPcmPlayer get param (req.str) not a topic\n");
        resp.status = false;
        return false;
    } catch (const std::out_of_range& oor) {
        // 如果数字超出int范围，也返回默认值
        ROS_ERROR("AlsaController::getPcmPlayer get param (req.str) not a topic\n");
        resp.status = false;
        return false;
    }
    if(num >= audioTopicVector_.size()) {
        ROS_ERROR("AlsaController::getPcmPlayer get param (req.str) out of range\n");
    }
    
    for(int i=0; i <= num; i++) {
        if(audioTopicVector_[i]) {
            resp.status = false;
            return false;
        }
    }
    for(int i=num+1; i < audioTopicVector_.size(); i++) {
        audioTopicVector_[i] = false;
    }
    audioTopicVector_[num] = true;
    _audioBuffer->m_Writeindex = _audioBuffer->m_Readindex;
    resp.status = true;
    return true;
}

bool AlsaController::endPcmPlayer(audio_player::EndPcmPlayer::Request& req, audio_player::EndPcmPlayer::Response& resp)
{
    if(req.str.find("/audio_player/audio_topic_") == 0) {
        std::stringstream ss(req.str);
        std::string temp;
        int num;
        
        while (std::getline(ss, temp, '_')) {}
        try {
            num = std::stoi(temp);
        } catch (const std::invalid_argument& e) {
            ROS_ERROR("AlsaController::getPcmPlayer get param (req.str) not a topic\n");
            resp.status = false;
            return false;
        } catch (const std::out_of_range& oor) {
            // 如果数字超出int范围，也返回默认值
            ROS_ERROR("AlsaController::getPcmPlayer get param (req.str) not a topic\n");
            resp.status = false;
            return false;
        }
        if(num >= audioTopicVector_.size()) {
            ROS_ERROR("AlsaController::endPcmPlayer get param (req.str) not a topic\n");
        }
        audioTopicVector_[num] = false;
        resp.status = true;
        return true;
    } else {
        ROS_ERROR("AlsaController::endPcmPlayer get param (req.str) not a topic\n");
        resp.status = false;
        return false;
    }
}

bool AlsaController::getWavPlayer(audio_player::GetWavPlayer::Request& req, audio_player::GetWavPlayer::Response& resp)
{
    for(int i=0; i < audioTopicVector_.size(); i++) {
        if(audioTopicVector_[i]) {
            resp.status = false;
            return false;
        }
    }
    audioTopicVector_[audioTopicVector_.size()-1] = true;
    _audioBuffer->m_Writeindex = _audioBuffer->m_Readindex;
    resp.status = true;
    return true;
}

bool AlsaController::playWav(audio_player::PlayWav::Request& req, audio_player::PlayWav::Response& resp)
{
    std::ifstream file(req.str.c_str(), std::ios::binary);
    if(!file) {
        ROS_ERROR("AlsaController::playWav get param (req.str) not a file path\n");
        resp.status = false;
        return false;
    }

    WaveHeader header;
    file.read(reinterpret_cast<char*>(&header), sizeof(WaveHeader));
    if (file.fail()) {
        ROS_ERROR("AlsaController::playWav failed to read the header\n");
        file.close();
        resp.status = false;
        return false;
    }
    
    if (strncmp(header.riff, "RIFF", 4) != 0 ||
        strncmp(header.file_type, "WAVE", 4) != 0 ||
        strncmp(header.fmt, "fmt ", 4) != 0 ||
        header.audio_format != 1 || // PCM
        strncmp(header.data, "data", 4) != 0) {
        ROS_ERROR("AlsaController::playWav invalid WAV file\n");
        file.close();
        resp.status = false;
        return false;
    }
    
    while(audioTopicVector_[audioTopicVector_.size()-1]) {
        int curlen = _audioBuffer->m_Totsize - (_audioBuffer->m_Writeindex + _audioBuffer->m_Totsize - _audioBuffer->m_Readindex) % _audioBuffer->m_Totsize;  //当前缓冲区空余长度
        while(CHUNKSIZE >= curlen) {
            ROS_ERROR("AlsaController::playWav _audioBuffer is not enough\n");
            usleep(10000);     //等待10ms
            curlen = _audioBuffer->m_Totsize - (_audioBuffer->m_Writeindex + _audioBuffer->m_Totsize - _audioBuffer->m_Readindex) % _audioBuffer->m_Totsize;  //当前缓冲区空余长度
        }
        if((_audioBuffer->m_Writeindex + CHUNKSIZE) > _audioBuffer->m_Totsize) {
            int overlen = _audioBuffer->m_Writeindex + CHUNKSIZE - _audioBuffer->m_Totsize;                  //超出长度部分
            int curlen = CHUNKSIZE - overlen;                                                           //可容纳长度
            file.read(_audioBuffer->m_Audio + _audioBuffer->m_Writeindex, curlen);                      //写入缓冲区
            auto bytesRead = file.gcount();
            _audioBuffer->m_Writeindex = (_audioBuffer->m_Writeindex + bytesRead) % _audioBuffer->m_Totsize;          //取余 写指针位置
            if(file.eof()) {
                ROS_ERROR("AlsaController::playWav file ptr is eof\n");
                file.close();
                resp.status = true;
                return true;
            }
            if(file.fail()) {
                ROS_ERROR("AlsaController::playWav failed to read audio data\n");
                file.close();
                resp.status = false;
                return false;
            }
            file.read(_audioBuffer->m_Audio + _audioBuffer->m_Writeindex, overlen);                     //覆盖原来
            bytesRead = file.gcount();
            _audioBuffer->m_Writeindex = (_audioBuffer->m_Writeindex + bytesRead) % _audioBuffer->m_Totsize;          //取余 写指针位置
            if(file.eof()) {
                ROS_ERROR("AlsaController::playWav file ptr is eof\n");
                file.close();
                resp.status = true;
                return true;
            }
            if(file.fail()) {
                ROS_ERROR("AlsaController::playWav failed to read audio data\n");
                file.close();
                resp.status = false;
                return false;
            }
        } else {
            file.read(_audioBuffer->m_Audio + _audioBuffer->m_Writeindex, CHUNKSIZE);                   //写入缓冲区
            auto bytesRead = file.gcount();
            _audioBuffer->m_Writeindex += bytesRead;
            if(file.eof()) {
                file.close();
                resp.status = true;
                return true;
            }
            if(file.fail()) {
                ROS_ERROR("AlsaController::playWav failed to read audio data\n");
                file.close();
                resp.status = false;
                return false;
            }
        }
    }
    ROS_ERROR("AlsaController::playWav permissions changed, not all files written\n");
    resp.status = false;
    return false;
}

bool AlsaController::endWavPlayer(audio_player::EndWavPlayer::Request& req, audio_player::EndWavPlayer::Response& resp)
{
    audioTopicVector_[audioTopicVector_.size()-1] = false;
    resp.status = true;
    return true;
}


bool AlsaController::getPlayerStatus(audio_player::GetPlayerStatus::Request& req, audio_player::GetPlayerStatus::Response& resp)
{
    resp.status = _player->getPlayerStatus();
    return true;
}

void AlsaController::audioCallback(const audio_player::PCMStream::ConstPtr& msg, std::string topic)
{
    std::stringstream ss(topic);
    std::string temp;
    int num;
    
    while (std::getline(ss, temp, '_')) {}
    ss >> num;
    if(!audioTopicVector_[num]) return;
    int curlen = _audioBuffer->m_Totsize - (_audioBuffer->m_Writeindex + _audioBuffer->m_Totsize - _audioBuffer->m_Readindex) % _audioBuffer->m_Totsize;  //当前缓冲区空余长度
    if(msg->data.size() >= curlen){   
        ROS_INFO("AlsaController::audioCallback _audioBuffer not enough\n");                                               //当前的缓冲区仅剩一帧可以写，此时要等待读取，暂时抛弃一部分数据
        return;
    }
    if((_audioBuffer->m_Writeindex + msg->data.size()) > _audioBuffer->m_Totsize) {                               //大于总长度
        int overlen = _audioBuffer->m_Writeindex + msg->data.size() - _audioBuffer->m_Totsize;                    //超出长度部分
        int curlen = msg->data.size() - overlen;                                                    //可容纳长度
        memcpy(_audioBuffer->m_Audio + _audioBuffer->m_Writeindex, &msg->data[0], curlen);          //写入缓冲区	  
        memcpy(_audioBuffer->m_Audio, &msg->data[0] + curlen, overlen);                             //覆盖原来
        _audioBuffer->m_Writeindex = (_audioBuffer->m_Writeindex + msg->data.size()) % _audioBuffer->m_Totsize;   //取余 写指针位置
    } else {                                                                                        //长度不超出
        memcpy(_audioBuffer->m_Audio + _audioBuffer->m_Writeindex, &msg->data[0], msg->data.size());                       //写入缓冲区
        _audioBuffer->m_Writeindex += msg->data.size();
    }
    
}
