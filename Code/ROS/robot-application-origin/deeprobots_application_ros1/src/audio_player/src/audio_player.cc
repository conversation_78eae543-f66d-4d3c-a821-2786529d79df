#include "alsa_controller.h"
#include <jsoncpp/json/json.h>

std::string getPlayoutDeviceName(ros::NodeHandle& nh)
{
    std::string playoutShPath;
    std::string playoutShParam;
    if(!nh.getParam("alsa_playout_sh_name", playoutShPath) 
            ||!nh.getParam("alsa_playout_sh_param", playoutShParam))
    {
        ROS_ERROR("Getparam (alsa_playback_sh_name or alsa_playback_sh_param) fail");
        return "default";
    }
    try
    {
        Json::Reader str2json;
        Json::Value paramJson;
        if(str2json.parse(playoutShParam.c_str(), paramJson)) {
            auto names = paramJson["name"];
            int cardId = -1;
            for(auto e:names)
            {
                auto nameTmp = e.asString();
                ROS_INFO("PlayoutDeviceName=%s",nameTmp.c_str());
                if(nameTmp=="default")
                {
                    return "default";
                }
                cardId = getCardId(playoutShPath, nameTmp);
                if(cardId>=0)
                {
                    return ("plughw:" + std::to_string(cardId) + ",0");
                }
            }
        } else {
            ROS_ERROR("Failed to parse playoutShParam");
            return "default";
        }
    }
    catch(const std::exception& e)
    {
        ROS_ERROR("%s",e.what());
    }
    ROS_ERROR("No mathch playbackDeviceName");
    return "default";
}

int main(int argc, char *argv[])
{
    char currentPath[256];
    if(getcwd(currentPath, 256) != nullptr) {
        ROS_INFO("Current working directory: %s\n", currentPath);
    }
    ros::init(argc, argv, "audio_player_node");
    ros::NodeHandle nh;
    std::string device = getPlayoutDeviceName(nh);
    std::string format;
    if(!nh.getParam("alsa_playout_format", format))
    {
        ROS_ERROR("Getparam (alsa_playout_format) fail");
        return 1;
    }
    int channels;
    if(!nh.getParam("alsa_playout_channels", channels))
    {
        ROS_ERROR("Getparam (alsa_playout_channels) fail");
        return 1;
    }
    int rate;
    if(!nh.getParam("alsa_playout_rate", rate))
    {
        ROS_ERROR("Getparam (alsa_playout_rate) fail");
        return 1;
    }
    int bufferFrame;
    if(!nh.getParam("alsa_playout_bufferFrame", bufferFrame))
    {
        ROS_ERROR("Getparam (alsa_playout_bufferFrame) fail");
        return 1;
    }
    int bufferSize;
    if(!nh.getParam("alsa_playout_bufferSize", bufferSize))
    {
        ROS_ERROR("Getparam (alsa_playout_bufferSize) fail");
        return 1;
    }
    int topics;
    if(!nh.getParam("alsa_input_topics", topics))
    {
        ROS_ERROR("Getparam (alsa_input_topics) fail");
        return 1;
    }

    auto playout = std::make_shared<AlsaController>(device, format, channels, rate, bufferFrame, bufferSize, topics, nh);
    if(!playout->init()) {
        ROS_ERROR("Init AlsaController fail\n");
        return 1;
    }
    // for(int i=0; i<10; i++) {
    //     if(playout->playWav()) {
    //         playout->readWav("/home/<USER>/robot-application/deeprobots_application_ros1/resource/audio/fetchExpress2.wav");
    //         playout->endWav();
    //     }
    // }
    
    ros::spin();
    return 0;
}
