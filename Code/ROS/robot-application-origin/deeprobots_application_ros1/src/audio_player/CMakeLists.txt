cmake_minimum_required(VERSION 3.0.2)
project(audio_player)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
  message_generation
)

find_package(ALSA REQUIRED)

add_message_files(
  FILES
  PCMStream.msg
)

add_service_files(
  FILES
  EndPcmPlayer.srv
  EndWavPlayer.srv
  GetPcmPlayer.srv
  GetWavPlayer.srv
  PlayWav.srv
  GetPlayerStatus.srv
)

generate_messages(
  DEPENDENCIES
  std_msgs  # Or other packages containing msgs
)

catkin_package(
#  INCLUDE_DIRS include
#  LIBRARIES audio_player
  CATKIN_DEPENDS roscpp std_msgs message_runtime
#  DEPENDS system_lib
)

include_directories(
# include
  ${catkin_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/include
)

add_executable(${PROJECT_NAME}_node
       src/audio_player.cc
       src/alsa_controller.cc
       src/alsa_player.cc
)

add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})

target_link_libraries(${PROJECT_NAME}_node
      ${catkin_LIBRARIES}
      ${ALSA_LIBRARIES}
      pthread
      jsoncpp
)
