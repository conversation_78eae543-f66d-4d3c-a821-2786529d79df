<launch>
        <param name="alsa_playout_sh_param" value='
                {
                        "name":[
                        "USB Audio Device",
                        "default"
                        ]
                }' />
        <param name="alsa_playout_sh_name" value="$(find homi_speech)/scripts/get_playback_card.sh" />
        <param name="alsa_playout_format" value="Signed 16 bit Little Endian" />
        <param name="alsa_playout_channels" value="1" />
        <param name="alsa_playout_rate" value="16000" />
        <param name="alsa_playout_bufferFrame" value="320" />
        <param name="alsa_playout_bufferSize" value="1048576" />  <!-- 1024*1024 -->
        <param name="alsa_input_topics" value="10" />
</launch>