#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import time
import os
import pexpect

# obexd 的可执行文件路径，根据你的系统修改
OBEXD_PATH = "/usr/lib/bluetooth/obexd"
# 接收文件保存目录
RECEIVE_DIR = "/mine"

def bluetooth_pair():
    """
    配对蓝牙设备。
    """
    # 启动 bluetoothctl
    child = pexpect.spawn('bluetoothctl', encoding='utf-8')

    # 启用蓝牙并设置为可发现的
    child.sendline('power on')
    print("Sent: power on")
    child.sendline('discoverable on')
    print("Sent: discoverable on")
    child.sendline('pairable on')
    print("Sent: pairable on")

    # 标志变量，用于记录是否已经确认了第一个授权请求
    authorize_count = 0

    # 循环检查输出，直到需要用户确认配对
    while True:
        try:
            index = child.expect([
                'Confirm passkey',
                'Pairing confirmed',
                'Failed to pair',
                'Authorize service.*\(yes/no\):',
                pexpect.EOF,
                pexpect.TIMEOUT
            ], timeout=120)  # 增加超时时间

            # 打印蓝牙控制工具的输出
            if child.after is not pexpect.TIMEOUT and child.after is not pexpect.EOF:
                print(child.before.strip())
                print(child.after.strip())

            if index == 0:  # Confirm passkey
                # 自动确认 PIN 码
                child.sendline('yes')
                print("Sent: yes")
                print("Passkey confirmed")
                # 如果确认 `passkey` 后可以直接结束配对，退出循环
                break

            elif index == 3:  # Authorize service
                # 授权服务请求
                child.sendline('yes')
                print("Sent: yes")
                authorize_count += 1
                print(f"Service authorization confirmed ({authorize_count})")
                if authorize_count == 2:
                    print("Second authorization confirmed, connect successful")
                    break

            elif index == 1:  # Pairing confirmed
                print("Pairing confirmed")
                if authorize_count >= 1:
                    print("Pairing successful")
                    break

            elif index == 2:  # Failed to pair
                print("Pairing failed")
                break

            elif index == 4:  # EOF encountered
                print("EOF encountered")
                break

            elif index == 5:  # Timeout occurred
                print("Timeout occurred, retrying...")
                continue

        except pexpect.exceptions.TIMEOUT:
            print("Timeout occurred, retrying...")
            continue

    # 完成后关闭蓝牙控制接口
    child.sendline('exit')
    print("Sent: exit")
    child.close()

def cleanup_bluetooth():
    """
    1) 关闭或删除可能的占用：agent off、remove、pkill...
    2) 保证没有残留 bluetoothctl 或 blueman-applet。
    """
    print("[*] 执行蓝牙清理操作...")

    # 杀掉可能存在的 blueman-applet（如你不需要它）
    subprocess.run(["pkill", "-f", "blueman-applet"], stderr=subprocess.DEVNULL)

    try:
        child = pexpect.spawn("bluetoothctl")
        child.expect("#")
        child.sendline("agent off")
        child.expect("#")
        child.sendline("paired-devices")
        child.expect("#")
        output = child.before.decode("utf-8")
        lines = output.strip().splitlines()
        for line in lines:
            if "Device " in line:
                parts = line.split()
                mac = parts[1]
                print(f"[*] remove 已配对设备: {mac}")
                child.sendline(f"remove {mac}")
                child.expect("#")
        child.close()
    except Exception as e:
        print(f"[!] 清理时出现异常：{e}")

    # 杀掉可能挂着的 bluetoothctl 进程
    subprocess.run(["pkill", "-f", "bluetoothctl"], stderr=subprocess.DEVNULL)

    print("[*] 清理完成。")

def start_obexd():
    """
    启动 obexd：
    1) -r 指定目录
    2) -a 自动接受文件推送
    3) --debug 输出调试信息
    """
    if not os.path.exists(RECEIVE_DIR):
        os.makedirs(RECEIVE_DIR, exist_ok=True)

    print(f"[*] 启动 obexd，文件将保存到: {RECEIVE_DIR}")
    proc = subprocess.Popen([
        OBEXD_PATH,
        "--debug",
        "-r", RECEIVE_DIR,
        "-a",
    ])
    return proc

def main():
    try:
        # (1) 清理蓝牙环境
        cleanup_bluetooth()

        # (2) 配对蓝牙设备
        bluetooth_pair()

        # (3) 启动 obexd 接收文件
        obexd_proc = start_obexd()

        print("[*] 已就绪，请用手机“蓝牙发送文件”到本设备。按 Ctrl+C 退出。")
        # 阻塞等待，直到用户 Ctrl+C
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        print("\n[!] 用户手动中断脚本")
    finally:
        # 脚本退出前，结束 obexd
        subprocess.run(["pkill", "-f", OBEXD_PATH])
        print("[*] obexd 已停止，脚本结束。")

if __name__ == "__main__":
    main()

