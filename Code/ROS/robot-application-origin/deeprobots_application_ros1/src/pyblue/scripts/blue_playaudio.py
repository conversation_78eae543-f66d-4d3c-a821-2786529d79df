import bluetooth
import wave
import struct
import subprocess
import time
import signal
from contextlib import contextmanager

# 蓝牙音箱的MAC地址
bd_addr = "00:02:7B:01:16:00"

# 超时处理上下文管理器
@contextmanager
def timeout(time):
    # Register a function to raise a TimeoutError on the signal.
    signal.signal(signal.SIGALRM, raise_timeout)
    # Schedule the signal to be sent after ``time``.
    signal.alarm(time)
    try:
        yield
    except TimeoutError:
        pass
    finally:
        # Unregister the signal so it won't be triggered
        # if the timeout is not reached.
        signal.signal(signal.SIGALRM, signal.SIG_IGN)

def raise_timeout(signum, frame):
    raise TimeoutError

def discover_devices(target_addr):
    print("正在搜索附近的蓝牙设备...")
    devices = bluetooth.discover_devices(duration=8, lookup_names=True, flush_cache=True, lookup_class=False)
    if not devices:
        print("没有找到任何设备")
        return None
    
    for addr, name in devices:
        print(f"找到设备: {name} ({addr})")
        if addr == target_addr:
            print(f"找到指定设备: {name} ({addr})")
            return (addr, name)
    
    print(f"未找到指定的设备: {target_addr}")
    return None

def is_paired(addr):
    print(f"检查设备 {addr} 是否已经配对...")
    try:
        result = subprocess.run(["bluetoothctl", "info", addr], capture_output=True, text=True, check=True)
        if "Paired: yes" in result.stdout:
            print(f"设备 {addr} 已经配对")
            return True
        else:
            print(f"设备 {addr} 未配对")
            return False
    except subprocess.CalledProcessError as e:
        print(f"检查设备 {addr} 配对状态失败: {e.stderr}")
        return False

def pair_device(addr):
    print(f"正在配对设备: {addr}")
    try:
        result = subprocess.run(["bluetoothctl", "pair", addr], capture_output=True, text=True, check=True)
        print(f"配对成功: {result.stdout}")
    except subprocess.CalledProcessError as e:
        print(f"设备 {addr} 配对失败: {e.stderr}")
        return False
    return True

def trust_device(addr):
    print(f"正在信任设备: {addr}")
    try:
        result = subprocess.run(["bluetoothctl", "trust", addr], capture_output=True, text=True, check=True)
        print(f"信任成功: {result.stdout}")
    except subprocess.CalledProcessError as e:
        print(f"设备 {addr} 信任失败: {e.stderr}")
        return False
    return True

def connect_device(addr, port=1, max_retries=3, retry_interval=5, timeout_duration=30):
    retries = 0
    while retries < max_retries:
        print(f"正在连接设备: {addr}... (尝试 {retries + 1}/{max_retries})")
        try:
            with timeout(timeout_duration):
                sock = bluetooth.BluetoothSocket(bluetooth.RFCOMM)
                sock.connect((addr, port))
                print(f"设备 {addr} 连接成功")
                return sock
        except (bluetooth.btcommon.BluetoothError, TimeoutError) as e:
            print(f"设备 {addr} 连接失败: {e}")
            if retries < max_retries - 1:
                print(f"等待 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)
            else:
                print("达到最大重试次数，放弃连接。")
        retries += 1
    return None

def main():
    # 发现设备
    target_device = discover_devices(bd_addr)
    if not target_device:
        return

    addr, name = target_device

    # 检查设备是否已经配对
    if not is_paired(addr):
        if not pair_device(addr):
            print("配对失败，退出程序")
            return

    if trust_device(addr):
        sock = connect_device(addr)
        if sock:
            # 打开音频文件
            with wave.open('/opt/ros/andlink_ros/robot-application/deeprobots_application_ros1/resource/ring.wav', 'rb') as wf:
                # 读取音频文件的数据
                data = wf.readframes(1024)

                # 循环直到文件结束
                while data:
                    # 发送音频数据
                    sock.send(data)
                    data = wf.readframes(1024)

            # 关闭套接字
            sock.close()
            print("蓝牙套接字已关闭")

if __name__ == "__main__":
    main()