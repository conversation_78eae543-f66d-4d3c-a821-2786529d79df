import subprocess

def get_connected_bluetooth_devices():
    # 调用 bluetoothctl 并获取输出
    try:
        result = subprocess.run(['bluetoothctl', 'devices'], capture_output=True, text=True, check=True)
        output = result.stdout.strip()
        
        # 解析输出
        connected_devices = []
        lines = output.split('\n')
        for line in lines:
            parts = line.split()
            if len(parts) >= 3:
                device_addr = parts[1]
                device_name = ' '.join(parts[2:])
                connected_devices.append((device_addr, device_name))
        
        return connected_devices
    except subprocess.CalledProcessError as e:
        print(f"Error running bluetoothctl: {e}")
        return []

if __name__ == "__main__":
    connected_devices = get_connected_bluetooth_devices()
    
    if connected_devices:
        print("已连接的蓝牙设备列表:")
        for addr, name in connected_devices:
            print(f"设备名称: {name}, MAC地址: {addr}")
    else:
        print("没有找到已连接的蓝牙设备。")