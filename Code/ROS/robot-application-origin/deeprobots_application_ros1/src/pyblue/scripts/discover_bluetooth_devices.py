import subprocess
import time
from bluetooth import *

# 蓝牙设备地址
device_address = "30:C0:1B:90:8A:8D"

# 音频文件路径
audio_file_path = "/home/<USER>/0920/robot-application/deeprobots_application_ros1/src/homi_speech/wave_src/321茄子.wav"

def connect_bluetooth_device(address):
    try:
        # 搜索设备
        nearby_devices = discover_devices(duration=10, lookup_names=True, flush_cache=True, lookup_class=False)
        print("Found devices:", nearby_devices)

        # 检查设备是否在附近
        if address not in [addr for addr, _ in nearby_devices]:
            print(f"Device {address} not found.")
            return False

        # 连接设备
        subprocess.run(["bluetoothctl", "connect", address], check=True)
        print(f"Connected to device {address}")
        return True
    except Exception as e:
        print(f"Failed to connect to device {address}: {e}")
        return False

def set_default_output_device(device_name):
    try:
        # 设置默认输出设备
        subprocess.run(["pactl", "set-default-sink", device_name], check=True)
        print(f"Set default output device to {device_name}")
    except Exception as e:
        print(f"Failed to set default output device to {device_name}: {e}")

def play_audio_file(file_path, device_name):
    try:
        # 播放音频文件
        subprocess.run(["paplay", "--device", device_name, file_path], check=True)
        print(f"Playing audio file {file_path} on device {device_name}")
    except Exception as e:
        print(f"Failed to play audio file {file_path} on device {device_name}: {e}")

if __name__ == "__main__":
    # 连接蓝牙设备
    if connect_bluetooth_device(device_address):
        # 获取设备名称
        device_name = f"bluez_sink.{device_address}.a2dp_sink"
        
        # 设置默认输出设备
        set_default_output_device(device_name)
        
        # 播放音频文件
        play_audio_file(audio_file_path, device_name)
    else:
        print("Failed to connect to the Bluetooth device.")