import pexpect

def bluetooth_pair(server_address):
    # 启动 bluetoothctl
    child = pexpect.spawn('bluetoothctl', encoding='utf-8')

    # 启用蓝牙并设置为可发现的
    child.sendline('power on')
    print("Sent: power on")
    child.sendline('discoverable on')
    print("Sent: discoverable on")
    child.sendline('pairable on')
    print("Sent: pairable on")

    # 标志变量，用于记录是否已经确认了第一个授权请求
    authorize_count = 0

    # 循环检查输出，直到需要用户确认配对
    while True:
        try:
            index = child.expect([
                'Confirm passkey',
                'Pairing confirmed',
                'Failed to pair',
                'Authorize service.*\(yes/no\):',
                pexpect.EOF,
                pexpect.TIMEOUT
            ], timeout=120)  # 增加超时时间

            # 打印蓝牙控制工具的输出
            if child.after is not pexpect.TIMEOUT and child.after is not pexpect.EOF:
                print(child.before.strip())
                print(child.after.strip())

            if index == 0 or index == 3:
                # 当检测到需要确认 PIN 码或服务授权时，自动发送 'yes'
                child.sendline('yes')
                print("Sent: yes")
                print("PIN or service authorization confirmed")
                if index == 3:
                    authorize_count += 1
                    if authorize_count == 2:
                        print("Second authorization confirmed, connect successful")
                        break
            elif index == 1:
                print("Pairing confirmed")
                if authorize_count >= 1:
                    print("Pairing successful")
                    break
            elif index == 2:
                print("Pairing failed")
                break
            elif index == 4:
                print("EOF encountered")
                break
            elif index == 5:
                print("Timeout occurred, retrying...")
                continue
        except pexpect.exceptions.TIMEOUT:
            print("Timeout occurred, retrying...")
            continue

    # 完成后关闭蓝牙控制接口
    child.sendline('exit')
    print("Sent: exit")
    child.close()

if __name__ == "__main__":
    server_address = "14:99:3E:F4:3C:AA"
    bluetooth_pair(server_address)