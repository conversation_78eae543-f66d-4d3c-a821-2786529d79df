import os
import bluetooth

# 创建蓝牙套接字
server_sock = bluetooth.BluetoothSocket(bluetooth.RFCOMM)

# 绑定到RFCOMM通道
server_sock.bind(("", 1))
server_sock.listen(1)

print("Waiting for connection...")
client_sock, client_info = server_sock.accept()
print("Accepted connection from", client_info)

# 接收文件名（包含完整路径）
full_filename = client_sock.recv(1024).decode('utf-8')
print("Receiving file:", full_filename)

# 提取文件名（不包含路径）
filename = os.path.basename(full_filename)
print("extrat file:", filename)
# 指定保存文件的目录
save_directory = "/home/<USER>/blue_receive_file"

# 确保保存目录存在
if not os.path.exists(save_directory):
    os.makedirs(save_directory, exist_ok=True)

# 构造完整的保存路径
save_path = os.path.join(save_directory, filename)

# 创建文件并写入数据
try:
    with open(save_path, 'wb') as f:
        while True:
            data = client_sock.recv(1024)
            if not data:
                break
            f.write(data)
except bluetooth.btcommon.BluetoothError as e:
    print(f"Bluetooth error: {e}")
finally:
    client_sock.close()
    server_sock.close()
    print("Connection closed or failed.")