import bluetooth
import wave

# 创建蓝牙套接字
server_sock = bluetooth.BluetoothSocket(bluetooth.RFCOMM)

# 获取一个可用的RFCOMM端口
port = bluetooth.get_available_port(bluetooth.RFCOMM)
server_sock.bind(("", port))

# 启动监听
server_sock.listen(1)
print("Listening on port %d" % port)

# 注册服务
uuid = "1e0ca4ea-299d-4335-93eb-27fcfe7fa848"
bluetooth.advertise_service(server_sock, "Audio Service", uuid)

# 等待客户端连接
client_sock, address = server_sock.accept()
print("Accepted connection from ", address)

# 接收音频数据并保存到文件
with wave.open('received_audio.wav', 'wb') as wav_file:
    wav_file.setnchannels(1)  # 设置声道数为1（单声道）
    wav_file.setsampwidth(2)  # 设置采样宽度为2字节（16位）
    wav_file.setframerate(44100)  # 设置采样率为44100 Hz

    buffer_size = 1024
    while True:
        data = client_sock.recv(buffer_size)
        if not data:
            break
        wav_file.writeframes(data)

print("Audio data received and saved to received_audio.wav")

# 关闭连接
client_sock.close()
server_sock.close()