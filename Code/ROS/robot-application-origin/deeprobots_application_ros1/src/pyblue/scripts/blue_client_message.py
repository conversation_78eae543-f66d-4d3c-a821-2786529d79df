import bluetooth
import os

# 服务器的 MAC 地址
server_address = "54:EF:33:9C:EA:4D"
port = 1

# 创建蓝牙套接字
client_sock = bluetooth.BluetoothSocket(bluetooth.RFCOMM)

# 连接到服务器
client_sock.connect((server_address, port))

try:
    # 发送文件名
    filename = "/opt/ros/andlink_ros/robot-application/deeprobots_application_ros1/resource/step2_凶左看右看.mp4"
    client_sock.send(filename.encode('utf-8'))

    # 读取文件并发送
    with open(filename, 'rb') as f:
        while True:
            data = f.read(1024)
            if not data:
                break
            client_sock.send(data)

    print("File sent successfully")

    # 发送结束标志
    client_sock.send(b'END')

    # 等待服务器确认消息
    confirmation = client_sock.recv(1024).decode('utf-8')
    print("Received confirmation:", confirmation)

except Exception as e:
    print("Error:", e)

finally:
    # 关闭连接
    client_sock.close()
    print("Connection closed")