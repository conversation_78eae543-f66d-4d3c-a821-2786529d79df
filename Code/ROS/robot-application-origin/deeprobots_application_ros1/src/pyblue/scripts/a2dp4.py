#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pexpect
import subprocess
import time
import sys
import os
import pyaudio
import struct
import numpy as np
import rclpy
from homi_speech_interface.msg import PCMStream, Wakeup

def send_wakeup_event(publisher, pcm_data=None):
    """
    发布唤醒事件消息。
    """
    msg = Wakeup()

    # 设置唤醒词相关信息
    msg.wakeup.ivw_word = "hello"  # 设置唤醒词，后续可以动态更改
    msg.wakeup.angle = 0  # 设置角度
    msg.wakeup.extra_info = ""  # 设置附加信息

    # 发布唤醒事件消息
    publisher.publish(msg)
    print(f"[*] 已发布唤醒事件消息: ivw_word={msg.ivw_word}, angle={msg.angle}, extra_info={msg.extra_info}")

def start_pulseaudio():
    """
    启动（或重启）Pulseaudio，确保在当前用户会话中运行。
    """
    # 先尝试杀掉可能残留的 pulseaudio
    subprocess.run(["pulseaudio", "-k"], stderr=subprocess.DEVNULL)
    # 再启动
    proc = subprocess.run(["pulseaudio", "--start"], capture_output=True)
    if proc.returncode != 0:
        print("[!] Pulseaudio 启动失败，输出：", proc.stderr.decode("utf-8"))
    else:
        print("[*] Pulseaudio 已启动。")

def cleanup_bluetooth():
    """
    1) 关闭或删除可能的占用：agent off、remove、pkill...
    2) 保证没有残留 bluetoothctl 或 blueman-applet。
    """
    print("[*] 执行蓝牙清理操作...")

    # 杀掉可能存在的 blueman-applet（如你不需要它）
    subprocess.run(["pkill", "-f", "blueman-applet"], stderr=subprocess.DEVNULL)

    try:
        child = pexpect.spawn("bluetoothctl")
        child.expect("#")
        child.sendline("agent off")
        child.expect("#")
        child.sendline("paired-devices")
        child.expect("#")
        output = child.before.decode("utf-8")
        lines = output.strip().splitlines()
        for line in lines:
            if "Device " in line:
                parts = line.split()
                mac = parts[1]
                print(f"[*] remove 已配对设备: {mac}")
                #child.sendline(f"remove {mac}")
                #child.expect("#")
        child.close()
    except Exception as e:
        print(f"[!] 清理时出现异常：{e}")

    # 杀掉可能挂着的 bluetoothctl 进程
    subprocess.run(["pkill", "-f", "bluetoothctl"], stderr=subprocess.DEVNULL)

    print("[*] 清理完成。")

def bluetooth_pair():
    """
    配对蓝牙设备。
    """
    # 启动 bluetoothctl
    child = pexpect.spawn('bluetoothctl', encoding='utf-8')

    # 启用蓝牙并设置为可发现的
    child.sendline('power on')
    print("Sent: power on")
    child.sendline('discoverable on')
    print("Sent: discoverable on")
    child.sendline('pairable on')
    print("Sent: pairable on")

    # 标志变量，用于记录是否已经确认了第一个授权请求
    authorize_count = 0

    # 循环检查输出，直到需要用户确认配对
    while True:
        try:
            index = child.expect([ 
                r'Confirm passkey',
                r'Authorize service.*\(yes/no\):',  
                r'Pairing confirmed',
                r'Failed to pair',
                pexpect.EOF,
                pexpect.TIMEOUT
            ], timeout=120)

            print(f"[DEBUG] Matched index: {index}")
            print(f"[DEBUG] child.before: {child.before.strip()}")
            print(f"[DEBUG] child.after: {child.after.strip()}")

            if index == 0:
                # Confirm passkey
                child.sendline('yes')
                print("[*] Passkey confirmed -> Sent: yes")

            elif index == 1:
                # Authorize service
                text = child.after.strip()
                if "0000110d-0000-1000-8000-00805f9b34fb" in text:
                    child.sendline('yes')
                    print("[*] Authorized A2DP (0x110D). 准备退出...")
                    break
                else:
                    child.sendline('no')
                    print(f"[!] 非 A2DP 服务，输出信息: {text}")

            elif index == 2:
                print("[*] Pairing confirmed")
                continue

            elif index == 3:
                print("[!] Pairing failed")
                break

            elif index == 4:
                print("[!] bluetoothctl EOF encountered，退出循环。")
                break

            elif index == 5:
                print("[!] 等待超时，继续等待。")
                continue

        except pexpect.exceptions.TIMEOUT:
            print("[!] pexpect 超时，继续等待。")
            continue

    child.sendline('exit')
    print("Sent: exit")
    child.close()

def find_monitor_device():
    """
    使用 pactl 命令查找所有蓝牙 A2DP Source Monitor 设备名。
    如果未找到，循环查找直到找到为止。
    """
    while True:
        try:
            output = subprocess.check_output(["pactl", "list", "sources", "short"]).decode("utf-8")
            print("[*] pactl list short sources 输出：")
            print(output)  # 打印完整输出，帮助调试
            for line in output.strip().splitlines():
                if "bluez_source" in line and "a2dp_source" in line:
                    parts = line.split()
                    if len(parts) >= 2:
                        return parts[1]
        except subprocess.CalledProcessError as e:
            print(f"[!] 获取 Monitor 设备时出错：{e}")
        
        print("[*] 未找到设备，正在等待...")
        time.sleep(1)

def capture_pcm_with_parec(node, publisher, device_name, output_file="output.raw", timeout=5):
    # 打印开始捕获的设备名称
    print(f"[*] 开始捕获设备: {device_name}")
    
    process = subprocess.Popen(
        ['parec', '--device=' + device_name, '--rate=16000', '--channels=1', '--format=s16le', output_file],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )

    last_time_data_received = time.time()

    try:
        while True:
            tail_process = subprocess.Popen(
                ['tail', '-n', '1', '-f', output_file],  # 实时查看文件
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            tail_output = tail_process.stdout.read(5000)
            if tail_output:
                last_time_data_received = time.time()
                print(f"[*] 成功读取到 {len(tail_output)} 字节数据")
                
                # 将捕获的字节流封装为 PCMStream 消息
                msg = PCMStream()
                msg.ts = int(time.time() * 1000)  # 使用当前时间戳（毫秒）
                msg.data = list(tail_output)  # 将字节流转换为列表

                # 发布消息到 "pcm_stream" 主题
                publisher.publish(msg)
                print(f"[*] 已发布 {len(tail_output)} 字节数据到 'pcm_stream' 主题。")
            else:
                current_time = time.time()
                if current_time - last_time_data_received > timeout:
                    print(f"[*] 超过 {timeout} 秒没有接收到数据，继续等待...")
                    last_time_data_received = current_time
                else:
                    print("[*] 等待数据中...")

            # 每隔1秒检查一次是否继续等待
            time.sleep(1)

    except KeyboardInterrupt:
        print("[*] 捕获中断，停止捕获数据")
    finally:
        process.terminate()
        print("[*] 进程已终止")

def main():
    rclpy.init()
    node = rclpy.create_node("audio_publisher_node")

    # 创建发布者
    pcm_publisher = node.create_publisher(PCMStream, "pcm_stream", 10)
    wakeup_publisher = node.create_publisher(Wakeup, "wakeup_event", 10)

    # 1) 启动/重启 Pulseaudio
    start_pulseaudio()

    # 2) 清理蓝牙环境
    cleanup_bluetooth()

    # 3) 执行自动配对
    bluetooth_pair()

    # 4) 发布唤醒事件消息，等待一段时间，确保 Monitor 设备被创建
    send_wakeup_event(wakeup_publisher)
    time.sleep(3)


    # 5) 捕获 PCM 数据并实时发布
    monitor_device = find_monitor_device()

    if monitor_device:
        capture_pcm_with_parec(node, pcm_publisher, monitor_device)
    else:
        print("[!] 未找到 A2DP Monitor 设备。请确保手机已成功连接并通过 A2DP 传输音频。")
        sys.exit(1)

    print("[*] 脚本执行完成。")
    rclpy.shutdown()  # 结束 rclpy

if __name__ == "__main__":
    main()


