import bluetooth
import wave

# 创建蓝牙套接字
client_sock = bluetooth.BluetoothSocket(bluetooth.RFCOMM)

# 连接到服务端
server_address = "54:EF:33:9C:EA:5B"  # 替换为服务端的MAC地址
port = 1  # 服务端监听的端口号
client_sock.connect((server_address, port))

# 读取音频文件并发送
with wave.open('audio_to_send.wav', 'rb') as wav_file:
    buffer_size = 1024
    while True:
        data = wav_file.readframes(buffer_size)
        if not data:
            break
        client_sock.send(data)

print("Audio data sent")

# 关闭连接
client_sock.close()