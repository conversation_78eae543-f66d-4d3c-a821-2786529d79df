import dbus
import subprocess
import time

def start_blueman_applet():
    # 检查 blueman-applet 是否已经在运行
    result = subprocess.run(['pgrep', 'blueman-applet'], capture_output=True, text=True)
    if not result.stdout:
        # 如果没有运行，启动 blueman-applet
        subprocess.Popen(['blueman-applet'])
        print("启动 blueman-applet")
        # 等待一段时间，确保 blueman-applet 完全启动
        time.sleep(5)

def set_blueman_accept_files(enable, save_path):
    try:
        # 确保 blueman-applet 已经运行
        start_blueman_applet()

        # 连接到 D-Bus 会话总线
        bus = dbus.SessionBus()

        # 获取 Blueman Applet 的 D-Bus 对象
        blueman_object = bus.get_object('org.blueman.Applet', '/org/blueman/Applet')

        # 获取 D-Bus 接口
        blueman_interface = dbus.Interface(blueman_object, 'org.blueman.Applet')

        # 设置“接受来自受信任设备的文件”选项
        blueman_interface.SetProperty('org.blueman.Applet', 'AcceptFiles', enable)

        # 设置保存文件的路径
        blueman_interface.SetProperty('org.blueman.Applet', 'SavePath', save_path)

        print(f"Blueman 设置更新：接受文件 {'启用' if enable else '禁用'}，保存路径设为 {save_path}")
    except dbus.exceptions.DBusException as e:
        if "NameHasNoOwner" in str(e) or "NoReply" in str(e):
            print("Blueman Applet 未运行，请先启动 Blueman Applet 服务。")
        else:
            print(f"发生 D-Bus 错误: {e}")

if __name__ == "__main__":
    # 示例：启用接受文件并设置保存路径
    set_blueman_accept_files(True, "/home/<USER>/blue_receive_file")