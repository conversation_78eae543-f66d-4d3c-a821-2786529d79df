import bluetooth
import select

class MyDiscoverer(bluetooth.DeviceDiscoverer):
    
    def pre_inquiry(self):
        self.done = False
        print("Starting inquiry...")
    
    def device_discovered(self, address, device_class, name):
        print("device_discovered")
        print(f"Device discovered: {address} - {name}")
        print(f"Device class: {device_class}")

    def inquiry_complete(self):
        self.done = True
        print("Inquiry complete")

d = MyDiscoverer()
d.find_devices(lookup_names = True, duration=30)

readfiles = [ d, ]

while True:
    rfds = select.select( readfiles, [], [] )[0]
    print("Checking for events...")

    if d in rfds:
        d.process_event()
        print("Event processed")


    if d.done:
         print("Done")
         break