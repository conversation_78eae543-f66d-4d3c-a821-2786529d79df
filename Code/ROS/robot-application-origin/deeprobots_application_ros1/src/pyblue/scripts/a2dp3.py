#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pexpect
import subprocess
import time
import sys
import os
import pyaudio
import struct
import numpy as np

def start_pulseaudio():
    """
    启动（或重启）Pulseaudio，确保在当前用户会话中运行。
    """
    # 先尝试杀掉可能残留的 pulseaudio
    subprocess.run(["pulseaudio", "-k"], stderr=subprocess.DEVNULL)
    # 再启动
    proc = subprocess.run(["pulseaudio", "--start"], capture_output=True)
    if proc.returncode != 0:
        print("[!] Pulseaudio 启动失败，输出：", proc.stderr.decode("utf-8"))
    else:
        print("[*] Pulseaudio 已启动。")

def cleanup_bluetooth():
    """
    1) 关闭或删除可能的占用：agent off、remove、pkill...
    2) 保证没有残留 bluetoothctl 或 blueman-applet。
    """
    print("[*] 执行蓝牙清理操作...")

    # 杀掉可能存在的 blueman-applet（如你不需要它）
    subprocess.run(["pkill", "-f", "blueman-applet"], stderr=subprocess.DEVNULL)

    try:
        child = pexpect.spawn("bluetoothctl")
        child.expect("#")
        child.sendline("agent off")
        child.expect("#")
        child.sendline("paired-devices")
        child.expect("#")
        output = child.before.decode("utf-8")
        lines = output.strip().splitlines()
        for line in lines:
            if "Device " in line:
                parts = line.split()
                mac = parts[1]
                print(f"[*] remove 已配对设备: {mac}")
                #child.sendline(f"remove {mac}")
                #child.expect("#")
        child.close()
    except Exception as e:
        print(f"[!] 清理时出现异常：{e}")

    # 杀掉可能挂着的 bluetoothctl 进程
    subprocess.run(["pkill", "-f", "bluetoothctl"], stderr=subprocess.DEVNULL)

    print("[*] 清理完成。")

def bluetooth_pair():
    """
    配对蓝牙设备。
    """
    # 启动 bluetoothctl
    child = pexpect.spawn('bluetoothctl', encoding='utf-8')

    # 启用蓝牙并设置为可发现的
    child.sendline('power on')
    print("Sent: power on")
    child.sendline('discoverable on')
    print("Sent: discoverable on")
    child.sendline('pairable on')
    print("Sent: pairable on")

    # 标志变量，用于记录是否已经确认了第一个授权请求
    authorize_count = 0

    # 循环检查输出，直到需要用户确认配对
    while True:
        try:
            index = child.expect([
                r'Confirm passkey',
                r'Authorize service.*\(yes/no\):',  
                r'Pairing confirmed',
                r'Failed to pair',
                pexpect.EOF,
                pexpect.TIMEOUT
            ], timeout=120)

            print(f"[DEBUG] Matched index: {index}")
            print(f"[DEBUG] child.before: {child.before.strip()}")
            print(f"[DEBUG] child.after: {child.after.strip()}")

            if index == 0:
                # Confirm passkey
                child.sendline('yes')
                print("[*] Passkey confirmed -> Sent: yes")

            elif index == 1:
                # Authorize service
                # child.before 或 child.after 中可以找到完整提示
                # 例如: "[agent] Authorize service 0000110d-0000-1000-8000-00805f9b34fb (yes/no):"
                # 可以检查其中是否包含 110d
                text = child.after.strip()
                if "0000110d-0000-1000-8000-00805f9b34fb" in text:
                    # 只要是 A2DP，就 yes -> break
                    child.sendline('yes')
                    print("[*] Authorized A2DP (0x110D). 准备退出...")
                    break
                else:
                    # 如果不是 A2DP，则您可以选择不处理
                    # 也可以 child.sendline('no') 或 yes，但脚本不会退出
                    # 这里给一个示例: 直接不授权（或者您可以改成自动 yes，但不退出）
                    child.sendline('no')
                    print(f"[!] 非 A2DP 服务，输出信息: {text}")

            elif index == 2:
                # Pairing confirmed
                print("[*] Pairing confirmed")
                # 如果尚未出现 A2DP 授权就配对成功，
                # 看您需求，可能也可以 break
                continue

            elif index == 3:
                # Failed to pair
                print("[!] Pairing failed")
                break

            elif index == 4:
                # EOF
                print("[!] bluetoothctl EOF encountered，退出循环。")
                break

            elif index == 5:
                # TIMEOUT
                print("[!] 等待超时，继续等待。")
                continue

        except pexpect.exceptions.TIMEOUT:
            print("[!] pexpect 超时，继续等待。")
            continue

    # 完成后关闭蓝牙控制接口
    child.sendline('exit')
    print("Sent: exit")
    child.close()


def find_monitor_device():
    """
    使用 pactl 命令查找所有蓝牙 A2DP Source Monitor 设备名。
    如果未找到，循环查找直到找到为止。
    """
    while True:
        try:
            output = subprocess.check_output(["pactl", "list", "sources", "short"]).decode("utf-8")
            print("[*] pactl list short sources 输出：")
            print(output)  # 打印完整输出，帮助调试
            for line in output.strip().splitlines():
                print(f"[DEBUG] 处理 line: {line}")  # 打印每一行
                if "bluez_source" in line and "a2dp_source" in line:
                    parts = line.split()
                    print(f"[DEBUG] 分割后的 parts: {parts}")  # 打印分割后的 parts
                    if len(parts) >= 2:
                        return parts[1]
        except subprocess.CalledProcessError as e:
            print(f"[!] 获取 Monitor 设备时出错：{e}")
        
        # 如果没有找到设备，等待1秒再重试
        print("[*] 未找到设备，正在等待...")
        time.sleep(1)


def capture_pcm_with_parec(device_name, output_file="output.raw", timeout=5):
    # 打印开始捕获的设备名称
    print(f"[*] 开始捕获设备: {device_name}")
    
    # 使用parec来实时捕获指定设备的音频数据并输出到文件
    process = subprocess.Popen(
        ['parec', '--device=' + device_name, '--rate=16000', '--channels=1', '--format=s16le', output_file],  # 输出到文件
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )

    last_time_data_received = time.time()  # 记录最后一次收到数据的时间

    try:
        while True:
            # 使用tail命令实时读取文件
            tail_process = subprocess.Popen(
                ['tail', '-n', '1', '-f', output_file],  # 实时查看文件
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            tail_output = tail_process.stdout.read(5000)  # 读取文件输出的字节流
            if tail_output:
                last_time_data_received = time.time()  # 更新最后一次数据接收时间
                print(f"[*] 成功读取到 {len(tail_output)} 字节数据")
                print(f"十六进制数据：{tail_output.hex()}")
            else:
                # 如果没有数据，检查是否超时
                current_time = time.time()
                if current_time - last_time_data_received > timeout:
                    print(f"[*] 超过 {timeout} 秒没有接收到数据，继续等待...")
                    last_time_data_received = current_time  # 更新超时等待时间
                else:
                    print("[*] 等待数据中...")

            # 每隔1秒检查一次是否继续等待
            time.sleep(1)

    except KeyboardInterrupt:
        print("[*] 捕获中断，停止捕获数据")
    finally:
        # 关闭进程
        process.terminate()
        print("[*] 进程已终止")



def main():
    # 1) 启动/重启 Pulseaudio
    start_pulseaudio()

    # 2) 清理蓝牙环境（可选，如果您想保留之前的配对可跳过）
    cleanup_bluetooth()

    # 3) 执行自动配对
    bluetooth_pair()

    # 4) 等待一段时间，确保 Monitor 设备被创建
    time.sleep(5)

    # 5) 捕获 PCM 数据并实时打印
    monitor_device = find_monitor_device()

    if monitor_device:
        capture_pcm_with_parec(monitor_device)
    else:
        print("[!] 未找到 A2DP Monitor 设备。请确保手机已成功连接并通过 A2DP 传输音频。")
        sys.exit(1)

    print("[*] 脚本执行完毕。")

if __name__ == '__main__':
    main()

