import bluetooth
import os
import time
# 服务器的 MAC 地址
server_address = "54:EF:33:9C:EA:4D"
port = 1

# 创建蓝牙套接字
client_sock = bluetooth.BluetoothSocket(bluetooth.RFCOMM)

# 连接到服务器
client_sock.connect((server_address, port))

try:
    # 发送文件名
    filename = "/opt/ros/andlink_ros/robot-application/deeprobots_application_ros1/resource/眨眼+开心+星星眼.mp4"
    client_sock.send(filename.encode('utf-8'))

    # 读取文件并发送
    with open(filename, 'rb') as f:
        while True:
            data = f.read(1024)
            if not data:
                break
            client_sock.send(data)
    
    time.sleep(120)
    print("File sent successfully")

except Exception as e:
    print("Error:", e)

# finally:
#     # 关闭连接
#     # client_sock.close()
#     print("Connection closed")