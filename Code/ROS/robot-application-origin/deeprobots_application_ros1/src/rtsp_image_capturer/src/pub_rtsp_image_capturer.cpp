#include <ros/ros.h>
#include <std_msgs/String.h>
#include <unistd.h>  

void publishTakePhoto(ros::NodeHandle &nh) {
    // 创建一个发布者对象，指定话题名称和消息类型
    ros::Publisher pub = nh.advertise<std_msgs::String>("/take_photo", 10);


        // 构造消息
        std_msgs::String msg;
        msg.data = "take_photo";

        sleep(2.0);
        // 发布消息
        pub.publish(msg);

        // 输出日志
        ROS_INFO("Published message: [%s]", msg.data.c_str());


}

int main(int argc, char **argv) {
    // 初始化ROS节点
    ros::init(argc, argv, "pub_rtsp_image_capturer");
    ros::NodeHandle nh;

    // 调用发布函数
    publishTakePhoto(nh);

    // 结束节点
    ros::shutdown();

    return 0;
}