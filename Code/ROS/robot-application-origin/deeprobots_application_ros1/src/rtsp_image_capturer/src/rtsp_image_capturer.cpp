#include <ros/ros.h>
// #include <std_msgs/Empty.h>
#include <std_msgs/String.h>
#include <cv_bridge/cv_bridge.h>
#include <image_transport/image_transport.h>
#include <opencv2/opencv.hpp>
#include <gst/gst.h>
#include <gst/app/gstappsink.h> 
#include <glib.h>
#include <sensor_msgs/Image.h>
#include <sensor_msgs/CameraInfo.h>
#include <iostream>
#include <sys/stat.h>  
#include <unistd.h> 
#include <filesystem>
#include <fstream>
#include "rtsp_image_capturer/PrintRequest.h"
// #include <homi_speech/def.h>
#include <homi_speech/UploadImageUrl.h>
#include <homi_speech/UploadImageUrlRequest.h>
#include <homi_speech/UploadImageUrlResponse.h>
#include <homi_speech_interface/UploadImage.h>
#include <homi_speech_interface/UploadImageRequest.h>
#include <homi_speech_interface/UploadImageResponse.h>
#include <homi_speech_interface/IotControl.h>
std::string save_dir = "/tmp/directory/";
std::string filename_prefix = "photo_";
static int count = 0;
gboolean stop_flag = false;


// void on_bus_message(GstBus *bus, GstMessage *msg, gpointer user_data);


void deletePhotoFilesInDirectory(const std::string& dir_path);

// bool captureImage(const std_msgs::Empty::ConstPtr &msg);

gboolean process_image_sample(GstElement *sink, gpointer user_data); 



// 函数用于调用打印服务
bool callPrintService(const std::string& image_path)
{
    rtsp_image_capturer::PrintRequest srv;
    srv.request.file_path = image_path;

    // 等待服务可用
    if (!ros::service::waitForService("print_request", 10)) {
        ROS_ERROR("Service not available after waiting");
        return false;
    }

    // 调用服务
    bool result = ros::service::call("print_request", srv);
    if (result) {
        ROS_INFO("Print request successful.");
    } else {
        ROS_ERROR("Print request failed.");
    }
    return result;
}

// 函数用于图片上传云盘
bool call_upload_image_service(const std::string& image_path) {
    homi_speech_interface::UploadImage srv;
    srv.request.fileName = image_path;
        // 等待服务可用
    if (!ros::service::waitForService("upload_image_service")) {
        ROS_ERROR("upload_image_service not available after waiting");
        return false;
    }
    // 调用服务
    bool result = ros::service::call("upload_image_service", srv);
    printf("result: %s\n", result ? "true" : "false");
    if (result) {
        ROS_INFO("upload_image_service request successful.");
    } else {
        ROS_ERROR("upload_image_service request failed.");
    }
    return result;

}

bool call_get_image_url_service(const int& filesize) {
    homi_speech::UploadImageUrl srv;
    srv.request.fileSize = filesize;
        // 等待服务可用
    if (!ros::service::waitForService("/homi_speech/speech_upload_image_url_service")) {
        ROS_ERROR("speech_upload_image_url_service not available after waiting");
        return false;
    }
    if (ros::service::call("/homi_speech/speech_upload_image_url_service", srv)) {
        ROS_INFO("speech_upload_image_url_service request successful.");
        return true;
    } else {
        ROS_ERROR("speech_upload_image_url_service request failed.");
        return false;
    }
}

// 删除指定目录中的 photo_X.jpg 文件
void deletePhotoFilesInDirectory(const std::string& dir_path) {
    DIR *dir;
    struct dirent *entry;

    if ((dir = opendir(dir_path.c_str())) == nullptr) {
        std::cerr << "Failed to open directory: " << dir_path << std::endl;
        return;
    }

    while ((entry = readdir(dir)) != nullptr) {
        std::string filename = entry->d_name;
        if (filename.find(filename_prefix) == 0 && filename.size() >= 10 && filename.substr(filename.size() - 4) == ".jpg") {
            // 检查文件名是否以整数结尾
            std::string number_part = filename.substr(filename_prefix.length(), filename.size() - filename_prefix.length() - 4);
            try {
                std::stoi(number_part); // 尝试将字符串转换为整数
                std::string full_path = dir_path + filename;
                if (remove(full_path.c_str()) != 0) {
                    std::cerr << "Failed to remove file: " << full_path << std::endl;
                } else {
                    std::cout << "Deleted file: " << full_path << std::endl;
                }
            } catch (const std::invalid_argument& e) {
                // 忽略不是整数的部分
                continue;
            }
        }
    }

    closedir(dir);
}

gboolean on_bus_message(GstBus *bus, GstMessage *message, gpointer user_data) {
    GMainLoop *loop = (GMainLoop *)user_data;

    switch (GST_MESSAGE_TYPE(message)) {
        case GST_MESSAGE_EOS:
            g_print("End-of-stream reached.\n");
            g_main_loop_quit(loop);
            break;
        case GST_MESSAGE_ERROR: {
            gchar *debug;
            GError *error;
            gst_message_parse_error(message, &error, &debug);
            g_free(debug);
            g_printerr("Error received from element %s: %s\n", GST_OBJECT_NAME(message->src), error->message);
            g_error_free(error);
            g_main_loop_quit(loop);
            break;
        }
        default:
            break;
    }

    return TRUE; // Continue watching the bus
}
bool captureImage() {
    GstElement *pipeline, *source, *decoder, *convert, *sink;
    GstCaps *caps;
    GError *error = NULL;


    // 初始化GStreamer
    gst_init(NULL, NULL);

    // // 创建管道
    // pipeline = gst_pipeline_new("rtsp_pipeline");
    // if (!pipeline) {
    //     ROS_ERROR("Failed to create pipeline.");
    //     // goto cleanup;
    //     return false;
    // }

    // // 添加元素
    // source = gst_element_factory_make("rtspsrc", "source");
    // if (!source) {
    //     ROS_ERROR("Failed to create rtspsrc element.");
    //     // goto cleanup;
    //     return false;
    // }
    // g_object_set(G_OBJECT(source), "location", rtsp_url.c_str(), NULL);
    // decoder = gst_element_factory_make("decodebin", "decode");
    // if (!decoder) {
    //     ROS_ERROR("Failed to create decodebin element.");
    //     // goto cleanup;
    //     return false;
    // }
    // convert = gst_element_factory_make("videoconvert", "convert");
    // if (!convert) {
    //     ROS_ERROR("Failed to create videoconvert element.");
    //     // goto cleanup;
    //     return false;
    // }
    // caps = gst_caps_new_simple("video/x-raw", "format", G_TYPE_STRING, "RGB", NULL);


    pipeline = gst_parse_launch("rtspsrc location=rtsp://127.0.0.1:8554/test latency=10 ! decodebin ! videoconvert !capsfilter caps=video/x-raw,format=BGR ! appsink name=callSink", NULL);

    if(pipeline == NULL){

        ROS_ERROR("pares failed pipeline!\n");
        return false;
        
    }

    sink = gst_bin_get_by_name(GST_BIN(pipeline), "callSink");

    // g_object_set(sink, "emit-signals", TRUE, NULL);

    // 设置 appsink 的模式
    g_object_set(sink, "emit-signals", TRUE, "max-buffers", 1, "drop", TRUE, NULL);

    // sink = gst_element_factory_make("appsink", "sink");
    // if (!sink) {
    //     ROS_ERROR("Failed to create appsink element.");
    //     // goto cleanup;
    //     return false;
    // }

    
    // g_object_set(sink, "emit-signals", TRUE, "sync", FALSE, NULL);
    // g_signal_connect(sink, "new-sample", G_CALLBACK(process_image_sample), &save_path);
        // g_signal_connect(sink, "new-sample", G_CALLBACK(process_image_sample), NULL);
        g_signal_connect(sink, "new-sample", G_CALLBACK(process_image_sample), &stop_flag);


    // 开始播放
    if (!gst_element_set_state(pipeline, GST_STATE_PLAYING)) {
        ROS_ERROR("Unable to set the pipeline to the playing state.\n");
        
        return false;
    }

    // 等待一段时间以确保管道开始运行
    g_usleep(5000000);  // 等待5秒


    // GMainLoop *main_loop = g_main_loop_new(NULL, FALSE);
    // 配置bus处理错误
    GstBus *bus = gst_element_get_bus(pipeline);
    // if (!gst_bus_add_watch(bus, on_bus_message, main_loop)) {
    //     ROS_ERROR("Failed to add bus watch.");
    //     // goto cleanup;
    //     return false;
    // }

  
    // 检查是否因为捕获到一帧而停止  
    if (stop_flag) {  
        ROS_INFO("Captured one frame, stopping pipeline...\n");  
    } 


    gst_object_unref(bus);

    // g_main_loop_run(main_loop);

// cleanup:

    gst_element_set_state(pipeline, GST_STATE_NULL);
    gst_object_unref(pipeline);

    // g_main_loop_unref(main_loop);

    count =0;
    return true;
}

// 添加水印的函数
cv::Mat add_watermark(const cv::Mat& original_image, const std::string& watermark_text) {
    if (original_image.empty()) { // 检查输入是否为空
        throw std::invalid_argument("The input image is empty.");
    }

    // 定义常量提高代码可读性
    const int FONT_FACE = cv::FONT_HERSHEY_SIMPLEX;
    const double SCALE = 0.7;
    const cv::Scalar COLOR(255, 255, 255);
    const int THICKNESS = 2;
    const int LINE_TYPE = cv::LINE_AA;

    cv::Mat watermarked_image = original_image.clone(); // 复制原始图像
    if (watermarked_image.empty()) { // 检查克隆是否成功
        throw std::runtime_error("Failed to clone the original image.");
    }

    cv::putText(watermarked_image, watermark_text, cv::Point(10, 50), // 文本位置
                FONT_FACE, SCALE, // 字体
                COLOR, THICKNESS, // 颜色和厚度
                LINE_TYPE); // 抗锯齿
    return watermarked_image;
}


bool checkImageValidity(const cv::Mat& image) {
    // 检查图像矩阵是否为空
    if (image.empty()) {
        ROS_ERROR("Image data is empty.");
        return false;
    }

    // 检查图像的数据类型
    int type = image.type();
    if (type != CV_8UC1 && type != CV_8UC3) {
        ROS_ERROR("Unsupported image type: %d", type);
        return false;
    }

    // 检查图像的通道数
    int channels = image.channels();
    if (channels != 1 && channels != 3) {
        ROS_ERROR("Unsupported number of channels: %d", channels);
        return false;
    }

    // 检查图像的尺寸
    int rows = image.rows;
    int cols = image.cols;
    if (rows <= 0 || cols <= 0) {
        ROS_ERROR("Invalid image dimensions: %dx%d", rows, cols);
        return false;
    }

    // 检查图像的数据指针是否为空
    void* imageData = image.data;
    if (imageData == nullptr) {
        ROS_ERROR("Image data pointer is null.");
        return false;
    }

    // 打印详细的错误信息
    // ROS_INFO("Image data size: %zu", image.total() * image.elemSize());
    // ROS_INFO("Image type: %d", image.type());
    // ROS_INFO("Image channels: %d", image.channels());
    // ROS_INFO("Image depth: %d", image.depth());

    return true;
}

bool createDirectoryIfNotExists(const std::string& dir_path) {
    struct stat info;

    // 检查目录是否存在
    if (stat(dir_path.c_str(), &info) != 0) {
        // 目录不存在，尝试创建
        // mode_t mode = S_IRWXU | S_IRGRP | S_IXGRP | S_IROTH | S_IXOTH; // 设置权限
        mode_t mode = 0755; // 设置权限
        if (mkdir(dir_path.c_str(), mode) != 0) {
            ROS_ERROR("Failed to create directory:%s", dir_path);
            return false;
        }
    } else if (!S_ISDIR(info.st_mode)) {
        ROS_ERROR("Path exists but is not a directory:%s", dir_path);
        return false;
    }

    return true;
}




gboolean process_image_sample(GstElement *sink, gpointer user_data) {
    GstSample *sample = nullptr;
    GstBuffer *buffer = nullptr;
    GstMapInfo map;
    cv::Mat image;
    // std::string save_path = *(static_cast<std::string*>(user_data));
    // std::string save_dir = "/tmp/directory/";

    std::string watermark_text = "Watermark Text"; // 定义水印文本

    // 拉取样本
    sample = gst_app_sink_pull_sample(GST_APP_SINK(sink));
    if (sample == nullptr) {
        ROS_ERROR("Failed to pull sample from sink.");
        return false;
    }

    // 获取缓冲区
    buffer = gst_sample_get_buffer(sample);
    if (buffer == nullptr) {
        ROS_ERROR("Failed to get buffer from sample.");
        gst_sample_unref(sample);
        return false;
    }

    // 映射缓冲区
    if (!gst_buffer_map(buffer, &map, GST_MAP_READ)) {
        ROS_ERROR("Failed to map buffer.");
        gst_sample_unref(sample);
        return false;
    }


    // 从 GstCaps 中获取宽度和高度
    GstCaps *caps = gst_sample_get_caps(sample);
    GstStructure *structure = gst_caps_get_structure(caps, 0);
    int width, height;
    if (!gst_structure_get_int(structure, "width", &width) || !gst_structure_get_int(structure, "height", &height)) {
        ROS_ERROR("Failed to get width or height from structure.");
        gst_structure_free(structure);
        gst_caps_unref(caps);
        gst_buffer_unmap(buffer, &map);
        gst_sample_unref(sample);
        return false;
    }


    // 创建cv::Mat对象
    // int height = 720; // 示例值
    // int width = 1280;  // 示例值
    guint stride = width *1; //对于RGB24数据，每个像素3字节
    image = cv::Mat(height, width, CV_8UC3, map.data, stride);
    cv::Mat copied_image;
    image.copyTo(copied_image);

    // // 解映射缓冲区
    // gst_buffer_unmap(buffer, &map);

    // // 释放样本
    // gst_sample_unref(sample);


        //创建文件夹（如果不存在）
        // std::string save_dir = "/tmp/directory/";
    if (!createDirectoryIfNotExists(save_dir)) {
        ROS_ERROR ("Failed to create directory: %s ", save_dir.c_str() );
            return false;
        }

    // // 删除目录中的 photo_X.jpg 文件
    // deletePhotoFilesInDirectory(save_dir);


    // 添加水印
    try{

        cv::Mat watermarked_image = add_watermark(copied_image, watermark_text);


        if (!checkImageValidity(watermarked_image)) 
        {
            ROS_ERROR("watermarked_image validation failed.");
            return false;
            }  


        // 保存图像
        std::string save_path = save_dir + "photo_" + std::to_string(count++) + ".jpg";
        // if (!save_path.empty()) 
        if (!save_path.empty() && count <= 1) 
        { 
            // bool success = cv::imwrite(save_path, watermarked_image);
            bool success = cv::imwrite(save_path, copied_image);//照片去掉水印
            if (!success) {
                ROS_ERROR("Failed to save image to path: %s", save_path.c_str());
            } else {
                ROS_INFO("Image with watermark saved at: %s", save_path.c_str());
                int filesize = std::filesystem::file_size(save_path.c_str());
                // callPrintService(save_path.c_str());
                call_get_image_url_service(filesize);
                call_upload_image_service(save_path.c_str());
                
                // 播放音频
                std::string filePath = "/home/<USER>/resource/audio/upload_cloud.wav";
                std::string command = "aplay -D plughw:4,0 \"" + filePath + "\"";
                int result = std::system(command.c_str());

                // 调用iot控制关闭氛围灯
                ros::service::waitForService("/homi_speech/speech_iot_control_service");    
                homi_speech_interface::IotControl srv1;
                srv1.request.param = "{\"outletStatus\":0}";
                bool response1 = ros::service::call("/homi_speech/speech_iot_control_service", srv1); 
                
                gboolean *stop_flag = (gboolean *)user_data;  
                *stop_flag = true;

                // if (!gst_element_set_state(pipeline, GST_STATE_NULL)) {
                //     ROS_ERROR("Unable to set the pipeline to NULL state.\n");
                    
                //     return false;
                // }

                gst_buffer_unmap(buffer, &map);
                gst_sample_unref(sample);
                return false;
            }

        } else {
                ROS_ERROR("Save path is empty or invalid.");
            }

    }
    catch (cv_bridge::Exception& e) {
        ROS_ERROR("cv_bridge exception: %s", e.what());
        return false;
        }


    // 解映射缓冲区
    gst_buffer_unmap(buffer, &map);

    // 释放样本
    gst_sample_unref(sample);

    return true;

 }
void takePhotoCallback(const std_msgs::String::ConstPtr& msg) {

    if (msg->data == "take_photo"){

        // 删除目录中的 photo_X.jpg 文件
        deletePhotoFilesInDirectory(save_dir);
        sleep(2);
        if(captureImage())
        {
            ROS_INFO_STREAM("Photo captured and saved.");
        } else {
            ROS_WARN_STREAM("Failed to capture photo.");
        }
    }
}

int main(int argc, char **argv) {
    ros::init(argc, argv, "sub_rtsp_image_capturer");
    ros::NodeHandle nh;
    ros::Subscriber sub = nh.subscribe("/take_photo", 10, takePhotoCallback);

    ros::spin();

    return 0;
}