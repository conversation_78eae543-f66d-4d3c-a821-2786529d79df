#!/usr/bin/env python3

import rospy
from print_image_node.srv import PrintRequest

def print_client(file_path):
    rospy.wait_for_service('print_request')
    try:
        print_request = rospy.ServiceProxy('print_request', PrintRequest)
        resp = print_request(file_path)
        return resp.success, resp.message
    except rospy.ServiceException as e:
        print("Service call failed: %s"%e)

def main():
    rospy.init_node('print_client')
    file_path = "/opt/ros/test.png"
    success, message = print_client(file_path)
    print(f"Print job result: {success}, Message: {message}")

if __name__ == "__main__":
    main()