#!/usr/bin/env python3

import rospy
from rtsp_image_capturer.srv import PrintRequest, PrintRequestResponse
import cups
import os

def handle_print_request(req):
    # 连接到CUPS服务
    conn = cups.Connection()

    # 获取所有打印机的信息
    printers = conn.getPrinters()

    # 如果找到了打印机
    if printers:
        # 选择第一个打印机作为默认打印机
        printer_name = list(printers.keys())[0]

        # 检查文件是否存在
        if os.path.isfile(req.file_path):
            # 发送文件到打印机
            conn.printFile(printer_name, req.file_path, "Python Print Job", {})
            return PrintRequestResponse(True, "文件已成功发送到打印机。")
        else:
            return PrintRequestResponse(False, "文件未找到，请检查文件路径是否正确。")
    else:
        return PrintRequestResponse(False, "未找到任何打印机，请确保打印机已正确安装并连接。")

def print_server():
    rospy.init_node('print_server')
    s = rospy.Service('print_request', PrintRequest, handle_print_request)
    print("Ready to receive print requests.")
    rospy.spin()

if __name__ == "__main__":
    print_server()