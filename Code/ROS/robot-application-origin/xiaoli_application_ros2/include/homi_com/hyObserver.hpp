#pragma once

#include <vector>
#include <algorithm>
#include <iostream>

namespace base
{
    using namespace std;

    // HyBaseObserver Interface
    class HyBaseObserver {
    public:
        virtual ~HyBaseObserver() = default;
        virtual void update(int state) = 0;
    };

    // HyBaseObserverMgr (Abstract class)
    class HyBaseObserverMgr {
    public:
        virtual ~HyBaseObserverMgr() = default;
        virtual void registerObserver(HyBaseObserver* observer) = 0;
        virtual void unRegisterObserver(HyBaseObserver* observer) = 0;
        virtual void notify() = 0;
    };


    /*!
     * \class HyHomiRotObserverMgr
     * \brief  观察者管理基类
     * \author GYJ 
     */
    class HyHomiRotObserverMgr  : public HyBaseObserverMgr {
    {
        typedef vector<const HyBaseObserver*> _Vty;
    private:
       _Vty m_vtObserver;

    protected:
        virtual ~HyBaseObserverMgr() {}

    public:
        void registerObserver(const HyBaseObserver* pObserver){
            _Vty::iterator it = m_vtObserver.begin();
            for (; it != m_vtObserver.end(); it++){
                if (*it == pObserver){
                    return;
                }
            }
            m_vtObserver.push_back(pObserver);
        }

        void unRegisterObserver(const HyBaseObserver* pObserver){
            _Vty::iterator it = m_vtObserver.begin();
            for (; it != m_vtObserver.end(); it++){
                if (*it == pObserver){
                    m_vtObserver.erase(it);
                    return;
                }
            }
        }

        virtual void notify(){
            _Vty::iterator it = m_vtObserver.begin();
            for (; it != m_vtObserver.end(); it++) {
                const_cast<HyBaseObserver*>(*it)->update();
            }
        }
    };

  
    /*!
     * \class HyHomiBotObserver
     * \brief  观察者管理基类
     * \author GYJ 
     */
    class HyHomiBotObserver : public HyBaseObserver {
    {
    public:
        virtual ~HyHomiBotObserver() {}
        virtual void HyHomiBotObserver() {}

        virtual void update() {
            std::cout << "HyHomiBotObserver update" << std::endl;
        }

    };


}

