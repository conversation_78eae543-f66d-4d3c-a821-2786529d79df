/*
  *Copyright(C),GYJ
  *FileName:  singleton.hpp
  *Function:  单例类封装
  *Date:      2024-11-13
*/
#pragma once
#include <memory>
#include <mutex>

namespace base
{
	template <class T>
	class singleton{
	public:
		static T& getInstance(){
			if (m_Instance.get() == nullptr){
				std::lock_guard<std::mutex> lock(m_mutex);
				//上锁，防止多线程同时访问
				if (m_Instance.get() == nullptr){
					m_Instance.reset(new T);
				}
			}
			return *m_Instance.get();
		}

		static void unInstance(){
			try {
				std::lock_guard<std::mutex> lock(m_mutex);
				m_Instance.release();
			}
			catch (const std::logic_error&){
			}
		}

	protected:
		singleton() {}
		~singleton() {}

	private:
		//禁用拷贝	
		singleton(const singleton&) {};
		//禁用赋值
		singleton& operator=(const singleton&) {};

		static std::unique_ptr<T> m_Instance;
		static std::mutex m_mutex;
	};

	template <class T>
	std::unique_ptr<T> singleton<T>::m_Instance = nullptr;

	template <class T>
	std::mutex singleton<T>::m_mutex;
}