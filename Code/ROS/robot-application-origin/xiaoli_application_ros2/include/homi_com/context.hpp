/*
  *Copyright(C),GYJ
  *FileName:  context.hpp
  *Function:  先进先出消息队列类，回调函数NotifyContextMsgCallback里可以PopContext，有未处理的消息可以在处理
  *Date:      2024-12-03
*/
#pragma once
#include <list>
#include <mutex>
#include <thread>
#include <chrono>
#include <optional>

typedef void(*NotifyContextMsgCallback)(void*);

namespace base {
    template<typename T>
    class Context {
    public:
        Context() {};
        ~Context() {};

        inline void pushContext(const T& t, bool bPriority = false) {
            std::lock_guard<std::mutex> lock(m_mutex);
            bPriority ? m_lstContext.push_front(t) : m_lstContext.push_back(t);
        }

        inline std::optional<T> popContext() {
            std::lock_guard<std::mutex> lock(m_mutex);
            if (m_lstContext.empty()) {
                return std::nullopt;
            }
            T t = m_lstContext.front();
            m_lstContext.pop_front();
            return t;
        }

        inline int getCount() {
            std::lock_guard<std::mutex> lock(m_mutex);
            return m_lstContext.size();
        }

        inline void clearContext() {
            std::lock_guard<std::mutex> lock(m_mutex);
            m_lstContext.clear();
        }

        inline void setNotifyMsgCallback(NotifyContextMsgCallback pMsgCallback, void* pHandle, int nTimer) {
            m_MsgCallback = pMsgCallback;
            m_pHadle = pHandle;
            int nSleepTimer = nTimer < 5 ? 5 : nTimer;

            std::thread th([&, nSleepTimer]() {
                do {
                    {
                        std::lock_guard<std::mutex> lock(m_mutex);
                        if (m_lstContext.empty()) {
                            std::this_thread::sleep_for(std::chrono::milliseconds(100));
                            continue;
                        }
                    }
                    std::this_thread::sleep_for(std::chrono::milliseconds(nSleepTimer));
                    if (m_MsgCallback && m_pHadle) {
                        m_MsgCallback(m_pHadle);
                    }
                } while (true);
            });
            th.detach();
        }

    private:
        std::list<T> m_lstContext;
        std::mutex m_mutex;
        void* m_pHadle = nullptr;
        NotifyContextMsgCallback m_MsgCallback;
    };
}