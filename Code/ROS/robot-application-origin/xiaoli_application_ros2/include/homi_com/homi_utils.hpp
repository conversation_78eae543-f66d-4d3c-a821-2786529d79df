#include <chrono>
#include <iostream>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <unistd.h>
#include <string>

namespace base {
    class homiUtils{
    public:
        /*获取当前的时间戳 单位s*/
        static unsigned long getCurrentTimeStamp() {
            auto now = std::chrono::system_clock::now();
            auto epoch = now.time_since_epoch();
            return std::chrono::duration_cast<std::chrono::seconds>(epoch).count();
        }

        static void killProcessByName(const std::string& processName) {
            std::string command = "ps aux | grep " + processName + " | grep -v grep | awk '{print $2}'";
            FILE* fp = popen(command.c_str(), "r");
            if (fp == nullptr) {
                perror("popen failed");
                return;
            }
            char buffer[256];
            while (fgets(buffer, sizeof(buffer), fp)) {
                pid_t pid = atoi(buffer);  
                if (pid > 0) {
                    std::cout << "Killing process with PID: " << pid << std::endl;
                    kill(pid, SIGKILL); 
                }
            }
            fclose(fp);
        }
    };
}
 