/*
 * Copyright(c) 2006 to 2020 ZettaScale Technology and others
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0, or the Eclipse Distribution License
 * v. 1.0 which is available at
 * http://www.eclipse.org/org/documents/edl-v10.php.
 *
 * SPDX-License-Identifier: EPL-2.0 OR BSD-3-Clause
 */
#ifndef CYCLONEDDS_DDS_CORE_DETAIL_MODULE_DOCS_HPP_
#define CYCLONEDDS_DDS_CORE_DETAIL_MODULE_DOCS_HPP_

// Implementation

/**
 * @file
 * This file exists only to be fed to doxygen. There is quite
 * literally nothing else to see here.
 */

/**
 * @addtogroup isocpp2_dcps ISO/IEC C++ 2 API for DDS Data-centric Publish-Subscribe (DCPS)
 */
/** @{*/
/** @dir dds */
/** @}*/

/**
 * @addtogroup isocpp2_dcps_domain Domain Module - ::dds::domain
 * @ingroup isocpp2_dcps */
/** @{*/
/** @dir dds/domain */
/** @}*/


/**
 * @addtogroup isocpp2_dcps_topic Topic Module- ::dds::topic
 * @ingroup isocpp2_dcps */
/** @{*/
/** @dir dds/topic */
/** @}*/

/**
 * @addtogroup isocpp2_dcps_pub Publication Module - ::dds::pub
 * @ingroup isocpp2_dcps */
/** @{*/
/** @dir dds/pub */
/** @}*/


/**
 * @addtogroup isocpp2_dcps_sub Subscription Module - ::dds::sub
 * @ingroup isocpp2_dcps */
/** @{*/
/** @dir dds/sub */
/** @}*/

/**
 * @addtogroup isocpp2_dcps_core Core / Common Module - ::dds::core
 * @ingroup isocpp2_dcps */
/** @{*/
/** @dir dds/core */
/** @}*/


/**
 * @addtogroup examplesdcpsisocpp ISO/IEC C++ DCPS PSM Examples
 */
/** @{*/
/** @dir examples/common */
/** @}*/



/**
 * @ingroup isocpp2_dcps
 */
namespace dds
{
/**
 * @ingroup isocpp2_dcps_domain
 * @ingroup isocpp2_dcps */
namespace domain
{
/** @ingroup isocpp2_dcps_domain
 * @ingroup isocpp2_dcps */
namespace qos
{
namespace detail {}
}
namespace detail {}
}
/**
 * @ingroup isocpp2_dcps_topic
 * @ingroup isocpp2_dcps */
namespace topic
{
/** @ingroup isocpp2_dcps_topic
 * @ingroup isocpp2_dcps */
namespace qos
{
namespace detail {}
}
namespace detail {}
}
/**
 * @ingroup isocpp2_dcps_pub
 * @ingroup isocpp2_dcps */
namespace pub
{
/** @ingroup isocpp2_dcps_pub
 * @ingroup isocpp2_dcps */
namespace qos
{
namespace detail {}
}
namespace detail {}
}
/**
 * @ingroup isocpp2_dcps_sub
 * @ingroup isocpp2_dcps */
namespace sub
{
/** @ingroup isocpp2_dcps_sub
 * @ingroup isocpp2_dcps */
namespace cond
{
namespace detail {}
}
/** @ingroup isocpp2_dcps_sub
 * @ingroup isocpp2_dcps */
namespace functors
{
namespace detail {}
}
/** @ingroup isocpp2_dcps_sub
 * @ingroup isocpp2_dcps */
namespace status {}
/** @ingroup isocpp2_dcps_sub
 * @ingroup isocpp2_dcps */
namespace qos
{
namespace detail {}
}
}
/**
 * @ingroup isocpp2_dcps_core
 * @ingroup isocpp2_dcps */
namespace core
{
/** @ingroup isocpp2_dcps_core
 * @ingroup isocpp2_dcps */
namespace policy
{
namespace detail {}
}
/** @ingroup isocpp2_dcps_core
 * @ingroup isocpp2_dcps */
namespace cond
{
namespace detail {}
}
/** @ingroup isocpp2_dcps_core
 * @ingroup isocpp2_dcps */
namespace status
{
namespace detail {}
}
namespace detail {}
}
}

// End of implementation

#endif /* CYCLONEDDS_DDS_CORE_DETAIL_MODULE_DOCS_HPP_ */
