#ifndef __OMG_DDS_DDSCXX_FEATURES_HPP__
#define __OMG_DDS_DDSCXX_FEATURES_HPP__

/* Whether or not support for shared memory is included */
/* #undef DDSCXX_HAS_SHM */

/* Whether or not support for type discovery is included */
#define DDSCXX_HAS_TYPE_DISCOVERY 1

/* Whether or not support for topic discovery is included */
#define DDSCXX_HAS_TOPIC_DISCOVERY 1

/* Whether to use boost for c++11 compatibility or not */
/* #undef DDSCXX_USE_BOOST */

#endif /* __OMG_DDS_DDSCXX_FEATURES_HPP__ */
