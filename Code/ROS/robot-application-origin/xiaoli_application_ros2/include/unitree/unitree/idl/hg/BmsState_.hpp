/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: BmsState_.idl
  Source: BmsState_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_HG_BMSSTATE__HPP
#define DDSCXX_UNITREE_IDL_HG_BMSSTATE__HPP

#include <cstdint>
#include <array>

namespace unitree_hg
{
namespace msg
{
namespace dds_
{
class BmsState_
{
private:
 uint8_t version_high_ = 0;
 uint8_t version_low_ = 0;
 uint8_t fn_ = 0;
 std::array<uint16_t, 40> cell_vol_ = { };
 std::array<uint32_t, 3> bmsvoltage_ = { };
 int32_t current_ = 0;
 uint8_t soc_ = 0;
 uint8_t soh_ = 0;
 std::array<int16_t, 12> temperature_ = { };
 uint16_t cycle_ = 0;
 uint16_t manufacturer_date_ = 0;
 std::array<uint32_t, 5> bmsstate_ = { };
 std::array<uint32_t, 3> reserve_ = { };

public:
  BmsState_() = default;

  explicit BmsState_(
    uint8_t version_high,
    uint8_t version_low,
    uint8_t fn,
    const std::array<uint16_t, 40>& cell_vol,
    const std::array<uint32_t, 3>& bmsvoltage,
    int32_t current,
    uint8_t soc,
    uint8_t soh,
    const std::array<int16_t, 12>& temperature,
    uint16_t cycle,
    uint16_t manufacturer_date,
    const std::array<uint32_t, 5>& bmsstate,
    const std::array<uint32_t, 3>& reserve) :
    version_high_(version_high),
    version_low_(version_low),
    fn_(fn),
    cell_vol_(cell_vol),
    bmsvoltage_(bmsvoltage),
    current_(current),
    soc_(soc),
    soh_(soh),
    temperature_(temperature),
    cycle_(cycle),
    manufacturer_date_(manufacturer_date),
    bmsstate_(bmsstate),
    reserve_(reserve) { }

  uint8_t version_high() const { return this->version_high_; }
  uint8_t& version_high() { return this->version_high_; }
  void version_high(uint8_t _val_) { this->version_high_ = _val_; }
  uint8_t version_low() const { return this->version_low_; }
  uint8_t& version_low() { return this->version_low_; }
  void version_low(uint8_t _val_) { this->version_low_ = _val_; }
  uint8_t fn() const { return this->fn_; }
  uint8_t& fn() { return this->fn_; }
  void fn(uint8_t _val_) { this->fn_ = _val_; }
  const std::array<uint16_t, 40>& cell_vol() const { return this->cell_vol_; }
  std::array<uint16_t, 40>& cell_vol() { return this->cell_vol_; }
  void cell_vol(const std::array<uint16_t, 40>& _val_) { this->cell_vol_ = _val_; }
  void cell_vol(std::array<uint16_t, 40>&& _val_) { this->cell_vol_ = _val_; }
  const std::array<uint32_t, 3>& bmsvoltage() const { return this->bmsvoltage_; }
  std::array<uint32_t, 3>& bmsvoltage() { return this->bmsvoltage_; }
  void bmsvoltage(const std::array<uint32_t, 3>& _val_) { this->bmsvoltage_ = _val_; }
  void bmsvoltage(std::array<uint32_t, 3>&& _val_) { this->bmsvoltage_ = _val_; }
  int32_t current() const { return this->current_; }
  int32_t& current() { return this->current_; }
  void current(int32_t _val_) { this->current_ = _val_; }
  uint8_t soc() const { return this->soc_; }
  uint8_t& soc() { return this->soc_; }
  void soc(uint8_t _val_) { this->soc_ = _val_; }
  uint8_t soh() const { return this->soh_; }
  uint8_t& soh() { return this->soh_; }
  void soh(uint8_t _val_) { this->soh_ = _val_; }
  const std::array<int16_t, 12>& temperature() const { return this->temperature_; }
  std::array<int16_t, 12>& temperature() { return this->temperature_; }
  void temperature(const std::array<int16_t, 12>& _val_) { this->temperature_ = _val_; }
  void temperature(std::array<int16_t, 12>&& _val_) { this->temperature_ = _val_; }
  uint16_t cycle() const { return this->cycle_; }
  uint16_t& cycle() { return this->cycle_; }
  void cycle(uint16_t _val_) { this->cycle_ = _val_; }
  uint16_t manufacturer_date() const { return this->manufacturer_date_; }
  uint16_t& manufacturer_date() { return this->manufacturer_date_; }
  void manufacturer_date(uint16_t _val_) { this->manufacturer_date_ = _val_; }
  const std::array<uint32_t, 5>& bmsstate() const { return this->bmsstate_; }
  std::array<uint32_t, 5>& bmsstate() { return this->bmsstate_; }
  void bmsstate(const std::array<uint32_t, 5>& _val_) { this->bmsstate_ = _val_; }
  void bmsstate(std::array<uint32_t, 5>&& _val_) { this->bmsstate_ = _val_; }
  const std::array<uint32_t, 3>& reserve() const { return this->reserve_; }
  std::array<uint32_t, 3>& reserve() { return this->reserve_; }
  void reserve(const std::array<uint32_t, 3>& _val_) { this->reserve_ = _val_; }
  void reserve(std::array<uint32_t, 3>&& _val_) { this->reserve_ = _val_; }

  bool operator==(const BmsState_& _other) const
  {
    (void) _other;
    return version_high_ == _other.version_high_ &&
      version_low_ == _other.version_low_ &&
      fn_ == _other.fn_ &&
      cell_vol_ == _other.cell_vol_ &&
      bmsvoltage_ == _other.bmsvoltage_ &&
      current_ == _other.current_ &&
      soc_ == _other.soc_ &&
      soh_ == _other.soh_ &&
      temperature_ == _other.temperature_ &&
      cycle_ == _other.cycle_ &&
      manufacturer_date_ == _other.manufacturer_date_ &&
      bmsstate_ == _other.bmsstate_ &&
      reserve_ == _other.reserve_;
  }

  bool operator!=(const BmsState_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_hg::msg::dds_::BmsState_>::getTypeName()
{
  return "unitree_hg::msg::dds_::BmsState_";
}

template <> constexpr bool TopicTraits<::unitree_hg::msg::dds_::BmsState_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_hg::msg::dds_::BmsState_>::type_map_blob_sz() { return 874; }
template<> constexpr unsigned int TopicTraits<::unitree_hg::msg::dds_::BmsState_>::type_info_blob_sz() { return 100; }
template<> inline const uint8_t * TopicTraits<::unitree_hg::msg::dds_::BmsState_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x36,  0x01,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf1,  0x4b,  0x98,  0xd8,  0x09,  0xa6,  0x3f,  0x63, 
 0xc7,  0xcb,  0x31,  0xa8,  0x18,  0x50,  0x6b,  0x00,  0x1e,  0x01,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x0e,  0x01,  0x00,  0x00,  0x0d,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x75,  0x6a,  0xda,  0x84,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x51,  0x99,  0x89,  0x90,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x4d,  0x9d,  0x6c,  0x17,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x28,  0x06,  0x2d,  0x87,  0xc3,  0x55,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x07,  0x46,  0x32,  0x3c,  0x06,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x04,  0x43,  0xb5,  0xc9,  0x17,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0xc7,  0xd5,  0xf8,  0x49,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x17,  0x39,  0xe7,  0x01,  0x00,  0x16,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0c,  0x03,  0xee,  0xf4, 
 0x38,  0xf7,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x06,  0x9a, 
 0x4c,  0x07,  0x40,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x06,  0xe9, 
 0x68,  0x5d,  0x46,  0x00,  0x16,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x05,  0x07,  0x35,  0x33,  0xbf,  0xfb,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x03,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0x00,  0x00,  0x02,  0x02,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0xf2,  0x0c,  0xdb,  0xa6,  0x86,  0x24,  0x98,  0xf5,  0x54,  0xba,  0x92,  0x7b, 
 0xa1,  0x8d,  0xe0,  0x00,  0xea,  0x01,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f, 
 0x68,  0x67,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x42, 
 0x6d,  0x73,  0x53,  0x74,  0x61,  0x74,  0x65,  0x5f,  0x00,  0x00,  0x00,  0x00,  0xb2,  0x01,  0x00,  0x00, 
 0x0d,  0x00,  0x00,  0x00,  0x1b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00, 
 0x0d,  0x00,  0x00,  0x00,  0x76,  0x65,  0x72,  0x73,  0x69,  0x6f,  0x6e,  0x5f,  0x68,  0x69,  0x67,  0x68, 
 0x00,  0x00,  0x00,  0x00,  0x1a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x76,  0x65,  0x72,  0x73,  0x69,  0x6f,  0x6e,  0x5f,  0x6c,  0x6f,  0x77,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x66,  0x6e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x28,  0x06,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x63,  0x65,  0x6c,  0x6c,  0x5f,  0x76,  0x6f,  0x6c, 
 0x00,  0x00,  0x00,  0x00,  0x25,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x07,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x62,  0x6d,  0x73,  0x76,  0x6f,  0x6c,  0x74,  0x61,  0x67,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x04,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x63,  0x75,  0x72,  0x72,  0x65,  0x6e,  0x74,  0x00,  0x00,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x04,  0x00,  0x00,  0x00,  0x73,  0x6f,  0x63,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x73,  0x6f,  0x68,  0x00,  0x00,  0x00,  0x00,  0x00,  0x26,  0x00,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x0c,  0x03,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x74,  0x65,  0x6d,  0x70,  0x65,  0x72,  0x61,  0x74, 
 0x75,  0x72,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x06,  0x00,  0x06,  0x00,  0x00,  0x00,  0x63,  0x79,  0x63,  0x6c,  0x65,  0x00,  0x00,  0x00, 
 0x20,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x06,  0x00,  0x12,  0x00,  0x00,  0x00, 
 0x6d,  0x61,  0x6e,  0x75,  0x66,  0x61,  0x63,  0x74,  0x75,  0x72,  0x65,  0x72,  0x5f,  0x64,  0x61,  0x74, 
 0x65,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x05,  0x07,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x62,  0x6d,  0x73,  0x73,  0x74,  0x61,  0x74,  0x65,  0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x07,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0x0c,  0xdb,  0xa6, 
 0x86,  0x24,  0x98,  0xf5,  0x54,  0xba,  0x92,  0x7b,  0xa1,  0x8d,  0xe0,  0xf1,  0x4b,  0x98,  0xd8,  0x09, 
 0xa6,  0x3f,  0x63,  0xc7,  0xcb,  0x31,  0xa8,  0x18,  0x50,  0x6b, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_hg::msg::dds_::BmsState_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x60,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x4b,  0x98,  0xd8,  0x09,  0xa6,  0x3f,  0x63,  0xc7,  0xcb,  0x31,  0xa8, 
 0x18,  0x50,  0x6b,  0x00,  0x22,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0x0c,  0xdb,  0xa6,  0x86,  0x24,  0x98,  0xf5,  0x54,  0xba,  0x92,  0x7b, 
 0xa1,  0x8d,  0xe0,  0x00,  0xee,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_hg::msg::dds_::BmsState_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_hg::msg::dds_::BmsState_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_hg::msg::dds_::BmsState_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_hg::msg::dds_::BmsState_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_hg::msg::dds_::BmsState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.version_high()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.version_low()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.fn()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.cell_vol()[0], instance.cell_vol().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.bmsvoltage()[0], instance.bmsvoltage().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.current()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.soc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.soh()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.temperature()[0], instance.temperature().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.cycle()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.manufacturer_date()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.bmsstate()[0], instance.bmsstate().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_hg::msg::dds_::BmsState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::BmsState_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_hg::msg::dds_::BmsState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.version_high()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.version_low()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.fn()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.cell_vol()[0], instance.cell_vol().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.bmsvoltage()[0], instance.bmsvoltage().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.current()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.soc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.soh()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.temperature()[0], instance.temperature().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.cycle()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.manufacturer_date()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.bmsstate()[0], instance.bmsstate().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_hg::msg::dds_::BmsState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::BmsState_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_hg::msg::dds_::BmsState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.version_high()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.version_low()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.fn()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.cell_vol()[0], instance.cell_vol().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.bmsvoltage()[0], instance.bmsvoltage().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.current()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.soc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.soh()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.temperature()[0], instance.temperature().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.cycle()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.manufacturer_date()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.bmsstate()[0], instance.bmsstate().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_hg::msg::dds_::BmsState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::BmsState_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_hg::msg::dds_::BmsState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.version_high()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.version_low()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.fn()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.cell_vol()[0], instance.cell_vol().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.bmsvoltage()[0], instance.bmsvoltage().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.current()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.soc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.soh()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.temperature()[0], instance.temperature().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.cycle()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.manufacturer_date()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.bmsstate()[0], instance.bmsstate().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_hg::msg::dds_::BmsState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::BmsState_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_HG_BMSSTATE__HPP
