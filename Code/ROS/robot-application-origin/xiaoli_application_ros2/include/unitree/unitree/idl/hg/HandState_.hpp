/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: HandState_.idl
  Source: HandState_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_HG_HANDSTATE__HPP
#define DDSCXX_UNITREE_IDL_HG_HANDSTATE__HPP

#include "unitree/idl/hg/IMUState_.hpp"

#include "unitree/idl/hg/MotorState_.hpp"

#include "unitree/idl/hg/PressSensorState_.hpp"

#include <cstdint>
#include <array>
#include <vector>

namespace unitree_hg
{
namespace msg
{
namespace dds_
{
class HandState_
{
private:
 std::vector<::unitree_hg::msg::dds_::MotorState_> motor_state_;
 std::vector<::unitree_hg::msg::dds_::PressSensorState_> press_sensor_state_;
 ::unitree_hg::msg::dds_::IMUState_ imu_state_;
 float power_v_ = 0.0f;
 float power_a_ = 0.0f;
 float system_v_ = 0.0f;
 float device_v_ = 0.0f;
 std::array<uint32_t, 2> error_ = { };
 std::array<uint32_t, 2> reserve_ = { };

public:
  HandState_() = default;

  explicit HandState_(
    const std::vector<::unitree_hg::msg::dds_::MotorState_>& motor_state,
    const std::vector<::unitree_hg::msg::dds_::PressSensorState_>& press_sensor_state,
    const ::unitree_hg::msg::dds_::IMUState_& imu_state,
    float power_v,
    float power_a,
    float system_v,
    float device_v,
    const std::array<uint32_t, 2>& error,
    const std::array<uint32_t, 2>& reserve) :
    motor_state_(motor_state),
    press_sensor_state_(press_sensor_state),
    imu_state_(imu_state),
    power_v_(power_v),
    power_a_(power_a),
    system_v_(system_v),
    device_v_(device_v),
    error_(error),
    reserve_(reserve) { }

  const std::vector<::unitree_hg::msg::dds_::MotorState_>& motor_state() const { return this->motor_state_; }
  std::vector<::unitree_hg::msg::dds_::MotorState_>& motor_state() { return this->motor_state_; }
  void motor_state(const std::vector<::unitree_hg::msg::dds_::MotorState_>& _val_) { this->motor_state_ = _val_; }
  void motor_state(std::vector<::unitree_hg::msg::dds_::MotorState_>&& _val_) { this->motor_state_ = _val_; }
  const std::vector<::unitree_hg::msg::dds_::PressSensorState_>& press_sensor_state() const { return this->press_sensor_state_; }
  std::vector<::unitree_hg::msg::dds_::PressSensorState_>& press_sensor_state() { return this->press_sensor_state_; }
  void press_sensor_state(const std::vector<::unitree_hg::msg::dds_::PressSensorState_>& _val_) { this->press_sensor_state_ = _val_; }
  void press_sensor_state(std::vector<::unitree_hg::msg::dds_::PressSensorState_>&& _val_) { this->press_sensor_state_ = _val_; }
  const ::unitree_hg::msg::dds_::IMUState_& imu_state() const { return this->imu_state_; }
  ::unitree_hg::msg::dds_::IMUState_& imu_state() { return this->imu_state_; }
  void imu_state(const ::unitree_hg::msg::dds_::IMUState_& _val_) { this->imu_state_ = _val_; }
  void imu_state(::unitree_hg::msg::dds_::IMUState_&& _val_) { this->imu_state_ = _val_; }
  float power_v() const { return this->power_v_; }
  float& power_v() { return this->power_v_; }
  void power_v(float _val_) { this->power_v_ = _val_; }
  float power_a() const { return this->power_a_; }
  float& power_a() { return this->power_a_; }
  void power_a(float _val_) { this->power_a_ = _val_; }
  float system_v() const { return this->system_v_; }
  float& system_v() { return this->system_v_; }
  void system_v(float _val_) { this->system_v_ = _val_; }
  float device_v() const { return this->device_v_; }
  float& device_v() { return this->device_v_; }
  void device_v(float _val_) { this->device_v_ = _val_; }
  const std::array<uint32_t, 2>& error() const { return this->error_; }
  std::array<uint32_t, 2>& error() { return this->error_; }
  void error(const std::array<uint32_t, 2>& _val_) { this->error_ = _val_; }
  void error(std::array<uint32_t, 2>&& _val_) { this->error_ = _val_; }
  const std::array<uint32_t, 2>& reserve() const { return this->reserve_; }
  std::array<uint32_t, 2>& reserve() { return this->reserve_; }
  void reserve(const std::array<uint32_t, 2>& _val_) { this->reserve_ = _val_; }
  void reserve(std::array<uint32_t, 2>&& _val_) { this->reserve_ = _val_; }

  bool operator==(const HandState_& _other) const
  {
    (void) _other;
    return motor_state_ == _other.motor_state_ &&
      press_sensor_state_ == _other.press_sensor_state_ &&
      imu_state_ == _other.imu_state_ &&
      power_v_ == _other.power_v_ &&
      power_a_ == _other.power_a_ &&
      system_v_ == _other.system_v_ &&
      device_v_ == _other.device_v_ &&
      error_ == _other.error_ &&
      reserve_ == _other.reserve_;
  }

  bool operator!=(const HandState_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_hg::msg::dds_::HandState_>::getTypeName()
{
  return "unitree_hg::msg::dds_::HandState_";
}

template <> constexpr bool TopicTraits<::unitree_hg::msg::dds_::HandState_>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::unitree_hg::msg::dds_::HandState_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_hg::msg::dds_::HandState_>::type_map_blob_sz() { return 2244; }
template<> constexpr unsigned int TopicTraits<::unitree_hg::msg::dds_::HandState_>::type_info_blob_sz() { return 244; }
template<> inline const uint8_t * TopicTraits<::unitree_hg::msg::dds_::HandState_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x1f,  0x03,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0xf1,  0x69,  0x9e,  0xb3,  0x27,  0x0c,  0x7d,  0x77, 
 0x88,  0xed,  0x03,  0xbe,  0xbe,  0x16,  0xb9,  0x00,  0xf2,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0xe2,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x1e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf1,  0x01,  0x00,  0x00,  0xf1, 
 0x7c,  0xc5,  0x84,  0xed,  0x94,  0x0b,  0x8f,  0x30,  0x18,  0xf1,  0x8d,  0xe0,  0xed,  0x24,  0xb2,  0x1e, 
 0x53,  0xdc,  0x00,  0x00,  0x1e,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf1, 
 0x01,  0x00,  0x00,  0xf1,  0x03,  0x93,  0x1d,  0x83,  0x67,  0x60,  0x08,  0x26,  0x6e,  0x46,  0x6a,  0x66, 
 0xb2,  0x55,  0xa0,  0x32,  0xed,  0x8c,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0xf1,  0xfc,  0x68,  0x71,  0x76,  0x65,  0x12,  0x06,  0x55,  0x84,  0x73,  0xdf,  0x44,  0x22, 
 0x80,  0x9e,  0x5f,  0x37,  0x13,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x52,  0xea,  0x3e,  0x50,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x3a,  0x40,  0xba,  0xb9,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x2b,  0xfa,  0xf9,  0xa4,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x8f,  0x6a,  0x1d,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0xcb,  0x5e, 
 0x10,  0x0e,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0xf1,  0x7c, 
 0xc5,  0x84,  0xed,  0x94,  0x0b,  0x8f,  0x30,  0x18,  0xf1,  0x8d,  0xe0,  0xed,  0x24,  0x00,  0x00,  0x00, 
 0xd6,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0xc6,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x15,  0xd6,  0x17,  0x12,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x76,  0x94,  0xf4,  0xa6,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x47,  0xbc,  0xdc,  0xd7,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0xe9,  0x16,  0x89,  0x09,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x8a,  0xf7,  0xae,  0xdf,  0x00,  0x16,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x03,  0xee,  0xf4, 
 0x38,  0xf7,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x0a, 
 0xcf,  0x8b,  0xe1,  0x00,  0x16,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0xa5,  0xfe,  0x26,  0xd5,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0xd1,  0x41,  0x1a,  0xed,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x04,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0xf1,  0x03,  0x93,  0x1d,  0x83,  0x67, 
 0x60,  0x08,  0x26,  0x6e,  0x46,  0x6a,  0x66,  0xb2,  0x55,  0x00,  0x00,  0x00,  0x6b,  0x00,  0x00,  0x00, 
 0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x5b,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0c,  0x09,  0xd9,  0x9f,  0xad,  0x96,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x0c,  0x09,  0xee,  0xf4,  0x38,  0xf7,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x1c,  0x9a,  0x44,  0xeb,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0xf1,  0xfc,  0x68,  0x71,  0x76, 
 0x65,  0x12,  0x06,  0x55,  0x84,  0x73,  0xdf,  0x44,  0x22,  0x80,  0x00,  0x00,  0x93,  0x00,  0x00,  0x00, 
 0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x83,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x09,  0x75,  0xb9,  0xaa,  0xa0,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x6b,  0x3b,  0x2d,  0x85,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x09,  0x0e,  0x12,  0x75,  0x69,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0xb0,  0x7d, 
 0x92,  0xed,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x03,  0xee, 
 0xf4,  0x38,  0xf7,  0x00,  0x1a,  0x05,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0xf2,  0xb9,  0xf2,  0x80, 
 0x69,  0x66,  0x06,  0x0f,  0xc0,  0xf9,  0x13,  0xa4,  0x7b,  0x8a,  0xb9,  0x00,  0x92,  0x01,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x2a,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00, 
 0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x68,  0x67,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a, 
 0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x48,  0x61,  0x6e,  0x64,  0x53,  0x74,  0x61,  0x74,  0x65, 
 0x5f,  0x00,  0x00,  0x00,  0x5a,  0x01,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x2e,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf2,  0x01,  0x00,  0x00,  0xf2,  0xa8,  0x6c,  0xba,  0xb3, 
 0xae,  0x0a,  0xa9,  0x8a,  0x1e,  0x74,  0x66,  0x43,  0xa0,  0x99,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00, 
 0x6d,  0x6f,  0x74,  0x6f,  0x72,  0x5f,  0x73,  0x74,  0x61,  0x74,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x35,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf2,  0x01,  0x00,  0x00,  0xf2, 
 0xcd,  0x53,  0x1f,  0x52,  0x24,  0x7f,  0x1d,  0x07,  0xb3,  0x82,  0x0f,  0xf4,  0x2b,  0x21,  0x00,  0x00, 
 0x13,  0x00,  0x00,  0x00,  0x70,  0x72,  0x65,  0x73,  0x73,  0x5f,  0x73,  0x65,  0x6e,  0x73,  0x6f,  0x72, 
 0x5f,  0x73,  0x74,  0x61,  0x74,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x7d,  0x41,  0x66,  0x75,  0x41,  0x77,  0xd8,  0x6f,  0xa2, 
 0x7c,  0xd2,  0x5b,  0x04,  0x03,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x69,  0x6d,  0x75,  0x5f, 
 0x73,  0x74,  0x61,  0x74,  0x65,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x08,  0x00,  0x00,  0x00,  0x70,  0x6f,  0x77,  0x65,  0x72,  0x5f,  0x76,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x70,  0x6f,  0x77,  0x65,  0x72,  0x5f,  0x61,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x17,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x73,  0x79,  0x73,  0x74,  0x65,  0x6d,  0x5f,  0x76,  0x00,  0x00,  0x00,  0x00,  0x17,  0x00,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x09,  0x00,  0x00,  0x00,  0x64,  0x65,  0x76,  0x69, 
 0x63,  0x65,  0x5f,  0x76,  0x00,  0x00,  0x00,  0x00,  0x20,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x65,  0x72,  0x72,  0x6f,  0x72,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x07,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00, 
 0x00,  0x00,  0xf2,  0xa8,  0x6c,  0xba,  0xb3,  0xae,  0x0a,  0xa9,  0x8a,  0x1e,  0x74,  0x66,  0x43,  0xa0, 
 0x99,  0x00,  0x00,  0x00,  0x66,  0x01,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x2b,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f, 
 0x68,  0x67,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x4d, 
 0x6f,  0x74,  0x6f,  0x72,  0x53,  0x74,  0x61,  0x74,  0x65,  0x5f,  0x00,  0x00,  0x2e,  0x01,  0x00,  0x00, 
 0x0a,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x64,  0x65,  0x00,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x02,  0x00,  0x00,  0x00,  0x71,  0x00,  0x00,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x64,  0x71,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x04,  0x00,  0x00,  0x00,  0x64,  0x64,  0x71,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x74,  0x61,  0x75,  0x5f,  0x65,  0x73,  0x74,  0x00,  0x00,  0x00,  0x00,  0x00,  0x26,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x03,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x74,  0x65,  0x6d,  0x70,  0x65,  0x72,  0x61,  0x74, 
 0x75,  0x72,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x04,  0x00,  0x00,  0x00,  0x76,  0x6f,  0x6c,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x21,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x73,  0x65,  0x6e,  0x73, 
 0x6f,  0x72,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x07,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x74,  0x6f,  0x72,  0x73,  0x74,  0x61, 
 0x74,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x07,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0xf2,  0xcd, 
 0x53,  0x1f,  0x52,  0x24,  0x7f,  0x1d,  0x07,  0xb3,  0x82,  0x0f,  0xf4,  0x2b,  0x21,  0x00,  0x00,  0x00, 
 0xca,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x31,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x29,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x68,  0x67,  0x3a,  0x3a, 
 0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x50,  0x72,  0x65,  0x73,  0x73, 
 0x53,  0x65,  0x6e,  0x73,  0x6f,  0x72,  0x53,  0x74,  0x61,  0x74,  0x65,  0x5f,  0x00,  0x00,  0x00,  0x00, 
 0x8a,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0c,  0x09,  0x00,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x70,  0x72,  0x65,  0x73,  0x73,  0x75,  0x72,  0x65,  0x00,  0x00,  0x00,  0x00, 
 0x26,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x0c,  0x09,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x74,  0x65,  0x6d,  0x70, 
 0x65,  0x72,  0x61,  0x74,  0x75,  0x72,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00,  0x05,  0x00,  0x00,  0x00,  0x6c,  0x6f,  0x73,  0x74, 
 0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0xf2,  0x7d, 
 0x41,  0x66,  0x75,  0x41,  0x77,  0xd8,  0x6f,  0xa2,  0x7c,  0xd2,  0x5b,  0x04,  0x03,  0x00,  0x00,  0x00, 
 0xfe,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x21,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x68,  0x67,  0x3a,  0x3a, 
 0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x49,  0x4d,  0x55,  0x53,  0x74, 
 0x61,  0x74,  0x65,  0x5f,  0x00,  0x00,  0x00,  0x00,  0xc6,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x25,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x04,  0x09,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x71,  0x75,  0x61,  0x74, 
 0x65,  0x72,  0x6e,  0x69,  0x6f,  0x6e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x09,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x67,  0x79,  0x72,  0x6f,  0x73,  0x63,  0x6f,  0x70, 
 0x65,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00, 
 0x61,  0x63,  0x63,  0x65,  0x6c,  0x65,  0x72,  0x6f,  0x6d,  0x65,  0x74,  0x65,  0x72,  0x00,  0x00,  0x00, 
 0x1e,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x72,  0x70,  0x79,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x1a,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x03,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x74,  0x65,  0x6d,  0x70,  0x65,  0x72,  0x61,  0x74,  0x75,  0x72,  0x65,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x7c,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0xf2,  0xb9,  0xf2,  0x80, 
 0x69,  0x66,  0x06,  0x0f,  0xc0,  0xf9,  0x13,  0xa4,  0x7b,  0x8a,  0xb9,  0xf1,  0x69,  0x9e,  0xb3,  0x27, 
 0x0c,  0x7d,  0x77,  0x88,  0xed,  0x03,  0xbe,  0xbe,  0x16,  0xb9,  0xf2,  0xa8,  0x6c,  0xba,  0xb3,  0xae, 
 0x0a,  0xa9,  0x8a,  0x1e,  0x74,  0x66,  0x43,  0xa0,  0x99,  0xf1,  0x7c,  0xc5,  0x84,  0xed,  0x94,  0x0b, 
 0x8f,  0x30,  0x18,  0xf1,  0x8d,  0xe0,  0xed,  0x24,  0xf2,  0xcd,  0x53,  0x1f,  0x52,  0x24,  0x7f,  0x1d, 
 0x07,  0xb3,  0x82,  0x0f,  0xf4,  0x2b,  0x21,  0xf1,  0x03,  0x93,  0x1d,  0x83,  0x67,  0x60,  0x08,  0x26, 
 0x6e,  0x46,  0x6a,  0x66,  0xb2,  0x55,  0xf2,  0x7d,  0x41,  0x66,  0x75,  0x41,  0x77,  0xd8,  0x6f,  0xa2, 
 0x7c,  0xd2,  0x5b,  0x04,  0x03,  0xf1,  0xfc,  0x68,  0x71,  0x76,  0x65,  0x12,  0x06,  0x55,  0x84,  0x73, 
 0xdf,  0x44,  0x22,  0x80, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_hg::msg::dds_::HandState_>::type_info_blob() {
  static const uint8_t blob[] = {
 0xf0,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x70,  0x00,  0x00,  0x00,  0x6c,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x69,  0x9e,  0xb3,  0x27,  0x0c,  0x7d,  0x77,  0x88,  0xed,  0x03,  0xbe, 
 0xbe,  0x16,  0xb9,  0x00,  0xf6,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x4c,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0x7c,  0xc5,  0x84,  0xed,  0x94,  0x0b,  0x8f, 
 0x30,  0x18,  0xf1,  0x8d,  0xe0,  0xed,  0x24,  0x00,  0xda,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf1,  0x03,  0x93,  0x1d,  0x83,  0x67,  0x60,  0x08,  0x26,  0x6e,  0x46,  0x6a,  0x66,  0xb2,  0x55,  0x00, 
 0x6f,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0xfc,  0x68,  0x71,  0x76,  0x65,  0x12,  0x06, 
 0x55,  0x84,  0x73,  0xdf,  0x44,  0x22,  0x80,  0x00,  0x97,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40, 
 0x70,  0x00,  0x00,  0x00,  0x6c,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0xb9,  0xf2,  0x80, 
 0x69,  0x66,  0x06,  0x0f,  0xc0,  0xf9,  0x13,  0xa4,  0x7b,  0x8a,  0xb9,  0x00,  0x96,  0x01,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x4c,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0xa8,  0x6c,  0xba,  0xb3,  0xae,  0x0a,  0xa9,  0x8a,  0x1e,  0x74,  0x66,  0x43,  0xa0,  0x99,  0x00, 
 0x6a,  0x01,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0xcd,  0x53,  0x1f,  0x52,  0x24,  0x7f,  0x1d, 
 0x07,  0xb3,  0x82,  0x0f,  0xf4,  0x2b,  0x21,  0x00,  0xce,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0x7d,  0x41,  0x66,  0x75,  0x41,  0x77,  0xd8,  0x6f,  0xa2,  0x7c,  0xd2,  0x5b,  0x04,  0x03,  0x00, 
 0x02,  0x01,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_hg::msg::dds_::HandState_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_hg::msg::dds_::HandState_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_hg::msg::dds_::HandState_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_hg::msg::dds_::HandState_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_hg::msg::dds_::HandState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.motor_state().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.motor_state()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.press_sensor_state().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.press_sensor_state()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.power_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.power_a()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.system_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.device_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.error()[0], instance.error().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_hg::msg::dds_::HandState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::HandState_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_hg::msg::dds_::HandState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.motor_state().size());
      if (!read(streamer, se_1))
        return false;
      instance.motor_state().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.motor_state()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.press_sensor_state().size());
      if (!read(streamer, se_1))
        return false;
      instance.press_sensor_state().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.press_sensor_state()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.power_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.power_a()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.system_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.device_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.error()[0], instance.error().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_hg::msg::dds_::HandState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::HandState_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_hg::msg::dds_::HandState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.motor_state().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.motor_state()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.press_sensor_state().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.press_sensor_state()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.power_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.power_a()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.system_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.device_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.error()[0], instance.error().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_hg::msg::dds_::HandState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::HandState_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_hg::msg::dds_::HandState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.motor_state()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.press_sensor_state()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.power_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.power_a()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.system_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.device_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.error()[0], instance.error().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_hg::msg::dds_::HandState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::HandState_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_HG_HANDSTATE__HPP
