/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: SportModeState_.idl
  Source: SportModeState_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_SPORTMODESTATE__HPP
#define DDSCXX_UNITREE_IDL_GO2_SPORTMODESTATE__HPP

#include "unitree/idl/go2/IMUState_.hpp"

#include "unitree/idl/go2/PathPoint_.hpp"

#include "unitree/idl/go2/TimeSpec_.hpp"

#include <cstdint>
#include <array>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class SportModeState_
{
private:
 ::unitree_go::msg::dds_::TimeSpec_ stamp_;
 uint32_t error_code_ = 0;
 ::unitree_go::msg::dds_::IMUState_ imu_state_;
 uint8_t mode_ = 0;
 float progress_ = 0.0f;
 uint8_t gait_type_ = 0;
 float foot_raise_height_ = 0.0f;
 std::array<float, 3> position_ = { };
 float body_height_ = 0.0f;
 std::array<float, 3> velocity_ = { };
 float yaw_speed_ = 0.0f;
 std::array<float, 4> range_obstacle_ = { };
 std::array<int16_t, 4> foot_force_ = { };
 std::array<float, 12> foot_position_body_ = { };
 std::array<float, 12> foot_speed_body_ = { };
 std::array<::unitree_go::msg::dds_::PathPoint_, 10> path_point_ = { };

public:
  SportModeState_() = default;

  explicit SportModeState_(
    const ::unitree_go::msg::dds_::TimeSpec_& stamp,
    uint32_t error_code,
    const ::unitree_go::msg::dds_::IMUState_& imu_state,
    uint8_t mode,
    float progress,
    uint8_t gait_type,
    float foot_raise_height,
    const std::array<float, 3>& position,
    float body_height,
    const std::array<float, 3>& velocity,
    float yaw_speed,
    const std::array<float, 4>& range_obstacle,
    const std::array<int16_t, 4>& foot_force,
    const std::array<float, 12>& foot_position_body,
    const std::array<float, 12>& foot_speed_body,
    const std::array<::unitree_go::msg::dds_::PathPoint_, 10>& path_point) :
    stamp_(stamp),
    error_code_(error_code),
    imu_state_(imu_state),
    mode_(mode),
    progress_(progress),
    gait_type_(gait_type),
    foot_raise_height_(foot_raise_height),
    position_(position),
    body_height_(body_height),
    velocity_(velocity),
    yaw_speed_(yaw_speed),
    range_obstacle_(range_obstacle),
    foot_force_(foot_force),
    foot_position_body_(foot_position_body),
    foot_speed_body_(foot_speed_body),
    path_point_(path_point) { }

  const ::unitree_go::msg::dds_::TimeSpec_& stamp() const { return this->stamp_; }
  ::unitree_go::msg::dds_::TimeSpec_& stamp() { return this->stamp_; }
  void stamp(const ::unitree_go::msg::dds_::TimeSpec_& _val_) { this->stamp_ = _val_; }
  void stamp(::unitree_go::msg::dds_::TimeSpec_&& _val_) { this->stamp_ = _val_; }
  uint32_t error_code() const { return this->error_code_; }
  uint32_t& error_code() { return this->error_code_; }
  void error_code(uint32_t _val_) { this->error_code_ = _val_; }
  const ::unitree_go::msg::dds_::IMUState_& imu_state() const { return this->imu_state_; }
  ::unitree_go::msg::dds_::IMUState_& imu_state() { return this->imu_state_; }
  void imu_state(const ::unitree_go::msg::dds_::IMUState_& _val_) { this->imu_state_ = _val_; }
  void imu_state(::unitree_go::msg::dds_::IMUState_&& _val_) { this->imu_state_ = _val_; }
  uint8_t mode() const { return this->mode_; }
  uint8_t& mode() { return this->mode_; }
  void mode(uint8_t _val_) { this->mode_ = _val_; }
  float progress() const { return this->progress_; }
  float& progress() { return this->progress_; }
  void progress(float _val_) { this->progress_ = _val_; }
  uint8_t gait_type() const { return this->gait_type_; }
  uint8_t& gait_type() { return this->gait_type_; }
  void gait_type(uint8_t _val_) { this->gait_type_ = _val_; }
  float foot_raise_height() const { return this->foot_raise_height_; }
  float& foot_raise_height() { return this->foot_raise_height_; }
  void foot_raise_height(float _val_) { this->foot_raise_height_ = _val_; }
  const std::array<float, 3>& position() const { return this->position_; }
  std::array<float, 3>& position() { return this->position_; }
  void position(const std::array<float, 3>& _val_) { this->position_ = _val_; }
  void position(std::array<float, 3>&& _val_) { this->position_ = _val_; }
  float body_height() const { return this->body_height_; }
  float& body_height() { return this->body_height_; }
  void body_height(float _val_) { this->body_height_ = _val_; }
  const std::array<float, 3>& velocity() const { return this->velocity_; }
  std::array<float, 3>& velocity() { return this->velocity_; }
  void velocity(const std::array<float, 3>& _val_) { this->velocity_ = _val_; }
  void velocity(std::array<float, 3>&& _val_) { this->velocity_ = _val_; }
  float yaw_speed() const { return this->yaw_speed_; }
  float& yaw_speed() { return this->yaw_speed_; }
  void yaw_speed(float _val_) { this->yaw_speed_ = _val_; }
  const std::array<float, 4>& range_obstacle() const { return this->range_obstacle_; }
  std::array<float, 4>& range_obstacle() { return this->range_obstacle_; }
  void range_obstacle(const std::array<float, 4>& _val_) { this->range_obstacle_ = _val_; }
  void range_obstacle(std::array<float, 4>&& _val_) { this->range_obstacle_ = _val_; }
  const std::array<int16_t, 4>& foot_force() const { return this->foot_force_; }
  std::array<int16_t, 4>& foot_force() { return this->foot_force_; }
  void foot_force(const std::array<int16_t, 4>& _val_) { this->foot_force_ = _val_; }
  void foot_force(std::array<int16_t, 4>&& _val_) { this->foot_force_ = _val_; }
  const std::array<float, 12>& foot_position_body() const { return this->foot_position_body_; }
  std::array<float, 12>& foot_position_body() { return this->foot_position_body_; }
  void foot_position_body(const std::array<float, 12>& _val_) { this->foot_position_body_ = _val_; }
  void foot_position_body(std::array<float, 12>&& _val_) { this->foot_position_body_ = _val_; }
  const std::array<float, 12>& foot_speed_body() const { return this->foot_speed_body_; }
  std::array<float, 12>& foot_speed_body() { return this->foot_speed_body_; }
  void foot_speed_body(const std::array<float, 12>& _val_) { this->foot_speed_body_ = _val_; }
  void foot_speed_body(std::array<float, 12>&& _val_) { this->foot_speed_body_ = _val_; }
  const std::array<::unitree_go::msg::dds_::PathPoint_, 10>& path_point() const { return this->path_point_; }
  std::array<::unitree_go::msg::dds_::PathPoint_, 10>& path_point() { return this->path_point_; }
  void path_point(const std::array<::unitree_go::msg::dds_::PathPoint_, 10>& _val_) { this->path_point_ = _val_; }
  void path_point(std::array<::unitree_go::msg::dds_::PathPoint_, 10>&& _val_) { this->path_point_ = _val_; }

  bool operator==(const SportModeState_& _other) const
  {
    (void) _other;
    return stamp_ == _other.stamp_ &&
      error_code_ == _other.error_code_ &&
      imu_state_ == _other.imu_state_ &&
      mode_ == _other.mode_ &&
      progress_ == _other.progress_ &&
      gait_type_ == _other.gait_type_ &&
      foot_raise_height_ == _other.foot_raise_height_ &&
      position_ == _other.position_ &&
      body_height_ == _other.body_height_ &&
      velocity_ == _other.velocity_ &&
      yaw_speed_ == _other.yaw_speed_ &&
      range_obstacle_ == _other.range_obstacle_ &&
      foot_force_ == _other.foot_force_ &&
      foot_position_body_ == _other.foot_position_body_ &&
      foot_speed_body_ == _other.foot_speed_body_ &&
      path_point_ == _other.path_point_;
  }

  bool operator!=(const SportModeState_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::SportModeState_>::getTypeName()
{
  return "unitree_go::msg::dds_::SportModeState_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::SportModeState_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::SportModeState_>::type_map_blob_sz() { return 2316; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::SportModeState_>::type_info_blob_sz() { return 244; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::SportModeState_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x33,  0x03,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0xf1,  0x9f,  0xa6,  0xcc,  0xdc,  0x5c,  0xaf,  0xde, 
 0xaa,  0xb8,  0xa7,  0x62,  0xe3,  0x10,  0x44,  0x00,  0x94,  0x01,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x84,  0x01,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00, 
 0x19,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x56,  0x7c,  0x5a,  0x93,  0x54, 
 0x1c,  0x3b,  0x10,  0x86,  0xa4,  0xba,  0x46,  0xf9,  0x8d,  0x96,  0xb8,  0xc7,  0x8d,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x8e,  0x95,  0xbd,  0x42,  0x00, 
 0x19,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x4b,  0x1b,  0xf4,  0xaf,  0x32, 
 0xe5,  0xbc,  0x67,  0x10,  0xef,  0xc8,  0x29,  0x31,  0x15,  0x9e,  0x5f,  0x37,  0x13,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x15,  0xd6,  0x17,  0x12,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x3c,  0x70,  0x9b,  0x10,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xe2,  0xc7,  0x6d,  0x1e,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xb5,  0x46,  0x7f,  0xa0,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x47,  0x57,  0xfe,  0x07,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xe0,  0x34,  0x4c,  0x32,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x09,  0xac,  0x1a,  0x45,  0x3d,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x25,  0xcf,  0xab,  0x66,  0x00,  0x16,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x09,  0xcd,  0x43, 
 0x62,  0x2f,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x03,  0x41,  0x56,  0x4b,  0x84,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x0d,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x0c,  0x09,  0xb3,  0x0f,  0x21,  0x66,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x0e,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x0c,  0x09,  0x43,  0x94,  0x9e,  0x02,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00,  0x0f,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf1,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0a,  0xf1,  0x6d,  0x6e, 
 0x83,  0x23,  0xe9,  0x1a,  0xd4,  0x2a,  0x82,  0x33,  0xae,  0xf6,  0xbd,  0xe2,  0xdc,  0x56,  0xb6,  0x0a, 
 0xf1,  0x56,  0x7c,  0x5a,  0x93,  0x54,  0x1c,  0x3b,  0x10,  0x86,  0xa4,  0xba,  0x46,  0xf9,  0x8d,  0x00, 
 0x33,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x23,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x04,  0x74,  0x45,  0x9c,  0xa3,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x07,  0xe2,  0x04,  0x64,  0xd5,  0xf1,  0x4b,  0x1b,  0xf4,  0xaf,  0x32,  0xe5,  0xbc,  0x67, 
 0x10,  0xef,  0xc8,  0x29,  0x31,  0x15,  0x00,  0x00,  0x93,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x83,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x04,  0x09,  0x75,  0xb9,  0xaa,  0xa0,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x09,  0x6b,  0x3b,  0x2d,  0x85,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x0e,  0x12, 
 0x75,  0x69,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0xb0,  0x7d,  0x92,  0xed,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xee,  0xf4,  0x38,  0xf7,  0xf1, 
 0x6d,  0x6e,  0x83,  0x23,  0xe9,  0x1a,  0xd4,  0x2a,  0x82,  0x33,  0xae,  0xf6,  0xbd,  0xe2,  0x00,  0x00, 
 0x83,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x73,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0xe9,  0x6c,  0x41,  0xab,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x9d,  0xd4,  0xe4,  0x61,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x41,  0x52,  0x90,  0x76,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x92,  0xdd,  0xa6,  0x44,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x76,  0x7f,  0xc7,  0x3d,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x94,  0x95,  0xfa,  0x6c,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x83,  0x29,  0x00,  0xa3,  0x00,  0x4f,  0x05,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0xf2,  0xe5,  0x97,  0x49,  0x63,  0x0f,  0xff,  0x75,  0xcc,  0x17,  0x0a,  0xfb,  0x23,  0x87,  0x83,  0x00, 
 0xa9,  0x02,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x2f,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x27,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a, 
 0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x53,  0x70,  0x6f,  0x72,  0x74, 
 0x4d,  0x6f,  0x64,  0x65,  0x53,  0x74,  0x61,  0x74,  0x65,  0x5f,  0x00,  0x00,  0x6d,  0x02,  0x00,  0x00, 
 0x10,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x61, 
 0x24,  0x29,  0x6f,  0x59,  0x45,  0x9b,  0xa9,  0x0b,  0xe9,  0x24,  0xc9,  0x35,  0xcc,  0x00,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x73,  0x74,  0x61,  0x6d,  0x70,  0x00,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x65,  0x72,  0x72,  0x6f, 
 0x72,  0x5f,  0x63,  0x6f,  0x64,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0xc4,  0xec,  0xb4,  0x9c,  0xd7,  0xb2,  0x25,  0xd2,  0x26, 
 0x28,  0xb6,  0xb7,  0x64,  0xcc,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x69,  0x6d,  0x75,  0x5f, 
 0x73,  0x74,  0x61,  0x74,  0x65,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x00,  0x05,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x64,  0x65,  0x00,  0x00,  0x00,  0x00, 
 0x17,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x70,  0x72,  0x6f,  0x67,  0x72,  0x65,  0x73,  0x73,  0x00,  0x00,  0x00,  0x00,  0x18,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x67,  0x61,  0x69,  0x74, 
 0x5f,  0x74,  0x79,  0x70,  0x65,  0x00,  0x00,  0x00,  0x20,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x12,  0x00,  0x00,  0x00,  0x66,  0x6f,  0x6f,  0x74,  0x5f,  0x72,  0x61,  0x69, 
 0x73,  0x65,  0x5f,  0x68,  0x65,  0x69,  0x67,  0x68,  0x74,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x09,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x70,  0x6f,  0x73,  0x69,  0x74,  0x69,  0x6f,  0x6e, 
 0x00,  0x00,  0x00,  0x00,  0x1a,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x62,  0x6f,  0x64,  0x79,  0x5f,  0x68,  0x65,  0x69,  0x67,  0x68,  0x74,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x76,  0x65,  0x6c,  0x6f,  0x63,  0x69,  0x74,  0x79,  0x00,  0x00,  0x00,  0x00,  0x18,  0x00,  0x00,  0x00, 
 0x0a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x79,  0x61,  0x77,  0x5f, 
 0x73,  0x70,  0x65,  0x65,  0x64,  0x00,  0x00,  0x00,  0x29,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x09,  0x00,  0x00, 
 0x0f,  0x00,  0x00,  0x00,  0x72,  0x61,  0x6e,  0x67,  0x65,  0x5f,  0x6f,  0x62,  0x73,  0x74,  0x61,  0x63, 
 0x6c,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x25,  0x00,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x03,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x66,  0x6f,  0x6f,  0x74,  0x5f,  0x66,  0x6f,  0x72,  0x63,  0x65,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x2d,  0x00,  0x00,  0x00,  0x0d,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0c,  0x09,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00, 
 0x66,  0x6f,  0x6f,  0x74,  0x5f,  0x70,  0x6f,  0x73,  0x69,  0x74,  0x69,  0x6f,  0x6e,  0x5f,  0x62,  0x6f, 
 0x64,  0x79,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x2a,  0x00,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0c,  0x09,  0x00,  0x00, 
 0x10,  0x00,  0x00,  0x00,  0x66,  0x6f,  0x6f,  0x74,  0x5f,  0x73,  0x70,  0x65,  0x65,  0x64,  0x5f,  0x62, 
 0x6f,  0x64,  0x79,  0x00,  0x00,  0x00,  0x00,  0x00,  0x31,  0x00,  0x00,  0x00,  0x0f,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf2,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0a,  0xf2,  0xd6,  0x62, 
 0x9a,  0x35,  0x54,  0x6d,  0x9f,  0xc9,  0xae,  0x65,  0xcf,  0xa2,  0xd4,  0x15,  0x0b,  0x00,  0x00,  0x00, 
 0x70,  0x61,  0x74,  0x68,  0x5f,  0x70,  0x6f,  0x69,  0x6e,  0x74,  0x00,  0x00,  0x00,  0xf2,  0x61,  0x24, 
 0x29,  0x6f,  0x59,  0x45,  0x9b,  0xa9,  0x0b,  0xe9,  0x24,  0xc9,  0x35,  0xcc,  0x6e,  0x00,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00, 
 0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a, 
 0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x54,  0x69,  0x6d,  0x65,  0x53,  0x70,  0x65,  0x63,  0x5f, 
 0x00,  0x00,  0x00,  0x00,  0x36,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x04,  0x00,  0x04,  0x00,  0x00,  0x00,  0x73,  0x65,  0x63,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x6e,  0x61,  0x6e,  0x6f,  0x73,  0x65,  0x63,  0x00,  0x00,  0x00,  0xf2,  0xc4, 
 0xec,  0xb4,  0x9c,  0xd7,  0xb2,  0x25,  0xd2,  0x26,  0x28,  0xb6,  0xb7,  0x64,  0xcc,  0x00,  0x00,  0x00, 
 0xfe,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x21,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a, 
 0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x49,  0x4d,  0x55,  0x53,  0x74, 
 0x61,  0x74,  0x65,  0x5f,  0x00,  0x00,  0x00,  0x00,  0xc6,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x25,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x04,  0x09,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x71,  0x75,  0x61,  0x74, 
 0x65,  0x72,  0x6e,  0x69,  0x6f,  0x6e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x09,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x67,  0x79,  0x72,  0x6f,  0x73,  0x63,  0x6f,  0x70, 
 0x65,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00, 
 0x61,  0x63,  0x63,  0x65,  0x6c,  0x65,  0x72,  0x6f,  0x6d,  0x65,  0x74,  0x65,  0x72,  0x00,  0x00,  0x00, 
 0x1e,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x72,  0x70,  0x79,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x1a,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x74,  0x65,  0x6d,  0x70,  0x65,  0x72,  0x61,  0x74,  0x75,  0x72,  0x65,  0x00, 
 0x00,  0x00,  0xf2,  0xd6,  0x62,  0x9a,  0x35,  0x54,  0x6d,  0x9f,  0xc9,  0xae,  0x65,  0xcf,  0xa2,  0xd4, 
 0x15,  0x00,  0x00,  0x00,  0xe3,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x2a,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f, 
 0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x50, 
 0x61,  0x74,  0x68,  0x50,  0x6f,  0x69,  0x6e,  0x74,  0x5f,  0x00,  0x00,  0x00,  0xab,  0x00,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x1b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x0d,  0x00,  0x00,  0x00,  0x74,  0x5f,  0x66,  0x72,  0x6f,  0x6d,  0x5f,  0x73,  0x74,  0x61,  0x72,  0x74, 
 0x00,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x78,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x02,  0x00,  0x00,  0x00,  0x79,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x04,  0x00,  0x00,  0x00,  0x79,  0x61,  0x77,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x76,  0x78,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00,  0x76,  0x79,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x76,  0x79,  0x61,  0x77,  0x00,  0x00,  0x00,  0x00,  0x7c,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0xf2,  0xe5,  0x97,  0x49,  0x63,  0x0f,  0xff,  0x75,  0xcc,  0x17,  0x0a,  0xfb, 
 0x23,  0x87,  0x83,  0xf1,  0x9f,  0xa6,  0xcc,  0xdc,  0x5c,  0xaf,  0xde,  0xaa,  0xb8,  0xa7,  0x62,  0xe3, 
 0x10,  0x44,  0xf2,  0x61,  0x24,  0x29,  0x6f,  0x59,  0x45,  0x9b,  0xa9,  0x0b,  0xe9,  0x24,  0xc9,  0x35, 
 0xcc,  0xf1,  0x56,  0x7c,  0x5a,  0x93,  0x54,  0x1c,  0x3b,  0x10,  0x86,  0xa4,  0xba,  0x46,  0xf9,  0x8d, 
 0xf2,  0xc4,  0xec,  0xb4,  0x9c,  0xd7,  0xb2,  0x25,  0xd2,  0x26,  0x28,  0xb6,  0xb7,  0x64,  0xcc,  0xf1, 
 0x4b,  0x1b,  0xf4,  0xaf,  0x32,  0xe5,  0xbc,  0x67,  0x10,  0xef,  0xc8,  0x29,  0x31,  0x15,  0xf2,  0xd6, 
 0x62,  0x9a,  0x35,  0x54,  0x6d,  0x9f,  0xc9,  0xae,  0x65,  0xcf,  0xa2,  0xd4,  0x15,  0xf1,  0x6d,  0x6e, 
 0x83,  0x23,  0xe9,  0x1a,  0xd4,  0x2a,  0x82,  0x33,  0xae,  0xf6,  0xbd,  0xe2, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::SportModeState_>::type_info_blob() {
  static const uint8_t blob[] = {
 0xf0,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x70,  0x00,  0x00,  0x00,  0x6c,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x9f,  0xa6,  0xcc,  0xdc,  0x5c,  0xaf,  0xde,  0xaa,  0xb8,  0xa7,  0x62, 
 0xe3,  0x10,  0x44,  0x00,  0x98,  0x01,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x4c,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0x56,  0x7c,  0x5a,  0x93,  0x54,  0x1c,  0x3b, 
 0x10,  0x86,  0xa4,  0xba,  0x46,  0xf9,  0x8d,  0x00,  0x37,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf1,  0x4b,  0x1b,  0xf4,  0xaf,  0x32,  0xe5,  0xbc,  0x67,  0x10,  0xef,  0xc8,  0x29,  0x31,  0x15,  0x00, 
 0x97,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0x6d,  0x6e,  0x83,  0x23,  0xe9,  0x1a,  0xd4, 
 0x2a,  0x82,  0x33,  0xae,  0xf6,  0xbd,  0xe2,  0x00,  0x87,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40, 
 0x70,  0x00,  0x00,  0x00,  0x6c,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0xe5,  0x97,  0x49, 
 0x63,  0x0f,  0xff,  0x75,  0xcc,  0x17,  0x0a,  0xfb,  0x23,  0x87,  0x83,  0x00,  0xad,  0x02,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x4c,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0x61,  0x24,  0x29,  0x6f,  0x59,  0x45,  0x9b,  0xa9,  0x0b,  0xe9,  0x24,  0xc9,  0x35,  0xcc,  0x00, 
 0x72,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0xc4,  0xec,  0xb4,  0x9c,  0xd7,  0xb2,  0x25, 
 0xd2,  0x26,  0x28,  0xb6,  0xb7,  0x64,  0xcc,  0x00,  0x02,  0x01,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0xd6,  0x62,  0x9a,  0x35,  0x54,  0x6d,  0x9f,  0xc9,  0xae,  0x65,  0xcf,  0xa2,  0xd4,  0x15,  0x00, 
 0xe7,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::SportModeState_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::SportModeState_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::SportModeState_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::SportModeState_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::SportModeState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.stamp(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.error_code()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.progress()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.gait_type()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.foot_raise_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.position()[0], instance.position().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.body_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.velocity()[0], instance.velocity().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.yaw_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.range_obstacle()[0], instance.range_obstacle().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.foot_force()[0], instance.foot_force().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.foot_position_body()[0], instance.foot_position_body().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.foot_speed_body()[0], instance.foot_speed_body().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.path_point()) {  //array depth 1
      if (!write(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::SportModeState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::SportModeState_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::SportModeState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.stamp(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.error_code()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.progress()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.gait_type()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.foot_raise_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.position()[0], instance.position().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.body_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.velocity()[0], instance.velocity().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.yaw_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.range_obstacle()[0], instance.range_obstacle().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.foot_force()[0], instance.foot_force().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.foot_position_body()[0], instance.foot_position_body().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.foot_speed_body()[0], instance.foot_speed_body().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (auto & a_1:instance.path_point()) {  //array depth 1
      if (!read(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::SportModeState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::SportModeState_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::SportModeState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.stamp(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.error_code()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.progress()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.gait_type()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.foot_raise_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.position()[0], instance.position().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.body_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.velocity()[0], instance.velocity().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.yaw_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.range_obstacle()[0], instance.range_obstacle().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.foot_force()[0], instance.foot_force().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.foot_position_body()[0], instance.foot_position_body().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.foot_speed_body()[0], instance.foot_speed_body().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.path_point()) {  //array depth 1
      if (!move(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::SportModeState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::SportModeState_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::SportModeState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.stamp(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.error_code()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.progress()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.gait_type()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.foot_raise_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.position()[0], instance.position().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.body_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.velocity()[0], instance.velocity().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.yaw_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.range_obstacle()[0], instance.range_obstacle().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.foot_force()[0], instance.foot_force().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.foot_position_body()[0], instance.foot_position_body().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.foot_speed_body()[0], instance.foot_speed_body().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.path_point()) {  //array depth 1
      if (!max(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::SportModeState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::SportModeState_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_SPORTMODESTATE__HPP
