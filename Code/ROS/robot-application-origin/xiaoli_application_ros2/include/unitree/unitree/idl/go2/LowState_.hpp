/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: LowState_.idl
  Source: LowState_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_LOWSTATE__HPP
#define DDSCXX_UNITREE_IDL_GO2_LOWSTATE__HPP

#include "unitree/idl/go2/BmsState_.hpp"

#include "unitree/idl/go2/IMUState_.hpp"

#include "unitree/idl/go2/MotorState_.hpp"

#include <cstdint>
#include <array>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class LowState_
{
private:
 std::array<uint8_t, 2> head_ = { };
 uint8_t level_flag_ = 0;
 uint8_t frame_reserve_ = 0;
 std::array<uint32_t, 2> sn_ = { };
 std::array<uint32_t, 2> version_ = { };
 uint16_t bandwidth_ = 0;
 ::unitree_go::msg::dds_::IMUState_ imu_state_;
 std::array<::unitree_go::msg::dds_::MotorState_, 20> motor_state_ = { };
 ::unitree_go::msg::dds_::BmsState_ bms_state_;
 std::array<int16_t, 4> foot_force_ = { };
 std::array<int16_t, 4> foot_force_est_ = { };
 uint32_t tick_ = 0;
 std::array<uint8_t, 40> wireless_remote_ = { };
 uint8_t bit_flag_ = 0;
 float adc_reel_ = 0.0f;
 uint8_t temperature_ntc1_ = 0;
 uint8_t temperature_ntc2_ = 0;
 float power_v_ = 0.0f;
 float power_a_ = 0.0f;
 std::array<uint16_t, 4> fan_frequency_ = { };
 uint32_t reserve_ = 0;
 uint32_t crc_ = 0;

public:
  LowState_() = default;

  explicit LowState_(
    const std::array<uint8_t, 2>& head,
    uint8_t level_flag,
    uint8_t frame_reserve,
    const std::array<uint32_t, 2>& sn,
    const std::array<uint32_t, 2>& version,
    uint16_t bandwidth,
    const ::unitree_go::msg::dds_::IMUState_& imu_state,
    const std::array<::unitree_go::msg::dds_::MotorState_, 20>& motor_state,
    const ::unitree_go::msg::dds_::BmsState_& bms_state,
    const std::array<int16_t, 4>& foot_force,
    const std::array<int16_t, 4>& foot_force_est,
    uint32_t tick,
    const std::array<uint8_t, 40>& wireless_remote,
    uint8_t bit_flag,
    float adc_reel,
    uint8_t temperature_ntc1,
    uint8_t temperature_ntc2,
    float power_v,
    float power_a,
    const std::array<uint16_t, 4>& fan_frequency,
    uint32_t reserve,
    uint32_t crc) :
    head_(head),
    level_flag_(level_flag),
    frame_reserve_(frame_reserve),
    sn_(sn),
    version_(version),
    bandwidth_(bandwidth),
    imu_state_(imu_state),
    motor_state_(motor_state),
    bms_state_(bms_state),
    foot_force_(foot_force),
    foot_force_est_(foot_force_est),
    tick_(tick),
    wireless_remote_(wireless_remote),
    bit_flag_(bit_flag),
    adc_reel_(adc_reel),
    temperature_ntc1_(temperature_ntc1),
    temperature_ntc2_(temperature_ntc2),
    power_v_(power_v),
    power_a_(power_a),
    fan_frequency_(fan_frequency),
    reserve_(reserve),
    crc_(crc) { }

  const std::array<uint8_t, 2>& head() const { return this->head_; }
  std::array<uint8_t, 2>& head() { return this->head_; }
  void head(const std::array<uint8_t, 2>& _val_) { this->head_ = _val_; }
  void head(std::array<uint8_t, 2>&& _val_) { this->head_ = _val_; }
  uint8_t level_flag() const { return this->level_flag_; }
  uint8_t& level_flag() { return this->level_flag_; }
  void level_flag(uint8_t _val_) { this->level_flag_ = _val_; }
  uint8_t frame_reserve() const { return this->frame_reserve_; }
  uint8_t& frame_reserve() { return this->frame_reserve_; }
  void frame_reserve(uint8_t _val_) { this->frame_reserve_ = _val_; }
  const std::array<uint32_t, 2>& sn() const { return this->sn_; }
  std::array<uint32_t, 2>& sn() { return this->sn_; }
  void sn(const std::array<uint32_t, 2>& _val_) { this->sn_ = _val_; }
  void sn(std::array<uint32_t, 2>&& _val_) { this->sn_ = _val_; }
  const std::array<uint32_t, 2>& version() const { return this->version_; }
  std::array<uint32_t, 2>& version() { return this->version_; }
  void version(const std::array<uint32_t, 2>& _val_) { this->version_ = _val_; }
  void version(std::array<uint32_t, 2>&& _val_) { this->version_ = _val_; }
  uint16_t bandwidth() const { return this->bandwidth_; }
  uint16_t& bandwidth() { return this->bandwidth_; }
  void bandwidth(uint16_t _val_) { this->bandwidth_ = _val_; }
  const ::unitree_go::msg::dds_::IMUState_& imu_state() const { return this->imu_state_; }
  ::unitree_go::msg::dds_::IMUState_& imu_state() { return this->imu_state_; }
  void imu_state(const ::unitree_go::msg::dds_::IMUState_& _val_) { this->imu_state_ = _val_; }
  void imu_state(::unitree_go::msg::dds_::IMUState_&& _val_) { this->imu_state_ = _val_; }
  const std::array<::unitree_go::msg::dds_::MotorState_, 20>& motor_state() const { return this->motor_state_; }
  std::array<::unitree_go::msg::dds_::MotorState_, 20>& motor_state() { return this->motor_state_; }
  void motor_state(const std::array<::unitree_go::msg::dds_::MotorState_, 20>& _val_) { this->motor_state_ = _val_; }
  void motor_state(std::array<::unitree_go::msg::dds_::MotorState_, 20>&& _val_) { this->motor_state_ = _val_; }
  const ::unitree_go::msg::dds_::BmsState_& bms_state() const { return this->bms_state_; }
  ::unitree_go::msg::dds_::BmsState_& bms_state() { return this->bms_state_; }
  void bms_state(const ::unitree_go::msg::dds_::BmsState_& _val_) { this->bms_state_ = _val_; }
  void bms_state(::unitree_go::msg::dds_::BmsState_&& _val_) { this->bms_state_ = _val_; }
  const std::array<int16_t, 4>& foot_force() const { return this->foot_force_; }
  std::array<int16_t, 4>& foot_force() { return this->foot_force_; }
  void foot_force(const std::array<int16_t, 4>& _val_) { this->foot_force_ = _val_; }
  void foot_force(std::array<int16_t, 4>&& _val_) { this->foot_force_ = _val_; }
  const std::array<int16_t, 4>& foot_force_est() const { return this->foot_force_est_; }
  std::array<int16_t, 4>& foot_force_est() { return this->foot_force_est_; }
  void foot_force_est(const std::array<int16_t, 4>& _val_) { this->foot_force_est_ = _val_; }
  void foot_force_est(std::array<int16_t, 4>&& _val_) { this->foot_force_est_ = _val_; }
  uint32_t tick() const { return this->tick_; }
  uint32_t& tick() { return this->tick_; }
  void tick(uint32_t _val_) { this->tick_ = _val_; }
  const std::array<uint8_t, 40>& wireless_remote() const { return this->wireless_remote_; }
  std::array<uint8_t, 40>& wireless_remote() { return this->wireless_remote_; }
  void wireless_remote(const std::array<uint8_t, 40>& _val_) { this->wireless_remote_ = _val_; }
  void wireless_remote(std::array<uint8_t, 40>&& _val_) { this->wireless_remote_ = _val_; }
  uint8_t bit_flag() const { return this->bit_flag_; }
  uint8_t& bit_flag() { return this->bit_flag_; }
  void bit_flag(uint8_t _val_) { this->bit_flag_ = _val_; }
  float adc_reel() const { return this->adc_reel_; }
  float& adc_reel() { return this->adc_reel_; }
  void adc_reel(float _val_) { this->adc_reel_ = _val_; }
  uint8_t temperature_ntc1() const { return this->temperature_ntc1_; }
  uint8_t& temperature_ntc1() { return this->temperature_ntc1_; }
  void temperature_ntc1(uint8_t _val_) { this->temperature_ntc1_ = _val_; }
  uint8_t temperature_ntc2() const { return this->temperature_ntc2_; }
  uint8_t& temperature_ntc2() { return this->temperature_ntc2_; }
  void temperature_ntc2(uint8_t _val_) { this->temperature_ntc2_ = _val_; }
  float power_v() const { return this->power_v_; }
  float& power_v() { return this->power_v_; }
  void power_v(float _val_) { this->power_v_ = _val_; }
  float power_a() const { return this->power_a_; }
  float& power_a() { return this->power_a_; }
  void power_a(float _val_) { this->power_a_ = _val_; }
  const std::array<uint16_t, 4>& fan_frequency() const { return this->fan_frequency_; }
  std::array<uint16_t, 4>& fan_frequency() { return this->fan_frequency_; }
  void fan_frequency(const std::array<uint16_t, 4>& _val_) { this->fan_frequency_ = _val_; }
  void fan_frequency(std::array<uint16_t, 4>&& _val_) { this->fan_frequency_ = _val_; }
  uint32_t reserve() const { return this->reserve_; }
  uint32_t& reserve() { return this->reserve_; }
  void reserve(uint32_t _val_) { this->reserve_ = _val_; }
  uint32_t crc() const { return this->crc_; }
  uint32_t& crc() { return this->crc_; }
  void crc(uint32_t _val_) { this->crc_ = _val_; }

  bool operator==(const LowState_& _other) const
  {
    (void) _other;
    return head_ == _other.head_ &&
      level_flag_ == _other.level_flag_ &&
      frame_reserve_ == _other.frame_reserve_ &&
      sn_ == _other.sn_ &&
      version_ == _other.version_ &&
      bandwidth_ == _other.bandwidth_ &&
      imu_state_ == _other.imu_state_ &&
      motor_state_ == _other.motor_state_ &&
      bms_state_ == _other.bms_state_ &&
      foot_force_ == _other.foot_force_ &&
      foot_force_est_ == _other.foot_force_est_ &&
      tick_ == _other.tick_ &&
      wireless_remote_ == _other.wireless_remote_ &&
      bit_flag_ == _other.bit_flag_ &&
      adc_reel_ == _other.adc_reel_ &&
      temperature_ntc1_ == _other.temperature_ntc1_ &&
      temperature_ntc2_ == _other.temperature_ntc2_ &&
      power_v_ == _other.power_v_ &&
      power_a_ == _other.power_a_ &&
      fan_frequency_ == _other.fan_frequency_ &&
      reserve_ == _other.reserve_ &&
      crc_ == _other.crc_;
  }

  bool operator!=(const LowState_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::LowState_>::getTypeName()
{
  return "unitree_go::msg::dds_::LowState_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::LowState_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::LowState_>::type_map_blob_sz() { return 3184; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::LowState_>::type_info_blob_sz() { return 244; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::LowState_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x7e,  0x04,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0xf1,  0x85,  0x3a,  0x99,  0xe2,  0x3d,  0x0d,  0xaa, 
 0x16,  0x10,  0xb6,  0x5c,  0x52,  0x6b,  0x40,  0x00,  0xff,  0x01,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0xef,  0x01,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x02,  0x02,  0x96,  0xe8,  0x9a,  0x29,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xa2,  0x18,  0x71,  0x24,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xf0,  0xd2,  0xa4,  0x1c,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x07,  0xaf,  0xbe,  0x94,  0xcd,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0x2a,  0xf7, 
 0x2f,  0x10,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x06,  0x36, 
 0x67,  0x6c,  0x2b,  0x00,  0x19,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x4b, 
 0x1b,  0xf4,  0xaf,  0x32,  0xe5,  0xbc,  0x67,  0x10,  0xef,  0xc8,  0x29,  0x31,  0x15,  0x9e,  0x5f,  0x37, 
 0x13,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf1, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x14,  0xf1,  0x42,  0xa2,  0x28,  0x8b,  0x79,  0x8a, 
 0xf8,  0xdb,  0x05,  0xe1,  0x27,  0x36,  0xb1,  0xbe,  0xb2,  0x1e,  0x53,  0xdc,  0x19,  0x00,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0xe1,  0x18,  0xaf,  0x07,  0x0b,  0xa2,  0x0b,  0xc2,  0xe0, 
 0x37,  0x0c,  0xec,  0x4c,  0xbb,  0x7f,  0x6e,  0x62,  0x18,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x04,  0x03,  0x41,  0x56,  0x4b,  0x84,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x03,  0x04,  0x1b, 
 0xa1,  0x76,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0xe5, 
 0xe5,  0xc0,  0x75,  0x00,  0x16,  0x00,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x28,  0x02,  0xa8,  0x2f,  0x18,  0xc9,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x0d,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xc8,  0xbc,  0x01,  0x8f,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xc1,  0x0c,  0x98,  0x67,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x0f,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xb0,  0x29,  0x0b,  0xbb,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xd2,  0x14,  0xc5,  0x1f,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x52,  0xea,  0x3e,  0x50,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x3a,  0x40,  0xba,  0xb9,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x04,  0x06,  0xa6,  0x69,  0xf7,  0x4e,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x15,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0xf5,  0xad,  0x59,  0xc5,  0xf1,  0x4b,  0x1b,  0xf4,  0xaf, 
 0x32,  0xe5,  0xbc,  0x67,  0x10,  0xef,  0xc8,  0x29,  0x31,  0x15,  0x00,  0x00,  0x93,  0x00,  0x00,  0x00, 
 0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x83,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x09,  0x75,  0xb9,  0xaa,  0xa0,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x6b,  0x3b,  0x2d,  0x85,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x09,  0x0e,  0x12,  0x75,  0x69,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0xb0,  0x7d, 
 0x92,  0xed,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xee, 
 0xf4,  0x38,  0xf7,  0xf1,  0x42,  0xa2,  0x28,  0x8b,  0x79,  0x8a,  0xf8,  0xdb,  0x05,  0xe1,  0x27,  0x36, 
 0xb1,  0xbe,  0x00,  0x00,  0xce,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0xbe,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x15,  0xd6,  0x17,  0x12,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x76,  0x94,  0xf4,  0xa6,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x47,  0xbc,  0xdc,  0xd7,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xe9,  0x16,  0x89,  0x09,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x8a,  0xf7,  0xae,  0xdf,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x6e,  0x96,  0x3d,  0x84,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x30,  0x50,  0xb6,  0xd9,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x4a,  0xe0,  0x48,  0xae,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xee,  0xf4,  0x38,  0xf7,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x1c,  0x9a,  0x44,  0xeb,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x0a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0xf1,  0xe1,  0x18,  0xaf,  0x07,  0x0b,  0xa2,  0x0b,  0xc2,  0xe0, 
 0x37,  0x0c,  0xec,  0x4c,  0xbb,  0x00,  0x00,  0x00,  0xc6,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0xb6,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x75,  0x6a,  0xda,  0x84,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x51,  0x99,  0x89,  0x90,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x9a,  0xcb,  0x44,  0x54,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xc7,  0xd5,  0xf8,  0x49,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x04,  0x43,  0xb5,  0xc9,  0x17,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x06,  0x9a,  0x4c,  0x07,  0x40,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x02,  0x02,  0x2a,  0x42,  0xc9,  0x1e,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x02,  0x55,  0x65,  0x52,  0x17,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0f,  0x06,  0x2d,  0x87, 
 0xc3,  0x55,  0x00,  0x00,  0x67,  0x07,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0xf2,  0x08,  0xf2,  0x39, 
 0xc4,  0x71,  0xd3,  0xa2,  0x7e,  0x67,  0xe7,  0xc7,  0x72,  0x31,  0x84,  0x00,  0x52,  0x03,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00, 
 0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a, 
 0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x4c,  0x6f,  0x77,  0x53,  0x74,  0x61,  0x74,  0x65,  0x5f, 
 0x00,  0x00,  0x00,  0x00,  0x1a,  0x03,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x1f,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x02,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x68,  0x65,  0x61,  0x64,  0x00,  0x00,  0x00,  0x00, 
 0x19,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x6c,  0x65,  0x76,  0x65,  0x6c,  0x5f,  0x66,  0x6c,  0x61,  0x67,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x1c,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0e,  0x00,  0x00,  0x00, 
 0x66,  0x72,  0x61,  0x6d,  0x65,  0x5f,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00,  0x00,  0x00, 
 0x1d,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x73,  0x6e,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x76,  0x65,  0x72,  0x73,  0x69,  0x6f,  0x6e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x18,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x06,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x62,  0x61,  0x6e,  0x64, 
 0x77,  0x69,  0x64,  0x74,  0x68,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0xf2,  0xc4,  0xec,  0xb4,  0x9c,  0xd7,  0xb2,  0x25,  0xd2,  0x26,  0x28,  0xb6,  0xb7,  0x64, 
 0xcc,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x69,  0x6d,  0x75,  0x5f,  0x73,  0x74,  0x61,  0x74, 
 0x65,  0x00,  0x00,  0x00,  0x32,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf2, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x14,  0xf2,  0xd2,  0xbc,  0xfb,  0x97,  0xbe,  0x37, 
 0xa8,  0xbc,  0x9c,  0x41,  0xe5,  0x0b,  0xa1,  0xc9,  0x0c,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x74,  0x6f, 
 0x72,  0x5f,  0x73,  0x74,  0x61,  0x74,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x2e,  0xd8,  0x57,  0xec,  0xb6,  0xb3,  0x05,  0x79,  0x80, 
 0x8c,  0x15,  0xfd,  0x14,  0x3d,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x62,  0x6d,  0x73,  0x5f, 
 0x73,  0x74,  0x61,  0x74,  0x65,  0x00,  0x00,  0x00,  0x25,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x03,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x66,  0x6f,  0x6f,  0x74,  0x5f,  0x66,  0x6f,  0x72,  0x63,  0x65,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x29,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x03,  0x00,  0x00,  0x0f,  0x00,  0x00,  0x00, 
 0x66,  0x6f,  0x6f,  0x74,  0x5f,  0x66,  0x6f,  0x72,  0x63,  0x65,  0x5f,  0x65,  0x73,  0x74,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x74,  0x69,  0x63,  0x6b,  0x00,  0x00,  0x00,  0x00,  0x2a,  0x00,  0x00,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x28,  0x02,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x77,  0x69,  0x72,  0x65,  0x6c,  0x65,  0x73,  0x73, 
 0x5f,  0x72,  0x65,  0x6d,  0x6f,  0x74,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x17,  0x00,  0x00,  0x00, 
 0x0d,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x09,  0x00,  0x00,  0x00,  0x62,  0x69,  0x74,  0x5f, 
 0x66,  0x6c,  0x61,  0x67,  0x00,  0x00,  0x00,  0x00,  0x17,  0x00,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x09,  0x00,  0x00,  0x00,  0x61,  0x64,  0x63,  0x5f,  0x72,  0x65,  0x65,  0x6c, 
 0x00,  0x00,  0x00,  0x00,  0x1f,  0x00,  0x00,  0x00,  0x0f,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x74,  0x65,  0x6d,  0x70,  0x65,  0x72,  0x61,  0x74,  0x75,  0x72,  0x65,  0x5f, 
 0x6e,  0x74,  0x63,  0x31,  0x00,  0x00,  0x00,  0x00,  0x1f,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x00,  0x11,  0x00,  0x00,  0x00,  0x74,  0x65,  0x6d,  0x70,  0x65,  0x72,  0x61,  0x74, 
 0x75,  0x72,  0x65,  0x5f,  0x6e,  0x74,  0x63,  0x32,  0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x08,  0x00,  0x00,  0x00,  0x70,  0x6f,  0x77,  0x65, 
 0x72,  0x5f,  0x76,  0x00,  0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x08,  0x00,  0x00,  0x00,  0x70,  0x6f,  0x77,  0x65,  0x72,  0x5f,  0x61,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x06,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00, 
 0x66,  0x61,  0x6e,  0x5f,  0x66,  0x72,  0x65,  0x71,  0x75,  0x65,  0x6e,  0x63,  0x79,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00, 
 0x15,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00,  0x04,  0x00,  0x00,  0x00,  0x63,  0x72,  0x63,  0x00, 
 0x00,  0x00,  0xf2,  0xc4,  0xec,  0xb4,  0x9c,  0xd7,  0xb2,  0x25,  0xd2,  0x26,  0x28,  0xb6,  0xb7,  0x64, 
 0xcc,  0x00,  0x00,  0x00,  0xfe,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f, 
 0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x49, 
 0x4d,  0x55,  0x53,  0x74,  0x61,  0x74,  0x65,  0x5f,  0x00,  0x00,  0x00,  0x00,  0xc6,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x25,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x09,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x71,  0x75,  0x61,  0x74,  0x65,  0x72,  0x6e,  0x69,  0x6f,  0x6e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x24,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x67,  0x79,  0x72,  0x6f, 
 0x73,  0x63,  0x6f,  0x70,  0x65,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00, 
 0x0e,  0x00,  0x00,  0x00,  0x61,  0x63,  0x63,  0x65,  0x6c,  0x65,  0x72,  0x6f,  0x6d,  0x65,  0x74,  0x65, 
 0x72,  0x00,  0x00,  0x00,  0x1e,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x72,  0x70,  0x79,  0x00,  0x00,  0x00,  0x00,  0x00,  0x1a,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x74,  0x65,  0x6d,  0x70,  0x65,  0x72,  0x61,  0x74, 
 0x75,  0x72,  0x65,  0x00,  0x00,  0x00,  0xf2,  0xd2,  0xbc,  0xfb,  0x97,  0xbe,  0x37,  0xa8,  0xbc,  0x9c, 
 0x41,  0xe5,  0x0b,  0xa1,  0xc9,  0x00,  0x00,  0x00,  0x62,  0x01,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00, 
 0x2b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74, 
 0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73, 
 0x5f,  0x3a,  0x3a,  0x4d,  0x6f,  0x74,  0x6f,  0x72,  0x53,  0x74,  0x61,  0x74,  0x65,  0x5f,  0x00,  0x00, 
 0x2a,  0x01,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x00,  0x05,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x64,  0x65,  0x00,  0x00,  0x00,  0x00, 
 0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x71,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x64,  0x71,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x04,  0x00,  0x00,  0x00,  0x64,  0x64,  0x71,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x74,  0x61,  0x75,  0x5f,  0x65,  0x73,  0x74,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x71,  0x5f,  0x72,  0x61,  0x77,  0x00,  0x00,  0x00,  0x15,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x07,  0x00,  0x00,  0x00,  0x64,  0x71,  0x5f,  0x72,  0x61,  0x77,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x64,  0x64,  0x71,  0x5f,  0x72,  0x61,  0x77,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x1a,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0c,  0x00,  0x00,  0x00, 
 0x74,  0x65,  0x6d,  0x70,  0x65,  0x72,  0x61,  0x74,  0x75,  0x72,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x13,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x6c,  0x6f,  0x73,  0x74,  0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0xf2,  0x2e, 
 0xd8,  0x57,  0xec,  0xb6,  0xb3,  0x05,  0x79,  0x80,  0x8c,  0x15,  0xfd,  0x14,  0x3d,  0x00,  0x00,  0x00, 
 0x5b,  0x01,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x21,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a, 
 0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x42,  0x6d,  0x73,  0x53,  0x74, 
 0x61,  0x74,  0x65,  0x5f,  0x00,  0x00,  0x00,  0x00,  0x23,  0x01,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x1b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0d,  0x00,  0x00,  0x00, 
 0x76,  0x65,  0x72,  0x73,  0x69,  0x6f,  0x6e,  0x5f,  0x68,  0x69,  0x67,  0x68,  0x00,  0x00,  0x00,  0x00, 
 0x1a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0c,  0x00,  0x00,  0x00, 
 0x76,  0x65,  0x72,  0x73,  0x69,  0x6f,  0x6e,  0x5f,  0x6c,  0x6f,  0x77,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x15,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x73,  0x74,  0x61,  0x74,  0x75,  0x73,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x04,  0x00,  0x00,  0x00,  0x73,  0x6f,  0x63,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x04,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x63,  0x75,  0x72,  0x72,  0x65,  0x6e,  0x74,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x06,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x63,  0x79,  0x63,  0x6c,  0x65,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x02,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x62,  0x71,  0x5f,  0x6e,  0x74,  0x63,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x22,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x02,  0x02,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x6d,  0x63,  0x75,  0x5f, 
 0x6e,  0x74,  0x63,  0x00,  0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0f,  0x06,  0x00,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x63,  0x65,  0x6c,  0x6c,  0x5f,  0x76,  0x6f,  0x6c,  0x00,  0x00,  0x00,  0x00, 
 0x7c,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0xf2,  0x08,  0xf2,  0x39,  0xc4,  0x71,  0xd3,  0xa2, 
 0x7e,  0x67,  0xe7,  0xc7,  0x72,  0x31,  0x84,  0xf1,  0x85,  0x3a,  0x99,  0xe2,  0x3d,  0x0d,  0xaa,  0x16, 
 0x10,  0xb6,  0x5c,  0x52,  0x6b,  0x40,  0xf2,  0xc4,  0xec,  0xb4,  0x9c,  0xd7,  0xb2,  0x25,  0xd2,  0x26, 
 0x28,  0xb6,  0xb7,  0x64,  0xcc,  0xf1,  0x4b,  0x1b,  0xf4,  0xaf,  0x32,  0xe5,  0xbc,  0x67,  0x10,  0xef, 
 0xc8,  0x29,  0x31,  0x15,  0xf2,  0xd2,  0xbc,  0xfb,  0x97,  0xbe,  0x37,  0xa8,  0xbc,  0x9c,  0x41,  0xe5, 
 0x0b,  0xa1,  0xc9,  0xf1,  0x42,  0xa2,  0x28,  0x8b,  0x79,  0x8a,  0xf8,  0xdb,  0x05,  0xe1,  0x27,  0x36, 
 0xb1,  0xbe,  0xf2,  0x2e,  0xd8,  0x57,  0xec,  0xb6,  0xb3,  0x05,  0x79,  0x80,  0x8c,  0x15,  0xfd,  0x14, 
 0x3d,  0xf1,  0xe1,  0x18,  0xaf,  0x07,  0x0b,  0xa2,  0x0b,  0xc2,  0xe0,  0x37,  0x0c,  0xec,  0x4c,  0xbb, 
};
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::LowState_>::type_info_blob() {
  static const uint8_t blob[] = {
 0xf0,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x70,  0x00,  0x00,  0x00,  0x6c,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x85,  0x3a,  0x99,  0xe2,  0x3d,  0x0d,  0xaa,  0x16,  0x10,  0xb6,  0x5c, 
 0x52,  0x6b,  0x40,  0x00,  0x03,  0x02,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x4c,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0x4b,  0x1b,  0xf4,  0xaf,  0x32,  0xe5,  0xbc, 
 0x67,  0x10,  0xef,  0xc8,  0x29,  0x31,  0x15,  0x00,  0x97,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf1,  0x42,  0xa2,  0x28,  0x8b,  0x79,  0x8a,  0xf8,  0xdb,  0x05,  0xe1,  0x27,  0x36,  0xb1,  0xbe,  0x00, 
 0xd2,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0xe1,  0x18,  0xaf,  0x07,  0x0b,  0xa2,  0x0b, 
 0xc2,  0xe0,  0x37,  0x0c,  0xec,  0x4c,  0xbb,  0x00,  0xca,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40, 
 0x70,  0x00,  0x00,  0x00,  0x6c,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0x08,  0xf2,  0x39, 
 0xc4,  0x71,  0xd3,  0xa2,  0x7e,  0x67,  0xe7,  0xc7,  0x72,  0x31,  0x84,  0x00,  0x56,  0x03,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x4c,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0xc4,  0xec,  0xb4,  0x9c,  0xd7,  0xb2,  0x25,  0xd2,  0x26,  0x28,  0xb6,  0xb7,  0x64,  0xcc,  0x00, 
 0x02,  0x01,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0xd2,  0xbc,  0xfb,  0x97,  0xbe,  0x37,  0xa8, 
 0xbc,  0x9c,  0x41,  0xe5,  0x0b,  0xa1,  0xc9,  0x00,  0x66,  0x01,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0x2e,  0xd8,  0x57,  0xec,  0xb6,  0xb3,  0x05,  0x79,  0x80,  0x8c,  0x15,  0xfd,  0x14,  0x3d,  0x00, 
 0x5f,  0x01,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::LowState_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::LowState_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::LowState_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::LowState_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::LowState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.head()[0], instance.head().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.level_flag()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.frame_reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.sn()[0], instance.sn().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.bandwidth()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.motor_state()) {  //array depth 1
      if (!write(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.bms_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.foot_force()[0], instance.foot_force().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.foot_force_est()[0], instance.foot_force_est().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.tick()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.wireless_remote()[0], instance.wireless_remote().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.bit_flag()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.adc_reel()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.temperature_ntc1()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 16:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.temperature_ntc2()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 17:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.power_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 18:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.power_a()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 19:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.fan_frequency()[0], instance.fan_frequency().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 20:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 21:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::LowState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::LowState_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::LowState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.head()[0], instance.head().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.level_flag()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.frame_reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.sn()[0], instance.sn().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.bandwidth()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (auto & a_1:instance.motor_state()) {  //array depth 1
      if (!read(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.bms_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.foot_force()[0], instance.foot_force().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.foot_force_est()[0], instance.foot_force_est().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.tick()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.wireless_remote()[0], instance.wireless_remote().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.bit_flag()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.adc_reel()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.temperature_ntc1()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 16:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.temperature_ntc2()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 17:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.power_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 18:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.power_a()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 19:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.fan_frequency()[0], instance.fan_frequency().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 20:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 21:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::LowState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::LowState_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::LowState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.head()[0], instance.head().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.level_flag()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.frame_reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.sn()[0], instance.sn().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.bandwidth()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.motor_state()) {  //array depth 1
      if (!move(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.bms_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.foot_force()[0], instance.foot_force().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.foot_force_est()[0], instance.foot_force_est().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.tick()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.wireless_remote()[0], instance.wireless_remote().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.bit_flag()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.adc_reel()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.temperature_ntc1()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 16:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.temperature_ntc2()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 17:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.power_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 18:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.power_a()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 19:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.fan_frequency()[0], instance.fan_frequency().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 20:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 21:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::LowState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::LowState_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::LowState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.head()[0], instance.head().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.level_flag()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.frame_reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.sn()[0], instance.sn().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.bandwidth()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.motor_state()) {  //array depth 1
      if (!max(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.bms_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.foot_force()[0], instance.foot_force().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.foot_force_est()[0], instance.foot_force_est().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.tick()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.wireless_remote()[0], instance.wireless_remote().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.bit_flag()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.adc_reel()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.temperature_ntc1()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 16:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.temperature_ntc2()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 17:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.power_v()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 18:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.power_a()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 19:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.fan_frequency()[0], instance.fan_frequency().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 20:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 21:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::LowState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::LowState_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_LOWSTATE__HPP
