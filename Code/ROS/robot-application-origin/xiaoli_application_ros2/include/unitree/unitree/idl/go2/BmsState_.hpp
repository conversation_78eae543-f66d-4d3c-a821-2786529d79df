/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: BmsState_.idl
  Source: BmsState_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_BMSSTATE__HPP
#define DDSCXX_UNITREE_IDL_GO2_BMSSTATE__HPP

#include <cstdint>
#include <array>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class BmsState_
{
private:
 uint8_t version_high_ = 0;
 uint8_t version_low_ = 0;
 uint8_t status_ = 0;
 uint8_t soc_ = 0;
 int32_t current_ = 0;
 uint16_t cycle_ = 0;
 std::array<uint8_t, 2> bq_ntc_ = { };
 std::array<uint8_t, 2> mcu_ntc_ = { };
 std::array<uint16_t, 15> cell_vol_ = { };

public:
  BmsState_() = default;

  explicit BmsState_(
    uint8_t version_high,
    uint8_t version_low,
    uint8_t status,
    uint8_t soc,
    int32_t current,
    uint16_t cycle,
    const std::array<uint8_t, 2>& bq_ntc,
    const std::array<uint8_t, 2>& mcu_ntc,
    const std::array<uint16_t, 15>& cell_vol) :
    version_high_(version_high),
    version_low_(version_low),
    status_(status),
    soc_(soc),
    current_(current),
    cycle_(cycle),
    bq_ntc_(bq_ntc),
    mcu_ntc_(mcu_ntc),
    cell_vol_(cell_vol) { }

  uint8_t version_high() const { return this->version_high_; }
  uint8_t& version_high() { return this->version_high_; }
  void version_high(uint8_t _val_) { this->version_high_ = _val_; }
  uint8_t version_low() const { return this->version_low_; }
  uint8_t& version_low() { return this->version_low_; }
  void version_low(uint8_t _val_) { this->version_low_ = _val_; }
  uint8_t status() const { return this->status_; }
  uint8_t& status() { return this->status_; }
  void status(uint8_t _val_) { this->status_ = _val_; }
  uint8_t soc() const { return this->soc_; }
  uint8_t& soc() { return this->soc_; }
  void soc(uint8_t _val_) { this->soc_ = _val_; }
  int32_t current() const { return this->current_; }
  int32_t& current() { return this->current_; }
  void current(int32_t _val_) { this->current_ = _val_; }
  uint16_t cycle() const { return this->cycle_; }
  uint16_t& cycle() { return this->cycle_; }
  void cycle(uint16_t _val_) { this->cycle_ = _val_; }
  const std::array<uint8_t, 2>& bq_ntc() const { return this->bq_ntc_; }
  std::array<uint8_t, 2>& bq_ntc() { return this->bq_ntc_; }
  void bq_ntc(const std::array<uint8_t, 2>& _val_) { this->bq_ntc_ = _val_; }
  void bq_ntc(std::array<uint8_t, 2>&& _val_) { this->bq_ntc_ = _val_; }
  const std::array<uint8_t, 2>& mcu_ntc() const { return this->mcu_ntc_; }
  std::array<uint8_t, 2>& mcu_ntc() { return this->mcu_ntc_; }
  void mcu_ntc(const std::array<uint8_t, 2>& _val_) { this->mcu_ntc_ = _val_; }
  void mcu_ntc(std::array<uint8_t, 2>&& _val_) { this->mcu_ntc_ = _val_; }
  const std::array<uint16_t, 15>& cell_vol() const { return this->cell_vol_; }
  std::array<uint16_t, 15>& cell_vol() { return this->cell_vol_; }
  void cell_vol(const std::array<uint16_t, 15>& _val_) { this->cell_vol_ = _val_; }
  void cell_vol(std::array<uint16_t, 15>&& _val_) { this->cell_vol_ = _val_; }

  bool operator==(const BmsState_& _other) const
  {
    (void) _other;
    return version_high_ == _other.version_high_ &&
      version_low_ == _other.version_low_ &&
      status_ == _other.status_ &&
      soc_ == _other.soc_ &&
      current_ == _other.current_ &&
      cycle_ == _other.cycle_ &&
      bq_ntc_ == _other.bq_ntc_ &&
      mcu_ntc_ == _other.mcu_ntc_ &&
      cell_vol_ == _other.cell_vol_;
  }

  bool operator!=(const BmsState_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::BmsState_>::getTypeName()
{
  return "unitree_go::msg::dds_::BmsState_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::BmsState_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::BmsState_>::type_map_blob_sz() { return 642; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::BmsState_>::type_info_blob_sz() { return 100; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::BmsState_>::type_map_blob() {
  static const uint8_t blob[] = {
 0xde,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf1,  0xe1,  0x18,  0xaf,  0x07,  0x0b,  0xa2,  0x0b, 
 0xc2,  0xe0,  0x37,  0x0c,  0xec,  0x4c,  0xbb,  0x00,  0xc6,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0xb6,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x75,  0x6a,  0xda,  0x84,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x51,  0x99,  0x89,  0x90,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x9a,  0xcb,  0x44,  0x54,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xc7,  0xd5,  0xf8,  0x49,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x04,  0x43,  0xb5,  0xc9,  0x17,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x06,  0x9a,  0x4c,  0x07,  0x40,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x02,  0x02,  0x2a,  0x42,  0xc9,  0x1e,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x02,  0x55,  0x65,  0x52,  0x17,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0f,  0x06,  0x2d,  0x87, 
 0xc3,  0x55,  0x00,  0x00,  0x73,  0x01,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0x2e,  0xd8,  0x57, 
 0xec,  0xb6,  0xb3,  0x05,  0x79,  0x80,  0x8c,  0x15,  0xfd,  0x14,  0x3d,  0x00,  0x5b,  0x01,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00, 
 0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a, 
 0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x42,  0x6d,  0x73,  0x53,  0x74,  0x61,  0x74,  0x65,  0x5f, 
 0x00,  0x00,  0x00,  0x00,  0x23,  0x01,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x1b,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0d,  0x00,  0x00,  0x00,  0x76,  0x65,  0x72,  0x73, 
 0x69,  0x6f,  0x6e,  0x5f,  0x68,  0x69,  0x67,  0x68,  0x00,  0x00,  0x00,  0x00,  0x1a,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x76,  0x65,  0x72,  0x73, 
 0x69,  0x6f,  0x6e,  0x5f,  0x6c,  0x6f,  0x77,  0x00,  0x00,  0x00,  0x00,  0x00,  0x15,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x07,  0x00,  0x00,  0x00,  0x73,  0x74,  0x61,  0x74, 
 0x75,  0x73,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x00,  0x04,  0x00,  0x00,  0x00,  0x73,  0x6f,  0x63,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x04,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x63,  0x75,  0x72,  0x72,  0x65,  0x6e,  0x74,  0x00,  0x00,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x06,  0x00,  0x06,  0x00,  0x00,  0x00,  0x63,  0x79,  0x63,  0x6c, 
 0x65,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x02,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x62,  0x71,  0x5f,  0x6e,  0x74,  0x63,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x02,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x6d,  0x63,  0x75,  0x5f,  0x6e,  0x74,  0x63,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0f,  0x06,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x63,  0x65,  0x6c,  0x6c,  0x5f,  0x76,  0x6f,  0x6c,  0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0xf2,  0x2e,  0xd8,  0x57,  0xec,  0xb6,  0xb3,  0x05,  0x79,  0x80,  0x8c,  0x15, 
 0xfd,  0x14,  0x3d,  0xf1,  0xe1,  0x18,  0xaf,  0x07,  0x0b,  0xa2,  0x0b,  0xc2,  0xe0,  0x37,  0x0c,  0xec, 
 0x4c,  0xbb, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::BmsState_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x60,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0xe1,  0x18,  0xaf,  0x07,  0x0b,  0xa2,  0x0b,  0xc2,  0xe0,  0x37,  0x0c, 
 0xec,  0x4c,  0xbb,  0x00,  0xca,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0x2e,  0xd8,  0x57,  0xec,  0xb6,  0xb3,  0x05,  0x79,  0x80,  0x8c,  0x15, 
 0xfd,  0x14,  0x3d,  0x00,  0x5f,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::BmsState_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::BmsState_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::BmsState_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::BmsState_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::BmsState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.version_high()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.version_low()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.status()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.soc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.current()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.cycle()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.bq_ntc()[0], instance.bq_ntc().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.mcu_ntc()[0], instance.mcu_ntc().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.cell_vol()[0], instance.cell_vol().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::BmsState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::BmsState_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::BmsState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.version_high()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.version_low()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.status()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.soc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.current()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.cycle()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.bq_ntc()[0], instance.bq_ntc().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.mcu_ntc()[0], instance.mcu_ntc().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.cell_vol()[0], instance.cell_vol().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::BmsState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::BmsState_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::BmsState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.version_high()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.version_low()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.status()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.soc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.current()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.cycle()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.bq_ntc()[0], instance.bq_ntc().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.mcu_ntc()[0], instance.mcu_ntc().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.cell_vol()[0], instance.cell_vol().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::BmsState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::BmsState_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::BmsState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.version_high()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.version_low()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.status()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.soc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.current()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.cycle()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.bq_ntc()[0], instance.bq_ntc().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.mcu_ntc()[0], instance.mcu_ntc().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.cell_vol()[0], instance.cell_vol().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::BmsState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::BmsState_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_BMSSTATE__HPP
