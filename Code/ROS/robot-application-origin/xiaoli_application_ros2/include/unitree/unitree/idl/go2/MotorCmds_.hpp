/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: MotorCmds_.idl
  Source: MotorCmds_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_MOTORCMDS__HPP
#define DDSCXX_UNITREE_IDL_GO2_MOTORCMDS__HPP

#include "unitree/idl/go2/MotorCmd_.hpp"

#include <vector>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class MotorCmds_
{
private:
 std::vector<::unitree_go::msg::dds_::MotorCmd_> cmds_;

public:
  MotorCmds_() = default;

  explicit MotorCmds_(
    const std::vector<::unitree_go::msg::dds_::MotorCmd_>& cmds) :
    cmds_(cmds) { }

  const std::vector<::unitree_go::msg::dds_::MotorCmd_>& cmds() const { return this->cmds_; }
  std::vector<::unitree_go::msg::dds_::MotorCmd_>& cmds() { return this->cmds_; }
  void cmds(const std::vector<::unitree_go::msg::dds_::MotorCmd_>& _val_) { this->cmds_ = _val_; }
  void cmds(std::vector<::unitree_go::msg::dds_::MotorCmd_>&& _val_) { this->cmds_ = _val_; }

  bool operator==(const MotorCmds_& _other) const
  {
    (void) _other;
    return cmds_ == _other.cmds_;
  }

  bool operator!=(const MotorCmds_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::MotorCmds_>::getTypeName()
{
  return "unitree_go::msg::dds_::MotorCmds_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::MotorCmds_>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::MotorCmds_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::MotorCmds_>::type_map_blob_sz() { return 708; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::MotorCmds_>::type_info_blob_sz() { return 148; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::MotorCmds_>::type_map_blob() {
  static const uint8_t blob[] = {
 0xf2,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0xf1,  0x8b,  0xdd,  0x20,  0x7f,  0x54,  0xf9,  0xc2, 
 0xac,  0x2e,  0xa6,  0xb2,  0xa0,  0x48,  0x2e,  0x00,  0x36,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x26,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x1e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf1,  0x01,  0x00,  0x00,  0xf1, 
 0xc8,  0xe5,  0x83,  0x5b,  0x0f,  0x0c,  0x17,  0x65,  0xbc,  0x23,  0x1d,  0xfa,  0x02,  0xff,  0xb0,  0x2e, 
 0x95,  0x33,  0xf1,  0xc8,  0xe5,  0x83,  0x5b,  0x0f,  0x0c,  0x17,  0x65,  0xbc,  0x23,  0x1d,  0xfa,  0x02, 
 0xff,  0x00,  0x00,  0x00,  0x8e,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x7e,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x15,  0xd6,  0x17,  0x12,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x76,  0x94,  0xf4,  0xa6,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x47,  0xbc,  0xdc,  0xd7,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x45,  0x80,  0xc2,  0x74,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x26,  0xb5,  0x68,  0xe4,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x87,  0x22,  0x16,  0x52,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0x00,  0x00,  0x82,  0x01,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0xf2,  0x2e,  0xa8,  0x7a,  0x3a,  0xc2,  0x22,  0xec,  0x38,  0x65,  0x96,  0x03,  0x6e,  0xce,  0x43,  0x00, 
 0x67,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x2a,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x22,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a, 
 0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x4d,  0x6f,  0x74,  0x6f,  0x72, 
 0x43,  0x6d,  0x64,  0x73,  0x5f,  0x00,  0x00,  0x00,  0x2f,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x27,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf2,  0x01,  0x00,  0x00,  0xf2, 
 0x42,  0x1f,  0xa5,  0xb2,  0xdb,  0x2d,  0xef,  0xf5,  0xc0,  0x3f,  0xff,  0x16,  0x30,  0x3b,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x63,  0x6d,  0x64,  0x73,  0x00,  0x00,  0x00,  0xf2,  0x42,  0x1f,  0xa5,  0xb2, 
 0xdb,  0x2d,  0xef,  0xf5,  0xc0,  0x3f,  0xff,  0x16,  0x30,  0x3b,  0x00,  0x00,  0xee,  0x00,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00, 
 0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a, 
 0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x4d,  0x6f,  0x74,  0x6f,  0x72,  0x43,  0x6d,  0x64,  0x5f, 
 0x00,  0x00,  0x00,  0x00,  0xb6,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x05,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x64,  0x65, 
 0x00,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x71,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00,  0x64,  0x71,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x12,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x74,  0x61,  0x75,  0x00,  0x00,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00,  0x6b,  0x70,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x6b,  0x64,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x07,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x40,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0xf2,  0x2e,  0xa8,  0x7a,  0x3a,  0xc2,  0x22,  0xec, 
 0x38,  0x65,  0x96,  0x03,  0x6e,  0xce,  0x43,  0xf1,  0x8b,  0xdd,  0x20,  0x7f,  0x54,  0xf9,  0xc2,  0xac, 
 0x2e,  0xa6,  0xb2,  0xa0,  0x48,  0x2e,  0xf2,  0x42,  0x1f,  0xa5,  0xb2,  0xdb,  0x2d,  0xef,  0xf5,  0xc0, 
 0x3f,  0xff,  0x16,  0x30,  0x3b,  0xf1,  0xc8,  0xe5,  0x83,  0x5b,  0x0f,  0x0c,  0x17,  0x65,  0xbc,  0x23, 
 0x1d,  0xfa,  0x02,  0xff, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::MotorCmds_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x90,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x40,  0x00,  0x00,  0x00,  0x3c,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x8b,  0xdd,  0x20,  0x7f,  0x54,  0xf9,  0xc2,  0xac,  0x2e,  0xa6,  0xb2, 
 0xa0,  0x48,  0x2e,  0x00,  0x3a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x1c,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0xc8,  0xe5,  0x83,  0x5b,  0x0f,  0x0c,  0x17, 
 0x65,  0xbc,  0x23,  0x1d,  0xfa,  0x02,  0xff,  0x00,  0x92,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40, 
 0x40,  0x00,  0x00,  0x00,  0x3c,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0x2e,  0xa8,  0x7a, 
 0x3a,  0xc2,  0x22,  0xec,  0x38,  0x65,  0x96,  0x03,  0x6e,  0xce,  0x43,  0x00,  0x6b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x1c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0x42,  0x1f,  0xa5,  0xb2,  0xdb,  0x2d,  0xef,  0xf5,  0xc0,  0x3f,  0xff,  0x16,  0x30,  0x3b,  0x00, 
 0xf2,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::MotorCmds_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::MotorCmds_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::MotorCmds_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::MotorCmds_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::MotorCmds_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.cmds().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.cmds()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::MotorCmds_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::MotorCmds_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::MotorCmds_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.cmds().size());
      if (!read(streamer, se_1))
        return false;
      instance.cmds().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.cmds()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::MotorCmds_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::MotorCmds_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::MotorCmds_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.cmds().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.cmds()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::MotorCmds_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::MotorCmds_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::MotorCmds_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.cmds()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::MotorCmds_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::MotorCmds_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_MOTORCMDS__HPP
