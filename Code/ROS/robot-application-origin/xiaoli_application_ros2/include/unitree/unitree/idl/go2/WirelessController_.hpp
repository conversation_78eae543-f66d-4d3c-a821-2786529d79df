/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: WirelessController_.idl
  Source: WirelessController_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_WIRELESSCONTROLLER__HPP
#define DDSCXX_UNITREE_IDL_GO2_WIRELESSCONTROLLER__HPP

#include <cstdint>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class WirelessController_
{
private:
 float lx_ = 0.0f;
 float ly_ = 0.0f;
 float rx_ = 0.0f;
 float ry_ = 0.0f;
 uint16_t keys_ = 0;

public:
  WirelessController_() = default;

  explicit WirelessController_(
    float lx,
    float ly,
    float rx,
    float ry,
    uint16_t keys) :
    lx_(lx),
    ly_(ly),
    rx_(rx),
    ry_(ry),
    keys_(keys) { }

  float lx() const { return this->lx_; }
  float& lx() { return this->lx_; }
  void lx(float _val_) { this->lx_ = _val_; }
  float ly() const { return this->ly_; }
  float& ly() { return this->ly_; }
  void ly(float _val_) { this->ly_ = _val_; }
  float rx() const { return this->rx_; }
  float& rx() { return this->rx_; }
  void rx(float _val_) { this->rx_ = _val_; }
  float ry() const { return this->ry_; }
  float& ry() { return this->ry_; }
  void ry(float _val_) { this->ry_ = _val_; }
  uint16_t keys() const { return this->keys_; }
  uint16_t& keys() { return this->keys_; }
  void keys(uint16_t _val_) { this->keys_ = _val_; }

  bool operator==(const WirelessController_& _other) const
  {
    (void) _other;
    return lx_ == _other.lx_ &&
      ly_ == _other.ly_ &&
      rx_ == _other.rx_ &&
      ry_ == _other.ry_ &&
      keys_ == _other.keys_;
  }

  bool operator!=(const WirelessController_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::WirelessController_>::getTypeName()
{
  return "unitree_go::msg::dds_::WirelessController_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::WirelessController_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::WirelessController_>::type_map_blob_sz() { return 382; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::WirelessController_>::type_info_blob_sz() { return 100; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::WirelessController_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x7b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf1,  0xd8,  0xdc,  0x69,  0xff,  0xf5,  0xd7,  0x59, 
 0x61,  0xcd,  0x0e,  0x3f,  0xc0,  0x49,  0xcb,  0x00,  0x63,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x53,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x80,  0x37,  0xcc,  0xea,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xe7,  0x28,  0xb4,  0x77,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x22,  0x77,  0x0f,  0xbd,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x73,  0x1a,  0x58,  0x83,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x06,  0x14,  0xf8,  0x02,  0xe1,  0x00, 
 0xd3,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0xe9,  0x68,  0x6f,  0xe7,  0x6f,  0x7b,  0x08, 
 0xf9,  0x2c,  0xb2,  0x49,  0x43,  0xac,  0x72,  0x00,  0xbb,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00, 
 0x33,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x2b,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74, 
 0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73, 
 0x5f,  0x3a,  0x3a,  0x57,  0x69,  0x72,  0x65,  0x6c,  0x65,  0x73,  0x73,  0x43,  0x6f,  0x6e,  0x74,  0x72, 
 0x6f,  0x6c,  0x6c,  0x65,  0x72,  0x5f,  0x00,  0x00,  0x7b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x6c,  0x78,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00,  0x6c,  0x79,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x72,  0x78,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00,  0x72,  0x79,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x13,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x06,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x6b,  0x65,  0x79,  0x73,  0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0xf2,  0xe9,  0x68,  0x6f,  0xe7,  0x6f,  0x7b,  0x08,  0xf9,  0x2c,  0xb2,  0x49,  0x43,  0xac,  0x72,  0xf1, 
 0xd8,  0xdc,  0x69,  0xff,  0xf5,  0xd7,  0x59,  0x61,  0xcd,  0x0e,  0x3f,  0xc0,  0x49,  0xcb, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::WirelessController_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x60,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0xd8,  0xdc,  0x69,  0xff,  0xf5,  0xd7,  0x59,  0x61,  0xcd,  0x0e,  0x3f, 
 0xc0,  0x49,  0xcb,  0x00,  0x67,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0xe9,  0x68,  0x6f,  0xe7,  0x6f,  0x7b,  0x08,  0xf9,  0x2c,  0xb2,  0x49, 
 0x43,  0xac,  0x72,  0x00,  0xbf,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::WirelessController_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::WirelessController_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::WirelessController_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::WirelessController_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::WirelessController_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.lx()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.ly()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.rx()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.ry()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.keys()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::WirelessController_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::WirelessController_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::WirelessController_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.lx()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.ly()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.rx()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.ry()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.keys()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::WirelessController_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::WirelessController_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::WirelessController_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.lx()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.ly()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.rx()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.ry()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.keys()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::WirelessController_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::WirelessController_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::WirelessController_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.lx()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.ly()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.rx()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.ry()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.keys()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::WirelessController_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::WirelessController_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_WIRELESSCONTROLLER__HPP
