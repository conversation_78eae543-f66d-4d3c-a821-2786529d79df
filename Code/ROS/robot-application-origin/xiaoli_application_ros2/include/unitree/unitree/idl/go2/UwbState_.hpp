/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: UwbState_.idl
  Source: UwbState_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_UWBSTATE__HPP
#define DDSCXX_UNITREE_IDL_GO2_UWBSTATE__HPP

#include <cstdint>
#include <array>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class UwbState_
{
private:
 std::array<uint8_t, 2> version_ = { };
 uint8_t channel_ = 0;
 uint8_t joy_mode_ = 0;
 float orientation_est_ = 0.0f;
 float pitch_est_ = 0.0f;
 float distance_est_ = 0.0f;
 float yaw_est_ = 0.0f;
 float tag_roll_ = 0.0f;
 float tag_pitch_ = 0.0f;
 float tag_yaw_ = 0.0f;
 float base_roll_ = 0.0f;
 float base_pitch_ = 0.0f;
 float base_yaw_ = 0.0f;
 std::array<float, 2> joystick_ = { };
 uint8_t error_state_ = 0;
 uint8_t buttons_ = 0;
 uint8_t enabled_from_app_ = 0;

public:
  UwbState_() = default;

  explicit UwbState_(
    const std::array<uint8_t, 2>& version,
    uint8_t channel,
    uint8_t joy_mode,
    float orientation_est,
    float pitch_est,
    float distance_est,
    float yaw_est,
    float tag_roll,
    float tag_pitch,
    float tag_yaw,
    float base_roll,
    float base_pitch,
    float base_yaw,
    const std::array<float, 2>& joystick,
    uint8_t error_state,
    uint8_t buttons,
    uint8_t enabled_from_app) :
    version_(version),
    channel_(channel),
    joy_mode_(joy_mode),
    orientation_est_(orientation_est),
    pitch_est_(pitch_est),
    distance_est_(distance_est),
    yaw_est_(yaw_est),
    tag_roll_(tag_roll),
    tag_pitch_(tag_pitch),
    tag_yaw_(tag_yaw),
    base_roll_(base_roll),
    base_pitch_(base_pitch),
    base_yaw_(base_yaw),
    joystick_(joystick),
    error_state_(error_state),
    buttons_(buttons),
    enabled_from_app_(enabled_from_app) { }

  const std::array<uint8_t, 2>& version() const { return this->version_; }
  std::array<uint8_t, 2>& version() { return this->version_; }
  void version(const std::array<uint8_t, 2>& _val_) { this->version_ = _val_; }
  void version(std::array<uint8_t, 2>&& _val_) { this->version_ = _val_; }
  uint8_t channel() const { return this->channel_; }
  uint8_t& channel() { return this->channel_; }
  void channel(uint8_t _val_) { this->channel_ = _val_; }
  uint8_t joy_mode() const { return this->joy_mode_; }
  uint8_t& joy_mode() { return this->joy_mode_; }
  void joy_mode(uint8_t _val_) { this->joy_mode_ = _val_; }
  float orientation_est() const { return this->orientation_est_; }
  float& orientation_est() { return this->orientation_est_; }
  void orientation_est(float _val_) { this->orientation_est_ = _val_; }
  float pitch_est() const { return this->pitch_est_; }
  float& pitch_est() { return this->pitch_est_; }
  void pitch_est(float _val_) { this->pitch_est_ = _val_; }
  float distance_est() const { return this->distance_est_; }
  float& distance_est() { return this->distance_est_; }
  void distance_est(float _val_) { this->distance_est_ = _val_; }
  float yaw_est() const { return this->yaw_est_; }
  float& yaw_est() { return this->yaw_est_; }
  void yaw_est(float _val_) { this->yaw_est_ = _val_; }
  float tag_roll() const { return this->tag_roll_; }
  float& tag_roll() { return this->tag_roll_; }
  void tag_roll(float _val_) { this->tag_roll_ = _val_; }
  float tag_pitch() const { return this->tag_pitch_; }
  float& tag_pitch() { return this->tag_pitch_; }
  void tag_pitch(float _val_) { this->tag_pitch_ = _val_; }
  float tag_yaw() const { return this->tag_yaw_; }
  float& tag_yaw() { return this->tag_yaw_; }
  void tag_yaw(float _val_) { this->tag_yaw_ = _val_; }
  float base_roll() const { return this->base_roll_; }
  float& base_roll() { return this->base_roll_; }
  void base_roll(float _val_) { this->base_roll_ = _val_; }
  float base_pitch() const { return this->base_pitch_; }
  float& base_pitch() { return this->base_pitch_; }
  void base_pitch(float _val_) { this->base_pitch_ = _val_; }
  float base_yaw() const { return this->base_yaw_; }
  float& base_yaw() { return this->base_yaw_; }
  void base_yaw(float _val_) { this->base_yaw_ = _val_; }
  const std::array<float, 2>& joystick() const { return this->joystick_; }
  std::array<float, 2>& joystick() { return this->joystick_; }
  void joystick(const std::array<float, 2>& _val_) { this->joystick_ = _val_; }
  void joystick(std::array<float, 2>&& _val_) { this->joystick_ = _val_; }
  uint8_t error_state() const { return this->error_state_; }
  uint8_t& error_state() { return this->error_state_; }
  void error_state(uint8_t _val_) { this->error_state_ = _val_; }
  uint8_t buttons() const { return this->buttons_; }
  uint8_t& buttons() { return this->buttons_; }
  void buttons(uint8_t _val_) { this->buttons_ = _val_; }
  uint8_t enabled_from_app() const { return this->enabled_from_app_; }
  uint8_t& enabled_from_app() { return this->enabled_from_app_; }
  void enabled_from_app(uint8_t _val_) { this->enabled_from_app_ = _val_; }

  bool operator==(const UwbState_& _other) const
  {
    (void) _other;
    return version_ == _other.version_ &&
      channel_ == _other.channel_ &&
      joy_mode_ == _other.joy_mode_ &&
      orientation_est_ == _other.orientation_est_ &&
      pitch_est_ == _other.pitch_est_ &&
      distance_est_ == _other.distance_est_ &&
      yaw_est_ == _other.yaw_est_ &&
      tag_roll_ == _other.tag_roll_ &&
      tag_pitch_ == _other.tag_pitch_ &&
      tag_yaw_ == _other.tag_yaw_ &&
      base_roll_ == _other.base_roll_ &&
      base_pitch_ == _other.base_pitch_ &&
      base_yaw_ == _other.base_yaw_ &&
      joystick_ == _other.joystick_ &&
      error_state_ == _other.error_state_ &&
      buttons_ == _other.buttons_ &&
      enabled_from_app_ == _other.enabled_from_app_;
  }

  bool operator!=(const UwbState_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::UwbState_>::getTypeName()
{
  return "unitree_go::msg::dds_::UwbState_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::UwbState_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::UwbState_>::type_map_blob_sz() { return 998; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::UwbState_>::type_info_blob_sz() { return 100; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::UwbState_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x53,  0x01,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf1,  0xd9,  0x00,  0xc3,  0x35,  0xed,  0x0e,  0xb3, 
 0x3d,  0x57,  0x4e,  0xb0,  0xbe,  0x39,  0xae,  0x00,  0x3b,  0x01,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x2b,  0x01,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x02,  0x02,  0x2a,  0xf7,  0x2f,  0x10,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xc4,  0x85,  0xd2,  0xed,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xf5,  0x20,  0x55,  0xed,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xb7,  0x48,  0xcc,  0x5d,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x17,  0x8d,  0xce,  0xa7,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xf5,  0x06,  0x73,  0x9b,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x2e,  0xae,  0x04,  0xde,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x6f,  0x2d,  0x01,  0xc9,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x2f,  0x7d,  0xc9,  0x14,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x1e,  0xe4,  0xc0,  0x80,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x0a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x69,  0x52,  0xd1,  0x26,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x02,  0xda,  0x41,  0x0a,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x83,  0xbd,  0x50,  0x7a,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x0d,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x09,  0x2e,  0xd6,  0x98,  0x33,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0xa7,  0xd8,  0x5b,  0x8a,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x0f,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x02,  0x63,  0x4e,  0xff,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x84,  0x86,  0xf2,  0x61,  0x00,  0x63,  0x02,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0xf2,  0x82,  0x5c,  0xad,  0xb9,  0xe5,  0x0d,  0xf1,  0x58,  0xaf,  0x64,  0xdc,  0x66,  0xa5,  0x20,  0x00, 
 0x4b,  0x02,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x21,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a, 
 0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x55,  0x77,  0x62,  0x53,  0x74, 
 0x61,  0x74,  0x65,  0x5f,  0x00,  0x00,  0x00,  0x00,  0x13,  0x02,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00, 
 0x22,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x02,  0x02,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x76,  0x65,  0x72,  0x73, 
 0x69,  0x6f,  0x6e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x00,  0x08,  0x00,  0x00,  0x00,  0x63,  0x68,  0x61,  0x6e,  0x6e,  0x65,  0x6c,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x17,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x6a,  0x6f,  0x79,  0x5f,  0x6d,  0x6f,  0x64,  0x65,  0x00,  0x00,  0x00,  0x00, 
 0x1e,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x10,  0x00,  0x00,  0x00, 
 0x6f,  0x72,  0x69,  0x65,  0x6e,  0x74,  0x61,  0x74,  0x69,  0x6f,  0x6e,  0x5f,  0x65,  0x73,  0x74,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x18,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x0a,  0x00,  0x00,  0x00,  0x70,  0x69,  0x74,  0x63,  0x68,  0x5f,  0x65,  0x73,  0x74,  0x00,  0x00,  0x00, 
 0x1b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x0d,  0x00,  0x00,  0x00, 
 0x64,  0x69,  0x73,  0x74,  0x61,  0x6e,  0x63,  0x65,  0x5f,  0x65,  0x73,  0x74,  0x00,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x79,  0x61,  0x77,  0x5f,  0x65,  0x73,  0x74,  0x00,  0x00,  0x00,  0x00,  0x00,  0x17,  0x00,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x09,  0x00,  0x00,  0x00,  0x74,  0x61,  0x67,  0x5f, 
 0x72,  0x6f,  0x6c,  0x6c,  0x00,  0x00,  0x00,  0x00,  0x18,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x74,  0x61,  0x67,  0x5f,  0x70,  0x69,  0x74,  0x63, 
 0x68,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x74,  0x61,  0x67,  0x5f,  0x79,  0x61,  0x77,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x18,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x0a,  0x00,  0x00,  0x00, 
 0x62,  0x61,  0x73,  0x65,  0x5f,  0x72,  0x6f,  0x6c,  0x6c,  0x00,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x62,  0x61,  0x73,  0x65, 
 0x5f,  0x70,  0x69,  0x74,  0x63,  0x68,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x17,  0x00,  0x00,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x09,  0x00,  0x00,  0x00,  0x62,  0x61,  0x73,  0x65, 
 0x5f,  0x79,  0x61,  0x77,  0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x0d,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x09,  0x00,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x6a,  0x6f,  0x79,  0x73,  0x74,  0x69,  0x63,  0x6b,  0x00,  0x00,  0x00,  0x00, 
 0x1a,  0x00,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0c,  0x00,  0x00,  0x00, 
 0x65,  0x72,  0x72,  0x6f,  0x72,  0x5f,  0x73,  0x74,  0x61,  0x74,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x0f,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x62,  0x75,  0x74,  0x74,  0x6f,  0x6e,  0x73,  0x00,  0x00,  0x00,  0x00,  0x00,  0x1f,  0x00,  0x00,  0x00, 
 0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x11,  0x00,  0x00,  0x00,  0x65,  0x6e,  0x61,  0x62, 
 0x6c,  0x65,  0x64,  0x5f,  0x66,  0x72,  0x6f,  0x6d,  0x5f,  0x61,  0x70,  0x70,  0x00,  0x00,  0x00,  0x00, 
 0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0x82,  0x5c,  0xad,  0xb9,  0xe5,  0x0d,  0xf1, 
 0x58,  0xaf,  0x64,  0xdc,  0x66,  0xa5,  0x20,  0xf1,  0xd9,  0x00,  0xc3,  0x35,  0xed,  0x0e,  0xb3,  0x3d, 
 0x57,  0x4e,  0xb0,  0xbe,  0x39,  0xae, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::UwbState_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x60,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0xd9,  0x00,  0xc3,  0x35,  0xed,  0x0e,  0xb3,  0x3d,  0x57,  0x4e,  0xb0, 
 0xbe,  0x39,  0xae,  0x00,  0x3f,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0x82,  0x5c,  0xad,  0xb9,  0xe5,  0x0d,  0xf1,  0x58,  0xaf,  0x64,  0xdc, 
 0x66,  0xa5,  0x20,  0x00,  0x4f,  0x02,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::UwbState_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::UwbState_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::UwbState_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::UwbState_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::UwbState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.channel()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.joy_mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.orientation_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.pitch_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.distance_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.yaw_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.tag_roll()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.tag_pitch()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.tag_yaw()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.base_roll()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.base_pitch()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.base_yaw()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.joystick()[0], instance.joystick().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.error_state()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.buttons()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 16:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.enabled_from_app()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::UwbState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::UwbState_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::UwbState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.channel()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.joy_mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.orientation_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.pitch_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.distance_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.yaw_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.tag_roll()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.tag_pitch()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.tag_yaw()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.base_roll()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.base_pitch()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.base_yaw()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.joystick()[0], instance.joystick().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.error_state()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.buttons()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 16:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.enabled_from_app()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::UwbState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::UwbState_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::UwbState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.channel()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.joy_mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.orientation_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.pitch_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.distance_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.yaw_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.tag_roll()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.tag_pitch()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.tag_yaw()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.base_roll()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.base_pitch()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.base_yaw()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.joystick()[0], instance.joystick().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.error_state()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.buttons()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 16:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.enabled_from_app()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::UwbState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::UwbState_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::UwbState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.channel()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.joy_mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.orientation_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.pitch_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.distance_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.yaw_est()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.tag_roll()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.tag_pitch()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.tag_yaw()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.base_roll()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.base_pitch()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.base_yaw()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.joystick()[0], instance.joystick().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.error_state()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.buttons()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 16:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.enabled_from_app()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::UwbState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::UwbState_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_UWBSTATE__HPP
