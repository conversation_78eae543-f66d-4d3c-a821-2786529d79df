/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: LowState_.idl
  Source: LowState_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_HG_LOWSTATE__HPP
#define DDSCXX_UNITREE_IDL_HG_LOWSTATE__HPP

#include "unitree/idl/hg/IMUState_.hpp"

#include "unitree/idl/hg/MotorState_.hpp"

#include <cstdint>
#include <array>

namespace unitree_hg
{
namespace msg
{
namespace dds_
{
class LowState_
{
private:
 std::array<uint32_t, 2> version_ = { };
 uint8_t mode_pr_ = 0;
 uint8_t mode_machine_ = 0;
 uint32_t tick_ = 0;
 ::unitree_hg::msg::dds_::IMUState_ imu_state_;
 std::array<::unitree_hg::msg::dds_::MotorState_, 35> motor_state_ = { };
 std::array<uint8_t, 40> wireless_remote_ = { };
 std::array<uint32_t, 4> reserve_ = { };
 uint32_t crc_ = 0;

public:
  LowState_() = default;

  explicit LowState_(
    const std::array<uint32_t, 2>& version,
    uint8_t mode_pr,
    uint8_t mode_machine,
    uint32_t tick,
    const ::unitree_hg::msg::dds_::IMUState_& imu_state,
    const std::array<::unitree_hg::msg::dds_::MotorState_, 35>& motor_state,
    const std::array<uint8_t, 40>& wireless_remote,
    const std::array<uint32_t, 4>& reserve,
    uint32_t crc) :
    version_(version),
    mode_pr_(mode_pr),
    mode_machine_(mode_machine),
    tick_(tick),
    imu_state_(imu_state),
    motor_state_(motor_state),
    wireless_remote_(wireless_remote),
    reserve_(reserve),
    crc_(crc) { }

  const std::array<uint32_t, 2>& version() const { return this->version_; }
  std::array<uint32_t, 2>& version() { return this->version_; }
  void version(const std::array<uint32_t, 2>& _val_) { this->version_ = _val_; }
  void version(std::array<uint32_t, 2>&& _val_) { this->version_ = _val_; }
  uint8_t mode_pr() const { return this->mode_pr_; }
  uint8_t& mode_pr() { return this->mode_pr_; }
  void mode_pr(uint8_t _val_) { this->mode_pr_ = _val_; }
  uint8_t mode_machine() const { return this->mode_machine_; }
  uint8_t& mode_machine() { return this->mode_machine_; }
  void mode_machine(uint8_t _val_) { this->mode_machine_ = _val_; }
  uint32_t tick() const { return this->tick_; }
  uint32_t& tick() { return this->tick_; }
  void tick(uint32_t _val_) { this->tick_ = _val_; }
  const ::unitree_hg::msg::dds_::IMUState_& imu_state() const { return this->imu_state_; }
  ::unitree_hg::msg::dds_::IMUState_& imu_state() { return this->imu_state_; }
  void imu_state(const ::unitree_hg::msg::dds_::IMUState_& _val_) { this->imu_state_ = _val_; }
  void imu_state(::unitree_hg::msg::dds_::IMUState_&& _val_) { this->imu_state_ = _val_; }
  const std::array<::unitree_hg::msg::dds_::MotorState_, 35>& motor_state() const { return this->motor_state_; }
  std::array<::unitree_hg::msg::dds_::MotorState_, 35>& motor_state() { return this->motor_state_; }
  void motor_state(const std::array<::unitree_hg::msg::dds_::MotorState_, 35>& _val_) { this->motor_state_ = _val_; }
  void motor_state(std::array<::unitree_hg::msg::dds_::MotorState_, 35>&& _val_) { this->motor_state_ = _val_; }
  const std::array<uint8_t, 40>& wireless_remote() const { return this->wireless_remote_; }
  std::array<uint8_t, 40>& wireless_remote() { return this->wireless_remote_; }
  void wireless_remote(const std::array<uint8_t, 40>& _val_) { this->wireless_remote_ = _val_; }
  void wireless_remote(std::array<uint8_t, 40>&& _val_) { this->wireless_remote_ = _val_; }
  const std::array<uint32_t, 4>& reserve() const { return this->reserve_; }
  std::array<uint32_t, 4>& reserve() { return this->reserve_; }
  void reserve(const std::array<uint32_t, 4>& _val_) { this->reserve_ = _val_; }
  void reserve(std::array<uint32_t, 4>&& _val_) { this->reserve_ = _val_; }
  uint32_t crc() const { return this->crc_; }
  uint32_t& crc() { return this->crc_; }
  void crc(uint32_t _val_) { this->crc_ = _val_; }

  bool operator==(const LowState_& _other) const
  {
    (void) _other;
    return version_ == _other.version_ &&
      mode_pr_ == _other.mode_pr_ &&
      mode_machine_ == _other.mode_machine_ &&
      tick_ == _other.tick_ &&
      imu_state_ == _other.imu_state_ &&
      motor_state_ == _other.motor_state_ &&
      wireless_remote_ == _other.wireless_remote_ &&
      reserve_ == _other.reserve_ &&
      crc_ == _other.crc_;
  }

  bool operator!=(const LowState_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_hg::msg::dds_::LowState_>::getTypeName()
{
  return "unitree_hg::msg::dds_::LowState_";
}

template <> constexpr bool TopicTraits<::unitree_hg::msg::dds_::LowState_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_hg::msg::dds_::LowState_>::type_map_blob_sz() { return 1850; }
template<> constexpr unsigned int TopicTraits<::unitree_hg::msg::dds_::LowState_>::type_info_blob_sz() { return 196; }
template<> inline const uint8_t * TopicTraits<::unitree_hg::msg::dds_::LowState_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x9a,  0x02,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0xf1,  0x2f,  0x38,  0x29,  0x6b,  0x52,  0xaa,  0x02, 
 0x30,  0xb9,  0x2d,  0x37,  0x8b,  0x1e,  0x39,  0x00,  0xef,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0xdf,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0x2a,  0xf7,  0x2f,  0x10,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xb6,  0x9f,  0x36,  0x51,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xed,  0xb8,  0xc4,  0x0a,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0xe5,  0xe5,  0xc0,  0x75,  0x00,  0x19,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0xfc,  0x68,  0x71,  0x76,  0x65,  0x12,  0x06,  0x55,  0x84, 
 0x73,  0xdf,  0x44,  0x22,  0x80,  0x9e,  0x5f,  0x37,  0x13,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf1,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x23,  0xf1,  0x7c,  0xc5,  0x84,  0xed,  0x94,  0x0b,  0x8f,  0x30,  0x18,  0xf1,  0x8d,  0xe0,  0xed,  0x24, 
 0xb2,  0x1e,  0x53,  0xdc,  0x16,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x28,  0x02,  0xa8,  0x2f,  0x18,  0xc9,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x04,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0xf5,  0xad,  0x59,  0xc5,  0xf1,  0xfc,  0x68,  0x71,  0x76, 
 0x65,  0x12,  0x06,  0x55,  0x84,  0x73,  0xdf,  0x44,  0x22,  0x80,  0x00,  0x00,  0x93,  0x00,  0x00,  0x00, 
 0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x83,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x09,  0x75,  0xb9,  0xaa,  0xa0,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x6b,  0x3b,  0x2d,  0x85,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x09,  0x0e,  0x12,  0x75,  0x69,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0xb0,  0x7d, 
 0x92,  0xed,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x03,  0xee, 
 0xf4,  0x38,  0xf7,  0xf1,  0x7c,  0xc5,  0x84,  0xed,  0x94,  0x0b,  0x8f,  0x30,  0x18,  0xf1,  0x8d,  0xe0, 
 0xed,  0x24,  0x00,  0x00,  0xd6,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0xc6,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x15,  0xd6,  0x17,  0x12,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x76,  0x94,  0xf4,  0xa6,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x47,  0xbc,  0xdc,  0xd7,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xe9,  0x16,  0x89,  0x09,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x8a,  0xf7,  0xae,  0xdf,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x03,  0xee,  0xf4,  0x38,  0xf7,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x0a,  0xcf,  0x8b,  0xe1,  0x00,  0x16,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0xa5,  0xfe, 
 0x26,  0xd5,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0xd1, 
 0x41,  0x1a,  0xed,  0x00,  0x16,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0x00,  0x00, 
 0x32,  0x04,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0xf2,  0x2b,  0xe9,  0x70,  0x6c,  0x95,  0x87,  0xe8, 
 0xd8,  0xaf,  0xd8,  0xbf,  0xc6,  0xde,  0x7b,  0x00,  0x8a,  0x01,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00, 
 0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74, 
 0x72,  0x65,  0x65,  0x5f,  0x68,  0x67,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73, 
 0x5f,  0x3a,  0x3a,  0x4c,  0x6f,  0x77,  0x53,  0x74,  0x61,  0x74,  0x65,  0x5f,  0x00,  0x00,  0x00,  0x00, 
 0x52,  0x01,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x76,  0x65,  0x72,  0x73,  0x69,  0x6f,  0x6e,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x6d,  0x6f,  0x64,  0x65,  0x5f,  0x70,  0x72,  0x00,  0x00,  0x00,  0x00,  0x00,  0x1b,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0d,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x64,  0x65, 
 0x5f,  0x6d,  0x61,  0x63,  0x68,  0x69,  0x6e,  0x65,  0x00,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00,  0x05,  0x00,  0x00,  0x00,  0x74,  0x69,  0x63,  0x6b, 
 0x00,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x7d, 
 0x41,  0x66,  0x75,  0x41,  0x77,  0xd8,  0x6f,  0xa2,  0x7c,  0xd2,  0x5b,  0x04,  0x03,  0x00,  0x00,  0x00, 
 0x0a,  0x00,  0x00,  0x00,  0x69,  0x6d,  0x75,  0x5f,  0x73,  0x74,  0x61,  0x74,  0x65,  0x00,  0x00,  0x00, 
 0x32,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf2,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x23,  0xf2,  0xa8,  0x6c,  0xba,  0xb3,  0xae,  0x0a,  0xa9,  0x8a,  0x1e,  0x74, 
 0x66,  0x43,  0xa0,  0x99,  0x0c,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x74,  0x6f,  0x72,  0x5f,  0x73,  0x74, 
 0x61,  0x74,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x2a,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x28,  0x02,  0x00,  0x00, 
 0x10,  0x00,  0x00,  0x00,  0x77,  0x69,  0x72,  0x65,  0x6c,  0x65,  0x73,  0x73,  0x5f,  0x72,  0x65,  0x6d, 
 0x6f,  0x74,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x07,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x12,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x63,  0x72,  0x63,  0x00,  0x00,  0x00,  0xf2,  0x7d,  0x41,  0x66,  0x75,  0x41,  0x77,  0xd8,  0x6f,  0xa2, 
 0x7c,  0xd2,  0x5b,  0x04,  0x03,  0x00,  0x00,  0x00,  0xfe,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00, 
 0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74, 
 0x72,  0x65,  0x65,  0x5f,  0x68,  0x67,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73, 
 0x5f,  0x3a,  0x3a,  0x49,  0x4d,  0x55,  0x53,  0x74,  0x61,  0x74,  0x65,  0x5f,  0x00,  0x00,  0x00,  0x00, 
 0xc6,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x25,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x09,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x71,  0x75,  0x61,  0x74,  0x65,  0x72,  0x6e,  0x69,  0x6f,  0x6e,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00, 
 0x67,  0x79,  0x72,  0x6f,  0x73,  0x63,  0x6f,  0x70,  0x65,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x09,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00,  0x61,  0x63,  0x63,  0x65,  0x6c,  0x65,  0x72,  0x6f, 
 0x6d,  0x65,  0x74,  0x65,  0x72,  0x00,  0x00,  0x00,  0x1e,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x72,  0x70,  0x79,  0x00,  0x00,  0x00,  0x00,  0x00,  0x1a,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x03,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x74,  0x65,  0x6d,  0x70, 
 0x65,  0x72,  0x61,  0x74,  0x75,  0x72,  0x65,  0x00,  0x00,  0x00,  0xf2,  0xa8,  0x6c,  0xba,  0xb3,  0xae, 
 0x0a,  0xa9,  0x8a,  0x1e,  0x74,  0x66,  0x43,  0xa0,  0x99,  0x00,  0x00,  0x00,  0x66,  0x01,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x2b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00, 
 0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x68,  0x67,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a, 
 0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x4d,  0x6f,  0x74,  0x6f,  0x72,  0x53,  0x74,  0x61,  0x74, 
 0x65,  0x5f,  0x00,  0x00,  0x2e,  0x01,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x05,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x64,  0x65, 
 0x00,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x71,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00,  0x64,  0x71,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x12,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x64,  0x64,  0x71,  0x00,  0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x08,  0x00,  0x00,  0x00,  0x74,  0x61,  0x75,  0x5f,  0x65,  0x73,  0x74,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x26,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x03,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00, 
 0x74,  0x65,  0x6d,  0x70,  0x65,  0x72,  0x61,  0x74,  0x75,  0x72,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x12,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x76,  0x6f,  0x6c,  0x00,  0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x73,  0x65,  0x6e,  0x73,  0x6f,  0x72,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x19,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x6d,  0x6f,  0x74,  0x6f,  0x72,  0x73,  0x74,  0x61,  0x74,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x22,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x04,  0x07,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65, 
 0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x5e,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0xf2,  0x2b,  0xe9,  0x70,  0x6c,  0x95,  0x87,  0xe8,  0xd8,  0xaf,  0xd8,  0xbf,  0xc6,  0xde,  0x7b,  0xf1, 
 0x2f,  0x38,  0x29,  0x6b,  0x52,  0xaa,  0x02,  0x30,  0xb9,  0x2d,  0x37,  0x8b,  0x1e,  0x39,  0xf2,  0x7d, 
 0x41,  0x66,  0x75,  0x41,  0x77,  0xd8,  0x6f,  0xa2,  0x7c,  0xd2,  0x5b,  0x04,  0x03,  0xf1,  0xfc,  0x68, 
 0x71,  0x76,  0x65,  0x12,  0x06,  0x55,  0x84,  0x73,  0xdf,  0x44,  0x22,  0x80,  0xf2,  0xa8,  0x6c,  0xba, 
 0xb3,  0xae,  0x0a,  0xa9,  0x8a,  0x1e,  0x74,  0x66,  0x43,  0xa0,  0x99,  0xf1,  0x7c,  0xc5,  0x84,  0xed, 
 0x94,  0x0b,  0x8f,  0x30,  0x18,  0xf1,  0x8d,  0xe0,  0xed,  0x24, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_hg::msg::dds_::LowState_>::type_info_blob() {
  static const uint8_t blob[] = {
 0xc0,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x58,  0x00,  0x00,  0x00,  0x54,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x2f,  0x38,  0x29,  0x6b,  0x52,  0xaa,  0x02,  0x30,  0xb9,  0x2d,  0x37, 
 0x8b,  0x1e,  0x39,  0x00,  0xf3,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x34,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0xfc,  0x68,  0x71,  0x76,  0x65,  0x12,  0x06, 
 0x55,  0x84,  0x73,  0xdf,  0x44,  0x22,  0x80,  0x00,  0x97,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf1,  0x7c,  0xc5,  0x84,  0xed,  0x94,  0x0b,  0x8f,  0x30,  0x18,  0xf1,  0x8d,  0xe0,  0xed,  0x24,  0x00, 
 0xda,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x58,  0x00,  0x00,  0x00,  0x54,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0x2b,  0xe9,  0x70,  0x6c,  0x95,  0x87,  0xe8,  0xd8,  0xaf,  0xd8,  0xbf, 
 0xc6,  0xde,  0x7b,  0x00,  0x8e,  0x01,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x34,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0x7d,  0x41,  0x66,  0x75,  0x41,  0x77,  0xd8, 
 0x6f,  0xa2,  0x7c,  0xd2,  0x5b,  0x04,  0x03,  0x00,  0x02,  0x01,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0xa8,  0x6c,  0xba,  0xb3,  0xae,  0x0a,  0xa9,  0x8a,  0x1e,  0x74,  0x66,  0x43,  0xa0,  0x99,  0x00, 
 0x6a,  0x01,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_hg::msg::dds_::LowState_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_hg::msg::dds_::LowState_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_hg::msg::dds_::LowState_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_hg::msg::dds_::LowState_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_hg::msg::dds_::LowState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.mode_pr()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.mode_machine()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.tick()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.motor_state()) {  //array depth 1
      if (!write(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.wireless_remote()[0], instance.wireless_remote().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_hg::msg::dds_::LowState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::LowState_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_hg::msg::dds_::LowState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.mode_pr()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.mode_machine()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.tick()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (auto & a_1:instance.motor_state()) {  //array depth 1
      if (!read(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.wireless_remote()[0], instance.wireless_remote().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_hg::msg::dds_::LowState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::LowState_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_hg::msg::dds_::LowState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.mode_pr()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.mode_machine()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.tick()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.motor_state()) {  //array depth 1
      if (!move(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.wireless_remote()[0], instance.wireless_remote().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_hg::msg::dds_::LowState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::LowState_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_hg::msg::dds_::LowState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.mode_pr()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.mode_machine()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.tick()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.imu_state(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.motor_state()) {  //array depth 1
      if (!max(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.wireless_remote()[0], instance.wireless_remote().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_hg::msg::dds_::LowState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::LowState_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_HG_LOWSTATE__HPP
