/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: InterfaceConfig_.idl
  Source: InterfaceConfig_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_INTERFACECONFIG__HPP
#define DDSCXX_UNITREE_IDL_GO2_INTERFACECONFIG__HPP

#include <cstdint>
#include <array>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class InterfaceConfig_
{
private:
 uint8_t mode_ = 0;
 uint8_t value_ = 0;
 std::array<uint8_t, 2> reserve_ = { };

public:
  InterfaceConfig_() = default;

  explicit InterfaceConfig_(
    uint8_t mode,
    uint8_t value,
    const std::array<uint8_t, 2>& reserve) :
    mode_(mode),
    value_(value),
    reserve_(reserve) { }

  uint8_t mode() const { return this->mode_; }
  uint8_t& mode() { return this->mode_; }
  void mode(uint8_t _val_) { this->mode_ = _val_; }
  uint8_t value() const { return this->value_; }
  uint8_t& value() { return this->value_; }
  void value(uint8_t _val_) { this->value_ = _val_; }
  const std::array<uint8_t, 2>& reserve() const { return this->reserve_; }
  std::array<uint8_t, 2>& reserve() { return this->reserve_; }
  void reserve(const std::array<uint8_t, 2>& _val_) { this->reserve_ = _val_; }
  void reserve(std::array<uint8_t, 2>&& _val_) { this->reserve_ = _val_; }

  bool operator==(const InterfaceConfig_& _other) const
  {
    (void) _other;
    return mode_ == _other.mode_ &&
      value_ == _other.value_ &&
      reserve_ == _other.reserve_;
  }

  bool operator!=(const InterfaceConfig_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::InterfaceConfig_>::getTypeName()
{
  return "unitree_go::msg::dds_::InterfaceConfig_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::InterfaceConfig_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::InterfaceConfig_>::type_map_blob_sz() { return 326; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::InterfaceConfig_>::type_info_blob_sz() { return 100; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::InterfaceConfig_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x66,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf1,  0x4f,  0x25,  0x85,  0x7b,  0xb1,  0x9a,  0x7b, 
 0x3a,  0xaf,  0x05,  0x03,  0xdb,  0x8f,  0xa9,  0x00,  0x4e,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x3e,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x15,  0xd6,  0x17,  0x12,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x20,  0x63,  0xc1,  0x60,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x02,  0x02,  0x9c,  0x3b,  0x62,  0x94,  0x00,  0x00,  0xae,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0xf2,  0x11,  0x6e,  0x4e,  0x71,  0x6a,  0xb8,  0x9f,  0x44,  0x70,  0x5d,  0xb6, 
 0x2b,  0x7d,  0x87,  0x00,  0x96,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x30,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f, 
 0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x49, 
 0x6e,  0x74,  0x65,  0x72,  0x66,  0x61,  0x63,  0x65,  0x43,  0x6f,  0x6e,  0x66,  0x69,  0x67,  0x5f,  0x00, 
 0x5a,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x00,  0x05,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x64,  0x65,  0x00,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x76,  0x61,  0x6c,  0x75,  0x65,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x02,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0x11,  0x6e,  0x4e,  0x71,  0x6a,  0xb8,  0x9f, 
 0x44,  0x70,  0x5d,  0xb6,  0x2b,  0x7d,  0x87,  0xf1,  0x4f,  0x25,  0x85,  0x7b,  0xb1,  0x9a,  0x7b,  0x3a, 
 0xaf,  0x05,  0x03,  0xdb,  0x8f,  0xa9, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::InterfaceConfig_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x60,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x4f,  0x25,  0x85,  0x7b,  0xb1,  0x9a,  0x7b,  0x3a,  0xaf,  0x05,  0x03, 
 0xdb,  0x8f,  0xa9,  0x00,  0x52,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0x11,  0x6e,  0x4e,  0x71,  0x6a,  0xb8,  0x9f,  0x44,  0x70,  0x5d,  0xb6, 
 0x2b,  0x7d,  0x87,  0x00,  0x9a,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::InterfaceConfig_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::InterfaceConfig_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::InterfaceConfig_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::InterfaceConfig_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::InterfaceConfig_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.value()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::InterfaceConfig_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::InterfaceConfig_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::InterfaceConfig_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.value()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::InterfaceConfig_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::InterfaceConfig_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::InterfaceConfig_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.value()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::InterfaceConfig_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::InterfaceConfig_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::InterfaceConfig_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.value()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::InterfaceConfig_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::InterfaceConfig_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_INTERFACECONFIG__HPP
