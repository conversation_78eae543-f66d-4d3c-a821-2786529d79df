/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: PoseWithCovarianceStamped_.idl
  Source: PoseWithCovarianceStamped_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_ROS2_POSEWITHCOVARIANCESTAMPED__HPP
#define DDSCXX_UNITREE_IDL_ROS2_POSEWITHCOVARIANCESTAMPED__HPP

#include "unitree/idl/ros2/PoseWithCovariance_.hpp"

#include "unitree/idl/ros2/Header_.hpp"


namespace geometry_msgs
{
namespace msg
{
namespace dds_
{
class PoseWithCovarianceStamped_
{
private:
 ::std_msgs::msg::dds_::Header_ header_;
 ::geometry_msgs::msg::dds_::PoseWithCovariance_ pose_;

public:
  PoseWithCovarianceStamped_() = default;

  explicit PoseWithCovarianceStamped_(
    const ::std_msgs::msg::dds_::Header_& header,
    const ::geometry_msgs::msg::dds_::PoseWithCovariance_& pose) :
    header_(header),
    pose_(pose) { }

  const ::std_msgs::msg::dds_::Header_& header() const { return this->header_; }
  ::std_msgs::msg::dds_::Header_& header() { return this->header_; }
  void header(const ::std_msgs::msg::dds_::Header_& _val_) { this->header_ = _val_; }
  void header(::std_msgs::msg::dds_::Header_&& _val_) { this->header_ = _val_; }
  const ::geometry_msgs::msg::dds_::PoseWithCovariance_& pose() const { return this->pose_; }
  ::geometry_msgs::msg::dds_::PoseWithCovariance_& pose() { return this->pose_; }
  void pose(const ::geometry_msgs::msg::dds_::PoseWithCovariance_& _val_) { this->pose_ = _val_; }
  void pose(::geometry_msgs::msg::dds_::PoseWithCovariance_&& _val_) { this->pose_ = _val_; }

  bool operator==(const PoseWithCovarianceStamped_& _other) const
  {
    (void) _other;
    return header_ == _other.header_ &&
      pose_ == _other.pose_;
  }

  bool operator!=(const PoseWithCovarianceStamped_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>::getTypeName()
{
  return "geometry_msgs::msg::dds_::PoseWithCovarianceStamped_";
}

template <> constexpr bool TopicTraits<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>::type_map_blob_sz() { return 1994; }
template<> constexpr unsigned int TopicTraits<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>::type_info_blob_sz() { return 388; }
template<> inline const uint8_t * TopicTraits<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x8f,  0x02,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0xf1,  0x3d,  0xeb,  0xb5,  0x11,  0xcc,  0xa4,  0xdc, 
 0x63,  0xf4,  0xba,  0x83,  0xde,  0x11,  0x70,  0x00,  0x51,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x41,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x19,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0xdc,  0xf1,  0x2c,  0xd2,  0xdd, 
 0x5e,  0x71,  0x2c,  0xb7,  0xb1,  0xe5,  0x1f,  0xa3,  0xf2,  0x09,  0x9f,  0xb9,  0x95,  0x00,  0x00,  0x00, 
 0x19,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0xb7,  0xb7,  0x26,  0xac,  0x8e, 
 0x3d,  0x19,  0x1d,  0xc5,  0x32,  0xa3,  0x3c,  0x52,  0xc9,  0x2d,  0x5f,  0x8a,  0xe9,  0xf1,  0xdc,  0xf1, 
 0x2c,  0xd2,  0xdd,  0x5e,  0x71,  0x2c,  0xb7,  0xb1,  0xe5,  0x1f,  0xa3,  0xf2,  0x44,  0x00,  0x00,  0x00, 
 0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x34,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x56, 
 0x7c,  0x5a,  0x93,  0x54,  0x1c,  0x3b,  0x10,  0x86,  0xa4,  0xba,  0x46,  0xf9,  0x8d,  0x96,  0xb8,  0xc7, 
 0x8d,  0x00,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x70,  0x00, 
 0x4b,  0xb3,  0x9c,  0x5c,  0xf1,  0x56,  0x7c,  0x5a,  0x93,  0x54,  0x1c,  0x3b,  0x10,  0x86,  0xa4,  0xba, 
 0x46,  0xf9,  0x8d,  0x00,  0x33,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x04,  0x74,  0x45,  0x9c,  0xa3,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0xe2,  0x04,  0x64,  0xd5,  0xf1,  0xb7,  0xb7,  0x26,  0xac, 
 0x8e,  0x3d,  0x19,  0x1d,  0xc5,  0x32,  0xa3,  0x3c,  0x52,  0xc9,  0x00,  0x00,  0x4e,  0x00,  0x00,  0x00, 
 0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x3e,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x2f, 
 0x93,  0xf0,  0x5b,  0x8c,  0xef,  0xbe,  0x11,  0x90,  0x31,  0xc4,  0x88,  0x17,  0xeb,  0x2d,  0x5f,  0x8a, 
 0xe9,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x24,  0x0a,  0xac,  0xdb,  0x06,  0x55,  0xf1,  0x2f, 
 0x93,  0xf0,  0x5b,  0x8c,  0xef,  0xbe,  0x11,  0x90,  0x31,  0xc4,  0x88,  0x17,  0xeb,  0x00,  0x00,  0x00, 
 0x51,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x41,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0xf1,  0x5e,  0x73,  0x97,  0xe7,  0xe8,  0x64,  0x40,  0xdf,  0x64,  0xaf,  0x76,  0xcd,  0x4c, 
 0xbc,  0x47,  0x57,  0xfe,  0x07,  0x00,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0xf1,  0x2e,  0xd7,  0x30,  0x7b,  0x8e,  0xc5,  0x7c,  0x4b,  0x34,  0x86,  0x46,  0xa9,  0x62, 
 0xa1,  0xda,  0x16,  0x39,  0x42,  0xf1,  0x5e,  0x73,  0x97,  0xe7,  0xe8,  0x64,  0x40,  0xdf,  0x64,  0xaf, 
 0x76,  0xcd,  0x4c,  0xbc,  0x43,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x33,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x9d,  0xd4,  0xe4,  0x61,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x41,  0x52,  0x90,  0x76,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0xfb,  0xad,  0xe9,  0xe3,  0xf1,  0x2e,  0xd7,  0x30,  0x7b, 
 0x8e,  0xc5,  0x7c,  0x4b,  0x34,  0x86,  0x46,  0xa9,  0x62,  0xa1,  0x00,  0x00,  0x53,  0x00,  0x00,  0x00, 
 0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x43,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x9d, 
 0xd4,  0xe4,  0x61,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x41, 
 0x52,  0x90,  0x76,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0xfb, 
 0xad,  0xe9,  0xe3,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0xf1, 
 0x29,  0x01,  0x86,  0x00,  0x58,  0x04,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0xf2,  0x24,  0x77,  0x5a, 
 0x9f,  0x85,  0xf9,  0x7b,  0x5b,  0xeb,  0x5a,  0x31,  0xb0,  0x0c,  0x05,  0x00,  0xa3,  0x00,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x3d,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x35,  0x00,  0x00,  0x00, 
 0x67,  0x65,  0x6f,  0x6d,  0x65,  0x74,  0x72,  0x79,  0x5f,  0x6d,  0x73,  0x67,  0x73,  0x3a,  0x3a,  0x6d, 
 0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x50,  0x6f,  0x73,  0x65,  0x57,  0x69, 
 0x74,  0x68,  0x43,  0x6f,  0x76,  0x61,  0x72,  0x69,  0x61,  0x6e,  0x63,  0x65,  0x53,  0x74,  0x61,  0x6d, 
 0x70,  0x65,  0x64,  0x5f,  0x00,  0x00,  0x00,  0x00,  0x57,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x25,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0xe5,  0x76,  0x5e,  0xc4,  0x8c, 
 0xff,  0xd4,  0x19,  0xed,  0x7f,  0xe8,  0x4e,  0x2a,  0x55,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x68,  0x65,  0x61,  0x64,  0x65,  0x72,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x19,  0x12,  0x04,  0x8a,  0x82,  0xed,  0x6f,  0x0a,  0x49, 
 0x06,  0x5e,  0x94,  0xe7,  0x0c,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x70,  0x6f,  0x73,  0x65, 
 0x00,  0x00,  0x00,  0xf2,  0xe5,  0x76,  0x5e,  0xc4,  0x8c,  0xff,  0xd4,  0x19,  0xed,  0x7f,  0xe8,  0x4e, 
 0x2a,  0x55,  0x00,  0x00,  0x7b,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x25,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x1d,  0x00,  0x00,  0x00,  0x73,  0x74,  0x64,  0x5f,  0x6d,  0x73,  0x67,  0x73, 
 0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x48,  0x65,  0x61, 
 0x64,  0x65,  0x72,  0x5f,  0x00,  0x00,  0x00,  0x00,  0x47,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x24,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0xd4,  0x85,  0x4f,  0x13,  0xae, 
 0xf3,  0x2d,  0xfe,  0x21,  0x57,  0xf3,  0xe6,  0x32,  0x0d,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x73,  0x74,  0x61,  0x6d,  0x70,  0x00,  0x00,  0x00,  0x17,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x70,  0x00,  0x09,  0x00,  0x00,  0x00,  0x66,  0x72,  0x61,  0x6d,  0x65,  0x5f,  0x69,  0x64, 
 0x00,  0x00,  0x00,  0xf2,  0xd4,  0x85,  0x4f,  0x13,  0xae,  0xf3,  0x2d,  0xfe,  0x21,  0x57,  0xf3,  0xe6, 
 0x32,  0x0d,  0x00,  0x00,  0x72,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x2d,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x25,  0x00,  0x00,  0x00,  0x62,  0x75,  0x69,  0x6c,  0x74,  0x69,  0x6e,  0x5f, 
 0x69,  0x6e,  0x74,  0x65,  0x72,  0x66,  0x61,  0x63,  0x65,  0x73,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a, 
 0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x54,  0x69,  0x6d,  0x65,  0x5f,  0x00,  0x00,  0x00,  0x00, 
 0x36,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x04,  0x00,  0x04,  0x00,  0x00,  0x00,  0x73,  0x65,  0x63,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x6e,  0x61,  0x6e,  0x6f,  0x73,  0x65,  0x63,  0x00,  0x00,  0x00,  0xf2,  0x19,  0x12,  0x04,  0x8a,  0x82, 
 0xed,  0x6f,  0x0a,  0x49,  0x06,  0x5e,  0x94,  0xe7,  0x0c,  0x00,  0x00,  0x00,  0x99,  0x00,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x36,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x2e,  0x00,  0x00,  0x00, 
 0x67,  0x65,  0x6f,  0x6d,  0x65,  0x74,  0x72,  0x79,  0x5f,  0x6d,  0x73,  0x67,  0x73,  0x3a,  0x3a,  0x6d, 
 0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x50,  0x6f,  0x73,  0x65,  0x57,  0x69, 
 0x74,  0x68,  0x43,  0x6f,  0x76,  0x61,  0x72,  0x69,  0x61,  0x6e,  0x63,  0x65,  0x5f,  0x00,  0x00,  0x00, 
 0x55,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0xf2,  0x0d,  0x47,  0x26,  0x4b,  0x4a,  0x66,  0xfa,  0x94,  0xbb,  0xfe,  0x34,  0xf3,  0x2f, 
 0x7d,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x70,  0x6f,  0x73,  0x65,  0x00,  0x00,  0x00,  0x00, 
 0x25,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x24,  0x0a,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x63,  0x6f,  0x76,  0x61, 
 0x72,  0x69,  0x61,  0x6e,  0x63,  0x65,  0x00,  0x00,  0x00,  0xf2,  0x0d,  0x47,  0x26,  0x4b,  0x4a,  0x66, 
 0xfa,  0x94,  0xbb,  0xfe,  0x34,  0xf3,  0x2f,  0x7d,  0x92,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00, 
 0x28,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x20,  0x00,  0x00,  0x00,  0x67,  0x65,  0x6f,  0x6d, 
 0x65,  0x74,  0x72,  0x79,  0x5f,  0x6d,  0x73,  0x67,  0x73,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a, 
 0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x50,  0x6f,  0x73,  0x65,  0x5f,  0x00,  0x5e,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x27,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x04, 
 0xac,  0x28,  0xf9,  0x19,  0x64,  0x23,  0x5d,  0xc9,  0x81,  0xe3,  0xf8,  0xfa,  0xdf,  0x00,  0x00,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x70,  0x6f,  0x73,  0x69,  0x74,  0x69,  0x6f,  0x6e,  0x00,  0x00,  0x00,  0x00, 
 0x2a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x6f,  0x01,  0xea,  0x49,  0x00, 
 0xbc,  0x02,  0x80,  0x58,  0xc3,  0xa8,  0xda,  0xe3,  0x52,  0x00,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00, 
 0x6f,  0x72,  0x69,  0x65,  0x6e,  0x74,  0x61,  0x74,  0x69,  0x6f,  0x6e,  0x00,  0x00,  0x00,  0xf2,  0x04, 
 0xac,  0x28,  0xf9,  0x19,  0x64,  0x23,  0x5d,  0xc9,  0x81,  0xe3,  0xf8,  0xfa,  0xdf,  0x00,  0x00,  0x00, 
 0x78,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x21,  0x00,  0x00,  0x00,  0x67,  0x65,  0x6f,  0x6d,  0x65,  0x74,  0x72,  0x79,  0x5f,  0x6d,  0x73,  0x67, 
 0x73,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x50,  0x6f, 
 0x69,  0x6e,  0x74,  0x5f,  0x00,  0x00,  0x00,  0x00,  0x40,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x10,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x78,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x79,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x0a,  0x00,  0x02,  0x00,  0x00,  0x00,  0x7a,  0x00,  0x00,  0x00,  0xf2,  0x6f,  0x01,  0xea, 
 0x49,  0x00,  0xbc,  0x02,  0x80,  0x58,  0xc3,  0xa8,  0xda,  0xe3,  0x52,  0x00,  0x90,  0x00,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x2e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x26,  0x00,  0x00,  0x00, 
 0x67,  0x65,  0x6f,  0x6d,  0x65,  0x74,  0x72,  0x79,  0x5f,  0x6d,  0x73,  0x67,  0x73,  0x3a,  0x3a,  0x6d, 
 0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x51,  0x75,  0x61,  0x74,  0x65,  0x72, 
 0x6e,  0x69,  0x6f,  0x6e,  0x5f,  0x00,  0x00,  0x00,  0x54,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x10,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x78,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x79,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x0a,  0x00,  0x02,  0x00,  0x00,  0x00,  0x7a,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x00,  0x02,  0x00,  0x00,  0x00,  0x77,  0x00,  0x00,  0x00, 
 0xd6,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0xf2,  0x24,  0x77,  0x5a,  0x9f,  0x85,  0xf9,  0x7b, 
 0x5b,  0xeb,  0x5a,  0x31,  0xb0,  0x0c,  0x05,  0xf1,  0x3d,  0xeb,  0xb5,  0x11,  0xcc,  0xa4,  0xdc,  0x63, 
 0xf4,  0xba,  0x83,  0xde,  0x11,  0x70,  0xf2,  0xe5,  0x76,  0x5e,  0xc4,  0x8c,  0xff,  0xd4,  0x19,  0xed, 
 0x7f,  0xe8,  0x4e,  0x2a,  0x55,  0xf1,  0xdc,  0xf1,  0x2c,  0xd2,  0xdd,  0x5e,  0x71,  0x2c,  0xb7,  0xb1, 
 0xe5,  0x1f,  0xa3,  0xf2,  0xf2,  0xd4,  0x85,  0x4f,  0x13,  0xae,  0xf3,  0x2d,  0xfe,  0x21,  0x57,  0xf3, 
 0xe6,  0x32,  0x0d,  0xf1,  0x56,  0x7c,  0x5a,  0x93,  0x54,  0x1c,  0x3b,  0x10,  0x86,  0xa4,  0xba,  0x46, 
 0xf9,  0x8d,  0xf2,  0x19,  0x12,  0x04,  0x8a,  0x82,  0xed,  0x6f,  0x0a,  0x49,  0x06,  0x5e,  0x94,  0xe7, 
 0x0c,  0xf1,  0xb7,  0xb7,  0x26,  0xac,  0x8e,  0x3d,  0x19,  0x1d,  0xc5,  0x32,  0xa3,  0x3c,  0x52,  0xc9, 
 0xf2,  0x0d,  0x47,  0x26,  0x4b,  0x4a,  0x66,  0xfa,  0x94,  0xbb,  0xfe,  0x34,  0xf3,  0x2f,  0x7d,  0xf1, 
 0x2f,  0x93,  0xf0,  0x5b,  0x8c,  0xef,  0xbe,  0x11,  0x90,  0x31,  0xc4,  0x88,  0x17,  0xeb,  0xf2,  0x04, 
 0xac,  0x28,  0xf9,  0x19,  0x64,  0x23,  0x5d,  0xc9,  0x81,  0xe3,  0xf8,  0xfa,  0xdf,  0xf1,  0x5e,  0x73, 
 0x97,  0xe7,  0xe8,  0x64,  0x40,  0xdf,  0x64,  0xaf,  0x76,  0xcd,  0x4c,  0xbc,  0xf2,  0x6f,  0x01,  0xea, 
 0x49,  0x00,  0xbc,  0x02,  0x80,  0x58,  0xc3,  0xa8,  0xda,  0xe3,  0x52,  0xf1,  0x2e,  0xd7,  0x30,  0x7b, 
 0x8e,  0xc5,  0x7c,  0x4b,  0x34,  0x86,  0x46,  0xa9,  0x62,  0xa1, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x80,  0x01,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0xb8,  0x00,  0x00,  0x00,  0xb4,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x3d,  0xeb,  0xb5,  0x11,  0xcc,  0xa4,  0xdc,  0x63,  0xf4,  0xba,  0x83, 
 0xde,  0x11,  0x70,  0x00,  0x55,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x94,  0x00,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0xdc,  0xf1,  0x2c,  0xd2,  0xdd,  0x5e,  0x71, 
 0x2c,  0xb7,  0xb1,  0xe5,  0x1f,  0xa3,  0xf2,  0x00,  0x48,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf1,  0x56,  0x7c,  0x5a,  0x93,  0x54,  0x1c,  0x3b,  0x10,  0x86,  0xa4,  0xba,  0x46,  0xf9,  0x8d,  0x00, 
 0x37,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0xb7,  0xb7,  0x26,  0xac,  0x8e,  0x3d,  0x19, 
 0x1d,  0xc5,  0x32,  0xa3,  0x3c,  0x52,  0xc9,  0x00,  0x52,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf1,  0x2f,  0x93,  0xf0,  0x5b,  0x8c,  0xef,  0xbe,  0x11,  0x90,  0x31,  0xc4,  0x88,  0x17,  0xeb,  0x00, 
 0x55,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0x5e,  0x73,  0x97,  0xe7,  0xe8,  0x64,  0x40, 
 0xdf,  0x64,  0xaf,  0x76,  0xcd,  0x4c,  0xbc,  0x00,  0x47,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf1,  0x2e,  0xd7,  0x30,  0x7b,  0x8e,  0xc5,  0x7c,  0x4b,  0x34,  0x86,  0x46,  0xa9,  0x62,  0xa1,  0x00, 
 0x57,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0xb8,  0x00,  0x00,  0x00,  0xb4,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0x24,  0x77,  0x5a,  0x9f,  0x85,  0xf9,  0x7b,  0x5b,  0xeb,  0x5a,  0x31, 
 0xb0,  0x0c,  0x05,  0x00,  0xa7,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x94,  0x00,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0xe5,  0x76,  0x5e,  0xc4,  0x8c,  0xff,  0xd4, 
 0x19,  0xed,  0x7f,  0xe8,  0x4e,  0x2a,  0x55,  0x00,  0x7f,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0xd4,  0x85,  0x4f,  0x13,  0xae,  0xf3,  0x2d,  0xfe,  0x21,  0x57,  0xf3,  0xe6,  0x32,  0x0d,  0x00, 
 0x76,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0x19,  0x12,  0x04,  0x8a,  0x82,  0xed,  0x6f, 
 0x0a,  0x49,  0x06,  0x5e,  0x94,  0xe7,  0x0c,  0x00,  0x9d,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0x0d,  0x47,  0x26,  0x4b,  0x4a,  0x66,  0xfa,  0x94,  0xbb,  0xfe,  0x34,  0xf3,  0x2f,  0x7d,  0x00, 
 0x96,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0x04,  0xac,  0x28,  0xf9,  0x19,  0x64,  0x23, 
 0x5d,  0xc9,  0x81,  0xe3,  0xf8,  0xfa,  0xdf,  0x00,  0x7c,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0x6f,  0x01,  0xea,  0x49,  0x00,  0xbc,  0x02,  0x80,  0x58,  0xc3,  0xa8,  0xda,  0xe3,  0x52,  0x00, 
 0x94,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.pose(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_& instance, bool as_key) {
  auto &props = get_type_props<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.pose(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_& instance, bool as_key) {
  auto &props = get_type_props<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.pose(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_& instance, bool as_key) {
  auto &props = get_type_props<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.pose(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_& instance, bool as_key) {
  auto &props = get_type_props<::geometry_msgs::msg::dds_::PoseWithCovarianceStamped_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_ROS2_POSEWITHCOVARIANCESTAMPED__HPP
