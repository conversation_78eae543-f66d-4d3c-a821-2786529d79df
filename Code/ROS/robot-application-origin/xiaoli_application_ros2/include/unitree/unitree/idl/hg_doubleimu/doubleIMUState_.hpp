/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: doubleIMUState_.idl
  Source: doubleIMUState_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_HG_DOUBLEIMU_DOUBLEIMUSTATE__HPP
#define DDSCXX_UNITREE_IDL_HG_DOUBLEIMU_DOUBLEIMUSTATE__HPP

#include <cstdint>
#include <array>

namespace unitree_hg_doubleimu
{
namespace msg
{
namespace dds_
{
class doubleIMUState_
{
private:
 std::array<float, 4> quaternion_ = { };
 std::array<float, 3> gyroscope_ = { };
 std::array<float, 3> accelerometer_ = { };
 std::array<float, 3> rpy_ = { };
 int16_t temperature_ = 0;
 uint32_t tick_ = 0;

public:
  doubleIMUState_() = default;

  explicit doubleIMUState_(
    const std::array<float, 4>& quaternion,
    const std::array<float, 3>& gyroscope,
    const std::array<float, 3>& accelerometer,
    const std::array<float, 3>& rpy,
    int16_t temperature,
    uint32_t tick) :
    quaternion_(quaternion),
    gyroscope_(gyroscope),
    accelerometer_(accelerometer),
    rpy_(rpy),
    temperature_(temperature),
    tick_(tick) { }

  const std::array<float, 4>& quaternion() const { return this->quaternion_; }
  std::array<float, 4>& quaternion() { return this->quaternion_; }
  void quaternion(const std::array<float, 4>& _val_) { this->quaternion_ = _val_; }
  void quaternion(std::array<float, 4>&& _val_) { this->quaternion_ = _val_; }
  const std::array<float, 3>& gyroscope() const { return this->gyroscope_; }
  std::array<float, 3>& gyroscope() { return this->gyroscope_; }
  void gyroscope(const std::array<float, 3>& _val_) { this->gyroscope_ = _val_; }
  void gyroscope(std::array<float, 3>&& _val_) { this->gyroscope_ = _val_; }
  const std::array<float, 3>& accelerometer() const { return this->accelerometer_; }
  std::array<float, 3>& accelerometer() { return this->accelerometer_; }
  void accelerometer(const std::array<float, 3>& _val_) { this->accelerometer_ = _val_; }
  void accelerometer(std::array<float, 3>&& _val_) { this->accelerometer_ = _val_; }
  const std::array<float, 3>& rpy() const { return this->rpy_; }
  std::array<float, 3>& rpy() { return this->rpy_; }
  void rpy(const std::array<float, 3>& _val_) { this->rpy_ = _val_; }
  void rpy(std::array<float, 3>&& _val_) { this->rpy_ = _val_; }
  int16_t temperature() const { return this->temperature_; }
  int16_t& temperature() { return this->temperature_; }
  void temperature(int16_t _val_) { this->temperature_ = _val_; }
  uint32_t tick() const { return this->tick_; }
  uint32_t& tick() { return this->tick_; }
  void tick(uint32_t _val_) { this->tick_ = _val_; }

  bool operator==(const doubleIMUState_& _other) const
  {
    (void) _other;
    return quaternion_ == _other.quaternion_ &&
      gyroscope_ == _other.gyroscope_ &&
      accelerometer_ == _other.accelerometer_ &&
      rpy_ == _other.rpy_ &&
      temperature_ == _other.temperature_ &&
      tick_ == _other.tick_;
  }

  bool operator!=(const doubleIMUState_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_hg_doubleimu::msg::dds_::doubleIMUState_>::getTypeName()
{
  return "unitree_hg_doubleimu::msg::dds_::doubleIMUState_";
}

template <> constexpr bool TopicTraits<::unitree_hg_doubleimu::msg::dds_::doubleIMUState_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_hg_doubleimu::msg::dds_::doubleIMUState_>::type_map_blob_sz() { return 554; }
template<> constexpr unsigned int TopicTraits<::unitree_hg_doubleimu::msg::dds_::doubleIMUState_>::type_info_blob_sz() { return 100; }
template<> inline const uint8_t * TopicTraits<::unitree_hg_doubleimu::msg::dds_::doubleIMUState_>::type_map_blob() {
  static const uint8_t blob[] = {
 0xbb,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf1,  0x8b,  0xcc,  0xc5,  0x2b,  0x0f,  0x1a,  0xc2, 
 0x5a,  0xa6,  0x45,  0x4a,  0x3f,  0x62,  0x20,  0x00,  0xa3,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x93,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x04,  0x09,  0x75,  0xb9,  0xaa,  0xa0,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x09,  0x6b,  0x3b,  0x2d,  0x85,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x0e,  0x12, 
 0x75,  0x69,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0xb0,  0x7d,  0x92,  0xed,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x03,  0xee,  0xf4,  0x38,  0xf7,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0xe5,  0xe5,  0xc0,  0x75,  0x00, 
 0x3f,  0x01,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0xd0,  0x8d,  0x00,  0x25,  0xc9,  0x25,  0x90, 
 0xdf,  0xc3,  0x06,  0x18,  0x52,  0xa9,  0xf7,  0x00,  0x27,  0x01,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00, 
 0x39,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x31,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74, 
 0x72,  0x65,  0x65,  0x5f,  0x68,  0x67,  0x5f,  0x64,  0x6f,  0x75,  0x62,  0x6c,  0x65,  0x69,  0x6d,  0x75, 
 0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x64,  0x6f,  0x75, 
 0x62,  0x6c,  0x65,  0x49,  0x4d,  0x55,  0x53,  0x74,  0x61,  0x74,  0x65,  0x5f,  0x00,  0x00,  0x00,  0x00, 
 0xdf,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x25,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x09,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x71,  0x75,  0x61,  0x74,  0x65,  0x72,  0x6e,  0x69,  0x6f,  0x6e,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00, 
 0x67,  0x79,  0x72,  0x6f,  0x73,  0x63,  0x6f,  0x70,  0x65,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x09,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00,  0x61,  0x63,  0x63,  0x65,  0x6c,  0x65,  0x72,  0x6f, 
 0x6d,  0x65,  0x74,  0x65,  0x72,  0x00,  0x00,  0x00,  0x1e,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x72,  0x70,  0x79,  0x00,  0x00,  0x00,  0x00,  0x00,  0x1a,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x03,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x74,  0x65,  0x6d,  0x70, 
 0x65,  0x72,  0x61,  0x74,  0x75,  0x72,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00,  0x05,  0x00,  0x00,  0x00,  0x74,  0x69,  0x63,  0x6b, 
 0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0xd0,  0x8d,  0x00, 
 0x25,  0xc9,  0x25,  0x90,  0xdf,  0xc3,  0x06,  0x18,  0x52,  0xa9,  0xf7,  0xf1,  0x8b,  0xcc,  0xc5,  0x2b, 
 0x0f,  0x1a,  0xc2,  0x5a,  0xa6,  0x45,  0x4a,  0x3f,  0x62,  0x20, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_hg_doubleimu::msg::dds_::doubleIMUState_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x60,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x8b,  0xcc,  0xc5,  0x2b,  0x0f,  0x1a,  0xc2,  0x5a,  0xa6,  0x45,  0x4a, 
 0x3f,  0x62,  0x20,  0x00,  0xa7,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0xd0,  0x8d,  0x00,  0x25,  0xc9,  0x25,  0x90,  0xdf,  0xc3,  0x06,  0x18, 
 0x52,  0xa9,  0xf7,  0x00,  0x2b,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_hg_doubleimu::msg::dds_::doubleIMUState_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_hg_doubleimu::msg::dds_::doubleIMUState_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_hg_doubleimu::msg::dds_::doubleIMUState_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_hg_doubleimu::msg::dds_::doubleIMUState_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_hg_doubleimu::msg::dds_::doubleIMUState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.quaternion()[0], instance.quaternion().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.gyroscope()[0], instance.gyroscope().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.accelerometer()[0], instance.accelerometer().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.rpy()[0], instance.rpy().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.temperature()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.tick()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_hg_doubleimu::msg::dds_::doubleIMUState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg_doubleimu::msg::dds_::doubleIMUState_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_hg_doubleimu::msg::dds_::doubleIMUState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.quaternion()[0], instance.quaternion().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.gyroscope()[0], instance.gyroscope().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.accelerometer()[0], instance.accelerometer().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.rpy()[0], instance.rpy().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.temperature()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.tick()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_hg_doubleimu::msg::dds_::doubleIMUState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg_doubleimu::msg::dds_::doubleIMUState_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_hg_doubleimu::msg::dds_::doubleIMUState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.quaternion()[0], instance.quaternion().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.gyroscope()[0], instance.gyroscope().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.accelerometer()[0], instance.accelerometer().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.rpy()[0], instance.rpy().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.temperature()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.tick()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_hg_doubleimu::msg::dds_::doubleIMUState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg_doubleimu::msg::dds_::doubleIMUState_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_hg_doubleimu::msg::dds_::doubleIMUState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.quaternion()[0], instance.quaternion().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.gyroscope()[0], instance.gyroscope().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.accelerometer()[0], instance.accelerometer().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.rpy()[0], instance.rpy().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.temperature()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.tick()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_hg_doubleimu::msg::dds_::doubleIMUState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg_doubleimu::msg::dds_::doubleIMUState_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_HG_DOUBLEIMU_DOUBLEIMUSTATE__HPP
