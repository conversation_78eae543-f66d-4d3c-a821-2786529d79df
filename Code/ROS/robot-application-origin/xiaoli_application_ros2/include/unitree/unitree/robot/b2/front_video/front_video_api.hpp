#ifndef __UT_ROBOT_B2_FRONT_VIDEO_API_HPP__
#define __UT_ROBOT_B2_FRONT_VIDEO_API_HPP__

#include <unitree/common/json/jsonize.hpp>

namespace unitree
{
namespace robot
{
namespace b2
{
/*service name*/
const std::string ROBOT_FRONT_VIDEO_SERVICE_NAME = "front_videohub";

/*api version*/
const std::string ROBOT_FRONT_VIDEO_API_VERSION = "1.0.0.0";

/*api id*/
const int32_t ROBOT_FRONT_VIDEO_API_ID_GETIMAGESAMPLE = 1001;
}
}
}

#endif //__UT_ROBOT_B2_FRONT_VIDEO_API_HPP__
