export ZMQ_VERSION=4.3.4
export PREFIX=${PREFIX:-/usr/local}
export PYZMQ=${PYZMQ:-pyzmq}




pip3 uninstall pyzmq
set -ex
echo "installing libzmq to $PREFIX"
if [ ! -d "./zeromq-${ZMQ_VERSION}" ]; then
  wget https://github.com/zeromq/libzmq/releases/download/v${ZMQ_VERSION}/zeromq-${ZMQ_VERSION}.tar.gz -O libzmq.tar.gz
  tar -xzf libzmq.tar.gz
fi

cd zeromq-${ZMQ_VERSION}
./configure --prefix=${PREFIX} --enable-drafts
make -j 8 && make install

# install pyzmq with drafts enabled
# --install-option disables installing pyzmq from wheels,
# which do not have draft support

echo "installing ${PYZMQ}"
export ZMQ_PREFIX=${PREFIX}
export ZMQ_DRAFT_API=1
#pip3 install -v --no-binary pyzmq --pre pyzmq
pip3 install -Iv pyzmq==20.0.0 --install-option=--enable-drafts --install-option=--zmq=${PREFIX}

cat << END | python
import sys
import zmq
print('python: %s' % sys.executable)
print(sys.version)
print('pyzmq-%s' % zmq.__version__)
print('libzmq-%s' % zmq.zmq_version())
print('Draft API available: %s' % zmq.DRAFT_API)
END
sleep 0.5
echo "Deleting the files"
cd ..
rm -rf "./zeromq-${ZMQ_VERSION}"
rm libzmq.tar.gz
