#ifndef TOPICS_LIB_DEFINES_H_
#define TOPICS_LIB_DEFINES_H_

// Sensor topics
#define LASER_TOPIC ("/scan")
#define SCAM_TOPIC ("/scam")
#define LIDAR_SPEED_PERCENT ("/Motherboard/lidar_speed_percent")
#define FAN_SPEED_PERCENT ("/Motherboard/fan_speed_percent")

#define GROUND_CAMERA_POINT_CLOUD_TOPIC ("/ground_camera_point_cloud")
#define GROUND_CAMERA_SNAPSHOT_TOPIC ("/ground_raw_snapshot")
#define GROUND_PLANE ("/ground_plane")

#define GROUND_CAMERA_RGB_IMAGE_TOPIC ("/ground_camera_rgb_image")

#define HEAD_CAMERA_POINT_CLOUD_TOPIC ("/head_camera_point_cloud")
#define PROJECTED_DEPTH_TOPIC ("/camera/depth/projected")
#define DEPTH_RECORDER ("/depth_recorder")

//#define ODOM_ROSAIA_TOPIC ("/RosAria/pose")
#define ODOM_POSE_PUB_TOPIC ("/odom")
#define ODOM_R_MEASURED_TOPIC ("/odom_R_measured")
#define ODOM_R_REQ_TOPIC ("/odom_R_req")
#define JOY_TOPIC ("/joy")
#define JOY_VELOCITY_TOPIC ("/joy/cmd_vel")
#define SOM_RESET_CONFIG_TOPIC ("/Motherboard/som_reset_config")
#define FRONT_RANGE_FINDER_TOPIC ("/Motherboard/tof")

#define PID_SPEED_CONFIG ("/Motherboard/pid_speed_config")
#define MOTHERBOARD_MB2TILT_TOPIC ("/Motherboard/Mb2tilt_meseges")
#define MOTHERBOARD_LEFT_PWM_TOPIC ("/Motherboard/left_PWM")
#define MOTHERBOARD_RIGHT_PWM_TOPIC ("/Motherboard/right_PWM")
#define MOTHERBOARD_PID_POSITION_TOPIC ("/Motherboard/pid_position_config")
#define MOTHERBOARD_TOP_POWER_TOPIC ("/Motherboard/top_power")
#define MOTHERBOARD_SET_RELATIVE_POS_TOPIC ("/Motherboard/set_relative_position")
#define MOTHERBOARD_MOTOR_OFF_TOPIC ("/Motherboard/motor_off")
#define MOTHERBOARD_SET_MOTOR_POWER_OPEN_LOOP_TOPIC ("/Motherboard/set_motor_power_open_loop")
#define MOTHERBOARD_DELAYED_SHUTDOWN_TOPIC ("/Motherboard/delayed_shutdown")
#define MOTHERBOARD_DELAYED_RESET_TOPIC ("/Motherboard/delayed_reset")
#define MOTHERBOARD_PID_MIMO_TOPIC ("/Motherboard/pid_mimo_config")
#define MOTHERBOARD_CFG_IMM_TOPIC ("/Motherboard/Config_IMM")

//--Motherboard--//
#define MOTHERBOARD_CALIBRATE_MAG_TOPIC ("/Motherboard/calibrate_mag_sensors")
#define MOTHERBOARD_PROXIMITY_TOPIC ("/Motherboard/proximity")
#define MOTHERBOARD_DEBUG_STATUS_TOPIC ("/Motherboard/mbDebugStatus")
#define MOTHERBOARD_ACCELOMETER_TOPIC ("/Motherboard/accelerometer")
#define MOTHERBOARD_GYRO_TOPIC ("/Motherboard/gyro")
#define MOTHERBOARD_EstVel ("/Motherboard/EstVel")
#define MOTHERBOARD_TOF_CONFIG_TOPIC ("/Motherboard/tof_config")
#define MOTHERBOARD_ACC_DEC_TOPIC ("/Motherboard/sergeyForgotToNameItToo")
#define MOTHERBOARD_ADVANCE_STATUS_TOPIC ("/Motherboard/mbAdvanceStatus")
// IMU
#define MOTHERBOARD_IMU_DATA_RAW ("/imu/data_raw")
#define MOTHERBOARD_IMU_DEBUG_TOPIC ("/Motherboard/imu_debug")

#define TILT_TILT2MB_TOPIC ("/Tilt/Tilt2Mb_meseges")
#define TILT_POSE_TOPIC ("/Tilt/pose")
#define TILT_EQUALIZER_TOPIC ("/Tilt/equalizer")
#define TILT_WIRELESSCHARGER_TOPIC ("/Tilt/wirlessCharger_report")
#define TILT_MOTOR_OFF_TOPIC ("/Tilt/motor_off")
#define TILT_SET_ANGLE_TOPIC ("/Tilt/set_angle")
#define TILT_SET_ANGLE_RELATIVE_TOPIC ("/Tilt/set_angle_relative")
#define TILT_CLOSED_LOOP_SPEED_TOPIC ("/Tilt/closed_loop_speed")
#define TILT_OPEN_LOOP_SPEED_TOPIC ("/Tilt/open_loop_speed")
#define TILT_AUDIO_ON_TOPIC ("/Tilt/audio_power_on")
#define TILT_FW_RESET_TOPIC ("/Tilt/fw_reset_cmd")
#define TILT_TOF_TOPIC ("/Tilt/tof")
#define TILT_COMM_OUT_TOPIC ("/Tilt/sergeyForgotToNameIt")
#define TILT_TOF_CONFIG_TOPIC ("/Tilt/tof_config")
#define TILT_AUDIO_PROFILE_TOPIC ("/Tilt/tilt_audio_profile_cmd")
#define TILT_ADVANCE_STATUS_TOPIC ("/Tilt/advance_status")

#define BUTTON_EVENT_TOPIC ("/Head/button_event")
#define HEAD_IMU_EULER_TOPIC ("/Head/imu_euler")
#define CALIBRATE_MPU6050_TOPIC ("/Head/calibrate_mpu6050")
#define HEAD_FW_RESET_CMD_TOPIC ("/Head/fw_reset_cmd")
#define MIC_GAIN_CMD_TOPIC ("/Head/Mic_Gain_cmd")

#define HEAD_PIPO_DEBUG_CMD ("/Head/pipo_debug_cmd")
#define IMU_DATA_RAW_TOPIC ("/Head/imu_data_raw")
#define HEAD_ADVANCE_STATUS_TOPIC ("/Head/advanceStatus")
#define HEAD_TOF_CAMERA_ON_OFF ("/Head/tof_camera_on_off")

// LuckyStar Sensor
#define HEAD_IMU ("/Head/IMU")

// Led topics
#define CAROUSEL_LED_TOPIC ("/carousel_animation")
#define HEAD_TRANS_SOLID_ANIMATION_TOPIC ("/Head/solid_animation")
#define HEAD_BREATH_ANIMATION_TOPIC ("/Head/breath_animation")
#define HEAD_TRANS_ANIMATION_TOPIC ("/Head/trans_animation")
#define HEAD_POINTER_ANIMATION_TOPIC ("/Head/pointer_animation")

// Navigation topics
#define LOCAL_MAP_TOPIC ("/local_obs_map")
#define LOCAL_COST_MAP_TOPIC ("/local_cost_map")
#define GLOBAL_COST_MAP_TOPIC ("/global_cost_map")
#define LOCAL_SEARCH_MAP_TOPIC ("/local_search_map")
#define GLOBAL_SEARCH_MAP_TOPIC ("/global_search_map")
#define LOCAL_MAP_POINTCLOUD_TOPIC ("/local_map_pointcloud")
#define FOLLOWER_LOCAL_MAP_TOPIC ("/follower_local_map")
#define JOINT_CALIBRATION_DEBUG_INFO ("/joint_calibration_debug_info")
#define OUTCAST_POINTS ("/outcast_points")
#define LOCAL_MAP_DEBUG_TOPIC ("/local_map_debug")
#define GLOBAL_MAP_TOPIC ("/map")
#define GLOBAL_EDIT_MAP_TOPIC ("/edit_map")
#define MAP_DIVERGE_NOTICE_TOPIC ("/diverge_notice")

#define RANGEFINDERS_TOPIC ("/rangefinders")
#define BACK_RANGEFINDERS_TOPIC ("/back_rangefinders")

#define GCAM_HOMEBASE_POSES_TOPIC ("/gcam_hb_poses")
#define VIS_GCAM_HB_POSES ("/vis_gcam_hb_poses")

// SLAM
#define TRAJECTORY_NODE_LIST ("/trajectory_node_list")
#define TRAJECTORY_NODE_DATA ("/trajectory_nodes_data")
#define TRAJECTORY_NODE_DATA_VIZ ("/trajectory_node_data_viz")
#define SUB_MAP_LIST ("/sub_map_list")
#define VISUALIZATION_LANDMARKS_POSES_LIST ("/visualization_landmark_poses_list")
#define CONSTRAINT_LIST ("/constraint_list")
#define SCAN_MATCHED_POINT_CLOUD ("/scan_matched_point_cloud")
#define TRACKED_POSE ("/tracked_pose")
// RM TO SLAM
#define LANDMARKS_POSES_LIST ("/landmark_poses_list")

// RM TO COMMAND PARSER
#define GENERAL_STATUS_TOPIC ("/general_status")
#define PERSON_DETECTION_TOPIC ("/person_detection")
#define MAP_PARAMETERS_TOPIC ("/map_parameters")
#define REPORT_DETAILED_POSE_TOPIC ("/report_detailed_pose")

// Other topics
#define COMMAND_TOPIC ("/command")
#define VELOCITY_COMMAND_OUT_TOPIC ("/RosAria/cmd_vel")
#define SYSCOMMAND_TOPIC ("/syscommand")
#define ASR_STATE_TOPIC ("/asr_state")
#define ASR_RESULT_TOPIC ("/asr_result")
#define FATAL_TO_USER_TOPIC ("/fatal_to_user")

#define PUB_ANGLE_TOPIC ("/pub_angle")
#define TOF_ZONE_TOPIC ("/tof_zone")
#define DANGER_AREA_TOPIC ("/vis_danger_area_array")
#define DOCKING_STATUS_TOPIC ("/docking_status")
#define TARGETS_TOPIC ("/vis_dynamic_tracking")
#define DEPTH_OBJECT_TOPIC ("/vis_depth_object")
#define SSD_OBJECTS_TOPIC ("/vis_ssd_objects")
#define LASER_PTS_TOPIC ("/vis_laser_pts")
#define DYNAMIC_MAP_POINTS_TOPIC ("/map_pnts")

#define RM_GOTO_PATH_TOPIC ("/rm_goto_path")
#define RM_ROBOT_POSE_AND_VELOCITY ("/RM_robot_pose_and_velocity")
#define RM_VIS_RADIUS_TOPIC ("/vis_robot_radius")
#define RM_VIS_CIRCLE_CENTER_TOPIC ("/vis_circle_center")
#define RM_VIS_SAVED_LOCATIONS_TOPIC ("/RM_saved_locations_markers")
#define RM_GENERAL_DEBUGGING_TOPIC ("/RM_general_debugging")
//#define HEY_TEMI_DIRECTION_TOPIC ("/hey_teamy_direction")
#define TRIGGER_WORD_WAKEUP_TOPIC ("/trigger_word_wakeup")
#define MIC_DIRECTION_TOPIC ("/voice_direction")
#define WORD_STOP_TOPIC ("/stop_word_wakeup")

// Be With
#define DYNAMIC_OBJECT_TOPIC ("/dynamic_object")
#define LASER_OBJECT_TOPIC ("/laser_object")
#define LOOK_AT_ME_TURN ("/look_at_me_turn")
#define LOOK_AT_ME_STATUS ("/look_at_me_status")
#define RM_TO_DEPTH_COMMAND ("/rm_to_depth_command")
#define NAN_ANGLE_TOPIC ("/nan_array")
#define MAP_PROGECTED_DEPTH_TOPIC ("/map_realsense")
#define POINT_CLOUD2_TOPIC ("/points2")
#define MATCH_POINT_CLOUD2_TOPIC ("/match_point")
#define POINT_CLOUD_DOCKING_TOPIC ("/points2_docking")

// RM + Depth Track
#define SSD_PEOPLE_DETECTION_TOPIC ("/ssd_people_detection")

// Path Plan
#define PATH_PLAN_TOPIC ("/path_plan_topic")  //[PATH_PLAN]
#define PATH_PLAN_LOCAL_MAP_TOPIC ("/path_plan_local_map")
#define PATH_PLAN_GLOBAL_MAP_TOPIC ("/path_plan_global_map")
#define PATH_PLAN_COMMAND_TOPIC ("/path_plan_command")

// Dynamic Tracking + Depth Track
#define DEPTH_TARGETS_DISTANCE_TOPIC ("/depth_distance")

// Health Monitor
#define HEALTH_MONITOR_TOPIC ("/robotNodesStatus")
#define HEALTH_MONITOR_KEEP_ALIVE_TOPIC ("/keepalive")
#define ALL_NODES_STATUS_TOPIC ("/nodes_status")

// MAPS
#define TRAVERSABILITY_NODES ("/traversability_nodes")
#define MAP_ELEMENTS ("/map_elements")

// RVIZ
#define RVIZ_NAV_MSGS_TOPIC ("/move_base_simple/goal")
#define CLICKED_POINT ("/clicked_point")
#define INIITAL_POSE_TOPIC ("/initialpose")

// RM to MAP
#define OBJECT_TO_MAP ("/object_to_map")

// RM to Dynamic
#define RM_TO_DYNAMIC ("/rm_to_dynamic")

// RM to Motherbard
#define RM_MIC_TOPIC ("/rm_mic_topic")

// ST TOF BOTTOM
#define TOF_BOTTOM_TOPIC ("/TOF_bottom/samples")
#define TOF_BOTTOM_CONFIG_TOPIC ("/TOF_bottom/tof_config")

// tf
#define MAP_TO_ODOM_TOPIC ("/map_to_odom_topic")
#define MAP_TO_LOCAL_MAP_TOPIC ("/map_to_localmap_topic")
#define ODOM_TO_BASE_LINK_TOPIC ("/odom_to_base_link_topic")
#define BASE_LINK_TO_TILT_TOPIC ("/base_link_to_tilt_topic")
#define GENERIC_TF_PUB_TOPIC ("/generic_tf_pub_topic")
#define TF_TOPIC ("/tf")

//------DOCKING NODE------//
// Docking to RM
#define DOCKING_STATION_OBJECT_TOPIC ("/docking_station_object_topic")
// RM to Docking
#define DETECT_DOCKING_STATION_REQUEST_TOPIC ("/detect_docking_station_request_topic")

// MARKERS
#define DOCKING_GENERAL_DEBUGGING_TOPIC ("/docking_general_debugging")

// Events
#define ROBOX_STATUS_TOPIC ("/robox_status_topic")
#define ROBOX_EVENTS_TOPIC ("/robox_events")
#define MOTHERBOARD_CLIFF_SENSOR_STATUS_TOPIC ("/Motherboard/cliff_sensors_status")
#define ROBOX_CONFIGURATION_TOPIC ("/robox_configuration")
#define BATTERY_INFO_TOPIC ("/battery_info")

#define COMMAND_PARSER_DEBUG_INFO ("/command_parser_debug_info")
#define HEAD_CAMERA_RAW_DEPTH_TOPIC ("/head_camera/depth_raw_image")
#define HEAD_CAMERA_RAW_RGB_TOPIC ("/head_camera/rgb_raw_image")
#define HEAD_CAMERA_DEPTH_INFO_TOPIC ("/head_camera/depth/camera_info")
#define HEAD_CAMERA_RGB_INFO_TOPIC ("/head_camera/rgb/camera_info")
// #define GROUND_CAMERA_RAW_RGB_TOPIC ("/ground_camera/rgb_raw_image")
// #define GROUND_CAMERA_RAW_DEPTH_TOPIC ("/ground_camera/depth_raw_image")

#endif /* LIB_DEFINES_H_ */
