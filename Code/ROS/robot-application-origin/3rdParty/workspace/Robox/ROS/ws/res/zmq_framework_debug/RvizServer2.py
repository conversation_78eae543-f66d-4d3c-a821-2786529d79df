#!/usr/bin/env python
import sys
import threading
import time
from io import BytesIO

import time
import rclpy
from rclpy.node import Node
from rosbags.typesys.msg import normalize_msgtype
from rosbags.serde import cdr_to_ros1, ros1_to_cdr, deserialize_cdr, serialize_cdr
from rosbags.typesys import types

from rclpy.serialization import deserialize_message, serialize_message
from tf2_ros import TransformBroadcaster
import zmq
from geometry_msgs.msg import PoseStamped
from geometry_msgs.msg import PointStamped
from geometry_msgs.msg import PoseWithCovarianceStamped
from multiprocessing import Queue

import config
import importlib


class RvizServer(Node):

  def __init__(self, robot_address):
    super().__init__('zmq_rviz_server')

    self.robot_address = robot_address
    if config.ROBOT_PRE_TCP in robot_address:
      self.robot_ip = robot_address.replace(config.ROBOT_PRE_TCP, "")
    elif config.ROBOT_PRE_UDP in robot_address:
      self.robot_ip = robot_address.replace(config.ROBOT_PRE_UDP, "")
    else:
      self.robot_ip = robot_address
    self.run()


  def signal_handler(self, sig, frame):
    pass

  def get_topic_port(self, topic_name):
    import os
    HOME_FOLDER = os.path.expanduser("~")
    line_number = 0
    port_start = 0
    current_path = os.path.dirname(os.path.realpath(__file__))
    parent_path = os.path.abspath(os.path.join(current_path, os.pardir))
    define_topics_file_path = os.path.join(parent_path, "generic_lib", "defines_topics.h")
    defines_file_path = os.path.join(parent_path, "generic_lib", "defines.h")

    with open(define_topics_file_path, 'r') as f:
      lines = f.readlines()
      for line in lines:
        line_number += 1
        if "#define" in line:
          if "/" in line:
            import re
            found = re.findall(r'"(.*?)"', line)
            if found:
              found = found[0]
              if topic_name == found:
                break

      with open(defines_file_path, 'r') as p:
        lines = p.readlines()
        for line in lines:
          if "#define ZMQ_PORT_START_NUM " in line:
            for l in line.split():
              if l.isdigit():
                port_start = int(l)
                break
            break

    return str(port_start + line_number)

  def zmq_receiver_thread(self, port, msgtype1_str, rviz_topic):
    socket_a = self.robot_address + ":" + str(port)
    print("Start topic: {}, socket_a: {}".format(rviz_topic, socket_a))
    subscriber_connected = False
    if config.ROBOT_PRE_TCP in socket_a:
      context = zmq.Context()
      subscriber = context.socket(zmq.SUB)
      try:
        subscriber.setsockopt_string(zmq.SUBSCRIBE, '')
      except TypeError:
        subscriber.setsockopt(zmq.SUBSCRIBE, b'')
      try:
        subscriber.setsockopt(zmq.CONFLATE, 1)  # to make the subscription only handle last message
        subscriber.connect(socket_a)
        subscriber_connected = True
      except Exception as e:
        print(e)
        print(rviz_topic, " Didn't connect TCP.")
    else:
      context = zmq.Context.instance()
      try:
        subscriber = context.socket(zmq.DISH)
        subscriber.setsockopt(zmq.CONFLATE, 1)
      except AttributeError:
        print("Your version of python-zmq is not updated.\n" \
            "Please run the following script (with sudo):\n" \
            "sudo sh {} \n".format(config.ZMQ_PYTHON_UPDATE_SCRIPT))
      try:
        subscriber.bind(socket_a)
        subscriber.join(str(port))
        subscriber_connected = True
      except Exception:
        print(rviz_topic, " Didn't connect UDP.")
    if not subscriber_connected:
      return
    msgtype2_str = normalize_msgtype(msgtype1_str)
    module_name = '.'.join(msgtype2_str.split('/')[:-1])
    class_name = msgtype2_str.split('/')[-1]
    msgtype2 = getattr(importlib.import_module(module_name), class_name)
    is_tf_topic = False
    if rviz_topic == "/tf":
      is_tf_topic = True
      ros_publisher = TransformBroadcaster(self)
    else:
      ros_publisher = self.create_publisher(msgtype2, rviz_topic, 10)

    while True:
      if 0:
        if subscriber_connected:
          print("No one is listening to: {}, disconnect it".format(rviz_topic))
          subscriber.disconnect(socket_a)
          subscriber_connected = False
        else:
          # print("Waiting for subscribers to connet: ", rviz_topic)
          time.sleep(1)
        continue
      else:
        if not subscriber_connected:
          subscriber.connect(socket_a)
          subscriber_connected = True
        else:
          pass
      try:
        data = subscriber.recv(copy=True)
        data = ros1_to_cdr(data, msgtype2_str)
        msg_deserialized = deserialize_message(data.tobytes(), msgtype2)
        if is_tf_topic:
          ros_publisher.sendTransform(msg_deserialized)
        else:
          if hasattr(msg_deserialized, 'header') and msg_deserialized.header.frame_id == "":
            # print("Message dropped: {}".format(msg_deserialized))
            continue
          ros_publisher.publish(msg_deserialized)
        # print("{} {}".format(rviz_topic, sys.getsizeof(data)))
      except KeyboardInterrupt:
        print("DONE!")
        exit(0)

  def create_remote_connections(self):
    with open(config.TOPIC_TO_RVIZ_FILE_PATH, 'r') as f:
      rviz_topic_list = [rviz.strip("\n") for rviz in f.readlines()]

    with open(config.TOPIC_TO_MSG_FILE_PATH, 'r') as f:
      topics_msgs_line = {topic_msg.split("=")[0].strip("\n"): topic_msg.split("=")[1].strip("\n") for topic_msg in f.readlines()}

    # signal.signal(signal.SIGINT, self.signal_handler)

    for rviz_topic in rviz_topic_list:
      if "#" not in rviz_topic:
        if rviz_topic == "/tf":
          for port in config.list_of_tf_topics:
            msgtype1_str = topics_msgs_line[rviz_topic]
            processThread = threading.Thread(target=self.zmq_receiver_thread, args=(port, msgtype1_str, rviz_topic))
            processThread.start()
        else:
          port = self.get_topic_port(rviz_topic)
          if rviz_topic not in topics_msgs_line:
            continue
          msgtype1_str = topics_msgs_line[rviz_topic]
          processThread = threading.Thread(target=self.zmq_receiver_thread, args=(port, msgtype1_str, rviz_topic))
          processThread.start()
        continue

  def run(self):
    self.create_remote_connections()


if __name__ == "__main__":
  if len(sys.argv) < 2:
    print("No robot number was entered! Please be focused!")
    sys.exit(0)
  else:
    rclpy.init(args=sys.argv)
    rviz_server = RvizServer(sys.argv[1])
    rclpy.spin(rviz_server)

    # Destroy the node explicitly
    # (optional - otherwise it will be done automatically
    # when the garbage collector destroys the node object)
    rviz_server.destroy_node()
    rclpy.shutdown()
