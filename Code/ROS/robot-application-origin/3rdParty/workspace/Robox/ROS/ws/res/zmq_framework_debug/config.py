import os
current_path = os.path.dirname(os.path.realpath(__file__)) 
STATIC_TRANFORM_FILE_PATH = os.path.join(current_path, "static_transform.py")
TOPIC_TO_RVIZ_FILE_PATH = os.path.join(current_path, "topic_to_rviz")
TOPIC_TO_MSG_FILE_PATH = os.path.join(current_path, "topic_to_msg")


HOME_FOLDER = os.path.expanduser("~")
ROBOT_CONFIG_FILE = os.path.join(HOME_FOLDER, "robot_config/tfConfig.txt")

NODE_NAME = "zmq_framework_debug"
ZMQ_FOLDER = os.path.join(HOME_FOLDER,"workspace","Robox","ROS","ws", "res", NODE_NAME)
ZMQ_PYTHON_UPDATE_SCRIPT = os.path.join(ZMQ_FOLDER, "update_python_zmq.sh")

PORT_ADDED = 10000
list_of_tf_topics = ["11900", "11901", "11902", "11903", "11904"]


ROBOT_PRE_TCP = "tcp://"
ROBOT_PRE_UDP = "udp://*"

data = [1] * 10000


RCV_TIMING = 5000

