cmake_minimum_required(VERSION 3.10)
#项目名称
PROJECT(SensorHub)

if(${CMAKE_HOST_UNIX})
set(CMAKE_CUSTOM_LIB_PATH "${CMAKE_CURRENT_SOURCE_DIR}/lib/${CMAKE_HOST_SYSTEM_PROCESSOR}")
else()
message(FATAL_ERROR "not support CMAKE_HOST_UNIX: ${CMAKE_HOST_UNIX}")
endif()

message(STATUS "CMAKE_CUSTOM_LIB_PATH: ${CMAKE_CUSTOM_LIB_PATH}")

add_executable(SensorHubNode
  src/sys_log.c
  src/sys_utils.c
  src/sensor_light.c
  src/sensor_touch.c
  src/sensor_hub.c
)

target_include_directories(SensorHubNode
  PRIVATE
  ${CMAKE_CURRENT_SOURCE_DIR}/include
  ${CMAKE_CURRENT_SOURCE_DIR}/src
)

target_link_libraries(SensorHubNode
  PRIVATE
  ${CMAKE_CUSTOM_LIB_PATH}/libubox.a
  pthread
)

add_executable(SensorHubNodeTest
  src/sys_log.c
  src/sys_utils.c
  src/sensor_hub_test.c
)

target_include_directories(SensorHubNodeTest
  PRIVATE
  ${CMAKE_CURRENT_SOURCE_DIR}/include
  ${CMAKE_CURRENT_SOURCE_DIR}/src
)

target_link_libraries(SensorHubNodeTest
  PRIVATE
  ${CMAKE_CUSTOM_LIB_PATH}/libubox.a
  pthread
)

install(TARGETS
  SensorHubNode
  SensorHubNodeTest
  DESTINATION ${CMAKE_CURRENT_SOURCE_DIR}/install/${PROJECT_NAME}
)
