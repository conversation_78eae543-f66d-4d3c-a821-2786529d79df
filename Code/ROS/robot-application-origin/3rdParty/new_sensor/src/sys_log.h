#ifndef _SYS_LOG_H_
#define _SYS_LOG_H_

#include "sys_common.h"

typedef enum
{
    SYS_LOG_LEVEL_DEBUG = 0,
    SYS_LOG_LEVEL_INFO,
    SYS_LOG_LEVEL_NOTE,
    SYS_LOG_LEVEL_ERROR,
    SYS_LOG_LEVEL_FATAL,
}SYS_LOG_LEVEL_E;

typedef enum
{
    SYS_LOG_MODE_FILE = 0,
    SYS_LOG_MODE_FUNC,
    SYS_LOG_MODE_TIME,
}SYS_LOG_MODE_E;

typedef struct _sys_log_t_
{
    INT32 level;
    INT32 mode;
    INT32 line;
    const INT8 *tag;
    const INT8 *file;
    const INT8 *func;
}SYS_LOG_T;

typedef struct _sys_log_capa_t_
{
    INT32 level;
    INT32 buf_size;
    INT32 log_size;
    INT32 log_num;
    INT8 log_path[64];
}SYS_LOG_CAPA_T;

VOID sys_log_init(const SYS_LOG_CAPA_T *capa);
VOID sys_log_deinit(VOID);
VOID sys_log_level_set(INT32 level);
INT32 sys_log_level_get(VOID);
INT8 *sys_log_level_str_get(INT32 level);
VOID sys_log_print(const SYS_LOG_T *log, const INT8 *fmt, ...);

#ifndef SYS_LOG_PRINT
#define SYS_LOG_PRINT(arg_tag, arg_level, arg_mode, fmt...) { \
    if(sys_log_level_get() <= arg_level) \
    { \
        SYS_LOG_T log; \
        memset(&log, 0, sizeof(SYS_LOG_T)); \
        log.level = (arg_level); \
        log.mode = (arg_mode); \
        log.tag = (arg_tag); \
        log.line = __LINE__; \
        log.file = __FILE__; \
        log.func = __FUNCTION__; \
        sys_log_print(&log, fmt); \
    } \
}
#endif

#endif // _SYS_LOG_H_