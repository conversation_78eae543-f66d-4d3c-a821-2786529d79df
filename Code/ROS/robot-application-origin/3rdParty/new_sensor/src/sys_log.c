#include <stdarg.h>
#include <time.h>
#include "sys_log.h"
#include <time.h>

#define SYS_LOG_VER ("1.0.0")

typedef struct _sys_log_ctrl_t_
{
    SYS_LOG_CAPA_T capa;
    struct tm last_time;
}SYS_LOG_CTRL_T;

static SYS_LOG_CTRL_T g_log_ctrl = {
    .capa.level = SYS_LOG_LEVEL_NOTE
};

SYS_LOG_CTRL_T *sys_log_ctrl_get(VOID)
{
    return &g_log_ctrl;
}

VOID sys_log_init(const SYS_LOG_CAPA_T *capa)
{
    printf("log version: %s\n", SYS_LOG_VER);
    memcpy(&g_log_ctrl.capa, capa, sizeof(SYS_LOG_CAPA_T));
    memset(&g_log_ctrl.last_time, 0, sizeof(struct tm));
}

VOID sys_log_deinit(VOID)
{
}

VOID sys_log_level_set(INT32 level)
{
    g_log_ctrl.capa.level = level;
}

INT32 sys_log_level_get(VOID)
{
    return g_log_ctrl.capa.level;
}

INT8 *sys_log_level_str_get(INT32 level)
{
    INT8 *str = NULL;

    switch(level)
    {
        case SYS_LOG_LEVEL_DEBUG:
            str = "D";
            break;
        case SYS_LOG_LEVEL_INFO:
            str = "I";
            break;
        case SYS_LOG_LEVEL_NOTE:
            str = "N";
            break;
        case SYS_LOG_LEVEL_ERROR:
            str = "E";
            break;
        case SYS_LOG_LEVEL_FATAL:
            str = "F";
            break;
        default:
            str = "X";
            break;
    }

    return str;
}

static VOID sys_log_filename_get(const INT8 *path, INT8 *file_name, INT32 size)
{
    INT32 i = 0;
    INT32 j = 0;
    INT32 len = 0;
    len = strlen(path);
    i = len - 1;
    while(i >= 0)
    {
        if('/' == path[i])
        {
            break;
        }
        i--;
        j++;
    }
    snprintf(file_name, size, "%s", path + (len - j));
}

VOID sys_log_print(const SYS_LOG_T *log, const INT8 *fmt, ...)
{
    SYS_PTR_CHECK_VOID(log, printf);
    if(log->level < g_log_ctrl.capa.level)
    {
        // printf("[%s: %d]not need printf\n", __FUNCTION__, __LINE__);
        return;
    }
    INT8 buf[256] = {0};
    INT32 len = 0;
    if(log->mode & (1 << SYS_LOG_MODE_TIME))
    {
        time_t cur_time = 0;
        struct tm p;
        memset(&p, 0, sizeof(struct tm));
        time(&cur_time);
        localtime_r(&cur_time, &p);
        if(p.tm_sec != g_log_ctrl.last_time.tm_sec)
        {
            len += snprintf(buf + len, sizeof(buf) - len, "[%04d-%02d-%02d_%02d:%02d:%02d]", (p.tm_year + 1900), (p.tm_mon + 1), \
                p.tm_mday, p.tm_hour, p.tm_min, p.tm_sec);
            memcpy(&g_log_ctrl.last_time, &p, sizeof(struct tm));
        }
    }
    INT8 *level_str = NULL;
    level_str = sys_log_level_str_get(log->level);
    len += snprintf(buf + len, sizeof(buf) - len, "[%s][%s]", level_str, log->tag);
    if((log->level >= SYS_LOG_LEVEL_ERROR) || (log->mode & (1 << SYS_LOG_MODE_FILE)))
    {
        INT8 file_name[128] = {0};
        sys_log_filename_get(log->file, file_name, sizeof(file_name));
        if(log->mode & (1 << SYS_LOG_MODE_FUNC))
        {
            len += snprintf(buf + len, sizeof(buf) - len, "[%s:%s:%d]", file_name, log->func, log->line);
        }
        else
        {
            len += snprintf(buf + len, sizeof(buf) - len, "[%s:%d]", file_name, log->line);
        }
    }
    else
    {
        if(log->mode & (1 << SYS_LOG_MODE_FUNC))
        {
            len += snprintf(buf + len, sizeof(buf) - len, "[%s:%d]", log->func, log->line);
        }
    }
    va_list args;
    va_start(args, fmt);
    len += vsnprintf(buf + len, sizeof(buf) - len, fmt, args);
    va_end(args);
    if('\n' != buf[len - 1])
    {
        if(len >= sizeof(buf))
        {
            buf[len - 1] = '\n';
        }
        else
        {
            buf[len] = '\n';
        }
    }
    // printf("len: %d\n", len);
    printf("%s", buf);
}