#include "sensor_hub.h"
#include "deep_cmd.h"
#include <netinet/in.h>
#include <sys/socket.h>
#include <unistd.h>

typedef struct _sensor_cmd_t_
{
    head_t head;
    UINT8 *buf;
    INT32 len;
}SENSOR_CMD_T;

#define FRAME_HEADER1 (0xEE)
#define FRAME_HEADER2 (0xEF)
#define FRAME_TAIL1 (0xEF)
#define FRAME_TAIL2 (0xFF)

typedef enum
{
    LIGHT_BLINK   = 1, // 流水
    LIGHT_BREATHE = 2, // 呼吸
    LIGHT_FLASH   = 3, // 闪烁
    LIGHT_KEEP    = 4, // 常亮
}SENSOR_LIGHT_E;

#define Frame_length         10

#define Test_ID              0x25
#define Test_version         0x30
#define Test_type            0x35
#define Test_hex             0x40
#define Test_size            0x45
#define Test_crc             0x50

#define Frame_ID             0x01
#define Frame_VERSION        0x01

#define Type_req            0x11
#define Type_cmd            0x12
#define Type_Order          0x15

#define Hex_MCU             0x30
#define Hex_Light           0x31
#define Hex_Duoji           0x32

static VOID sensor_hex_print(UINT8 *buf, INT32 size)
{
    INT32 i = 0;
    printf("data_len: %d\n", size);
    for(i = 1; i <= size; i++)
    {
        printf("0x%02x:", buf[i - 1]);
        if((i % 8) == 0)
        {
            printf("\n");
        }
    }
    printf("\n");
}

INT32 sensor_light_data_pack(const SENSOR_CMD_T *cmd, UINT8 *buf, INT32 size)
{
    INT32 ret          = SYS_OK;
    INT32 off          = 0;
    INT32 len_off      = 0;
    UINT8 light_id     = 0;
    UINT8 light_r      = 0;
    UINT8 light_g      = 0;
    UINT8 light_b      = 0;
    UINT8 light_type   = 0;
    UINT8 light_bright = 0;
    UINT16 light_freq  = 0;
    UINT32 light_state = 0;
    UINT32 light_cmd   = 0;

    light_cmd = SYS_B_2_L_32(cmd->buf, off);
    off += 4;
    SENSOR_NOTE("LIGHT_TYPE: 0x%08x", light_cmd);
    light_id = 0x66;
    switch(light_cmd)
    {
        case DEEP_CMD_LIGHT_1:
            // 蓝灯常亮（正常运行，联网正常）
            light_type   = LIGHT_KEEP;
            light_freq   = 500;
            light_r      = 0;
            light_g      = 0;
            light_b      = 255;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_2:
            // 蓝灯闪烁（开机自检）
            light_type   = LIGHT_FLASH;
            light_freq   = 500;
            light_r      = 0;
            light_g      = 0;
            light_b      = 255;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_3:
            // 蓝色呼吸灯（状态正常、等待联网中）
            light_type   = LIGHT_BREATHE;
            light_freq   = 500;
            light_r      = 0;
            light_g      = 0;
            light_b      = 255;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_C:
            // 蓝色流水灯
            light_type   = LIGHT_BLINK;
            light_freq   = 500;
            light_r      = 0;
            light_g      = 0;
            light_b      = 255;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_6:
            // 红灯常亮（低电量）
            light_type   = LIGHT_KEEP;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 0;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_7:
            // 红灯闪烁（故障，需要重启）
            light_type   = LIGHT_FLASH;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 0;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_D:
            // 红灯呼吸灯
            light_type   = LIGHT_BREATHE;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 0;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_E:
            // 红灯流水灯
            light_type   = LIGHT_BLINK;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 0;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_01:
            // 橙色闪烁
            light_type   = LIGHT_FLASH;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 140;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_02:
            // 粉色闪烁
            light_type   = LIGHT_FLASH;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 192;
            light_b      = 203;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_03:
            // 白色呼吸
            light_type   = LIGHT_BREATHE;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 255;
            light_b      = 255;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_04:
            // 白色常亮
            light_type   = LIGHT_KEEP;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 255;
            light_b      = 255;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_05:
            // 橙色呼吸
            light_type   = LIGHT_BREATHE;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 140;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_06:
            // 橙色流水
            light_type   = LIGHT_BLINK;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 140;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        default:
            ret = SYS_ERROR;
            SENSOR_NOTE("unkown LIGHT_TYPE: 0x%08x", light_cmd);
            break;
    }
    if(SYS_OK != ret)
    {
        return ret;
    }

    // Frame_length = 10 Bytes
    off          = 0;
    buf[off]     = FRAME_HEADER1;
    buf[off + 1] = FRAME_HEADER2;
    buf[off + 2] = Frame_ID;
    buf[off + 3] = Frame_VERSION;
    buf[off + 4] = Type_Order;
    buf[off + 5] = Hex_Light;
    off += 6;
    len_off = off;
    off += 2; // skip size
    SYS_L_2_B_16(buf, off, Test_crc); // crc
    off += 2;

    UINT16 temp_off = 0;
    UINT16 data_len = 0;
    temp_off        = off;
    buf[off]        = light_id;   // id
    buf[off + 1]    = light_type; // SENSOR_LIGHT_E
    off += 2;
    SYS_L_2_B_16(buf, off, light_freq); // freq
    off += 2;
    buf[off]     = light_r;      // R
    buf[off + 1] = light_g;      // G
    buf[off + 2] = light_b;      // B
    buf[off + 3] = light_bright; // Brightness
    off += 4;
    SYS_L_2_B_32(buf, off, light_state); // status_id
    off += 4;
    data_len = off - temp_off;
    SYS_L_2_B_16(buf, len_off, data_len); // data_len
    ret = off;
    return ret;
}

INT32 sensor_flashlight_data_pack(const SENSOR_CMD_T *cmd, UINT8 *buf, INT32 size)
{
    INT32 ret           = SYS_OK;
    INT32 off           = 0;
    INT32 len_off       = 0;
    UINT32 light_bright = 0;

    off += 4;
    light_bright = SYS_B_2_L_32(cmd->buf, off);
    off += 4;
    SENSOR_NOTE("light_bright: %d", light_bright);

    // Frame_length = 10 Bytes
    off          = 0;
    buf[off]     = FRAME_HEADER1;
    buf[off + 1] = FRAME_HEADER2;
    buf[off + 2] = Frame_ID;
    buf[off + 3] = Frame_VERSION;
    buf[off + 4] = Type_Order;
    buf[off + 5] = Hex_Duoji;
    off += 6;
    len_off = off;
    off += 2; // skip size
    SYS_L_2_B_16(buf, off, Test_crc); // crc
    off += 2;

    UINT16 temp_off = 0;
    UINT16 data_len = 0;
    temp_off        = off;
    SYS_L_2_B_32(buf, off, light_bright); // Brightness
    off += 4;
    data_len = off - temp_off;
    SYS_L_2_B_16(buf, len_off, data_len); // data_len
    ret = off;
    return ret;
}

INT32 sensor_cmd_handle(SENSOR_INFO_T *info, const SENSOR_CMD_T *cmd)
{
    INT32 ret      = SYS_OK;
    INT32 data_len = 0;
    UINT8 buf[256] = {0};
    switch(cmd->head.cmd)
    {
        case DEEP_CMD_LIGHT:
            SENSOR_NOTE("DEEP_CMD_LIGHT: 0x%08x", cmd->head.cmd);
            ret = sensor_light_data_pack(cmd, buf, sizeof(buf));
            break;
        case DEEP_CMD_FLASHLIGHT:
            SENSOR_NOTE("DEEP_CMD_FLASHLIGHT: 0x%08x", cmd->head.cmd);
            ret = sensor_flashlight_data_pack(cmd, buf, sizeof(buf));
            break;
        default:
            SENSOR_ERROR("unkown cmd: 0x%08x", cmd->head.cmd);
            ret = SYS_ERROR;
            break;
    }
    if(ret > 0)
    {
        data_len = ret;
        sensor_hex_print(buf, data_len);
        ret = write(info->extra_fd, buf, data_len);
        if(ret != data_len)
        {
            SENSOR_ERROR("write() failed, error: %s, [%d != %d]", strerror(errno), ret, data_len);
            ret = SYS_ERROR;
        }
        else
        {
            ret = SYS_OK;
        }
    }
    return ret;
}

VOID sensor_light_callback(INT32 fd, VOID *arg)
{
    SYS_PTR_CHECK_VOID(arg, SENSOR_ERROR);
    INT32 len        = 0;
    UINT8 buf[1024] = {0};
    INT32 off        = 0;
    SENSOR_CMD_T sensor_cmd;
    struct sockaddr_in client_addr;
    socklen_t client_addr_len = 0;
    SENSOR_INFO_T *info = NULL;

    info            = (SENSOR_INFO_T *)arg;
    client_addr_len = sizeof(struct sockaddr_in);
    memset(buf, 0, sizeof(buf));
    memset(&client_addr, 0, sizeof(client_addr));
    len = recvfrom(fd, buf, sizeof(buf), 0, (struct sockaddr *)&client_addr, &client_addr_len);
    if(len < sizeof(pk_t))
    {
        SENSOR_ERROR("recv failed, len: %d\n", len);
        return;
    }
    memset(&sensor_cmd, 0, sizeof(SENSOR_CMD_T));
    sensor_cmd.head.len = SYS_B_2_L_32(buf, off);
    off += 4;
    sensor_cmd.head.cmd = SYS_B_2_L_32(buf, off);
    off += 4;
    sensor_cmd.head.stat = SYS_B_2_L_32(buf, off);
    off += 4;
    if(sensor_cmd.head.len < sizeof(pk_t))
    {
        SENSOR_ERROR("data error, len: %d\n", sensor_cmd.head.len);
        return;
    }
    sensor_cmd.buf = buf + off;
    sensor_cmd.len = len - off;
    if(SYS_OK != sensor_cmd_handle(info, &sensor_cmd))
    {
        SENSOR_ERROR("handle failed, cmd: 0x%0x\n", sensor_cmd.head.cmd);
    }
}
