// udp - light
// tty - touch
// tty - uwb

#include "sys_common.h"
#include "sys_utils.h"
#include "sensor_hub.h"
#include "sensor_ligth.h"
#include "sensor_touch.h"

#define SENSOR_ULOOP_MAGIC (0xAACC)

typedef VOID (*sensor_uloop_recv_callback)(INT32 fd, VOID *arg);
VOID sensor_recv_callback(INT32 fd, VOID *arg);

typedef struct _sensor_uloop_fd_t_
{
    SYS_ULOOP_FD_T sock;
    UINT32 magic;
    INT32 is_add;
    SENSOR_INFO_T info;
    sensor_uloop_recv_callback recv_cb;
}SENSOR_ULOOP_FD_T;

typedef struct _sensor_extra_info_t_
{
    INT32 fd;
}SENSOR_EXTRA_INFO_T;

typedef struct _sensor_ctrl_t_
{
    INT32 num;
    INT32 size;
    SENSOR_ULOOP_FD_T *info;
    SENSOR_EXTRA_INFO_T extra;
}SENSOR_CTRL_T;

static SENSOR_CTRL_T g_sensor_ctrl = {
    .num = 0,
    .size = 0,
    .info = NULL
};

static VOID sensor_print_usage(VOID)
{
    SENSOR_NOTE("------ RX test -------");
    SENSOR_NOTE("-t 0 - off; 1 - on");
}

static INT32 sensor_parse_options(INT32 argc, INT8 *argv[])
{
    INT32 opt = 0;

    while(-1 != (opt = getopt(argc, argv, "?hlt:")))
    {
        switch(opt)
        {
            case 'h':
            case '?':
            default:
                sensor_print_usage();
                return -1;
        }
    }
    return 0;
}

static VOID sensor_signal_fatal_handler(INT32 signum)
{
    SENSOR_FATAL(">>>>>> Fatal signal %d caught <<<<<<\n", signum);
    exit(signum);
}

static VOID sensor_signal_ignore_handler(INT32 signum)
{
    SENSOR_ERROR(">>>>>> Ignore signal %d caught <<<<<<\n", signum);
}

VOID sensor_signal_init(VOID)
{
	struct sigaction sig_stop_action;
    struct sigaction sig_no_reaction;

    /* Signal handlers */
    sig_stop_action.sa_handler = sensor_signal_fatal_handler;
    sigemptyset(&sig_stop_action.sa_mask);
    sig_stop_action.sa_flags = 0;

    sigaction(SIGTERM, &sig_stop_action, NULL);
    sigaction(SIGINT, &sig_stop_action, NULL);
    sigaction(SIGSEGV, &sig_stop_action, NULL);
    sigaction(SIGBUS, &sig_stop_action, NULL);
    sigaction(SIGKILL, &sig_stop_action, NULL);
    sigaction(SIGFPE, &sig_stop_action, NULL);
    sigaction(SIGILL, &sig_stop_action, NULL);
    sigaction(SIGQUIT, &sig_stop_action, NULL);
    sigaction(SIGHUP, &sig_stop_action, NULL);
    sigaction(SIGPIPE, &sig_stop_action, NULL);

    sig_no_reaction.sa_handler = sensor_signal_ignore_handler;
    sigemptyset(&sig_no_reaction.sa_mask);
    sig_no_reaction.sa_flags = 0;
    sigaction(SIGALRM, &sig_no_reaction, NULL);
    sigaction(SIGUSR1, &sig_no_reaction, NULL);
    sigaction(SIGUSR2, &sig_no_reaction, NULL);
}

static void sensor_light_uloop_callback(struct uloop_fd *u_fd, unsigned int events)
{
    SYS_PTR_CHECK_VOID(u_fd, SENSOR_ERROR);
    SENSOR_ULOOP_FD_T *uloop = (SENSOR_ULOOP_FD_T *)u_fd;
    SYS_VAL_NE_CHECK_VOID(uloop->magic, SENSOR_ULOOP_MAGIC, SENSOR_ERROR);
    SYS_PTR_CHECK_VOID(uloop->recv_cb, SENSOR_ERROR);
    uloop->recv_cb(uloop->sock.fd, (VOID *)&uloop->info);
}

static void sensor_touch_uloop_callback(struct uloop_fd *u_fd, unsigned int events)
{
    SYS_PTR_CHECK_VOID(u_fd, SENSOR_ERROR);
    SENSOR_ULOOP_FD_T *uloop = (SENSOR_ULOOP_FD_T *)u_fd;
    SYS_VAL_NE_CHECK_VOID(uloop->magic, SENSOR_ULOOP_MAGIC, SENSOR_ERROR);
    SYS_PTR_CHECK_VOID(uloop->recv_cb, SENSOR_ERROR);
    uloop->recv_cb(uloop->sock.fd, (VOID *)&uloop->info);
}

#define SENSOR_LIGHT_UDP_PORT (12345)
#define SENSOR_LIGHT_SERIAL   ("/dev/ttyS3")
#define SENSOR_GPIO_LIGHT_ID  ("40")
#define SENSOR_GPIO_TOUCH1_ID ("112")
#define SENSOR_GPIO_TOUCH2_ID ("113")

#define SENSOR_GPIO_ID_ROOT(id, buf, size) \
    snprintf(buf, size, "/sys/class/gpio/gpio%s", id)

#define SENSOR_GPIO_ID_DIRECTION(id, buf, size) \
    snprintf(buf, size, "/sys/class/gpio/gpio%s/direction", id)

#define SENSOR_GPIO_ID_EDGE(id, buf, size) \
    snprintf(buf, size, "/sys/class/gpio/gpio%s/edge", id)

#define SENSOR_GPIO_ID_VALUE(id, buf, size) \
    snprintf(buf, size, "/sys/class/gpio/gpio%s/value", id)

static INT32 sensor_file_write_only(const INT8 *filename, const INT8 *data)
{
    FILE *fp = NULL;
    fp = fopen(filename, "w");
    if(NULL == fp)
    {
        SENSOR_ERROR("fopen() export failed\n");
        return SYS_ERROR;
    }
    fprintf(fp, "%s", data);
    fclose(fp);
    return SYS_OK;
}

static INT32 sensor_gpio_check(const INT8 *gpio_id)
{
    INT8 buf[256] = {0};
    memset(buf, 0, sizeof(buf));
    SENSOR_GPIO_ID_ROOT(gpio_id, buf, sizeof(buf));
    if(access(buf, F_OK) == 0)
    {
        return SYS_OK;
    }
    return sensor_file_write_only("/sys/class/gpio/export", gpio_id);
}

static INT32 sensor_light_init(const INT8 *gpio_id)
{
    INT32 ret = SYS_ERROR;
    INT8 buf[256] = {0};
    while(1)
    {
        ret = SYS_ERROR;
        do
        {
            // gpio
            if(SYS_OK != sensor_gpio_check(gpio_id))
            {
                SENSOR_ERROR("check() gpio %d failed\n", gpio_id);
                break;
            }
            // direction
            memset(buf, 0, sizeof(buf));
            SENSOR_GPIO_ID_DIRECTION(gpio_id, buf, sizeof(buf));
            if(SYS_OK != sensor_file_write_only(buf, "out"))
            {
                SENSOR_ERROR("write() %s failed\n", buf);
                break;
            }
            // value
            memset(buf, 0, sizeof(buf));
            SENSOR_GPIO_ID_VALUE(gpio_id, buf, sizeof(buf));
            if(SYS_OK != sensor_file_write_only(buf, "0"))
            {
                SENSOR_ERROR("write() %s failed\n", buf);
                break;
            }
            ret = SYS_OK;
        }while(0);
        if(SYS_OK == ret)
        {
            break;
        }
        sys_time_wait(1000);
    }
    return ret;
}

static INT32 sensor_touch_init(const INT8 *gpio_id)
{
    INT32 ret = SYS_ERROR;
    INT8 buf[256] = {0};
    while(1)
    {
        ret = SYS_ERROR;
        do
        {
            // gpio
            if(SYS_OK != sensor_gpio_check(gpio_id))
            {
                SENSOR_ERROR("check() gpio %s failed\n", gpio_id);
                break;
            }
            // direction
            memset(buf, 0, sizeof(buf));
            SENSOR_GPIO_ID_DIRECTION(gpio_id, buf, sizeof(buf));
            if(SYS_OK != sensor_file_write_only(buf, "in"))
            {
                SENSOR_ERROR("write() %s failed\n", buf);
                break;
            }
            // edge
            memset(buf, 0, sizeof(buf));
            SENSOR_GPIO_ID_EDGE(gpio_id, buf, sizeof(buf));
            if(SYS_OK != sensor_file_write_only(buf, "both"))
            {
                SENSOR_ERROR("write() %s failed\n", buf);
                break;
            }
            // value
            ret = SYS_OK;
        }while(0);
        if(SYS_OK == ret)
        {
            break;
        }
        sys_time_wait(1000);
    }
    return ret;
}

static INT32 sensor_light_uloop(const INT8 *gpio_id, SENSOR_CTRL_T *ctrl)
{
    INT32 fd = 0;
    fd = sys_socket_udp_server_create(SENSOR_LIGHT_UDP_PORT);
    if(-1 == fd)
    {
        SENSOR_ERROR("create udp server failed\n");
        return SYS_ERROR;
    }
    sys_socket_noblock_set(fd);
    INT32 serial_fd = 0;
    serial_fd = sys_serial_init(SENSOR_LIGHT_SERIAL, SYS_FILE_RDWR);
    if(-1 == serial_fd)
    {
        SENSOR_ERROR("create serial %s failed\n", SENSOR_LIGHT_SERIAL);
        SYS_SAFE_CLOSE(fd);
        return SYS_ERROR;
    }
    SENSOR_ULOOP_FD_T *uloop_fd = NULL;
    uloop_fd                    = &g_sensor_ctrl.info[g_sensor_ctrl.num];
    uloop_fd->sock.cb           = sensor_light_uloop_callback;
    uloop_fd->sock.fd           = fd;
    uloop_fd->magic             = SENSOR_ULOOP_MAGIC;
    uloop_fd->recv_cb           = sensor_light_callback;
    uloop_fd->info.fd           = uloop_fd->sock.fd;
    uloop_fd->info.extra_fd     = serial_fd;
    snprintf(uloop_fd->info.desc, sizeof(uloop_fd->info.desc), "light_%s", gpio_id);
    if(sys_uloop_add_read((SYS_ULOOP_FD_T *)uloop_fd) < 0)
    {
        SENSOR_ERROR("add %s failed", uloop_fd->info.desc);
    }
    else
    {
        SENSOR_NOTE("add %s ok", uloop_fd->info.desc);
        uloop_fd->is_add = 1;
    }
    g_sensor_ctrl.num++;
    return SYS_OK;
}

static INT32 sensor_touch_uloop(const INT8 *gpio_id, SENSOR_CTRL_T *ctrl)
{
    INT32 fd      = 0;
    INT8 buf[256] = {0};
    memset(buf, 0, sizeof(buf));
    SENSOR_GPIO_ID_VALUE(gpio_id, buf, sizeof(buf));
    fd = sys_file_open(buf, SYS_FILE_RDONLY);
    if(-1 == fd)
    {
        SENSOR_ERROR("open %s failed", buf);
        return SYS_ERROR;
    }
    sys_socket_noblock_set(fd);
    SENSOR_ULOOP_FD_T *uloop_fd = NULL;
    uloop_fd                    = &g_sensor_ctrl.info[g_sensor_ctrl.num];
    uloop_fd->sock.cb           = sensor_touch_uloop_callback;
    uloop_fd->sock.fd           = fd;
    uloop_fd->magic             = SENSOR_ULOOP_MAGIC;
    uloop_fd->recv_cb           = sensor_touch_callback;
    uloop_fd->info.fd           = uloop_fd->sock.fd;
    uloop_fd->info.extra_fd     = g_sensor_ctrl.extra.fd;
    snprintf(uloop_fd->info.desc, sizeof(uloop_fd->info.desc), "touch_%s", gpio_id);
    if(sys_uloop_add_read((SYS_ULOOP_FD_T *)uloop_fd) < 0)
    {
        SENSOR_ERROR("add %s failed", uloop_fd->info.desc);
    }
    else
    {
        SENSOR_NOTE("add %s ok", uloop_fd->info.desc);
        uloop_fd->is_add = 1;
    }
    g_sensor_ctrl.num++;
    return SYS_OK;
}

static INT32 sensor_touch_common_init(SENSOR_CTRL_T *ctrl)
{
    INT32 fd = 0;
    fd = sys_socket_create(SYS_SOCKET_UDP);
    if(-1 == fd)
    {
        SENSOR_ERROR("create udp server failed\n");
        return SYS_ERROR;
    }
    sys_socket_noblock_set(fd);
    ctrl->extra.fd = fd;
    return SYS_OK;
}

static INT32 sensor_ctrl_init(VOID)
{
    memset(&g_sensor_ctrl, 0, sizeof(SENSOR_CTRL_T));
    g_sensor_ctrl.size = 3;
    g_sensor_ctrl.info = (SENSOR_ULOOP_FD_T *)calloc(g_sensor_ctrl.size, sizeof(SENSOR_ULOOP_FD_T));
    SYS_PTR_CHECK_RET(g_sensor_ctrl.info, SENSOR_ERROR, SYS_ERROR);
    g_sensor_ctrl.num = 0;
    return SYS_OK;
}

static INT32 sensor_ctrl_deinit(VOID)
{
    SYS_SAFE_FREE(g_sensor_ctrl.info);
    g_sensor_ctrl.size = 0;
    g_sensor_ctrl.num  = 0;
    return SYS_OK;
}

INT32 main(INT32 argc, INT8 *argv[])
{
    SENSOR_NOTE("------> start sensor <------");
    if(-1 == sensor_parse_options(argc, argv))
    {
        SENSOR_NOTE("------> exit sensor <------");
        return 0;
    }
    SYS_LOG_CAPA_T capa;
    memset(&capa, 0, sizeof(SYS_LOG_CAPA_T));
    capa.level = SYS_LOG_LEVEL_NOTE;
    sys_log_init(&capa);
    sensor_signal_init();
    INT32 ret = SYS_ERROR;
    do
    {
        if(SYS_OK != sys_uloop_init())
        {
            SENSOR_ERROR("init uloop failed");
            break;
        }
        if(SYS_OK != sensor_ctrl_init())
        {
            SENSOR_ERROR("init ctrl failed");
            break;
        }
        if(SYS_OK != sensor_light_init(SENSOR_GPIO_LIGHT_ID))
        {
            SENSOR_ERROR("init light %s failed", SENSOR_GPIO_LIGHT_ID);
            break;
        }
        if(SYS_OK != sensor_light_uloop(SENSOR_GPIO_LIGHT_ID, &g_sensor_ctrl))
        {
            SENSOR_ERROR("add light %s uloop failed", SENSOR_GPIO_LIGHT_ID);
            break;
        }
        if(SYS_OK != sensor_touch_common_init(&g_sensor_ctrl))
        {
            SENSOR_ERROR("touch common init failed");
            break;
        }
        if(SYS_OK != sensor_touch_init(SENSOR_GPIO_TOUCH2_ID))
        {
            SENSOR_ERROR("init touch %s failed", SENSOR_GPIO_TOUCH2_ID);
            break;
        }
        if(SYS_OK != sensor_touch_uloop(SENSOR_GPIO_TOUCH2_ID, &g_sensor_ctrl))
        {
            SENSOR_ERROR("add touch %s uloop failed", SENSOR_GPIO_TOUCH2_ID);
            break;
        }
        ret = SYS_OK;
    }while(0);
    if(SYS_OK == ret)
    {
        sys_uloop_run();
        sys_uloop_done();
    }
    SENSOR_NOTE("------> exit sensor <------");
    (VOID)sensor_ctrl_deinit();
    return 0;
}
