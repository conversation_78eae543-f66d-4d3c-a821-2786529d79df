#ifndef _SYS_COMMON_H_
#define _SYS_COMMON_H_

#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>

// 数据类型
typedef char INT8;
typedef unsigned char UINT8;
typedef short INT16;
typedef unsigned short UINT16;
typedef int INT32;
typedef unsigned int UINT32;
typedef long long INT64;
typedef unsigned long long UINT64;
typedef void VOID;

#define SYS_OK (0)     // success
#define SYS_ERROR (-1) // fail

#ifndef SYS_VAR_STR
#define SYS_VAR_STR(x) (#x)
#endif

#ifndef SYS_MAX
#define SYS_MAX(a, b) ((a) > (b) ? (a) : (b))
#endif

#ifndef SYS_MIN
#define SYS_MIN(a, b) ((a) > (b) ? (b) : (a))
#endif

#ifndef SYS_ALIGN_UP
#define SYS_ALIGN_UP(x, align) (((x) + ((align) - 1)) & (~((align) - 1)))
#endif

#ifndef SYS_ALIGN_DOWN
#define SYS_ALIGN_DOWN(x, align) ((x) & (~((align) - 1)))
#endif

#ifndef SYS_ARRAY_SIZE
#define SYS_ARRAY_SIZE(x) ((sizeof(x)) / (sizeof(x[0])))
#endif

#ifndef SYS_IS_BIT_SET
#define SYS_IS_BIT_SET(state, bit) ((state) & (1 << bit))
#endif

#ifndef SYS_BIT_SET
#define SYS_BIT_SET(state, bit) (state = ((state) | (1 << bit)))
#endif

#ifndef SYS_BIT_RESET
#define SYS_BIT_RESET(state, bit) (state = ((state) & (~(1 << bit))))
#endif

// big-endian -> little-endian
#ifndef SYS_B_2_L_16
#define SYS_B_2_L_16(data, offset) (((UINT16)data[offset] << 8) & (0xFF00)) | \
                                    (((UINT16)data[offset + 1]) & (0x00FF))
#endif

#ifndef SYS_B_2_L_24
#define SYS_B_2_L_24(data, offset) (((UINT32)data[offset] << 16) & (0xFF0000)) | \
                                    (((UINT32)data[offset + 1] << 8) & (0x00FF00)) | \
                                    (((UINT32)data[offset + 2]) & (0x0000FF))
#endif

#ifndef SYS_B_2_L_32
#define SYS_B_2_L_32(data, offset) (((UINT32)data[offset] << 24) & (0xFF000000)) | \
                                    (((UINT32)data[offset + 1] << 16) & (0x00FF0000)) | \
                                    (((UINT32)data[offset + 2] << 8) & (0x0000FF00)) | \
                                    (((UINT32)data[offset + 3]) & (0x000000FF))
#endif

// little-endian -> big-endian
#ifndef SYS_L_2_B_16
#define SYS_L_2_B_16(data, offset, num) data[offset] = (((num) & (0xFF00)) >> 8) & 0xFF; \
    data[offset + 1] = ((num) & (0x00FF))
#endif

#ifndef SYS_L_2_B_24
#define SYS_L_2_B_24(data, offset, num) data[offset] = (((num) & (0xFF0000)) >> 16) & (0xFF); \
    data[offset + 1] = (((num) & (0x00FF00)) >> 8) & (0xFF); \
    data[offset + 2] = (num) & (0x0000FF)
#endif

#ifndef SYS_L_2_B_32
#define SYS_L_2_B_32(data, offset, num) data[offset] = (((num) & (0xFF000000)) >> 24) & (0xFF); \
    data[offset + 1] = (((num) & (0x00FF0000)) >> 16) & (0xFF); \
    data[offset + 2] = (((num) & (0x0000FF00)) >> 8) & (0xFF); \
    data[offset + 3] = (num) & (0x000000FF)
#endif

#if __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
    #ifndef SYS_E_2B
    #define SYS_E_2B(data, offset) (((UINT16)data[offset] << 8) & (0xFF00)) | \
                                        (((UINT16)data[offset + 1]) & (0x00FF))
    #endif

    #ifndef SYS_E_3B
    #define SYS_E_3B(data, offset) (((UINT32)data[offset] << 16) & (0xFF0000)) | \
                                        (((UINT32)data[offset + 1] << 8) & (0x00FF00)) | \
                                        (((UINT32)data[offset + 2]) & (0x0000FF))
    #endif

    #ifndef SYS_E_4B
    #define SYS_E_4B(data, offset) (((UINT32)data[offset] << 24) & (0xFF000000)) | \
                                        (((UINT32)data[offset + 1] << 16) & (0x00FF0000)) | \
                                        (((UINT32)data[offset + 2] << 8) & (0x0000FF00)) | \
                                        (((UINT32)data[offset + 3]) & (0x000000FF))
    #endif
#elif __BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__
    #ifndef SYS_E_2B
    #define SYS_E_2B(data, offset) (((UINT16)data[offset + 1] << 8) & (0xFF00)) | \
                                        (((UINT16)data[offset]) & (0x00FF))
    #endif

    #ifndef SYS_E_3B
    #define SYS_E_3B(data, offset) (((UINT32)data[offset + 2] << 16) & (0xFF0000)) | \
                                        (((UINT32)data[offset + 1] << 8) & (0x00FF00)) | \
                                        (((UINT32)data[offset]) & (0x0000FF))
    #endif

    #ifndef SYS_E_4B
    #define SYS_E_4B(data, offset) (((UINT32)data[offset + 3] << 24) & (0xFF000000)) | \
                                        (((UINT32)data[offset + 2] << 16) & (0x00FF0000)) | \
                                        (((UINT32)data[offset + 1] << 8) & (0x0000FF00)) | \
                                        (((UINT32)data[offset]) & (0x000000FF))
    #endif
#else
#error You must specify your architecture endianess
#endif

/* Extract/insert 1 byte */
static inline VOID _E1B(UINT8 **packet_ppointer, UINT8 *memory_pointer)
{
    *memory_pointer     = **packet_ppointer;
    (*packet_ppointer) += 1;
}

static inline VOID _I1B(UINT8 *memory_pointer, UINT8 **packet_ppointer)
{
    **packet_ppointer   = *memory_pointer;
    (*packet_ppointer) += 1;
}

/* Extract/insert 2 bytes */
static inline VOID _E2B(UINT8 **packet_ppointer, UINT16 *memory_pointer)
{
#if __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
    *(((UINT8 *)memory_pointer)+0)  = **packet_ppointer; (*packet_ppointer)++;
    *(((UINT8 *)memory_pointer)+1)  = **packet_ppointer; (*packet_ppointer)++;
#elif __BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__
    *(((UINT8 *)memory_pointer)+1)  = **packet_ppointer; (*packet_ppointer)++;
    *(((UINT8 *)memory_pointer)+0)  = **packet_ppointer; (*packet_ppointer)++;
#else
#error You must specify your architecture endianess
#endif
}

static inline VOID _I2B(UINT16 *memory_pointer, UINT8 **packet_ppointer)
{
#if __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
    **packet_ppointer = *(((UINT8 *)memory_pointer)+0); (*packet_ppointer)++;
    **packet_ppointer = *(((UINT8 *)memory_pointer)+1); (*packet_ppointer)++;
#elif __BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__
    **packet_ppointer = *(((UINT8 *)memory_pointer)+1); (*packet_ppointer)++;
    **packet_ppointer = *(((UINT8 *)memory_pointer)+0); (*packet_ppointer)++;
#else
#error You must specify your architecture endianess
#endif
}

/* Extract/insert 3 bytes */
static inline VOID _E3B(UINT8 **packet_ppointer, UINT32 *memory_pointer)
{
#if __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
    *(((UINT8 *)memory_pointer)+0)  = **packet_ppointer; (*packet_ppointer)++;
    *(((UINT8 *)memory_pointer)+1)  = **packet_ppointer; (*packet_ppointer)++;
    *(((UINT8 *)memory_pointer)+2)  = **packet_ppointer; (*packet_ppointer)++;
#elif __BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__
    *(((UINT8 *)memory_pointer)+2)  = **packet_ppointer; (*packet_ppointer)++;
    *(((UINT8 *)memory_pointer)+1)  = **packet_ppointer; (*packet_ppointer)++;
    *(((UINT8 *)memory_pointer)+0)  = **packet_ppointer; (*packet_ppointer)++;
#else
#error You must specify your architecture endianess
#endif
}

static inline VOID _I3B(UINT32 *memory_pointer, UINT8 **packet_ppointer)
{
#if __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
    **packet_ppointer = *(((UINT8 *)memory_pointer)+0); (*packet_ppointer)++;
    **packet_ppointer = *(((UINT8 *)memory_pointer)+1); (*packet_ppointer)++;
    **packet_ppointer = *(((UINT8 *)memory_pointer)+2); (*packet_ppointer)++;
#elif __BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__
    **packet_ppointer = *(((UINT8 *)memory_pointer)+2); (*packet_ppointer)++;
    **packet_ppointer = *(((UINT8 *)memory_pointer)+1); (*packet_ppointer)++;
    **packet_ppointer = *(((UINT8 *)memory_pointer)+0); (*packet_ppointer)++;
#else
#error You must specify your architecture endianess
#endif
}

/* Extract/insert 4 bytes */
static inline VOID _E4B(UINT8 **packet_ppointer, UINT32 *memory_pointer)
{
#if __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
    *(((UINT8 *)memory_pointer)+0)  = **packet_ppointer; (*packet_ppointer)++;
    *(((UINT8 *)memory_pointer)+1)  = **packet_ppointer; (*packet_ppointer)++;
    *(((UINT8 *)memory_pointer)+2)  = **packet_ppointer; (*packet_ppointer)++;
    *(((UINT8 *)memory_pointer)+3)  = **packet_ppointer; (*packet_ppointer)++;
#elif __BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__
    *(((UINT8 *)memory_pointer)+3)  = **packet_ppointer; (*packet_ppointer)++;
    *(((UINT8 *)memory_pointer)+2)  = **packet_ppointer; (*packet_ppointer)++;
    *(((UINT8 *)memory_pointer)+1)  = **packet_ppointer; (*packet_ppointer)++;
    *(((UINT8 *)memory_pointer)+0)  = **packet_ppointer; (*packet_ppointer)++;
#else
#error You must specify your architecture endianess
#endif
}

static inline VOID _I4B(UINT32 *memory_pointer, UINT8 **packet_ppointer)
{
#if __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
    **packet_ppointer = *(((UINT8 *)memory_pointer)+0); (*packet_ppointer)++;
    **packet_ppointer = *(((UINT8 *)memory_pointer)+1); (*packet_ppointer)++;
    **packet_ppointer = *(((UINT8 *)memory_pointer)+2); (*packet_ppointer)++;
    **packet_ppointer = *(((UINT8 *)memory_pointer)+3); (*packet_ppointer)++;
#elif __BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__
    **packet_ppointer = *(((UINT8 *)memory_pointer)+3); (*packet_ppointer)++;
    **packet_ppointer = *(((UINT8 *)memory_pointer)+2); (*packet_ppointer)++;
    **packet_ppointer = *(((UINT8 *)memory_pointer)+1); (*packet_ppointer)++;
    **packet_ppointer = *(((UINT8 *)memory_pointer)+0); (*packet_ppointer)++;
#else
#error You must specify your architecture endianess
#endif
}

/* Extract/insert N bytes (ignore endianess) */
static inline VOID _EnB(UINT8 **packet_ppointer, void *memory_pointer, UINT32 n)
{
    memcpy(memory_pointer, *packet_ppointer, n);
    (*packet_ppointer) += n;
}
static inline VOID _InB(void *memory_pointer, UINT8 **packet_ppointer, UINT32 n)
{
    memcpy(*packet_ppointer, memory_pointer, n);
    (*packet_ppointer) += n;
}

// free memory safely
#ifndef SYS_SAFE_FREE
#define SYS_SAFE_FREE(p) if((NULL) != (p)) \
    { \
        free(p); \
        p = NULL; \
    }
#endif

#ifndef SYS_SAFE_CLOSE
#define SYS_SAFE_CLOSE(p) if((-1) != (p)) \
    { \
        close(p); \
        p = -1; \
    }
#endif

// print, when val1 == val2
#ifndef SYS_VAL_EQ_CHECK_PRINT
#define SYS_VAL_EQ_CHECK_PRINT(val1, val2, PRINT) if((val1) == (val2)) \
{ \
    PRINT("%d == %d\n", (val1), (val2)); \
}
#endif

// return void, when val1 == val2
#ifndef SYS_VAL_EQ_CHECK_VOID
#define SYS_VAL_EQ_CHECK_VOID(val1, val2, PRINT) if((val1) == (val2)) \
{ \
    PRINT("%d == %d\n", (val1), (val2)); \
    return; \
}
#endif

// return ret, when val1 == val2
#ifndef SYS_VAL_EQ_CHECK_RET
#define SYS_VAL_EQ_CHECK_RET(val1, val2, PRINT, RET) if((val1) == (val2)) \
{ \
    PRINT("%d == %d\n", (val1), (val2)); \
    return (RET); \
}
#endif

// return ret without print, when val1 == val2
#ifndef SYS_VAL_EQ_CHECK_RET_WITHOUT_PRINT
#define SYS_VAL_EQ_CHECK_RET_WITHOUT_PRINT(val1, val2, RET) if((val1) == (val2)) \
{ \
    return (RET); \
}
#endif

// break, when val1 == val2
#ifndef SYS_VAL_EQ_CHECK_BREAK
#define SYS_VAL_EQ_CHECK_BREAK(val1, val2, PRINT) if((val1) == (val2)) \
{ \
    PRINT("%d == %d\n", (val1), (val2)); \
    break; \
}
#endif

// print, when val1 != val2
#ifndef SYS_VAL_NE_CHECK_PRINT
#define SYS_VAL_NE_CHECK_PRINT(val1, val2, PRINT) if((val1) != (val2)) \
{ \
    PRINT("%d != %d\n", (val1), (val2)); \
}
#endif

// return void, when val1 != val2
#ifndef SYS_VAL_NE_CHECK_VOID
#define SYS_VAL_NE_CHECK_VOID(val1, val2, PRINT) if((val1) != (val2)) \
{ \
    PRINT("%d != %d\n", (val1), (val2)); \
    return; \
}
#endif

// return ret, when val1 != val2
#ifndef SYS_VAL_NE_CHECK_RET
#define SYS_VAL_NE_CHECK_RET(val1, val2, PRINT, RET) if((val1) != (val2)) \
{ \
    PRINT("%d != %d\n", (val1), (val2)); \
    return (RET); \
}
#endif

// break, when val1 != val2
#ifndef SYS_VAL_NE_CHECK_BREAK
#define SYS_VAL_NE_CHECK_BREAK(val1, val2, PRINT) if((val1) != (val2)) \
{ \
    PRINT("%d != %d\n", (val1), (val2)); \
    break; \
}
#endif

// print, when val1 < val2
#ifndef SYS_VAL_LT_CHECK_PRINT
#define SYS_VAL_LT_CHECK_PRINT(val1, val2, PRINT) if((val1) < (val2)) \
{ \
    PRINT("%d < %d\n", (val1), (val2)); \
}
#endif

// return void, when val1 < val2
#ifndef SYS_VAL_LT_CHECK_VOID
#define SYS_VAL_LT_CHECK_VOID(val1, val2, PRINT) if((val1) < (val2)) \
{ \
    PRINT("%d < %d\n", (val1), (val2)); \
    return; \
}
#endif

// return ret, when val1 < val2
#ifndef SYS_VAL_LT_CHECK_RET
#define SYS_VAL_LT_CHECK_RET(val1, val2, PRINT, RET) if((val1) < (val2)) \
{ \
    PRINT("%d < %d\n", (val1), (val2)); \
    return (RET); \
}
#endif

// break, when val1 < val2
#ifndef SYS_VAL_LT_CHECK_BREAK
#define SYS_VAL_LT_CHECK_BREAK(val1, val2, PRINT) if((val1) < (val2)) \
{ \
    PRINT("%d < %d\n", (val1), (val2)); \
    break; \
}
#endif

// print, when val1 <= val2
#ifndef SYS_VAL_LE_CHECK_PRINT
#define SYS_VAL_LE_CHECK_PRINT(val1, val2, PRINT) if((val1) <= (val2)) \
{ \
    PRINT("%d <= %d\n", (val1), (val2)); \
}
#endif

// return void, when val1 <= val2
#ifndef SYS_VAL_LE_CHECK_VOID
#define SYS_VAL_LE_CHECK_VOID(val1, val2, PRINT) if((val1) <= (val2)) \
{ \
    PRINT("%d <= %d\n", (val1), (val2)); \
    return; \
}
#endif

// return ret, when val1 <= val2
#ifndef SYS_VAL_LE_CHECK_RET
#define SYS_VAL_LE_CHECK_RET(val1, val2, PRINT, RET) if((val1) <= (val2)) \
{ \
    PRINT("%d <= %d\n", (val1), (val2)); \
    return (RET); \
}
#endif

// break, when val1 <= val2
#ifndef SYS_VAL_LE_CHECK_BREAK
#define SYS_VAL_LE_CHECK_BREAK(val1, val2, PRINT) if((val1) <= (val2)) \
{ \
    PRINT("%d <= %d\n", (val1), (val2)); \
    break; \
}
#endif

// return void, when ptr is null
#ifndef SYS_PTR_CHECK_VOID
#define SYS_PTR_CHECK_VOID(p, PRINT) if((NULL) == (p)) \
    { \
        PRINT("ptr is null\n"); \
        return; \
    }
#endif

// return ret, when ptr is null
#ifndef SYS_PTR_CHECK_RET
#define SYS_PTR_CHECK_RET(p, PRINT, RET) if((NULL) == (p)) \
    { \
        PRINT("ptr is null\n"); \
        return (RET); \
    }
#endif

// break, when ptr is null
#ifndef SYS_PTR_CHECK_BREAK
#define SYS_PTR_CHECK_BREAK(p, PRINT) if((NULL) == (p)) \
    { \
        PRINT("ptr is null\n"); \
        break; \
    }
#endif

// print, when ptr is null
#ifndef SYS_PTR_CHECK_PRINT
#define SYS_PTR_CHECK_PRINT(p, PRINT) if((NULL) == (p)) \
    { \
        PRINT("ptr is null\n"); \
    }
#endif

#endif // _SYS_COMMON_H_
