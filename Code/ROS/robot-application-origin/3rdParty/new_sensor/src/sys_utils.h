#ifndef _SYS_UTILS_H_
#define _SYS_UTILS_H_

#include "sys_common.h"
#include "libubox/uloop.h"

typedef enum _sys_file_mode_e_
{
    SYS_FILE_RDONLY = 0x00,
    SYS_FILE_WRONLY = 0x01,
    SYS_FILE_RDWR   = 0x02
}SYS_FILE_MODE_E;

typedef enum _sys_socket_mode_e_
{
    SYS_SOCKET_UDP = 0x00,
    SYS_SOCKET_TCP = 0x01,
}SYS_SOCKET_MODE_E;

/****************************** time ****************************/
UINT64 sys_sys_time_sec(VOID);
UINT64 sys_sys_time_msec(VOID);
VOID sys_time_wait(UINT32 msec);

/****************************** file ****************************/
INT32 sys_file_open(const INT8 *file, SYS_FILE_MODE_E mode);
INT32 sys_file_read(INT32 fd, UINT8 *data, INT32 size);
INT32 sys_socket_noblock_set(INT32 fd);

/****************************** socket ****************************/
INT32 sys_socket_create(SYS_SOCKET_MODE_E mode);
INT32 sys_socket_udp_server_create(UINT16 port);
INT32 sys_socket_sendto(INT32 fd, const INT8 *ip, UINT16 port, const UINT8 *buf, INT32 len);

/****************************** serial ****************************/
INT32 sys_serial_init(const INT8 *dev_name, SYS_FILE_MODE_E mode);

/****************************** uloop ****************************/
typedef struct uloop_fd SYS_ULOOP_FD_T;

INT32 sys_uloop_init(VOID);
INT32 sys_uloop_run(VOID);
VOID sys_uloop_end(VOID);
VOID sys_uloop_done(VOID);
INT32 sys_uloop_add_read(SYS_ULOOP_FD_T *sock);
INT32 sys_uloop_del(SYS_ULOOP_FD_T *sock);

#endif // _SYS_UTILS_H_
