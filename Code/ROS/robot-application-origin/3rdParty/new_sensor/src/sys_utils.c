#include "sys_utils.h"
#include "sys_log.h"
#include <arpa/inet.h>
#include <unistd.h>
#include <time.h>
#include <fcntl.h>
#include <linux/filter.h>
#include <linux/if_packet.h>
#include <net/ethernet.h>
#include <net/if.h>
#include <netinet/in.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/select.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <errno.h>
#include <termios.h>

#define SYS_UTILS_DEBUG(fmt...) SYS_LOG_PRINT("SYS", SYS_LOG_LEVEL_DEBUG, (1 << SYS_LOG_MODE_FILE), fmt);
#define SYS_UTILS_INFO(fmt...)  SYS_LOG_PRINT("SYS", SYS_LOG_LEVEL_INFO,  (1 << SYS_LOG_MODE_FILE), fmt);
#define SYS_UTILS_NOTE(fmt...)  SYS_LOG_PRINT("SYS", SYS_LOG_LEVEL_NOTE,  (1 << SYS_LOG_MODE_FILE), fmt);
#define SYS_UTILS_ERROR(fmt...) SYS_LOG_PRINT("SYS", SYS_LOG_LEVEL_ERROR, (1 << SYS_LOG_MODE_FILE), fmt);
#define SYS_UTILS_FATAL(fmt...) SYS_LOG_PRINT("SYS", SYS_LOG_LEVEL_FATAL, (1 << SYS_LOG_MODE_FILE), fmt);

/****************************** time ****************************/
UINT64 sys_sys_time_sec(VOID)
{
    struct timespec times = {0, 0};
    clock_gettime(CLOCK_MONOTONIC, &times);
    return times.tv_sec;
}

UINT64 sys_sys_time_msec(VOID)
{
    struct timespec times = {0, 0};
    clock_gettime(CLOCK_MONOTONIC, &times);
    return ((times.tv_sec * 1000) + ((times.tv_nsec / 1000) / 1000));
}

VOID sys_time_wait(UINT32 msec)
{
    usleep(msec * 1000);
}

/****************************** file ****************************/
INT32 sys_file_open(const INT8 *file, SYS_FILE_MODE_E mode)
{
    INT32 fd = -1;
    switch(mode)
    {
        case SYS_FILE_RDONLY:
            fd = open(file, O_RDONLY);
            break;
        case SYS_FILE_WRONLY:
            fd = open(file, O_WRONLY);
            break;
        case SYS_FILE_RDWR:
            fd = open(file, O_RDWR);
            break;
        default:
            break;
    }
    if(-1 == fd)
    {
        SYS_UTILS_ERROR("open() %s failed, error: %s", file, strerror(errno));
        return SYS_ERROR;
    }
    return fd;
}

INT32 sys_file_read(INT32 fd, UINT8 *data, INT32 size)
{
    INT32 ret = SYS_OK;
    ret = read(fd, data, size);
    if(ret <= 0)
    {
        SYS_UTILS_ERROR("read() failed, error: %s", strerror(errno));
        return SYS_ERROR;
    }
    return ret;
}

INT32 sys_socket_noblock_set(INT32 fd)
{
    INT32 flags = 0;
    flags = fcntl(fd, F_GETFL, 0);
    if(flags < 0)
    {
        SYS_UTILS_ERROR("fcntl() failed, error: %s", strerror(errno));
        return SYS_ERROR;
    }
    if(fcntl(fd, F_SETFL, flags | O_NONBLOCK) < 0)
    {
        SYS_UTILS_ERROR("fcntl() failed, error: %s", strerror(errno));
        return SYS_ERROR;
    }

    return 0;
}

/****************************** socket ****************************/
INT32 sys_socket_create(SYS_SOCKET_MODE_E mode)
{
    INT32 fd = -1;
    switch(mode)
    {
        case SYS_SOCKET_TCP:
            fd = socket(PF_INET, SOCK_STREAM, 0);
            break;
        case SYS_SOCKET_UDP:
            fd = socket(PF_INET, SOCK_DGRAM, 0);
            break;
        default:
            break;
    }
    if(-1 == fd)
    {
        SYS_UTILS_ERROR("socket() failed, error: %s\n", strerror(errno));
        return SYS_ERROR;
    }
    return fd;
}

INT32 sys_socket_udp_server_create(UINT16 port)
{
    INT32 fd = 0;
    fd = socket(PF_INET, SOCK_DGRAM, 0);
    if(-1 == fd)
    {
        SYS_UTILS_ERROR("socket() failed, error: %s\n", strerror(errno));
        return SYS_ERROR;
    }
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(struct sockaddr_in));
    server_addr.sin_family      = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port        = htons(port);
    INT32 ret = 0;
    ret = bind(fd, (struct sockaddr *)&server_addr, sizeof(struct sockaddr_in));
    if(-1 == ret)
    {
        SYS_UTILS_ERROR("bind() failed, error: %s\n", strerror(errno));
        SYS_SAFE_CLOSE(fd);
        return SYS_ERROR;
    }
    return fd;
}

INT32 sys_socket_sendto(INT32 fd, const INT8 *ip, UINT16 port, const UINT8 *buf, INT32 len)
{
    INT32 ret = SYS_OK;
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(struct sockaddr_in));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port   = htons(port);
    inet_pton(AF_INET, ip, &server_addr.sin_addr.s_addr);
    ret = sendto(fd, buf, len, 0, (struct sockaddr *)&server_addr, sizeof(struct sockaddr_in));
    if(ret != len)
    {
        SYS_UTILS_ERROR("sendto() failed, ret: %d, len: %d, error: %s\n", ret, len, strerror(errno));
        ret = SYS_ERROR;
    }
    return SYS_OK;
}

/****************************** serial ****************************/
static INT32 sys_configure_serial_port(INT32 fd)
{
    struct termios tty;
    memset(&tty, 0, sizeof tty);
    if(tcgetattr(fd, &tty) != 0)
    {
        SYS_UTILS_ERROR("tcgetattr() failed, fd: %d, error: %s", fd, strerror(errno));
        return SYS_ERROR;
    }

    cfsetispeed(&tty, B115200);
    cfsetospeed(&tty, B115200);

    tty.c_cflag &= ~PARENB;
    tty.c_cflag &= ~CSTOPB;
    tty.c_cflag &= ~CSIZE;
    tty.c_cflag |= CS8;
    tty.c_cflag &= ~CRTSCTS;
    tty.c_cflag |= CREAD | CLOCAL;

    tty.c_lflag &= ~ICANON;
    tty.c_lflag &= ~ECHO;
    tty.c_lflag &= ~ECHOE;
    tty.c_lflag &= ~ECHONL;
    tty.c_lflag &= ~ISIG;

    tty.c_iflag &= ~(IXON | IXOFF | IXANY);
    tty.c_iflag &= ~(ICRNL | INLCR);
    tty.c_iflag &= ~(IGNCR);

    tty.c_oflag &= ~OPOST;

    tty.c_cc[VMIN]  = 1;
    tty.c_cc[VTIME] = 0;

    if(tcsetattr(fd, TCSANOW, &tty) != 0)
    {
        SYS_UTILS_ERROR("tcgetattr() failed, fd: %d, error: %s", fd, strerror(errno));
        return SYS_ERROR;
    }
    return SYS_OK;
}

INT32 sys_serial_init(const INT8 *dev_name, SYS_FILE_MODE_E mode)
{
    INT32 fd = -1;
    fd = open(dev_name, mode);
    if(fd < 0)
    {
        SYS_UTILS_ERROR("open %s failed", dev_name);
        return SYS_ERROR;
    }
    if(SYS_OK != sys_configure_serial_port(fd))
    {
        SYS_UTILS_ERROR("set %s failed", dev_name);
        close(fd);
        return SYS_ERROR;
    }
    return fd;
}

/****************************** uloop ****************************/
INT32 sys_uloop_init(VOID)
{
	return uloop_init();
}

INT32 sys_uloop_run(VOID)
{
	return uloop_run();
}

VOID sys_uloop_end(VOID)
{
	uloop_end();
}

VOID sys_uloop_done(VOID)
{
	uloop_done();
}

INT32 sys_uloop_add_read(SYS_ULOOP_FD_T *sock)
{
    if(uloop_fd_add((struct uloop_fd *)sock, ULOOP_READ) < 0)
    {
        SYS_UTILS_ERROR("add uloop failed, error: %s", strerror(errno));
        return SYS_ERROR;
    }

    return SYS_OK;
}

INT32 sys_uloop_del(SYS_ULOOP_FD_T *sock)
{
    if(uloop_fd_delete((struct uloop_fd *)sock) < 0)
    {
        SYS_UTILS_ERROR("del uloop failed, error: %s", strerror(errno));
        return SYS_ERROR;
    }
    return SYS_OK;
}

