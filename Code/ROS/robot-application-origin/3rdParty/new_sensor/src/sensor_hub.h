#ifndef _SYS_SENSOR_HUB_H_
#define _SYS_SENSOR_HUB_H_

#include "sys_common.h"
#include "sys_log.h"

#define SENSOR_DEBUG(fmt...) SYS_LOG_PRINT("SENSOR", SYS_LOG_LEVEL_DEBUG, (1 << SYS_LOG_MODE_FILE), fmt);
#define SENSOR_INFO(fmt...)  SYS_LOG_PRINT("SENSOR", SYS_LOG_LEVEL_INFO,  (1 << SYS_LOG_MODE_FILE), fmt);
#define SENSOR_NOTE(fmt...)  SYS_LOG_PRINT("SENSOR", SYS_LOG_LEVEL_NOTE,  (1 << SYS_LOG_MODE_FILE), fmt);
#define SENSOR_ERROR(fmt...) SYS_LOG_PRINT("SENSOR", SYS_LOG_LEVEL_ERROR, (1 << SYS_LOG_MODE_FILE), fmt);
#define SENSOR_FATAL(fmt...) SYS_LOG_PRINT("SENSOR", SYS_LOG_LEVEL_FATAL, (1 << SYS_LOG_MODE_FILE), fmt);

typedef struct _sensor_info_t_
{
    INT32 fd;       // light - socket; touch - gpio
    INT32 extra_fd; // light - serial; touch - udp client
    INT8 desc[32];  // light; touch
}SENSOR_INFO_T;

#endif // _SYS_SENSOR_HUB_H_
