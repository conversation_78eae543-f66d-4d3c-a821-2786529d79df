#include <stdlib.h>
#include <stdio.h>
#include <sys/stat.h>
#include <unistd.h>
#include <string.h>
#include <assert.h>
#include <sys/time.h>
#include <time.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <sys/socket.h>
#include <errno.h>
#include <getopt.h>
#include <signal.h>
#include "sys_common.h"
#include "sys_log.h"
#include "sys_utils.h"
// #include "xiaoli_application_ros2/include/xiaoli_com/deep_cmd.h"
#include "deep_cmd.h"

#define SENSOR_TEST_LOG_DEBUG(fmt...) SYS_LOG_PRINT("TEST", SYS_LOG_LEVEL_DEBUG, (1 << SYS_LOG_MODE_FILE), fmt);
#define SENSOR_TEST_LOG_INFO(fmt...)  SYS_LOG_PRINT("TEST", SYS_LOG_LEVEL_INFO,  (1 << SYS_LOG_MODE_FILE), fmt);
#define SENSOR_TEST_LOG_NOTE(fmt...)  SYS_LOG_PRINT("TEST", SYS_LOG_LEVEL_NOTE,  (1 << SYS_LOG_MODE_FILE), fmt);
#define SENSOR_TEST_LOG_ERROR(fmt...) SYS_LOG_PRINT("TEST", SYS_LOG_LEVEL_ERROR, (1 << SYS_LOG_MODE_FILE), fmt);
#define SENSOR_TEST_LOG_FATAL(fmt...) SYS_LOG_PRINT("TEST", SYS_LOG_LEVEL_FATAL, (1 << SYS_LOG_MODE_FILE), fmt);

int sensor_test_init(VOID)
{
    return 0;
}

int sensor_test_deinit(VOID)
{
    return 0;
}

static void sensor_test_print_usage(void)
{
    SENSOR_TEST_LOG_NOTE("------ TX test -------");
    SENSOR_TEST_LOG_NOTE("-t 1-flash");
    SENSOR_TEST_LOG_NOTE("-t 2-blue keep;3-flash;4-breathe;5-blink");
    SENSOR_TEST_LOG_NOTE("-t 6-red keep;7-flash;8-breathe;9-blink");
    SENSOR_TEST_LOG_NOTE("-t 10-orange flash;11-breathe;12-blink");
    SENSOR_TEST_LOG_NOTE("-t 13-white breathe;14-keep");
    SENSOR_TEST_LOG_NOTE("-t 15-pink flash");
}

static int g_cmd_no = 0;

static int sensor_test_parse_options(int argc, char *argv[])
{
    int opt = 0;

    while(-1 != (opt = getopt(argc, argv, "?ht:")))
    {
        switch(opt)
        {
            case 't':
                g_cmd_no = atoi(optarg);
                break;
            case 'h':
            case '?':
            default:
                sensor_test_print_usage();
                return -1;
        }
    }

    return 0;
}

static void sensor_test_signal_fatal_handler(int signum)
{
    SENSOR_TEST_LOG_FATAL(">>>>>> Fatal signal %d caught <<<<<<\n", signum);
    exit(signum);
}

static void sensor_test_signal_ignore_handler(int signum)
{
    SENSOR_TEST_LOG_ERROR(">>>>>> Ignore signal %d caught <<<<<<\n", signum);
}

void sensor_test_signal_init(void)
{
	struct sigaction sig_stop_action;
    struct sigaction sig_no_reaction;

    /* Signal handlers */
    sig_stop_action.sa_handler = sensor_test_signal_fatal_handler;
    sigemptyset(&sig_stop_action.sa_mask);
    sig_stop_action.sa_flags = 0;

    sigaction(SIGTERM, &sig_stop_action, NULL);
    sigaction(SIGINT, &sig_stop_action, NULL);
    sigaction(SIGSEGV, &sig_stop_action, NULL);
    sigaction(SIGBUS, &sig_stop_action, NULL);
    sigaction(SIGKILL, &sig_stop_action, NULL);
    sigaction(SIGFPE, &sig_stop_action, NULL);
    sigaction(SIGILL, &sig_stop_action, NULL);
    sigaction(SIGQUIT, &sig_stop_action, NULL);
    sigaction(SIGHUP, &sig_stop_action, NULL);
    sigaction(SIGPIPE, &sig_stop_action, NULL);

    sig_no_reaction.sa_handler = sensor_test_signal_ignore_handler;
    sigemptyset(&sig_no_reaction.sa_mask);
    sig_no_reaction.sa_flags = 0;
    sigaction(SIGALRM, &sig_no_reaction, NULL);
    sigaction(SIGUSR1, &sig_no_reaction, NULL);
    sigaction(SIGUSR2, &sig_no_reaction, NULL);
}

int main(int argc, char *argv[])
{
    int fd = 0;

    SENSOR_TEST_LOG_NOTE("------> start sensor test <------");
    if(-1 == sensor_test_parse_options(argc, argv))
    {
        SENSOR_TEST_LOG_NOTE("------> exit sensor test <------");
        return 0;
    }
    SYS_LOG_CAPA_T capa;
    memset(&capa, 0, sizeof(SYS_LOG_CAPA_T));
    capa.level = SYS_LOG_LEVEL_NOTE;
    sys_log_init(&capa);
    sensor_test_signal_init();
    sensor_test_init();
    do
    {
        fd = socket(PF_INET, SOCK_DGRAM, 0);
        if(-1 == fd)
        {
            SENSOR_TEST_LOG_ERROR("socket() failed, %s\n", strerror(errno));
            break;
        }
        sys_socket_noblock_set(fd);
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(struct sockaddr_in));
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(12345);
        inet_pton(AF_INET, "127.0.0.1", &server_addr.sin_addr.s_addr);
        int ret                 = 0;
        int off                 = 0;
        int len                 = 0;
        unsigned char buf[1024] = {0};
        unsigned int cmd        = 0;
        unsigned int sub_cmd    = 0;
        switch(g_cmd_no)
        {
            case 1:
                cmd = DEEP_CMD_FLASHLIGHT;
                break;
            case 2:
                // 蓝灯常亮（正常运行，联网正常）
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_1;
                break;
            case 3:
                // 蓝灯闪烁（开机自检）
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_2;
                break;
            case 4:
                // 蓝色呼吸灯（状态正常、等待联网中）
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_3;
                break;
            case 5:
                // 蓝色流水灯
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_C;
                break;
            case 6:
                // 红灯常亮（低电量）
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_6;
                break;
            case 7:
                // 红灯闪烁（故障，需要重启）
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_7;
                break;
            case 8:
                // 红灯呼吸灯
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_D;
                break;
            case 9:
                // 红灯流水灯
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_E;
                break;
            case 10:
                // 橙色闪烁
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_01;
                break;
            case 11:
                // 橙色呼吸
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_05;
                break;
            case 12:
                // 橙色流水
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_06;
                break;
            case 13:
                // 白色呼吸
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_03;
                break;
            case 14:
                // 白色常亮
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_04;
                break;
            case 15:
                // 粉色闪烁
                cmd     = DEEP_CMD_LIGHT;
                sub_cmd = DEEP_CMD_LIGHT_02;
                break;
            default:
                cmd = 0;
                SENSOR_TEST_LOG_ERROR("unkown cmd no: %d\n", g_cmd_no);
                break;
        }
        if(cmd <= 0)
        {
            break;
        }
        memset(buf, 0, sizeof(buf));
        len = sizeof(pk_t);
        off = 0;
        SYS_L_2_B_32(buf, off, sizeof(pk_t));
        off += 4;
        SYS_L_2_B_32(buf, off, cmd);
        off += 4;
        SYS_L_2_B_32(buf, off, 0x00000001);
        off += 4;
        switch(cmd)
        {
            case DEEP_CMD_LIGHT:
                SYS_L_2_B_32(buf, off, sub_cmd);
                off += 4;
                break;
            case DEEP_CMD_FLASHLIGHT:
                SYS_L_2_B_32(buf, off, 1);
                off += 4;
                SYS_L_2_B_32(buf, off, 100);
                off += 4;
                break;
            default:
                SENSOR_TEST_LOG_ERROR("unkown cmd: 0x%08x\n", cmd);
                off = 0;
                break;
        }
        if(off <= 0)
        {
            break;
        }
        ret = sendto(fd, buf, len, 0, (struct sockaddr *)&server_addr, sizeof(struct sockaddr_in));
        if(ret != len)
        {
            SENSOR_TEST_LOG_ERROR("sendto() failed, ret: %d, len: %d\n", ret, len);
        }
        else
        {
            SENSOR_TEST_LOG_NOTE("send: %d", len);
        }
    }while(0);
    if(-1 != fd)
    {
        close(fd);
        fd = -1;
    }
    sensor_test_deinit();
    SENSOR_TEST_LOG_NOTE("------> exit sensor test <------");
    return 0;
}
