#include "sensor_hub.h"
#include "deep_cmd.h"
#include <netinet/in.h>
#include <sys/socket.h>
#include <unistd.h>
#include "sys_utils.h"

VOID sensor_touch_callback(INT32 fd, VOID *arg)
{
    SYS_PTR_CHECK_VOID(arg, SENSOR_ERROR);
    SENSOR_INFO_T *info = NULL;
    UINT32 value        = 0;
    info                = (SENSOR_INFO_T *)arg;
    if(SYS_OK != sys_file_read(fd, (UINT8 *)&value, sizeof(UINT8)))
    {
        SENSOR_ERROR("read %s failed\n", info->desc);
        return;
    }
    pk_t pk;
    memset(&pk, 0, sizeof(pk_t));
    pk.head.len                  = htonl(sizeof(pk_t));
    pk.head.cmd                  = htonl(DEEP_CMD_SENSOR);
    pk.rsp_body.sensor.sensor_id = htonl(1);
    pk.rsp_body.sensor.value     = htonl(value);
    pk.rsp_body.sensor.timestamp = htonl(sys_sys_time_sec());
    if(SYS_OK != sys_socket_sendto(info->extra_fd, "127.0.0.1", 1234, (const UINT8 *)&pk, sizeof(pk_t)))
    {
        SENSOR_ERROR("sendto %s failed, len: %d\n", info->desc);
    }
}
