#include <errno.h>
#include <netinet/in.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <unistd.h>
#include <signal.h>
#include <fcntl.h>
#include <termios.h>
#include "sys_common.h"
#include "sys_log.h"
#include "sys_utils.h"
// #include "xiaoli_application_ros2/include/xiaoli_com/deep_cmd.h"
#include "deep_cmd.h"

#define SENSOR_LOG_DEBUG(fmt...) SYS_LOG_PRINT("SENSOR", SYS_LOG_LEVEL_DEBUG, (1 << SYS_LOG_MODE_FILE), fmt);
#define SENSOR_LOG_INFO(fmt...)  SYS_LOG_PRINT("SENSOR", SYS_LOG_LEVEL_INFO,  (1 << SYS_LOG_MODE_FILE), fmt);
#define SENSOR_LOG_NOTE(fmt...)  SYS_LOG_PRINT("SENSOR", SYS_LOG_LEVEL_NOTE,  (1 << SYS_LOG_MODE_FILE), fmt);
#define SENSOR_LOG_ERROR(fmt...) SYS_LOG_PRINT("SENSOR", SYS_LOG_LEVEL_ERROR, (1 << SYS_LOG_MODE_FILE), fmt);
#define SENSOR_LOG_FATAL(fmt...) SYS_LOG_PRINT("SENSOR", SYS_LOG_LEVEL_FATAL, (1 << SYS_LOG_MODE_FILE), fmt);

static VOID sensor_hex_print(UINT8 *buf, INT32 size)
{
    INT32 i = 0;
    printf("data_len: %d\n", size);
    for(i = 1; i <= size; i++)
    {
        printf("0x%02x:", buf[i - 1]);
        if((i % 8) == 0)
        {
            printf("\n");
        }
    }
    printf("\n");
}

static INT32 g_open_serial = 0;

static VOID sensor_print_usage(VOID)
{
    SENSOR_LOG_NOTE("------ RX test -------");
    SENSOR_LOG_NOTE("-t 0 - off; 1 - on");
}

static INT32 sensor_parse_options(INT32 argc, INT8 *argv[])
{
    INT32 opt = 0;

    while(-1 != (opt = getopt(argc, argv, "?hlt:")))
    {
        switch(opt)
        {
            case 't':
                g_open_serial = atoi(optarg);
                break;
            case 'h':
            case '?':
            default:
                sensor_print_usage();
                return -1;
        }
    }

    return 0;
}

static VOID sensor_signal_fatal_handler(INT32 signum)
{
    SENSOR_LOG_FATAL(">>>>>> Fatal signal %d caught <<<<<<\n", signum);
    exit(signum);
}

static VOID sensor_signal_ignore_handler(INT32 signum)
{
    SENSOR_LOG_ERROR(">>>>>> Ignore signal %d caught <<<<<<\n", signum);
}

VOID sensor_signal_init(VOID)
{
	struct sigaction sig_stop_action;
    struct sigaction sig_no_reaction;

    /* Signal handlers */
    sig_stop_action.sa_handler = sensor_signal_fatal_handler;
    sigemptyset(&sig_stop_action.sa_mask);
    sig_stop_action.sa_flags = 0;

    sigaction(SIGTERM, &sig_stop_action, NULL);
    sigaction(SIGINT, &sig_stop_action, NULL);
    sigaction(SIGSEGV, &sig_stop_action, NULL);
    sigaction(SIGBUS, &sig_stop_action, NULL);
    sigaction(SIGKILL, &sig_stop_action, NULL);
    sigaction(SIGFPE, &sig_stop_action, NULL);
    sigaction(SIGILL, &sig_stop_action, NULL);
    sigaction(SIGQUIT, &sig_stop_action, NULL);
    sigaction(SIGHUP, &sig_stop_action, NULL);
    sigaction(SIGPIPE, &sig_stop_action, NULL);

    sig_no_reaction.sa_handler = sensor_signal_ignore_handler;
    sigemptyset(&sig_no_reaction.sa_mask);
    sig_no_reaction.sa_flags = 0;
    sigaction(SIGALRM, &sig_no_reaction, NULL);
    sigaction(SIGUSR1, &sig_no_reaction, NULL);
    sigaction(SIGUSR2, &sig_no_reaction, NULL);
}

typedef struct _sensor_info_t_
{
    INT32 sock_fd;
    INT32 serial_fd;
}SENSOR_INFO_T;

typedef struct _sensor_cmd_t_
{
    head_t head;
    UINT8 *buf;
    INT32 len;
}SENSOR_CMD_T;

INT32 sensor_init(SENSOR_INFO_T *info)
{
    memset(info, 0, sizeof(SENSOR_INFO_T));
    info->sock_fd   = -1;
    info->serial_fd = -1;
    return SYS_OK;
}

INT32 sensor_deinit(SENSOR_INFO_T *info)
{
    if(-1 != info->sock_fd)
    {
        close(info->sock_fd);
        info->sock_fd = -1;
    }
    if(-1 != info->serial_fd)
    {
        close(info->serial_fd);
        info->serial_fd = -1;
    }
    return SYS_OK;
}

#define FRAME_HEADER1 (0xEE)
#define FRAME_HEADER2 (0xEF)
#define FRAME_TAIL1 (0xEF)
#define FRAME_TAIL2 (0xFF)

typedef enum
{
    LIGHT_BLINK   = 1, // 流水
    LIGHT_BREATHE = 2, // 呼吸
    LIGHT_FLASH   = 3, // 闪烁
    LIGHT_KEEP    = 4, // 常亮
}SENSOR_LIGHT_E;

#define Frame_length         10

#define Test_ID              0x25
#define Test_version         0x30
#define Test_type            0x35
#define Test_hex             0x40
#define Test_size            0x45
#define Test_crc             0x50

#define Frame_ID             0x01
#define Frame_VERSION        0x01

#define Type_req            0x11
#define Type_cmd            0x12
#define Type_Order          0x15

#define Hex_MCU             0x30
#define Hex_Light           0x31
#define Hex_Duoji           0x32

INT32 sensor_light_data_pack(const SENSOR_CMD_T *cmd, UINT8 *buf, INT32 size)
{
    INT32 ret          = SYS_OK;
    INT32 off          = 0;
    INT32 len_off      = 0;
    UINT8 light_id     = 0;
    UINT8 light_r      = 0;
    UINT8 light_g      = 0;
    UINT8 light_b      = 0;
    UINT8 light_type   = 0;
    UINT8 light_bright = 0;
    UINT16 light_freq  = 0;
    UINT32 light_state = 0;
    UINT32 light_cmd   = 0;

    light_cmd = SYS_B_2_L_32(cmd->buf, off);
    off += 4;
    SENSOR_LOG_NOTE("LIGHT_TYPE: 0x%08x", light_cmd);
    light_id = 0x66;
    switch(light_cmd)
    {
        case DEEP_CMD_LIGHT_1:
            // 蓝灯常亮（正常运行，联网正常）
            light_type   = LIGHT_KEEP;
            light_freq   = 500;
            light_r      = 0;
            light_g      = 0;
            light_b      = 255;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_2:
            // 蓝灯闪烁（开机自检）
            light_type   = LIGHT_FLASH;
            light_freq   = 500;
            light_r      = 0;
            light_g      = 0;
            light_b      = 255;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_3:
            // 蓝色呼吸灯（状态正常、等待联网中）
            light_type   = LIGHT_BREATHE;
            light_freq   = 500;
            light_r      = 0;
            light_g      = 0;
            light_b      = 255;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_C:
            // 蓝色流水灯
            light_type   = LIGHT_BLINK;
            light_freq   = 500;
            light_r      = 0;
            light_g      = 0;
            light_b      = 255;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_6:
            // 红灯常亮（低电量）
            light_type   = LIGHT_KEEP;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 0;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_7:
            // 红灯闪烁（故障，需要重启）
            light_type   = LIGHT_FLASH;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 0;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_D:
            // 红灯呼吸灯
            light_type   = LIGHT_BREATHE;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 0;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_E:
            // 红灯流水灯
            light_type   = LIGHT_BLINK;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 0;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_01:
            // 橙色闪烁
            light_type   = LIGHT_FLASH;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 140;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_02:
            // 粉色闪烁
            light_type   = LIGHT_FLASH;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 192;
            light_b      = 203;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_03:
            // 白色呼吸
            light_type   = LIGHT_BREATHE;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 255;
            light_b      = 255;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_04:
            // 白色常亮
            light_type   = LIGHT_KEEP;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 255;
            light_b      = 255;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_05:
            // 橙色呼吸
            light_type   = LIGHT_BREATHE;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 140;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        case DEEP_CMD_LIGHT_06:
            // 橙色流水
            light_type   = LIGHT_BLINK;
            light_freq   = 500;
            light_r      = 255;
            light_g      = 140;
            light_b      = 0;
            light_bright = 100;
            light_state  = 63;
            break;
        default:
            ret = SYS_ERROR;
            SENSOR_LOG_NOTE("unkown LIGHT_TYPE: 0x%08x", light_cmd);
            break;
    }
    if(SYS_OK != ret)
    {
        return ret;
    }

    // Frame_length = 10 Bytes
    off          = 0;
    buf[off]     = FRAME_HEADER1;
    buf[off + 1] = FRAME_HEADER2;
    buf[off + 2] = Frame_ID;
    buf[off + 3] = Frame_VERSION;
    buf[off + 4] = Type_Order;
    buf[off + 5] = Hex_Light;
    off += 6;
    len_off = off;
    off += 2; // skip size
    SYS_L_2_B_16(buf, off, Test_crc); // crc
    off += 2;

    UINT16 temp_off = 0;
    UINT16 data_len = 0;
    temp_off        = off;
    buf[off]        = light_id;   // id
    buf[off + 1]    = light_type; // SENSOR_LIGHT_E
    off += 2;
    SYS_L_2_B_16(buf, off, light_freq); // freq
    off += 2;
    buf[off]     = light_r;      // R
    buf[off + 1] = light_g;      // G
    buf[off + 2] = light_b;      // B
    buf[off + 3] = light_bright; // Brightness
    off += 4;
    SYS_L_2_B_32(buf, off, light_state); // status_id
    off += 4;
    data_len = off - temp_off;
    SYS_L_2_B_16(buf, len_off, data_len); // data_len
    ret = off;
    return ret;
}

INT32 sensor_flashlight_data_pack(const SENSOR_CMD_T *cmd, UINT8 *buf, INT32 size)
{
    INT32 ret           = SYS_OK;
    INT32 off           = 0;
    INT32 len_off       = 0;
    UINT32 light_bright = 0;

    off += 4;
    light_bright = SYS_B_2_L_32(cmd->buf, off);
    off += 4;
    SENSOR_LOG_NOTE("light_bright: %d", light_bright);

    // Frame_length = 10 Bytes
    off          = 0;
    buf[off]     = FRAME_HEADER1;
    buf[off + 1] = FRAME_HEADER2;
    buf[off + 2] = Frame_ID;
    buf[off + 3] = Frame_VERSION;
    buf[off + 4] = Type_Order;
    buf[off + 5] = Hex_Duoji;
    off += 6;
    len_off = off;
    off += 2; // skip size
    SYS_L_2_B_16(buf, off, Test_crc); // crc
    off += 2;

    UINT16 temp_off = 0;
    UINT16 data_len = 0;
    temp_off        = off;
    SYS_L_2_B_32(buf, off, light_bright); // Brightness
    off += 4;
    data_len = off - temp_off;
    SYS_L_2_B_16(buf, len_off, data_len); // data_len
    ret = off;
    return ret;
}

INT32 sensor_cmd_handle(SENSOR_INFO_T *info, const SENSOR_CMD_T *cmd)
{
    INT32 ret      = SYS_OK;
    INT32 data_len = 0;
    UINT8 buf[256] = {0};
    switch(cmd->head.cmd)
    {
        case DEEP_CMD_LIGHT:
            SENSOR_LOG_NOTE("DEEP_CMD_LIGHT: 0x%08x", cmd->head.cmd);
            ret = sensor_light_data_pack(cmd, buf, sizeof(buf));
            break;
        case DEEP_CMD_FLASHLIGHT:
            SENSOR_LOG_NOTE("DEEP_CMD_FLASHLIGHT: 0x%08x", cmd->head.cmd);
            ret = sensor_flashlight_data_pack(cmd, buf, sizeof(buf));
            break;
        default:
            SENSOR_LOG_ERROR("unkown cmd: 0x%08x", cmd->head.cmd);
            ret = SYS_ERROR;
            break;
    }
    if(ret > 0)
    {
        data_len = ret;
        sensor_hex_print(buf, data_len);
        ret = write(info->serial_fd, buf, data_len);
        if(ret != data_len)
        {
            SENSOR_LOG_ERROR("write() failed, error: %s, [%d != %d]", strerror(errno), ret, data_len);
            ret = SYS_ERROR;
        }
        else
        {
            ret = SYS_OK;
        }
    }
    return ret;
}

INT32 configure_serial_port(INT32 fd)
{
    struct termios tty;
    memset(&tty, 0, sizeof tty);
    if(tcgetattr(fd, &tty) != 0)
    {
        SENSOR_LOG_ERROR("tcgetattr() failed, fd: %d, error: %s", fd, strerror(errno));
        return SYS_ERROR;
    }

    cfsetispeed(&tty, B115200);
    cfsetospeed(&tty, B115200);

    tty.c_cflag &= ~PARENB;
    tty.c_cflag &= ~CSTOPB;
    tty.c_cflag &= ~CSIZE;
    tty.c_cflag |= CS8;
    tty.c_cflag &= ~CRTSCTS;
    tty.c_cflag |= CREAD | CLOCAL;

    tty.c_lflag &= ~ICANON;
    tty.c_lflag &= ~ECHO;
    tty.c_lflag &= ~ECHOE;
    tty.c_lflag &= ~ECHONL;
    tty.c_lflag &= ~ISIG;

    tty.c_iflag &= ~(IXON | IXOFF | IXANY);
    tty.c_iflag &= ~(ICRNL | INLCR);
    tty.c_iflag &= ~(IGNCR);

    tty.c_oflag &= ~OPOST;

    tty.c_cc[VMIN]  = 1;
    tty.c_cc[VTIME] = 0;

    if(tcsetattr(fd, TCSANOW, &tty) != 0)
    {
        SENSOR_LOG_ERROR("tcgetattr() failed, fd: %d, error: %s", fd, strerror(errno));
        return SYS_ERROR;
    }
    return SYS_OK;
}

INT32 sensor_serial_init(const INT8 *dev_name)
{
    INT32 fd = -1;
    fd = open(dev_name, O_RDWR);
    if(fd < 0)
    {
        SENSOR_LOG_ERROR("open %s failed", dev_name);
        return -1;
    }
    if(SYS_OK != configure_serial_port(fd))
    {
        SENSOR_LOG_ERROR("set %s failed", dev_name);
        close(fd);
        return -1;
    }
    return fd;
}

INT32 main(INT32 argc, INT8 *argv[])
{
    SENSOR_LOG_NOTE("------> start sensor <------");
    if(-1 == sensor_parse_options(argc, argv))
    {
        SENSOR_LOG_NOTE("------> exit sensor <------");
        return 0;
    }
    SYS_LOG_CAPA_T capa;
    memset(&capa, 0, sizeof(SYS_LOG_CAPA_T));
    capa.level = SYS_LOG_LEVEL_NOTE;
    sys_log_init(&capa);
    sensor_signal_init();
    const INT8 *serial_name = "/dev/ttyS3";
    INT32 fd = 0;
    INT32 serial_fd = -1;
    SENSOR_INFO_T sensor_info;
    (VOID)sensor_init(&sensor_info);
    do
    {
        if(g_open_serial)
        {
            while(1)
            {
                serial_fd = sensor_serial_init(serial_name);
                if(serial_fd > 0)
                {
                    SENSOR_LOG_NOTE("open %s ok, fd: %d", serial_name, serial_fd);
                    break;
                }
                SENSOR_LOG_ERROR("open %s failed", serial_name);
                sys_time_wait(10000);
            }
            sensor_info.serial_fd = serial_fd;
        }
        fd = socket(PF_INET, SOCK_DGRAM, 0);
        if(-1 == fd)
        {
            SENSOR_LOG_ERROR("socket() failed, error: %s\n", strerror(errno));
            break;
        }
        sensor_info.sock_fd = fd;
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(struct sockaddr_in));
        server_addr.sin_family = AF_INET;
        server_addr.sin_addr.s_addr = INADDR_ANY;
        server_addr.sin_port = htons(12345);
        INT32 ret = 0;
        ret = bind(fd, (struct sockaddr *)&server_addr, sizeof(struct sockaddr_in));
        if(-1 == ret)
        {
            SENSOR_LOG_ERROR("bind() failed, error: %s\n", strerror(errno));
            break;
        }
        INT32 len        = 0;
        UINT8 buf[1024] = {0};
        INT32 off        = 0;
        SENSOR_CMD_T sensor_cmd;
        struct sockaddr_in client_addr;
        socklen_t client_addr_len = 0;
        client_addr_len           = sizeof(struct sockaddr_in);
        while(1)
        {
            off = 0;
            memset(buf, 0, sizeof(buf));
            memset(&client_addr, 0, sizeof(client_addr));
            len = recvfrom(fd, buf, sizeof(buf), 0, (struct sockaddr *)&client_addr, &client_addr_len);
            if(len < sizeof(pk_t))
            {
                SENSOR_LOG_ERROR("recv failed, len: %d\n", len);
                continue;
            }
            memset(&sensor_cmd, 0, sizeof(SENSOR_CMD_T));
            sensor_cmd.head.len = SYS_B_2_L_32(buf, off);
            off += 4;
            sensor_cmd.head.cmd = SYS_B_2_L_32(buf, off);
            off += 4;
            sensor_cmd.head.stat = SYS_B_2_L_32(buf, off);
            off += 4;
            if(sensor_cmd.head.len < sizeof(pk_t))
            {
                SENSOR_LOG_ERROR("data error, len: %d\n", sensor_cmd.head.len);
                continue;
            }
            sensor_cmd.buf = buf + off;
            sensor_cmd.len = len - off;
            ret = sensor_cmd_handle(&sensor_info, &sensor_cmd);
            if(SYS_OK != ret)
            {
                SENSOR_LOG_ERROR("handle failed, cmd: 0x%0x\n", sensor_cmd.head.cmd);
            }
            // ret = sendto(fd, buf, len, 0, (struct sockaddr *)&client_addr, sizeof(struct sockaddr_in));
            // if(ret != len)
            // {
            //     SENSOR_LOG_ERROR("sendto() failed, ret: %d, len: %d\n", ret, len);
            // }
        }
    }while(0);
    (VOID)sensor_deinit(&sensor_info);
    SENSOR_LOG_NOTE("------> exit sensor <------");
    return 0;
}
