#pragma once
/****命令码类型定义（灯光、动作、表情、语音、切网）****/
//调节灯光
#define DEEP_CMD_LIGHT                                   0x00000100  //命令，下同
#define DEEP_CMD_LIGHT_RES                               0x80000100  //响应，下同
//显示表情     
#define DEEP_CMD_EXPRESSION                              0x00000110
#define DEEP_CMD_EXPRESSION_RES                          0x80000110
//执行动作（运动技能）
#define DEEP_CMD_MOTION                                  0x00000120
#define DEEP_CMD_MOTION_RES                              0x80000120
//播放语音（命令值为具体语音文件路径）
#define DEEP_CMD_AUDIO                                   0x00000130
#define DEEP_CMD_AUDIO_RES                               0x80000130
//传感器
#define DEEP_CMD_SENSOR                                  0x00000140
#define DEEP_CMD_SENSOR_RES                              0x80000140//通过该CMD发送传感器数据
//切换网络命令
#define DEEP_CMD_NET                                     0x00000150
#define DEEP_CMD_NET_RES                                 0x80000150
//手电筒
#define DEEP_CMD_FLASHLIGHT                              0x00000160
#define DEEP_CMD_FLASHLIGHT_RES                          0x80000160
//音量
#define DEEP_CMD_VOLUME                                  0x00000170
#define DEEP_CMD_VOLUME_RES                              0x80000170
//电量
#define DEEP_CMD_POWER                                   0x00000180
#define DEEP_CMD_POWER_RES                               0x80000180
//动作模式
#define DEEP_CMD_ACTION                                  0x00000190
#define DEEP_CMD_ACRION_RES                              0x80000190
//自适应地形状态
#define DEEP_CMD_AI_MOTION                                0x00000200
#define DEEP_CMD_AI_MOTION_RES                            0x80000200
//移动和转动
#define DEEP_CMD_MOVEANDRO                               0x00000201
#define DEEP_CMD_MOVEANDRO_RES                               0x80000201


/****状态码定义****/
#define DEEP_STATUS_RUN                                   0x00000001 //运动状态
#define DEEP_STATUS_STOP                                  0x00000001 //停止状态
#define DEEP_STATUS_ERROR                                 0x00000002 //错误状态
#define DEEP_STATUS_OFFLINE                               0x00000003 //离线状态

/****命令值定义****/
//灯光类型
#define DEEP_CMD_LIGHT_1                                   0x00000101//蓝灯常亮（正常运行，联网正常）
#define DEEP_CMD_LIGHT_2                                   0x00000102//蓝灯闪烁（开机自检）
#define DEEP_CMD_LIGHT_3                                   0x00000103//蓝色呼吸灯（状态正常、等待联网中）
#define DEEP_CMD_LIGHT_4                                   0x00000104//黄灯常亮（机器人建联成功，APP可开启远程控制）
#define DEEP_CMD_LIGHT_5                                   0x00000105//黄灯闪烁（机器人互动建联中）
#define DEEP_CMD_LIGHT_6                                   0x00000106//红灯常亮（低电量）
#define DEEP_CMD_LIGHT_7                                   0x00000107//红灯闪烁（故障，需要重启）
#define DEEP_CMD_LIGHT_8                                   0x00000108//白色常亮
#define DEEP_CMD_LIGHT_9                                   0x00000109//白色呼吸
#define DEEP_CMD_LIGHT_A                                   0x0000010A//黄色呼吸
#define DEEP_CMD_LIGHT_B                                   0x0000010B//橙色常亮
#define DEEP_CMD_LIGHT_C                                   0x0000010C//蓝色流水灯
#define DEEP_CMD_LIGHT_D                                   0x0000010D//红色呼吸灯
#define DEEP_CMD_LIGHT_E                                   0x0000010E//红色流水灯

/* 新的燈光類型
5-6位:
04:white
02:orange
03:pink
01:yellow
05:blue

7-8位:
01:常亮 — keepAwake
02:闪烁 — blinking
03:呼吸灯 — breathing
04:流水灯 — running
*/
#define DEEP_CMD_LIGHT_01                                  0x00000202//橙色闪烁
#define DEEP_CMD_LIGHT_02                                  0x00000302//粉色闪烁
#define DEEP_CMD_LIGHT_03                                  0x00000403//白色呼吸
#define DEEP_CMD_LIGHT_04                                  0x00000401//白色常亮
#define DEEP_CMD_LIGHT_05                                  0x00000203//橙色呼吸
#define DEEP_CMD_LIGHT_06                                  0x00000204//橙色流水

//传感器类型
#define DEEP_CMD_SENSOR_RES_BACK                         0x80000141     //背部传感器
#define DEEP_CMD_SENSOR_RES_HEAD                         0x80000142     //头部传感器
#define DEEP_CMD_SENSOR_RES_LOAD                         0x80000143     //载物传感器

//语音类型
#define DEEP_CMD_AUDIO_HEAD                              0x0000013E     //摸头语音
#define DEEP_CMD_AUDIO_BACK                              0x0000013F     //摸背语音

//动作模式
#define DEEP_CMD_ACTION_1                                   0x00000191//跟随
#define DEEP_CMD_ACTION_WALK                                0x21010300//步行/牵引
#define DEEP_CMD_ACTION_RUN                                 0x21010307//奔跑
#define DEEP_CMD_ACTION_STAIR                               0x21010407//爬楼
#define DEEP_CMD_ACTION_CLIMBE                              0x21010402//爬坡
#define DEEP_CMD_ACTION_SOFTSTOP                            0x21020C0E//软急停

//POSITION MODE
#define POSITION_CMD     0x31010D07
#define POSITION_ANG_VEL 0x122


typedef struct head_t{
    int len; // 消息长度
    int cmd; // 命令码
    int stat;// 状态码
}head_t;//协议头

typedef struct light_t {
    int color; // 颜色
    int type; //显示方式
    int frequency; // 频率
    int bright;//明暗
}light_t;//指示灯

typedef struct flashlight_t
{
    int flashswitch;
    int brightness;
}flashlight_t;//手电筒


typedef struct expression_t {
    int type; // 表情类型
}expression_t;

typedef struct motion_t {
    int type; // 动作（站立、趴下、作揖、比心等）
}motion_t;

typedef struct actionMode_t
{
    int type;       //动作模式选择（跟随followMe、步行walk、奔跑run、 爬楼stairClimbe、爬坡climbe、牵引traction）
}actionMode_t;

typedef struct intelligentMode_t
{
    int type;       //1开启自适应地形，0关闭自适应地形
}intelligentMode_t;

typedef struct sceneMode_t
{
    int type;       //场景模式选择：0-宅家模式 1-遛狗模式 2-外出模式 3-遥控模式
}sceneMode_t;

typedef struct audio_t {
    char path[256]; // 音频文件路径
}audio_t;

typedef struct sensor_t {
    int sensor_id; // 传感器 ID
    int value; // 传感器值（状态）
    int timestamp; // 时间戳
}sensor_t;

typedef struct net_t {
    int netswitch; // 网络选择
}net_t;

typedef struct wifi_status{
    char wifi_name[256];
    int wifiSwitch; //on or off
    int isWifiConnect; //whether use wifi to access internet
}wifi_status;

typedef struct power_status{
    int chargeStatus;   //0未在充电，1充电中 2 充满
    int power_level;
}power_status;

typedef struct rotateAndMove_t{
    int angle;   //转动角度(弧度)
    int distance; //移动距离(米)
}rotateAndMove_t;


typedef struct rqt_body_t {
    union {
        light_t light;
        expression_t expression;
        motion_t motion;
        audio_t audio;
        net_t net;
        actionMode_t actionType;
        intelligentMode_t intelligentSwitch;
        rotateAndMove_t attitude;
        flashlight_t flashlight;
    };
}rqt_body_t;

typedef struct rsp_body_t {
    union {
        sensor_t sensor;
        wifi_status wifiAttribute;
        power_status powerAttribute;
    };
}rsp_body_t;

typedef struct pk_t {
    head_t head;
    union {
        rqt_body_t rqt_body;
        rsp_body_t rsp_body;
    };
}pk_t;