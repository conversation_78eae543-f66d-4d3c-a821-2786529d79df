/*!
 * \file Websocket.cpp
 *
 * \fun webSocket 客户端/服务端类的实现
 * \author GYJ
 * \date 2022.3
 *
 */
 #include "Websocket.h"
 #include "libWebSocket.h"
 #include "cJSON.h"

using websocketpp::lib::placeholders::_1;
 using websocketpp::lib::placeholders::_2;
 using websocketpp::lib::bind;
 
 namespace WS
 { 
 
	 //接收消息回调函数
	 extern MsgCallback g_RecvMsgCallback;
	 //回调函数句柄
	 extern void* g_pMsgHadle;
	 extern WebSocket* g_pWebSocket;
 
	 WebSocketSever::WebSocketSever(){
		 m_nType = EN_WS_SEVER;
		 // Initialize Asio Transport
		 m_server.init_asio();
		 // Register handler callbacks
		 m_server.set_open_handler(bind(&WebSocketSever::open, this, ::_1));
		 m_server.set_close_handler(bind(&WebSocketSever::close, this, ::_1));
		 m_server.set_message_handler(bind(&WebSocketSever::message, this, ::_1, ::_2));
		 m_server.set_reuse_addr(true);
	 }
 
 
	 WebSocketSever::~WebSocketSever(){
		 lock_guard<mutex> guard(m_action_lock);
		 std::cout << "Closing Server" << std::endl;
		 websocketpp::lib::error_code ec;
		 for (auto it = m_connections.begin(); it != m_connections.end(); ++it){
			 m_server.close(it->first, websocketpp::close::status::normal, "", ec);
			 if (ec){
				 std::cout << "> Error initiating client close: " << ec.message() << std::endl;
			 }
			 m_connections.erase(it->first);
		 }
		 m_server.stop();
	 }
 
 
	 void WebSocketSever::Init(int nPort/* = 9002*/){
		 std::thread th(bind(&WebSocketSever::process_messages, this));
		 th.detach();
		 this->run(nPort);
	 }
 
	 void WebSocketSever::run(uint16_t port){
		 //m_server.stop_listening();
		 // listen on specified port
		m_server.listen(websocketpp::lib::asio::ip::tcp::v4(), port);
		 // Start the server accept loop
		m_server.start_accept();
		 // Start the ASIO io_service run loop
		thread th(bind(&webserver::run, &m_server));
		th.detach();
		 //m_server.run();
	 }
 
	 void WebSocketSever::open(connection_hdl hdl){
		 {
			 lock_guard<mutex> guard(m_action_lock);
			 //std::cout << "on_open" << std::endl;
			 m_actions.push(action(SUBSCRIBE, hdl));
		 }
		 m_action_cond.notify_one();
	 }
 
	 void WebSocketSever::close(connection_hdl hdl){
		 {
			 lock_guard<mutex> guard(m_action_lock);
			 m_actions.push(action(UNSUBSCRIBE, hdl));
 
			 auto it = m_connections.find(hdl);
			 if (it != m_connections.end()){
				 int nId = it->second;
				 m_connections.erase(it);
				 if (0 == m_connections.size()){
					 m_nMaxCount == 0;
				 }
				 cJSON* cJson = cJSON_CreateObject();
				 cJSON_AddNumberToObject(cJson, "client_id", nId);
				 cJSON_AddStringToObject(cJson,"type", "connect_close");
				 g_RecvMsgCallback(g_pMsgHadle, cJSON_PrintUnformatted(cJson), nId);
				 cJSON_Delete(cJson);
			 }
		 }
		 m_action_cond.notify_one();
	 }
 
	 void WebSocketSever::send(std::string const & msg, int nId){
		 connection_hdl hdl;
		 {
			 lock_guard<mutex> guard(m_connection_lock);
			 for (auto it : m_connections)
			 {
				 if (it.second == nId)
				 {
					 hdl = it.first;
				 }
			 }
		 }
		 {
			 lock_guard<mutex> guard(m_action_lock);
			 m_actions.push(action(MESSAGE, nId, hdl, msg));
		 }
		 m_action_cond.notify_one();
	 }
 
	 void WebSocketSever::message(connection_hdl hdl, webserver::message_ptr msg){
		 int nId = 0;
		 {
			 lock_guard<mutex> guard(m_connection_lock);
			 for (auto it : m_connections){
				 if (it.first.lock().get() == hdl.lock().get())
				 {
					 nId = it.second;
					 break;
				 }
			 }
		 }
		 // queue message up for sending by processing thread
		 {
			   //lock_guard<mutex> guard(m_action_lock);
			   //m_actions.push(action(MESSAGE, nId, hdl, msg->get_payload() + "   response"));
			  g_RecvMsgCallback(g_pMsgHadle, msg->get_payload().c_str(), nId);
		 }
		 m_action_cond.notify_one();
	 }
 
	 void WebSocketSever::process_messages()
	 {
		 while (1){
			 unique_lock<mutex> lock(m_action_lock);
			 while (m_actions.empty()){
				 m_action_cond.wait(lock);
			 }
			 action a = m_actions.front();
			 m_actions.pop();
 
			 lock.unlock();
 
			 if (a.type == SUBSCRIBE) {
				 lock_guard<mutex> guard(m_connection_lock);
				 m_connections[a.hdl] = m_nMaxCount++;
			 }
			 else if (a.type == UNSUBSCRIBE){
				 lock_guard<mutex> guard(m_connection_lock);
				 auto it = m_connections.find(a.hdl);
				 if (it != m_connections.end()){
					 m_connections.erase(it);
				 }
				 if (0 == m_connections.size()){
					 m_nMaxCount == 0;
				 }
			 }
			 else if (a.type == MESSAGE){
				 lock_guard<mutex> guard(m_connection_lock);
				 websocketpp::lib::error_code ec;
				 m_server.send(a.hdl, a.msg, websocketpp::frame::opcode::text, ec);
				 if (ec){
					 std::cout << "> Error sending message: " << ec.message() << std::endl;
					 continue;
				 }
			 }
		 }
	 }
 
	 WebSocketClient::WebSocketClient() {
		 m_nNextId = 0;
		 m_nType = EN_WS_ClIENT;
		 m_endpoint.clear_access_channels(websocketpp::log::alevel::all);
		 m_endpoint.clear_error_channels(websocketpp::log::elevel::all);
 
		 m_endpoint.init_asio();
		 m_endpoint.start_perpetual();
 
		 m_thread.reset(new websocketpp::lib::thread(&webClient::run, &m_endpoint));
	 }
 
	 WebSocketClient::~WebSocketClient(){
		 m_endpoint.stop_perpetual();
		 for (con_list::const_iterator it = m_connection_list.begin(); it != m_connection_list.end(); ++it) {
			 if (it->second->get_status() != "Open") {
				 // Only close open connections
				 continue;
			 }
			 std::cout << "> Closing connection " << it->second->get_id() << std::endl;
			 websocketpp::lib::error_code ec;
			 m_endpoint.close(it->second->get_hdl(), websocketpp::close::status::going_away, "", ec);
			 if (ec){
				 std::cout << "> Error closing connection " << it->second->get_id() << ": "
					 << ec.message() << std::endl;
			 }
		 }
		 m_thread->join();
	 }
 
 
	 int WebSocketClient::connect(std::string const & uri, std::string client_type){
		 this->client_type = client_type;
		 websocketpp::lib::error_code ec;
		 webClient::connection_ptr con = m_endpoint.get_connection(uri, ec);
		 if (ec) {
			 std::cout << "> Connect initialization error: " << ec.message() << std::endl;
			 return -1;
		 }
		 int new_id = m_nNextId++;
		 conMetadata::ptr metadata_ptr(new conMetadata(new_id, con->get_handle(), uri));
		 std::cout << "url ----- " <<  uri << std::endl;
		 m_connection_list[new_id] = metadata_ptr;
 
		 con->set_open_handler(websocketpp::lib::bind(
			 &conMetadata::on_open,
			 metadata_ptr,
			 &m_endpoint,
			 websocketpp::lib::placeholders::_1
		 ));
		 con->set_fail_handler(websocketpp::lib::bind(
			 &conMetadata::on_fail,
			 metadata_ptr,
			 &m_endpoint,
			 websocketpp::lib::placeholders::_1
		 ));
		 con->set_close_handler(websocketpp::lib::bind(
			 &conMetadata::on_close,
			 metadata_ptr,
			 &m_endpoint,
			 websocketpp::lib::placeholders::_1
		 ));
		 con->set_message_handler(websocketpp::lib::bind(
			 &conMetadata::on_message,
			 metadata_ptr,
			 &m_endpoint,
			 websocketpp::lib::placeholders::_1,
			 websocketpp::lib::placeholders::_2
		 ));
		 //std::cout << "> Connect  begin " <<  std::endl;
		 m_endpoint.connect(con);
		 //m_endpoint.run();
		 //std::cout << "> Connect  end " <<  std::endl;
		 return new_id;
	 }
 
	 void WebSocketClient::close(int id,  std::string reason)
	 {
		 websocketpp::close::status::value code;
		 websocketpp::lib::error_code ec;
		 con_list::iterator metadata_it = m_connection_list.find(id);
		 if (metadata_it == m_connection_list.end()) {
			 std::cout << "> No connection found with id " << id << std::endl;
			 return;
		 }
		 m_endpoint.close(metadata_it->second->get_hdl(), code, reason, ec);
		 if (ec) {
			 std::cout << "> Error initiating close: " << ec.message() << std::endl;
		 }
	 }
 
	 void WebSocketClient::send(std::string const & msg, int nId){
		 websocketpp::lib::error_code ec;
		 if (m_connection_list[nId]){
			 m_endpoint.send(m_connection_list[nId]->get_hdl(), msg, websocketpp::frame::opcode::text, ec);
			 if (ec){
				 std::cout << "> Error sending message: " << ec.message() << std::endl;
				 std::string errorMessage = ec.message();
				 return;
			 }
		 }
	 }
 
	 conMetadata::ptr WebSocketClient::getMetadata(int id) const{
		 con_list::const_iterator metadata_it = m_connection_list.find(id);
		 if (metadata_it == m_connection_list.end()){
			 return conMetadata::ptr();
		 }else {
			 return metadata_it->second;
		 }
	 }
 
	 conMetadata::conMetadata(int id, websocketpp::connection_hdl hdl, std::string uri)
		 : m_id(id)
		 , m_hdl(hdl)
		 , m_status("Connecting")
		 , m_uri(uri)
		 , m_server("N/A"){
 
	 }
 
	 void conMetadata::on_open(webClient * c, websocketpp::connection_hdl hdl){
		 m_status = "Open";
		 webClient::connection_ptr con = c->get_con_from_hdl(hdl);
		 m_server = con->get_response_header("Server");
 
		 cJSON* cJson = cJSON_CreateObject();
		 cJSON_AddNumberToObject(cJson, "client_id", m_id);
		 cJSON_AddStringToObject(cJson,"type", "connect_success");
		 cJSON_AddStringToObject(cJson,"content", m_server.c_str());
		 g_RecvMsgCallback(g_pMsgHadle, cJSON_PrintUnformatted(cJson), m_id);
		 cJSON_Delete(cJson);
 
		 std::this_thread::sleep_for(std::chrono::milliseconds(20));
 
		 WebSocketClient* pClient = reinterpret_cast<WebSocketClient*>(g_pWebSocket);
		 if (nullptr == pClient){
			 return;
		 }
		 string clienTyp = pClient->client_type;
		 if(clienTyp.empty()){
			return;
		 }
		 cJSON* cSendJson = cJSON_CreateObject();
		 cJSON_AddStringToObject(cSendJson, "client_type", clienTyp.c_str());
		 cJSON_AddStringToObject(cSendJson,"action", "success");
		 pClient->send(cJSON_PrintUnformatted(cSendJson), m_id);
		 cJSON_Delete(cSendJson);

	 }
 
	 void conMetadata::on_fail(webClient * c, websocketpp::connection_hdl hdl){
		 m_status = "Failed";
		 webClient::connection_ptr con = c->get_con_from_hdl(hdl);
		 m_server = con->get_response_header("Server");
		 m_error_reason = con->get_ec().message();
 
		 cJSON* cJson = cJSON_CreateObject();
		 cJSON_AddNumberToObject(cJson, "client_id", m_id);
		 cJSON_AddStringToObject(cJson,"type", "connect_falied");
		 cJSON_AddStringToObject(cJson,"content", m_error_reason.c_str());
		 g_RecvMsgCallback(g_pMsgHadle, cJSON_PrintUnformatted(cJson), m_id);
		 cJSON_Delete(cJson);
 
	 }
 
	 void conMetadata::on_close(webClient * c, websocketpp::connection_hdl hdl)
	 {
		 m_status = "Closed";
		 webClient::connection_ptr con = c->get_con_from_hdl(hdl);
		 std::stringstream s;
		 s << "close code: " << con->get_remote_close_code() << " ("
			 << websocketpp::close::status::get_string(con->get_remote_close_code())
			 << "), close reason: " << con->get_remote_close_reason();
		 m_error_reason = s.str();
		 
		 cJSON* cJson = cJSON_CreateObject();
		 cJSON_AddNumberToObject(cJson, "client_id", m_id);
		 cJSON_AddStringToObject(cJson,"type", "connect_close");
		 cJSON_AddStringToObject(cJson,"content", m_error_reason.c_str());
		 g_RecvMsgCallback(g_pMsgHadle, cJSON_PrintUnformatted(cJson), m_id);
		 cJSON_Delete(cJson);
	 }
 
	 void conMetadata::on_message(webClient * c, websocketpp::connection_hdl hdl, webClient::message_ptr msg){
		 std::stringstream s;
		 s << "recv msg: " << msg->get_payload();
		 g_RecvMsgCallback(g_pMsgHadle, msg->get_payload().c_str(), m_id);
	 }
 
	 websocketpp::connection_hdl conMetadata::get_hdl() const {
		 return m_hdl;
	 }
 
	 int conMetadata::get_id() const {
		 return m_id;
	 }
 
	 std::string conMetadata::get_status() const {
		 return m_status;
	 }
 } 