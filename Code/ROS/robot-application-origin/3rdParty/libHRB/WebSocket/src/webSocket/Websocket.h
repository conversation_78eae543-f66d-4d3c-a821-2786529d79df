/*!
 * \file Websocket.cpp
 *
 * \fun webSocket 客户端/服务端类的实现
 * \author GYJ
 * \date 2022.3
 *
 */
 #ifndef _WEBSOCKET_H_
 #define _WEBSOCKET_H_
 
 #include <websocketpp/config/asio_no_tls.hpp>
 #include <websocketpp/server.hpp>
 #include <websocketpp/roles/server_endpoint.hpp>
 #include <websocketpp/config/asio_no_tls_client.hpp>
 #include <websocketpp/client.hpp>
 #include <websocketpp/common/memory.hpp>
 
 #include <cstdlib>
 #include <map>
 #include <string>
 #include <sstream>
 #include <set>
 #include <queue>

#include <mutex>
#include <condition_variable>
#include <thread>

 
 using namespace std;
 using namespace websocketpp;
 
 using websocketpp::connection_hdl;
 //using websocketpp::lib::mutex;
 //using websocketpp::lib::lock_guard;
 //using websocketpp::lib::unique_lock;
 //using websocketpp::lib::condition_variable;
 
 
 namespace WS
 {
	 /* on_open insert connection_hdl into channel
	  * on_close remove connection_hdl from channel
	  * on_message queue send to all channels
	  */
	 enum action_type
	 {
		 SUBSCRIBE,
		 UNSUBSCRIBE,
		 MESSAGE
	 };
 
	 struct action
	 {
		 action(action_type t, connection_hdl h) : type(t), hdl(h) {}
		 action(action_type t, int id, connection_hdl h, std::string m) :type(t), nId(id), hdl(h), msg(m) {}
 
		 action_type type;
		 websocketpp::connection_hdl hdl;
		 int nId;
		 std::string msg;
	 };
 
	 /****************************webSocket基类*************************************/
	 /*!
	  * \class WebSocket
	  *
	  * \brief
	  * \fun   webSocket基类
	  * \author GYJ
	  * \date 2022.3
	  */
	 class WebSocket
	 {
	 public:
		 WebSocket() {};
		 virtual void send(std::string const & msg, int nId = 0) = 0;
 
	 public:
		 int m_nType = 0;
	 };
 
	 /****************************webSocket服务端类*************************************/
 
	 typedef websocketpp::server<websocketpp::config::asio> webserver;
	 typedef std::map<connection_hdl, int, std::owner_less<connection_hdl>> con_map;
 
 
	 //发送内容<内容 客户端id>
	 typedef std::tuple<std::string, int> struSendMsg;
 
	 /*!
	  * \class WebSocketSever
	  *
	  * \brief webSocket服务端
	  * \fun
	  * \author GYJ
	  * \date 2022.3
	  */
	 class WebSocketSever : public WebSocket
	 {
	 public:
		 WebSocketSever();
		 ~WebSocketSever();
 
		 void send(std::string const & msg, int nId = 0);
 
	 public:
		 void Init(int nPort/* = 9002*/);
 
		 void run(uint16_t port);
		 void open(connection_hdl hdl);
		 void close(connection_hdl hdl);
		 void message(connection_hdl hdl, webserver::message_ptr msg);
		 void process_messages();
 
	 private:
		 webserver m_server;
		 std::queue<action> m_actions;
		 con_map m_connections;
		 std::mutex m_action_lock;
		 std::mutex m_connection_lock;
		 std::condition_variable m_action_cond;
		 int m_nMaxCount = 0;
	 };
 
 
	 /****************************webSocket客户端类*************************************/
 
	 typedef websocketpp::client<websocketpp::config::asio_client> webClient;
 
 
	 /*!
	  * \class conMetadata
	  *
	  * \brief
	  * \fun  
	  * \author GYJ
	  * \date 2022.3
	  */
	 class conMetadata
	 {
	 public:
		 typedef websocketpp::lib::shared_ptr<conMetadata> ptr;
		 conMetadata(int id, websocketpp::connection_hdl hdl, std::string uri);
		 void on_open(webClient * c, websocketpp::connection_hdl hdl);
		 void on_fail(webClient * c, websocketpp::connection_hdl hdl);
		 void on_close(webClient * c, websocketpp::connection_hdl hdl);
		 void on_message(webClient * c, websocketpp::connection_hdl hdl, webClient::message_ptr msg);
		 websocketpp::connection_hdl get_hdl() const;
		 int get_id() const;
		 std::string get_status() const;
 
	 private:
		 int m_id;
		 websocketpp::connection_hdl m_hdl;
		 std::string m_status;
		 std::string m_uri;
		 std::string m_server;
		 std::string m_error_reason;
	 };
 
	 /*!
	  * \class WebSocketClient
	  *
	  * \brief
	  * \fun  客户端类实现
	  * \author GYJ
	  * \date 2022.3
	  */
	 class WebSocketClient : public WebSocket
	 {
	 public:
		 WebSocketClient();
	 
		 ~WebSocketClient();
 
		 /*        
		  * \fun webSocket客户端连接
		  * \author GYJ
		  * \date 2022.3
		  * */
		 int connect(std::string const & url, std::string client_type);
		 
		 /*        
		  * \fun webSocket客户端关闭 
		  * \author GYJ
		  * \date 2022.3
		  * */
		 void close(int id, std::string reason);
 
		 /*        
		  * \fun 客户端发送消息
		  * \author GYJ
		  * \date 2022.3
		  * */
		 void send(std::string const & msg, int nId = 0);
		 
		 /*        
		  * \fun 
		  * \author GYJ
		  * \date 2022.3
		  * */
		 conMetadata::ptr getMetadata(int id) const;
		 
	 public:
		 std::string client_type = "";
 
	 private:
		 typedef std::map<int, conMetadata::ptr> con_list;
 
		 webClient m_endpoint;
		 websocketpp::lib::shared_ptr<websocketpp::lib::thread> m_thread;
 
		 con_list m_connection_list;
		 int m_nNextId = 0;
 
		 
	 };
 
 };
 
 #endif // !WEBSOCKET_H
 
 
 