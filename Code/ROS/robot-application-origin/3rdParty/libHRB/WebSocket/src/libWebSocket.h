/*!
 * \file libWebSocket.h
 * \fun
 *    webSocket库封装
 *    包括客户端/服务端使用类
 * \author GYJ
 * \date 03. 2022
 * 
 */
 #pragma once

 #include <string>
 using namespace std;
 
 #ifdef WIN_
 #define LIBWEBSOCKET_API __declspec(dllexport)
 #else
 #define LIBWEBSOCKET_API 
 #endif
 
 namespace WS
 {
	 //SOCKET类型
	 enum WS_MODULE_E
	 {
		 EN_WS_SEVER = 0,
		 EN_WS_ClIENT,
	 };
 
	 //接收的消息回调
	 typedef void(*MsgCallback)(void*, const char* msg, int nIndex);
 
	 /*
	 * @file:  WS_Init
	 * @briefs: webSocket初始化
	 */
	 LIBWEBSOCKET_API void WS_Init(WS_MODULE_E enType = EN_WS_SEVER, int nPort = 9002);
 
	 /*
	 * @file:  WS_UnInit
	 * @briefs: 
	 */
	 LIBWEBSOCKET_API void WS_UnInit();
 
	 /*
	 * @file:  WS_Connect
	 * @briefs:  作为客户端时使用：连接服务端  url为服务端连接，返回连接客户端ID, client_type可以作为客户端的唯一标识
	 */
	 LIBWEBSOCKET_API int WS_Connect(const char* url, std::string client_type = "");
 
	 /*
	 * @file:  IPC_SetMsgCallback
	 * @briefs: 设置接收的消息回调
	 */
	 LIBWEBSOCKET_API void WS_SetMsgCallback(MsgCallback pMsgCallback, void* pHandle = nullptr);
 
	 /*
	 * @file:  WS_Send
	 * @briefs: 发送消息
	 */
	 LIBWEBSOCKET_API void WS_Send(const char* message, int nInd = 0);
 
	 /*
	 * @file:  WS_Close
	 * @briefs: 关闭连接
	 */
	 LIBWEBSOCKET_API void WS_Close(int nInd, const char* reason);
 }
 