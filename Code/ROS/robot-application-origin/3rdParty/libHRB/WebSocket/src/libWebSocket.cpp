/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2025-03-05 17:07:19
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2025-05-08 17:34:05
 * @FilePath: \robot-application\3rdParty\libHRB\WebSocket\src\libWebSocket.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*!
 * \file libWebSocket.cpp
 * \fun
 *    webSocket库封装
 *    包括客户端/服务端使用类
 * \author GYJ
 * \date 2022.3
 *
 */
 #include "libWebSocket.h"
 #include "webSocket/Websocket.h"
 
 #include <websocketpp/common/thread.hpp>
 
 using websocketpp::lib::thread;
 
 namespace WS
 {
	 WebSocket* g_pWebSocket = nullptr;
 
	 //webSocket类型
	 WS_MODULE_E g_enType;
 
	 //消息回调函数句柄
	 void* g_pMsgHadle = nullptr;
 
	 //接受消息回调函数
	 MsgCallback g_RecvMsgCallback;
 
	 void WS_Init(WS_MODULE_E enType, int nPort/* = 9002*/){
		 if (nullptr != g_pWebSocket)
		 {
			 return;
		 }
		 if (EN_WS_ClIENT == enType)
		 {
			 g_pWebSocket = new WebSocketClient;
		 }
		 else if (EN_WS_SEVER == enType)
		 {
			 g_pWebSocket = new WebSocketSever;
			 WebSocketSever* pSever = reinterpret_cast<WebSocketSever*>(g_pWebSocket);
			 pSever->Init(nPort);
		 }
		 g_enType = enType;
	 }
 
	 void WS_UnInit(){
		 if (nullptr == g_pWebSocket){
			 return;
		 }
		 delete g_pWebSocket;
	 }
 
	 int WS_Connect(const char* url, std::string client_type){
		 if (EN_WS_ClIENT != g_enType)
		 {
			 return -1;
		 }
		 if (nullptr == g_pWebSocket)
		 {
			 return -1;
		 }
		 WebSocketClient* pClient = reinterpret_cast<WebSocketClient*>(g_pWebSocket);
		 int iRet = pClient->connect(url, client_type);
		 return iRet;
	 }
 
	 void WS_SetMsgCallback(MsgCallback pMsgCallback, void* pHandle){
		 g_RecvMsgCallback = pMsgCallback;
		 g_pMsgHadle = pHandle;
	 }
 
	 void WS_Close(int nInd, const char* reason){
		 if (nullptr == g_pWebSocket){
			 return;
		 }
		 if (EN_WS_ClIENT == g_enType){
			 WebSocketClient* pClient = reinterpret_cast<WebSocketClient*>(g_pWebSocket);
			 pClient->close(nInd, reason);
		 }
		 else if (EN_WS_SEVER == g_enType){
			 WebSocketSever* pSever = reinterpret_cast<WebSocketSever*>(g_pWebSocket);
			 //pSever->close(reason);
		 }
	 }
 
	 void WS_Send(const char* message, int nInd/* = 0*/){
		 if (nullptr == g_pWebSocket){
			 return;
		 }
		 g_pWebSocket->send(message, nInd);
	 }
 }	