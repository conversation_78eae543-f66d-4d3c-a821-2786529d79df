cmake_minimum_required(VERSION 3.10)
#项目名称
PROJECT(WebSocket)  

set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}")
#添加C++11支持及其他选项 
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}  -std=c++11 -g  -fPIC") 


# 明确使用动态库（DLL）
set(Boost_USE_STATIC_LIBS OFF)  

#获取当前路径的上层路径
set(PROJECT_INIT_PATH ${CMAKE_CURRENT_SOURCE_DIR})
message("DIR=" ${PROJECT_INIT_PATH})

set(BIN_DIR "${PROJECT_INIT_PATH}/bin")
set(LIB_DIR "${PROJECT_INIT_PATH}/lib")

#添加头文件
include_directories(
    ${PROJECT_INIT_PATH}/include
    ${PROJECT_INIT_PATH}/src
    ${PROJECT_INIT_PATH}/../include/cJSON
)

#将所有的源文件
file(GLOB DIR_LIB_SRCS
	"${CMAKE_CURRENT_SOURCE_DIR}/src/*"
	"${CMAKE_CURRENT_SOURCE_DIR}/src/webSocket/*"
    "${CMAKE_CURRENT_SOURCE_DIR}/../include/*"
	"${CMAKE_CURRENT_SOURCE_DIR}/../include/cJSON/*"
)

#include_directories(/usr/local/include)
#message("DIR_LIB_SRCS=" ${DIR_LIB_SRCS})

if (${CMAKE_HOST_WIN32})
    message("当前系统为 Windows 架构")
    # Windows 平台特定处理逻辑
    if(CMAKE_CXX_FLAGS MATCHES "-m32")
        set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${PROJECT_INIT_PATH}/lib/win/x86)
    else()
        set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${PROJECT_INIT_PATH}/lib/win/x64)
    endif()
elseif(${CMAKE_HOST_UNIX})
    # 关键定义
    add_definitions(
        -DBOOST_OVERRIDE=override
        -DBOOST_NOEXCEPT=noexcept
        -DBOOST_SYSTEM_NO_DEPRECATED
        -DBOOST_ERROR_CODE_HEADER_ONLY
    )

    message("当前系统为 Linux 架构")
    # Linux 平台特定处理逻辑
    if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "x86_64")
        message("当前系统为 x86-64/AMD64 架构")
        set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${PROJECT_INIT_PATH}/lib/linux/x86_64)
    elseif(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "aarch64")
        message("当前系统不支持 x86-64/AMD64 架构")
        set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${PROJECT_INIT_PATH}/lib/linux/aarch64)
    endif()
elseif (${CMAKE_SYSTEM} MATCHES "FreeBSD|OpenBSD")
    # FreeBSD 或 OpenBSD 平台特定处理逻辑
endif()

# 通过变量 SRC 生成 libWebSocket.so 共享库
add_library(${PROJECT_NAME} SHARED ${DIR_LIB_SRCS})

if(WIN32)
    message("LIB DIR = "${LIB_DIR})
    target_link_libraries(${PROJECT_NAME} PRIVATE
        ${LIB_DIR}/libboost_system-mgw112-mt-x64-1_71.dll.a
        ${LIB_DIR}/libboost_thread-mgw112-mt-x64-1_71.dll.a
        ws2_32     # Windows Sockets API
        advapi32   # Advanced API (注册表等)
        crypt32    # 加密API (可能需要)
        mswsock    # Microsoft Winsock 扩展
    )

    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy
            "${BIN_DIR}/libboost_thread-mgw112-mt-x64-1_71.dll"
            $<TARGET_FILE_DIR:${PROJECT_NAME}>
    )
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy
            "${BIN_DIR}/libboost_system-mgw112-mt-x64-1_71.dll"
            $<TARGET_FILE_DIR:${PROJECT_NAME}>
    )
endif()

#set(CMAKE_INSTALL_PREFIX /usr/local)
#安装共享库和头文件
#install(TARGETS WebSocket LIBRARY DESTINATION lib)
#install(FILES ${PROJECT_INIT_PATH}/src/libWebSocket.h)



