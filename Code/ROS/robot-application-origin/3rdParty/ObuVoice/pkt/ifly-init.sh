#!/bin/bash

#exit

PWD_BIN=/home/<USER>/bin
PWD_AUTOSTART=${PWD_BIN}/autoStart
#目录存在，讯飞环境；目录不存在，杭研环境（区别：路径不含autoStart目录）
if [ ! -d ${PWD_AUTOSTART} ]
then
	PWD_BIN=$(dirname $(readlink -f "$0"))
	PWD_AUTOSTART=${PWD_BIN}
fi

LOGFILE=${PWD_AUTOSTART}/ifly.log

echo "PWD_BIN: [${PWD_BIN}]" >> ${LOGFILE}
echo "PWD_AUTOSTART: [${PWD_AUTOSTART}]" >> ${LOGFILE}


#exit

cd ${PWD_AUTOSTART}
if [ -f "${PWD_AUTOSTART}/logToFile" ]
then
	${PWD_AUTOSTART}/ifly-start.sh start >> ${LOGFILE} 2>> ${LOGFILE} &
else
	${PWD_AUTOSTART}/ifly-start.sh start &
fi
cd -
