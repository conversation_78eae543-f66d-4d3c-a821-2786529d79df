[auth]
appid=xxxxxx
abroad_auth_file_path=./Vtn_obu/resource/miniesr/vtn_auth.txt
#obu_url=http://zhjt_dev.ihou.com:8089/iptv-auth-platform/engine/auth
#obu_url=http://************:8089/iptv-auth-platform/engine/auth
#obu_key = ZFI0QUgwS202TnNtdGxtZQ==
#obu_iv = WWpHUWtTSVVMUDNadkk2VA==

[cae]
log_level = 1
#是否开启降噪功能, 0为不开启，其他为开启，默认开启
cae_enable = 1
beam = 1
#原始音频采样点大小
input_audio_unit=2
#debug = true

[caeEngine]

# 麦间距, 单位mm
mic_dist_mm = 35
# 参考信号路数
ref_num = 2
#debug = true

aes_model=./Vtn_obu/resource/miniesr/mlp_aes_1024_tv.bin
partition_model=./Vtn_obu/resource/miniesr/mlp_pt6_pudu_20220126.bin
#partition_model_rec=../resource/miniesr/mlp_div_Rim6_dir20_tomic_front_coh_8bit.bin
partition_model_rec=./Vtn_obu/resource/miniesr/mlp_sp_cldnn_1024.bin

[ivw]
#save_wakeup_audio = 1
#是否开启唤醒功能, 0为不开启，其他为开启
ivw_enable = 1

#唤醒资源
mlp_res=./Vtn_obu/resource/miniesr/res_vs_8-qvalue.bin
filler_keywords_res=./Vtn_obu/resource/miniesr/filler_keywords.bin

#在命令词模式下多久后切入主唤醒词模式
switch_interval = 60

#自定义唤醒词开关，1为开启，0关闭
gener_keyword_enable = 1
#gram资源路径
gram_path=./Vtn_obu/resource/miniesr/gram_monoPhone_178_gb18030.bin
#gram_path = ./Vtn_obu/resource/miniesr/gram_CN_LFR_3003.bin
#content路径
content_res=./Vtn_obu/resource/miniesr/test_keyword.txt

main_wakeup_branch_value = 1
mlc_wakeup_branch_value = 1

