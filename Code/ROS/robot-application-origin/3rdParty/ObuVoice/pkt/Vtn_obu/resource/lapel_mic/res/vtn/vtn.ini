
[vtn]
;  default is 1. LEVEL: 0=off, 1=verbose, 2=debug, 3=info, 4=warning, 5=error
log_level = 4

;  can be empty, default is "./"
;  will be overrided on "vtn_api_init" if you provide it.
; work_dir = ./

;  default is 0. which means log nothing. 
;  if non-zero, we will log to ${log_audio_dir}.
;  1:origin, 2:frontend_internal, 4:frontend_out; example: 7 = (1 | 2 | 4), 6 = (2 | 4)
; log_audio = 6

;  can be empty, default is "${work_dir}/bin/output"
; log_audio_dir = ./bin/output/

;  can be empty, default input audio sample_rate is 16k
; sample_rate = 16000

;  support short/int/float, default is short
; input_type = int

;  queue size can be empty, use default 
; queue_size_chief = 1
; queue_size_assistant = 1

;   whether support output wakeup segment audio. default is 1.
; output_wakeup_segment = 0

;  identifier of resource. if empty, you must provide each resource path! this config maybe override in your code.
;    lookup backend res bin(mlp/filler/keyword) on "${work_dir}/res/${BACKEND_DIR_NAME}/${res_identifier}/"
;    lookup frontend td.res which match "${work_dir}/res/${FRONTEND_DIR_NAME}/*_td_${td_type}_${res_identifier}_*.bin"
res_identifier = xfxf


[auth]
; via = net
;   your appid, you can apply it from aiui website.
; appid = abcd1234
;   license file path. default is empty, which means we check auth through network.
; license = ./res/vtn/device_license.dat


[frontend]
;  default is 1.
; on = 1

;  instance number. default is 1
; inst_num = 2

;  can be empty, default cfg is on "${work_dir}/res/${FRONTEND_DIR_NAME}/cae_init_param.ini" 
; cfg = ./res/cae_line4mic_std/cae_init_param.ini

; sample_rate = 16000
; work_mode = 0
; array_type = 0
;   rec audio use sp_vad solution: 0 off, 1 on.
; sp_vad_on = 0
; mic_num = 2
ref_num = 2
;  millimeters of mic distance.
; mic_dist_mm = 35
; oneshot_enable = 1
; aec_on = 1
; aec_coef = 
; gevd_on = 0
; aes_on = 1
; aes_model = ./res/cae_line4mic_std/mlp_aes_1024_tv_xtxt_denoise.bin
; aes_model = ./res/cae_line4mic_std/mlp_aes_1024_tv.bin
; partition_on = 1
; partition_model = ./res/cae_line4mic_std/mlp_partition_4mic_5beam_512.bin
; partition_model_rec = ./res/cae_line4mic_std/mlp_lstm_sp_20201016.bin
; select_on = 0
; select_model = ./res/cae_line4mic_std/mlp_select_6to3_1024.bin
; td_on = 1
; td_type = cldnn
; td_delay = 5
; td_mvdr = 1
; td_dynamic = 1
; td_model = ./res/cae_line4mic_std/mlp_td_cldnn_arm_hxxj_20231013_d5d0.bin
; td_type = fsmn
; td_mvdr = 0
; td_model = ./res/cae_line4mic_std/mlp_td_fsmn_hxxj.bin
; td_type = cldnn
; td_delay = 5
; td_mvdr = 0
; td_dynamic = 0
; td_model = ./res/cae_line4mic_std/mlp_td_cldnn_arm_xtxt_20240407_d5d0.bin
; nr_on = 0
; nr_model = 
; pf_model = 
; bff_model =
;   doa: 0 off, 1 on. default we follow frontend feature.
; doa_on = 0
;   model doa, only valid for "doa_on = 1". default we follow frontend feature.
; doa_nn_on = 0
; doa_model =
;   ffw: 0 off, 1 on. default we follow frontend feature. 
; ffw_on = 0
; ffw_model =


[backend]
;  default is 1.
; on = 0

;  how many inst. default is 3 for 2/4/6mic.
;  for double 4mic, it should be 6.
;  only valid for ivw, miniesr use default 1.
; inst_num = 3

;  support 1 or 2. only valid for ivw.
;  default is 1. (always run all channel instance). 
;  2 means we only run instance that in your wakeup beam.  
; rewake_mode = 2

;  strategy: 0: power; 1: power/score. default is 0
; pk_strategy = 1

;  can be empty, we pick default cfg on "${work_dir}/res/${BACKEND_DIR_NAME}/ivw_g.cfg"
; cfg = ./res/ivw_3.17.12/ivw_g.cfg

;  closed loop mode(also called logpcm). default is 0: off.
; closed_loop_mode = 1

;  can be empty. if you provide res_identifier, 
;  we will lookup all resource under "${work_dir}/res/${BACKEND_DIR_NAME}/${res_identifier}/"
; mlp = ./res/ivw_3.17.12/xtxt/mlp.bin
; filler = ./res/ivw_3.17.12/xtxt/filler.bin
; keyword = ./res/ivw_3.17.12/xtxt/keyword.bin
; command = 
;  if you set "grammar = null", we won't load it.
; grammar = ./res/ivw_3.17.12/grammar.bin
; grammar = null
;  for config miniesr res.
; mlp = ./res/miniesr_1ch/xfxf/mlp.bin
; keyword = ./res/miniesr_1ch/xfxf/keyword.bin
; command = ./res/miniesr_1ch/xfxf/command.bin
;  if you set "custom_keyword = null", we won't load it.
; custom_keyword = null


[word_evaluator]
;  if you set "model = null", we won't load it.
; model = ./res/wake_word_evaluate/wakeup_word_score.bin

