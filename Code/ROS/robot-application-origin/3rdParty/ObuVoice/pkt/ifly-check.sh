#!/bin/bash

#if self is running , exit
shell_file_name=$0
ret_grep=`ps -ef | grep -v grep | grep ${shell_file_name} | wc -l`
echo "ifly-check: ${shell_file_name}: grep self result: [${ret_grep}]"
if [ ${ret_grep} -gt 2 ]
then
	echo "$ifly-check: {shell_file_name} shell is running, exit"
	exit
else
	echo "ifly-check: ${shell_file_name} shell not running, so run"
fi

sleep 16

PWD_BIN=/home/<USER>/bin
PWD_AUTOSTART=${PWD_BIN}/autoStart
#目录存在，讯飞环境；目录不存在，杭研环境（区别：路径不含autoStart目录）
if [ ! -d ${PWD_AUTOSTART} ]
then
	PWD_BIN=$(dirname $(readlink -f "$0"))
	PWD_AUTOSTART=${PWD_BIN}
fi

echo "PWD_BIN: [${PWD_BIN}]"
echo "PWD_AUTOSTART: [${PWD_AUTOSTART}]"

LOGFILE=${PWD_AUTOSTART}/ifly.log

count_vtn=0
count_robot=0

while true;
do
		#检查进程是否存在，不存在则计数+1，存在则清空计数
		for procname in vtn_obu_node message_obu_robot
		do
			date=`date`
			num=`ps -ef | grep -c $procname`
			if [ $num -ne 2 ]
			then
			   if [ $procname == 'vtn_obu_node' ]
			   then
				   count_vtn=$(( $count_vtn + 1 ))
				   echo "ifly-check: [$date]: vtn_obu_node dead checked times [$count_vtn]"
			   elif [ $procname == 'message_obu_robot' ]
			   then
				   count_robot=$(( $count_robot + 1 ))
				   echo "ifly-check: [$date]: message_obu_robot dead checked times [$count_robot]"
			   fi
			else
				if [ $procname == 'vtn_obu_node' ]
				then
					count_vtn=0
				elif [ $procname == 'message_obu_robot' ]
				then
					count_robot=0
				fi
			fi
		done


		if [ $count_vtn -ge 3 ];
		then
			count_vtn=0
			echo "ifly-check: [$date][RESTART]: restart [vtn_obu_node]"
			echo "ifly-check: [$date][RESTART]: [${PWD_AUTOSTART}/Vtn_obu/bin/vtn_obu_node ${PWD_AUTOSTART}/Vtn_obu/mmvee.cfg >> ${LOGFILE} 2>> ${LOGFILE} &]"
			if [ -f "${PWD_AUTOSTART}/logToFile" ]
			then
				${PWD_AUTOSTART}/Vtn_obu/bin/vtn_obu_node ${PWD_AUTOSTART}/Vtn_obu/mmvee.cfg >> ${LOGFILE} 2>> ${LOGFILE} &
			else
				${PWD_AUTOSTART}/Vtn_obu/bin/vtn_obu_node ${PWD_AUTOSTART}/Vtn_obu/mmvee.cfg &
			fi
		fi

		if [ $count_robot -ge 3 ];
		then
			count_robot=0
			echo "ifly-check: [$date][RESTART]: restart [message_obu_robot]"
			echo "ifly-check: [$date][RESTART]: [${PWD_AUTOSTART}/Vtn_obu/bin/message_obu_robot >> ${LOGFILE} 2>> ${LOGFILE} &]"
			if [ -f "${PWD_AUTOSTART}/logToFile" ]
			then
				${PWD_AUTOSTART}/Vtn_obu/bin/message_obu_robot >> ${LOGFILE} 2>> ${LOGFILE} &
			else
				${PWD_AUTOSTART}/Vtn_obu/bin/message_obu_robot &
			fi
		fi
	sleep 2
done
