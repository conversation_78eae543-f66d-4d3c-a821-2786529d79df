#!/bin/bash

echo "ifly-start: =============== begin"

AUTOSTART_SHELL_VERSION="1.0.1"

PROCESS_VTN=vtn_obu_node
PROCESS_ROBOT=message_obu_robot

PWD_BIN=/home/<USER>/bin
PWD_AUTOSTART=${PWD_BIN}/autoStart
#目录存在，讯飞环境；目录不存在，杭研环境（区别：路径不含autoStart目录）
if [ ! -d ${PWD_AUTOSTART} ]
then
	PWD_BIN=$(dirname $(readlink -f "$0"))
	PWD_AUTOSTART=${PWD_BIN}
fi

#echo "PWD_BIN: [${PWD_BIN}]"
#echo "PWD_AUTOSTART: [${PWD_AUTOSTART}]"

LOGFILE=${PWD_AUTOSTART}/ifly.log

echo "ifly-start: version is [${AUTOSTART_SHELL_VERSION}]" >> ${LOGFILE} 2>> ${LOGFILE}

cd ${PWD_AUTOSTART}
export LD_LIBRARY_PATH=${PWD_AUTOSTART}/Vtn_obu/lib:$LD_LIBRARY_PATH
echo $LD_LIBRARY_PATH >> ${LOGFILE} 2>> ${LOGFILE}

#if [ -f ${LOGFILE} ]
#then
#	sudo rm -rf ${LOGFILE}
#fi

function kill_process()
{
	pid=$(pgrep -f $1)
	echo "ifly-start: kill_process: $1 pid [$pid]" >> ${LOGFILE} 2>> ${LOGFILE}
	if [ ${#pid} -gt 0 ]
	then
		echo "ifly-start: kill_process: kill $1($pid)" >> ${LOGFILE} 2>> ${LOGFILE}
		kill -9 $pid
	else
		echo "ifly-start: kill_process: no need to kill $1 not exist" >> ${LOGFILE} 2>> ${LOGFILE}
	fi
}

function start_vtn()
{
	echo "ifly-start: start_vtn: =============== begin" >> ${LOGFILE} 2>> ${LOGFILE}

	echo "ifly-start: start_vtn: ${PWD_AUTOSTART}/Vtn_obu/bin/${PROCESS_VTN} ${PWD_AUTOSTART}/Vtn_obu/mmvee.cfg &" >> ${LOGFILE} 2>> ${LOGFILE}
	echo "${PWD_AUTOSTART}/Vtn_obu/bin/${PROCESS_VTN} ${PWD_AUTOSTART}/Vtn_obu/mmvee.cfg &"
	${PWD_AUTOSTART}/Vtn_obu/bin/${PROCESS_VTN} ${PWD_AUTOSTART}/Vtn_obu/mmvee.cfg >> ${LOGFILE} 2>> ${LOGFILE} &
	#${PWD_AUTOSTART}/Vtn_obu/bin/${PROCESS_VTN} ${PWD_AUTOSTART}/Vtn_obu/mmvee.cfg  &

	echo "ifly-start: start_vtn: =============== end" >> ${LOGFILE} 2>> ${LOGFILE}
}

function start_rebot()
{
	echo "ifly-start: start_rebot: =============== begin" >> ${LOGFILE} 2>> ${LOGFILE}

	echo "ifly-start: start_rebot: ${PWD_AUTOSTART}/Vtn_obu/bin/${PROCESS_ROBOT} &" >> ${LOGFILE} 2>> ${LOGFILE}
	${PWD_AUTOSTART}/Vtn_obu/bin/${PROCESS_ROBOT} >> ${LOGFILE} 2>> ${LOGFILE} &

	echo "ifly-start: start_rebot: =============== end" >> ${LOGFILE} 2>> ${LOGFILE}
}

#debug: run demo to receive data to check whether project process works well
PROCESS_DEMO=demo_client
LOGDEMO=${PWD_AUTOSTART}/demo.log
#LOGDEMO=/tmp/demo.log
function start_demo()
{
	echo "ifly-start: start_demo: =============== begin" >> ${LOGFILE} 2>> ${LOGFILE}

	echo "ifly-start: start_demo: ${PWD_AUTOSTART}/${PROCESS_DEMO} > ${LOGDEMO} &" >> ${LOGFILE} 2>> ${LOGFILE}

	if [ -f ${PWD_AUTOSTART}/${PROCESS_DEMO} ]
	then
		chmod +x ${PWD_AUTOSTART}/${PROCESS_DEMO}
		${PWD_AUTOSTART}/${PROCESS_DEMO} >> ${LOGDEMO} 2>> ${LOGDEMO} &
		#${PWD_AUTOSTART}/${PROCESS_DEMO} > ${LOGDEMO} &
	else
		echo "ifly-start: start_demo: no ${PWD_AUTOSTART}/${PROCESS_DEMO}" >> ${LOGFILE} 2>> ${LOGFILE}
	fi

	echo "ifly-start: start_demo: =============== end" >> ${LOGFILE} 2>> ${LOGFILE}
}

function start_check()
{
	echo "ifly-start: ${PWD_AUTOSTART}/ifly-check.sh &" >> ${LOGFILE} 2>> ${LOGFILE}
	if [ -f "${PWD_AUTOSTART}/logToFile" ]
	then
		${PWD_AUTOSTART}/ifly-check.sh >> ${LOGFILE} 2>> ${LOGFILE} &
	else
		${PWD_AUTOSTART}/ifly-check.sh &
	fi
}

function work_stop()
{
	echo "ifly-start: work_stop: will kill ifly-check.sh" >> ${LOGFILE} 2>> ${LOGFILE}
	kill_process "ifly-check.sh"

	echo "ifly-start: work_stop: will kill ${PROCESS_ROBOT}" >> ${LOGFILE} 2>> ${LOGFILE}
	kill_process "${PROCESS_ROBOT}"

	echo "ifly-start: work_stop: will kill ${PROCESS_VTN}" >> ${LOGFILE} 2>> ${LOGFILE}
	kill_process "${PROCESS_VTN}"

	echo "ifly-start: work_stop: will kill ${PROCESS_DEMO}" >> ${LOGFILE} 2>> ${LOGFILE}
	kill_process "${PROCESS_DEMO}"
}

function work_start()
{
	sleep 8
	work_stop
	sleep 1
	start_vtn
	sleep 1
	start_rebot
	start_check
}

function work_all()
{
	sleep 8
	work_stop
	sleep 1
	start_vtn
	sleep 1
	start_rebot
	sleep 1
	start_demo
	start_check
}

opcode=$1
#echo "opcode: [$opcode]"
if [ "${opcode}"s == "all"s ]
then
	work_all
elif [ "${opcode}"s == "start"s ]
then
	work_start
elif [ "${opcode}"s == "vtn"s ]
then
	#sleep 3 #等待3秒系统释放端口号
	fuser -k 8080/tcp #强制释放端口号8080
	echo "ifly-start: match [vtn], call start_vtn"
	start_vtn
elif [ "${opcode}"s == "robot"s ]
then
	#sleep 3 #等待3秒系统释放端口号
	fuser -k 9090/tcp #强制释放端口号9090
	start_rebot
elif [ "${opcode}"s == "demo"s ]
then
	start_demo
elif [ "${opcode}"s == "stop"s ]
then
	work_stop
else
	echo "ifly-start: unknown opcode: use like: source ${BASH_SOURCE} all/start/stop/vtn/robot" >> ${LOGFILE} 2>> ${LOGFILE}
fi

echo "ifly-start: =============== end"
cd -
