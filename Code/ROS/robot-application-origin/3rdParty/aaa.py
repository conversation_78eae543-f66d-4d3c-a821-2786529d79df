import socket
import struct
import threading
from pynput.keyboard import Key, Listener


class UdpCommand:

    def __init__(self, local_port=20001, ctrl_ip="*************", ctrl_port=43893):
        self.local_port = local_port
        self.server = socket.socket(socket.AF_INET, socket.SOCK_DGRAM, 0)
        # print('socket successfully created')
        self.server.settimeout(5)
        self.ip = ctrl_ip
        self.ctrl_addr = (ctrl_ip, ctrl_port)
        self.comm_lock = threading.Lock()

    def __exit__(self):
        self.server.close()

    def sendSimpleData(self, code, value=0):
        data = struct.pack("<1I2i", code, value, 0)
        self.comm_lock.acquire()
        if self.server:
            try:
                self.server.sendto(data, self.ctrl_addr)
            except socket.error as ex:
                print(ex)
        self.comm_lock.release()

    # def send_uint_data(self, code, value=0):
    #     data = struct.pack("<3I", code, value, 0)
    #     self.comm_lock.acquire()
    #     if self.server:
    #         try:
    #             self.server.sendto(data, self.ctrl_addr)
    #         except socket.error as ex:
    #             print(ex)
    #     self.comm_lock.release()

    def sendPosData(self, code=51, x=0.0, y=0.0, yaw=0.0):
        self.sendMultiDataWrapper(code, '3d', x=x, y=y, theta=yaw)

    def sendPostureCmd(self, code=0x31010B07, x=0, y=0, z=0, roll=0, pitch=0, yaw=0, rate=1):
        self.sendMultiDataWrapper(code, '7i', roll=roll, pitch=pitch, yaw=yaw, x=x, y=y, z=z, rate=rate)

    def sendPostureCmdNew(self, code=0x21010B07, x=0, y=0, z=0, roll=0, pitch=0, yaw=0):
        self.sendMultiDataWrapper(code, '6i', x=x, y=y, z=z, roll=roll, pitch=pitch, yaw=yaw)

    def sendTargetPos(self, code=0x31010D07, type=0, x=0, y=0, theta=0):
        self.sendMultiDataWrapper(code, '1i3f', t=type, x=x, y=y, theta=theta)

    def sendAimPos(self, code=60, x=0, y=0, z=0, theta=0):
        self.sendMultiDataWrapper(code, '4i', x=x, y=y, theta=theta, z=z)

    def sendMultiDataWrapper(self, code, *args, **kwargs):
        str = args[0]
        str_len = len(str)
        total_size = 0
        total_num = 0
        for i in range(0, str_len, 2):
            num, tp = int(str[i]), str[i+1]
            total_size = total_size + num * self.getFmtSize(tp)
            total_num = total_num + num
        if total_num != len(kwargs):
            print('data length is not right')
            return
        tup = (code, total_size, 1) + tuple(kwargs.values())
        data = struct.pack('<3I'+str, *tup)
        self.comm_lock.acquire()
        if self.server:
            try:
                self.server.sendto(data, self.ctrl_addr)
            except socket.error as ex:
                print(ex)
        self.comm_lock.release()

    def getFmtSize(self, l):
        if l.lower() == 'i':
            return 4
        elif l.lower() == 'f':
            return 4
        elif l.lower() == 'd':
            return 8
        return 0




if __name__ == '__main__':
        u = UdpCommand()

        def on_press(key):
            try:
                if key.char == 'q':#越障+RL
                    u.sendSimpleData(code=0x21010529)
                elif key.char == 'w':#平地+RL
                    u.sendSimpleData(code=0x2101052a)
                elif key.char == 'f':#导航模式
                    u.sendSimpleData(code=0x21010c03)
                elif key.char == 'g':#手柄模式
                    u.sendSimpleData(code=0x21010c02)
                elif key == Key.esc:
                    # 按下Esc键时停止监听
                    return False
                else:
                    print(key)
            except AttributeError:
                pass

        def on_release(key):
            if key == Key.esc:
                # 按下Esc键时停止监听
                return False

        with Listener(on_press=on_press, on_release=on_release) as listener:
            listener.join()
#
