#!/bin/bash

# RK3588交叉编译环境验证脚本
# 检查所有必要的工具和配置是否正确

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# 检查基本工具
check_basic_tools() {
    print_header "检查基本工具"
    
    local tools=("cmake" "make" "gcc" "g++")
    local all_ok=true
    
    for tool in "${tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            local version=$($tool --version | head -1)
            print_success "$tool: $version"
        else
            print_error "$tool 未安装"
            all_ok=false
        fi
    done
    
    if [ "$all_ok" = true ]; then
        print_success "基本工具检查通过"
    else
        print_error "基本工具检查失败"
        return 1
    fi
}

# 检查交叉编译工具链
check_cross_toolchain() {
    print_header "检查交叉编译工具链"
    
    local cross_tools=("aarch64-linux-gnu-gcc" "aarch64-linux-gnu-g++" "aarch64-linux-gnu-ar" "aarch64-linux-gnu-strip")
    local all_ok=true
    
    for tool in "${cross_tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            local version=$($tool --version | head -1)
            print_success "$tool: $version"
        else
            print_error "$tool 未安装"
            all_ok=false
        fi
    done
    
    if [ "$all_ok" = false ]; then
        print_error "交叉编译工具链检查失败"
        print_info "请安装: sudo apt-get install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu"
        return 1
    fi
    
    # 测试交叉编译器
    print_info "测试交叉编译器..."
    local test_file="/tmp/cross_test.c"
    cat > "$test_file" << 'EOF'
#include <stdio.h>
int main() {
    printf("Cross compile test\n");
    return 0;
}
EOF
    
    if aarch64-linux-gnu-gcc "$test_file" -o "/tmp/cross_test" 2>/dev/null; then
        print_success "交叉编译器测试通过"
        rm -f "$test_file" "/tmp/cross_test"
    else
        print_error "交叉编译器测试失败"
        rm -f "$test_file" "/tmp/cross_test"
        return 1
    fi
    
    print_success "交叉编译工具链检查通过"
}

# 检查项目文件
check_project_files() {
    print_header "检查项目文件"
    
    local required_files=(
        "CMakeLists.txt"
        "rk3588-toolchain.cmake"
        "build_rk3588.sh"
        "deploy_to_rk3588.sh"
        "Makefile"
        "factory_write.c"
        "factory_read.c"
        "factory_update.c"
        "vendor.h"
    )
    
    local all_ok=true
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "找到: $file"
        else
            print_error "缺失: $file"
            all_ok=false
        fi
    done
    
    # 检查脚本权限
    local scripts=("build_rk3588.sh" "deploy_to_rk3588.sh" "test_factory_write.sh")
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            if [ -x "$script" ]; then
                print_success "$script 有执行权限"
            else
                print_warning "$script 没有执行权限，正在添加..."
                chmod +x "$script"
                print_success "$script 执行权限已添加"
            fi
        fi
    done
    
    if [ "$all_ok" = true ]; then
        print_success "项目文件检查通过"
    else
        print_error "项目文件检查失败"
        return 1
    fi
}

# 检查CMake配置
check_cmake_config() {
    print_header "检查CMake配置"
    
    print_info "验证CMake工具链文件..."
    if [ -f "rk3588-toolchain.cmake" ]; then
        # 检查工具链文件内容
        if grep -q "CMAKE_SYSTEM_NAME.*Linux" "rk3588-toolchain.cmake" && \
           grep -q "CMAKE_SYSTEM_PROCESSOR.*aarch64" "rk3588-toolchain.cmake" && \
           grep -q "aarch64-linux-gnu-gcc" "rk3588-toolchain.cmake"; then
            print_success "工具链文件配置正确"
        else
            print_error "工具链文件配置有误"
            return 1
        fi
    else
        print_error "工具链文件不存在"
        return 1
    fi
    
    print_info "测试CMake配置..."
    local test_dir="/tmp/cmake_test_$$"
    mkdir -p "$test_dir"
    
    if cmake -DCMAKE_TOOLCHAIN_FILE="$(pwd)/rk3588-toolchain.cmake" -B "$test_dir" -S . >/dev/null 2>&1; then
        print_success "CMake配置测试通过"
        rm -rf "$test_dir"
    else
        print_error "CMake配置测试失败"
        rm -rf "$test_dir"
        return 1
    fi
    
    print_success "CMake配置检查通过"
}

# 运行快速构建测试
run_build_test() {
    print_header "运行构建测试"
    
    print_info "执行快速构建测试..."
    
    # 创建临时构建目录
    local build_dir="build_test_$$"
    mkdir -p "$build_dir"
    
    # 配置
    if cmake -DCMAKE_TOOLCHAIN_FILE="$(pwd)/rk3588-toolchain.cmake" \
             -DCMAKE_BUILD_TYPE=Release \
             -B "$build_dir" -S . >/dev/null 2>&1; then
        print_success "CMake配置成功"
    else
        print_error "CMake配置失败"
        rm -rf "$build_dir"
        return 1
    fi
    
    # 编译
    if make -C "$build_dir" -j2 >/dev/null 2>&1; then
        print_success "编译成功"
        
        # 检查生成的文件
        local executables=("factory_read" "factory_write" "factory_update")
        for exe in "${executables[@]}"; do
            if [ -f "$build_dir/$exe" ]; then
                # 检查文件架构
                local arch=$(file "$build_dir/$exe" | grep -o "aarch64\|ARM aarch64" || echo "unknown")
                if [[ "$arch" == *"aarch64"* ]]; then
                    print_success "$exe: 正确的aarch64架构"
                else
                    print_warning "$exe: 架构检查异常 ($arch)"
                fi
            else
                print_error "$exe: 未生成"
            fi
        done
    else
        print_error "编译失败"
        rm -rf "$build_dir"
        return 1
    fi
    
    # 清理
    rm -rf "$build_dir"
    print_success "构建测试通过"
}

# 显示总结
show_summary() {
    print_header "验证总结"
    
    print_info "环境验证完成！"
    print_info ""
    print_info "下一步操作:"
    print_info "1. 交叉编译: make rk3588 或 ./build_rk3588.sh"
    print_info "2. 本地编译: make native"
    print_info "3. 部署到设备: ./deploy_to_rk3588.sh --host <RK3588_IP>"
    print_info "4. 查看帮助: make help"
    print_info ""
    print_info "文档位置:"
    print_info "- README_factory_write.md (程序使用说明)"
    print_info "- README_RK3588_CrossCompile.md (交叉编译指南)"
}

# 主函数
main() {
    print_header "RK3588交叉编译环境验证"
    echo ""
    
    local failed=0
    
    check_basic_tools || failed=$((failed + 1))
    echo ""
    
    check_cross_toolchain || failed=$((failed + 1))
    echo ""
    
    check_project_files || failed=$((failed + 1))
    echo ""
    
    check_cmake_config || failed=$((failed + 1))
    echo ""
    
    run_build_test || failed=$((failed + 1))
    echo ""
    
    if [ $failed -eq 0 ]; then
        print_success "所有检查通过！环境配置正确。"
        show_summary
        exit 0
    else
        print_error "有 $failed 项检查失败，请修复后重试。"
        exit 1
    fi
}

# 执行主函数
main "$@"
