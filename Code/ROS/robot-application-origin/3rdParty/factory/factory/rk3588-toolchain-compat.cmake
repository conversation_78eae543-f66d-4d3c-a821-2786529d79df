# RK3588 兼容性交叉编译工具链配置文件
# 使用 GCC ARM 10.3-2021.07 工具链，用于解决GLIBC版本兼容性问题的动态链接方案

# 设置目标系统信息
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR aarch64)

# 设置工具链路径
set(TOOLCHAIN_ROOT /opt/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu)
set(TOOLCHAIN_BIN ${TOOLCHAIN_ROOT}/bin)
set(CROSS_COMPILE_PREFIX aarch64-none-linux-gnu-)

# 指定编译器
set(CMAKE_C_COMPILER ${TOOLCHAIN_BIN}/${CROSS_COMPILE_PREFIX}gcc)
set(CMAKE_CXX_COMPILER ${TOOLCHAIN_BIN}/${CROSS_COMPILE_PREFIX}g++)
set(CMAKE_ASM_COMPILER ${TOOLCHAIN_BIN}/${CROSS_COMPILE_PREFIX}gcc)

# 指定其他工具
set(CMAKE_AR ${TOOLCHAIN_BIN}/${CROSS_COMPILE_PREFIX}ar)
set(CMAKE_STRIP ${TOOLCHAIN_BIN}/${CROSS_COMPILE_PREFIX}strip)
set(CMAKE_NM ${TOOLCHAIN_BIN}/${CROSS_COMPILE_PREFIX}nm)
set(CMAKE_OBJCOPY ${TOOLCHAIN_BIN}/${CROSS_COMPILE_PREFIX}objcopy)
set(CMAKE_OBJDUMP ${TOOLCHAIN_BIN}/${CROSS_COMPILE_PREFIX}objdump)
set(CMAKE_RANLIB ${TOOLCHAIN_BIN}/${CROSS_COMPILE_PREFIX}ranlib)

# 编译器标志 - 添加兼容性设置
set(CMAKE_C_FLAGS_INIT "-march=armv8-a -mtune=cortex-a76.cortex-a55 -D_GNU_SOURCE")
set(CMAKE_CXX_FLAGS_INIT "-march=armv8-a -mtune=cortex-a76.cortex-a55 -D_GNU_SOURCE")

# 优化标志
set(CMAKE_C_FLAGS_RELEASE "-O2 -DNDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "-O2 -DNDEBUG")
set(CMAKE_C_FLAGS_DEBUG "-O0 -g")
set(CMAKE_CXX_FLAGS_DEBUG "-O0 -g")

# 链接器标志 - 兼容性设置
# 使用较老的符号版本，避免新GLIBC依赖
set(CMAKE_EXE_LINKER_FLAGS_INIT "-Wl,--hash-style=gnu -Wl,--as-needed")
set(CMAKE_SHARED_LINKER_FLAGS_INIT "-Wl,--hash-style=gnu -Wl,--as-needed")

# 设置sysroot路径
set(CMAKE_SYSROOT ${TOOLCHAIN_ROOT}/aarch64-none-linux-gnu/libc)
set(CMAKE_FIND_ROOT_PATH ${CMAKE_SYSROOT})

# 搜索路径配置
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)

# 设置默认构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 打印交叉编译信息
message(STATUS "Cross compiling for RK3588 platform (COMPATIBILITY MODE)")
message(STATUS "Using GCC ARM 10.3-2021.07 toolchain")
message(STATUS "Toolchain root: ${TOOLCHAIN_ROOT}")
message(STATUS "Target system: ${CMAKE_SYSTEM_NAME}")
message(STATUS "Target processor: ${CMAKE_SYSTEM_PROCESSOR}")
message(STATUS "C Compiler: ${CMAKE_C_COMPILER}")
message(STATUS "CXX Compiler: ${CMAKE_CXX_COMPILER}")
message(STATUS "Sysroot: ${CMAKE_SYSROOT}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Linking mode: DYNAMIC with compatibility flags")
