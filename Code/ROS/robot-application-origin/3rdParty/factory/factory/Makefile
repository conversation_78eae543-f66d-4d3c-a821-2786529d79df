# Factory项目Makefile
# 支持本地编译和RK3588交叉编译

.PHONY: all clean rk3588 native debug help install test

# 默认目标
all: rk3588

# RK3588交叉编译
rk3588:
	@echo "=== 开始RK3588交叉编译 ==="
	@chmod +x build_rk3588.sh
	@./build_rk3588.sh

# RK3588交叉编译 (Debug模式)
rk3588-debug:
	@echo "=== 开始RK3588交叉编译 (Debug模式) ==="
	@chmod +x build_rk3588.sh
	@./build_rk3588.sh debug

# RK3588交叉编译 (静态链接 - 解决GLIBC版本问题)
rk3588-static:
	@echo "=== 开始RK3588交叉编译 (静态链接模式) ==="
	@chmod +x build_rk3588.sh
	@./build_rk3588.sh static

# RK3588交叉编译 (兼容性模式)
rk3588-compat:
	@echo "=== 开始RK3588交叉编译 (兼容性模式) ==="
	@chmod +x build_rk3588.sh
	@./build_rk3588.sh compat

# 本地编译
native:
	@echo "=== 开始本地编译 ==="
	@mkdir -p build_native
	@cd build_native && \
		cmake -DCMAKE_BUILD_TYPE=Release .. && \
		make -j$$(nproc) && \
		make install
	@echo "=== 本地编译完成 ==="

# 本地编译 (Debug模式)
native-debug:
	@echo "=== 开始本地编译 (Debug模式) ==="
	@mkdir -p build_native
	@cd build_native && \
		cmake -DCMAKE_BUILD_TYPE=Debug .. && \
		make -j$$(nproc) && \
		make install
	@echo "=== 本地编译完成 ==="

# 清理所有构建文件
clean:
	@echo "=== 清理构建文件 ==="
	@rm -rf build_rk3588* build_native install
	@echo "=== 清理完成 ==="

# 清理并重新编译RK3588
clean-rk3588: clean rk3588

# 清理并重新编译本地
clean-native: clean native

# 测试 (需要先编译)
test:
	@echo "=== 运行测试 ==="
	@if [ -f "./install/Linux-aarch64/factory_write" ]; then \
		echo "找到RK3588版本，但无法在本机运行"; \
	elif [ -f "./install/native/factory_write" ]; then \
		echo "运行本地版本测试..."; \
		chmod +x install/native/test_factory_write.sh; \
		cd install/native && ./test_factory_write.sh; \
	else \
		echo "错误: 未找到可执行文件，请先编译"; \
		exit 1; \
	fi

# 安装到系统路径 (仅本地版本)
install-system:
	@echo "=== 安装到系统路径 ==="
	@if [ -f "./install/native/factory_write" ]; then \
		sudo cp install/native/factory_* /usr/local/bin/; \
		echo "已安装到 /usr/local/bin/"; \
	else \
		echo "错误: 请先执行 'make native' 编译本地版本"; \
		exit 1; \
	fi

# 检查工具链
check-toolchain:
	@echo "=== 检查交叉编译工具链 ==="
	@if command -v aarch64-linux-gnu-gcc >/dev/null 2>&1; then \
		echo "✓ aarch64-linux-gnu-gcc 已安装"; \
		aarch64-linux-gnu-gcc --version | head -1; \
	else \
		echo "✗ aarch64-linux-gnu-gcc 未安装"; \
		echo "请安装: sudo apt-get install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu"; \
	fi
	@if command -v cmake >/dev/null 2>&1; then \
		echo "✓ cmake 已安装"; \
		cmake --version | head -1; \
	else \
		echo "✗ cmake 未安装"; \
		echo "请安装: sudo apt-get install cmake"; \
	fi

# 显示构建信息
info:
	@echo "=== 构建信息 ==="
	@echo "项目名称: factory"
	@echo "支持平台: 本地 (native) 和 RK3588 交叉编译"
	@echo "构建目录:"
	@echo "  - build_native/  (本地编译)"
	@echo "  - build_rk3588/  (RK3588交叉编译)"
	@echo "安装目录:"
	@echo "  - install/native/        (本地版本)"
	@echo "  - install/Linux-aarch64/ (RK3588版本)"
	@echo ""
	@echo "可执行文件:"
	@echo "  - factory_read   (读取factory数据)"
	@echo "  - factory_write  (写入factory数据)"
	@echo "  - factory_update (更新factory数据)"

# 帮助信息
help:
	@echo "Factory项目构建系统"
	@echo ""
	@echo "可用目标:"
	@echo "  all              默认目标，等同于 rk3588"
	@echo "  rk3588           RK3588交叉编译 (Release模式)"
	@echo "  rk3588-debug     RK3588交叉编译 (Debug模式)"
	@echo "  rk3588-static    RK3588交叉编译 (静态链接，解决GLIBC问题)"
	@echo "  rk3588-compat    RK3588交叉编译 (兼容性模式)"
	@echo "  native           本地编译 (Release模式)"
	@echo "  native-debug     本地编译 (Debug模式)"
	@echo "  clean            清理所有构建文件"
	@echo "  clean-rk3588     清理并重新编译RK3588版本"
	@echo "  clean-native     清理并重新编译本地版本"
	@echo "  test             运行测试"
	@echo "  install-system   安装本地版本到系统路径"
	@echo "  check-toolchain  检查编译工具链"
	@echo "  info             显示构建信息"
	@echo "  help             显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make rk3588      # RK3588交叉编译"
	@echo "  make native      # 本地编译"
	@echo "  make clean       # 清理构建文件"
	@echo "  make test        # 运行测试"
