#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>

// 验证MAC地址格式
int validate_mac_address(const char *mac)
{
    if (strlen(mac) != 17) return 0;

    for (int i = 0; i < 17; i++) {
        if (i % 3 == 2) {
            if (mac[i] != ':') return 0;
        } else {
            if (!((mac[i] >= '0' && mac[i] <= '9') ||
                  (mac[i] >= 'A' && mac[i] <= 'F') ||
                  (mac[i] >= 'a' && mac[i] <= 'f'))) {
                return 0;
            }
        }
    }
    return 1;
}

// 将MAC地址字符串转换为6字节二进制数据
int parse_mac_address(const char *mac_str, uint8_t *mac_bytes)
{
    if (!mac_str || !mac_bytes) {
        return -1;
    }
    
    if (!validate_mac_address(mac_str)) {
        return -1;
    }
    
    // 解析MAC地址字符串 "6A:9B:B6:D9:E5:31" -> {0x6A, 0x9B, 0xB6, 0xD9, 0xE5, 0x31}
    for (int i = 0; i < 6; i++) {
        char hex_str[3];
        hex_str[0] = mac_str[i * 3];
        hex_str[1] = mac_str[i * 3 + 1];
        hex_str[2] = '\0';
        
        char *endptr;
        unsigned long val = strtoul(hex_str, &endptr, 16);
        if (*endptr != '\0' || val > 0xFF) {
            return -1;
        }
        mac_bytes[i] = (uint8_t)val;
    }
    
    return 0;
}

int main(int argc, char *argv[])
{
    printf("MAC地址转换测试程序\n");
    printf("===================\n\n");
    
    // 测试用例
    const char *test_macs[] = {
        "6A:9B:B6:D9:E5:31",
        "AA:BB:CC:DD:EE:FF",
        "00:11:22:33:44:55",
        "2C:C3:E6:E3:5B:72",
        "invalid:mac:addr",  // 无效格式
        "6A:9B:B6:D9:E5",    // 长度不对
        "GG:HH:II:JJ:KK:LL"  // 无效字符
    };
    
    int num_tests = sizeof(test_macs) / sizeof(test_macs[0]);
    
    for (int i = 0; i < num_tests; i++) {
        printf("测试 %d: %s\n", i + 1, test_macs[i]);
        
        uint8_t mac_bytes[6];
        int result = parse_mac_address(test_macs[i], mac_bytes);
        
        if (result == 0) {
            printf("  ✓ 转换成功: ");
            for (int j = 0; j < 6; j++) {
                printf("0x%02X", mac_bytes[j]);
                if (j < 5) printf(" ");
            }
            printf("\n");
        } else {
            printf("  ✗ 转换失败: 无效的MAC地址格式\n");
        }
        printf("\n");
    }
    
    // 如果提供了命令行参数，测试用户输入的MAC地址
    if (argc > 1) {
        printf("用户输入测试: %s\n", argv[1]);
        uint8_t mac_bytes[6];
        int result = parse_mac_address(argv[1], mac_bytes);
        
        if (result == 0) {
            printf("  ✓ 转换成功: ");
            for (int j = 0; j < 6; j++) {
                printf("0x%02X", mac_bytes[j]);
                if (j < 5) printf(" ");
            }
            printf("\n");
            
            // 显示二进制表示
            printf("  二进制数据: ");
            for (int j = 0; j < 6; j++) {
                printf("%02X", mac_bytes[j]);
            }
            printf("\n");
        } else {
            printf("  ✗ 转换失败: 无效的MAC地址格式\n");
        }
    }
    
    printf("\n使用方法: %s [MAC地址]\n", argv[0]);
    printf("示例: %s 6A:9B:B6:D9:E5:31\n", argv[0]);
    
    return 0;
}
