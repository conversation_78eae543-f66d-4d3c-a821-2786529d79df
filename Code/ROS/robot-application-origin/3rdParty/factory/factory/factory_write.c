#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <string.h>
#include <errno.h>
#include "vendor.h"

// 打印使用说明
void print_usage(const char *program_name)
{
    printf("Usage: %s <sn> <cmei> <mac> <ovd_login>\n", program_name);
    printf("Parameters:\n");
    printf("  sn        - Device Serial Number (VENDOR_CMCC_SN_ID)\n");
    printf("  cmei      - Device CMEI (VENDOR_CMCC_CMEI_ID)\n");
    printf("  mac       - MAC Address (VENDOR_CMCC_MAC_ID)\n");
    printf("  ovd_login - OVD Login Password (VENDOR_CMCC_OVD_LOGIN_ID)\n");
    printf("Example:\n");
    printf("  %s 1830004229212345670000072 212345670000072 6A:9B:B6:D9:E5:31 1EIpLNAn\n", program_name);
}

// 验证MAC地址格式
int validate_mac_address(const char *mac)
{
    if (strlen(mac) != 17) return 0;

    for (int i = 0; i < 17; i++) {
        if (i % 3 == 2) {
            if (mac[i] != ':') return 0;
        } else {
            if (!((mac[i] >= '0' && mac[i] <= '9') ||
                  (mac[i] >= 'A' && mac[i] <= 'F') ||
                  (mac[i] >= 'a' && mac[i] <= 'f'))) {
                return 0;
            }
        }
    }
    return 1;
}

// 将MAC地址字符串转换为6字节二进制数据
int parse_mac_address(const char *mac_str, uint8_t *mac_bytes)
{
    if (!mac_str || !mac_bytes) {
        return -1;
    }

    if (!validate_mac_address(mac_str)) {
        return -1;
    }

    // 解析MAC地址字符串 "6A:9B:B6:D9:E5:31" -> {0x6A, 0x9B, 0xB6, 0xD9, 0xE5, 0x31}
    for (int i = 0; i < 6; i++) {
        char hex_str[3];
        hex_str[0] = mac_str[i * 3];
        hex_str[1] = mac_str[i * 3 + 1];
        hex_str[2] = '\0';

        char *endptr;
        unsigned long val = strtoul(hex_str, &endptr, 16);
        if (*endptr != '\0' || val > 0xFF) {
            return -1;
        }
        mac_bytes[i] = (uint8_t)val;
    }

    return 0;
}

// 使用传入数据写入vendor storage (支持MAC地址二进制格式)
int vendor_storage_write_with_data(int sys_fd, int check_id, const char *data)
{
    uint32_t len              = 0;
    int ret                   = 0;
    uint8_t p_buf[2048]       = {0};
    struct rk_vendor_req *req = NULL;

    if (!data) {
        printf("Error: data is NULL for ID 0x%02x\n", check_id);
        return -1;
    }

    memset(p_buf, 0, sizeof(p_buf));
    req      = (struct rk_vendor_req *)p_buf;
    req->tag = VENDOR_REQ_TAG;
    req->id  = check_id;

    // 特殊处理MAC地址 - 写入6字节二进制数据
    if (check_id == VENDOR_CMCC_MAC_ID) {
        uint8_t mac_bytes[6];
        if (parse_mac_address(data, mac_bytes) != 0) {
            printf("Error: Failed to parse MAC address: %s\n", data);
            return -1;
        }

        // 将6字节MAC地址复制到请求数据中
        memcpy(req->data, mac_bytes, 6);
        len = 6;
        req->len = len;

        printf("MAC address parsed: %s -> ", data);
        for (int i = 0; i < 6; i++) {
            printf("0x%02X", mac_bytes[i]);
            if (i < 5) printf(" ");
        }
        printf("\n");
    } else {
        // 其他数据类型使用字符串格式
        len = snprintf((char *)req->data, 128, "%s", data);
        req->len = len;
    }

    ret = ioctl(sys_fd, VENDOR_WRITE_IO, req);
    if(-1 == ret)
    {
        printf("vendor_write ID: 0x%02x failed, error: %s\n", req->id, strerror(errno));
        return -1;
    }

    // 打印写入结果
    if (check_id == VENDOR_CMCC_MAC_ID) {
        printf("vendor_write: ID: 0x%02x, MAC data (6 bytes): ", req->id);
        for (int i = 0; i < 6; i++) {
            printf("0x%02X", req->data[i]);
            if (i < 5) printf(" ");
        }
        printf("\n");
    } else {
        printf("vendor_write: ID: 0x%02x, data: \n%d:%s\n", req->id, req->len, req->data);
    }

    return 0;
}

// 保留原有的测试函数以兼容性
int vendor_storage_write_test(int sys_fd, int check_id)
{
    uint32_t i                = 0;
    uint32_t len              = 0;
    int ret                   = 0;
    uint8_t p_buf[2048]       = {0};
    struct rk_vendor_req *req = NULL;

    memset(p_buf, 0, sizeof(p_buf));
    req      = (struct rk_vendor_req *)p_buf;
    req->tag = VENDOR_REQ_TAG;
    req->id  = check_id;
    switch(check_id)
    {
        case VENDOR_CMCC_TYPE_ID:
            len = snprintf((char *)req->data, 128, "%s", "2320647");
            break;
        case VENDOR_CMCC_SN_ID:
            len = snprintf((char *)req->data, 128, "%s", "1830004229212345670000072");
            break;
        case VENDOR_CMCC_CMEI_ID:
            len = snprintf((char *)req->data, 128, "%s", "212345670000072");
            break;
        case VENDOR_CMCC_MAC_ID:
            len = snprintf((char *)req->data, 128, "%s", "6A:9B:B6:D9:E5:31");
            break;
        case VENDOR_CMCC_OVD_MEDIA_ID:
            len = snprintf((char *)req->data, 128, "%s", "1EIpLNAn");
            break;
        case VENDOR_CMCC_OVD_LOGIN_ID:
            len = snprintf((char *)req->data, 128, "%s", "1EIpLNAn");
            break;
        case VENDOR_CMCC_APP_KEY:
            len = snprintf((char *)req->data, 128, "%s", "sqp9ci1qrptp1orl");
            break;
        case VENDOR_CMCC_APP_SECRET:
            len = snprintf((char *)req->data, 128, "%s", "2ezmzb9v27fkztj7");
            break;
        default:
            break;
    }
    req->len = len;
    // printf("len: %d, data: %s\n", len, (char *)req->data);
    // return 0;
    ret = ioctl(sys_fd, VENDOR_WRITE_IO, req);
    if(-1 == ret)
    {
        printf("vendor_write ID: 0x%02x failed, error: %s\n", req->id, strerror(errno));
        return -1;
    }
    printf("vendor_write: ID: 0x%02x, data: \n%d:%s\n", req->id, req->len, req->data);
    return 0;
}

int main(int argc, char *argv[])
{
    // 检查参数数量
    if (argc != 5) {
        printf("Error: Expected 4 parameters, got %d\n", argc - 1);
        print_usage(argv[0]);
        return -1;
    }

    // 获取命令行参数
    const char *sn = argv[1];
    const char *cmei = argv[2];
    const char *mac = argv[3];
    const char *ovd_login = argv[4];

    // 验证参数
    if (strlen(sn) == 0 || strlen(cmei) == 0 || strlen(mac) == 0 || strlen(ovd_login) == 0) {
        printf("Error: All parameters must be non-empty\n");
        print_usage(argv[0]);
        return -1;
    }

    // 验证MAC地址格式
    if (!validate_mac_address(mac)) {
        printf("Error: Invalid MAC address format. Expected format: XX:XX:XX:XX:XX:XX\n");
        return -1;
    }

    printf("Writing factory data with parameters:\n");
    printf("  SN: %s\n", sn);
    printf("  CMEI: %s\n", cmei);
    printf("  MAC: %s\n", mac);
    printf("  OVD Login: %s\n", ovd_login);
    printf("\n");

    // 打开vendor storage设备
    int sys_fd = open(VENDOR_STORAGE, O_RDWR, 0);
    if(sys_fd < 0)
    {
        printf("vendor_storage open failed, error: %s\n", strerror(errno));
        return -1;
    }

    // 写入4个参数
    int ret = 0;

    // 写入SN
    if (vendor_storage_write_with_data(sys_fd, VENDOR_CMCC_SN_ID, sn) != 0) {
        ret = -1;
    }

    // 写入CMEI
    if (vendor_storage_write_with_data(sys_fd, VENDOR_CMCC_CMEI_ID, cmei) != 0) {
        ret = -1;
    }

    // 写入MAC
    if (vendor_storage_write_with_data(sys_fd, VENDOR_CMCC_MAC_ID, mac) != 0) {
        ret = -1;
    }

    // 写入OVD Login
    if (vendor_storage_write_with_data(sys_fd, VENDOR_CMCC_OVD_LOGIN_ID, ovd_login) != 0) {
        ret = -1;
    }

    // 关闭文件描述符
    if(sys_fd >= 0)
    {
        close(sys_fd);
        sys_fd = -1;
    }

    if (ret == 0) {
        printf("\nAll factory data written successfully!\n");
    } else {
        printf("\nSome factory data write operations failed!\n");
    }

    return ret;
}
