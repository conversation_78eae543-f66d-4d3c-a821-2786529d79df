#!/bin/bash

# RK3588交叉编译构建脚本
# 使用 GCC ARM 10.3-2021.07 工具链

set -e  # 遇到错误立即退出

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="factory"
BUILD_DIR="${SCRIPT_DIR}/build_rk3588"
TOOLCHAIN_FILE="${SCRIPT_DIR}/rk3588-toolchain.cmake"
BUILD_TYPE="Release"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查工具链
check_toolchain() {
    print_info "检查GCC ARM 10.3交叉编译工具链..."
    
    local gcc_path="/opt/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc"
    
    if [ ! -f "$gcc_path" ]; then
        print_error "GCC ARM 10.3工具链未找到: $gcc_path"
        exit 1
    fi
    
    print_success "交叉编译工具链检查通过"
    $gcc_path --version | head -1
}

# 主函数
main() {
    print_info "=== RK3588交叉编译构建脚本 (GCC ARM 10.3) ==="
    
    # 处理参数
    case "$1" in
        "static")
            TOOLCHAIN_FILE="${SCRIPT_DIR}/rk3588-toolchain-static.cmake"
            BUILD_DIR="${SCRIPT_DIR}/build_rk3588_static"
            print_info "使用静态链接模式"
            ;;
        "compat")
            TOOLCHAIN_FILE="${SCRIPT_DIR}/rk3588-toolchain-compat.cmake"
            BUILD_DIR="${SCRIPT_DIR}/build_rk3588_compat"
            print_info "使用兼容性模式"
            ;;
        "debug")
            BUILD_TYPE="Debug"
            print_info "使用Debug模式"
            ;;
    esac
    
    # 执行构建
    check_toolchain
    
    print_info "创建构建目录: ${BUILD_DIR}"
    mkdir -p "${BUILD_DIR}"
    
    print_info "配置CMake..."
    cd "${BUILD_DIR}"
    cmake -DCMAKE_TOOLCHAIN_FILE="${TOOLCHAIN_FILE}" -DCMAKE_BUILD_TYPE="${BUILD_TYPE}" "${SCRIPT_DIR}"
    
    print_info "开始编译..."
    make -j$(nproc)
    
    print_info "安装项目..."
    make install
    
    print_success "=== 构建完成 ==="
    print_info "可执行文件位置: ${SCRIPT_DIR}/install/Linux-aarch64/"
}

main "$@"
