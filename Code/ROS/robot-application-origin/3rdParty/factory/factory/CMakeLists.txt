cmake_minimum_required(VERSION 3.16)
project(factory VERSION 1.0.0 LANGUAGES C CXX)

# 设置C/C++标准
set(CMAKE_C_STANDARD 99)
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置默认构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 打印构建信息
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C Compiler: ${CMAKE_C_COMPILER}")
message(STATUS "CXX Compiler: ${CMAKE_CXX_COMPILER}")
if(CMAKE_CROSSCOMPILING)
    message(STATUS "Cross compiling for: ${CMAKE_SYSTEM_NAME}-${CMAKE_SYSTEM_PROCESSOR}")
endif()

set(SRC_FILES
  src/Common/SerialPort.cpp
  src/Common/UdpCommunicator.cpp
  # src/Common/MessageHandler.cpp
  # src/Common/deep_cmd.h
  src/SensorModule/floodlightcontrol/FloodLightControl.cpp
  src/SensorModule/ledlightcontrol/LightControl.cpp
  src/SensorModule/neckcontrol/NeckControl.cpp
  src/SensorModule/touch/GpioSensor.cpp
  src/SensorModule/ultrasonic/Ultrasonic.cpp
  src/main.cpp
)

add_executable(factory_update
  ${CMAKE_CURRENT_SOURCE_DIR}/factory_update.c
)
target_include_directories(factory_update PRIVATE
  ${CMAKE_CURRENT_SOURCE_DIR}
)

add_executable(factory_read
  ${CMAKE_CURRENT_SOURCE_DIR}/factory_read.c
)
target_include_directories(factory_read PRIVATE
  ${CMAKE_CURRENT_SOURCE_DIR}
)

add_executable(factory_write
  ${CMAKE_CURRENT_SOURCE_DIR}/factory_write.c
)
target_include_directories(factory_write PRIVATE
  ${CMAKE_CURRENT_SOURCE_DIR}
)

# MAC地址转换测试程序
add_executable(test_mac_conversion
  ${CMAKE_CURRENT_SOURCE_DIR}/test_mac_conversion.c
)
target_include_directories(test_mac_conversion PRIVATE
  ${CMAKE_CURRENT_SOURCE_DIR}
)

# 设置安装路径
if(CMAKE_CROSSCOMPILING)
    set(INSTALL_DIR ${CMAKE_CURRENT_SOURCE_DIR}/install/${CMAKE_SYSTEM_NAME}-${CMAKE_SYSTEM_PROCESSOR})
else()
    set(INSTALL_DIR ${CMAKE_CURRENT_SOURCE_DIR}/install/native)
endif()

install(TARGETS
  factory_update
  factory_read
  factory_write
  test_mac_conversion
  DESTINATION ${INSTALL_DIR}
)

# 安装脚本和文档
install(FILES
  test_factory_write.sh
  README_factory_write.md
  DESTINATION ${INSTALL_DIR}
  PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
)
