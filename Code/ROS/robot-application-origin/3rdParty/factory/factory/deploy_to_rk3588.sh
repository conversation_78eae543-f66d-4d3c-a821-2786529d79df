#!/bin/bash

# RK3588部署脚本
# 将编译好的factory程序部署到RK3588设备

set -e

# 配置变量 (请根据实际情况修改)
RK3588_HOST="${RK3588_HOST:-*************}"  # RK3588设备IP地址
RK3588_USER="${RK3588_USER:-root}"           # 用户名
RK3588_PORT="${RK3588_PORT:-22}"             # SSH端口
REMOTE_PATH="${REMOTE_PATH:-/usr/local/bin}" # 远程安装路径

# 本地路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOCAL_INSTALL_DIR="${SCRIPT_DIR}/install/Linux-aarch64"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "RK3588部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "环境变量:"
    echo "  RK3588_HOST    RK3588设备IP地址 (默认: *************)"
    echo "  RK3588_USER    SSH用户名 (默认: root)"
    echo "  RK3588_PORT    SSH端口 (默认: 22)"
    echo "  REMOTE_PATH    远程安装路径 (默认: /usr/local/bin)"
    echo ""
    echo "选项:"
    echo "  --host IP      设置RK3588设备IP地址"
    echo "  --user USER    设置SSH用户名"
    echo "  --port PORT    设置SSH端口"
    echo "  --path PATH    设置远程安装路径"
    echo "  --test         部署后运行测试"
    echo "  --help         显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认配置部署"
    echo "  $0 --host ************* --test       # 指定IP并运行测试"
    echo "  RK3588_HOST=************* $0         # 使用环境变量"
}

# 检查本地文件
check_local_files() {
    print_info "检查本地编译文件..."
    
    if [ ! -d "${LOCAL_INSTALL_DIR}" ]; then
        print_error "未找到RK3588编译输出目录: ${LOCAL_INSTALL_DIR}"
        print_info "请先运行: make rk3588"
        exit 1
    fi
    
    local files_found=0
    for file in factory_read factory_write factory_update; do
        if [ -f "${LOCAL_INSTALL_DIR}/${file}" ]; then
            print_success "找到: ${file}"
            files_found=$((files_found + 1))
        else
            print_warning "未找到: ${file}"
        fi
    done
    
    if [ $files_found -eq 0 ]; then
        print_error "未找到任何可执行文件"
        exit 1
    fi
    
    print_success "本地文件检查完成 (找到 ${files_found} 个文件)"
}

# 检查网络连接
check_connection() {
    print_info "检查与RK3588设备的连接..."
    print_info "目标: ${RK3588_USER}@${RK3588_HOST}:${RK3588_PORT}"
    
    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes -p "${RK3588_PORT}" "${RK3588_USER}@${RK3588_HOST}" "echo 'Connection test successful'" 2>/dev/null; then
        print_error "无法连接到RK3588设备"
        print_info "请检查:"
        print_info "  1. 设备IP地址是否正确: ${RK3588_HOST}"
        print_info "  2. SSH服务是否运行"
        print_info "  3. 网络连接是否正常"
        print_info "  4. SSH密钥是否配置正确"
        exit 1
    fi
    
    print_success "连接测试成功"
}

# 创建远程目录
create_remote_dir() {
    print_info "创建远程目录: ${REMOTE_PATH}"
    
    ssh -p "${RK3588_PORT}" "${RK3588_USER}@${RK3588_HOST}" "mkdir -p ${REMOTE_PATH}"
    
    if [ $? -eq 0 ]; then
        print_success "远程目录创建成功"
    else
        print_error "远程目录创建失败"
        exit 1
    fi
}

# 部署文件
deploy_files() {
    print_info "开始部署文件..."
    
    # 部署可执行文件
    for file in factory_read factory_write factory_update; do
        if [ -f "${LOCAL_INSTALL_DIR}/${file}" ]; then
            print_info "部署: ${file}"
            scp -P "${RK3588_PORT}" "${LOCAL_INSTALL_DIR}/${file}" "${RK3588_USER}@${RK3588_HOST}:${REMOTE_PATH}/"
            
            # 设置执行权限
            ssh -p "${RK3588_PORT}" "${RK3588_USER}@${RK3588_HOST}" "chmod +x ${REMOTE_PATH}/${file}"
            
            print_success "${file} 部署完成"
        fi
    done
    
    # 部署脚本和文档 (如果存在)
    for file in test_factory_write.sh README_factory_write.md; do
        if [ -f "${LOCAL_INSTALL_DIR}/${file}" ]; then
            print_info "部署: ${file}"
            scp -P "${RK3588_PORT}" "${LOCAL_INSTALL_DIR}/${file}" "${RK3588_USER}@${RK3588_HOST}:${REMOTE_PATH}/"
            
            if [[ "${file}" == *.sh ]]; then
                ssh -p "${RK3588_PORT}" "${RK3588_USER}@${RK3588_HOST}" "chmod +x ${REMOTE_PATH}/${file}"
            fi
            
            print_success "${file} 部署完成"
        fi
    done
}

# 验证部署
verify_deployment() {
    print_info "验证部署结果..."
    
    ssh -p "${RK3588_PORT}" "${RK3588_USER}@${RK3588_HOST}" "ls -la ${REMOTE_PATH}/factory_*"
    
    print_success "部署验证完成"
}

# 检查GLIBC兼容性
check_glibc_compatibility() {
    print_info "检查GLIBC兼容性..."

    # 获取目标设备的GLIBC版本
    local remote_glibc=$(ssh -p "${RK3588_PORT}" "${RK3588_USER}@${RK3588_HOST}" "ldd --version 2>/dev/null | head -1" || echo "unknown")
    print_info "目标设备GLIBC版本: $remote_glibc"

    # 检查可执行文件的GLIBC依赖
    if [ -f "${LOCAL_INSTALL_DIR}/factory_write" ]; then
        local glibc_deps=$(aarch64-linux-gnu-objdump -T "${LOCAL_INSTALL_DIR}/factory_write" 2>/dev/null | grep "GLIBC_" | sort -u || echo "")
        if [ -n "$glibc_deps" ]; then
            print_info "程序GLIBC依赖:"
            echo "$glibc_deps" | while read line; do
                print_info "  $line"
            done
        else
            print_success "程序使用静态链接，无GLIBC依赖"
        fi
    fi
}

# 运行远程测试
run_remote_test() {
    print_info "在RK3588设备上运行测试..."

    # 首先测试基本可执行性
    print_info "测试程序可执行性..."
    if ssh -p "${RK3588_PORT}" "${RK3588_USER}@${RK3588_HOST}" "${REMOTE_PATH}/factory_write --help" 2>/dev/null; then
        print_success "程序可以正常运行"

        # 检查是否有测试脚本
        if ssh -p "${RK3588_PORT}" "${RK3588_USER}@${RK3588_HOST}" "[ -f ${REMOTE_PATH}/test_factory_write.sh ]"; then
            print_info "运行测试脚本..."
            ssh -p "${RK3588_PORT}" "${RK3588_USER}@${RK3588_HOST}" "cd ${REMOTE_PATH} && ./test_factory_write.sh"
        fi
    else
        print_error "程序无法运行，可能是GLIBC兼容性问题"
        print_info "建议使用静态链接重新编译: make rk3588-static"
        print_info "详细信息请参考: GLIBC_COMPATIBILITY_GUIDE.md"
        return 1
    fi

    print_success "远程测试完成"
}

# 主函数
main() {
    local run_test=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --host)
                RK3588_HOST="$2"
                shift 2
                ;;
            --user)
                RK3588_USER="$2"
                shift 2
                ;;
            --port)
                RK3588_PORT="$2"
                shift 2
                ;;
            --path)
                REMOTE_PATH="$2"
                shift 2
                ;;
            --test)
                run_test=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_info "=== RK3588部署脚本 ==="
    print_info "目标设备: ${RK3588_USER}@${RK3588_HOST}:${RK3588_PORT}"
    print_info "远程路径: ${REMOTE_PATH}"
    echo ""
    
    # 执行部署步骤
    check_local_files
    check_connection
    check_glibc_compatibility
    create_remote_dir
    deploy_files
    verify_deployment

    if [ "$run_test" = true ]; then
        run_remote_test
    fi
    
    print_success "=== 部署完成 ==="
    print_info "可以通过SSH连接到设备并运行程序:"
    print_info "  ssh -p ${RK3588_PORT} ${RK3588_USER}@${RK3588_HOST}"
    print_info "  ${REMOTE_PATH}/factory_write --help"
}

# 执行主函数
main "$@"
