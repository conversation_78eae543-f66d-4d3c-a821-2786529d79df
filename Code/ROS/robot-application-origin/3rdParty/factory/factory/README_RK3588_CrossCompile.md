# RK3588交叉编译指南

## 概述

本文档介绍如何为RK3588平台交叉编译factory项目。项目已配置完整的交叉编译环境，支持自动化构建和部署。

## 系统要求

### 开发主机要求
- Ubuntu 18.04+ 或其他Linux发行版
- CMake 3.16+
- Make工具
- SSH客户端 (用于部署)

### 交叉编译工具链
需要安装aarch64交叉编译工具链：

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu

# 验证安装
aarch64-linux-gnu-gcc --version
```

## 项目结构

```
factory/
├── CMakeLists.txt              # 主CMake配置文件
├── rk3588-toolchain.cmake      # RK3588交叉编译工具链配置
├── build_rk3588.sh            # 自动化构建脚本
├── deploy_to_rk3588.sh         # 部署脚本
├── Makefile                    # 简化构建的Makefile
├── factory_write.c             # 主要源文件
├── factory_read.c              # 读取程序
├── factory_update.c            # 更新程序
├── vendor.h                    # 头文件
└── README_RK3588_CrossCompile.md # 本文档
```

## 快速开始

### 1. 检查工具链
```bash
make check-toolchain
```

### 2. 交叉编译
```bash
# 方法1: 使用Makefile (推荐)
make rk3588

# 方法2: 使用构建脚本
./build_rk3588.sh

# 方法3: 手动CMake
mkdir build_rk3588
cd build_rk3588
cmake -DCMAKE_TOOLCHAIN_FILE=../rk3588-toolchain.cmake ..
make -j$(nproc)
make install
```

### 3. 查看构建结果
```bash
ls -la install/Linux-aarch64/
```

## 详细使用说明

### 构建选项

#### Release模式 (默认)
```bash
make rk3588
# 或
./build_rk3588.sh
```

#### Debug模式
```bash
make rk3588-debug
# 或
./build_rk3588.sh debug
```

#### 清理构建
```bash
make clean
# 或
./build_rk3588.sh clean
```

### 本地编译 (用于测试)
```bash
# 本地编译
make native

# 本地测试
make test
```

## 部署到RK3588设备

### 配置部署环境
设置环境变量或修改脚本中的默认值：

```bash
export RK3588_HOST="*************"    # RK3588设备IP
export RK3588_USER="root"              # SSH用户名
export RK3588_PORT="22"                # SSH端口
export REMOTE_PATH="/usr/local/bin"    # 远程安装路径
```

### 部署命令
```bash
# 基本部署
./deploy_to_rk3588.sh

# 指定IP地址部署
./deploy_to_rk3588.sh --host *************

# 部署并运行测试
./deploy_to_rk3588.sh --test

# 完整示例
./deploy_to_rk3588.sh --host ************* --user root --port 22 --test
```

## 工具链配置说明

### rk3588-toolchain.cmake
- 配置aarch64交叉编译器
- 设置RK3588特定的编译标志
- 优化Cortex-A76/A55架构

### 关键配置
```cmake
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR aarch64)
set(CMAKE_C_COMPILER aarch64-linux-gnu-gcc)
set(CMAKE_CXX_COMPILER aarch64-linux-gnu-g++)
set(CMAKE_C_FLAGS_INIT "-march=armv8-a -mtune=cortex-a76.cortex-a55")
```

## 常见问题

### 1. 工具链未找到
```
错误: aarch64-linux-gnu-gcc 未找到
解决: sudo apt-get install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu
```

### 2. SSH连接失败
```
错误: 无法连接到RK3588设备
解决: 
- 检查IP地址和网络连接
- 确认SSH服务运行
- 配置SSH密钥认证
```

### 3. 权限问题
```
错误: 无法写入/dev/vendor_storage
解决: 在RK3588设备上以root权限运行程序
```

## 高级配置

### 自定义工具链路径
如果使用自定义工具链，修改 `rk3588-toolchain.cmake`:

```cmake
# 设置工具链路径
set(TOOLCHAIN_PATH "/path/to/custom/toolchain")
set(CMAKE_C_COMPILER ${TOOLCHAIN_PATH}/bin/aarch64-linux-gnu-gcc)
set(CMAKE_CXX_COMPILER ${TOOLCHAIN_PATH}/bin/aarch64-linux-gnu-g++)
```

### 添加Sysroot
```cmake
set(CMAKE_SYSROOT "/path/to/rk3588/sysroot")
set(CMAKE_FIND_ROOT_PATH ${CMAKE_SYSROOT})
```

## 验证部署

### 在RK3588设备上测试
```bash
# SSH连接到设备
ssh root@*************

# 检查文件
ls -la /usr/local/bin/factory_*

# 运行程序
/usr/local/bin/factory_write --help

# 测试写入 (需要实际硬件支持)
/usr/local/bin/factory_write SN123456 CMEI123 AA:BB:CC:DD:EE:FF password123
```

## 故障排除

### 构建问题
1. 检查CMake版本: `cmake --version`
2. 检查工具链: `aarch64-linux-gnu-gcc --version`
3. 清理重建: `make clean && make rk3588`

### 部署问题
1. 测试SSH连接: `ssh root@RK3588_IP`
2. 检查网络: `ping RK3588_IP`
3. 验证文件权限: `ls -la install/Linux-aarch64/`

### 运行时问题
1. 检查设备文件: `ls -la /dev/vendor_storage`
2. 确认权限: 以root用户运行
3. 查看系统日志: `dmesg | tail`

## 支持的Make目标

```bash
make help                # 显示所有可用目标
make rk3588             # RK3588交叉编译
make rk3588-debug       # RK3588 Debug编译
make native             # 本地编译
make clean              # 清理构建文件
make test               # 运行测试
make check-toolchain    # 检查工具链
make info               # 显示构建信息
```

## 联系支持

如遇到问题，请检查：
1. 工具链是否正确安装
2. CMake配置是否正确
3. 网络连接是否正常
4. RK3588设备是否支持vendor_storage
