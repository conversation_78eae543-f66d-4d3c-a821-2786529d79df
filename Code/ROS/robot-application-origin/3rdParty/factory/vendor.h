#ifndef _VENDOR_H_
#define _VENDOR_H_

#include <stdint.h>

#define CMCC_DEV_CFG ("/etc/cmcc_robot/cmcc_dev.ini")
#define VENDOR_STORAGE ("/dev/vendor_storage")

#define VENDOR_REQ_TAG (0x56524551)
#define VENDOR_READ_IO _IOW('v', 0X01, unsigned int)
#define VENDOR_WRITE_IO _IOW('v', 0x02, unsigned int)

// devNO=0301
// devType=2320647
// devSn=1830004229212345670000072
// devCmei=212345670000072
// macAddr=2C:C3:E6:E3:5B:72
// OVDMediaEncPassword=1EIpLNAn
// OVDLoginPassword=1EIpLNAn
// mmveeSn=4534290
// APPKey=sqp9ci1qrptp1orl
// APPSecret=2ezmzb9v27fkztj7

#define VENDOR_CMCC_SN_ID (0x01)
#define VENDOR_CMCC_MAC_ID (0x02)
#define VENDOR_CMCC_OVD_LOGIN_ID (0x0B)
#define VENDOR_CMCC_CMEI_ID (0x0F)

#define VENDOR_CMCC_TYPE_ID (0x100)
#define VENDOR_CMCC_OVD_MEDIA_ID (0x101)
#define VENDOR_CMCC_APP_KEY (0x102)
#define VENDOR_CMCC_APP_SECRET (0x103)

struct rk_vendor_req
{
    uint32_t tag;
    uint16_t id;
    uint16_t len;
    uint8_t data[1];
};

#endif // _VENDOR_H_
