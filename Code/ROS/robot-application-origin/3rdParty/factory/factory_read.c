#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <string.h>
#include <errno.h>
#include "vendor.h"

int vendor_storage_read_test(int sys_fd, int check_id)
{
    uint32_t i                = 0;
    int ret                   = 0;
    uint8_t p_buf[2048]       = {0};
    struct rk_vendor_req *req = NULL;

    memset(p_buf, 0, sizeof(p_buf));
    req      = (struct rk_vendor_req *)p_buf;
    req->tag = VENDOR_REQ_TAG;
    req->id  = check_id;
    req->len = 512;

    ret = ioctl(sys_fd, VENDOR_READ_IO, req);
    if(-1 == ret)
    {
        printf("vendor_read ID: 0x%02x failed, error: %s\n", req->id, strerror(errno));
        return -1;
    }
    if(VENDOR_CMCC_MAC_ID == req->id)
    {
        printf("vendor_read: ID: 0x%02x, [%d]data, %02X:%02X:%02X:%02X:%02X:%02X\n", \
            req->id, req->len, req->data[0], req->data[1], req->data[2], req->data[3], req->data[4], req->data[5]);
    }
    else
    {
        printf("vendor_read: ID: 0x%02x, [%d]data, %s\n", req->id, req->len, req->data);
    }
    return 0;
}

int main(void)
{
    int sys_fd = open(VENDOR_STORAGE, O_RDONLY, 0);
    if(sys_fd < 0)
    {
        printf("vendor_storage open failed, error: %s\n", strerror(errno));
        return -1;
    }
    int id_list[] = {VENDOR_CMCC_SN_ID, VENDOR_CMCC_CMEI_ID, \
                    VENDOR_CMCC_MAC_ID, VENDOR_CMCC_OVD_LOGIN_ID};
    for(int i = 0; i < (sizeof(id_list) / sizeof(id_list[0])); i++)
    {
        vendor_storage_read_test(sys_fd, id_list[i]);
    }
    if(-1 != sys_fd)
    {
        close(sys_fd);
        sys_fd = -1;
    }
    return 0;
}
