cmake_minimum_required(VERSION 3.16)
project(factory)

set(SRC_FILES
  src/Common/SerialPort.cpp
  src/Common/UdpCommunicator.cpp
  # src/Common/MessageHandler.cpp
  # src/Common/deep_cmd.h
  src/SensorModule/floodlightcontrol/FloodLightControl.cpp
  src/SensorModule/ledlightcontrol/LightControl.cpp
  src/SensorModule/neckcontrol/NeckControl.cpp
  src/SensorModule/touch/GpioSensor.cpp
  src/SensorModule/ultrasonic/Ultrasonic.cpp
  src/main.cpp
)

add_executable(factory_update
  ${CMAKE_CURRENT_SOURCE_DIR}/factory_update.c
)
target_include_directories(factory_update PRIVATE
  ${CMAKE_CURRENT_SOURCE_DIR}
)

add_executable(factory_read
  ${CMAKE_CURRENT_SOURCE_DIR}/factory_read.c
)
target_include_directories(factory_read PRIVATE
  ${CMAKE_CURRENT_SOURCE_DIR}
)

# add_executable(factory_write
#   ${CMAKE_CURRENT_SOURCE_DIR}/factory_write.c
# )
# target_include_directories(factory_write PRIVATE
#   ${CMAKE_CURRENT_SOURCE_DIR}
# )

install(TARGETS
  factory_update
  factory_read
  # factory_write
  DESTINATION ${CMAKE_CURRENT_SOURCE_DIR}/install/${PROJECT_NAME}
)
