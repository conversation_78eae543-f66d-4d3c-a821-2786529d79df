#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <string.h>
#include <errno.h>
#include "vendor.h"

int vendor_storage_write_test(int sys_fd, int check_id)
{
    uint32_t i                = 0;
    uint32_t len              = 0;
    int ret                   = 0;
    uint8_t p_buf[2048]       = {0};
    struct rk_vendor_req *req = NULL;

    memset(p_buf, 0, sizeof(p_buf));
    req      = (struct rk_vendor_req *)p_buf;
    req->tag = VENDOR_REQ_TAG;
    req->id  = check_id;
    switch(check_id)
    {
        case VENDOR_CMCC_TYPE_ID:
            len = snprintf((char *)req->data, 128, "%s", "2320647");
            break;
        case VENDOR_CMCC_SN_ID:
            len = snprintf((char *)req->data, 128, "%s", "1830004229212345670000072");
            break;
        case VENDOR_CMCC_CMEI_ID:
            len = snprintf((char *)req->data, 128, "%s", "212345670000072");
            break;
        case VENDOR_CMCC_MAC_ID:
            len = snprintf((char *)req->data, 128, "%s", "6A:9B:B6:D9:E5:31");
            break;
        case VENDOR_CMCC_OVD_MEDIA_ID:
            len = snprintf((char *)req->data, 128, "%s", "1EIpLNAn");
            break;
        case VENDOR_CMCC_OVD_LOGIN_ID:
            len = snprintf((char *)req->data, 128, "%s", "1EIpLNAn");
            break;
        case VENDOR_CMCC_APP_KEY:
            len = snprintf((char *)req->data, 128, "%s", "sqp9ci1qrptp1orl");
            break;
        case VENDOR_CMCC_APP_SECRET:
            len = snprintf((char *)req->data, 128, "%s", "2ezmzb9v27fkztj7");
            break;
        default:
            break;
    }
    req->len = len;
    // printf("len: %d, data: %s\n", len, (char *)req->data);
    // return 0;
    ret = ioctl(sys_fd, VENDOR_WRITE_IO, req);
    if(-1 == ret)
    {
        printf("vendor_write ID: 0x%02x failed, error: %s\n", req->id, strerror(errno));
        return -1;
    }
    printf("vendor_write: ID: 0x%02x, data: \n%d:%s\n", req->id, req->len, req->data);
    return 0;
}

int main(void)
{
    int sys_fd = open(VENDOR_STORAGE, O_RDWR, 0);
    if(sys_fd < 0)
    {
        printf("vendor_storage open failed, error: %s\n", strerror(errno));
        return -1;
    }
    int id_list[] = {VENDOR_CMCC_SN_ID, VENDOR_CMCC_CMEI_ID, \
                    VENDOR_CMCC_MAC_ID, VENDOR_CMCC_OVD_LOGIN_ID};
    for(int i = 0; i < (sizeof(id_list) / sizeof(id_list[0])); i++)
    {
        vendor_storage_write_test(sys_fd, id_list[i]);
    }
    if(-1 != sys_fd)
    {
        close(sys_fd);
        sys_fd = -1;
    }
    return 0;
}
