#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <string.h>
#include <errno.h>
#include "vendor.h"

typedef struct _vendor_factory_t_
{
    char devNO[32];
    char devType[32];
    char devSn[32];
    char dev<PERSON><PERSON>[32];
    char macAddr[32];
    char OVDMediaEncPassword[32];
    char OVDLoginPassword[32];
    char mmveeSn[32];
    char APPKey[32];
    char APPSecret[32];
}VENDOR_FACTORY_T;

typedef struct _vendor_info_t_
{
    VENDOR_FACTORY_T factory;
    int to_update;
    int cmcc_fd;
    int sys_fd;
    int size;
    int last_len;
    int len;
    int has_header;
    int header_len;
    char header[256];
    char buf[1024];
}VENDOR_INFO_T;

void vendor_factory_info_print(const char *module, VENDOR_INFO_T *info)
{
    printf(">>>>>> [%s] <<<<<<\n", module);
    printf("header, len: %d\n", info->header_len);
    printf("header, data: \n%s\n", info->header);
    printf("body, size: %d, len: %d, last: %d\n", info->size, info->len, info->last_len);
    if(info->len > info->header_len)
    {
        printf("body, data: %s\n", info->buf);
    }
}

int vendor_factory_info_init(VENDOR_INFO_T *info)
{
    int cmcc_fd = 0;
    cmcc_fd = open(CMCC_DEV_CFG, O_RDWR | O_CREAT, 0664);
    if(cmcc_fd < 0)
    {
        printf("open %s failed, error: %s\n", CMCC_DEV_CFG, strerror(errno));
        return -1;
    }
    info->cmcc_fd = cmcc_fd;
    int sys_fd = 0;
    sys_fd = open(VENDOR_STORAGE, O_RDONLY, 0);
    if(sys_fd < 0)
    {
        printf("open %s failed, error: %s\n", VENDOR_STORAGE, strerror(errno));
        return -1;
    }
    info->sys_fd = sys_fd;

    int len  = 0;
    int size = 0;

    len              = snprintf(info->header, sizeof(info->header), "[cfg]\nversion=V1.0.0\n[factory]\n");
    info->header_len = len;
    size             = lseek(info->cmcc_fd, 0, SEEK_END);
    info->size       = size;
    lseek(info->cmcc_fd, 0, SEEK_SET);
    len = read(info->cmcc_fd, info->buf, sizeof(info->buf));
    if(-1 == len)
    {
        info->size       = 0;
        info->len        = 0;
        info->last_len   = 0;
        info->has_header = 0;
        memset(info->buf, 0, sizeof(info->buf));
        return 0;
    }
    info->len      = len;
    info->last_len = len;
    char *p = NULL;
    p = strstr(info->buf, info->header);
    if(NULL == p)
    {
        printf("not find header\n");
        info->size       = 0;
        info->len        = 0;
        info->has_header = 0;
        memset(info->buf, 0, sizeof(info->buf));
        return 0;
    }
    info->has_header = 1;
    return 0;
}

void vendor_factory_info_deinit(VENDOR_INFO_T *info)
{
    if(-1 != info->cmcc_fd)
    {
        close(info->cmcc_fd);
        info->cmcc_fd = -1;
    }
    if(-1 != info->sys_fd)
    {
        close(info->sys_fd);
        info->sys_fd = -1;
    }
}

int vendor_factory_info_update(VENDOR_INFO_T *info, int check_id)
{
    char *key_str = NULL;
    char *key_value = NULL;
    switch(check_id)
    {
        case VENDOR_CMCC_SN_ID:
            key_str = "devSn=";
            key_value = info->factory.devSn;
            break;
        case VENDOR_CMCC_CMEI_ID:
            key_str = "devCmei=";
            key_value = info->factory.devCmei;
            break;
        case VENDOR_CMCC_MAC_ID:
            key_str = "macAddr=";
            key_value = info->factory.macAddr;
            break;
        case VENDOR_CMCC_OVD_LOGIN_ID:
            key_str = "OVDLoginPassword=";
            key_value = info->factory.OVDLoginPassword;
            break;
        case VENDOR_CMCC_TYPE_ID:
            key_str = "devType=";
            key_value = info->factory.devType;
            break;
        case VENDOR_CMCC_OVD_MEDIA_ID:
            key_str = "OVDMediaEncPassword=";
            key_value = info->factory.OVDMediaEncPassword;
            break;
        case VENDOR_CMCC_APP_KEY:
            key_str = "APPKey=";
            key_value = info->factory.APPKey;
            break;
        case VENDOR_CMCC_APP_SECRET:
            key_str = "APPSecret=";
            key_value = info->factory.APPSecret;
            break;
        default:
            printf("unknown ID: 0x%02x\n", check_id);
            return -1;
    }
    char key_data[256] = {0};
    int len = 0;
    len = snprintf(key_data, sizeof(key_data), "%s%s", key_str, key_value);
    printf("to update, [0x%02x][%s][%d]%s\n", check_id, key_str, len, key_data);
    char *p = NULL;
    p = strstr(info->buf, key_str);
    if(NULL == p)
    {
        printf("not find, need add ID: 0x%02x\n", check_id);
        info->to_update = 1;
        if((info->len >= 1) && (('\n') != info->buf[info->len - 1]))
        {
            len = snprintf(info->buf + info->len, sizeof(info->buf) - info->len, "\n%s", key_data);
        }
        else
        {
            len = snprintf(info->buf + info->len, sizeof(info->buf) - info->len, "%s", key_data);
        }
        info->len += len;
        return 0;
    }
    key_data[len] = '\n';
    len++;
    if(0 == memcmp(p, key_data, len))
    {
        printf("the same, ignore\n");
        return 0;
    }
    printf("need update\n");
    info->to_update = 1;
    int off = 0;
    int left_len = 0;
    int right_len = 0;
    char temp_buf[1024] = {0};
    left_len = p - info->buf;
    if(left_len > 0)
    {
        memcpy(temp_buf + off, info->buf, left_len);
        off += left_len;
    }
    memcpy(temp_buf + off, key_data, len);
    off += len;
    printf("left_len: %d, right_len: %d, len: %d, off: %d\n", left_len, right_len, info->len, off);
    int i = 0;
    while((left_len + i) < info->len)
    {
        if(p[i] == '\n')
        {
            i++;
            break;
        }
        i++;
    }
    left_len += i;
    right_len = info->len - left_len;
    if(right_len > 0)
    {
        memcpy(temp_buf + off, p + i, right_len);
        off += right_len;
    }
    memset(info->buf, 0, sizeof(info->buf));
    memcpy(info->buf, temp_buf, off);
    info->len = off;
    printf("left_len: %d, right_len: %d, i: %d, off: %d\n", left_len, right_len, i, off);
    return 0;
}

int vendor_factory_read(VENDOR_INFO_T *info, int check_id)
{
    uint32_t i                = 0;
    int ret                   = 0;
    uint8_t p_buf[2048]       = {0};
    struct rk_vendor_req *req = NULL;

    memset(p_buf, 0, sizeof(p_buf));
    req      = (struct rk_vendor_req *)p_buf;
    req->tag = VENDOR_REQ_TAG;
    req->id  = check_id;
    req->len = 512;

    ret = ioctl(info->sys_fd, VENDOR_READ_IO, req);
    if(-1 == ret)
    {
        printf("vendor_read ID: 0x%02x failed, error: %s\n", req->id, strerror(errno));
        return -1;
    }
    if(VENDOR_CMCC_MAC_ID == req->id)
    {
        printf("vendor_read: ID: 0x%02x, [%d]data, %02X:%02X:%02X:%02X:%02X:%02X\n", \
            req->id, req->len, req->data[0], req->data[1], req->data[2], req->data[3], req->data[4], req->data[5]);
    }
    else
    {
        printf("vendor_read: ID: 0x%02x, [%d]data, %s\n", req->id, req->len, req->data);
    }
    switch(req->id)
    {
        case VENDOR_CMCC_SN_ID:
            snprintf(info->factory.devSn, sizeof(info->factory.devSn), "%s", req->data);
            break;
        case VENDOR_CMCC_CMEI_ID:
            snprintf(info->factory.devCmei, sizeof(info->factory.devCmei), "%s", req->data);
            break;
        case VENDOR_CMCC_MAC_ID:
            snprintf(info->factory.macAddr, sizeof(info->factory.macAddr), "%02X:%02X:%02X:%02X:%02X:%02X", \
                    req->data[0], req->data[1], req->data[2], req->data[3], req->data[4], req->data[5]);
            break;
        case VENDOR_CMCC_OVD_LOGIN_ID:
            snprintf(info->factory.OVDLoginPassword, sizeof(info->factory.OVDLoginPassword), "%s", req->data);
            snprintf(info->factory.OVDMediaEncPassword, sizeof(info->factory.OVDMediaEncPassword), "%s", req->data);
            break;
        default:
            printf("unknown ID: 0x%02x\n", req->id);
            return -1;
    }
    return 0;
}

int vendor_factory_fill(VENDOR_INFO_T *info)
{
    int i = 0;
    int id_list[] = {VENDOR_CMCC_SN_ID, VENDOR_CMCC_CMEI_ID, \
                    VENDOR_CMCC_MAC_ID, VENDOR_CMCC_OVD_LOGIN_ID};

    snprintf(info->factory.devType, sizeof(info->factory.devType), "%s", "591884");
    snprintf(info->factory.APPKey, sizeof(info->factory.APPKey), "%s", "inv53tlraz2r5zwu");
    snprintf(info->factory.APPSecret, sizeof(info->factory.APPSecret), "%s", "01dj4l6tk409b29u");
    for(i = 0; i < (sizeof(id_list) / sizeof(id_list[0])); i++)
    {
        if(-1 == vendor_factory_read(info, id_list[i]))
        {
            printf("read id: 0x%0x failed\n", id_list[i]);
            return -1;
        }
    }
    return 0;
}

int vendor_update_factory(VENDOR_INFO_T *info)
{
    int i = 0;

    int id_list_update[] = {VENDOR_CMCC_TYPE_ID, VENDOR_CMCC_SN_ID, VENDOR_CMCC_CMEI_ID, \
                    VENDOR_CMCC_MAC_ID, VENDOR_CMCC_OVD_MEDIA_ID, VENDOR_CMCC_OVD_LOGIN_ID, \
                    VENDOR_CMCC_APP_KEY, VENDOR_CMCC_APP_SECRET};

    if(-1 == vendor_factory_fill(info))
    {
        printf("fill factory failed\n");
        return -1;
    }

    for(i = 0; i < (sizeof(id_list_update) / sizeof(id_list_update[0])); i++)
    {
#if 0
        if((info->len >= 1) && (('\n') != info->buf[info->len - 1]))
        {
            printf("check, add enter, len: %d\n", info->len);
            info->buf[info->len - 1] = '\n';
            info->len += 1;
        }
#endif
        if(-1 == vendor_factory_info_update(info, id_list_update[i]))
        {
            printf("update id: 0x%0x failed\n", id_list_update[i]);
            return -1;
        }
        // vendor_factory_info_print("update", info);
    }
    if((info->len >= 1) && (('\n') != info->buf[info->len - 1]))
    {
        printf("check last, add enter, len: %d\n", info->len);
        info->buf[info->len] = '\n';
        info->len += 1;
    }
    if(!info->to_update)
    {
        printf("not need update factory info\n");
        return 0;
    }
    vendor_factory_info_print("last", info);
    lseek(info->cmcc_fd, 0, SEEK_SET);
    int ret = 0;
    int total_len = 0;
    total_len = (info->has_header) ? (info->len) : (info->len + info->header_len);
    if(total_len < info->last_len)
    {
        printf("cur: %d < last: %d, remove and create\n", total_len, info->last_len);
        if(-1 != info->cmcc_fd)
        {
            close(info->cmcc_fd);
            info->cmcc_fd = -1;
        }
        ret = remove(CMCC_DEV_CFG);
        if(-1 == ret)
        {
            printf("remove, %s failed, error: %s\n", CMCC_DEV_CFG, strerror(errno));
            return -1;
        }
        int cmcc_fd = 0;
        cmcc_fd = open(CMCC_DEV_CFG, O_RDWR | O_CREAT, 0664);
        if(cmcc_fd < 0)
        {
            printf("open %s failed, error: %s\n", CMCC_DEV_CFG, strerror(errno));
            return -1;
        }
        info->cmcc_fd = cmcc_fd;
    }
    int skip = 0;
    if((0 == info->has_header) && (info->header_len > 0))
    {
        ret = write(info->cmcc_fd, info->header, info->header_len);
        if(ret != info->header_len)
        {
            printf("write header failed, ret: %d, len: %d, error: %s\n", ret, info->header_len, strerror(errno));
            return -1;
        }
#if 0
        if((info->len > 0) && (info->buf[info->len - 1]))
        {
            skip = 1;
        }
#endif
    }
    if(info->len <= skip)
    {
        printf("no body\n");
        return -1;
    }
    ret = write(info->cmcc_fd, info->buf + skip, info->len - skip);
    if(ret != (info->len - skip))
    {
        printf("write body failed, ret: %d, len: %d, error: %s\n", ret, (info->len - skip), strerror(errno));
        return -1;
    }
    return 0;
}

int main(void)
{
    int ret = 0;
    VENDOR_INFO_T info;
    do
    {
        memset(&info, 0, sizeof(VENDOR_INFO_T));
        ret = vendor_factory_info_init(&info);
        if(-1 == ret)
        {
            printf("init factory info failed\n");
            break;
        }
        vendor_factory_info_print("origin", &info);
        ret = vendor_update_factory(&info);
        if(-1 == ret)
        {
            printf("update factory failed\n");
            break;
        }
    }
    while(0);
    vendor_factory_info_deinit(&info);
    return 0;
}
