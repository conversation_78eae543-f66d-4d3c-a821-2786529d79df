#ifndef NECKCONTROL_H
#define NECKCONTROL_H

#include "Common/SerialPort.h"
#include <mutex>
#include <thread>
#include <atomic>
#include <future>

class NeckControl
{
  public:
  NeckControl(sensorhub::SerialPort& serialPort);
  ~NeckControl();

  // 控制颈部位置 (0-100%)
  bool controlNeck(int targetPosition);
  int  getCurrentPosition();

  private:
  sensorhub::SerialPort&       serialPort_;
  unsigned char                cnt_;
  std::atomic<bool>            isMoving_;
  std::mutex                   movementMutex_;
  std::shared_ptr<std::thread> movementThread_;
  int                          currentPosition_;

  bool sendNeckCommand(int position);
  void moveToPosition(int targetPosition, int speedDegreesPerSecond);

  void setCurrentPosition(int position);
};

#endif // NECKCONTROL_H
