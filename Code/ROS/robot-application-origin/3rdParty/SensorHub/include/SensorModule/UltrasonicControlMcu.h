#ifndef ULTRASONIC_CONTROL_MCU_H
#define ULTRASONIC_CONTROL_MCU_H

#include "Common/SerialPort.h"
#include <vector>
#include <cstdint>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <iostream>

constexpr uint8_t FRAME_TYPE_REQUEST = 0x17;
constexpr uint8_t FRAME_FUNC_CODE_ULTRASONIC = 0x33;
constexpr uint8_t FRAME_TYPE_RESPONSE = 0x21;

class UltrasonicControlMcu
{
  public:
  UltrasonicControlMcu(SerialPort& serialPort);
  ~UltrasonicControlMcu();
  bool sendUltrasonicCommand();

  std::vector<uint8_t> getLatestDistances();

  private:
  private:
  SerialPort& serialPort_;
  uint8_t cnt_;

  std::thread run_thread_;
  std::atomic<bool> exit_flag_;

  std::mutex data_mutex_;
  std::vector<uint8_t> latest_distances_;
  std::condition_variable data_cv_;

  uint16_t calculate_crc(const unsigned char* data, size_t length);

  void runThread();

  bool parseFrame(const std::vector<unsigned char>& frame, std::vector<uint8_t>& distances);
};

#endif // ULTRASONIC_CONTROL_MCU_H
