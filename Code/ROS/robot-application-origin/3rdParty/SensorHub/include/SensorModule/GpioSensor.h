/*
 * GpioSensor.h
 * @description 用于获取触摸状态
 * @created 2024-10-02T11:21:36.683Z+08:00
 * @last-modified 2024-10-02T13:59:46.839Z+08:00
 */

// GpioSensor.h
#ifndef GPIOSENSOR_H
#define GPIOSENSOR_H

#include <string>
#include <fstream>
#include <atomic>
#include <thread>
#include <iostream>
#include <fcntl.h>
#include <unistd.h>
#include <sys/epoll.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <cstring>
#include "Common/UdpCommunicator.h"

class UdpCommunicator;

class GpioSensor
{
  public:
  GpioSensor();
  ~GpioSensor();

  bool initialize(const std::string& pinIdentifier, const std::string& edge, const int& id);

  void startMonitoring();
  void stopMonitoring();

  void setUdpCommunicator(UdpCommunicator* udpCommunicator);

  private:
  int identifyId_;
  int gpio_pin_;

  std::thread monitoringThread_;
  std::atomic<bool> monitoring_;
  UdpCommunicator* udpCommunicator_;

  void exportGPIO(int gpioPin);
  bool setEdgeTrigger(const std::string& edge);
  bool setDirection(const std::string& direction);
  int calculateGPIOPin(const std::string& pinIdentifier);
  char readGPIOValue();
  bool setGPIOValue(const uint8_t value);

  void monitorGPIOChanges();
  void sendGPIOValue(char value);
};

#endif // GPIOSENSOR_H
