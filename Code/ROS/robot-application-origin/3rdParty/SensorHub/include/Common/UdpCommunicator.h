/*
 * <AUTHOR> m-sharp
 * @Date : 2024-10-02 11:22:24
 * @LastEditors : m-sharp <EMAIL>
 * @LastEditTime : 2024-10-08 21:28:15
 * @FilePath : /CB1/SensorHub/include/Common/UdpCommunicator.h
 * @brief :
 *
 * Copyright (c) 2024 by DeepRobotics.cn, All Rights Reserved.
 */

#ifndef UDPCOMMUNICATOR_H
#define UDPCOMMUNICATOR_H

#include <atomic>
#include <functional>
#include <mutex>
#include <string>
#include <thread>

/**
 * @brief UDP通讯器
 *        负责UDP通讯的链接建立/接收/发送/接收回调
 */
class UdpCommunicator
{
  public:
  static UdpCommunicator* createSendingAndReceiving(const std::string& localIp, int localPort);

  static UdpCommunicator* createSendingOnly();

  ~UdpCommunicator();

  bool startListening();
  void stopListening();

  void setReceiveCallback(std::function<void(const std::string& remoteIp, int remotePort,
                                             const char* data, size_t size)>
                              callback);
  /**
   * @brief
   *
   * @param [in] data
   * @param [in] size
   * @return true
   * @return false
   */
  bool sendData(const char* data, size_t size);
  bool sendData(const std::string& remoteIp, int remotePort, const char* data, size_t size);

  private:
  UdpCommunicator(const UdpCommunicator&) = delete;
  UdpCommunicator& operator=(const UdpCommunicator&) = delete;
  UdpCommunicator();
  bool initialize(const std::string& localIp, int localPort);
  bool initialize();
  void listeningLoop();
  enum class Mode
  {
    SendingOnly,
    SendingAndReceiving
  } mode_;

  int         sockfd_;
  std::string localIp_;
  int         localPort_;

  std::thread       listeningThread_;
  std::atomic<bool> running_;

  std::function<void(const std::string& remoteIp, int remotePort, const char* data, size_t size)>
      receiveCallback_;

  std::string remoteIp_;
  int         remotePort_;
  std::mutex  remoteAddrMutex_;
};

#endif // UDPCOMMUNICATOR_H
