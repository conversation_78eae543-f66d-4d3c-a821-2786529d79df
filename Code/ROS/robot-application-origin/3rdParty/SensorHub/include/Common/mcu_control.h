#ifndef _HOST_SYS_H
#define _HOST_SYS_H

#include <iostream>
#include <cstring>
#include <unistd.h> // 包含sleep() 和 usleep()
#include <vector>
#include <queue>
#include <chrono>
#include <mutex>
#include <condition_variable>
#include <variant>

#include "SerialPort.hpp"
#include "CircularQueue.h"
#include "mcu_protocolstack.h"

std::string getFileNameFromPath(const std::string& filepath);
void        delay(unsigned milliseconds);

class mcu_Control
{
  public:
#define REALTIME_LIMIT 200
  mcu_Control(std::string device = "/dev/ttyS1");
  ~mcu_Control();
  void mcuFmUpdate(const std::string& filepath = "./app.bin");

  void mcuReset(void);

  void setGearMotor(uint16_t data);
  void setFloodLight(uint16_t data);

  void ControlColorLight(uint8_t blue, uint8_t red, uint8_t green, uint32_t enable);
  void ControlColorLightBreath(uint8_t blue, uint8_t red, uint8_t green, uint32_t enable,
                               uint16_t duty);
  void ControlColorLightBlink(uint8_t blue, uint8_t red, uint8_t green, uint32_t enable,
                              uint16_t duty);

  void CB1LightControl(uint8_t eye_mode, uint8_t tail_mode, uint8_t flood_mode, uint16_t eye_duty,
                       uint16_t tail_duty, uint32_t eye_rgb, uint32_t tail_rgb);

  std::vector<uint16_t> getUltraSonicDistance();
  bool                  getTouchState(uint8_t& state);
  std::string           getSoftwareVer();

  private:
  mcu_ProtocolStack* pmcu_ProtocolStack_t;
  bool               is_realtime(STEADY_CLOCK time);
};
// cb1 专用灯效类

#endif
