cmake_minimum_required(VERSION 3.16)
project(SensorHub)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(SRC_FILES
  src/Common/SerialPort.cpp
  src/Common/UdpCommunicator.cpp
  # src/Common/MessageHandler.cpp
  # src/Common/deep_cmd.h
  src/SensorModule/floodlightcontrol/FloodLightControl.cpp
  src/SensorModule/ledlightcontrol/LightControl.cpp
  src/SensorModule/neckcontrol/NeckControl.cpp
  src/SensorModule/touch/GpioSensor.cpp
  src/SensorModule/ultrasonic/Ultrasonic.cpp
  src/main.cpp
)

add_executable(SensorHubNode ${SRC_FILES})

target_include_directories(SensorHubNode PRIVATE
  ${CMAKE_SOURCE_DIR}/include
  ${CMAKE_SOURCE_DIR}/../../xiaoli_application_ros2/include/xiaoli_com
)

target_link_libraries(SensorHubNode pthread)

install(TARGETS
  SensorHubNode
  DESTINATION ${CMAKE_SOURCE_DIR}/install/${PROJECT_NAME}
)
