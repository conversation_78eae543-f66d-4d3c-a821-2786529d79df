#include "Common/SerialPort.h"
// #include "Common/MessageHandler.h"
#include "Common/UdpCommunicator.h"
#include "SensorModule/LightControl.h"
#include "SensorModule/NeckControl.h"
#include "SensorModule/FloodLightControl.h"
#include "SensorModule/ultrasonic.hpp"
#include "SensorModule/GpioSensor.h"

#include <iostream>


int main()
{
	std::cout << "Main start 2" << std::endl;
	printf("print Main start!\n");

//	// 初始化串口
//	SerialPort serialPort("/dev/ttyS0", B115200);
//	if (!serialPort.openPort()) 
//	{
//		std::cerr << "Failed to open serial port." << std::endl;
//		return -1;
//	}


	// 初始化UDP接口
//	FloodLightControl floodLightControl(serialPort);
//	UdpCommunicator* sensorHubServer = UdpCommunicator::createSendingAndReceiving("127.0.0.1", 12345);
//	if (!sensorHubServer) 
//	{
//		std::cerr << "Failed to create UdpCommunicator." << std::endl;
//		return -1;
//	}

//	  GpioSensor touchSensor1;
	  GpioSensor touchSensor2;
	  //Ultrasonic ultrasonic;
  
	  // GPIO 初始化
//	  if (!touchSensor1.initialize("112", "both", 1)) 
//	  {
//		  std::cerr << "Failed to initialize GPIO 112 sensor." << std::endl;
//		  return -1;
//	  }


	  
	  printf("before initialize!!\n");
	  if (!touchSensor2.initialize("113", "both", 1)) 
	  {
		  std::cerr << "Failed to initialize GPIO 113 sensor." << std::endl;
		  return -1;
	  }
  
	  // Monitor the touch sensor value changes and send data once there is a change
	  UdpCommunicator* sendClientUdp = UdpCommunicator::createSendingOnly();
//	  touchSensor1.setUdpCommunicator(sendClientUdp);
//	  touchSensor1.startMonitoring();
	  touchSensor2.setUdpCommunicator(sendClientUdp);
	  touchSensor2.startMonitoring();
  
  while(1)
  {
    usleep(100 * 1000);
  }
	  // std::cout << "Server is running. Press Enter to exit." << std::endl;
	  // std::cin.get();
//	  touchSensor1.stopMonitoring();
	  // touchSensor2.stopMonitoring();


	std::cout << "main finished !" << std::endl;
	return 0;



/* 临时调试屏蔽 --mhz */


/*
	
  McuAgent& mcuAgent = McuAgent::Instance();
  // Start listening for client control commands
  UdpCommunicator* sensorHubServer = UdpCommunicator::createSendingAndReceiving("0.0.0.0", 12345);
  if (!sensorHubServer) {
    std::cerr << "Failed to create UdpCommunicator." << std::endl;
    return -1;
  }
  MessageHandler messageHandler(mcuAgent, *sensorHubServer);
  sensorHubServer->setReceiveCallback(
      [&](const std::string& remoteIp, const int& port, const char* data, size_t size) {
        messageHandler.handleMessage(remoteIp, port, data, size);
      });

  if (!sensorHubServer->startListening()) {
    std::cerr << "Failed to start UDP listening." << std::endl;
    return -1;
  }

  // Monitor the touch sensor value changes and send data once there is a change
  // UdpCommunicator* sendClientUdp = UdpCommunicator::createSendingOnly();

  std::cout << "Server is running. Press Enter to exit." << std::endl;
  while (std::cin.get() != '\n') {
    sleep(1);
  };
  sensorHubServer->stopListening();

  return 0;

*/
}
