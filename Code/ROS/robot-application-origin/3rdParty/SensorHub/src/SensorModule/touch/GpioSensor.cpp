#include "SensorModule/GpioSensor.h"

#include "deep_cmd.h"

GpioSensor::GpioSensor() : gpio_pin_(-1), monitoring_(false), udpCommunicator_(nullptr) {}

GpioSensor::~GpioSensor() { stopMonitoring(); }

void GpioSensor::setUdpCommunicator(UdpCommunicator *udpCommunicator)
{
  udpCommunicator_ = udpCommunicator;
}

bool GpioSensor::initialize(const std::string &pinIdentifier, const std::string &edge,
                            const int &id)
{
  identifyId_ = id;
  
//  gpio_pin_   = calculateGPIOPin(pinIdentifier);
  gpio_pin_ = std::atoi(pinIdentifier.c_str());

  if (gpio_pin_ < 0) {
    std::cerr << "Invalid GPIO pin identifier: " << pinIdentifier << std::endl;
    return false;
  }
  exportGPIO(gpio_pin_);
  if (!setDirection("in")) {
    return false;
  }
  if (!setEdgeTrigger(edge)) {
    return false;
  }
  return true;
}

void GpioSensor::exportGPIO(int gpioPin)
{
  std::ofstream exportFile("/sys/class/gpio/export");
  if (!exportFile.is_open()) {
    std::cerr << "Failed to open /sys/class/gpio/export" << std::endl;
    return;
  }
  exportFile << gpioPin;
  exportFile.close();
}

bool GpioSensor::setEdgeTrigger(const std::string &edge)
{
  std::string   edgeFilePath = "/sys/class/gpio/gpio" + std::to_string(gpio_pin_) + "/edge";
  std::ofstream edgeFile(edgeFilePath);
  if (!edgeFile.is_open()) {
    std::cerr << "Failed to open " << edgeFilePath << std::endl;
    return false;
  }
  edgeFile << edge;
  edgeFile.close();
  return true;
}

bool GpioSensor::setDirection(const std::string &direction)
{
  std::string directionFilePath = "/sys/class/gpio/gpio" + std::to_string(gpio_pin_) + "/direction";
  std::ofstream directionFile(directionFilePath);
  if (!directionFile.is_open()) {
    std::cerr << "Failed to open " << directionFilePath << std::endl;
    return false;
  }
  directionFile << direction;
  directionFile.close();
  return true;
}

bool GpioSensor::setGPIOValue(const uint8_t value)
{
  std::string   valueFilePath = "/sys/class/gpio/gpio" + std::to_string(gpio_pin_) + "/value";
  std::ofstream valueFile(valueFilePath);
  if (!valueFile.is_open()) {
    std::cerr << "Failed to open " << valueFilePath << std::endl;
    return false;
  }
  valueFile << std::to_string(value);
  valueFile.close();
  return true;
}

int GpioSensor::calculateGPIOPin(const std::string &pinIdentifier)
{
	if (pinIdentifier.size() != 3) 
	{
		return -1;
	}


	int  bank      = pinIdentifier[0] - '0'; // Convert character to integer
	char group     = pinIdentifier[1];
	int  x         = pinIdentifier[2] - '0'; // Convert character to integer
	int  group_num = group - 'A';
	
	int number = group_num * 8 + x;
	int pin    = bank * 32 + number;

	return pin;
}

char GpioSensor::readGPIOValue()
{
  std::string   valueFilePath = "/sys/class/gpio/gpio" + std::to_string(gpio_pin_) + "/value";
  std::ifstream valueFile(valueFilePath);
  if (!valueFile.is_open()) {
    std::cerr << "Failed to open " << valueFilePath << std::endl;
    return '\0';
  }
  char value;
  valueFile >> value;
  valueFile.close();
  return value;
}

void GpioSensor::startMonitoring()
{
  monitoring_       = true;
  monitoringThread_ = std::thread(&GpioSensor::monitorGPIOChanges, this);
}

void GpioSensor::stopMonitoring()
{
  monitoring_ = false;
  if (monitoringThread_.joinable()) {
    monitoringThread_.join();
  }
}

void GpioSensor::monitorGPIOChanges()
{
	
  std::string valueFilePath = "/sys/class/gpio/gpio" + std::to_string(gpio_pin_) + "/value";
  
  int         fd            = open(valueFilePath.c_str(), O_RDONLY | O_NONBLOCK);
  if (fd < 0) {
    std::cerr << "Failed to open " << valueFilePath << std::endl;
    return;
  }

  int epollFd = epoll_create1(0);
  if (epollFd < 0) {
    std::cerr << "Failed to create epoll instance." << std::endl;
    close(fd);
    return;
  }

  struct epoll_event ev;
  ev.events  = EPOLLPRI | EPOLLERR;
  ev.data.fd = fd;
  if (epoll_ctl(epollFd, EPOLL_CTL_ADD, fd, &ev) < 0) {
    std::cerr << "Failed to add fd to epoll." << std::endl;
    close(fd);
    close(epollFd);
    return;
  }

  char buf;
  lseek(fd, 0, SEEK_SET);
  read(fd, &buf, 1);

  while (monitoring_) {
    struct epoll_event events[1];
    int                nfds = epoll_wait(epollFd, events, 1, -1);
    if (nfds < 0) 
	{
		if (errno == EINTR) 
		{
			continue;
		}
		std::cerr << "epoll_wait failed." << std::endl;
		break;
    }

    if (events[0].events & EPOLLPRI) 
	{
		lseek(fd, 0, SEEK_SET);
		char    buf;
		ssize_t len = read(fd, &buf, 1);
		if (len > 0) 
		{
			printf(" GPIO Changed![%c]\n ", buf);
			sendGPIOValue(buf);
		}
    }
  }

  close(fd);
  close(epollFd);
}

void GpioSensor::sendGPIOValue(char value)
{
   //if (udpCommunicator_) 
   {
     pk_t pk{};
     pk.head.len = htonl(sizeof(pk_t));
     pk.head.cmd = htonl(DEEP_CMD_SENSOR);

     pk.rsp_body.sensor.sensor_id = htonl(identifyId_);
     pk.rsp_body.sensor.value = htonl(static_cast<int>(value));
     pk.rsp_body.sensor.timestamp = htonl(static_cast<int>(time(nullptr)));

     char buffer[sizeof(pk_t)];
     memset(buffer, 0, sizeof(buffer));
     memcpy(buffer, &pk, sizeof(pk_t));

     bool sendSuccess = udpCommunicator_->sendData("127.0.0.1", 1234, buffer, sizeof(buffer));
     if (!sendSuccess) 
	 {
       std::cerr << "Failed to send sensor data via UDP." << std::endl;
     } 
	 else 
	 {
       std::cout << "Sensor data sent successfully." << std::endl;
     }

	    } 
//	 else
//	 	{
//     std::cerr << "UdpCommunicator is not initialized." << std::endl;
//   }
}
