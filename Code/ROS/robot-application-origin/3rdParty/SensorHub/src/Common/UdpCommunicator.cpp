#include "Common/UdpCommunicator.h"
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <cstring>
#include <iostream>

UdpCommunicator::UdpCommunicator()
    : sockfd_(-1),
      localPort_(0),
      running_(false),
      remotePort_(0),
      mode_(Mode::SendingOnly)
{
}

UdpCommunicator* UdpCommunicator::createSendingAndReceiving(const std::string& localIp,
                                                            int                localPort)
{
  UdpCommunicator* communicator = new UdpCommunicator();
  if (communicator->initialize(localIp, localPort)) {
    communicator->mode_ = Mode::SendingAndReceiving;
    return communicator;
  }
  delete communicator;
  return nullptr;
}

UdpCommunicator* UdpCommunicator::createSendingOnly()
{
  UdpCommunicator* communicator = new UdpCommunicator();
  if (communicator->initialize()) {
    communicator->mode_ = Mode::SendingOnly;
    return communicator;
  }
  delete communicator;
  return nullptr;
}

UdpCommunicator::~UdpCommunicator()
{
  stopListening();
  if (sockfd_ != -1) {
    close(sockfd_);
    sockfd_ = -1;
  }
}

bool UdpCommunicator::initialize(const std::string& localIp, int localPort)
{
  localIp_   = localIp;
  localPort_ = localPort;

  sockfd_ = socket(AF_INET, SOCK_DGRAM, 0);
  if (sockfd_ < 0) {
    std::cerr << "Failed to create UDP socket." << std::endl;
    return false;
  }

  sockaddr_in localAddr{};
  localAddr.sin_family      = AF_INET;
  localAddr.sin_addr.s_addr = inet_addr(localIp_.c_str());
  localAddr.sin_port        = htons(localPort_);

  if (bind(sockfd_, (struct sockaddr*)&localAddr, sizeof(localAddr)) < 0) {
    std::cerr << "Failed to bind UDP socket." << std::endl;
    close(sockfd_);
    sockfd_ = -1;
    return false;
  }

  return true;
}

bool UdpCommunicator::initialize()
{
  sockfd_ = socket(AF_INET, SOCK_DGRAM, 0);
  if (sockfd_ < 0) {
    std::cerr << "Failed to create UDP socket for sending." << std::endl;
    return false;
  }

  return true;
}

bool UdpCommunicator::startListening()
{
  if (mode_ != Mode::SendingAndReceiving) {
    std::cerr << "UdpCommunicator is not initialized for receiving." << std::endl;
    return false;
  }
  if (running_) {
    std::cerr << "UdpCommunicator is already running." << std::endl;
    return false;
  }
  if (sockfd_ == -1) {
    std::cerr << "Socket not initialized or bound. Cannot start listening." << std::endl;
    return false;
  }
  running_         = true;
  listeningThread_ = std::thread(&UdpCommunicator::listeningLoop, this);
  return true;
}

void UdpCommunicator::stopListening()
{
  running_ = false;
  if (listeningThread_.joinable()) {
    listeningThread_.join();
  }
}

void UdpCommunicator::setReceiveCallback(
    std::function<void(const std::string& remoteIp, int remotePort, const char* data, size_t size)>
        callback)
{
  receiveCallback_ = callback;
}

bool UdpCommunicator::sendData(const char* data, size_t size)
{
  if (mode_ != Mode::SendingAndReceiving) {
    std::cerr << "UdpCommunicator is not initialized for receiving. Cannot use sendData() without "
                 "parameters."
              << std::endl;
    return false;
  }
  if (sockfd_ == -1) {
    std::cerr << "Socket not initialized." << std::endl;
    return false;
  }

  std::string remoteIpCopy;
  int         remotePortCopy;

  {
    std::lock_guard<std::mutex> lock(remoteAddrMutex_);
    remoteIpCopy   = remoteIp_;
    remotePortCopy = remotePort_;
  }

  if (remoteIpCopy.empty() || remotePortCopy == 0) {
    std::cerr << "Remote IP and port not set." << std::endl;
    return false;
  }

  sockaddr_in remoteAddr{};
  remoteAddr.sin_family = AF_INET;
  remoteAddr.sin_port   = htons(remotePortCopy);
  if (inet_pton(AF_INET, remoteIpCopy.c_str(), &remoteAddr.sin_addr) <= 0) {
    std::cerr << "Invalid remote IP address: " << remoteIpCopy << std::endl;
    return false;
  }

  ssize_t sentSize =
      sendto(sockfd_, data, size, 0, (struct sockaddr*)&remoteAddr, sizeof(remoteAddr));
  if (sentSize < 0) {
    std::cerr << "Failed to send UDP data." << std::endl;
    return false;
  }

  return true;
}

bool UdpCommunicator::sendData(const std::string& remoteIp, int remotePort, const char* data,
                               size_t size)
{
  if (sockfd_ == -1) {
    std::cerr << "Socket not initialized." << std::endl;
    return false;
  }

  sockaddr_in remoteAddr{};
  remoteAddr.sin_family = AF_INET;
  remoteAddr.sin_port   = htons(remotePort);
  if (inet_pton(AF_INET, remoteIp.c_str(), &remoteAddr.sin_addr) <= 0) {
    std::cerr << "Invalid remote IP address: " << remoteIp << std::endl;
    return false;
  }

  ssize_t sentSize =
      sendto(sockfd_, data, size, 0, (struct sockaddr*)&remoteAddr, sizeof(remoteAddr));
  if (sentSize < 0) {
    std::cerr << "Failed to send UDP data." << std::endl;
    return false;
  }

  return true;
}

void UdpCommunicator::listeningLoop()
{
  const int bufferSize = 2048;
  char      buffer[bufferSize];

  while (running_) {
    sockaddr_in remoteAddr{};
    socklen_t   addrLen = sizeof(remoteAddr);

    ssize_t receivedSize =
        recvfrom(sockfd_, buffer, bufferSize, 0, (struct sockaddr*)&remoteAddr, &addrLen);
    if (receivedSize < 0) {
      if (errno == EINTR) {
        continue;
      } else {
        std::cerr << "Failed to receive UDP data." << std::endl;
        break;
      }
    }

    {
      std::lock_guard<std::mutex> lock(remoteAddrMutex_);
      remoteIp_   = inet_ntoa(remoteAddr.sin_addr);
      remotePort_ = ntohs(remoteAddr.sin_port);
    }

    if (receiveCallback_) {
      receiveCallback_(remoteIp_, remotePort_, buffer, receivedSize);
    }
  }
}
