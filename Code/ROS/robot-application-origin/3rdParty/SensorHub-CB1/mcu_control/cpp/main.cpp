#include <iostream>
#include <cstring>
#include <unistd.h>  // 包含sleep() 和 usleep()
#include <mcu_control.h>
#include <cstdint>
#include <sstream>

#include "mcu_agent.hpp"

using namespace std;

// 用户输入转换为字节序列的函数
std::vector<uint8_t> string_to_bytes(const std::string& input) {
    std::istringstream iss(input);
    std::vector<uint8_t> result;
    uint8_t value;
    char delimiter = ' ';
    std::string token;
    while (std::getline(iss, token, delimiter)) {
        std::istringstream converter(token);
        converter >> std::hex >> value;
        result.push_back(value);
    }
    return result;
}


int main()
{
    McuAgent &dddd = McuAgent::Instance();
    mcu_Control mcu_control_t = dddd.mcu_control_;
    // mcu_Control mcu_control_t =mcu_Control("/dev/ttyS1");
    while(1)
    {
        cout<<"========================chose function:======================================"<<endl;
        cout<<"1.MCU Reset 2.MCU OTA 3.ultrasonic 4.get touch state 5.neck control          "<<endl;
        cout<<"6.Light control 7.light breath 8.light blink  9.CB1 control light            "<<endl;
        cout<<"10.get software version  11. preset color test 12. flashlight control        "<<endl;
        cout<<"============================================================================="<<endl;
        int choice;
        cin>>choice;
        if (cin.fail()) { // 检查输入是否为整数
            cin.clear(); // 清除错误标志
            cin.ignore(numeric_limits<streamsize>::max(), '\n'); // 忽略行内剩余字符
            cout << "Invalid input, please enter an integer." << endl;
            continue; // 继续下一次循环
        } 
        cout<<"current choice:"<<choice<<endl;
        if(choice == 1){
            mcu_control_t.mcuReset();
        }
        else if(choice == 2){
            mcu_control_t.mcuFmUpdate("/home/<USER>/Downloads/app.bin");
        }
        else if(choice == 3){
            std::vector<uint16_t> dis = mcu_control_t.getUltraSonicDistance();
            if(dis.size() != 0){
                std::cout<<"front distance :"<<dis[0] <<std::endl;
                std::cout<<"back distance :"<<dis[1] <<std::endl;
            }
            else{
                std::cout<<"Current no distance data!"<<std::endl;
            }
            
        }
        else if(choice == 4){
            uint8_t data;
            if( mcu_control_t.getTouchState(data) == true){
                if( (data & 0x01) == 0x01){
                    std::cout<<"Detect touch1 "<<std::endl;
                }
                if( (data & 0x02) == 0x02){
                    std::cout<<"Detect touch2 "<<std::endl;
                }
                if( (data & 0x04) == 0x04){
                    std::cout<<"Detect touch3 "<<std::endl;
                }
            }
            else{
                std::cout<<"not touch"<<std::endl;
            }
            
        }
        else if(choice == 5){
            std::cout<<"请输入舵机控制数值(0-2000)"<<std::endl;
            uint16_t data;
            cin>>data;
            if(data >0 && data <2000){
                mcu_control_t.setGearMotor(data);
            }
            else{
              std::cout << "输入超出范围" << data << std::endl;
            }
            
        }
        else if(choice == 6){
            std::cout<<"设置颜色为黄色常量"<<std::endl;
            mcu_control_t.ControlColorLight(0,255,255,0xffffffff);
        }
        else if(choice == 7){
            std::cout<<"绿色呼吸"<<std::endl;
            mcu_control_t.ControlColorLightBreath(0,0,255,0xffffffff,4000);
        }
        else if(choice == 8){
            std::cout<<"红色闪烁"<<std::endl;
            mcu_control_t.ControlColorLightBlink(0,255,0,0xffffffff,4000);
            
        }
        else if(choice == 9){
            // 获取用户输入的眼灯模式
            std::cout << "请输入眼灯模式(1.呼吸 2.闪烁 3.常亮 4.关闭):";
            int eye_mode;
            std::cin >> eye_mode;

            // 获取用户输入的尾灯模式
            std::cout << "请输入尾灯模式(1.呼吸 2.闪烁 3.常亮 4.流水灯 5.红黄交替闪烁):";
            int tail_mode;
            std::cin >> tail_mode;

            // 获取用户输入的胸灯模式
            std::cout << "请输入胸灯模式(1.关闭 2.打开):";
            int chest_mode;
            std::cin >> chest_mode;

            // 获取用户输入的眼灯周期
            std::cout << "请输入眼灯周期(0-65535ms):";
            int eye_period;
            std::cin >> eye_period;


            // 获取用户输入的尾灯周期
            std::cout << "请输入尾灯周期(0-65535ms):";
            int tail_period;
            std::cin >> tail_period;

            // 获取用户输入的眼灯 RGB 值
            uint32_t eye_rgb; 
            std::cout << "请输入眼灯 RGB 颜色值,以空格区分,范围(0-255): ";
            std::string eye_rgb_input;
            std::getline(std::cin >> std::ws, eye_rgb_input);
            std::vector<uint8_t> eye_rgb_values = string_to_bytes(eye_rgb_input);
            if (eye_rgb_values.size() == 3) {
                std::cout << "Eye RGB Values: ";
                for (uint8_t value : eye_rgb_values) {
                    std::cout << static_cast<int>(value) << " ";
                }
                std::cout << std::endl;
                eye_rgb = eye_rgb_values[0]<<16 | eye_rgb_values[1]<<8 | eye_rgb_values[2] ;
            }

            // 获取用户输入的尾灯 RGB 值
            uint32_t tail_rgb; 
            std::cout << "请输入尾灯 BRG颜色值,以空格区分,范围(0-255): ";
            std::string tail_rgb_input;
            std::getline(std::cin >> std::ws, tail_rgb_input);
            std::vector<uint8_t> tail_rgb_values = string_to_bytes(tail_rgb_input);
            if (tail_rgb_values.size() == 3) {
                std::cout << "Tail RGB Values: ";
                for (uint8_t value : tail_rgb_values) {
                    std::cout << static_cast<int>(value) << " ";
                    tail_rgb = tail_rgb |value;
                }
                std::cout << std::endl;
                tail_rgb = tail_rgb_values[0]<<16 | tail_rgb_values[1]<<8 | tail_rgb_values[2] ;

            }

            mcu_control_t.CB1LightControl((uint8_t)eye_mode, (uint8_t)tail_mode,(uint8_t)chest_mode,(uint16_t)eye_period,(uint16_t)tail_period,(uint32_t)eye_rgb,(uint32_t)tail_rgb);


        }
        else if(choice == 10){
            std::string str=mcu_control_t.getSoftwareVer();
            std::cout<<"current software version:"<<str<<std::endl;            
        }
        else if(choice == 11){
            // 枚举数组
            uint32_t eye_presets[] = {
                kLightEyeON,
                kLightEyeOff,
                kLightEyeBreath,
                kLightEyeTwinkle,
            };
            std::cout<<"Eye control"<<std::endl;
            for (uint32_t preset : eye_presets) {
                std::cout<<std::hex<<"Current preset: 0x"<<preset<<std::endl;
                dddd.PresetEyeLight(preset);
                delay(10000);
            }
            uint32_t flash_presets[] = {
                kLightFlashOn,
                kLightFlashOff
            };
            std::cout<<"Flash control"<<std::endl;
            for (uint32_t preset : flash_presets) {
                std::cout<<std::hex<<"Current preset: 0x"<<preset<<std::endl;
                dddd.PresetFlashLight(preset);
                delay(10000);
            }

            uint32_t tail_presets[] = {

                kLightTailWhiteBrtheSlow,
                kLightTailWhiteBrtheMidle,
                kLightTailWhiteBrtheFast,

                kLightTailCyanFlash,
                kLightTailWhiteFlash,
                kLightTailRedFlash,
                kLightTailRedYewFlash,

                kLightTailCyan,
                kLightTailWhite,
                kLightTailRed,

                kLightTailCyanFlow,
                kLightTailColorFlow,

                kLightTailF1};
            std::cout<<"Tail control"<<std::endl;
            for (uint32_t preset : tail_presets) {
                std::cout<<std::hex<<"Current preset: 0x"<<preset<<std::endl;
                dddd.PresetTailLight(preset);
                delay(10000);
            }

        }
        else if(choice == 12){
            std::cout << "0: 关闭 ,1: 打开"<<std::endl;
            int sub_choice ;
            std::cin >> sub_choice ;
            if( sub_choice == 0){
                dddd.PresetFlashLight(kLightFlashOff);
            }
            else if( sub_choice == 1){
                dddd.PresetFlashLight(kLightFlashOn);
            }          
        }
        else if(choice == 13){
            // dddd.PresetTailLight(kLightTailF1);
            dddd.PresetTailLight(kLightTailB5);

        }  
        else if(choice == 14){
            dddd.PresetEyeLight(kLightEyeBreath);

        }  
        else if(choice == 15){
          dddd.PresetTailLight(kLightTailColorFlow);

        }  
        else if(choice == 16){
          dddd.PresetTailLight(kLightTailWhiteBrtheSlow);

        }  
        else if(choice == 17){
          dddd.PresetTailLight(kLightTailCyanFlash);

        }  
        else if(choice == 18){
            std::cout<<"Input ms: ";
            int aa;
            cin>>aa;
            std::cout<<"Input cnt: ";
            int bb;
            cin>>bb;
            mcu_control_t.stressTest(aa,bb);

            // 枚举数组
            uint32_t eye_presets[] = {
                kLightEyeON,
                kLightEyeOff,
                kLightEyeBreath,
                kLightEyeTwinkle,
            };
            std::cout<<"Eye control"<<std::endl;
            for (uint32_t preset : eye_presets) {
                std::cout<<std::hex<<"Current preset: 0x"<<preset<<std::endl;
                dddd.PresetEyeLight(preset);
                delay(5000);
            }
            uint32_t flash_presets[] = {
                kLightFlashOn,
                kLightFlashOff
            };
            std::cout<<"Flash control"<<std::endl;
            for (uint32_t preset : flash_presets) {
                std::cout<<std::hex<<"Current preset: 0x"<<preset<<std::endl;
                dddd.PresetFlashLight(preset);
                delay(3000);
            }

            uint32_t tail_presets[] = {kLightTailWhiteBrtheSlow,
                                       kLightTailWhiteBrtheMidle,
                                       kLightTailWhiteBrtheFast,

                                       kLightTailCyanFlash,
                                       kLightTailWhiteFlash,
                                       kLightTailRedFlash,
                                       kLightTailRedYewFlash,

                                       kLightTailCyan,
                                       kLightTailWhite,
                                       kLightTailRed,

                                       kLightTailCyanFlow,
                                       kLightTailColorFlow,

                                       kLightTailF1};
            std::cout<<"Tail control"<<std::endl;
            for (uint32_t preset : tail_presets) {
                std::cout<<std::hex<<"Current preset: 0x"<<preset<<std::endl;
                dddd.PresetTailLight(preset);
                delay(5000);
            }

            for(int i=0;i<10;i++){
                std::string str=mcu_control_t.getSoftwareVer();
                std::cout<<"current software version:"<<str<<std::endl;  
                delay(100);
            }

            for(int i=0; i<10; i++){
                mcu_control_t.CB1Eyeblink();
                std::cout<<"Eye blink" << std::endl;
            }

            std::cout<<"getUltraSonicDistance" << std::endl;
            for(int i=0; i<100; i++){
                std::vector<uint16_t> dis = mcu_control_t.getUltraSonicDistance();
                if(dis.size() != 0){
                    std::cout<<"front distance :"<<dis[0] <<std::endl;
                    std::cout<<"back distance :"<<dis[1] <<std::endl;
                }     
                delay(50);  
            }
            
            

        }
        else if(choice == 19){
            mcu_control_t.CB1Eyeblink();
        }
        else{
            std::cout<<"invalid data"<<endl;
        }
    }
}
