#include "SerialPort.hpp"
#include <iostream>
#include <cstring>
#include <fcntl.h>
#include <unistd.h>
#include <cerrno>
#include <sstream>
#include <sys/select.h>

SerialPort::SerialPort(const std::string& device) : fd(-1), deviceName(device) {
    memset(&tty, 0, sizeof tty);
}

SerialPort::~SerialPort() {
    close();
}

bool SerialPort::open() {
    fd = ::open(deviceName.c_str(), O_RDWR | O_NOCTTY | O_SYNC);
    if (fd == -1) {
        std::cerr << "Error opening serial port: " << std::strerror(errno) << std::endl;
        return false;
    }

    // 配置串口
    if (!configurePort()) {
        close();  // 配置失败时关闭串口
        return false;
    }

    return true;
}

void SerialPort::close() {
    if (fd != -1) {
        ::close(fd);
        fd = -1;
    }
}

bool SerialPort::configurePort() {
    if (fd == -1) {
        throw std::runtime_error("Serial port is not opened.");
    }

    // 初始化 termios 结构体
    memset(&tty, 0, sizeof(tty));
    if (tcgetattr(fd, &tty) != 0) {
        std::cerr << "Error getting terminal attributes: " << std::strerror(errno) << std::endl;
        return false;
    }

    // 设置波特率
    cfsetospeed(&tty, B9600);
    cfsetispeed(&tty, B9600);

    // 设置数据位、停止位和奇偶校验
    tty.c_cflag &= ~CSIZE;  // 清除数据位设置
    tty.c_cflag |= CS8;     // 默认设置为 8 数据位
    tty.c_cflag &= ~CSTOPB; // 默认设置为 1 停止位
    tty.c_cflag &= ~PARENB; // 默认不使用奇偶校验

    // 设置超时等待
    tty.c_cc[VTIME] = 50; // 超时秒数, 0.1s的间隔
    tty.c_cc[VMIN] = 0;   // 最小接收字节数

    // 应用配置
    if (tcsetattr(fd, TCSANOW, &tty) != 0) {
        std::cerr << "Error setting terminal attributes: " << std::strerror(errno) << std::endl;
        return false;
    }

    return true;
}

bool SerialPort::setBaudRate(speed_t baudRate) {
    if (fd == -1) {
        std::cerr << "Serial port is not opened." << std::endl;
        return false;
    }

    cfsetospeed(&tty, baudRate);
    cfsetispeed(&tty, baudRate);

    // 应用配置
    if (tcsetattr(fd, TCSANOW, &tty) != 0) {
        std::cerr << "Error setting baud rate: " << std::strerror(errno) << std::endl;
        return false;
    }

    return true;
}

bool SerialPort::setDataBits(int dataBits) {
    if (fd == -1) {
        std::cerr << "Serial port is not opened." << std::endl;
        return false;
    }

    tty.c_cflag &= ~CSIZE;  // 清除数据位设置
    switch (dataBits) {
        case 5: tty.c_cflag |= CS5; break;
        case 6: tty.c_cflag |= CS6; break;
        case 7: tty.c_cflag |= CS7; break;
        case 8: tty.c_cflag |= CS8; break;
        default:
            std::cerr << "Unsupported data bits setting: " << dataBits << std::endl;
            return false;
    }

    // 应用配置
    if (tcsetattr(fd, TCSANOW, &tty) != 0) {
        std::cerr << "Error setting data bits: " << std::strerror(errno) << std::endl;
        return false;
    }

    return true;
}

bool SerialPort::setStopBits(int stopBits) {
    if (fd == -1) {
        std::cerr << "Serial port is not opened." << std::endl;
        return false;
    }

    switch (stopBits) {
        case 1:
            tty.c_cflag &= ~CSTOPB;  // 1 stop bit
            break;
        case 2:
            tty.c_cflag |= CSTOPB;   // 2 stop bits
            break;
        default:
            std::cerr << "Unsupported stop bits setting: " << stopBits << std::endl;
            return false;
    }

    // 应用配置
    if (tcsetattr(fd, TCSANOW, &tty) != 0) {
        std::cerr << "Error setting stop bits: " << std::strerror(errno) << std::endl;
        return false;
    }

    return true;
}

bool SerialPort::setParity(bool enableParity, bool oddParity) {
    if (fd == -1) {
        std::cerr << "Serial port is not opened." << std::endl;
        return false;
    }

    if (enableParity) {
        tty.c_cflag |= PARENB;  // 启用奇偶校验
        if (oddParity) {
            tty.c_cflag |= PARODD;  // 奇校验
        } else {
            tty.c_cflag &= ~PARODD;  // 偶校验
        }
    } else {
        tty.c_cflag &= ~PARENB;  // 禁用奇偶校验
    }

    // 应用配置
    if (tcsetattr(fd, TCSANOW, &tty) != 0) {
        std::cerr << "Error setting parity: " << std::strerror(errno) << std::endl;
        return false;
    }

    return true;
}


 // 设置硬件流控和软件流控
bool SerialPort::setFlowControl(bool enableHardwareFlowControl, bool enableSoftwareFlowControl) {
    if (fd == -1) {
        std::cerr << "Serial port is not opened." << std::endl;
        return false;
    }

    struct termios tty;
    if (tcgetattr(fd, &tty) != 0) {
        std::cerr << "Error getting current serial port attributes." << std::endl;
        return false;
    }

    if (enableHardwareFlowControl) {
        // 启用硬件流控（RTS/CTS）
        tty.c_cflag |= CRTSCTS;
    } else {
        // 禁用硬件流控
        tty.c_cflag &= ~CRTSCTS;
    }

    if (enableSoftwareFlowControl) {
        // 启用软件流控（XON/XOFF）
        tty.c_iflag |= IXON | IXOFF | IXANY;
    } else {
        // 禁用软件流控
        tty.c_iflag &= ~(IXON | IXOFF | IXANY);
    }

    // 应用配置
    if (tcsetattr(fd, TCSANOW, &tty) != 0) {
        std::cerr << "Error setting flow control: " << std::strerror(errno) << std::endl;
        return false;
    }

    return true;
};


bool SerialPort::disableCanonicalMode() {
    if (fd == -1) {
        std::cerr << "Serial port is not opened." << std::endl;
        return false;
    }

    // 获取当前串口配置
    if (tcgetattr(fd, &tty) != 0) {
        std::cerr << "Error from tcgetattr: " << std::strerror(errno) << std::endl;
        return false;
    }

    // 禁用规范模式、回显、信号处理
    tty.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
    tty.c_oflag &= ~OPOST; // 禁用输出处理

    // 应用配置
    if (tcsetattr(fd, TCSANOW, &tty) != 0) {
        std::cerr << "Error setting non-canonical mode: " << std::strerror(errno) << std::endl;
        return false;
    }

    return true;
}

bool SerialPort::setRecvOvertime(int timeoutSeconds, int minBytes) {
    if (fd == -1) {
        std::cerr << "Serial port is not opened." << std::endl;
        return false;
    }

    // 获取当前串口设置
    if (tcgetattr(fd, &tty) != 0) {
        std::cerr << "Error getting current serial port settings: " << std::strerror(errno) << std::endl;
        return false;
    }

    // 设置超时和最小接收字节数
    tty.c_cc[VTIME] = timeoutSeconds; // 超时为0.1秒单位
    tty.c_cc[VMIN] = minBytes;        // 最小接收字节数,最大255

    // 应用串口配置
    if (tcsetattr(fd, TCSANOW, &tty) != 0) {
        std::cerr << "Error setting receive overtime: " << std::strerror(errno) << std::endl;
        return false;
    }

    // 设置阻塞模式
    int flags = fcntl(fd, F_GETFL, 0);
    if (flags == -1) {
        std::cerr << "Error getting file descriptor flags: " << std::strerror(errno) << std::endl;
        return false;
    }

    // 清除 O_NONBLOCK 标志以设置为阻塞模式
    if (fcntl(fd, F_SETFL, flags & ~O_NONBLOCK) == -1) {
        std::cerr << "Error setting file descriptor to blocking mode: " << std::strerror(errno) << std::endl;
        return false;
    }

    return true;
}

bool SerialPort::setNonBlocking() {
    if (fd == -1) {
        std::cerr << "Serial port is not opened." << std::endl;
        return false;
    }

    int flags = fcntl(fd, F_GETFL, 0);
    if (flags == -1) {
        std::cerr << "Failed to get file status flags: " << std::strerror(errno) << std::endl;
        return false;
    }

    // 设置非阻塞标志
    flags |= O_NONBLOCK;
    if (fcntl(fd, F_SETFL, flags) == -1) {
        std::cerr << "Failed to set non-blocking mode: " << std::strerror(errno) << std::endl;
        return false;
    }

    return true;
}


std::string SerialPort::getCurrentConfiguration() {
    if (fd == -1) {
        return "Serial port is not opened.";
    }

    if (tcgetattr(fd, &tty) != 0) {
        return "Error retrieving port configuration: " + std::string(std::strerror(errno));
    }

    std::ostringstream config;

    // 获取并格式化波特率
    auto formatBaudRate = [](speed_t speed) {
        switch (speed) {
            case B9600: return "9600";
            case B19200: return "19200";
            case B38400: return "38400";
            case B57600: return "57600";
            case B115200: return "115200";
            case B230400: return "230400";
            default: return "Unknown";
        }
    };

    speed_t inputSpeed = cfgetispeed(&tty);
    speed_t outputSpeed = cfgetospeed(&tty);

    config << "Input Baud Rate: " << formatBaudRate(inputSpeed) << "\n";
    config << "Output Baud Rate: " << formatBaudRate(outputSpeed) << "\n";

    // 数据位
    config << "Data Bits: ";
    switch (tty.c_cflag & CSIZE) {
        case CS5: config << "5"; break;
        case CS6: config << "6"; break;
        case CS7: config << "7"; break;
        case CS8: config << "8"; break;
        default: config << "Unknown"; break;
    }
    config << "\n";

    // 停止位
    config << "Stop Bits: " << ((tty.c_cflag & CSTOPB) ? "2" : "1") << "\n";

    // 奇偶校验
    config << "Parity: ";
    if (tty.c_cflag & PARENB) {
        if (tty.c_cflag & PARODD) {
            config << "Odd";
        } else {
            config << "Even";
        }
    } else {
        config << "None";
    }
    config << "\n";

    // 硬件流控制
    config << "Hardware Flow Control: " << ((tty.c_cflag & CRTSCTS) ? "Enabled" : "Disabled") << "\n";

    // 软件流控制
    config << "Software Flow Control: " << ((tty.c_iflag & (IXON | IXOFF | IXANY)) ? "Enabled" : "Disabled") << "\n";


    config  << "Configured VMIN: " << static_cast<int>(tty.c_cc[VMIN])  << "\n";
    config  << "Configured VTIME: " << static_cast<int>(tty.c_cc[VTIME])  << "\n";

    return config.str();
}


ssize_t SerialPort::write(const void* buf, size_t len) {
    if (fd == -1 ) {
        std::cerr << "Serial port is not opened " << std::endl;
        return -1;
    }
    return ::write(fd, buf, len);
}

ssize_t SerialPort::read(void* buf, size_t len) {
    if (fd == -1 ) {
        std::cerr << "Serial port is not opened " << std::endl;
        return -1;
    }
    return ::read(fd, buf, len);
}



ssize_t SerialPort::readNonBlocking(void* buf, size_t len, int timeoutMillis) {
    if (fd == -1) {
        std::cerr << "Serial port is not opened" << std::endl;
        return -1;
    }

    // 使用select等待数据准备好
    fd_set readfds;
    FD_ZERO(&readfds);
    FD_SET(fd, &readfds);

    struct timeval timeout;
    timeout.tv_sec = timeoutMillis / 1000;            // 秒部分
    timeout.tv_usec = (timeoutMillis % 1000) * 1000;  // 毫秒部分转换为微秒

    // select等待fd上的数据
    int result = select(fd + 1, &readfds, nullptr, nullptr, &timeout);
    if (result == -1) {
        std::cerr << "Select failed: " << std::strerror(errno) << std::endl;
        return -1;
    } else if (result == 0) {
        // // 超时，没有数据可读
        // std::cerr << "Read timeout." << std::endl;
        return 0;
    }

    // 非阻塞读取数据
    ssize_t bytesRead = ::read(fd, buf, len);
    if (bytesRead == -1 && errno != EAGAIN && errno != EWOULDBLOCK) {
        std::cerr << "Read error: " << std::strerror(errno) << std::endl;
        return -1;
    }

    return bytesRead;
}


void SerialPort::clearInputBuffer(void) {
    if (fd == -1) {
        std::cerr << "Serial port is not opened" << std::endl;
        return;
    }
    if (tcflush(fd, TCIFLUSH) != 0) {
        std::cerr << "Failed to clear input buffer: " << strerror(errno) << std::endl;
    }
}

 




       
