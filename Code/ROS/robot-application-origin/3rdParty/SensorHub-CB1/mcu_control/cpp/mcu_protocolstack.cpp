#include <thread>
#include <sstream>
#include <iomanip> // 用于 std::hex 和 std::setfill

#include "mcu_protocolstack.h"

// 将 uint8_t 数组转换为十六进制字符串
std::string toHexString(const uint8_t* byteArray, size_t length) {
    std::stringstream ss;
    ss << std::hex << std::setfill('0'); // 设置为十六进制输出，并补零
    for (size_t i = 0; i < length; ++i) {
        ss<<"0x" << std::setw(2) << static_cast<int>(byteArray[i]); // 每个字节转换为两位十六进制数
        if (i < length - 1) {
            ss << ","; // 在每个数值后加上逗号
        }
    }
    return ss.str();
}



// 打印时间点
void print_time_point(const std::chrono::time_point<std::chrono::steady_clock>& time_point) {

    auto duration_c = time_point.time_since_epoch();  // 获取从纪元开始到现在的时间持续时间
    auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(duration_c).count(); // 转换为毫秒并获取数值
    DebugPrint( std::dec<< "Current time since epoch in milliseconds: " << milliseconds);
}

// 从队列中获取指定长度的数据
bool getBytesFromQueue(std::deque<uint8_t>& dataQueue, std::vector<uint8_t>& outputBuffer, size_t length) {
    if (dataQueue.size() < length) {
        // 如果队列中的数据不足指定长度，返回 false 表示失败
        return false;
    }

    // 从队列中获取指定长度的数据
    for (size_t i = 0; i < length; ++i) {
        uint8_t byte = dataQueue.front(); // 获取队列的第一个字节
        dataQueue.pop_front(); // 从队列中移除该字节
        outputBuffer.push_back(byte); // 将字节添加到输出缓冲区
    }

    return true; // 成功获取指定长度的数据
}

// 从队列中获取指定长度的数据（非破坏性检查）
bool checkBytesFromQueue(const std::deque<uint8_t>& dataQueue, size_t length) {
    return dataQueue.size() >= length; // 检查队列中的元素数量
}

// 打印队列内容
void printQueue(const std::deque<uint8_t>& queue) {
    std::cout << "Queue contents: ";
    for (const auto& value : queue) {
        std::cout << std::hex << "0x" << static_cast<int>(value) << " "; // 以十六进制打印
    }
    std::cout << std::dec << std::endl; // 恢复回十进制
}



/* Exported types ------------------------------------------------------------*/
typedef struct{
    uint8_t header[2];
    uint8_t frame_id;
    uint8_t version;
    uint8_t type;
    uint8_t func_code;
    uint16_t size;
    uint16_t crc16;
}frameHeader_t;

uint8_t frame_header[HEADER_LEN]={FRAME_HEAD,0x00,0x00,0x16,0x34,0x00,0x00,0x00,0x00};


mcu_ProtocolStack::mcu_ProtocolStack(std::string device)
{
    program_start_time = std::chrono::steady_clock::now();


    this->serial = new SerialPort(device);
    // // 串口设备路径（根据实际情况修改）

    
    // 打开串口
    if (!this->serial->open()) {
        std::cerr << "Failed to open serial port!" << std::endl;
    }

    // 配置串口 || !serial->setRecvOvertime(1, 10)        !serial->setRecvOvertime(0,0)  ||   !serial->setNonBlocking()
    if (!serial->setBaudRate(B115200) ||
        !serial->setDataBits(8) ||
        !serial->setStopBits(1) ||
        !serial->setParity(false, false) ||
        !serial->setFlowControl(false, false)||
        !serial->setRecvOvertime(1,255)||
        !serial->disableCanonicalMode()
        ) {
        
        std::cerr << "Failed to configure serial port!" << std::endl;
        serial->close();
        delete serial;
        throw std::runtime_error("Failed to configure serial port!");
    }


    std::cout <<serial->getCurrentConfiguration() << std::endl;
    serial->clearInputBuffer();

    std::thread thread2([this]() { this->rx_parse(); });
    thread2.detach();

}

mcu_ProtocolStack::~mcu_ProtocolStack()
{
    this->serial->close();
    delete this->serial;
}








void mcu_ProtocolStack::rx_parse(){
    uint8_t header[]={FRAME_HEAD};
    std::deque<uint8_t> serialDataQueue; // 用于存储接收到的串口数据的队列
    std::vector<uint8_t> frameBuffer;
    frameHeader_t fh_t;
    DebugPrint("Enter rx_parse");

    
    while(1){ 

        int readlen = serial->read(rxbuffer,RXBUFFERLEN) ;
        // DebugPrint("Recv data frame: "<<toHexString(rxbuffer, readlen) );
        if(readlen > 0) {
            for (ssize_t i = 0; i < readlen; ++i) {
                serialDataQueue.push_back(rxbuffer[i]);
            }
            // printQueue(serialDataQueue);
        }
       
        
        while (!serialDataQueue.empty()) {

            // 检查是否有至少2个字节来匹配帧头
            if (!checkBytesFromQueue(serialDataQueue, 2)) {
                DebugPrint("Frame head size !=2" );
                break; // 不足2字节，退出解析等待更多数据
            }
            // 先提取帧头
            frameBuffer.clear(); // 清空frameBuffer，准备接收新帧
            getBytesFromQueue(serialDataQueue, frameBuffer, 2);
            // 检查帧头
            if (frameBuffer[0] == header[0] && frameBuffer[1] == header[1]) {
                // 如果帧头匹配，继续获取剩余的帧头部分
                if(!checkBytesFromQueue(serialDataQueue, 8) ){
                    // DebugPrint("frameBuffer head frame 8 failed,quit");
                    serialDataQueue.push_front( header[1]);
                    serialDataQueue.push_front( header[0]);
                    break;
                }
                getBytesFromQueue(serialDataQueue, frameBuffer, 8);
                // 解析帧头
                fh_t.frame_id = frameBuffer[FRAME_ID_BIAS];
                fh_t.version = frameBuffer[FRAME_VERSION_BIAS];
                fh_t.type = frameBuffer[FRAME_TYPE];
                fh_t.func_code = frameBuffer[FRAME_FUNC_CODE];
                fh_t.size = (frameBuffer[FRAME_DATA_SIZE_BIAS] << 8) | frameBuffer[FRAME_DATA_SIZE_BIAS + 1];
                fh_t.crc16 = (frameBuffer[FRAME_CRC_BIAS] << 8) | frameBuffer[FRAME_CRC_BIAS + 1];

                // DebugPrint("fh_t.size: " << std::dec << fh_t.size);

                // 检查是否有足够的数据来解析帧数据
                if (!checkBytesFromQueue(serialDataQueue, fh_t.size)) {
                    DebugPrint("frameBuffer frame data length not enough");
                    // 将帧头保留在队列中
                    serialDataQueue.push_back(frameBuffer[0]);
                    serialDataQueue.push_back(frameBuffer[1]);
                    for (size_t i = 2; i < frameBuffer.size(); ++i) {
                        serialDataQueue.push_back(frameBuffer[i]); // 将帧头的剩余部分也保留
                    }
                    break; // 数据不足，退出解析等待更多数据
                }
                getBytesFromQueue(serialDataQueue, frameBuffer, fh_t.size);

                // 处理不同类型的帧
                if (fh_t.type == 0x15) {
                    
                    noack_request_notify_struct_t temp;
                    temp.func_code = fh_t.func_code;
                    temp.current_time = std::chrono::steady_clock::now();
                    temp.notify_queue_size = fh_t.size;
                    temp.notify_queue = new uint8_t[fh_t.size];
                    memcpy(temp.notify_queue, &frameBuffer[FRAME_DATA_BIAS], fh_t.size);
                    noack_request_notify_struct_enqueue(temp);

                    // DebugPrint("Recv 0x15 frame: "<<toHexString(temp.notify_queue, temp.notify_queue_size) );
                } else if (fh_t.type == 0x16) {
                    frame_header[FRAME_ID_BIAS] = fh_t.frame_id;
                    frame_header[FRAME_TYPE] = fh_t.type;
                    frame_header[FRAME_FUNC_CODE] = fh_t.func_code;
                    frame_header[FRAME_DATA_SIZE_BIAS] = 0;
                    frame_header[FRAME_DATA_SIZE_BIAS + 1] = 0;
                    ssize_t bytes_sent = serial->write(frame_header, 10);
                    if (bytes_sent < 0) {
                        std::cerr << "Error writing to serial port to ack" << std::endl;
                    } else {
                        ack_request_notify_struct temp;
                        temp.current_time = std::chrono::steady_clock::now();
                        temp.func_code = fh_t.func_code;
                        temp.notify_queue = new uint8_t[fh_t.size];
                        memcpy(temp.notify_queue, &frameBuffer[FRAME_DATA_BIAS], fh_t.size);
                        temp.notify_queue_size = fh_t.size;
                        ack_request_notify_struct_enqueue(temp);
                    }
                } else if (fh_t.type == 0x17) {
                    data_request_notify_struct_t temp;
                    temp.current_time = std::chrono::steady_clock::now();
                    temp.func_code = fh_t.func_code;
                    temp.frame_id = fh_t.frame_id;
                    temp.notify_queue = new uint8_t[fh_t.size];
                    memcpy(temp.notify_queue, &frameBuffer[FRAME_DATA_BIAS], fh_t.size);
                    temp.notify_queue_size = fh_t.size;
                    data_request_notify_struct_enqueue(temp);
                } else if (fh_t.type == 0x21) {
                    DebugPrint("Recv 0x21 frame");
                    if (fh_t.size != 0) {
                        reply_data_notify_t temp;
                        temp.current_time = std::chrono::steady_clock::now();
                        temp.func_code = fh_t.func_code;
                        temp.frame_id = fh_t.frame_id;
                        temp.notify_queue_size = fh_t.size;
                        temp.notify_queue = new uint8_t[fh_t.size];
                        memcpy(temp.notify_queue, &frameBuffer[FRAME_DATA_BIAS], fh_t.size);
                        reply_data_notify_enqueue(temp);
                    } else {
                        ack_notify_struct_t temp;
                        temp.current_time = std::chrono::steady_clock::now();
                        temp.func_code = fh_t.func_code;
                        temp.frame_id = fh_t.frame_id;
                        ack_notify_enqueue(temp);
                    }
                }

                // 清空frameBuffer，准备解析下一帧
                frameBuffer.clear();                
            }
            else{
                // std::cout<<"*******************************************************"<<std::endl;
                // std::cout<<"Clear Not Frame Head Format Data: "<<std::endl;
                // std::cout<<std::hex<<"0x"<<static_cast<int>(frameBuffer[0])<<","<<"0x"<<static_cast<int>(frameBuffer[1])<<std::endl;
                // std::cout<<"*******************************************************"<<std::endl;
                serialDataQueue.push_front( frameBuffer[1]);

            }

        }
    }
        
}



// ack_notify 入队
bool mcu_ProtocolStack::ack_notify_enqueue(ack_notify_struct_t t) {
    std::lock_guard<std::mutex> lock(ack_mtx);

    // 如果队列已满，删除最早的数据
    if (ack_notify_struct_queue.size() >= max_queue_size) {
        ack_notify_struct_queue.erase(ack_notify_struct_queue.begin());
    }

    // 添加新数据
    ack_notify_struct_queue.push_back(t);
    return true;
}

// reply_data_notify 入队
bool mcu_ProtocolStack::reply_data_notify_enqueue(reply_data_notify_t t) {
    std::lock_guard<std::mutex> lock(reply_data_mtx);

    // 如果队列已满，删除最早的数据
    if (reply_data_notify_queue.size() >= max_queue_size) {
        reply_data_notify_queue.erase(reply_data_notify_queue.begin());
    }

    // 添加新数据
    reply_data_notify_queue.push_back(t);
    return true;
}



// data_request_notify_struct 入队
bool mcu_ProtocolStack::data_request_notify_struct_enqueue(data_request_notify_struct_t t) {
    std::lock_guard<std::mutex> lock(data_request_mtx);

    // 如果队列已满，删除最早的数据
    if (data_request_notify_struct_queue.size() >= max_queue_size) {
        data_request_notify_struct_queue.erase(data_request_notify_struct_queue.begin());
    }

    // 添加新数据
    data_request_notify_struct_queue.push_back(t);
    return true;
}

bool mcu_ProtocolStack::ack_request_notify_struct_enqueue(ack_request_notify_struct_t t){
    std::lock_guard<std::mutex> lock(ack_request_mtx);

    // 如果队列已满，删除最早的数据
    if (ack_request_notify_struct_queue.size() >= max_queue_size) {
        ack_request_notify_struct_queue.erase(ack_request_notify_struct_queue.begin());
    }

    // 添加新数据
    ack_request_notify_struct_queue.push_back(t);
    return true;
}

// noack_request_notify 入队
bool mcu_ProtocolStack::noack_request_notify_struct_enqueue(noack_request_notify_struct_t t) {
    std::lock_guard<std::mutex> lock(noack_request_mtx);

    // 如果队列已满，删除最早的数据
    if (noack_request_notify_struct_queue.size() >= max_queue_size) {
        noack_request_notify_struct_queue.erase(noack_request_notify_struct_queue.begin());
    }

    // 添加新数据
    noack_request_notify_struct_queue.push_back(t);
    return true;
}



uint8_t mcu_ProtocolStack::sendData(FuncCode frame_type, uint8_t func_code, uint16_t len, uint8_t *dat)
{
    static uint8_t frame_id = 0;  


    uint8_t current_frame_id = frame_id;      

    frame_header[FRAME_ID_BIAS]  = frame_id++;
    frame_header[FRAME_TYPE]  = frame_type;
    frame_header[FRAME_FUNC_CODE]  = func_code;
    frame_header[FRAME_DATA_SIZE_BIAS]  = len >> 8;
    frame_header[FRAME_DATA_SIZE_BIAS+1]  = len ;
    memcpy( (frame_header+FRAME_DATA_BIAS), dat, len);
    ssize_t bytes_sent = serial->write( frame_header, 10+len);
    if (bytes_sent < 0) {
        std::cerr << "Error writing to serial port" << std::endl;
    }
    else{
        frame_mgt_t *frame = new frame_mgt_t;
        frame->func_code = func_code;
        frame->frame_id = current_frame_id;
        tx_frame_queue.push_back(*frame);
        delete frame;
        frame = nullptr;
        std::string str = toHexString(frame_header, 10+len);
        DebugPrint("Send Hex date :"<<str);

    }
    return current_frame_id;
}





// 抽象函数：遍历容器并查找指定 frame_id 的元素
int mcu_ProtocolStack::findFrameById(std::vector<frame_mgt_t>& cmd_queue, uint8_t target_frame_id) {
    // 使用传统迭代器遍历容器
    for (auto it = cmd_queue.begin(); it != cmd_queue.end(); ++it) {
        std::cout << "Frame ID: " << static_cast<int>(it->frame_id) << std::endl;
        
        if (it->frame_id == target_frame_id) {
            // 直接返回迭代器与 begin 的距离
            return static_cast<int>(std::distance(cmd_queue.begin(), it));
        }
    }
    return -1;
}

int mcu_ProtocolStack::findFrameByFuncCode(std::vector<frame_mgt_t>& cmd_queue, uint8_t target_func_code)
{
    // 使用传统迭代器遍历容器
    for (auto it = cmd_queue.end(); it != cmd_queue.begin(); --it) {
        std::cout << "Checking Frame ID: " << static_cast<int>(it->frame_id) << std::endl;
        
        if (it->frame_id == target_func_code) {
            return static_cast<int>(std::distance(cmd_queue.begin(), it));
        }
    }
    
     return -1; // 没有找到指定 frame_id 的元素
}
int mcu_ProtocolStack::findRTXRemap(uint8_t func_code){
    int index = findFrameByFuncCode(tx_frame_queue, 0x33);
    if(index){
        int sub_index = findFrameById(rx_frame_queue,tx_frame_queue[index].frame_id) ;
        if( sub_index ){
            return sub_index;
        }
    }
    return -1;
}


bool mcu_ProtocolStack::is_reply_ack(uint8_t func_code, querystruct_t &temp){
    std::lock_guard<std::mutex> lock(ack_mtx);
    for (auto it = ack_notify_struct_queue.begin(); it != ack_notify_struct_queue.end(); ++it) {
        for (auto it_tx = tx_frame_queue.begin(); it_tx != tx_frame_queue.end(); ++it_tx){
            if (it->func_code == func_code && it->frame_id == it_tx->frame_id) { // 假设 ack_notify_struct_t 结构中有 frame_id
                ack_notify_struct_t t = *it;
                temp.current_time = t.current_time;
      
                ack_notify_struct_queue.erase(it);
                return true;
            }
        }
        
    }
    return false;
}
bool mcu_ProtocolStack::is_reply_data(uint8_t func_code, querystruct_t &temp){
    std::lock_guard<std::mutex> lock(reply_data_mtx);
    for (auto it = reply_data_notify_queue.begin(); it != reply_data_notify_queue.end(); ++it) {
        for (auto it_tx = tx_frame_queue.begin(); it_tx != tx_frame_queue.end(); ++it_tx){
            if (it->func_code == it_tx->func_code && it->frame_id == it_tx->frame_id) { // 假设 reply_data_notify_t 结构中有 frame_id
                reply_data_notify_t t = *it;

                memcpy(temp.data, t.notify_queue, t.notify_queue_size);
                temp.size = t.notify_queue_size;
                temp.current_time = t.current_time;

                reply_data_notify_queue.erase(it);
                tx_frame_queue.erase(it_tx);
                return true;
            }

        }
        
    }
    return false;
}
bool mcu_ProtocolStack::is_data_request(uint8_t frame_id, uint8_t func_code, querystruct_t &temp){
    std::lock_guard<std::mutex> lock(data_request_mtx);
    for (auto it = data_request_notify_struct_queue.begin(); it != data_request_notify_struct_queue.end(); ++it) {
        if (it->func_code == func_code && it->frame_id == frame_id) { // 假设 data_request_notify_struct_t 结构中有 frame_id
            data_request_notify_struct_t t = *it;
            memcpy(t.notify_queue, temp.data, temp.size);
            temp.size = t.notify_queue_size;
            temp.current_time = t.current_time;
            data_request_notify_struct_queue.erase(it);
            return true;
        }
    }
    
    return false;
}

bool mcu_ProtocolStack::is_noack_data_request(uint8_t func_code, querystruct_t &temp) {
    std::lock_guard<std::mutex> lock(noack_request_mtx);
    // 修正逆序遍历的迭代器
    for (auto it = noack_request_notify_struct_queue.rbegin(); it != noack_request_notify_struct_queue.rend(); ++it) {
        if (it->func_code == func_code) {
            noack_request_notify_struct_t t = *it;
            
            // 检查目标缓冲区大小是否足够
            if (t.notify_queue_size > sizeof(temp.data)) {
                std::cerr << "Error: buffer overflow risk. notify_queue_size exceeds temp.data size." << std::endl;
                return false;
            }

            // 执行内存拷贝操作
            memcpy(temp.data, t.notify_queue, t.notify_queue_size);
            temp.size = t.notify_queue_size;
            // std::cout << "t.notify_queue_size: " << t.notify_queue_size << std::endl;
            temp.current_time = t.current_time;

            // 正确删除元素
            noack_request_notify_struct_queue.erase(std::next(it).base());
            return true;
        }
    }
    return false;
}



STEADY_CLOCK mcu_ProtocolStack::getProgramRunTime(){
    return program_start_time;
}





// 打印队列值
void mcu_ProtocolStack::print_ack_notify_struct_queue() {
    std::cout << "ack_notify_struct_queue:\n";
    for (const auto& item : ack_notify_struct_queue) {
        std::cout << "func_code: 0x" << std::hex << static_cast<int>(item.func_code) 
                  << ", frame_id: " << static_cast<int>(item.frame_id) 
                  << ", current_time: ";
        print_time_point(item.current_time);
    }
    std::cout << std::endl;
}

// 打印 reply_data_notify_queue
void mcu_ProtocolStack::print_reply_data_notify_queue() {
    std::cout << "reply_data_notify_queue:\n";
    for (const auto& item : reply_data_notify_queue) {
        std::cout << "func_code: 0x" << std::hex << static_cast<int>(item.func_code)
                  << ", frame_id: " << static_cast<int>(item.frame_id) 
                  << ", notify_queue_size: " << item.notify_queue_size
                  << ", notify_queue:"<<toHexString(item.notify_queue,item.notify_queue_size)
                  << ", current_time: ";
        print_time_point(item.current_time);
    }
    std::cout << std::endl;
}

// 打印 data_request_notify_struct_queue
void mcu_ProtocolStack::print_data_request_notify_struct_queue() {
    std::cout << "data_request_notify_struct_queue:\n";
    for (const auto& item : data_request_notify_struct_queue) {
        std::cout << "func_code: 0x" << std::hex << static_cast<int>(item.func_code) 
                  << ", frame_id: " << static_cast<int>(item.frame_id) 
                  << ", notify_queue_size: " << item.notify_queue_size 
                  << ", notify_queue:"<<toHexString(item.notify_queue,item.notify_queue_size)
                  << ", current_time: ";
        print_time_point(item.current_time);
    }
    std::cout << std::endl;
}

// 打印 noack_request_notify_struct_queue
void mcu_ProtocolStack::print_noack_request_notify_struct_queue() {
    std::cout << "noack_request_notify_struct_queue:\n";
    for (const auto& item : noack_request_notify_struct_queue) {
        std::cout << "func_code: 0x" << std::hex << static_cast<int>(item.func_code) 
                  << ", notify_queue_size: " << item.notify_queue_size 
                  << ", notify_queue:"<<toHexString(item.notify_queue,item.notify_queue_size)
                  << ", current_time: ";
                  print_time_point(item.current_time);
        //  // 输出时间差的秒级和毫秒级信息
        // auto duration_s = std::chrono::duration_cast<std::chrono::duration<double>>(item.current_time);
        // std::cout << "print Duration (seconds): " << duration_s.count() << " s" << std::endl;

        // auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(item.current_time);
        // std::cout << std::dec << "print Duration (milliseconds): " << duration_ms.count() << " ms" << std::endl;
    }
    std::cout << std::endl;
}