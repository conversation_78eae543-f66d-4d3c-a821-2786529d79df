#include <fstream>
#include <iostream>
#include <vector>

#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>

#include <chrono>
#include <thread>

#include "mcu_control.h"



void delay(unsigned milliseconds) {
    std::this_thread::sleep_for(std::chrono::milliseconds(milliseconds));
}





// 获取文件路径中的文件名
std::string getFileNameFromPath(const std::string& filepath){
    size_t pos = filepath.find_last_of("/\\");
    if (pos == std::string::npos) {
        // 如果没有找到斜杠，说明整个路径就是文件名
        return filepath;
    }
    return filepath.substr(pos + 1);
}



class mcu_Control::Impl {
public:
    mcu_ProtocolStack* pmcu_ProtocolStack_t;

    Impl(const std::string& device) {
        // 初始化 mcu_ProtocolStack 对象
        pmcu_ProtocolStack_t = new mcu_ProtocolStack(device);
    }

    ~Impl() {
        // 释放 mcu_ProtocolStack 对象
        delete pmcu_ProtocolStack_t;
    }
};


mcu_Control::mcu_Control(std::string device) : pImpl(new Impl(device)) {}

mcu_Control::~mcu_Control() {
    delete pImpl;
}


void mcu_Control::mcuFmUpdate(const std::string& filepath)
{
    // 打开文件
    std::ifstream file(filepath, std::ios::binary);
    if (!file) {
        std::cerr << "Error opening file: " << filepath << std::endl;
        return;
    }

    // 读取文件内容到 vector
    std::vector<uint8_t> buffer((std::istreambuf_iterator<char>(file)), {});
    size_t total_size = buffer.size();
    size_t sent = 0;
    size_t chunk_size = 128;
    uint16_t bias = 0;
    querystruct_t temp;
    // 循环发送数据，直到所有数据都发送完
    while (sent < total_size) {
        size_t to_send = std::min(chunk_size, total_size - sent);

        // 创建发送头信息，长度为4字节的固定数据
        uint8_t send_bytes[4] = {0xee, 0xee, 0x00, 0x00};

        // 设置偏移值
        send_bytes[2] = (bias >> 8) & 0xFF;
        send_bytes[3] = bias & 0xFF;

        // 从 buffer 中提取需要发送的数据
        std::vector<uint8_t> data_chunk(buffer.begin() + sent, buffer.begin() + sent + to_send);

        // 拼接发送数据，将头信息和数据块拼接
        std::vector<uint8_t> combined_vector(send_bytes, send_bytes + sizeof(send_bytes));
        combined_vector.insert(combined_vector.end(), data_chunk.begin(), data_chunk.end());

        // 调用 sendData 函数发送拼接后的数据包
        pImpl->pmcu_ProtocolStack_t->sendData(ACK_REQUEST, 0x34, combined_vector.size(), combined_vector.data());
        
        int cnt;
        for(cnt=0; cnt < 10; cnt++){    
            delay(100);
            pImpl->pmcu_ProtocolStack_t->print_ack_notify_struct_queue();
            // 等待ACK确认
            if (!pImpl->pmcu_ProtocolStack_t->is_reply_ack(0x34, temp) ) {
                std::cerr << "No."<<cnt<<",Failed to receive ACK for data chunk at bias: " << bias << std::endl;
                pImpl->pmcu_ProtocolStack_t->sendData(ACK_REQUEST, 0x34, combined_vector.size(), combined_vector.data());
                delay(1000);
            }
            else{
                break;
            }
        }
        if(cnt == 10){
            std::cerr << "OTA error ,quit!" <<std::endl;
            return ;
        }

        

        // 更新已发送数据大小和偏移
        sent += to_send;
        bias++;
    }

    // 提取文件名
    std::string filename = getFileNameFromPath(filepath);

    // 将文件名转换为字节数组形式
    std::vector<uint8_t> filename_bytes(filename.begin(), filename.end());

    // 创建发送文件名的数据包
    {
        uint8_t send_bytes[2] = {0xff, 0xfe};
        std::vector<uint8_t> combined_vector(send_bytes, send_bytes + sizeof(send_bytes));
        combined_vector.insert(combined_vector.end(), filename_bytes.begin(), filename_bytes.end());  // 添加文件名

        pImpl->pmcu_ProtocolStack_t->sendData(ACK_REQUEST, 0x34, combined_vector.size(), combined_vector.data());
        for(int cnt=0; cnt<3; cnt++){    
            delay(100);
            if (!pImpl->pmcu_ProtocolStack_t->is_reply_ack(0x34, temp) ) {
                std::cerr << "Failed to modify filename" << std::endl;
            }
            else{
                std::cout << "Modify filename successfully!" << std::endl;
                break;
            }
        }
    }

    // 发送固件大小
    {
        uint8_t send_bytes[6] = {0xff, 0xfd, 0x00, 0x00, 0x00, 0x00};
        // 文件大小的高字节向前放
        for (uint8_t i = 0; i < 4; i++) {
            send_bytes[5 - i] = static_cast<uint8_t>(total_size >> (i * 8));
        }
        pImpl->pmcu_ProtocolStack_t->sendData(ACK_REQUEST, 0x34, sizeof(send_bytes), send_bytes);
        for(int cnt=0; cnt<3; cnt++){    
            delay(100);
            if (!pImpl->pmcu_ProtocolStack_t->is_reply_ack(0x34, temp) ) {
                std::cerr << "Failed to modify filesize" << std::endl;
            }
            else{
                std::cout << "Modify filesize successfully!" << std::endl;
                break;
            }
        }
        
    }

    // 发送完成标志
    {
        uint8_t send_bytes[2] = {0xff, 0xff};
        pImpl->pmcu_ProtocolStack_t->sendData(ACK_REQUEST, 0x34, sizeof(send_bytes), send_bytes);
        for(int cnt=0; cnt<3; cnt++){    
            delay(100);
            if (!pImpl->pmcu_ProtocolStack_t->is_reply_ack(0x34, temp) ) {
                std::cerr << "Failed to transmit sign" << std::endl;
            }
            else{
                std::cout << "Modify transmit sign successfully!" << std::endl;
                break;
            }
        }
    }
}




bool mcu_Control::is_realtime(STEADY_CLOCK time){
    
   

    auto cat_duration_c = time.time_since_epoch();  // 获取从纪元开始到现在的时间持续时间
    auto cat_milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(cat_duration_c).count(); // 转换为毫秒并获取数值
    std::cout << std::dec<< "cat_duration_c Current time since epoch in milliseconds: " << cat_milliseconds << std::endl;

    STEADY_CLOCK current_time = std::chrono::steady_clock::now();
    auto duration_c = current_time.time_since_epoch();  // 获取从纪元开始到现在的时间持续时间
    auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(duration_c).count(); // 转换为毫秒并获取数值
    std::cout << std::dec<< "Current time since epoch in milliseconds: " << milliseconds << std::endl;

    auto duration = current_time - time;
    // 输出时间差的秒级和毫秒级信息
    auto duration_s = std::chrono::duration_cast<std::chrono::duration<double>>(duration);
    std::cout << "Duration (seconds): " << duration_s.count() << " s" << std::endl;

    auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(duration);
    std::cout << std::dec << "Duration (milliseconds): " << duration_ms.count() << " ms" << std::endl;


    

    if(duration_ms.count() < 200){
        std::cout<<"TRUE"<<std::endl;
        return true;
    } 
    return false;
}


void mcu_Control::mcuReset(void)
{
    pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST, 0x30, 0, nullptr);
}


void mcu_Control::setFloodLight(uint16_t data)
{
    uint8_t dt[2]={0};
    if(data>0 && data<100){
        dt[0] = data>>8;
        dt[1] = data;
        pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST,0x31,2,dt);
    }
    else{
        std::cout<<"请输入0-100的数值!"<<std::endl;
    }
}

void mcu_Control::setGearMotor(uint16_t data)
{
    uint8_t dt[2]={0};
    if(data>=0 && data<=2000){
        dt[0] = data>>8;
        dt[1] = data;
        pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST, 0x32, 2, dt);
    }
    else{
        std::cout<<"请输入0-2000的数值!"<<std::endl;
    }
    
}

void mcu_Control::ControlColorLight(uint8_t blue,uint8_t red,uint8_t green,uint32_t enable)
{
    uint8_t dt[8]={0};
    dt[0] = blue;
    dt[1] = red;
    dt[2] = green;
    dt[4] = enable & 0xff;
    dt[5] = (enable>>8) & 0xff;
    dt[6] = (enable>>16) & 0xff;
    dt[7] = (enable>>24) & 0xff;
    pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST, 0x31, sizeof(dt), dt);
}

void mcu_Control::ControlColorLightBreath(uint8_t blue,uint8_t red,uint8_t green,uint32_t enable,uint16_t duty)
{
    uint8_t dt[11]={0};
    dt[0] = blue;
    dt[1] = red;
    dt[2] = green;

    dt[4] = enable & 0xff;
    dt[5] = (enable>>8) & 0xff;
    dt[6] = (enable>>16) & 0xff;
    dt[7] = (enable>>24) & 0xff;
    dt[8] = 1;
    dt[9] = (duty>>8) & 0xff;
    dt[10] = duty & 0xff;
    pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST, 0x31, sizeof(dt), dt);
}


void mcu_Control::ControlColorLightBlink(uint8_t blue,uint8_t red,uint8_t green,uint32_t enable,uint16_t duty)
{
    uint8_t dt[11]={0};
    dt[0] = blue;
    dt[1] = red;
    dt[2] = green;

    dt[4] = enable & 0xff;
    dt[5] = (enable>>8) & 0xff;
    dt[6] = (enable>>16) & 0xff;
    dt[7] = (enable>>24) & 0xff;
    dt[8] = 2;
    dt[9] = (duty>>8) & 0xff;
    dt[10] = duty & 0xff;
    pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST, 0x31, sizeof(dt), dt);
}

void mcu_Control::CB1LightControl(uint8_t eye_mode, uint8_t tail_mode,uint8_t flood_mode,uint16_t eye_duty,uint16_t tail_duty,uint32_t eye_rgb,uint32_t tail_rgb)
{
    uint8_t dt[13]={0};
    dt[0] = eye_mode;
    dt[1] = tail_mode;
    dt[2] = flood_mode;

    dt[3] = eye_duty>>8;
    dt[4] = eye_duty;

    dt[5] = tail_duty>>8;
    dt[6] = tail_duty;

    dt[7] =  eye_rgb;
    dt[8] =  eye_rgb>>16;
    dt[9] =  eye_rgb>>8;

    dt[10] =  tail_rgb;  //b
    dt[11] =  tail_rgb>>16, //r
    dt[12] =  tail_rgb>>8, //g
     
    
    
    pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST, 0x37, sizeof(dt), dt);
}

bool mcu_Control::getTouchState(uint8_t &state){
    // pImpl->pmcu_ProtocolStack_t->print_noack_request_notify_struct_queue(); 

    querystruct_t temp;
    if(pImpl->pmcu_ProtocolStack_t->is_noack_data_request(0x35,temp) == true){
        // std::cout<<"touch judge"<<std::endl;
        state = temp.data[0] ; 
        return is_realtime(temp.current_time);
    }
    return false;
    

}


std::vector<uint16_t> mcu_Control::getUltraSonicDistance(){
    static STEADY_CLOCK last_record_time = std::chrono::steady_clock::now();

    STEADY_CLOCK current_time = std::chrono::steady_clock::now();
    auto duration = current_time - last_record_time;

    auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(duration);

    querystruct_t temp;
    std::vector<uint16_t> ret;
    if(pImpl->pmcu_ProtocolStack_t->is_reply_data(0x33,temp) ){
        uint16_t front_dis = temp.data[0] << 8 |  temp.data[1];
        uint16_t back_dis = temp.data[2] << 8 |  temp.data[3];      
        ret.push_back(front_dis);
        ret.push_back(back_dis);
    }
    else if(duration_ms.count() >100){
        // std::cout<<"Current no distance data!"<<std::endl;
        pImpl->pmcu_ProtocolStack_t->sendData(RESPONSE_WITH_CONTENT, 0x33, 0, nullptr);
        last_record_time = current_time;
        
        
    }
    return ret;  
}


std::string mcu_Control::getSoftwareVer(){
    querystruct_t temp;
    pImpl->pmcu_ProtocolStack_t->sendData(RESPONSE_WITH_CONTENT, 0x38, 0, nullptr);
    delay(500);
    // pImpl->pmcu_ProtocolStack_t->print_reply_data_notify_queue(); 
    if(pImpl->pmcu_ProtocolStack_t->is_reply_data(0x38,temp) == true){
        // DebugPrint(std::dec<<"size:"<<temp.size);
        // DebugPrint("data:"<<toHexString(temp.data, temp.size)); 
        std::string str((char*)temp.data);
        return str;
    }
    return "Get failed !";
    
}




void mcu_Control::stressTest(int time,int cnt){
    querystruct_t temp;
    int success_cnt =0;
    int fail_cnt = 0;
    for(int i=0;i<cnt;i++){
        pImpl->pmcu_ProtocolStack_t->sendData(ACK_REQUEST, 0x50, 0, nullptr);

        if(pImpl->pmcu_ProtocolStack_t->is_reply_ack(0x50,temp) == true){
            std::cout<<"Stress Test successfully!"<<std::endl;
            success_cnt ++;
        }
        else{
            std::cout<<"Stress Test failed!"<<std::endl;
            fail_cnt ++;
        }
        delay(time);
    }
    std::cout<<"success cnt : "<<static_cast<int>(success_cnt)\
             <<",fail cnt : "<<static_cast<int>(fail_cnt) <<std::endl;
}



void mcu_Control::CB1Eyeblink(void){
    static bool sign = false;
    if(!sign){
        sign = true;
        std::thread myThread([this]() {
            CB1LightControl(2, 0, 0, 1, 0, 0, 0);
            // // 执行指令1
            // std::cout << "Executing command 1" << std::endl;
            // 模拟耗时操作
            delay(100);
            CB1LightControl(1, 0, 0, 1, 0, 0x00FFE0, 0);
            sign = false;
        });
        // 分离线程
        myThread.detach();
    }
    

}