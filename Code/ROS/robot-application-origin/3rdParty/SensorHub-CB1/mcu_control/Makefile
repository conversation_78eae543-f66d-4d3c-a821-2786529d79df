# Cross-compiler settings for ARM (default is x86)
# ARCH ?= x86  # 默认使用 x86 编译，可以通过命令行传递 ARCH=arm 来切换到交叉编译
ARCH = arm64

ifeq ($(ARCH), arm)
    # Cross-compiler settings for ARM (AArch32)
    CC = arm-linux-gnueabihf-gcc
    CXX = arm-linux-gnueabihf-g++
    CFLAGS = -Wall -march=armv7-a -mfloat-abi=hard -mfpu=vfpv3-d16 
    CXXFLAGS = -Wall -march=armv7-a -mfloat-abi=hard -mfpu=vfpv3-d16 -lstdc++
else ifeq ($(ARCH), arm64)
    # Cross-compiler settings for AArch64
    CC = aarch64-linux-gnu-gcc
    CXX = aarch64-linux-gnu-g++
    CFLAGS = -Wall -march=armv8-a -fPIC -g 
    CXXFLAGS = -Wall -march=armv8-a -fPIC -g -lstdc++
else
    # x86 compile (default)
    CC = gcc
    CXX = g++
    CFLAGS = -Wall -fPIC 
    CXXFLAGS = -Wall -fPIC -pthread -lstdc++
endif

# Linker flags for shared library
LDFLAGS.shared = -shared

# Linker flags for executable
LDFLAGS.exec = -no-pie -pthread # 添加 -no-pie 以确保生成普通可执行文件

# Directories
SRC_DIR = c
CPP_DIR = cpp
INC_DIR = inc
LIB_DIR = output/lib/
OBJ_DIR = output/obj/
BIN_DIR = output/bin/

# File extensions
C_SRCS = $(wildcard $(SRC_DIR)/*.c)
CPP_SRCS = $(wildcard $(CPP_DIR)/*.cpp)
C_OBJS = $(patsubst $(SRC_DIR)/%.c, $(OBJ_DIR)/%.o, $(C_SRCS))
CPP_OBJS = $(patsubst $(CPP_DIR)/%.cpp, $(OBJ_DIR)/%.o, $(CPP_SRCS))
OBJS = $(C_OBJS) $(CPP_OBJS)

# Output files
EXEC = $(BIN_DIR)/mcu_control
LIB_SHARED = $(LIB_DIR)/libsensorhub.so
LIB_STATIC = $(LIB_DIR)/libsensorhub.a

# Object files without main
LIB_OBJS = $(filter-out $(OBJ_DIR)/%main.o, $(OBJS))

# Default target
all: clean dirs $(LIB_SHARED) $(LIB_STATIC) $(EXEC) 

exe: dirs $(EXEC)

# Create necessary directories
dirs:
	@mkdir -p $(OBJ_DIR) $(LIB_DIR) $(BIN_DIR)

# Rule to create .o files from .c files
$(OBJ_DIR)/%.o: $(SRC_DIR)/%.c
	$(CC) $(CFLAGS) -I$(INC_DIR) -c $< -o $@

# Rule to create .o files from .cpp files
$(OBJ_DIR)/%.o: $(CPP_DIR)/%.cpp
	$(CXX) $(CXXFLAGS) -I$(INC_DIR) -c $< -o $@

# Link executable including objects with main function
$(EXEC): $(OBJS)
	$(CXX) $(OBJS) $(LDFLAGS.exec) -L$(LIB_DIR) -static-libstdc++ -lsensorhub -o $@  

# Build shared library
$(LIB_SHARED): $(LIB_OBJS)
	$(CXX) $(LDFLAGS.shared) -o $@ $(LIB_OBJS)

# Build static library
$(LIB_STATIC): $(LIB_OBJS)
	$(AR) rcs $@ $(LIB_OBJS)

# Clean up
clean:
	@rm -rf $(OBJ_DIR)/*.o $(LIB_DIR)/*.so $(LIB_DIR)/*.a $(BIN_DIR)/*

.PHONY: all clean dirs
