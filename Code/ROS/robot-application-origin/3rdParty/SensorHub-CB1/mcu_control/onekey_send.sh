#!/bin/bash
###
# <AUTHOR> m-sharp
# @Date : 2024-10-11 15:59:21
# @LastEditors : m-sharp <EMAIL>
# @LastEditTime : 2024-10-12 16:54:28
# @FilePath : /CB1/SensorHub/mcu_control/onekey_send.sh
# @brief :
#
# Copyright (c) 2024 by DeepRobotics.cn, All Rights Reserved.
###

# 配置变量
LOCAL_DIR="./output/bin"       # 本地目录路径
REMOTE_USER="ysc"              # 远程服务器用户名
REMOTE_HOST="***********"      # 远程服务器地址
REMOTE_DIR="/home/<USER>/jy_mcu/" # 远程目录路径
REMOTE_PASS="'"                # 远程服务器密码

# 指定要拷贝的文件（使用空格分隔）
FILES="mcu_control"

# 确保本地目录存在
if [ ! -d "$LOCAL_DIR" ]; then
  echo "本地目录不存在: $LOCAL_DIR"
  exit 1
fi

# # 拷贝文件夹到远程服务器
# echo "正在拷贝文件从 $LOCAL_DIR 到 $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR"
# scp -r "$LOCAL_DIR" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR"

# # 检查拷贝是否成功
# if [ $? -eq 0 ]; then
#   echo "文件成功拷贝到 $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR"
# else
#   echo "文件拷贝失败"
#   exit 1
# fi

# 拷贝文件到远程服务器
for FILE in $FILES; do
  LOCAL_FILE="$LOCAL_DIR/$FILE"
  if [ -f "$LOCAL_FILE" ]; then
    echo "正在拷贝 $LOCAL_FILE 到 $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR"
    # scp "$LOCAL_FILE" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR"
    sshpass -p "$REMOTE_PASS" scp "$LOCAL_FILE" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR"

    # 检查拷贝是否成功
    if [ $? -eq 0 ]; then
      echo "$LOCAL_FILE 成功拷贝到 $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR"
    else
      echo "$LOCAL_FILE 拷贝失败"
    fi
  else
    echo "本地文件不存在: $LOCAL_FILE"
  fi
done
