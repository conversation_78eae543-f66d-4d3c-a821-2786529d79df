/*
 * @Date : 2024-10-12 12:40:19
 * @LastEditors : m-sharp <EMAIL>
 * @LastEditTime : 2024-10-13 12:05:06
 * @FilePath : /CB1/SensorHub/mcu_control/inc/mcu_agent.hpp
 * @brief :
 *
 * Copyright (c) 2024 by DeepRobotics.cn, All Rights Reserved.
 */
#pragma once

#include <unistd.h> // 包含sleep() 和 usleep()

#include <cstdint>
#include <cstring>
#include <iostream>
#include <sstream>

#include "deep_cmd.h"
#include "mcu_control.h"

#pragma pack(push, 1)
struct LightParamV0
{
  uint8_t  mode;   ///< 模式(1.呼吸 2.闪烁 3.常亮 4.流水灯 5.交叉 6.关闭)
  uint16_t period; ///< 单次循环周期(0-65535ms)
  uint32_t color;  ///< 0x00RRGGBB
  LightParamV0() : mode(0), period(0), color(0) {}
};
#pragma pack(pop)

class McuAgent
{
  private:

  LightParamV0 eye_param_;
  LightParamV0 flash_param_;
  LightParamV0 tail_param_;
  McuAgent(/* args */) : mcu_control_(mcu_Control("/dev/ttyS1")) {};

  public:
  mcu_Control mcu_control_;
  static McuAgent &Instance()
  {
    static McuAgent ma;
    return ma;
  };

  /// 禁用传值构造和右值构造函数
  McuAgent(const McuAgent &)            = delete;
  McuAgent(const McuAgent &&)           = delete;
  McuAgent &operator=(const McuAgent &) = delete;

  void McuReset() { mcu_control_.mcuReset(); }

  void McuUpgrade() { mcu_control_.mcuFmUpdate(); }

  std::vector<uint16_t> GetUltralsonicData() { return mcu_control_.getUltraSonicDistance(); }

  uint8_t GetTouchData()
  {
    uint8_t data;
    mcu_control_.getTouchState(data);
    return data;
  }

  /**
   * @brief 舵机的范围是0-180度,对应0-2000,物理限位是30-150,对应334-1666,1度对应11
   *        狗头是从右向左为正,Z轴逆时针
   * @param {uint16_t} _angle
   * @return {*}
   */
  void SetNeckData(uint16_t _angle)
  {
    static uint16_t last;
    if (_angle > 2000) {
      std::cout << "输入超出范围" << std::endl;
      return;
    }
    // if (_angle > 1600 || _angle < 400) {
    //   std::cout << "输入超出范围" << std::endl;
    //   return;
    // }
    if (last != _angle) mcu_control_.setGearMotor(_angle);
    last = _angle;
    return;
  }

  /**
   * @brief
   * @param {uint8_t} _mode
   * @return {*}
   */
  void PresetFlashLight(uint32_t _mode = kLightFlashOn)
  {
    bool is_valid = true;
    static uint32_t last;
    switch (_mode) {
      case kLightFlashOn: flash_param_.mode = 1; break;
      case kLightFlashOff: flash_param_.mode = 2; break;
      default: is_valid = false; break;
    }
    if (is_valid == true && last != _mode) {
      mcu_control_.CB1LightControl(0, 0, flash_param_.mode, 0, 0, 0, 0);
    }
    last = _mode;
  }

  /**
   * @brief 根据预设类型控制眼灯
   * @param {uint8_t} _type
   * @return {*}
   */
  void PresetEyeLight(uint32_t _type = kLightEyeON)
  {
    bool is_valid = true;
    static uint32_t last;

    switch (_type) {
      case kLightEyeON:
        eye_param_.mode   = 1;
        eye_param_.period = 1;
        eye_param_.color  = 0x00FFE0;
        break;
      case kLightEyeOff:
        eye_param_.mode   = 2;
        eye_param_.period = 1;
        eye_param_.color  = 0x00FFE0;
        break;
      case kLightEyeBreath:
        eye_param_.mode   = 3;
        eye_param_.period = 3000;
        eye_param_.color  = 0x00FFE0;
        break;
      case kLightEyeTwinkle:
        eye_param_.mode   = 4;
        eye_param_.period = 100;
        eye_param_.color  = 0x00FFE0;
        break;
      default: is_valid = false; break;
    }
    if (is_valid == true && last != _type) {
      mcu_control_.CB1LightControl(eye_param_.mode, 0, 0, eye_param_.period, 0, eye_param_.color,
                                   0);
    }
    last = _type;
  }

  /**
   * @brief 根据预设类型控制尾灯
   * @param {uint8_t} _type 尾灯的预置类型1.呼吸 2.闪烁 3.常亮 4.流水灯 5.交叉
   * @return {*}
   */
  void PresetTailLight(uint32_t _type = kLightTailColorFlow)
  {
    bool is_valid = true;

    static uint32_t last;

    switch (_type) {
      case kLightTailWhiteBrtheSlow:
        tail_param_.mode   = 1;
        tail_param_.period = 3000;
        tail_param_.color  = 0xFFFFFF;
        break;
      case kLightTailWhiteBrtheMidle:
        tail_param_.mode   = 1;
        tail_param_.period = 2000;
        tail_param_.color  = 0xFFFFFF;
        break;
      case kLightTailWhiteBrtheFast:
        tail_param_.mode   = 1;
        tail_param_.period = 1000;
        tail_param_.color  = 0xFFFFFF;
        break;
      case kLightTailCyanFlash:
        tail_param_.mode   = 2;
        tail_param_.period = 1000;
        tail_param_.color  = 0x00FFE0;
        break;
      case kLightTailWhiteFlash:
        tail_param_.mode   = 2;
        tail_param_.period = 1000;
        tail_param_.color  = 0xFFFFFF;
        break;
      case kLightTailRedFlash:
        tail_param_.mode   = 2;
        tail_param_.period = 1000;
        tail_param_.color  = 0xFF0000;
        break;
      case kLightTailRedYewFlash:
        tail_param_.mode   = 5;
        tail_param_.period = 4000;
        tail_param_.color  = 0;
        break;
      case kLightTailB5:
        tail_param_.mode   = 2;
        tail_param_.period = 250;
        tail_param_.color  = 0xffe400;
        break;
      case kLightTailCyan:
        tail_param_.mode   = 3;
        tail_param_.period = 1000;
        tail_param_.color  = 0x00FFE0;
        break;
      case kLightTailWhite:
        tail_param_.mode   = 3;
        tail_param_.period = 1000;
        tail_param_.color  = 0xFFFFFF;
        break;
      case kLightTailRed:
        tail_param_.mode   = 3;
        tail_param_.period = 1000;
        tail_param_.color  = 0xFF0000;
        break;
      case kLightTailCyanFlow:
        tail_param_.mode   = 4;
        tail_param_.period = 2000;
        tail_param_.color  = 0x00FFE0;
        break;
      case kLightTailColorFlow:
        tail_param_.mode   = 6;
        tail_param_.period = 1000;
        tail_param_.color  = 0x00FF00;
        break;
      case kLightTailF1: 
        tail_param_.color = 0xFFFF00; 
        tail_param_.period = 1;
        tail_param_.mode   = 7;
        break;

      default: is_valid = false; break;
    }
    if (is_valid == true && last != _type) {
      mcu_control_.CB1LightControl(0, tail_param_.mode, 0, 0, tail_param_.period, 0,
                                   tail_param_.color);
    }
    last = _type;
  }

  void Tail(uint32_t _type = kLightTailColorFlow) { PresetTailLight(_type); }
  void Eye(uint32_t _type = kLightEyeON) { PresetEyeLight(_type); }

  std::string McuVersion() { return mcu_control_.getSoftwareVer(); }

  ~McuAgent(){};
};
