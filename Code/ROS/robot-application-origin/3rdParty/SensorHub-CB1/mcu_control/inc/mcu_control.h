#ifndef _HOST_SYS_H
#define _HOST_SYS_H


#include <iostream>
#include <cstring>
#include <unistd.h>  // 包含sleep() 和 usleep()
#include <vector>
#include <queue>
#include <chrono>
#include <mutex>
#include <condition_variable>
#include <variant>


#include "SerialPort.hpp"
#include "mcu_protocolstack.h"

std::string getFileNameFromPath(const std::string& filepath);
void delay(unsigned milliseconds);



class mcu_ProtocolStack;  // 前向声明

class mcu_Control{
    public:
    #define REALTIME_LIMIT 200
        mcu_Control(std::string device="/dev/ttyS1");
        ~mcu_Control();
        void mcuFmUpdate(const std::string& filepath = "./app.bin"); //必须为bin文件 
        
        void mcuReset(void);

        void setGearMotor(uint16_t data);  //数值范围 0-2000，1000为狗头居中位置
        void setFloodLight(uint16_t data); //百分比 0-100%

        void ControlColorLight(uint8_t blue,uint8_t red,uint8_t green,uint32_t enable);  //颜色数值范围代表0-255，enable表示最大支持的像素为32个
        void ControlColorLightBreath(uint8_t blue,uint8_t red,uint8_t green,uint32_t enable,uint16_t duty);//颜色数值范围代表0-255，enable表示最大支持的像素为32个，周期单位为ms
        void ControlColorLightBlink(uint8_t blue,uint8_t red,uint8_t green,uint32_t enable,uint16_t duty);//颜色数值范围代表0-255，enable表示最大支持的像素为32个，周期单位为ms

        /**
         * @brief 控制灯光效果的函数
         * 
         * 该函数用于控制不同类型的灯光效果，包括眼睛灯、尾灯和泛光灯。
         * 
         * @param eye_mode 眼睛灯模式
         *     - 1: 常亮（恒亮）
         *     - 2: 全灭
         *     - 3: 呼吸模式
         *     - 4: 闪烁
         * 
         * @param tail_mode 尾灯模式
         *     - 1: 呼吸
         *     - 2: 闪烁
         *     - 3: 全亮
         *     - 4: 单灯珠流水灯
         *     - 5: 红黄交替
         *     - 6: 四灯珠流水灯
         *     - 7: 黄色替换
         * 
         * @param flood_mode 泛光灯模式
         *     - 1: 常亮（恒亮）
         *     - 2: 全灭
         * 
         * @param eye_duty 单位：ms
         * 
         * @param tail_duty 单位：ms
         * 
         * @param eye_rgb 眼睛灯的RGB颜色值（32位整数，格式为0x00RRGGBB）
         * 
         * @param tail_rgb 尾灯的RGB颜色值（32位整数，格式为0x00RRGGBB）
         */
        void CB1LightControl(uint8_t eye_mode, uint8_t tail_mode,uint8_t flood_mode,uint16_t eye_duty,uint16_t tail_duty,uint32_t eye_rgb,uint32_t tail_rgb);
        void CB1Eyeblink(void);
      
        
        std::vector<uint16_t> getUltraSonicDistance();//单位：mm，两个值分别代表前后
        bool getTouchState(uint8_t &state);//后三位代表触摸状态，1.检测到触摸状态 0.无触摸状态
        std::string getSoftwareVer(); //返回字符串代表单片机固定版本
        void stressTest(int time,int cnt);
        

    private:
        class Impl;              // 前向声明内部实现类
        Impl* pImpl;             // 指向实现类的指针
        // mcu_ProtocolStack *pmcu_ProtocolStack_t;
        bool is_realtime(STEADY_CLOCK time);

};
//cb1 专用灯效类

#endif


