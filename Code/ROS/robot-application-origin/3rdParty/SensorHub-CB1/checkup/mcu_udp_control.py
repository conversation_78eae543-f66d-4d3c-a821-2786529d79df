import socket
import struct

# 定义命令码
DEEP_CMD_LIGHT = 0x00000100
DEEP_CMD_FLASHLIGHT = 0x00000160

# 定义颜色码
DEEP_CMD_LIGHT_1 = 0x00000101  # 蓝灯常亮
DEEP_CMD_LIGHT_2 = 0x00000102  # 蓝灯闪烁
DEEP_CMD_LIGHT_3 = 0x00000103  # 蓝色呼吸灯
DEEP_CMD_LIGHT_4 = 0x00000104  # 黄灯常亮
DEEP_CMD_LIGHT_5 = 0x00000105  # 黄灯闪烁
DEEP_CMD_LIGHT_6 = 0x00000106  # 红灯常亮
DEEP_CMD_LIGHT_7 = 0x00000107  # 红灯闪烁

# 定义灯光位置
DEEP_CMD_LIGHT_EYES = 0x00000001  # 眼睛灯
DEEP_CMD_LIGHT_CHEST = 0x00000002  # 胸灯
DEEP_CMD_LIGHT_TAIL = 0x00000003  # 尾灯

# 定义结构体
class Head:
    def __init__(self, len, cmd, stat):
        self.len = len
        self.cmd = cmd
        self.stat = stat

class Light:
    def __init__(self, color, duration):
        self.color = color
        self.duration = duration

class Flashlight:
    def __init__(self,brightness):
        self.flashswitch = 0
        self.brightness = brightness

# 打包函数
def pack_head(head):
    print(head.len)
    print(head.cmd)
    print(head.stat)
    return struct.pack('>III', head.len, head.cmd, head.stat)

def pack_light(light):
    return struct.pack('>ii', light.color, light.duration)

def pack_flashlight(flashlight):
    return struct.pack('>ii', flashlight.flashswitch,flashlight.brightness)

# 创建UDP套接字
def create_udp_socket():
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    return sock

# 发送消息
def send_message(sock, message, address):
    sock.sendto(message, address)

# 构建并发送灯光控制消息
def send_light_control(color, position, duration=0):
    light = Light(color, duration)
    body = pack_light(light)
    print(len(body) + 12)
    head = Head(len(body) + 12, DEEP_CMD_LIGHT, 0)  # 12 是头的长度
    header = pack_head(head)
    message = header + body
    print(message)
    sock = create_udp_socket()
    send_message(sock, message, ('***********', 12345))
    sock.close()

# 构建并发送手电筒控制消息
def send_flashlight_control(brightness):
    flashlight = Flashlight(brightness)
    body = pack_flashlight(flashlight)
    print(len(body) + 12)
    head = Head(len(body) + 12, DEEP_CMD_FLASHLIGHT, 0)  # 12 是头的长度
    header = pack_head(head)
    print(header.hex())
    message = header + body
    print(message.hex())
    sock = create_udp_socket()
    send_message(sock, message, ('***********', 12345))
    sock.close()

# 示例用法
if __name__ == "__main__":
    # 发送灯光控制消息 能正确通讯解析
    # send_light_control(DEEP_CMD_LIGHT_2, DEEP_CMD_LIGHT_EYES)
    print("Sent light control message (Blue light on eyes)")

    # 发送手电筒控制消息
    send_flashlight_control(100)  # 50% 亮度
    print("Sent flashlight control message (50% brightness)")