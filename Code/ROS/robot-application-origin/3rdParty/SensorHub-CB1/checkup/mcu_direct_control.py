import sys
import serial
import threading
import time 
import os



class SerialPort:
    def __init__(self, port=None, baudrate=9600, timeout=1, logger=None, vendor_id=None, product_id=None):
        self.vendor_id = vendor_id
        self.product_id = product_id

        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial_connection = None
        print(f"Initializing SerialPort with port={self.port}, baudrate={self.baudrate}, timeout={self.timeout}")

    def find_serial_device(self):
        """找到具有特定Vendor ID和Product ID的串口设备"""
        ports = list(serial.tools.list_ports.comports())
        for port in ports:
            if port.vid == self.vendor_id and port.pid == self.product_id:
                return port.device
        return None

    def open_port(self):
        if self.serial_connection is None:
            if self.port is None and self.vendor_id is not None and self.product_id is not None:
                self.port = self.find_serial_device()

            if self.port is None:
                print("No suitable serial port found.")
                return False

            try:
                self.serial_connection = serial.Serial(
                    port=self.port,
                    baudrate=self.baudrate,
                    timeout=self.timeout
                )
                print("Serial port opened successfully.")
                return True
            except serial.SerialException as e:
                print(f"Error opening serial port: {e}")
                return False

    def close_port(self):
        if self.serial_connection is not None and self.serial_connection.is_open:
            self.serial_connection.close()
            print("Serial port closed.")

    def write_data(self, data):
        if self.serial_connection is not None and self.serial_connection.is_open:
            try:
                self.serial_connection.write(data)
                print("Data sent successfully.")
            except serial.SerialException as e:
                print(f"Error writing to serial port: {e}")

    def read_data(self, size=1024):
        if self.serial_connection is not None and self.serial_connection.is_open:
            try:
                return self.serial_connection.read(size)
            except serial.SerialException as e:
                print(f"Error reading from serial port: {e}")
                return None

    def readline(self):
        if self.serial_connection is not None and self.serial_connection.is_open:
            try:
                return self.serial_connection.readline()
            except serial.SerialException as e:
                print(f"Error reading line from serial port: {e}")
                return None


def read_bin_file_in_chunks(file_path, chunk_size=1024):
    """分段读取二进制文件。"""
    try:
        with open(file_path, 'rb') as file:
            while True:
                chunk = file.read(chunk_size)
                if not chunk:
                    break
                yield chunk
    except IOError as e:
        print(f"Error reading file {file_path}: {e}")


ok_received = False


# def is_recv_ok(event):
#     """检查是否接收到 OK 信号。"""
#     global ok_received
#     while not event.is_set():
#         if ok_received:
#             ok_received = False
#             break


def is_recv_ok(event, timeout=25):
    """检查是否接收到 OK 信号，增加超时处理。
    
    :param event: threading.Event 用于线程的控制
    :param timeout: 等待超时的秒数，默认为 5 秒
    :return: True 如果接收到 OK 信号，False 如果超时
    """
    global ok_received
    start_time = time.time()
    while not event.is_set():
        if ok_received:
            ok_received = False
            print("OK signal received.")
            return True
        # 检查是否超时
        if time.time() - start_time > timeout:
            print("Timeout waiting for OK signal.")
            return False
        time.sleep(0.1)  # 减少 CPU 占用率

    print("Event set, stopping wait.")
    return False
def receive_input(sp, stop_event):
    """接收串口输入数据。"""
    global ok_received
    while not stop_event.is_set():
        try:
            response = sp.read_data()
            if response:
                print("Hex:", ', '.join(["0x{:02x}".format(byte) for byte in response]))
                print("Received:", response)
                if response == b'\xaa\xaa\x00\x00':
                    ok_received = True
                    print("OK received")
        except KeyboardInterrupt:
            print("\n程序已中断")
            stop_event.set()
            break


if __name__ == "__main__":
    bin_file = r'E:\YSC_workspace\ysc_projects\cb_projects\CB1\release\CB1_application\MDK-ARM\app.bin'
    sp = SerialPort(port='/dev/ttyS1', baudrate=115200, timeout=10)
    if not sp.open_port():
        sys.exit("无法打开串口，程序退出。")

    # stop_event = threading.Event()
    # receiver_thread = threading.Thread(target=receive_input, args=(sp, stop_event))
    # receiver_thread.start()
    FRAME_ID_BIAS = 2
    FRAME_VERSION_BIAS = 3
    FRAME_TYPE = 4
    FRAME_FUNC_CODE = 5
    FRAME_DATA_SIZE_BIAS = 6
    FRAME_CRC_BIAS = 8
    FRAME_DATA_BIAS = 10
    
    cnt = 0

    try:
        while True:
            print("0.Exit 1.MCU reset 2.light control 3.neck control 4.ultrasonic 5.Firmware update 6.light BRG test 7.flood light")
            user_input = input("输入选择的命令: \n")
            if user_input == '5':
                choice = input("选择模式: 1.固件更新 2.传输结束 3.修改固件名字 4.修改固件尺寸\n")
                send_bytes = bytearray(b"\x44\x52\x00\x00\x16\x34\x00\x00\x00\x00")
                if choice == '1':
                    bias = 0
                    for chunk in read_bin_file_in_chunks(file_path=bin_file, chunk_size = 128):
                        send_bytes = bytearray(b"\x44\x52\x00\x00\x16\x34\x00\x00\x00\x00\xee\xee")
                        print(f"读取到的数据块大小: {len(chunk)} 字节")
                        length_value = len(chunk) + 4 
                        send_bytes[FRAME_DATA_SIZE_BIAS: FRAME_DATA_SIZE_BIAS+2] = length_value.to_bytes(2, byteorder='big')
                        temp_bytes = bias.to_bytes(2, byteorder='big')
                        send_bytes.extend(temp_bytes)
                        send_bytes.extend(chunk)
                        print(f"发送的数据块大小: {len(send_bytes)} 字节")
                        # 使用列表推导式生成格式化的字符串，并且每8个元素换行
                        hex_output = ',\n'.join(
                            [', '.join(["0x{:02x}".format(byte) for byte in send_bytes[i:i + 8]]) 
                            for i in range(0, len(send_bytes), 8)])

                        print("Hex:\n" + hex_output)
                        for i in range(0, 5):
                            sp.write_data(send_bytes)
                            response = sp.read_data()
                            if response :
                                print("Received update state:", response)
                                break
    
                        if i == 5:
                            break
                        bias = bias + 1
                        

                
                elif choice == '2':  
                    temp_bytes = bytearray(b"\xff\xff")
                    send_bytes.extend(temp_bytes)
                    send_bytes[FRAME_DATA_SIZE_BIAS: FRAME_DATA_SIZE_BIAS+2] = len(temp_bytes).to_bytes(2, byteorder='big')
                    print("Hex:", ', '.join(["0x{:02x}".format(byte) for byte in send_bytes]))
                    sp.write_data(send_bytes)
                    # is_recv_ok(stop_event)
                elif choice == '3':
                    temp_bytes = bytearray(b"\xff\xfe")
                    text = "factory_v1.0"
                    byte_stream = bytes(text, 'utf-8')
                    temp_bytes.extend(byte_stream)
                    length_value = len(temp_bytes) + 2
                    length_byte = length_value.to_bytes(2, byteorder='big')
                    send_bytes[FRAME_DATA_SIZE_BIAS: FRAME_DATA_SIZE_BIAS+2] = length_byte
                    send_bytes.extend(temp_bytes)
                    print("Hex:", ', '.join(["0x{:02x}".format(byte) for byte in send_bytes]))
                    sp.write_data(send_bytes)
                    # is_recv_ok(stop_event)
                elif choice == '4':
                    temp_bytes = bytearray(b"\xff\xfd\x00\x01\x00\x00")
                    file_size = os.path.getsize(bin_file)
                    print("file_size:",file_size)
                    temp_bytes[2:] = file_size.to_bytes(4, byteorder='big') 
                    send_bytes.extend(temp_bytes)
                    length_byte = len(temp_bytes).to_bytes(2, byteorder='big')
                    send_bytes[FRAME_DATA_SIZE_BIAS: FRAME_DATA_SIZE_BIAS+2] = length_byte
                
                    print("Hex:", ', '.join(["0x{:02x}".format(byte) for byte in send_bytes]))
                    print(send_bytes)
                    sp.write_data(send_bytes)

                if send_bytes[FRAME_TYPE] != 0x15:
                    cnt = (cnt+1) % 256
                    for i in range(0, 5):         
                        response = sp.read_data()
                        if response :
                            print("Recv Hex:", ', '.join(["0x{:02x}".format(byte) for byte in response]))
                            print("Received:", response)
                            break      
                continue

            send_bytes = bytearray(b"\x44\x52\x00\x00\x01\x00\x00\x00\x00\x00")
            send_bytes_size = len(send_bytes)
            if user_input == '0':
                break
            elif user_input == '1':  # MCU reset
                send_bytes[FRAME_TYPE] = 0x15
                send_bytes[FRAME_FUNC_CODE] = 0x30
                send_bytes[FRAME_DATA_SIZE_BIAS:FRAME_DATA_SIZE_BIAS+2] = int(0).to_bytes(2, byteorder='big')
                sp.write_data(send_bytes)
                for i in range(0, 30):         
                    response = sp.read_data()
                    if response :
                        print("Recv Hex:", ', '.join(["0x{:02x}".format(byte) for byte in response]))
                        print("Received:", response)
                        break    
                continue  
            elif user_input == '2': # light control
                send_bytes[FRAME_TYPE] = 0x15
                send_bytes[FRAME_FUNC_CODE] = 0x31
                extend_send_bytes = bytearray(b"\x00\x00\x00\x00\x00\x00\x00\x00")
                send_bytes[FRAME_DATA_SIZE_BIAS:FRAME_DATA_SIZE_BIAS+2] = int(len(extend_send_bytes)).to_bytes(2, byteorder='big')
                
                input_choice = input("请输入控制灯组的编号(1-4): ")
               



                if input_choice == '1':
                    input_choice = input("组灯状态: ")
                    print("输入的灯状态为:", int(input_choice))
                    extend_send_bytes[4] = int(input_choice)
                elif input_choice == '2':
                    input_choice = input("组灯状态: ")
                    print("输入的灯状态为:", int(input_choice))
                    extend_send_bytes[5] = int(input_choice)
                elif input_choice == '3':
                    input_choice = input("组灯状态: ")
                    print("输入的灯状态为:", int(input_choice))
                    extend_send_bytes[6] = int(input_choice)
                elif input_choice == '4':
                    input_choice = input("组灯状态:(0:关闭, 1:打开) ")
                    print("输入的灯状态为:", int(input_choice))
                    extend_send_bytes[7] = int(input_choice)
                
                
                
                input_string = input("请输入RGB颜色值,以空格区分,范围(0-255): ")
                # 按空格分割字符串，得到一个字符串列表
                rgb_strings = input_string.split()

                # 定义一个列表来存储转换后的整数
                rgb_values = []

                # 遍历字符串列表，将每个字符串转换为整数并验证范围
                for value_str in rgb_strings:
                    try:
                        value = int(value_str)
                        if 0 <= value <= 255:
                            rgb_values.append(value)
                        else:
                            raise ValueError
                    except ValueError:
                        print(f"输入无效，请确保每个值都在0到255之间：{value_str}")
                        break
                else:
                    # 如果所有值都有效，则打印转换后的整数列表
                    print(f"RGB颜色值为: {rgb_values}")

                # 如果你想在所有值都有效的情况下继续执行其他操作，可以在这里添加代码
                if len(rgb_values) == 3:
                    # 将RGB值转换为字节流
                    rgb_bytes = bytes(rgb_values)
                    # 输出字节流
                    print(f"RGB颜色值对应的字节流: {rgb_bytes}")
                    # r, g, b = rgb_values
                    # # 这里可以处理r, g, b三个整数值
                    # print(f"红色分量: {r}, 绿色分量: {g}, 蓝色分量: {b}")
                    extend_send_bytes[0:3] = rgb_bytes
                    send_bytes.extend(extend_send_bytes)
 
            elif user_input == '3': #neck control
                send_bytes[FRAME_TYPE] = 0x15
                send_bytes[FRAME_FUNC_CODE] = 0x32
                extend_send_bytes = bytearray(b"\x00\x00")
                send_bytes[FRAME_DATA_SIZE_BIAS:FRAME_DATA_SIZE_BIAS+2] = int(len(extend_send_bytes)).to_bytes(2, byteorder='big')
                input_choice = input("请输入舵机偏转(机械限位的0-100%): ")
                extend_send_bytes[0:2] = int(input_choice).to_bytes(2, byteorder='big')
                send_bytes.extend(extend_send_bytes)
                
            elif user_input == '4': # ultrasonic control
                send_bytes[FRAME_TYPE] = 0x17
                send_bytes[FRAME_FUNC_CODE] = 0x33
                send_bytes[FRAME_DATA_SIZE_BIAS:FRAME_DATA_SIZE_BIAS+2] = int(0).to_bytes(2, byteorder='big')
            elif user_input == '6':
                for i in range(24):
                    temp_bytes = 0x000001 << i
                    extend_send_bytes = temp_bytes.to_bytes(4, byteorder='big')
                    send_bytes = bytearray(b"\x44\x52\x00\x00\x17\x31\x00\x08\x00\x00\x00\x00\xaa\x00") ##BLUE
                    send_bytes.extend(extend_send_bytes)
                    sp.write_data(send_bytes)
                    time.sleep(0.5)
                    send_bytes = bytearray(b"\x44\x52\x00\x00\x17\x31\x00\x08\x00\x00\x00\xaa\x00\x00") #RED
                    send_bytes.extend(extend_send_bytes)
                    sp.write_data(send_bytes)
                    time.sleep(0.5)
                    send_bytes = bytearray(b"\x44\x52\x00\x00\x17\x31\x00\x08\x00\x00\xaa\x00\x00\x00") #GREEN
                    send_bytes.extend(extend_send_bytes)
                    sp.write_data(send_bytes)
                    time.sleep(0.5)
                continue
            elif user_input == '7': #flood control
                send_bytes[FRAME_TYPE] = 0x15
                send_bytes[FRAME_FUNC_CODE] = 0x36
                extend_send_bytes = bytearray(b"\x00\x00")
                send_bytes[FRAME_DATA_SIZE_BIAS:FRAME_DATA_SIZE_BIAS+2] = int(len(extend_send_bytes)).to_bytes(2, byteorder='big')
                input_choice = input("照明亮度(0-100%): ")
                extend_send_bytes[0:2] = int(input_choice).to_bytes(2, byteorder='big')
                send_bytes.extend(extend_send_bytes)
            else:
                print("Invalid input")
                continue
            
            send_bytes[FRAME_ID_BIAS:FRAME_ID_BIAS+1] = int(cnt).to_bytes(1, byteorder='big')
            print("Send Hex:", ', '.join(["0x{:02x}".format(byte) for byte in send_bytes]))
            sp.write_data(send_bytes)
            if send_bytes[FRAME_TYPE] != 0x15:
                cnt = (cnt+1) % 256
                for i in range(0, 15):         
                    response = sp.read_data()
                    if response :
                        print("Recv Hex:", ', '.join(["0x{:02x}".format(byte) for byte in response]))
                        print("Received:", response)
                        break      
                

    except KeyboardInterrupt:
        print("程序已中断")
        # stop_event.set()
    finally:
        sp.close_port()
        # stop_event.set()
        # receiver_thread.join()

