cmake_minimum_required(VERSION 3.16)
project(SensorHub)

# 设置工具链文件
set(CMAKE_TOOLCHAIN_FILE "arm-toolchain.cmake")
include(${CMAKE_TOOLCHAIN_FILE})
find_package(Threads REQUIRED)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_BUILD_TYPE "Release")

set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -O0 -Wall -g -ggdb")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -Wall")

set(SRC_FILES
  src/Common/SerialPort.cpp
  src/Common/UdpCommunicator.cpp
  src/Common/MessageHandler.cpp
  src/SensorModule/floodlightcontrol/FloodLightControl.cpp
  src/SensorModule/ledlightcontrol/LightControl.cpp
  src/SensorModule/neckcontrol/NeckControl.cpp
  src/SensorModule/touch/GpioSensor.cpp
  src/SensorModule/ultrasonic/Ultrasonic.cpp
  src/main.cpp
)

add_executable(SensorHubNode ${SRC_FILES})

target_include_directories(SensorHubNode PRIVATE
  ${CMAKE_SOURCE_DIR}/include
  ${CMAKE_SOURCE_DIR}/mcu_control/inc
)

link_directories(${CMAKE_SOURCE_DIR}/lib)

add_library(McuControl SHARED IMPORTED)
set_target_properties(McuControl PROPERTIES
  IMPORTED_LOCATION "${CMAKE_SOURCE_DIR}/lib/libsensorhub.so"
)

target_link_libraries(SensorHubNode PRIVATE McuControl -lpthread)
