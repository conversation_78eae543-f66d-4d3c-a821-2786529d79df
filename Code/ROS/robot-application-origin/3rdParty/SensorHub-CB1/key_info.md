
SerialPort serialPort("/dev/ttyS1", B115200);

UdpCommunicator* sensorHubServer = UdpCommunicator::createSendingAndReceiving("0.0.0.0", 12345);


neckControl.controlNeck(1000); // 千分比

handleLightCommand(const char* data, size_t size)  自检灯/联网灯  eyetail light眼尾灯控制

/// flash与flood在本项目中指同一种东西
flashData.brightness = ntohl(flashData.brightness);
if (!floodLightControl_.controlFloodLight(flashData.brightness)) {
  std::cerr << "Failed to control flashlight." << std::endl;
}

class MessageHandler  // 做指令响应