// #include <iostream>
// #include <cstring>
// #include <unistd.h>  // 包含sleep() 和 usleep()


#include "mcu_agent.hpp"
#include <mcu_control.h>

using namespace std;

McuAgent *McuAgent::instance = nullptr;

// 用户输入转换为字节序列的函数
std::vector<uint8_t> string_to_bytes(const std::string& input) {
    std::istringstream iss(input);
    std::vector<uint8_t> result;
    uint8_t value;
    char delimiter = ' ';
    std::string token;
    while (std::getline(iss, token, delimiter)) {
        std::istringstream converter(token);
        converter >> std::hex >> value;
        result.push_back(value);
    }
    return result;
}





void stm32f4_control(mcu_Control &mcu_control_t){
    while(1)
    {
        cout<<"============================  选择单片机功能  ========================================="<<endl;
        cout<<"1.MCU 复位 2.获取当前所处分区(boot/app) 3.跳转至app 4.获取固件版本 5.MCU固件升级           "<<endl;
        cout<<"6.尾灯控制  15.压力测试                                                                 "<<endl;
        cout<<"================================================================================="<<endl;
        int choice;
        cin>>choice;
        if (cin.fail()) { // 检查输入是否为整数
            cin.clear(); // 清除错误标志
            cin.ignore(numeric_limits<streamsize>::max(), '\n'); // 忽略行内剩余字符
            cout << "Invalid input, please enter an integer." << endl;
            continue; // 继续下一次循环
        } 
        cout<<"current choice:"<<choice<<endl;
        if(choice == 1){
            mcu_control_t.mcuReset();
        }
        else if(choice == 2){
            uint8_t temp = mcu_control_t.getCurrentSector();
            if(temp == 1) // 1:bootloader 2:app
            {
                cout<<"Current Sector is Boot!"<<endl;           
            }
            else if(temp == 2) 
            {
                cout<<"Current Sector is APP!"<<endl;
            }
            else
            {
                cout<<"Current Sector Unkown!"<<endl;
            }
            
        }
        else if(choice == 3){
            if(mcu_control_t.getCurrentSector() == 1) // 1:bootloader 2:app
            {
                cout<<"Current Sector is Boot!"<<endl;   
                mcu_control_t.jump2sector();        
            }
            else if(mcu_control_t.getCurrentSector() == 2) 
            {
                cout<<"Current Sector is APP!"<<endl;
                mcu_control_t.mcuReset();
            }
            else
            {
                cout<<"Current Sector Unkown!"<<endl;
            }
           
        }
        else if(choice == 4){
            cout<<"Current MCU Version :"<<mcu_control_t.getSoftwareVer() <<endl;
        }
        else if(choice == 5){
            int sector = mcu_control_t.getCurrentSector();

            if( sector == 1) // 1:bootloader 2:app
            {
                cout<<"当前所在 BOOT 分区 , 准备升级 APP 固件!"<<endl;   
                // cout<<"Current Sector is Boot,Ready to update APP !"<<endl;    
                mcu_control_t.mcuFmUpdate("/home/<USER>/Downloads/f4_app.bin");       
            }
            else if(sector == 2) 
            {
                cout<<"当前所在 APP 分区 , 准备升级 BOOT 固件!"<<endl;   
                // cout<<"Current Sector is APP,Ready to update Boot !"<<endl;    
                mcu_control_t.mcuFmUpdate("/home/<USER>/Downloads/f4_boot.bin");  
                // cout<<"Current Sector is APP, must be boot sector!"<<endl;
            }
            else
            {
                cout<<"Current Sector Unkown!"<<endl;
            }
            
        }
        else if(choice == 6){
            std::cout<<"红色常亮"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(1, 0x00ff00, 1000);
            delay(3000);
            std::cout<<"绿色闪烁，周期1s"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(2, 0xff0000, 1000);
            delay(5000);
            std::cout<<"蓝色呼吸，周期2s"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(3, 0x0000ff, 2000);
            delay(6000);
            std::cout<<"流水灯，周期1s"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(4, 0x0000ff, 1000);
            delay(6000);
            std::cout<<"4灯珠流水灯，周期2s"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(5, 0x0000ff, 2000);
            delay(6000);
            std::cout<<"红黄交替，周期2s"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(6, 0x0000ff, 2000);
            delay(6000);
            std::cout<<"黄色替换"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(7, 0x0000ff, 2000);
            delay(6000);
            std::cout<<"全灭"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(1, 0x000000, 2000);
            delay(3000);
        }
        else if(choice == 7){
            std::cout<<"红色常亮"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(1, 0x00ff00, 1000);
        }
        else if(choice == 8){
            std::cout<<"绿色闪烁，周期1s"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(2, 0xff0000, 1000);
        }
        else if(choice == 9){
            std::cout<<"蓝色呼吸，周期2s"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(3, 0x0000ff, 2000);
        }
        else if(choice == 10){
            std::cout<<"流水灯，周期1s"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(4, 0x0000ff, 1000);
        }
        else if(choice == 11){
            std::cout<<"4灯珠流水灯，周期2s"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(5, 0x0000ff, 2000);
        }
        else if(choice == 12){
            std::cout<<"红黄交替，周期2s"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(6, 0x0000ff, 2000);
        }
        else if(choice == 13){
            std::cout<<"黄色替换"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(7, 0x0000ff, 2000);
        }
        else if(choice == 14){
            std::cout<<"全灭"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(1, 0x000000, 2000);
        }
        else if(choice == 15){
            int choice_s;
            cin>>choice_s;
            mcu_control_t.StressTest(choice_s);
        }
        
        else{
            std::cout<<"invalid data"<<endl;
        }
    }
}


void stm32f1_control(mcu_Control &mcu_control_t){
    while(1)
    {

        cout<<"============================  选择单片机功能  ========================================="<<endl;
        cout<<"1.MCU 复位 2.获取当前所处分区(boot/app) 3.跳转至app 4.获取固件版本 5.MCU固件升级           "<<endl;
        cout<<"6.眼灯功能预览 7.获取触摸状态 8.颈电机控制 9.照明灯控制 10.超声数据获取"<<endl;                    
        cout<<"11.头部控尾灯预览（尾灯接在F1上）  15.压力测试                                                            "<<endl;  
        cout<<"================================================================================="<<endl;


       
        int choice;
        cin>>choice;
        if (cin.fail()) { // 检查输入是否为整数
            cin.clear(); // 清除错误标志
            cin.ignore(numeric_limits<streamsize>::max(), '\n'); // 忽略行内剩余字符
            cout << "Invalid input, please enter an integer." << endl;
            continue; // 继续下一次循环
        } 
        cout<<"current choice:"<<choice<<endl;
        if(choice == 1){
            mcu_control_t.mcuReset();
        }
        else if(choice == 2){
            uint8_t temp = mcu_control_t.getCurrentSector();
            if(temp == 1) // 1:bootloader 2:app
            {
                cout<<"Current Sector is Boot!"<<endl;           
            }
            else if(temp == 2) 
            {
                cout<<"Current Sector is APP!"<<endl;
            }
            else
            {
                cout<<"Current Sector Unkown!"<<endl;
            }
            
        }
        else if(choice == 3){
            mcu_control_t.jump2sector();
        }
        else if(choice == 4){
            cout<<"Current MCU Version :"<<mcu_control_t.getSoftwareVer() <<endl;
        }
        else if(choice == 5){   
            int sector = mcu_control_t.getCurrentSector();

            if( sector == 1) // 1:bootloader 2:app
            {
                cout<<"当前所在 BOOT 分区 , 准备升级 APP 固件!"<<endl;
                // cout<<"Current Sector is Boot,Ready to update APP !"<<endl;    
                mcu_control_t.mcuFmUpdate("/home/<USER>/Downloads/f1_app.bin");       
            }
            else if(sector == 2) 
            {
                cout<<"当前所在 APP 分区 , 准备升级 BOOT 固件!"<<endl; 
                // cout<<"Current Sector is APP,Ready to update Boot !"<<endl;    
                mcu_control_t.mcuFmUpdate("/home/<USER>/Downloads/f1_boot.bin");  
                // cout<<"Current Sector is APP, must be boot sector!"<<endl;
            }
            else
            {
                cout<<"Current Sector Unkown!"<<endl;
            }
            
            
        }
        else if(choice == 6){
            std::cout<<"红色常亮"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(1, 0x00ff00, 1000);
            delay(3000);
            std::cout<<"绿色闪烁，周期1s"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(2, 0xff0000, 1000);
            delay(5000);
            std::cout<<"蓝色呼吸，周期2s"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(3, 0x0000ff, 6000);
            delay(12000);
            std::cout<<"全灭"<<std::endl;
            mcu_control_t.CB1_V3_LEDcontrol(1, 0x000000, 2000);
            delay(3000);
        }
        else if(choice == 7){
            uint8_t temp;
            bool ret = mcu_control_t.getTouchState(temp);
            if(ret == true){
                if( (temp & 0x01) == 0x01){
                    std::cout<<"touch1 detect"<<std::endl;
                }
                if( (temp & 0x02) == 0x02){
                    std::cout<<"touch2 detect"<<std::endl;
                }
                if( (temp & 0x04) == 0x04){
                    std::cout<<"touch3 detect"<<std::endl;
                }

            }
            
        }
        else if(choice == 8){
            uint16_t temp;
            std::cout<<"请输入0-2000的数值:"<<std::endl;
            std::cin >> temp;
            if(temp >= 0 && temp <= 2000 ){
                mcu_control_t.setGearMotor(temp);
            }
            else{
                std::cout<<"超出范围"<<std::endl;
            }
        }
        else if(choice == 9){
            int mode;
            std::cout<<"控制照明灯: 1(打开) 2(关闭)"<<std::endl;
            std::cin >> mode;
            if(mode >= 1 && mode <= 2 ){
                mcu_control_t.CB1_V3_FlashLight(mode);
            }
            else{
                std::cout<<"超出范围"<<std::endl;
            }
        }
        else if(choice == 10){
            std::vector<uint16_t> dis =  mcu_control_t.getUltraSonicDistance();
            for (size_t i = 0; i < dis.size(); ++i) {
                std::cout <<std::dec<< "Distance " << i << ": " << static_cast<int>(dis[i]) << " mm" << std::endl;
            }


        }
        else if(choice == 11){
            std::cout<<"红色常亮"<<std::endl;
            mcu_control_t.CB1_V2_TailLight(1, 0x00ff00, 1000);
            delay(3000);
            std::cout<<"绿色闪烁，周期1s"<<std::endl;
            mcu_control_t.CB1_V2_TailLight(2, 0xff0000, 1000);
            delay(5000);
            std::cout<<"蓝色呼吸，周期2s"<<std::endl;
            mcu_control_t.CB1_V2_TailLight(3, 0x0000ff, 6000);
            delay(12000);
            std::cout<<"全灭"<<std::endl;
            mcu_control_t.CB1_V2_TailLight(1, 0x000000, 2000);
            delay(3000);


        }
         else if(choice == 15){
            int choice_s;
            cin>>choice_s;
            std::cout<<std::dec<<"stress test: "<<static_cast<int>(choice_s)<<std::endl;
            mcu_control_t.StressTest(choice_s);
        }
        else{
            std::cout<<"invalid data"<<endl;
        }
    }
}


void hardware_v3(McuAgent &agent)
{
    while(1){
        cout<<"========================choose function:========================================="<<endl;
        cout<<"1.预设灯光测试           "<<endl;
        cout<<"================================================================================="<<endl;


        int choice;
        cin>>choice;
        if (cin.fail()) { // 检查输入是否为整数
            cin.clear(); // 清除错误标志
            cin.ignore(numeric_limits<streamsize>::max(), '\n'); // 忽略行内剩余字符
            cout << "Invalid input, please enter an integer." << endl;
            continue; // 继续下一次循环
        } 
        cout<<"current choice:"<<choice<<endl;

        if(choice == 1){
            std::cout<<"眼灯效果展示"<<std::endl;
            std::cout<<"青色常亮"<<std::endl;
            agent.PresetEyeLight(kLightEyeON);
            delay(5000);
            std::cout<<"熄灭"<<std::endl;
            agent.PresetEyeLight(kLightEyeOff);
            delay(5000);
            std::cout<<"青色呼吸"<<std::endl; //question
            agent.PresetEyeLight(kLightEyeBreath);
            delay(5000);
            std::cout<<"眨眼"<<std::endl;
            agent.PresetEyeLight(kLightEyeTwinkle);
            delay(5000);

            std::cout<<"胸灯效果展示"<<std::endl;
            std::cout<<"打开"<<std::endl;
            agent.PresetFlashLight(kLightFlashOn);
            delay(5000);
            std::cout<<"关闭"<<std::endl;
            agent.PresetFlashLight(kLightFlashOff);
            delay(5000);

            std::cout<<"尾灯效果展示"<<std::endl;
            std::cout<<"白色呼吸低速"<<std::endl;
            agent.PresetTailLight(kLightTailWhiteBrtheSlow);
            delay(5000);
            std::cout<<"白色呼吸中速"<<std::endl;
            agent.PresetTailLight(kLightTailWhiteBrtheMidle);
            delay(5000);
            std::cout<<"白色呼吸高速"<<std::endl;
            agent.PresetTailLight(kLightTailWhiteBrtheFast);
            delay(5000);
            std::cout<<"青色闪烁"<<std::endl;
            agent.PresetTailLight(kLightTailCyanFlash);
            delay(5000);
            std::cout<<"白色闪烁"<<std::endl;
            agent.PresetTailLight(kLightTailWhiteFlash);
            delay(5000);
            std::cout<<"红色闪烁"<<std::endl;
            agent.PresetTailLight(kLightTailRedFlash);
            delay(5000);
            std::cout<<"红黄交替闪烁"<<std::endl; //question
            agent.PresetTailLight(kLightTailRedYewFlash);
            delay(5000);
            std::cout<<"青色快闪"<<std::endl;
            agent.PresetTailLight(kLightTailB5);
            delay(5000);
            std::cout<<"青色常亮"<<std::endl;
            agent.PresetTailLight(kLightTailCyan);
            delay(5000);
            std::cout<<"白色常亮"<<std::endl;
            agent.PresetTailLight(kLightTailWhite);
            delay(5000);
            std::cout<<"红色常亮"<<std::endl;
            agent.PresetTailLight(kLightTailRed);
            delay(5000);
            std::cout<<"青色流水"<<std::endl;
            agent.PresetTailLight(kLightTailCyanFlow);
            delay(5000);
            std::cout<<"4青色流水"<<std::endl;
            agent.PresetTailLight(kLightTailColorFlow);
            delay(5000);
            std::cout<<"黄色替换"<<std::endl;
            agent.PresetTailLight(kLightTailF1);
            delay(5000);
        }
    }

}
int main()
{
    McuAgent &dddd = McuAgent::Instance();
    // mcu_Control *mcuf1_control_t = &dddd.head_mcu_control_;
    // mcu_Control *mcuf4_control_t = &dddd.tail_mcu_control_;
    
    
    // mcu_Control mcuf1_control_t =mcu_Control("/dev/ttyS1");
    // mcu_Control mcuf4_control_t = mcu_Control("/dev/ttyS7");

    int choice;
    int mode;
    while(1){
        cout<<"========================choose MCU==========================================="<<endl;
        cout<<"1.STM32F1  2.STM32F4 3.Hardware_v2                                                      "<<endl;
        cout<<"============================================================================="<<endl;
        cin>>choice;
        if (cin.fail()) { // 检查输入是否为整数
            cin.clear(); // 清除错误标志
            cin.ignore(numeric_limits<streamsize>::max(), '\n'); // 忽略行内剩余字符
            cout << "Invalid input, please enter an integer." << endl;
            continue; // 继续下一次循环
        } 
        if(choice == 1){
            mode = 1;
            break;
        }
        else if(choice == 2){
            mode = 2;
            break;
        }
        else if(choice == 3){
            mode = 3;
            break;
        }
        else{
            cout<<"invalid choice"<<endl;
            continue;
        }
    }
    if(mode == 1){
        stm32f1_control(dddd.head_mcu_control_);
    }
    else if(mode == 2){
        stm32f4_control(dddd.tail_mcu_control_);
    }
    else if(mode == 3){
        hardware_v3(dddd);
    }
    
}
