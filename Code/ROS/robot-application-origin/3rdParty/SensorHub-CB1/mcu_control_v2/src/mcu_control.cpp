#include <fstream>
#include <iostream>
#include <vector>

#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <atomic>

#include <chrono>
#include <thread>

#include "mcu_control.h"



void delay(unsigned milliseconds) {
    std::this_thread::sleep_for(std::chrono::milliseconds(milliseconds));
}





// 获取文件路径中的文件名
std::string getFileNameFromPath(const std::string& filepath){
    size_t pos = filepath.find_last_of("/\\");
    if (pos == std::string::npos) {
        // 如果没有找到斜杠，说明整个路径就是文件名
        return filepath;
    }
    return filepath.substr(pos + 1);
}

bool isSerialPortBusy(const std::string& portName) {
    std::string command = "sudo lsof " + portName;
    FILE* pipe = popen(command.c_str(), "r");
    if (!pipe) {
        std::cerr << "Error opening pipe" << std::endl;
        return false;
    }

    char buffer[128];
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        // 如果 lsof 输出了信息，说明设备被占用
        pclose(pipe);
        return true;
    }

    pclose(pipe);
    return false;
}



class mcu_Control::Impl {
public:
    mcu_ProtocolStack* pmcu_ProtocolStack_t;

    Impl(const std::string& device) {

        if(isSerialPortBusy(device) == true){
            throw std::logic_error(device + " 被占用，请关闭其他程序后重启该程序！");
        }

        // 初始化 mcu_ProtocolStack 对象
        pmcu_ProtocolStack_t = new mcu_ProtocolStack(device);
        // DebugPrint("Device :"<<device);
    }

    ~Impl() {
        // 释放 mcu_ProtocolStack 对象
        delete pmcu_ProtocolStack_t;
    }
};


mcu_Control::mcu_Control(std::string device) : pImpl(new Impl(device)) {


    for(uint8_t i =0 ;i<10 ;i++){
        uint8_t temp = getCurrentSector();
        if ( temp == 2){
            return;
        }
        else{
            jump2sector();
        }
        delay(10);
    }
    throw std::logic_error("Cann't enter app!");
    

}

mcu_Control::~mcu_Control() {
    delete pImpl;
}


/* 
    MCU基础功能
*/

void mcu_Control::mcuFmUpdate(const std::string& filepath)
{
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);
    // 打开文件
    std::ifstream file(filepath, std::ios::binary);
    if (!file) {
        std::cerr << "Error opening file: " << filepath << std::endl;
        return;
    }

    // 读取文件内容到 vector
    std::vector<uint8_t> buffer((std::istreambuf_iterator<char>(file)), {});
    size_t total_size = buffer.size();
    size_t sent = 0;
    size_t chunk_size = 128;
    uint16_t bias = 0;
    querystruct_t temp;
    // 循环发送数据，直到所有数据都发送完
    while (sent < total_size) {
        size_t to_send = std::min(chunk_size, total_size - sent);

        // 创建发送头信息，长度为4字节的固定数据
        uint8_t send_bytes[4] = {0xee, 0xee, 0x00, 0x00};

        // 设置偏移值
        send_bytes[2] = bias & 0xFF;
        send_bytes[3] = (bias >> 8) & 0xFF;

        // 从 buffer 中提取需要发送的数据
        std::vector<uint8_t> data_chunk(buffer.begin() + sent, buffer.begin() + sent + to_send);

        // 拼接发送数据，将头信息和数据块拼接
        std::vector<uint8_t> combined_vector(send_bytes, send_bytes + sizeof(send_bytes));
        combined_vector.insert(combined_vector.end(), data_chunk.begin(), data_chunk.end());

        // 调用 sendData 函数发送拼接后的数据包
        uint16_t count_down ;
        uint8_t frame_id;

        // frame_id = pImpl->pmcu_ProtocolStack_t->sendData(RESPONSE_WITH_CONTENT, 0x34, combined_vector.size(), combined_vector.data());
        // while(!pImpl->pmcu_ProtocolStack_t->is_reply_data(frame_id, 0x34,temp)){
        //     int aaa;
        //     std::cin>>aaa;
        //     if(aaa == 1){
        //         break;
        //     }
        // }


        for(count_down =0; count_down<5;count_down++){
            frame_id = pImpl->pmcu_ProtocolStack_t->sendData(RESPONSE_WITH_CONTENT, 0x34, combined_vector.size(), combined_vector.data());
            delay(1000);
            if (pImpl->pmcu_ProtocolStack_t->is_reply_data(frame_id, 0x34,temp))
            {
                if(temp.data[0] == 1){
                    std::cout<<std::dec<<"Successed to send bias :"<<static_cast<int>(bias)<< " frame_id: "<<static_cast<int>(frame_id)  << std::endl;
                    break;
                }
                else if(temp.data[0] == 0){
                    std::cout<<"Update data frame fail"<<std::endl;
                }
                
            }
            else{
                std::cout<<std::dec<<"Failed to send bias :"<<static_cast<int>(bias)<< " frame_id: "<<static_cast<int>(frame_id)  << std::endl;
                uint8_t cnt_sub;
                for(cnt_sub = 0 ;cnt_sub<5; cnt_sub++){
                    delay(500);
                    if (pImpl->pmcu_ProtocolStack_t->is_reply_data(frame_id, 0x34,temp)){         
                        break;
                    }
                    else{
                        std::cout<<"Repeat query reply failed: "<<static_cast<int>(cnt_sub) <<std::endl;
                    }
                } 
                if(cnt_sub<5){
                    break;
                }
            }

        }
        if(count_down == 5){
            std::cout<<"OTA Failed !" << std::endl;
            return ; 
        }
       
        
        

        // 更新已发送数据大小和偏移
        sent += to_send;
        bias++;
    }
}



void mcu_Control::mcuBootFmUpdate(const std::string& filepath)
{
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);
    // 打开文件
    std::ifstream file(filepath, std::ios::binary);
    if (!file) {
        std::cerr << "Error opening file: " << filepath << std::endl;
        return;
    }

    // 读取文件内容到 vector
    std::vector<uint8_t> buffer((std::istreambuf_iterator<char>(file)), {});
    size_t total_size = buffer.size();
    size_t sent = 0;
    size_t chunk_size = 128;
    uint16_t bias = 0;
    querystruct_t temp;
    // 循环发送数据，直到所有数据都发送完
    while (sent < total_size) {
        size_t to_send = std::min(chunk_size, total_size - sent);

        // 创建发送头信息，长度为4字节的固定数据
        uint8_t send_bytes[4] = {0xee, 0xee, 0x00, 0x00};

        // 设置偏移值
        send_bytes[2] = bias & 0xFF;
        send_bytes[3] = (bias >> 8) & 0xFF;

        // 从 buffer 中提取需要发送的数据
        std::vector<uint8_t> data_chunk(buffer.begin() + sent, buffer.begin() + sent + to_send);

        // 拼接发送数据，将头信息和数据块拼接
        std::vector<uint8_t> combined_vector(send_bytes, send_bytes + sizeof(send_bytes));
        combined_vector.insert(combined_vector.end(), data_chunk.begin(), data_chunk.end());

        // 调用 sendData 函数发送拼接后的数据包
        uint16_t count_down ;
        uint8_t frame_id;

        for(count_down =0; count_down<5;count_down++){
            frame_id = pImpl->pmcu_ProtocolStack_t->sendData(RESPONSE_WITH_CONTENT, 0x34, combined_vector.size(), combined_vector.data());
            delay(1000);
            if (pImpl->pmcu_ProtocolStack_t->is_reply_data(frame_id, 0x34,temp))
            {
                if(temp.data[0] == 1){
                    std::cout<<std::dec<<"Successed to send bias :"<<static_cast<int>(bias)<< " frame_id: "<<static_cast<int>(frame_id)  << std::endl;
                    break;
                }
                else if(temp.data[0] == 0){
                    std::cout<<"Update data frame fail"<<std::endl;
                }
                
            }
            else{
                std::cout<<std::dec<<"Failed to send bias :"<<static_cast<int>(bias)<< " frame_id: "<<static_cast<int>(frame_id)  << std::endl;
                uint8_t cnt_sub;
                for(cnt_sub = 0 ;cnt_sub<5; cnt_sub++){
                    delay(500);
                    if (pImpl->pmcu_ProtocolStack_t->is_reply_data(frame_id, 0x34,temp)){         
                        break;
                    }
                    else{
                        std::cout<<"Repeat query reply failed: "<<static_cast<int>(cnt_sub) <<std::endl;
                    }
                } 
                if(cnt_sub<5){
                    break;
                }
            }

        }
        if(count_down == 5){
            std::cout<<"OTA Failed !" << std::endl;
            return ; 
        }
       
        
        

        // 更新已发送数据大小和偏移
        sent += to_send;
        bias++;
    }
}




bool mcu_Control::is_realtime(STEADY_CLOCK time){
    
   

    auto cat_duration_c = time.time_since_epoch();  // 获取从纪元开始到现在的时间持续时间
    auto cat_milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(cat_duration_c).count(); // 转换为毫秒并获取数值
    std::cout << std::dec<< "cat_duration_c Current time since epoch in milliseconds: " << cat_milliseconds << std::endl;

    STEADY_CLOCK current_time = std::chrono::steady_clock::now();
    auto duration_c = current_time.time_since_epoch();  // 获取从纪元开始到现在的时间持续时间
    auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(duration_c).count(); // 转换为毫秒并获取数值
    std::cout << std::dec<< "Current time since epoch in milliseconds: " << milliseconds << std::endl;

    auto duration = current_time - time;
    // 输出时间差的秒级和毫秒级信息
    auto duration_s = std::chrono::duration_cast<std::chrono::duration<double>>(duration);
    std::cout << "Duration (seconds): " << duration_s.count() << " s" << std::endl;

    auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(duration);
    std::cout << std::dec << "Duration (milliseconds): " << duration_ms.count() << " ms" << std::endl;


    

    if(duration_ms.count() < REALTIME_LIMIT){
        std::cout<<"TRUE"<<std::endl;
        return true;
    } 
    return false;
}


void mcu_Control::mcuReset(void)
{
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);

    pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST, 0x30, 0, nullptr);
}


uint8_t mcu_Control::getCurrentSector(void)
{
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);

    std::string str = this-> getSoftwareVer();
    size_t pos = str.find('-');

    if (pos != std::string::npos) {
        std::string temp = str.substr(0, pos);
        if (temp == "boot") {
            return 1;
        } else if (temp == "app") {
            return 2;
        }
    }
    std::cerr << "Error: No '-' found in the software version string." << std::endl;
    return 0;

}


std::string mcu_Control::getSoftwareVer(){

    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);

    uint16_t frame_id;
    querystruct_t temp;
    frame_id = pImpl->pmcu_ProtocolStack_t->sendData(RESPONSE_WITH_CONTENT, 0x32, 0, nullptr);
    for(uint8_t i = 0 ; i<5 ; i++){
        delay(200);
        if(pImpl->pmcu_ProtocolStack_t->is_reply_data(frame_id, 0x32,temp) ){
            std::string str((char*)temp.data);
            return str;
        }
    }
    
    return "Get failed !";
    
}


void mcu_Control::jump2sector(void)
{
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);

    pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST, 0x31, 0, nullptr);
}




/* 
    BSP功能函数
*/
void mcu_Control::CB1_V3_LEDcontrol(uint8_t mode, uint32_t brg, uint16_t duty)
{
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);

    uint8_t dt[6]={0};
    dt[0] = mode;
    dt[1] = duty;
    dt[2] = duty>>8;
    dt[3] = brg>>16;
    dt[4] = brg>>8;
    dt[5] = brg;
    pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST, 0x40, sizeof(dt), dt);

} 


void mcu_Control::CB1_V2_TailLight(uint8_t tail_mode, uint32_t tail_rgb, uint16_t duty)
{
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);

    uint8_t dt[6]={0};
    dt[0] = tail_mode;
    dt[1] = duty;
    dt[2] = duty>>8;
    dt[3] = tail_rgb>>16;
    dt[4] = tail_rgb>>8;
    dt[5] = tail_rgb;
    pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST, 0x43, sizeof(dt), dt);

}

void mcu_Control::CB1_V3_FlashLight(uint8_t mode)
{
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);

    uint8_t dt[1]={0};
    dt[0] = mode;
    pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST, 0x41, sizeof(dt), dt);

} 


void mcu_Control::setFloodLight(uint16_t data)
{
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);

    uint8_t dt[2]={0};
    if(data>0 && data<100){
        dt[0] = data>>8;
        dt[1] = data;
        pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST,0x31,2,dt);
    }
    else{
        std::cout<<"请输入0-100的数值!"<<std::endl;
    }
}

void mcu_Control::setGearMotor(uint16_t data)
{
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);

    uint8_t dt[2]={0};
    if(data>=0 && data<=2000){
        uint16_t temp = data + 500;

        dt[0] = temp;
        dt[1] = temp>>8;
        pImpl->pmcu_ProtocolStack_t->sendData(NO_RESPONSE_REQUEST, 0x42, 2, dt);
    }
    else{
        std::cout<<"请输入0-2000的数值!"<<std::endl;
    }
    
}


bool mcu_Control::getTouchState(uint8_t &state){
    // pImpl->pmcu_ProtocolStack_t->print_noack_request_notify_struct_queue(); 

    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);

    querystruct_t temp;
    if(pImpl->pmcu_ProtocolStack_t->is_noack_data_request(0x61,temp) == true){
        // std::cout<<"touch judge"<<std::endl;
        state = temp.data[0] ; 
        return is_realtime(temp.current_time);
    }
    return false;
    

}



std::vector<uint16_t> mcu_Control::getUltraSonicDistance(){
    
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);

    querystruct_t temp;
    std::vector<uint16_t> dis;
    if(pImpl->pmcu_ProtocolStack_t->is_noack_data_request(0x60,temp) == true){
        dis.push_back(temp.data[1]<<8 | temp.data[2]);
        dis.push_back(temp.data[5]<<8 | temp.data[6]);
        
        STEADY_CLOCK end = std::chrono::steady_clock::now();

        // 计算两个时间点之间的差值
        std::chrono::duration<double, std::milli> diff = end - temp.current_time;
        // 输出结果，以毫秒为单位
        DebugPrint("Get Latest Ultrasonic Overtime: " << diff.count() << " ms" ) ;
    }
    return dis;  
}






void mcu_Control::CB1Eyeblink(uint16_t duty){
    static std::atomic<bool> sign{false}; // 静态局部变量，确保线程安全

    if (!sign.load()) { // 使用 load 方法读取原子变量的值
        sign.store(true); // 使用 store 方法设置原子变量的值
        std::thread myThread([this, duty]() {
            CB1_V3_LEDcontrol(1, 0, 0);
            delay(duty);
            CB1_V3_LEDcontrol(1, 0xFF00E0, 0); // 假设这是关闭LED的操作
            sign.store(false);
        });
        // 分离线程
        myThread.detach();
    }
}


void mcu_Control::StressTest(uint32_t conut){
    for(size_t i = 0 ;i<conut ; i++){
        pImpl->pmcu_ProtocolStack_t->sendData(RESPONSE_WITH_CONTENT, 0x32, 0, nullptr);
    }
    

}



