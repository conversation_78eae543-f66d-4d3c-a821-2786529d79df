#include <thread>
#include <iomanip> // 用于 std::hex 和 std::setfill


#include "mcu_protocolstack.h"


// 将 uint8_t 数组转换为十六进制字符串
std::string toHexString(const uint8_t* byteArray, size_t length) {
    std::stringstream ss;
    ss << std::hex << std::setfill('0'); // 设置为十六进制输出，并补零
    for (size_t i = 0; i < length; ++i) {
        ss<<"0x" << std::setw(2) << static_cast<int>(byteArray[i]); // 每个字节转换为两位十六进制数
        if (i < length - 1) {
            ss << ","; // 在每个数值后加上逗号
        }
    }
    return ss.str();
}



// 打印时间点
void print_time_point(const std::chrono::time_point<std::chrono::steady_clock>& time_point) {

    auto duration_c = time_point.time_since_epoch();  // 获取从纪元开始到现在的时间持续时间
    // auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(duration_c).count(); // 转换为毫秒并获取数值
    // DebugPrint( std::dec<< "Current time since epoch in milliseconds: " << milliseconds);
}

// 从队列中获取指定长度的数据
bool getBytesFromQueue(std::deque<uint8_t>& dataQueue, std::vector<uint8_t>& outputBuffer, size_t length) {
    if (dataQueue.size() < length) {
        // 如果队列中的数据不足指定长度，返回 false 表示失败
        return false;
    }

    // 从队列中获取指定长度的数据
    for (size_t i = 0; i < length; ++i) {
        uint8_t byte = dataQueue.front(); // 获取队列的第一个字节
        dataQueue.pop_front(); // 从队列中移除该字节
        outputBuffer.push_back(byte); // 将字节添加到输出缓冲区
    }

    return true; // 成功获取指定长度的数据
}

// 从队列中获取指定长度的数据（非破坏性检查）
bool checkBytesFromQueue(const std::deque<uint8_t>& dataQueue, size_t length) {
    return dataQueue.size() >= length; // 检查队列中的元素数量
}

// 打印队列内容
void printQueue(const std::deque<uint8_t>& queue) {
    std::cout << "Queue contents: ";
    for (const auto& value : queue) {
        std::cout << std::hex << "0x" << static_cast<int>(value) << " "; // 以十六进制打印
    }
    std::cout << std::dec << std::endl; // 恢复回十进制
}



/* Exported types ------------------------------------------------------------*/


uint8_t frame_header[HEADER_LEN]={FRAME_HEAD,0x00,0x00,0x16,0x34,0x00,0x00,0x00,0x00};


mcu_ProtocolStack::mcu_ProtocolStack(std::string device)
{
    program_start_time = std::chrono::steady_clock::now();

    // DrLogger::initialize("mcu_control","./");
    // logger_t = new DrLogger("mcu_control");




    this->serial = new SerialPort(device);
    // // 串口设备路径（根据实际情况修改）

    
    // 打开串口
    if (!this->serial->open()) {
        std::cerr << "Failed to open serial port!" << std::endl;
    }

    // 配置串口 || !serial->setRecvOvertime(1, 10)        !serial->setRecvOvertime(0,0)  ||   !serial->setNonBlocking()
    if (!serial->setBaudRate(B115200) ||
        !serial->setDataBits(8) ||
        !serial->setStopBits(1) ||
        !serial->setParity(false, false) ||
        !serial->setFlowControl(false, false)||
        !serial->setRecvOvertime(1,255)||
        !serial->disableCanonicalMode()
        ) {
        
        std::cerr << "Failed to configure serial port!" << std::endl;
        serial->close();
        delete serial;
        throw std::runtime_error("Failed to configure serial port!");
    }


    std::cout <<serial->getCurrentConfiguration() << std::endl;
    // logger_t->debug("Mcu_protocolstack Init:" +device+ serial->getCurrentConfiguration());

    serial->clearInputBuffer();

    // std::thread thread1([this]() { this->Serial_Recv(); });
    // thread1.detach();

    std::thread thread2([this]() { this->rx_parse(); });
    thread2.detach();

}

mcu_ProtocolStack::~mcu_ProtocolStack()
{
    this->serial->close();
    delete this->serial;
}



void mcu_ProtocolStack::rx_parse() {
    constexpr uint8_t HEADER[] = {FRAME_HEAD}; // 帧头标识
    std::deque<uint8_t> serialDataQueue;      // 缓存接收的数据
    std::vector<uint8_t> frameBuffer;
    frameHeader_t fh_t;

    const int FRAME_HEAD_SIZE = 10;

    DebugPrint("Enter rx_parse");

    while (1) {
        // 1. 尝试从串口读取数据
        int readlen = serial->read(rxbuffer, RXBUFFERLEN);
        if (readlen > 0) {
            // 将接收到的数据推入队列
            serialDataQueue.insert(serialDataQueue.end(), rxbuffer, rxbuffer + readlen);
        }

        // 2. 解析数据
        while (serialDataQueue.size() >= FRAME_HEAD_SIZE) {
            // 检查帧头
            if (serialDataQueue[0] == HEADER[0] && serialDataQueue[1] == HEADER[1]) {
                // 提取帧头
                if (serialDataQueue.size() < FRAME_HEAD_SIZE) {
                    // 等待更多数据到达
                    break;
                }
                frameBuffer.assign(serialDataQueue.begin(), serialDataQueue.begin() + FRAME_HEAD_SIZE);
                serialDataQueue.erase(serialDataQueue.begin(), serialDataQueue.begin() + FRAME_HEAD_SIZE);

                // 解析帧头
                fh_t.frame_id = frameBuffer[FRAME_ID_BIAS];
                fh_t.version = frameBuffer[FRAME_VERSION_BIAS];
                fh_t.type = frameBuffer[FRAME_TYPE];
                fh_t.func_code = frameBuffer[FRAME_FUNC_CODE];
                fh_t.size = (frameBuffer[FRAME_DATA_SIZE_BIAS + 1] << 8) | frameBuffer[FRAME_DATA_SIZE_BIAS];
                fh_t.crc16 = (frameBuffer[FRAME_CRC_BIAS + 1] << 8) | frameBuffer[FRAME_CRC_BIAS];

                // 检查帧数据长度
                if (serialDataQueue.size() < fh_t.size) {
                    // 数据不足，重新等待
                    break;
                }

                // 提取帧数据
                frameBuffer.insert(frameBuffer.end(), serialDataQueue.begin(), serialDataQueue.begin() + fh_t.size);
                serialDataQueue.erase(serialDataQueue.begin(), serialDataQueue.begin() + fh_t.size);

                // 校验CRC
                uint16_t crc = crc16(frameBuffer.data() + FRAME_DATA_BIAS, fh_t.size);
                if (crc != fh_t.crc16) {
                    DebugPrint("CRC error: expected " << std::hex << fh_t.crc16 << ", got " << crc);
                    continue; // 跳过此帧，等待下一个帧
                }

                // 分发帧处理
                processFrame(fh_t, frameBuffer);
            } else {
                // 如果不是帧头，丢弃一个字节并继续
                serialDataQueue.pop_front();
            }
        }
    }
}

// 处理完整帧
void mcu_ProtocolStack::processFrame(const frameHeader_t& fh_t, const std::vector<uint8_t>& frameBuffer) {
    const uint8_t* frameData = frameBuffer.data() + FRAME_DATA_BIAS;

    switch (fh_t.type) {
        case 0x15: {
            noack_request_notify_struct_t temp;
            temp.func_code = fh_t.func_code;
            temp.current_time = std::chrono::steady_clock::now();
            temp.notify_queue_size = fh_t.size;
            temp.notify_queue = new uint8_t[fh_t.size];
            memcpy(temp.notify_queue, frameData, fh_t.size);
            noack_request_notify_struct_enqueue(temp);
            break;
        }
        case 0x16: {
            frame_header[FRAME_ID_BIAS] = fh_t.frame_id;
            frame_header[FRAME_TYPE] = fh_t.type;
            frame_header[FRAME_FUNC_CODE] = fh_t.func_code;
            frame_header[FRAME_DATA_SIZE_BIAS] = 0;
            frame_header[FRAME_DATA_SIZE_BIAS + 1] = 0;
            ssize_t bytes_sent = serial->write(frame_header, 10);
            if (bytes_sent < 0) {
                std::cerr << "Error writing to serial port to ack" << std::endl;
            } else {
                ack_request_notify_struct temp;
                temp.current_time = std::chrono::steady_clock::now();
                temp.func_code = fh_t.func_code;
                temp.notify_queue = new uint8_t[fh_t.size];
                memcpy(temp.notify_queue, frameData, fh_t.size);
                temp.notify_queue_size = fh_t.size;
                ack_request_notify_struct_enqueue(temp);
            }
            break;
        }
        case 0x17: {
            data_request_notify_struct_t temp;
            temp.current_time = std::chrono::steady_clock::now();
            temp.func_code = fh_t.func_code;
            temp.frame_id = fh_t.frame_id;
            temp.notify_queue = new uint8_t[fh_t.size];
            memcpy(temp.notify_queue, frameData, fh_t.size);
            temp.notify_queue_size = fh_t.size;
            data_request_notify_struct_enqueue(temp);
            break;
        }
        case 0x21: {
            if (fh_t.size != 0) {
                reply_data_notify_t temp;
                temp.current_time = std::chrono::steady_clock::now();
                temp.func_code = fh_t.func_code;
                temp.frame_id = fh_t.frame_id;
                temp.notify_queue_size = fh_t.size;
                temp.notify_queue = new uint8_t[fh_t.size];
                memcpy(temp.notify_queue, frameData, fh_t.size);
                reply_data_notify_enqueue(temp);
            } else {
                ack_notify_struct_t temp;
                temp.current_time = std::chrono::steady_clock::now();
                temp.func_code = fh_t.func_code;
                temp.frame_id = fh_t.frame_id;
                ack_notify_enqueue(temp);
            }
            break;
        }
        default:
            DebugPrint("Unknown frame type: " << fh_t.type);
            break;
    }
}



// ack_notify 入队
bool mcu_ProtocolStack::ack_notify_enqueue(ack_notify_struct_t t) {
    std::lock_guard<std::mutex> lock(ack_mtx);

    // 如果队列已满，删除最早的数据
    if (ack_notify_struct_queue.size() >= max_queue_size) {
        ack_notify_struct_queue.erase(ack_notify_struct_queue.begin());
    }

    // 添加新数据
    ack_notify_struct_queue.push_back(t);
    return true;
}

// reply_data_notify 入队
bool mcu_ProtocolStack::reply_data_notify_enqueue(reply_data_notify_t t) {
    std::lock_guard<std::mutex> lock(reply_data_mtx);

    // 如果队列已满，删除最早的数据
    if (reply_data_notify_queue.size() >= max_queue_size) {
        reply_data_notify_queue.erase(reply_data_notify_queue.begin());
    }

    // 添加新数据
    reply_data_notify_queue.push_back(t);
    return true;
}



// data_request_notify_struct 入队
bool mcu_ProtocolStack::data_request_notify_struct_enqueue(data_request_notify_struct_t t) {
    std::lock_guard<std::mutex> lock(data_request_mtx);

    // 如果队列已满，删除最早的数据
    if (data_request_notify_struct_queue.size() >= max_queue_size) {
        data_request_notify_struct_queue.erase(data_request_notify_struct_queue.begin());
    }

    // 添加新数据
    data_request_notify_struct_queue.push_back(t);
    return true;
}

bool mcu_ProtocolStack::ack_request_notify_struct_enqueue(ack_request_notify_struct_t t){
    std::lock_guard<std::mutex> lock(ack_request_mtx);

    // 如果队列已满，删除最早的数据
    if (ack_request_notify_struct_queue.size() >= max_queue_size) {
        ack_request_notify_struct_queue.erase(ack_request_notify_struct_queue.begin());
    }

    // 添加新数据
    ack_request_notify_struct_queue.push_back(t);
    return true;
}

// noack_request_notify 入队
bool mcu_ProtocolStack::noack_request_notify_struct_enqueue(noack_request_notify_struct_t t) {
    std::lock_guard<std::mutex> lock(noack_request_mtx);

    // 如果队列已满，删除最早的数据
    if (noack_request_notify_struct_queue.size() >= max_queue_size) {
        noack_request_notify_struct_queue.erase(noack_request_notify_struct_queue.begin());
    }

    // 添加新数据
    noack_request_notify_struct_queue.push_back(t);
    return true;
}



uint8_t mcu_ProtocolStack::sendData(FuncCode frame_type, uint8_t func_code, uint16_t len, uint8_t *dat)
{
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);

    static uint8_t frame_id = 0;  

    frame_id ++;  

    frame_header[FRAME_ID_BIAS]  = frame_id;
    frame_header[FRAME_TYPE]  = frame_type;
    frame_header[FRAME_FUNC_CODE]  = func_code;
    frame_header[FRAME_DATA_SIZE_BIAS]  =  len ;
    frame_header[FRAME_DATA_SIZE_BIAS+1]  =len >> 8;

    uint16_t crc=crc16(dat,len) ;
    frame_header[FRAME_CRC_BIAS]  =  crc;
    frame_header[FRAME_CRC_BIAS+1]  = crc >> 8;

    DebugPrint(std::hex<<"Send data crc16: "<< static_cast<int>(crc));
    // std::cout<<std::hex<<"crc16:   "<< static_cast<int>(crc)<<std::endl;

    memcpy( (frame_header+FRAME_DATA_BIAS), dat, len);

    

    ssize_t bytes_sent = serial->write( frame_header, 10+len);
    if (bytes_sent < 0) {
        std::cerr << "Error writing to serial port" << std::endl;
    }
    else{
        frame_mgt_t *frame = new frame_mgt_t;
        frame->func_code = func_code;
        frame->frame_id = frame_id;
        tx_frame_queue.push_back(*frame);
        delete frame;
        frame = nullptr;
        std::string str = toHexString(frame_header, 10+len);

        // logger_t->debug("Current serialport:" + serial->getCurrentDeviceName() );
        // logger_t->debug("Mcu_protocolstack Send:" +str);
        DebugPrint("Send Hex date :"<<str);

    }
    return frame_id;
}





// 抽象函数：遍历容器并查找指定 frame_id 的元素
int mcu_ProtocolStack::findFrameById(std::vector<frame_mgt_t>& cmd_queue, uint8_t target_frame_id) {
    // 使用传统迭代器遍历容器
    for (auto it = cmd_queue.begin(); it != cmd_queue.end(); ++it) {
        std::cout << "Frame ID: " << static_cast<int>(it->frame_id) << std::endl;
        
        if (it->frame_id == target_frame_id) {
            // 直接返回迭代器与 begin 的距离
            return static_cast<int>(std::distance(cmd_queue.begin(), it));
        }
    }
    return -1;
}

int mcu_ProtocolStack::findFrameByFuncCode(std::vector<frame_mgt_t>& cmd_queue, uint8_t target_func_code)
{
    // 使用传统迭代器遍历容器
    for (auto it = cmd_queue.end(); it != cmd_queue.begin(); --it) {
        std::cout << "Checking Frame ID: " << static_cast<int>(it->frame_id) << std::endl;
        
        if (it->frame_id == target_func_code) {
            return static_cast<int>(std::distance(cmd_queue.begin(), it));
        }
    }
    
     return -1; // 没有找到指定 frame_id 的元素
}
int mcu_ProtocolStack::findRTXRemap(uint8_t func_code){
    int index = findFrameByFuncCode(tx_frame_queue, 0x33);
    if(index){
        int sub_index = findFrameById(rx_frame_queue,tx_frame_queue[index].frame_id) ;
        if( sub_index ){
            return sub_index;
        }
    }
    return -1;
}


bool mcu_ProtocolStack::is_reply_ack(uint8_t func_code, querystruct_t &temp ){
    std::lock_guard<std::mutex> lock(ack_mtx);
    for (auto it = ack_notify_struct_queue.begin(); it != ack_notify_struct_queue.end(); ++it) {
        for (auto it_tx = tx_frame_queue.begin(); it_tx != tx_frame_queue.end(); ++it_tx){
            if (it->func_code == func_code && it->frame_id == it_tx->frame_id) { // 假设 ack_notify_struct_t 结构中有 frame_id
                ack_notify_struct_t t = *it;
                temp.current_time = t.current_time;
      
                ack_notify_struct_queue.erase(it);
                return true;
            }
        }
        
    }
    return false;
}
bool mcu_ProtocolStack::is_reply_data(uint8_t func_code, querystruct_t &temp){
    std::lock_guard<std::mutex> lock(reply_data_mtx);
    for (auto it = reply_data_notify_queue.begin(); it != reply_data_notify_queue.end(); ++it) {
        for (auto it_tx = tx_frame_queue.begin(); it_tx != tx_frame_queue.end(); ++it_tx){
            if (it->func_code == it_tx->func_code && it->frame_id == it_tx->frame_id) { // 假设 reply_data_notify_t 结构中有 frame_id
                reply_data_notify_t t = *it;

                memcpy(temp.data, t.notify_queue, t.notify_queue_size);
                temp.size = t.notify_queue_size;
                temp.current_time = t.current_time;

                reply_data_notify_queue.erase(it);
                tx_frame_queue.erase(it_tx);
                return true;
            }

        }
        
    }
    return false;
}
bool mcu_ProtocolStack::is_data_request(uint8_t frame_id, uint8_t func_code, querystruct_t &temp){
    std::lock_guard<std::mutex> lock(data_request_mtx);
    for (auto it = data_request_notify_struct_queue.begin(); it != data_request_notify_struct_queue.end(); ++it) {
        if (it->func_code == func_code && it->frame_id == frame_id) { // 假设 data_request_notify_struct_t 结构中有 frame_id
            data_request_notify_struct_t t = *it;
            memcpy(t.notify_queue, temp.data, temp.size);
            temp.size = t.notify_queue_size;
            temp.current_time = t.current_time;
            data_request_notify_struct_queue.erase(it);
            return true;
        }
    }
    
    return false;
}

bool mcu_ProtocolStack::is_noack_data_request(uint8_t func_code, querystruct_t &temp) {
    std::lock_guard<std::mutex> lock(noack_request_mtx);
    // 修正逆序遍历的迭代器
    for (auto it = noack_request_notify_struct_queue.rbegin(); it != noack_request_notify_struct_queue.rend(); ++it) {
        if (it->func_code == func_code) {
            noack_request_notify_struct_t t = *it;
            
            // 检查目标缓冲区大小是否足够
            if (t.notify_queue_size > sizeof(temp.data)) {
                std::cerr << "Error: buffer overflow risk. notify_queue_size exceeds temp.data size." << std::endl;
                return false;
            }

            // 执行内存拷贝操作
            memcpy(temp.data, t.notify_queue, t.notify_queue_size);
            temp.size = t.notify_queue_size;
            temp.current_time = t.current_time;

            // 正确删除元素
            noack_request_notify_struct_queue.erase(std::next(it).base());
            return true;
        }
    }
    return false;
}


//根据frame_id寻找是否对应
bool mcu_ProtocolStack::is_reply_ack(uint8_t frame_id, uint8_t func_code, querystruct_t &temp ){
    std::lock_guard<std::mutex> lock(ack_mtx);
    for (auto it = ack_notify_struct_queue.begin(); it != ack_notify_struct_queue.end(); ++it) {
        for (auto it_tx = tx_frame_queue.begin(); it_tx != tx_frame_queue.end(); ++it_tx){
            if (it->func_code == func_code && it->frame_id == frame_id) { // 假设 ack_notify_struct_t 结构中有 frame_id
                ack_notify_struct_t t = *it;
                temp.current_time = t.current_time;
      
                ack_notify_struct_queue.erase(it);
                return true;
            }
        }
        
    }
    return false;
}
bool mcu_ProtocolStack::is_reply_data(uint8_t frame_id,uint8_t func_code, querystruct_t &temp){
    std::lock_guard<std::mutex> lock(reply_data_mtx);
    for (auto it = reply_data_notify_queue.begin(); it != reply_data_notify_queue.end(); ++it) {
        for (auto it_tx = tx_frame_queue.begin(); it_tx != tx_frame_queue.end(); ++it_tx){
            if (it->func_code == it_tx->func_code && it->frame_id == frame_id) { // 假设 reply_data_notify_t 结构中有 frame_id
                reply_data_notify_t t = *it;

                memcpy(temp.data, t.notify_queue, t.notify_queue_size);
                temp.size = t.notify_queue_size;
                temp.current_time = t.current_time;

                reply_data_notify_queue.erase(it);
                tx_frame_queue.erase(it_tx);
                return true;
            }

        }
        
    }
    return false;
}



STEADY_CLOCK mcu_ProtocolStack::getProgramRunTime(){
    return program_start_time;
}





// 打印队列值
void mcu_ProtocolStack::print_ack_notify_struct_queue() {
    std::cout << "ack_notify_struct_queue:\n";
    for (const auto& item : ack_notify_struct_queue) {
        std::cout << "func_code: 0x" << std::hex << static_cast<int>(item.func_code) 
                  << ", frame_id: " << static_cast<int>(item.frame_id) 
                  << ", current_time: ";
        print_time_point(item.current_time);
    }
    std::cout << std::endl;
}

// 打印 reply_data_notify_queue
void mcu_ProtocolStack::print_reply_data_notify_queue() {
    std::cout << "reply_data_notify_queue:\n";
    for (const auto& item : reply_data_notify_queue) {
        std::cout << "func_code: 0x" << std::hex << static_cast<int>(item.func_code)
                  << ", frame_id: " << static_cast<int>(item.frame_id) 
                  << ", notify_queue_size: " << item.notify_queue_size
                  << ", notify_queue:"<<toHexString(item.notify_queue,item.notify_queue_size)
                  << ", current_time: ";
        print_time_point(item.current_time);
    }
    std::cout << std::endl;
}

// 打印 data_request_notify_struct_queue
void mcu_ProtocolStack::print_data_request_notify_struct_queue() {
    std::cout << "data_request_notify_struct_queue:\n";
    for (const auto& item : data_request_notify_struct_queue) {
        std::cout << "func_code: 0x" << std::hex << static_cast<int>(item.func_code) 
                  << ", frame_id: " << static_cast<int>(item.frame_id) 
                  << ", notify_queue_size: " << item.notify_queue_size 
                  << ", notify_queue:"<<toHexString(item.notify_queue,item.notify_queue_size)
                  << ", current_time: ";
        print_time_point(item.current_time);
    }
    std::cout << std::endl;
}

// 打印 noack_request_notify_struct_queue
void mcu_ProtocolStack::print_noack_request_notify_struct_queue() {
    std::cout << "noack_request_notify_struct_queue:\n";
    for (const auto& item : noack_request_notify_struct_queue) {
        std::cout << "func_code: 0x" << std::hex << static_cast<int>(item.func_code) 
                  << ", notify_queue_size: " << item.notify_queue_size 
                  << ", notify_queue:"<<toHexString(item.notify_queue,item.notify_queue_size)
                  << ", current_time: ";
                  print_time_point(item.current_time);
        //  // 输出时间差的秒级和毫秒级信息
        // auto duration_s = std::chrono::duration_cast<std::chrono::duration<double>>(item.current_time);
        // std::cout << "print Duration (seconds): " << duration_s.count() << " s" << std::endl;

        // auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(item.current_time);
        // std::cout << std::dec << "print Duration (milliseconds): " << duration_ms.count() << " ms" << std::endl;
    }
    std::cout << std::endl;
}



uint16_t mcu_ProtocolStack::crc16(const uint8_t *data, size_t length) {
    uint16_t crc = 0xFFFF; // 初始值

    for (size_t i = 0; i < length; i++) {
        crc ^= data[i]; // 将当前字节与CRC值异或

        for (int j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc >>= 1;
                crc ^= 0xA001;  // CRC-16多项式（0xA001）
            } else {
                crc >>= 1;
            }
        }
    }

    return crc;
}
