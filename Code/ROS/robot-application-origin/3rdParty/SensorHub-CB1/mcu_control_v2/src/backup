

       

        int framebyte_cnt=0;
        DebugPrint("Recv data frame: "<<toHexString(rxbuffer, readlen) );


        while(framebyte_cnt < readlen){
            if(rxbuffer[framebyte_cnt] ==  header[0] && rxbuffer[framebyte_cnt+1] ==  header[1]){
                // DebugPrint("Parse data frame: "<<toHexString(rxbuffer, readlen) );

                fh_t.frame_id = rxbuffer[framebyte_cnt + FRAME_ID_BIAS];
                fh_t.version = rxbuffer[framebyte_cnt + FRAME_VERSION_BIAS];
                fh_t.type = rxbuffer[framebyte_cnt + FRAME_TYPE];
                fh_t.func_code = rxbuffer[framebyte_cnt + FRAME_FUNC_CODE];
                fh_t.size = rxbuffer[framebyte_cnt + FRAME_DATA_SIZE_BIAS]<<8 | rxbuffer[framebyte_cnt + FRAME_DATA_SIZE_BIAS+1];
                fh_t.crc16 = rxbuffer[framebyte_cnt + FRAME_CRC_BIAS]<<8 | rxbuffer[framebyte_cnt + FRAME_CRC_BIAS+1];

                

                if( fh_t.type == 0x15 ){
                    // DebugPrint("Noack request data frame: "<<toHexString(rxbuffer, readlen) );

                    noack_request_notify_struct_t temp;
                    temp.func_code  = fh_t.func_code;
                    temp.current_time = std::chrono::steady_clock::now();
                    temp.notify_queue_size = fh_t.size;
                    temp.notify_queue =  new uint8_t[fh_t.size];
                    
                    // DebugPrint("0x15 :rxbuffer");
                    memcpy(temp.notify_queue , rxbuffer + framebyte_cnt +FRAME_DATA_BIAS, fh_t.size);

                    noack_request_notify_struct_enqueue(temp);
                    
                }
                else if ( fh_t.type == 0x16 ){ //ACK请求，自动回复
                    frame_header[FRAME_ID_BIAS]  = fh_t.frame_id;
                    frame_header[FRAME_TYPE]  = fh_t.type;
                    frame_header[FRAME_FUNC_CODE]  = fh_t.func_code;
                    frame_header[FRAME_DATA_SIZE_BIAS]  = 0;
                    frame_header[FRAME_DATA_SIZE_BIAS+1]  = 0 ;
                    ssize_t bytes_sent = serial->write(frame_header , 10);
                    if (bytes_sent < 0) {
                        std::cerr << "Error writing to serial port to ack" << std::endl;
                    }
                    else{
                        ack_request_notify_struct temp;
                        temp.current_time = std::chrono::steady_clock::now();

                        temp.func_code = fh_t.func_code;
                        temp.notify_queue =  new uint8_t[fh_t.size];
                        memcpy(temp.notify_queue, rxbuffer + framebyte_cnt +FRAME_DATA_BIAS,fh_t.size);
                        temp.notify_queue_size = fh_t.size;

                        ack_request_notify_struct_enqueue(temp);
                        

                    }
                }
                else if ( fh_t.type == 0x17){ //携带数据请求
                    DebugPrint("enter func type 0x17");
                    data_request_notify_struct_t temp;
                    temp.current_time = std::chrono::steady_clock::now();

                    temp.func_code  = fh_t.func_code;
                    temp.frame_id = fh_t.frame_id;
                    temp.notify_queue =  new uint8_t[fh_t.size];
                    memcpy(temp.notify_queue, rxbuffer + framebyte_cnt + FRAME_DATA_BIAS,fh_t.size);
                    temp.notify_queue_size = fh_t.size;

                    data_request_notify_struct_enqueue(temp);
                }
                else if ( fh_t.type == 0x21){ //应答帧
                    DebugPrint("enter func type 0x21");

                    
                    
                    if(fh_t.size!=0){
                        reply_data_notify_t temp;
                    
                        temp.current_time = std::chrono::steady_clock::now();
                        temp.func_code  = fh_t.func_code;
                        temp.frame_id = fh_t.frame_id;
                    
                        temp.notify_queue_size = fh_t.size;
                        temp.notify_queue =  new uint8_t[fh_t.size];
                        memcpy(temp.notify_queue, rxbuffer + framebyte_cnt + FRAME_DATA_BIAS,fh_t.size);
                        reply_data_notify_enqueue(temp);
                    }
                    else{
                        DebugPrint("Ack data frame: "<<toHexString(rxbuffer, readlen) );
                        ack_notify_struct_t temp;
                        temp.current_time = std::chrono::steady_clock::now();
                        temp.func_code  = fh_t.func_code;
                        temp.frame_id = fh_t.frame_id;
                        ack_notify_enqueue(temp);
                    }
                    
                    
                    // std::cout<<"fh_t.size:"<<fh_t.size<<std::endl;
                    // std::cout<<"frame_header+FRAME_DATA_BIAS:"<<toHexString(rxbuffer+FRAME_DATA_BIAS, fh_t.size)<<std::endl;
                }
                framebyte_cnt = framebyte_cnt + FRAME_DATA_BIAS + fh_t.size;
                DebugPrint("framebyte_cnt: "<<framebyte_cnt);                        
            }
            else{
                framebyte_cnt = framebyte_cnt + 2;
            }
                
                
            
        }