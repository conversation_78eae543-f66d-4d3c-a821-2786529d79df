cmake_minimum_required(VERSION 3.10)

# Project configuration
project(SensorHubProject LANGUAGES C CXX)

# Set default build type to Release if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Set the architecture (default is arm64, can override with -DARCH=arm or -DARCH=x86)
set(ARCH arm64 CACHE STRING "Target architecture (x86, arm, arm64)")

# Set cross-compiler based on architecture
if(ARCH STREQUAL "arm")
    set(CMAKE_C_COMPILER arm-linux-gnueabihf-gcc)
    set(CMAKE_CXX_COMPILER arm-linux-gnueabihf-g++)
    add_compile_options(-Wall -march=armv7-a -mfloat-abi=hard -mfpu=vfpv3-d16)
elseif(ARCH STREQUAL "arm64")
    set(CMAKE_C_COMPILER aarch64-linux-gnu-gcc)
    set(CMAKE_CXX_COMPILER aarch64-linux-gnu-g++)
    add_compile_options(-Wall -march=armv8-a -fPIC -g)
else()
    set(CMAKE_C_COMPILER gcc)
    set(CMAKE_CXX_COMPILER g++)
    add_compile_options(-Wall -fPIC)
endif()

# Directories
set(INC_DIR include)
set(OUTPUT_DIR ../output)
set(LIB_DIR ${OUTPUT_DIR}/lib)
set(OBJ_DIR ${OUTPUT_DIR}/obj)
set(BIN_DIR ${OUTPUT_DIR}/bin)

# Create output directories
file(MAKE_DIRECTORY ${LIB_DIR} ${OBJ_DIR} ${BIN_DIR})

# Source files
file(GLOB CPP_SRCS "src/*.cpp")

# Object files
add_library(sensorhub_shared SHARED ${CPP_SRCS})
add_library(sensorhub_static STATIC  ${CPP_SRCS})

# Include directories
target_include_directories(sensorhub_shared PUBLIC ${INC_DIR})
target_include_directories(sensorhub_static PUBLIC ${INC_DIR})

# Link libraries for shared/static libraries
set_target_properties(sensorhub_shared PROPERTIES
    OUTPUT_NAME "sensorhub"
    ARCHIVE_OUTPUT_DIRECTORY ${LIB_DIR}
    LIBRARY_OUTPUT_DIRECTORY ${LIB_DIR}
    RUNTIME_OUTPUT_DIRECTORY ${BIN_DIR}
)

set_target_properties(sensorhub_static PROPERTIES
    OUTPUT_NAME "sensorhub"
    ARCHIVE_OUTPUT_DIRECTORY ${LIB_DIR}
)

# Executable target
add_executable(mcu_control ${CPP_SRCS})
target_include_directories(mcu_control PUBLIC ${INC_DIR})
target_link_libraries(mcu_control sensorhub_shared pthread)
set_target_properties(mcu_control PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${BIN_DIR}
)

# Add compile definitions
target_compile_definitions(sensorhub_shared PRIVATE SENSORHUB_SHARED)
target_compile_definitions(sensorhub_static PRIVATE SENSORHUB_STATIC)
target_compile_definitions(mcu_control PRIVATE MCU_CONTROL)


