#ifndef _COMMON_LIB_H
#define _COMMON_LIB_H

#include <iostream>
#include <cstring>
#include <unistd.h>  // 包含sleep() 和 usleep()
#include <vector>
#include <queue>
#include <chrono>
#include <mutex>
#include <condition_variable>
#include <variant>
#include <cstdint>
#include <sstream>
#include <string>
#include <fcntl.h>

// #define DebugPrint(info) std::cout<<"Debug:"<<info<<std::endl
#define DebugPrint(info) 


#endif