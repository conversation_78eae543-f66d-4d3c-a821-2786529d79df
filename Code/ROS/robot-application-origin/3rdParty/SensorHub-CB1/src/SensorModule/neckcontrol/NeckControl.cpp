#include "SensorModule/NeckControl.h"
#include "Common/Constants.h"
#include <iostream>
#include <vector>

NeckControl::NeckControl(sensorhub::SerialPort& serialPort)
    : serialPort_(serialPort),
      cnt_(0),
      isMoving_(false),
      currentPosition_(50)
{
}

NeckControl::~NeckControl() {}

bool NeckControl::controlNeck(int targetPosition)
{
  if (targetPosition < 0 || targetPosition > 2000) {
    std::cerr << "Invalid neck position (must be 0-100)." << std::endl;
    return false;
  }

  // std::lock_guard<std::mutex> lock(movementMutex_);
  // if (isMoving_) {
  //   std::cerr << "Neck is already moving." << std::endl;
  //   return false;
  // }
  // isMoving_ = true;
  // std::async(std::launch::async, &NeckControl::moveToPosition, this, targetPosition,
  //            speedDegreesPerSecond);
  return sendNeckCommand(targetPosition);
  // return true;
}

void NeckControl::moveToPosition(int targetPosition, int speedDegreesPerSecond)
{
  int currentPosition = getCurrentPosition();
  if (currentPosition == targetPosition) {
    isMoving_ = false;
    return;
  }

  int step    = (targetPosition > currentPosition) ? 1 : -1;
  int delayMs = 1000 / speedDegreesPerSecond;

  while (currentPosition != targetPosition && isMoving_) {
    currentPosition += step;
    if (!sendNeckCommand(currentPosition)) {
      std::cerr << "Failed to send neck control command." << std::endl;
      break;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(delayMs));
  }

  isMoving_ = false;
}

bool NeckControl::sendNeckCommand(int position)
{
  std::vector<unsigned char> sendBytes(10, 0x00);
  sendBytes[0] = 0x44;
  sendBytes[1] = 0x52;

  sendBytes[FRAME_TYPE]      = 0x15;
  sendBytes[FRAME_FUNC_CODE] = 0x32;

  sendBytes[FRAME_ID_BIAS] = cnt_;
  cnt_                     = (cnt_ + 1) % 256;

  std::vector<unsigned char> extendSendBytes(2, 0x00);

  extendSendBytes[0] = (position >> 8) & 0xFF;
  extendSendBytes[1] = position & 0xFF;

  uint16_t dataSize                   = extendSendBytes.size();
  sendBytes[FRAME_DATA_SIZE_BIAS]     = (dataSize >> 8) & 0xFF;
  sendBytes[FRAME_DATA_SIZE_BIAS + 1] = dataSize & 0xFF;

  sendBytes.insert(sendBytes.end(), extendSendBytes.begin(), extendSendBytes.end());

  if (!serialPort_.writeData(sendBytes.data(), sendBytes.size())) {
    std::cerr << "Failed to send neck control command." << std::endl;
    return false;
  }
  setCurrentPosition(position);
  return true;
}

int NeckControl::getCurrentPosition() { return currentPosition_; }

void NeckControl::setCurrentPosition(int position) { currentPosition_ = position; }