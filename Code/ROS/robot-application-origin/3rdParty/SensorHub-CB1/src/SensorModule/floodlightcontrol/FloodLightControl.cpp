#include "SensorModule/FloodLightControl.h"
#include "Common/Constants.h"
#include <iostream>

FloodLightControl::FloodLightControl(sensorhub::SerialPort& serialPort)
    : serialPort_(serialPort),
      cnt_(0)
{
}

FloodLightControl::~FloodLightControl() {}

bool FloodLightControl::controlFloodLight(int brightness)
{
  if (brightness < 0 || brightness > 100) {
    std::cerr << "Invalid brightness value (must be 0-100)." << std::endl;
    return false;
  }

  return sendFloodLightCommand(brightness);
}

bool FloodLightControl::sendFloodLightCommand(int brightness)
{
  std::vector<unsigned char> sendBytes(10, 0x00);
  sendBytes[0] = 0x44;
  sendBytes[1] = 0x52;

  sendBytes[FRAME_TYPE]      = 0x15;
  sendBytes[FRAME_FUNC_CODE] = 0x36;

  sendBytes[FRAME_ID_BIAS] = cnt_;
  cnt_                     = (cnt_ + 1) % 256;

  std::vector<unsigned char> extendSendBytes(2, 0x00);

  extendSendBytes[0] = (brightness >> 8) & 0xFF;
  extendSendBytes[1] = brightness & 0xFF;

  uint16_t dataSize                   = extendSendBytes.size();
  sendBytes[FRAME_DATA_SIZE_BIAS]     = (dataSize >> 8) & 0xFF;
  sendBytes[FRAME_DATA_SIZE_BIAS + 1] = dataSize & 0xFF;

  sendBytes.insert(sendBytes.end(), extendSendBytes.begin(), extendSendBytes.end());

  if (!serialPort_.writeData(sendBytes.data(), sendBytes.size())) {
    std::cerr << "Failed to send flood light control command." << std::endl;
    return false;
  }

  return true;
}
