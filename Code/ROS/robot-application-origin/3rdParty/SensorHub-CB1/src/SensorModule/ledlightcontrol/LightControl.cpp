#include "SensorModule/LightControl.h"
#include "Common/Constants.h"
#include <iostream>

LightControl::LightControl(sensorhub::SerialPort& serialPort) : serialPort_(serialPort), cnt_(0) {}

LightControl::~LightControl() {}

bool LightControl::controlLight(int lightNumber, int red, int green, int blue)
{
  if (lightNumber < 1 || lightNumber > 32 || red < 0 || red > 255 || green < 0 || green > 255 ||
      blue < 0 || blue > 255) {
    std::cerr << "Invalid parameters for light control." << std::endl;
    return false;
  }

  return sendLightCommand(lightNumber, red, green, blue);
}

bool LightControl::controlLights(const LedStruct& ledStruct)
{
  if (ledStruct.color[0] > 255 || ledStruct.color[1] > 255 || ledStruct.color[2] > 255) {
    std::cerr << "Invalid color values in LedStruct." << std::endl;
    return false;
  }

  if (ledStruct.status == 0) {
    std::cerr << "No lights selected in LedStruct.status." << std::endl;
    return false;
  }

  return sendMultipleLightsCommand(ledStruct);
}

bool LightControl::sendLightCommand(int lightNumber, int red, int green, int blue)
{
  std::vector<unsigned char> sendBytes(10, 0x00);
  sendBytes[0] = 0x44;
  sendBytes[1] = 0x52;

  sendBytes[FRAME_TYPE]      = 0x15;
  sendBytes[FRAME_FUNC_CODE] = 0x31;

  sendBytes[FRAME_ID_BIAS] = cnt_;
  cnt_                     = (cnt_ + 1) % 256;

  std::vector<unsigned char> extendSendBytes(8, 0x00);

  uint32_t lightMask = 1 << (lightNumber - 1);
  extendSendBytes[4] = (lightMask >> 24) & 0xFF;
  extendSendBytes[5] = (lightMask >> 16) & 0xFF;
  extendSendBytes[6] = (lightMask >> 8) & 0xFF;
  extendSendBytes[7] = lightMask & 0xFF;

  extendSendBytes[0] = red;
  extendSendBytes[1] = green;
  extendSendBytes[2] = blue;

  uint16_t dataSize                   = extendSendBytes.size();
  sendBytes[FRAME_DATA_SIZE_BIAS]     = (dataSize >> 8) & 0xFF;
  sendBytes[FRAME_DATA_SIZE_BIAS + 1] = dataSize & 0xFF;

  sendBytes.insert(sendBytes.end(), extendSendBytes.begin(), extendSendBytes.end());

  if (!serialPort_.writeData(sendBytes.data(), sendBytes.size())) {
    std::cerr << "Failed to send light control command." << std::endl;
    return false;
  }

  return true;
}

bool LightControl::sendMultipleLightsCommand(const LedStruct& ledStruct)
{
  std::vector<unsigned char> sendBytes(10, 0x00);
  sendBytes[0] = 0x44;
  sendBytes[1] = 0x52;

  sendBytes[FRAME_TYPE]      = 0x15;
  sendBytes[FRAME_FUNC_CODE] = 0x31;

  sendBytes[FRAME_ID_BIAS] = cnt_;
  cnt_                     = (cnt_ + 1) % 256;

  std::vector<unsigned char> extendSendBytes(8, 0x00);

  uint32_t lightMask = ledStruct.status;
  extendSendBytes[4] = (lightMask >> 24) & 0xFF;
  extendSendBytes[5] = (lightMask >> 16) & 0xFF;
  extendSendBytes[6] = (lightMask >> 8) & 0xFF;
  extendSendBytes[7] = lightMask & 0xFF;

  extendSendBytes[0] = ledStruct.color[0]; // red
  extendSendBytes[1] = ledStruct.color[1]; // green
  extendSendBytes[2] = ledStruct.color[2]; // blue

  uint16_t dataSize                   = extendSendBytes.size();
  sendBytes[FRAME_DATA_SIZE_BIAS]     = (dataSize >> 8) & 0xFF;
  sendBytes[FRAME_DATA_SIZE_BIAS + 1] = dataSize & 0xFF;

  sendBytes.insert(sendBytes.end(), extendSendBytes.begin(), extendSendBytes.end());

  if (!serialPort_.writeData(sendBytes.data(), sendBytes.size())) {
    std::cerr << "Failed to send multiple lights control command." << std::endl;
    return false;
  }

  return true;
}
