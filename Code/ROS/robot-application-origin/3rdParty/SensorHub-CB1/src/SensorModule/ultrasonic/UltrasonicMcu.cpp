#include "SensorModule/UltrasonicControlMcu.h"
#include "Common/Constants.h"
#include <unistd.h>

UltrasonicControlMcu::UltrasonicControlMcu(SerialPort& serialPort)
    : serialPort_(serialPort),
      cnt_(0)
{
  run_thread_ = std::thread(&UltrasonicControlMcu::runThread, this);
}

UltrasonicControlMcu::~UltrasonicControlMcu() {}

uint16_t UltrasonicControlMcu::calculate_crc(const unsigned char* data, size_t length)
{
  uint16_t crc = 0;
  for (size_t i = 0; i < length; ++i) {
    crc += data[i];
  }
  return crc;
}

bool UltrasonicControlMcu::sendUltrasonicCommand()
{
  std::vector<unsigned char> sendBytes(10, 0x00);
  sendBytes[0] = 0x44;
  sendBytes[1] = 0x52;

  sendBytes[FRAME_TYPE] = 0x17;
  sendBytes[FRAME_FUNC_CODE] = 0x33;

  sendBytes[FRAME_ID_BIAS] = cnt_;
  cnt_ = (cnt_ + 1) % 256;

  uint16_t dataSize = 0;
  sendBytes[FRAME_DATA_SIZE_BIAS] = (dataSize >> 8) & 0xFF;
  sendBytes[FRAME_DATA_SIZE_BIAS + 1] = dataSize & 0xFF;

  uint16_t crc = calculate_crc(&sendBytes[2], 6 + dataSize);
  sendBytes[FRAME_CRC_BIAS] = (crc >> 8) & 0xFF;
  sendBytes[FRAME_CRC_BIAS + 1] = crc & 0xFF;

  if (!serialPort_.writeData(sendBytes.data(), sendBytes.size())) {
    std::cerr << "Failed to send ultrasonic control command." << std::endl;
    return false;
  }

  std::cout << "Ultrasonic control command sent successfully." << std::endl;
  return true;
}

std::vector<uint8_t> UltrasonicControlMcu::getLatestDistances()
{
  std::lock_guard<std::mutex> lock(data_mutex_);
  return latest_distances_;
}

void UltrasonicControlMcu::runThread()
{
  std::vector<unsigned char> buffer;
  while (!exit_flag_) {
    if (!sendUltrasonicCommand()) {
      std::cerr << "Failed to send ultrasonic command in thread." << std::endl;
      usleep(500 * 1000); // 500 ms
      continue;
    }

    unsigned char byte;
    size_t bytesRead = serialPort_.readData(&byte, 1);
    if (bytesRead == 1) {
      buffer.push_back(byte);

      while (buffer.size() >= 10) {
        if (buffer[0] != 0x44 || buffer[1] != 0x52) {
          buffer.erase(buffer.begin());
          continue;
        }

        uint16_t data_length = (buffer[6] << 8) | buffer[7];
        size_t total_frame_length = 10 + data_length;

        if (buffer.size() < total_frame_length) {
          break;
        }

        std::vector<unsigned char> frame(buffer.begin(), buffer.begin() + total_frame_length);

        std::vector<uint8_t> distances;
        if (parseFrame(frame, distances)) {
          {
            std::lock_guard<std::mutex> lock(data_mutex_);
            latest_distances_ = distances;
          }
          data_cv_.notify_one();
          std::cout << "Received Distances: ";
          for (auto dist : distances) {
            std::cout << static_cast<int>(dist) << " cm ";
          }
          std::cout << std::endl;
        } else {
          std::cerr << "Failed to parse ultrasonic response frame." << std::endl;
        }

        buffer.erase(buffer.begin(), buffer.begin() + total_frame_length);
      }
    } else {
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    usleep(500 * 1000); // 500 ms
  }
}

/**
 * @brief
 *
 * @param [in] frame
 * @param [out] distances
 * @return true
 * @return false
 */
bool UltrasonicControlMcu::parseFrame(const std::vector<unsigned char>& frame,
                                      std::vector<uint8_t>& distances)
{
  if (frame[0] != 0x44 || frame[1] != 0x52) {
    std::cerr << "Invalid frame header during parsing." << std::endl;
    return false;
  }

  uint8_t frame_id = frame[FRAME_ID_BIAS];
  uint8_t protocol_version = frame[FRAME_VERSION_BIAS];
  uint8_t frame_type = frame[FRAME_TYPE];
  uint8_t data_code = frame[FRAME_FUNC_CODE];
  uint16_t data_length = (frame[FRAME_DATA_SIZE_BIAS] << 8) | frame[FRAME_DATA_SIZE_BIAS + 1];
  uint16_t received_crc = (frame[FRAME_CRC_BIAS] << 8) | frame[FRAME_CRC_BIAS + 1];

  uint16_t calculated_crc = calculate_crc(&frame[2], 6 + data_length);

  if (calculated_crc != received_crc) {
    std::cerr << "CRC mismatch during parsing. Calculated: 0x" << std::hex << calculated_crc
              << ", Received: 0x" << received_crc << std::dec << std::endl;
    return false;
  }

  if (frame_type != FRAME_TYPE_RESPONSE) {
    std::cerr << "Unexpected frame type during parsing: 0x" << std::hex
              << static_cast<int>(frame_type) << std::dec << std::endl;
    return false;
  }

  if (data_code != FRAME_FUNC_CODE_ULTRASONIC) {
    std::cerr << "Unexpected data code during parsing: 0x" << std::hex
              << static_cast<int>(data_code) << std::dec << std::endl;
    return false;
  }

  if (data_length != (frame.size() - FRAME_DATA_BIAS - 2)) {
    std::cerr << "Data length mismatch during parsing." << std::endl;
    return false;
  }

  distances.clear();
  for (size_t i = 0; i < data_length; ++i) {
    distances.push_back(frame[FRAME_DATA_BIAS + i]);
  }

  return true;
}