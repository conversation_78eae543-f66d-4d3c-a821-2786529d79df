#include "SensorModule/ultrasonic.hpp"

Ultrasonic::Ultrasonic() : fd_(-1), exit_flag_(false)
{
  sensorInit();
  startThread();
}

Ultrasonic::~Ultrasonic()
{
  stopThread();

  if (run_thread_ && run_thread_->joinable()) {
    run_thread_->join();
  }

  if (fd_ >= 0) {
    close(fd_);
    fd_ = -1;
  }
}

void Ultrasonic::setUdpCommunicator(UdpCommunicator *udpCommunicator)
{
  udpCommunicator_ = udpCommunicator;
}

void Ultrasonic::startThread()
{
  run_thread_ = std::make_shared<std::thread>(&Ultrasonic::runThread, this);
}

void Ultrasonic::stopThread() { exit_flag_ = true; }

void Ultrasonic::sensorInit()
{
  fd_ = open("/dev/ttyS4", O_RDWR);
  if (fd_ < 0) {
    std::cerr << "Open Ultrasonic serial port failed." << std::endl;
    return;
  }
  fcntl(fd_, F_SETFL, 0);
  if (!SetSpeed(fd_, B9600)) {
    perror("Speed set action encountered an error");
    close(fd_);
    fd_ = -1;
    return;
  }
  if (!SetParity(fd_, 8, 1, 'N', 18)) {
    perror("Parity set action encountered an error");
    close(fd_);
    fd_ = -1;
    return;
  }
}

void Ultrasonic::runThread()
{
  uint8_t datas_buffer[7];
  uint8_t led_buf[1] = {0xff};
  while (!exit_flag_) {
    ssize_t bytes_written = write(fd_, led_buf, 1);
    if (bytes_written != 1) {
      std::cerr << "Failed to write to serial port: " << strerror(errno) << std::endl;
      continue;
    }
    ssize_t receive_count = read(fd_, datas_buffer, 4);
    if (receive_count == 4) {
      int distance = datas_buffer[1] * 256 + datas_buffer[2];
      std::cout << " distance :  " << distance << std::endl;
    } else {
      std::cerr << "Failed to read data from sensor." << std::endl;
    }
    usleep(500 * 1000);
  }
}

bool Ultrasonic::SetSpeed(int fd, int speed)
{
  struct termios options;

  if (tcgetattr(fd, &options) != 0) {
    perror("Error getting current serial port options");
    return false;
  }

  cfsetispeed(&options, speed);
  cfsetospeed(&options, speed);

  if (tcsetattr(fd, TCSANOW, &options) != 0) {
    perror("Error setting serial port speed");
    return false;
  }

  return true;
}

bool Ultrasonic::SetParity(int fd, int databits, int stopbits, char parity, int vtime)
{
  struct termios options;

  if (tcgetattr(fd, &options) != 0) {
    perror("Error getting current serial port options");
    return false;
  }

  options.c_cflag &= ~CSIZE;
  switch (databits) {
    case 7: options.c_cflag |= CS7; break;
    case 8: options.c_cflag |= CS8; break;
    default: fprintf(stderr, "Unsupported data size\n"); return false;
  }

  switch (parity) {
    case 'n':
    case 'N':
      options.c_cflag &= ~PARENB; // Clear parity enable
      options.c_iflag &= ~INPCK;  // Disable parity checking
      break;
    case 'o':
    case 'O':
      options.c_cflag |= (PARENB | PARODD); // Enable odd parity
      options.c_iflag |= INPCK;             // Enable parity checking
      break;
    case 'e':
    case 'E':
      options.c_cflag |= PARENB;  // Enable parity
      options.c_cflag &= ~PARODD; // Enable even parity
      options.c_iflag |= INPCK;   // Enable parity checking
      break;
    case 's':
    case 'S':                     // Space parity
      options.c_cflag &= ~PARENB; // Disable parity
      options.c_cflag &= ~CSTOPB; // 1 stop bit
      break;
    default: fprintf(stderr, "Unsupported parity\n"); return false;
  }

  switch (stopbits) {
    case 1: options.c_cflag &= ~CSTOPB; break;
    case 2: options.c_cflag |= CSTOPB; break;
    default: fprintf(stderr, "Unsupported stop bits\n"); return false;
  }

  options.c_cc[VTIME] = vtime;
  options.c_cc[VMIN] = 0;

  options.c_iflag &= ~(IXON | IXOFF | IXANY);
  options.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
  options.c_oflag &= ~OPOST;

  tcflush(fd, TCIFLUSH);
  if (tcsetattr(fd, TCSANOW, &options) != 0) {
    perror("Error setting serial port parity");
    return false;
  }

  return true;
}

void Ultrasonic::sendUltrasonicValue(int32_t value)
{
  // if (udpCommunicator_) {
  //   // char buffer[sizeof(pk_t)];
  //   // memset(buffer, 0, sizeof(buffer));
  //   // memcpy(buffer, &pk, sizeof(pk_t));

  //   bool sendSuccess = udpCommunicator_->sendData("127.0.0.1", 1234, buffer, sizeof(buffer));
  //   if (!sendSuccess) {
  //     std::cerr << "Failed to send sensor data via UDP." << std::endl;
  //   } else {
  //     std::cout << "Sensor data sent successfully." << std::endl;
  //   }
  // } else {
  //   std::cerr << "UdpCommunicator is not initialized." << std::endl;
  // }
}
