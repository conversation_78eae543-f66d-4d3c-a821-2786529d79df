#include "Common/MessageHandler.h"
#include <iostream>
#include <cstring>
#include <arpa/inet.h>

MessageHandler::MessageHandler(McuAgent& mcuAgent, UdpCommunicator& udpCommunicator)
    : mcuAgent_(mcuAgent),
      udpCommunicator_(udpCommunicator)
{
  commandHandlers_[kCodeQueryStatus] = [this](const char* data, size_t size) {
    this->handleQueryCommand(data, size);
  };
  commandHandlers_[kCodeLight] = [this](const char* data, size_t size) {
    this->handleLightCommand(data, size);
  };
  commandHandlers_[kCodeNeckMotor] = [this](const char* data, size_t size) {
    this->handleNeckCommand(data, size);
  };
}

void MessageHandler::handleMessage(const std::string& remoteIp, const int& port, const char* data,
                                   const size_t size)
{
  if (size < sizeof(FrameHead)) {
    std::cerr << "Received data too short." << std::endl;
    return;
  }

  FrameHead head;
  memcpy(&head, data, sizeof(FrameHead));

  if (head.size != size) {
    std::cerr << "Data length mismatch." << std::endl;
    return;
  }

  auto it = commandHandlers_.find(head.code);
  if (it != commandHandlers_.end()) {
    it->second(data + sizeof(FrameHead), size - sizeof(FrameHead));
  } else {
    std::cerr << "Unknown command: " << head.code << std::endl;
  }
  sendResponse(remoteIp, head.code);
}

void MessageHandler::handleLightCommand(const char* data, const size_t size)
{
  if (size < sizeof(LightParam)) {
    std::cerr << "Received data too short for LightParam." << std::endl;
    return;
  }

  LightParam lightParam{};
  memcpy(&lightParam, data, sizeof(LightParam));
  uint32_t method   = lightParam.method;
  uint8_t  category = (method >> 16) & 0xFF;
  uint16_t action   = method & 0xFFFF;
  switch (category) {
    case kLightEye: // 眼灯控制
      handleEyeLight(action, lightParam);
      break;
    case kLightFlash: // 照明灯灯控制
      handleFloodLight(action, lightParam);
      break;
    case kLightTail: // 尾灯控制
      handleTailLight(action, lightParam);
      break;
    default: std::cerr << "Unknown category: " << static_cast<int>(category) << std::endl; break;
  }
}

void MessageHandler::handleNeckCommand(const char* data, const size_t size)
{
  if (size < sizeof(NeckParam)) {
    std::cerr << "Received data too short for neck_t." << std::endl;
    return;
  }

  NeckParam nect_angle;
  memcpy(&nect_angle, data, sizeof(NeckParam));

  mcuAgent_.SetNeckData(nect_angle.angle);
}

void MessageHandler::handleQueryCommand(const char* data, const size_t size) {}

void MessageHandler::sendResponse(const std::string& remoteIp, const uint32_t code)
{
  Frame responseFrame{};

  responseFrame.head.size              = sizeof(Frame);
  responseFrame.head.code              = code;
  std::vector<uint16_t> ultrasonicData = mcuAgent_.GetUltralsonicData();
  responseFrame.rsp_body.ultrasonic.distance_front =
      ultrasonicData.size() > 0 ? ultrasonicData[0] : 0;
  responseFrame.rsp_body.ultrasonic.distance_back =
      ultrasonicData.size() > 1 ? ultrasonicData[1] : 0;
  // responseFrame.rsp_body.neck.angle                = neckControl_.getCurrentPosition();
  responseFrame.rsp_body.touch.touch_jaw    = 0;
  responseFrame.rsp_body.touch.elapse_jaw   = 0;
  responseFrame.rsp_body.touch.touch_left   = 0;
  responseFrame.rsp_body.touch.elapse_left  = 0;
  responseFrame.rsp_body.touch.touch_right  = 0;
  responseFrame.rsp_body.touch.elapse_right = 0;
  responseFrame.rsp_body.eye.method         = 0;
  responseFrame.rsp_body.tail.method        = 0;
  responseFrame.rsp_body.flash.method       = 0;

  size_t            responseSize = sizeof(Frame);
  std::vector<char> buffer(responseSize);
  memcpy(buffer.data(), &responseFrame.head, sizeof(FrameHead));
  memcpy(buffer.data() + sizeof(FrameHead), &responseFrame.rsp_body, sizeof(FrameBodyRespond));

  if (!udpCommunicator_.sendData(remoteIp, 43883, buffer.data(), buffer.size())) {
    std::cerr << "Failed to send response." << std::endl;
  }
}

void MessageHandler::handleEyeLight(uint16_t action, const LightParam& param)
{
  mcuAgent_.PresetEyeLight(param.method);
}

void MessageHandler::handleFloodLight(uint16_t action, const LightParam& param)
{
  mcuAgent_.PresetFlashLight(param.method);
}

void MessageHandler::handleTailLight(uint16_t action, const LightParam& param)
{
  mcuAgent_.PresetTailLight(param.method);
}