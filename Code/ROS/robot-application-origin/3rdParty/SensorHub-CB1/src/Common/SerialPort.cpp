#include "Common/SerialPort.h"
namespace sensorhub {
SerialPort::SerialPort(const std::string& portName, int baudRate)
    : portName_(portName),
      baudRate_(baudRate),
      fd_(-1)
{
}

SerialPort::~SerialPort() { closePort(); }

bool SerialPort::openPort()
{
  fd_ = open(portName_.c_str(), O_RDWR | O_NOCTTY | O_NDELAY);
  if (fd_ == -1) {
    std::cerr << "Failed to open serial port: " << portName_ << std::endl;
    return false;
  }

  tcgetattr(fd_, &oldTio_);

  memset(&newTio_, 0, sizeof(newTio_));
  newTio_.c_cflag     = baudRate_ | CS8 | CLOCAL | CREAD;
  newTio_.c_iflag     = IGNPAR;
  newTio_.c_oflag     = 0;
  newTio_.c_lflag     = 0;
  newTio_.c_cc[VTIME] = 0;
  newTio_.c_cc[VMIN]  = 1;

  tcflush(fd_, TCIFLUSH);
  tcsetattr(fd_, TCSANOW, &newTio_);

  return true;
}

void SerialPort::closePort()
{
  if (fd_ != -1) {
    tcsetattr(fd_, TCSANOW, &oldTio_);
    close(fd_);
    fd_ = -1;
  }
}

bool SerialPort::writeData(const unsigned char* data, size_t size)
{
  if (fd_ == -1) {
    std::cerr << "Serial port not open." << std::endl;
    return false;
  }
  ssize_t bytesWritten = write(fd_, data, size);
  if (bytesWritten < 0) {
    std::cerr << "Failed to write to serial port." << std::endl;
    return false;
  }
  return true;
}

ssize_t SerialPort::readData(unsigned char* buffer, size_t size)
{
  if (fd_ == -1) {
    std::cerr << "Serial port not open." << std::endl;
    return -1;
  }
  return read(fd_, buffer, size);
}

bool SerialPort::isOpen() const { return fd_ != -1; }

} // namespace sensorhub