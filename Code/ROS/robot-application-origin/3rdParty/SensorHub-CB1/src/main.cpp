#include "Common/SerialPort.h"
#include "Common/MessageHandler.h"
#include "Common/UdpCommunicator.h"
#include "SensorModule/LightControl.h"
#include "SensorModule/NeckControl.h"
#include "SensorModule/FloodLightControl.h"
#include "SensorModule/ultrasonic.hpp"
#include "SensorModule/GpioSensor.h"

#include <iostream>

int main()
{
  McuAgent& mcuAgent = McuAgent::Instance();
  // Start listening for client control commands
  UdpCommunicator* sensorHubServer = UdpCommunicator::createSendingAndReceiving("0.0.0.0", 12345);
  if (!sensorHubServer) {
    std::cerr << "Failed to create UdpCommunicator." << std::endl;
    return -1;
  }
  MessageHandler messageHandler(mcuAgent, *sensorHubServer);
  sensorHubServer->setReceiveCallback(
      [&](const std::string& remoteIp, const int& port, const char* data, size_t size) {
        messageHandler.handleMessage(remoteIp, port, data, size);
      });

  if (!sensorHubServer->startListening()) {
    std::cerr << "Failed to start UDP listening." << std::endl;
    return -1;
  }

  // Monitor the touch sensor value changes and send data once there is a change
  // UdpCommunicator* sendClientUdp = UdpCommunicator::createSendingOnly();

  std::cout << "Server is running. Press Enter to exit." << std::endl;
  while (std::cin.get() != '\n') {
    sleep(1);
  };
  sensorHubServer->stopListening();

  return 0;
}