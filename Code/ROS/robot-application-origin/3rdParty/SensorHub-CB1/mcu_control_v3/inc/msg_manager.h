//
// Created by <PERSON><PERSON><PERSON><PERSON> on 3/10/25.
//

#ifndef MSG_MANAGER_H
#define MSG_MANAGER_H

#include <cstdint>
#include <termios.h>
#include <cstddef>
#include <iostream>
#include <fstream>
#include <fcntl.h>
#include <unistd.h>
#include <cstring>
#include <algorithm>
#include <vector>
#include <cstddef>

typedef struct {
    uint8_t header[2];
    uint8_t frame_id;//数据帧id
    uint8_t version;//版本
    uint8_t type;//数据帧类型
    uint8_t hex_code;//数据帧数据码
    uint16_t cmd_size = 0;//数据帧 自定义数据长度
    uint16_t crc16;//校验码

    uint8_t cmd_data[256];
    uint8_t data_length;

    uint16_t bin_length;
    char bin_data[65535];

    uint8_t update_cmd[10]="#update\r\n"; //升级命令，当接收到这条命令时进行升级
    uint8_t reboot_cmd[10] = "#reboot\r\n";//重启命令 接受到此命令时候进行重启进入bootloader
}OTA_Data;

class MsgManager {
public:
    MsgManager(const char* device, speed_t baud_rate);
    ~MsgManager();

    bool initialize();
    void run();
    bool send_message(OTA_Data data);

    bool send_bin(OTA_Data data);
    bool reboot_cmd(OTA_Data data);
    bool update_cmd(OTA_Data data);

    std::streamsize transmit_bin(std::ifstream& file, char* buffer, std::streamsize chunkSize);

private:
    int serial_port;
    const char* device;
    speed_t baud_rate;
    struct termios tty;
    OTA_Data msg_data;

    void process_message(const uint8_t* message, uint8_t length);
    void read_serial_data();
    bool configure_serial_port();

    static constexpr uint8_t FRAME_HEADER1 = 0xEE;
    static constexpr uint8_t FRAME_HEADER2 = 0xEF;
    static constexpr uint8_t FRAME_TAIL1 = 0xEF;
    static constexpr uint8_t FRAME_TAIL2 = 0xFF;
};


#endif //MSG_MANAGER_H
