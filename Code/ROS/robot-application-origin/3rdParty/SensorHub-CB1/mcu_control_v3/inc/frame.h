#include <msg_manager.h>
//
// Created by y<PERSON><PERSON><PERSON> on 3/12/25.
//


#ifndef FRAME_H
#define FRAME_H

#define Frame_length         10

#define Test_ID              0x25
#define Test_version         0x30
#define Test_type            0x35
#define Test_hex             0x40
#define Test_size            0x45
#define Test_crc             0x50

#define Frame_ID             0x01
#define Frame_VERSION        0x01

#define Type_req            0x11
#define Type_cmd            0x12
#define Type_Order          0x15

#define Hex_MCU             0x30
#define Hex_Light           0x31
#define Hex_Duoji           0x32

//单个LED的颜色控制结构体
typedef struct
{
    uint8_t R;
    uint8_t G;
    uint8_t B;
    uint8_t Brightness;
    uint32_t status_id;
}RGB_Color_TypeDef;

typedef struct LiteLedStruct {
    uint8_t id = 0x66;
    uint8_t type;
    uint16_t freq;
    RGB_Color_TypeDef color_data;
}LiteLedStruct;


void SOC_Order_cmd(OTA_Data *data, uint8_t hex_code, uint16_t crc,
                        uint8_t type, uint16_t freq,
                        uint8_t WSB_R, uint8_t WSB_G, uint8_t WSB_B, uint8_t Brightness,
                        uint32_t status_id);


void Light_control(OTA_Data *light, //uint8_t *light_data,
                    uint8_t type, uint16_t freq,
                    uint8_t WSB_R, uint8_t WSB_G, uint8_t WSB_B, uint8_t Brightness,
                    uint32_t status_id) ;
void DuoJi_control(OTA_Data *duoji, uint32_t CCR_data);





#endif //FRAME_H
