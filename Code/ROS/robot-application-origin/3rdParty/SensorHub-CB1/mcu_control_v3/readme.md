项目：mcu_control_v3
文档版本：V1.0

开发环境说明
RK3588端基于Cmake和g++交叉编译环境下进行开发

关于MCU_Control
概述：在原有Sensorhub仓库下mcu_control代码基础上进行开发


mcu_control_v3
│   
│
├──main.cpp                 //主函数
│
├── firmware                //存放固件(bin)文件的位置
│
├── inc                     //存放头文件
│   ├── msg_manager         //封装好设备数据传输类 
│   ├── frame               //存储对应命令函数和命令定义 
│   
│
└── src
    ├── msg_manager         //封装信息发送和底层tty设备使能函数
    └── frame               //命令帧打包和调度功能时序


使用说明：
mcu_control中最重要的结构体OTA_Data 其中封装好了数据帧数据和固件缓存数据
在主函数中新建设备类后 调用initialize进行初始化 随后即可进入while循环进行命令调用

命令函数由于存在不定长数据 所以在使用时候务必关注OTA_Data结构体中cmd_size变量 
此变量用于存储不定长数据长度 决定着数据打包&解包的识别






