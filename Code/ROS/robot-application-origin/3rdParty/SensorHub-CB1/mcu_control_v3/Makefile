# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mcu_control_v3

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mcu_control_v3

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mcu_control_v3/CMakeFiles /home/<USER>/mcu_control_v3/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mcu_control_v3/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named mcu_control_v3

# Build rule for target.
mcu_control_v3: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 mcu_control_v3
.PHONY : mcu_control_v3

# fast build rule for target.
mcu_control_v3/fast:
	$(MAKE) -f CMakeFiles/mcu_control_v3.dir/build.make CMakeFiles/mcu_control_v3.dir/build
.PHONY : mcu_control_v3/fast

main.o: main.cpp.o

.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) -f CMakeFiles/mcu_control_v3.dir/build.make CMakeFiles/mcu_control_v3.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i

.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) -f CMakeFiles/mcu_control_v3.dir/build.make CMakeFiles/mcu_control_v3.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s

.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) -f CMakeFiles/mcu_control_v3.dir/build.make CMakeFiles/mcu_control_v3.dir/main.cpp.s
.PHONY : main.cpp.s

src/frame.o: src/frame.cpp.o

.PHONY : src/frame.o

# target to build an object file
src/frame.cpp.o:
	$(MAKE) -f CMakeFiles/mcu_control_v3.dir/build.make CMakeFiles/mcu_control_v3.dir/src/frame.cpp.o
.PHONY : src/frame.cpp.o

src/frame.i: src/frame.cpp.i

.PHONY : src/frame.i

# target to preprocess a source file
src/frame.cpp.i:
	$(MAKE) -f CMakeFiles/mcu_control_v3.dir/build.make CMakeFiles/mcu_control_v3.dir/src/frame.cpp.i
.PHONY : src/frame.cpp.i

src/frame.s: src/frame.cpp.s

.PHONY : src/frame.s

# target to generate assembly for a file
src/frame.cpp.s:
	$(MAKE) -f CMakeFiles/mcu_control_v3.dir/build.make CMakeFiles/mcu_control_v3.dir/src/frame.cpp.s
.PHONY : src/frame.cpp.s

src/msg_manager.o: src/msg_manager.cpp.o

.PHONY : src/msg_manager.o

# target to build an object file
src/msg_manager.cpp.o:
	$(MAKE) -f CMakeFiles/mcu_control_v3.dir/build.make CMakeFiles/mcu_control_v3.dir/src/msg_manager.cpp.o
.PHONY : src/msg_manager.cpp.o

src/msg_manager.i: src/msg_manager.cpp.i

.PHONY : src/msg_manager.i

# target to preprocess a source file
src/msg_manager.cpp.i:
	$(MAKE) -f CMakeFiles/mcu_control_v3.dir/build.make CMakeFiles/mcu_control_v3.dir/src/msg_manager.cpp.i
.PHONY : src/msg_manager.cpp.i

src/msg_manager.s: src/msg_manager.cpp.s

.PHONY : src/msg_manager.s

# target to generate assembly for a file
src/msg_manager.cpp.s:
	$(MAKE) -f CMakeFiles/mcu_control_v3.dir/build.make CMakeFiles/mcu_control_v3.dir/src/msg_manager.cpp.s
.PHONY : src/msg_manager.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... mcu_control_v3"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... src/frame.o"
	@echo "... src/frame.i"
	@echo "... src/frame.s"
	@echo "... src/msg_manager.o"
	@echo "... src/msg_manager.i"
	@echo "... src/msg_manager.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

