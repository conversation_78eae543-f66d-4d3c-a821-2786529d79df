#include <iostream>

#include <frame.h>

#include <cstring>
#include <unistd.h>  // 包含sleep() 和 usleep()
#include <cstdint>
#include <sstream>
#include <termios.h>
#include <fstream>
#include <fcntl.h>
#include <algorithm>
#include <vector>



int main() {
    OTA_Data msg_data;
    MsgManager test_uart("/dev/ttyS3", B115200);
    test_uart.initialize();
    while(1) {
        std::cout<<"========================chose function:======================================"<<std::endl;
        std::cout<<"1.MCU Reset 2.MCU OTA                                5.lamp control          "<<std::endl;
        std::cout<<"6.Light control 7.light breath 8.light blink  9.CB1 control light            "<<std::endl;
        std::cout<<"                                                                             "<<std::endl;
        std::cout<<"============================================================================="<<std::endl;
    int choice;
    std::cin>>choice;
    if (std::cin.fail()) { // 检查输入是否为整数
        std::cin.clear(); // 清除错误标志
        std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n'); // 忽略行内剩余字符
        std::cout << "Invalid input, please enter an integer." << std::endl;
        continue; // 继续下一次循环
    }

    if(choice == 1){
        std::cout<<"1. Reset to default state"<<std::endl;
        test_uart.reboot_cmd(msg_data);
        sleep(2);
        test_uart.update_cmd(msg_data);//pretend data losing
        sleep(2);
    }
    else if(choice == 2) {
    std::ifstream file("/home/<USER>/mcu_control_v3/firmware/APP.bin", std::ios::binary);
    if (!file) {
        std::cerr << "文件打开失败" << std::endl;
        return 1;
    }
    std::streamsize bytesRead = test_uart.transmit_bin(file, msg_data.bin_data, 65536);
    std::cout << "实际读取了 " << bytesRead << " 字节" << std::endl;


    msg_data.bin_length = bytesRead;

    test_uart.send_bin(msg_data);

    file.close();
    }
    else if(choice == 5) {
        std::cout<<"请输入lamp控制数值(0-100)"<<std::endl;
        uint32_t data;
        std::cin>>data;
        // if(data > 106 && data < 246){
            SOC_Order_cmd(&msg_data, Hex_Duoji, Test_crc,
                     0,  0,
                     0, 0, 0, 0,
                     data);
            test_uart.send_message(msg_data);
        // }
        // else{
        //     std::cout << "输入超出范围" << data << std::endl;
        // }
    }
    else if(choice == 6) {
        std::cout<<"设置颜色为粉色常量"<<std::endl;
        SOC_Order_cmd(&msg_data, Hex_Light, Test_crc,
                            4,  500,
                            255, 0, 255, 100,
                            63);
        test_uart.send_message(msg_data);
    }
    else if(choice == 7){
        std::cout<<"蓝色呼吸"<<std::endl;
        SOC_Order_cmd(&msg_data, Hex_Light, Test_crc,
                            2,  500,
                            0, 0, 255, 100,
                            63);
        test_uart.send_message(msg_data);
    }
    else if(choice == 8){
        std::cout<<"红色闪烁"<<std::endl;
        SOC_Order_cmd(&msg_data, Hex_Light, Test_crc,
                            3,  500,
                            255, 0, 0, 100,
                            63);
        test_uart.send_message(msg_data);
    }
    else if(choice == 9){
        std::cout<<"流水灯"<<std::endl;
        SOC_Order_cmd(&msg_data, Hex_Light, Test_crc,
                            1,  500,
                            255, 0, 0, 100,
                            63);
        test_uart.send_message(msg_data);
    }
    else if(choice == 10){
        std::cout<<"闪烁速度"<<std::endl;
        std::cout<<"请输入闪烁数值(0-1000)"<<std::endl;
        uint16_t freq;
        std::cin>>freq;
        SOC_Order_cmd(&msg_data, Hex_Light, Test_crc,
                            3,  freq,
                            255, 0, 255, 100,
                            63);
        test_uart.send_message(msg_data);
    }
    }

    return 0;
}
// TIP See CLion help at <a
// href="https://www.jetbrains.com/help/clion/">jetbrains.com/help/clion/</a>.
//  Also, you can try interactive lessons for CLion by selecting
//  'Help | Learn IDE Features' from the main menu.
