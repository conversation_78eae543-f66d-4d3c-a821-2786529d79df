#include "frame.h"

#include <bits/fs_fwd.h>
//
// Created by y<PERSON><PERSON><PERSON> on 3/13/25.
//
uint8_t light_Data[12];
LiteLedStruct Lite_RGB_data;//all light



void SOC_Order_cmd(OTA_Data *data, uint8_t hex_code, uint16_t crc,
                        uint8_t type, uint16_t freq,
                        uint8_t WSB_R, uint8_t WSB_G, uint8_t WSB_B, uint8_t Brightness,
                        uint32_t status_id) {
    data->frame_id = Frame_ID;
    data->version = Frame_VERSION;
    data->type = Type_Order;

    data->hex_code = hex_code;
    data->crc16 = crc;

    switch (hex_code) {
        case 0x31: Light_control(data, type, freq, WSB_R, WSB_G,
                                WSB_B, Brightness, status_id);break;
        case 0x32: DuoJi_control(data, status_id);break;
        default:break;
    }
    data->data_length = Frame_length + data->cmd_size;
}




void Light_control(OTA_Data *light, //uint8_t *light_data,
                    uint8_t type, uint16_t freq,
                    uint8_t WSB_R, uint8_t WSB_G, uint8_t WSB_B, uint8_t Brightness,
                    uint32_t status_id) {

    Lite_RGB_data.type = type;
    Lite_RGB_data.freq = freq;

    Lite_RGB_data.color_data.R = WSB_R;
    Lite_RGB_data.color_data.G = WSB_G;
    Lite_RGB_data.color_data.B = WSB_B;
    Lite_RGB_data.color_data.Brightness = Brightness;
    Lite_RGB_data.color_data.status_id = status_id;

    light->cmd_data[0] = Lite_RGB_data.id;
    light->cmd_data[1] = Lite_RGB_data.type;
    light->cmd_data[2] = Lite_RGB_data.freq >> 8;
    light->cmd_data[3] = Lite_RGB_data.freq & 0xFF;
    light->cmd_data[4] = Lite_RGB_data.color_data.R;
    light->cmd_data[5] = Lite_RGB_data.color_data.G;
    light->cmd_data[6] = Lite_RGB_data.color_data.B;
    light->cmd_data[7] = Lite_RGB_data.color_data.Brightness;
    light->cmd_data[8] = Lite_RGB_data.color_data.status_id >> 24;
    light->cmd_data[9] = Lite_RGB_data.color_data.status_id >> 16;
    light->cmd_data[10] = Lite_RGB_data.color_data.status_id >> 8;
    light->cmd_data[11] = Lite_RGB_data.color_data.status_id & 0xFF;


    light->cmd_size = 12;
}

void DuoJi_control(OTA_Data *duoji, uint32_t CCR_data) {

    duoji->cmd_data[0] = CCR_data >> 24;
    duoji->cmd_data[1] = CCR_data >> 16;
    duoji->cmd_data[2] = CCR_data >> 8;
    duoji->cmd_data[3] = CCR_data & 0xFF;


    duoji->cmd_size = 4;
}