//
// Created by y<PERSON><PERSON><PERSON> on 3/10/25.
//
#include "msg_manager.h"


using namespace std;

// uint8_t temp_buffer[256];//temp data
char temp_buffer[256];//temp data

MsgManager::MsgManager(const char* device, speed_t baud_rate)
    : serial_port(-1), device(device), baud_rate(baud_rate) {
    std::cout << "tty open "<< device <<std::endl;
}

MsgManager::~MsgManager(void) {
    if (serial_port >= 0) {
        close(serial_port);
        std::cout << "tty close" <<std::endl;
    }
}


bool MsgManager::initialize() {
    serial_port = open(device, O_RDWR);
    if (serial_port < 0) {
        std::cerr << "Error " << errno << " opening " << device << ": " << strerror(errno) << std::endl;
        return false;
    }
    configure_serial_port();
    return serial_port;
}

void MsgManager::run() {
    read_serial_data();
}

bool <PERSON>g<PERSON>anager::send_message(OTA_Data data) {
    uint8_t buffer[256];

    size_t message_length = data.data_length;

    buffer[0] = FRAME_HEADER1;
    buffer[1] = FRAME_HEADER2;

    buffer[2] = data.frame_id;
    buffer[3] = data.version;
    buffer[4] = data.type;
    buffer[5] = data.hex_code;
    buffer[6] = data.cmd_size >> 8;
    buffer[7] = data.cmd_size & 0xFF;
    buffer[8] = data.crc16 >> 8;
    buffer[9] = data.crc16 & 0xFF;

    for (int i = 0; i < data.cmd_size; i++) {
        buffer[10 + i] = data.cmd_data[i];
    }

    // buffer[10 + data.cmd_size] = 123;
    // buffer[11 + data.cmd_size] = FRAME_TAIL2;


    if (write(serial_port, buffer, message_length) != message_length) {
        std::cerr << "Failed to write message to serial port" << std::endl;
        return false;
    }

    return true;
}

bool MsgManager::send_bin(OTA_Data data) {
    char buffer[65536];

    size_t message_length = data.bin_length;
    int i = 0 ;
    for (i ; i < message_length ; i++) {

        buffer[i] = data.bin_data[i];

    }

    if (write(serial_port, buffer, message_length) != message_length) {
        std::cerr << "Failed to write message to serial port" << std::endl;
        return false;
    }
    std::cout << "Succeed to write message to serial port" << std::endl;
    return true;
}

bool MsgManager::reboot_cmd(OTA_Data data) {

    write(serial_port, data.reboot_cmd, 10);
    std::cout << "reboot_cmd" <<std::endl;
    return true;
}
bool MsgManager::update_cmd(OTA_Data data) {

    write(serial_port, data.update_cmd, 9);
    std::cout << "update_cmd" <<std::endl;
    return true;

}

// 自定义函数：从文件读取二进制数据到buffer，返回实际读取的字节数
streamsize MsgManager::transmit_bin(ifstream& file, char* buffer, streamsize chunkSize) {
    file.read(buffer, chunkSize);     // 非格式化输入
    return file.gcount();             // 直接返回实际读取的字节数
}



void MsgManager::read_serial_data() {

    int length_02 = read(serial_port, &temp_buffer, sizeof(temp_buffer));

    uint8_t print_buffer[length_02];
    for (int j = 0; j < length_02; j++) {
        print_buffer[j] = temp_buffer[j];
        if (print_buffer[j] == '\0') {
            j = length_02;
        };
    }
    std::cout<<print_buffer<<std::endl;

}

bool MsgManager::configure_serial_port() {
    memset(&tty, 0, sizeof tty);
    if (tcgetattr(serial_port, &tty) != 0) {
        std::cerr << "Error " << errno << " from tcgetattr: " << strerror(errno) << std::endl;
        return false;
    }

    cfsetispeed(&tty, baud_rate);
    cfsetospeed(&tty, baud_rate);

    tty.c_cflag &= ~PARENB;
    tty.c_cflag &= ~CSTOPB;
    tty.c_cflag &= ~CSIZE;
    tty.c_cflag |= CS8;
    tty.c_cflag &= ~CRTSCTS;
    tty.c_cflag |= CREAD | CLOCAL;

    tty.c_lflag &= ~ICANON;
    tty.c_lflag &= ~ECHO;
    tty.c_lflag &= ~ECHOE;
    tty.c_lflag &= ~ECHONL;
    tty.c_lflag &= ~ISIG;

    tty.c_iflag &= ~(IXON | IXOFF | IXANY);
    tty.c_iflag &= ~(ICRNL | INLCR);
    tty.c_iflag &= ~(IGNCR);

    tty.c_oflag &= ~OPOST;

    tty.c_cc[VMIN] = 1;
    tty.c_cc[VTIME] = 0;

    if (tcsetattr(serial_port, TCSANOW, &tty) != 0) {
        std::cerr << "Error " << errno << " from tcsetattr: " << strerror(errno) << std::endl;
        return false;
    }
    return true;
}

