#!/bin/bash

current_path=$(dirname "$(readlink -f "$0")")
branch=$(git symbolic-ref --short HEAD)
if [[ "$branch" == *\/* ]]; then
  branch=${branch##*/}
fi
commit_id=$(git rev-parse --short=7 HEAD)
echo "Current branch:$branch, commit_id:$commit_id"

# 读取数字或创建文件
if [ -f "build_id" ]; then
  number=$(cat build_id)
else
  number=0
  touch build_id
fi

# 对数字加1并写回文件
number=$((number + 1))
printf "%06d" $number >build_id

Ip="***********"
# yscname="user"
# Passwd="123456"
yscname="ysc"
Passwd="'"

ping -c 2 -w 2 $Ip
if [[ $? != 0 ]]; then
  echo "can't connect to $Ip"
  exit
fi

echo "Start to sync file ...."
sshpass -p "$Passwd" ssh $yscname@*********** "mkdir -p /home/<USER>/jy_mcu/"
sshpass -p "$Passwd" rsync -avz --progress "$current_path"/build/SensorHubNode $yscname@***********:/home/<USER>/jy_mcu/
