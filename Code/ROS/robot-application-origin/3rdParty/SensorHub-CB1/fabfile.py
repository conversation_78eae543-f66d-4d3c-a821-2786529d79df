from fabric import task, Connection
import os
import subprocess

ip = "***********"  # 替换为实际的IP地址
username = "ysc"  # 替换为实际的用户名
password = "'"  # 替换为实际的密码

# 定义任务
@task
def get_git_info():
    """ 获取 Git 分支名称和简短的 commit ID """
    branch = subprocess.check_output(['git', 'symbolic-ref', '--short', 'HEAD']).decode().strip()
    if '/' in branch:
        branch = branch.split('/')[-1]
    
    commit_id = subprocess.check_output(['git', 'rev-parse', '--short=7', 'HEAD']).decode().strip()
    return branch, commit_id

@task
def update_build_id(c, current_path):
    """ 更新 build_id 文件中的数字并返回新值 """
    build_id_file = os.path.join(current_path, "build_id")
    if os.path.isfile(build_id_file):
        with open(build_id_file, 'r') as file:
            number = int(file.read())
    else:
        number = 0
        with open(build_id_file, 'w') as file:
            file.write('0')
    
    number += 1
    with open(build_id_file, 'w') as file:
        file.write(f"{number:06d}")
    return number

@task
def test_ssh_connection(c, ip):
    """ 测试 SSH 连接 """
    result = c.run(f'ping -c 2 -w 2 {ip}', warn=True)
    if result.failed:
        print(f"can't connect to {ip}")
        return False
    return True

@task
def upload_file(c, current_path):
    """ 使用SCP上传文件到远程服务器 """
    remote_path = "/home/<USER>/"
    local_file = os.path.join(current_path, "build", "SensorHubNode")
    c.put(local_file, remote=remote_path)

@task
def deploy(c):
    """ 部署任务 """
    # 获取当前脚本所在的目录
    current_path = os.path.dirname(os.path.realpath(__file__))
    
    # 获取 Git 分支名称和提交ID
    branch, commit_id = get_git_info(c)
    print(f"Current branch: {branch}, commit_id: {commit_id}")
    
    # 更新构建ID
    new_build_id = update_build_id(c, current_path)
    print(f"Updated build_id: {new_build_id}")
    
    # 创建连接
    c = Connection(host="***********", user="ysc", connect_kwargs={"password": password})
    
    # 测试SSH连接
    if not test_ssh_connection(c, "***********"):
        return
    
    # 上传文件
    upload_file(c, current_path)

# 默认执行的任务
if __name__ == "__main__":
    from invoke import Program
    program = Program(namespace=__name__)
    program.run()