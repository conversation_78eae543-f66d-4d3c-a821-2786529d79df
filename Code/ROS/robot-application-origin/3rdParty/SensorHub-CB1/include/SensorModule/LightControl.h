#ifndef LIGHTCONTROL_H
#define LIGHTCONTROL_H

#include "Common/SerialPort.h"
#include <vector>
struct LedStruct
{
  uint8_t  color[4]; // color[0]: red, color[1]: green, color[2]: blue, color[3]: reserved
  uint32_t status;   // Each bit represents the state of a light (bit 0 for light 1, bit 1 for light
                     // 2, etc.)
};
class LightControl
{
  public:
  LightControl(sensorhub::SerialPort& serialPort);
  ~LightControl();

  bool controlLight(int lightNumber, int red, int green, int blue);
  bool controlLights(const LedStruct& ledStruct);

  private:
  sensorhub::SerialPort& serialPort_;
  unsigned char          cnt_;

  bool sendLightCommand(int lightNumber, int red, int green, int blue);
  bool sendMultipleLightsCommand(const LedStruct& ledStruct);
};

#endif // LIGHTCONTROL_H
