#ifndef ULTRASOUND_HPP
#define ULTRASOUND_HPP

#include <iostream>
#include <thread>
#include <memory>
#include <unistd.h>
#include <fcntl.h>
#include <mutex>
#include <condition_variable>
#include <termios.h>
#include <errno.h>
#include <cstring>
#include <fstream>
#include <iostream>
#include <unistd.h>
#include <fcntl.h>
#include <sys/epoll.h>
#include <poll.h>
#include <chrono>
#include <arpa/inet.h>
#include "Common/UdpCommunicator.h"
// #include "Common/deep_cmd.h"

class Ultrasonic
{
  private:
  int                          fd_;
  bool                         exit_flag_;
  std::condition_variable      cv_;
  std::shared_ptr<std::thread> run_thread_;
  UdpCommunicator*             udpCommunicator_;

  void runThread();
  void sensorInit();
  void startThread();
  void stopThread();
  bool SetSpeed(int fd, int speed);
  bool SetParity(int fd, int databits, int stopbits, char parity, int vtime);
  void sendUltrasonicValue(int32_t value);

  public:
  Ultrasonic();
  ~Ultrasonic();
  void setUdpCommunicator(UdpCommunicator* udpCommunicator);
};

#endif