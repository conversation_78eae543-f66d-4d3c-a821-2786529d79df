#ifndef FLOODLIGHTCONTROL_H
#define FLOODLIGHTCONTROL_H

#include "Common/SerialPort.h"
#include <vector>

/**
 * @description: 照明灯控制
 */
class FloodLightControl
{
  public:
  FloodLightControl(sensorhub::SerialPort& serialPort);
  ~FloodLightControl();

  // 控制泛光灯亮度 (0-100%)
  bool controlFloodLight(int brightness);

  private:
  sensorhub::SerialPort& serialPort_;
  unsigned char          cnt_;

  bool sendFloodLightCommand(int brightness);
};

#endif // FLOODLIGHTCONTROL_H
