/*
 * @Date: 2024-10-02 11:22:24
 * @LastEditors: m-sharp <EMAIL>
 * @LastEditTime: 2024-10-08 20:33:29
 * @FilePath: /CB1/SensorHub/include/Common/MessageHandler.h
 * @Description:
 *
 * Copyright (c) 2024 by DeepRobotics.cn, All Rights Reserved.
 */
#ifndef MESSAGEHANDLER_H
#define MESSAGEHANDLER_H

#include <cstddef>
#include <functional>
#include <unordered_map>

#include "Common/UdpCommunicator.h"
#include "SensorModule/LightControl.h"
#include "SensorModule/NeckControl.h"
#include "../mcu_control/inc/mcu_agent.hpp"

/**
 * @description: UDP报文处理器
 */
class MessageHandler
{
  public:
  /**
   * @description: UDP报文处理器构造函数
   */
      MessageHandler(McuAgent& mcuAgent, UdpCommunicator& udpCommunicator);

      /**
       * @brief 处理报文
       * @param {string&} remoteIp 远端发送IP
       * @param {int&} port  远端发送端口
       * @param {char*} data 远端发送数据
       * @param {size_t} size 远端发送数据的长度.
       * @return {*} 返回空
       */
      void handleMessage(const std::string& remoteIp, const int& port, const char* data,
                         const size_t size);

  private:
  // LightControl&    lightControl_;    ///< 灯控制器
  // NeckControl&     neckControl_;     ///< 舵机控制器
      McuAgent&        mcuAgent_;
      UdpCommunicator& udpCommunicator_; ///< UDP通讯器

      /**
       * @brief 指令与处理函数的键值对
       */
      std::unordered_map<int, std::function<void(const char*, const size_t)>> commandHandlers_;

      /**
       * @description: 灯控制处理函数
       * @param {char*} data
       * @param {size_t} size
       * @return {*}
       */
      void handleLightCommand(const char* data, const size_t size);
      /**
       * @description: 脖子舵机控制函数
       * @param {char*} data
       * @param {size_t} size
       * @return {*}
       */
      void handleNeckCommand(const char* data, const size_t size);

      void handleQueryCommand(const char* data, const size_t size);

      void sendResponse(const std::string& remoteIp, const uint32_t code);

      void handleEyeLight(uint16_t action, const LightParam& param);

      void handleFloodLight(uint16_t action, const LightParam& param);

      void handleTailLight(uint16_t action, const LightParam& param);
};

#endif // MESSAGEHANDLER_H