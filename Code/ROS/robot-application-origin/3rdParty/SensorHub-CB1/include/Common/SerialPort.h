#ifndef SERIALPORT_H
#define SERIALPORT_H

#include <string>
#include <termios.h>
#include <fcntl.h>
#include <unistd.h>
#include <cstring>
#include <iostream>

/**
 * @brief 与微控板的通讯港口
 */
namespace sensorhub {

class SerialPort
{
  public:
  SerialPort(const std::string& portName, int baudRate);
  ~SerialPort();

  bool    openPort();
  void    closePort();
  bool    writeData(const unsigned char* data, size_t size);
  ssize_t readData(unsigned char* buffer, size_t size);
  bool    isOpen() const;

  private:
  std::string    portName_;
  int            baudRate_;
  int            fd_;
  struct termios oldTio_;
  struct termios newTio_;
};
} // namespace sensorhub
#endif // SERIALPORT_H