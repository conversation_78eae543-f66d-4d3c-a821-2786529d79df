#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    homi_speech 测试脚本${NC}"
echo -e "${BLUE}========================================${NC}"

# 检查脚本是否存在
echo -e "${YELLOW}检查启动脚本...${NC}"

if [ -f "run_homi_speech.sh" ]; then
    echo -e "${GREEN}✓ run_homi_speech.sh 存在${NC}"
else
    echo -e "${RED}✗ run_homi_speech.sh 不存在${NC}"
    exit 1
fi

if [ -f "setup_env.sh" ]; then
    echo -e "${GREEN}✓ setup_env.sh 存在${NC}"
else
    echo -e "${RED}✗ setup_env.sh 不存在${NC}"
    exit 1
fi

# 检查launch文件
echo -e "${YELLOW}检查launch文件...${NC}"

if [ -f "src/launch_package/launch/homi_speech_only.launch.py" ]; then
    echo -e "${GREEN}✓ homi_speech_only.launch.py 存在${NC}"
else
    echo -e "${RED}✗ homi_speech_only.launch.py 不存在${NC}"
fi

if [ -f "src/launch_package/launch/homi_speech_minimal.launch.py" ]; then
    echo -e "${GREEN}✓ homi_speech_minimal.launch.py 存在${NC}"
else
    echo -e "${RED}✗ homi_speech_minimal.launch.py 不存在${NC}"
fi

# 检查编译结果
echo -e "${YELLOW}检查编译结果...${NC}"

if [ -d "install/homi_speech" ]; then
    echo -e "${GREEN}✓ homi_speech 包已编译${NC}"
else
    echo -e "${RED}✗ homi_speech 包未编译${NC}"
fi

if [ -d "install/homi_speech_interface" ]; then
    echo -e "${GREEN}✓ homi_speech_interface 包已编译${NC}"
else
    echo -e "${RED}✗ homi_speech_interface 包未编译${NC}"
fi

if [ -d "install/launch_package" ]; then
    echo -e "${GREEN}✓ launch_package 包已编译${NC}"
else
    echo -e "${RED}✗ launch_package 包未编译${NC}"
fi

echo -e ""
echo -e "${BLUE}使用方法:${NC}"
echo -e "${GREEN}1. 启动完整版本:${NC}"
echo -e "   ./run_homi_speech.sh full"
echo -e ""
echo -e "${GREEN}2. 启动最小版本:${NC}"
echo -e "   ./run_homi_speech.sh minimal"
echo -e ""
echo -e "${GREEN}3. 查看帮助:${NC}"
echo -e "   ./run_homi_speech.sh help"
echo -e ""

# 提供快速启动选项
echo -e "${YELLOW}是否现在启动 homi_speech？${NC}"
echo -e "1) 启动完整版本"
echo -e "2) 启动最小版本"
echo -e "3) 不启动，退出"
echo -e ""
read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo -e "${GREEN}启动完整版本...${NC}"
        ./run_homi_speech.sh full
        ;;
    2)
        echo -e "${GREEN}启动最小版本...${NC}"
        ./run_homi_speech.sh minimal
        ;;
    3)
        echo -e "${YELLOW}退出测试脚本${NC}"
        exit 0
        ;;
    *)
        echo -e "${RED}无效选择，退出${NC}"
        exit 1
        ;;
esac

echo -e "${BLUE}========================================${NC}"
