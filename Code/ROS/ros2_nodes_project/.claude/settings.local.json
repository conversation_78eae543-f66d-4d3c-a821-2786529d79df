{"permissions": {"allow": ["<PERSON><PERSON>(source:*)", "Bash(colcon build:*)", "Bash(ros2 pkg list:*)", "Bash(AMENT_PREFIX_PATH=/mine/note/Code/ROS/ros2_nodes_project/install:$AMENT_PREFIX_PATH ros2 pkg list)", "Bash(export:*)", "Bash(ls:*)", "Bash(./build.sh)", "<PERSON><PERSON>(echo:*)", "Bash(/mine/note/Code/ROS/ros2_nodes_project/install/robdog_control/lib/robdog_control/robdog_control_node --help)"], "deny": []}}