#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}设置ROS2环境变量...${NC}"

# 获取当前目录
WORKSPACE_DIR=$(pwd)

# 设置基础ROS2环境变量
export ROS_DOMAIN_ID=0
export RMW_IMPLEMENTATION=rmw_fastrtps_cpp

# 设置ROS2环境
source /opt/ros/humble/setup.bash

# 设置项目环境变量
export AMENT_PREFIX_PATH="$WORKSPACE_DIR/install:$WORKSPACE_DIR/install/homi_speech:$WORKSPACE_DIR/install/launch_package:$WORKSPACE_DIR/install/homi_speech_interface:$WORKSPACE_DIR/install/robdog_control:$AMENT_PREFIX_PATH"
export LD_LIBRARY_PATH="$WORKSPACE_DIR/install/homi_speech_interface/lib:$WORKSPACE_DIR/install/homi_speech/lib:$WORKSPACE_DIR/install/robdog_control/lib:$LD_LIBRARY_PATH"

# 设置Python路径（如果存在）
if [ -d "$WORKSPACE_DIR/install/homi_speech_interface/local/lib/python3.10/site-packages" ]; then
    export PYTHONPATH="$WORKSPACE_DIR/install/homi_speech_interface/local/lib/python3.10/site-packages:$PYTHONPATH"
fi

# 验证包是否可以找到
echo -e "${YELLOW}验证包索引...${NC}"
if [ -f "$WORKSPACE_DIR/install/robdog_control/share/ament_index/resource_index/packages/robdog_control" ]; then
    echo -e "${GREEN}✓ robdog_control 包索引存在${NC}"
else
    echo -e "${RED}✗ robdog_control 包索引不存在${NC}"
fi

if [ -f "$WORKSPACE_DIR/install/homi_speech_interface/share/ament_index/resource_index/packages/homi_speech_interface" ]; then
    echo -e "${GREEN}✓ homi_speech_interface 包索引存在${NC}"
else
    echo -e "${RED}✗ homi_speech_interface 包索引不存在${NC}"
fi

# 显示环境变量
echo -e "${YELLOW}环境变量设置:${NC}"
echo -e "ROS_DOMAIN_ID: $ROS_DOMAIN_ID"
echo -e "RMW_IMPLEMENTATION: $RMW_IMPLEMENTATION"
echo -e "AMENT_PREFIX_PATH: $AMENT_PREFIX_PATH"

echo -e "${GREEN}环境设置完成！${NC}"
echo -e "${YELLOW}现在可以运行: ./install/robdog_control/lib/robdog_control/robdog_control_node${NC}"
