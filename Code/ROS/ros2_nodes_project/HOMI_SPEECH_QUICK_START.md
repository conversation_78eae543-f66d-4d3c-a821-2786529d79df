# 🚀 homi_speech 快速启动指南

## ✅ 脚本状态检查

所有启动脚本都已恢复并可正常使用：

- ✅ `run_homi_speech.sh` - homi_speech主启动脚本
- ✅ `setup_env.sh` - 环境设置脚本  
- ✅ `run_robdog.sh` - robdog控制节点启动脚本
- ✅ `test_homi_speech.sh` - 测试脚本

## 🎯 快速启动命令

### 1. **启动homi_speech完整版本**
```bash
cd /mine/note/Code/ROS/ros2_nodes_project
./run_homi_speech.sh full
```

### 2. **启动homi_speech最小版本**
```bash
cd /mine/note/Code/ROS/ros2_nodes_project
./run_homi_speech.sh minimal
```

### 3. **启动robdog控制节点**
```bash
cd /mine/note/Code/ROS/ros2_nodes_project
./run_robdog.sh
```

### 4. **运行测试脚本**
```bash
cd /mine/note/Code/ROS/ros2_nodes_project
./test_homi_speech.sh
```

## 📋 启动模式说明

### 完整版本 (full)
- **包含**: 语音处理 + 音频录制 + 音频播放
- **Launch文件**: `homi_speech_only.launch.py`
- **适用场景**: 完整的语音交互功能

### 最小版本 (minimal)  
- **包含**: 仅语音处理模块
- **Launch文件**: `homi_speech_minimal.launch.py`
- **适用场景**: 测试和调试

## 🔧 环境配置

脚本会自动设置以下环境变量：
- `ROS_DOMAIN_ID=0`
- `RMW_IMPLEMENTATION=rmw_fastrtps_cpp`
- `AMENT_PREFIX_PATH` - 包路径
- `LD_LIBRARY_PATH` - 库路径
- `PYTHONPATH` - Python路径

## 📊 系统检查

运行测试脚本会检查：
- ✅ 启动脚本存在性
- ✅ Launch文件存在性  
- ✅ 编译包完整性
- ✅ 环境变量设置

## 🚨 故障排除

### 1. **脚本权限问题**
```bash
chmod +x run_homi_speech.sh
chmod +x setup_env.sh
chmod +x run_robdog.sh
chmod +x test_homi_speech.sh
```

### 2. **包未编译**
```bash
colcon build --packages-select homi_speech homi_speech_interface launch_package --symlink-install
```

### 3. **ROS2环境问题**
```bash
source /opt/ros/humble/setup.bash
```

### 4. **网络服务测试**
```bash
# 启动homi_speech后，在另一个终端测试
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"scanWifiNetworks\"}'}"
```

## 🎮 使用流程

### 标准启动流程
1. **启动homi_speech**:
   ```bash
   ./run_homi_speech.sh full
   ```

2. **在新终端启动robdog**:
   ```bash
   ./run_robdog.sh
   ```

3. **测试WiFi扫描**:
   ```bash
   ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"scanWifiNetworks\"}'}"
   ```

### 调试启动流程
1. **启动最小版本**:
   ```bash
   ./run_homi_speech.sh minimal
   ```

2. **检查服务状态**:
   ```bash
   ros2 service list | grep homi_speech
   ```

## 📝 日志查看

### 查看启动日志
```bash
# homi_speech日志会直接显示在终端
# 如需保存日志，使用：
./run_homi_speech.sh full 2>&1 | tee homi_speech.log
```

### 查看ROS2节点状态
```bash
ros2 node list
ros2 topic list
ros2 service list
```

## 🔄 重启服务

### 停止服务
- 在运行的终端按 `Ctrl+C`

### 重新启动
```bash
./run_homi_speech.sh full
```

## 📞 服务接口

### 网络服务接口
- **服务名**: `/homi_speech/network_service`
- **服务类型**: `homi_speech_interface/srv/NetCtrl`
- **请求格式**: `{data: '{"command": "scanWifiNetworks"}'}`

### WiFi扫描命令
```bash
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"scanWifiNetworks\"}'}"
```

## 🎯 下一步

1. **启动homi_speech**: `./run_homi_speech.sh full`
2. **启动robdog**: `./run_robdog.sh` 
3. **测试WiFi功能**: 使用平台发送WiFi扫描请求
4. **验证响应格式**: 检查返回的平台标准格式

---

**🎊 所有脚本已恢复，可以正常使用homi_speech了！**
