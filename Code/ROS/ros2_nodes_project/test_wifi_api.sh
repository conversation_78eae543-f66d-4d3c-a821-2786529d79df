#!/bin/bash

# WiFi API 测试脚本
# 用于测试远程WiFi管理接口

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# API配置
BASE_URL="http://36.140.17.36:10000"
DEVICE_ID="1222004229866666660001992"
TENANT_CODE="25506132"
NONCE="abchcgdyuqmnbd79n"
SIGN="uklyLmbAq9qEYhUnxZXnThL3sRQ36dxK5TVxseQp1II="
APP_KEY="aHDHUQQwX8C6"
AUTH_TOKEN="eyJhbGciOiJIUzI1NiJ9.eyJpZCI6MjYxODk1NjM5Nzc5MDU3NjY0LCJ0ZW5hbnRVc2VySWQiOiIxODg2NzEwMzg4MSIsInBob25lIjoiMTg4NjcxMDM4ODEiLCJkZXZpY2VJZCI6IjEyMjIwMDQyMjk4NjY2NjY2NjAwMDE5OTIiLCJleHBpcmUiOjIwNjkwNTM3Nzc3MDQsInZlcnNpb24iOjB9.bCtfUYsi-7fFDg2Gz9ebbq5PqN8ftOJpSuVt9JdqKQg"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}       WiFi API 测试工具${NC}"
echo -e "${BLUE}========================================${NC}"

# 显示使用说明
show_usage() {
    echo -e "${YELLOW}使用方法:${NC}"
    echo -e "  $0 [命令]"
    echo -e ""
    echo -e "${YELLOW}命令:${NC}"
    echo -e "  ${GREEN}list${NC}     - 获取WiFi列表"
    echo -e "  ${GREEN}set${NC}      - 设置WiFi连接"
    echo -e "  ${GREEN}status${NC}   - 查询WiFi连接状态"
    echo -e "  ${GREEN}help${NC}     - 显示此帮助信息"
    echo -e ""
    echo -e "${YELLOW}示例:${NC}"
    echo -e "  $0 list      # 获取WiFi列表"
    echo -e "  $0 status    # 查询连接状态"
}

# 生成当前时间戳
get_timestamp() {
    echo $(date +%s)000
}

# 通用请求头
get_headers() {
    local timestamp=$(get_timestamp)
    echo "--header 'VSS-Tenant-Code: $TENANT_CODE' \
--header 'VSS-Timestamp: $timestamp' \
--header 'VSS-Nonce: $NONCE' \
--header 'VSS-Sign: $SIGN' \
--header 'VSS-Authorization-Token: $AUTH_TOKEN' \
--header 'VSS-App-Key: $APP_KEY' \
--header 'VSS-Device-Id: $DEVICE_ID' \
--header 'Content-Type: application/json'"
}

# 获取WiFi列表
get_wifi_list() {
    echo -e "${YELLOW}正在获取WiFi列表...${NC}"
    echo -e "${BLUE}设备ID: $DEVICE_ID${NC}"
    echo -e ""
    
    local headers=$(get_headers)
    local url="$BASE_URL/robot/business/api/device/client/config/wifi/list?deviceId=$DEVICE_ID"
    
    echo -e "${YELLOW}请求URL: $url${NC}"
    echo -e ""
    
    local response=$(eval "curl --silent --request GET --url '$url' $headers")
    
    echo -e "${GREEN}响应结果:${NC}"
    echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
    
    # 解析响应
    local success=$(echo "$response" | grep -o '"success":[^,]*' | cut -d':' -f2)
    local query_status=$(echo "$response" | grep -o '"queryStatus":[^,]*' | cut -d':' -f2)
    
    echo -e ""
    if [[ "$success" == "true" ]]; then
        echo -e "${GREEN}✓ API调用成功${NC}"
        case $query_status in
            0) echo -e "${YELLOW}📡 查询状态: 查询中${NC}" ;;
            1) echo -e "${GREEN}📡 查询状态: 查询成功${NC}" ;;
            2) echo -e "${RED}📡 查询状态: 查询失败${NC}" ;;
            *) echo -e "${YELLOW}📡 查询状态: 未知($query_status)${NC}" ;;
        esac
    else
        echo -e "${RED}✗ API调用失败${NC}"
    fi
}

# 查询WiFi连接状态
get_wifi_status() {
    echo -e "${YELLOW}正在查询WiFi连接状态...${NC}"
    echo -e "${BLUE}设备ID: $DEVICE_ID${NC}"
    echo -e ""
    
    # 这里可以添加WiFi状态查询的API调用
    echo -e "${YELLOW}注意: WiFi状态查询API需要根据实际接口实现${NC}"
}

# 主程序
case "${1:-help}" in
    "list")
        get_wifi_list
        ;;
    "status")
        get_wifi_status
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        echo -e "${RED}错误: 未知命令 '$1'${NC}"
        echo -e ""
        show_usage
        exit 1
        ;;
esac

echo -e ""
echo -e "${BLUE}========================================${NC}"
