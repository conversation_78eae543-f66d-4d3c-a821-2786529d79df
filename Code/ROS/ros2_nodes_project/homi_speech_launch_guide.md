# homi_speech 独立启动指南

本文档介绍如何单独运行 `homi_speech` 语音模块，提供了两个不同的 launch 文件选项。

## 文件说明

### 1. homi_speech_only.launch.py
**完整版本** - 包含语音模块运行所需的所有依赖节点

**包含的节点：**
- `audio_recorder_node` - 音频录制节点（带自动重启）
- `audio_player_node` - 音频播放节点
- `homi_speech` - 语音处理模块
- `homi_audio_player` - 音频播放器

**适用场景：**
- 完整的语音功能测试
- 需要音频录制和播放功能
- 生产环境使用

### 2. homi_speech_minimal.launch.py
**最小版本** - 只启动核心语音模块

**包含的节点：**
- `homi_speech` - 语音处理模块

**适用场景：**
- 快速测试语音模块
- 调试语音处理逻辑
- 最小化资源占用

## 使用方法

### 方法1：使用完整版本
```bash
# 设置环境
cd /mine/note/Code/ROS/ros2_nodes_project
source ./setup_env.sh

# 启动完整的语音模块
ros2 launch launch_package homi_speech_only.launch.py
```

### 方法2：使用最小版本
```bash
# 设置环境
cd /mine/note/Code/ROS/ros2_nodes_project
source ./setup_env.sh

# 启动最小化语音模块
ros2 launch launch_package homi_speech_minimal.launch.py
```

### 方法3：直接使用colcon启动
```bash
# 编译并启动
cd /mine/note/Code/ROS/ros2_nodes_project
colcon build --packages-select launch_package
source install/setup.bash
ros2 launch launch_package homi_speech_only.launch.py
```

## 验证运行状态

### 检查节点状态
```bash
# 查看运行中的节点
ros2 node list

# 查看语音相关话题
ros2 topic list | grep -E "(audio|speech|homi)"

# 查看节点详细信息
ros2 node info /homi_speech_node  # 具体节点名称可能不同
```

### 检查日志
```bash
# 查看所有节点日志
ros2 log view

# 查看特定节点日志
ros2 log view /audio_recorder/audio_recorder_node
ros2 log view /audio_player/audio_player_node
```

## 故障排除

### 常见问题

1. **音频设备问题**
   ```bash
   # 检查音频设备
   aplay -l  # 查看播放设备
   arecord -l  # 查看录制设备
   ```

2. **权限问题**
   ```bash
   # 添加用户到音频组
   sudo usermod -a -G audio $USER
   # 重新登录后生效
   ```

3. **依赖包缺失**
   ```bash
   # 检查包是否存在
   ros2 pkg list | grep -E "(homi_speech|audio_recorder|audio_player)"
   
   # 重新编译相关包
   colcon build --packages-select homi_speech audio_recorder audio_player homi_audio_player
   ```

4. **环境变量问题**
   ```bash
   # 确保正确设置环境
   echo $ROS_DOMAIN_ID
   echo $RMW_IMPLEMENTATION
   echo $AMENT_PREFIX_PATH
   ```

## 配置文件

### 音频播放器配置
位置：`audio_player/config/param.yaml`

### 语音模块配置
位置：`homi_speech/launch/speech_helper_near.launch.py`

## 性能监控

### 资源使用情况
```bash
# 查看CPU和内存使用
top -p $(pgrep -f "homi_speech\|audio_recorder\|audio_player")

# 查看ROS2节点性能
ros2 run rqt_top rqt_top
```

### 网络流量
```bash
# 查看ROS2话题流量
ros2 topic hz /audio_node/audio_data  # 示例话题名
ros2 topic bw /audio_node/audio_data
```

## 注意事项

1. **音频设备独占**：确保没有其他程序占用音频设备
2. **网络配置**：确保ROS_DOMAIN_ID设置正确，避免与其他ROS2系统冲突
3. **资源限制**：语音处理可能消耗较多CPU资源，注意系统负载
4. **日志管理**：长时间运行时注意日志文件大小

## 扩展使用

### 添加自定义参数
可以修改launch文件添加自定义参数：

```python
# 在launch文件中添加参数
DeclareLaunchArgument(
    'custom_param',
    default_value='default_value',
    description='Custom parameter description'
)
```

### 集成到其他系统
这些launch文件可以作为子模块包含到更大的系统中：

```python
homi_speech_include = IncludeLaunchDescription(
    PythonLaunchDescriptionSource([os.path.join(
        get_package_share_directory('launch_package'), 'launch'),
        '/homi_speech_only.launch.py'])
)
```
