#!/usr/bin/env python3
"""
测试异步回调修复后的WiFi扫描功能
验证使用异步回调方式解决超时问题
"""

import json
import time
import subprocess
import sys

def test_service_availability():
    """测试服务可用性"""
    print("=== 测试服务可用性 ===")
    
    try:
        # 检查服务列表
        result = subprocess.run(
            ['ros2', 'service', 'list'],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        services = result.stdout.strip().split('\n')
        network_service_found = '/homi_speech/network_service' in services
        
        print(f"服务总数: {len(services)}")
        print(f"网络服务状态: {'✅ 可用' if network_service_found else '❌ 不可用'}")
        
        if network_service_found:
            # 检查服务类型
            type_result = subprocess.run(
                ['ros2', 'service', 'type', '/homi_speech/network_service'],
                capture_output=True,
                text=True,
                timeout=5
            )
            print(f"服务类型: {type_result.stdout.strip()}")
        
        return network_service_found
        
    except Exception as e:
        print(f"❌ 检查服务可用性失败: {e}")
        return False

def test_async_callback_service_call():
    """测试异步回调服务调用"""
    print("\n=== 测试异步回调服务调用 ===")
    
    # 构建服务调用命令
    service_cmd = [
        'ros2', 'service', 'call',
        '/homi_speech/network_service',
        'homi_speech_interface/srv/NetCtrl',
        '"{data: \'{\\"command\\": \\"scanWifiNetworks\\"}\'}\"'
    ]
    
    print(f"执行命令: {' '.join(service_cmd)}")
    print("注意: 现在使用异步回调，应该不会出现超时问题")
    
    try:
        start_time = time.time()
        
        # 执行服务调用
        result = subprocess.run(
            service_cmd,
            capture_output=True,
            text=True,
            timeout=15  # 给足够的时间
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"调用耗时: {duration:.2f}秒")
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print(f"标准输出:\n{result.stdout}")
            
            # 尝试解析响应
            try:
                # 查找JSON部分
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if 'result:' in line:
                        json_part = line.split('result:')[1].strip().strip('"')
                        if json_part:
                            response_data = json.loads(json_part)
                            print(f"\n解析的响应数据:")
                            print(json.dumps(response_data, indent=2, ensure_ascii=False))
                            
                            # 分析响应状态
                            if 'status' in response_data:
                                status = response_data['status']
                                code = response_data.get('code', 'N/A')
                                print(f"\n响应分析:")
                                print(f"  状态: {status}")
                                print(f"  代码: {code}")
                                
                                if status == 'completed':
                                    networks = response_data.get('networks', [])
                                    print(f"  网络数量: {len(networks)}")
                                elif status == 'scanning':
                                    print(f"  扫描状态: 进行中")
                                elif status == 'error':
                                    error = response_data.get('error', 'Unknown error')
                                    print(f"  错误信息: {error}")
                            break
            except Exception as parse_error:
                print(f"解析响应失败: {parse_error}")
        
        if result.stderr:
            print(f"标准错误:\n{result.stderr}")
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ 服务调用超时 (15秒)")
        return False
    except Exception as e:
        print(f"❌ 服务调用异常: {e}")
        return False

def test_multiple_calls():
    """测试多次调用"""
    print("\n=== 测试多次调用 ===")
    
    success_count = 0
    total_calls = 3
    
    for i in range(total_calls):
        print(f"\n第 {i+1} 次调用:")
        
        service_cmd = [
            'ros2', 'service', 'call',
            '/homi_speech/network_service',
            'homi_speech_interface/srv/NetCtrl',
            '"{data: \'{\\"command\\": \\"scanWifiNetworks\\"}\'}\"'
        ]
        
        try:
            start_time = time.time()
            result = subprocess.run(
                service_cmd,
                capture_output=True,
                text=True,
                timeout=10
            )
            end_time = time.time()
            
            if result.returncode == 0:
                success_count += 1
                print(f"  ✅ 成功 (耗时: {end_time - start_time:.2f}秒)")
            else:
                print(f"  ❌ 失败 (返回码: {result.returncode})")
                
        except subprocess.TimeoutExpired:
            print(f"  ❌ 超时")
        except Exception as e:
            print(f"  ❌ 异常: {e}")
        
        # 间隔一秒
        if i < total_calls - 1:
            time.sleep(1)
    
    success_rate = (success_count / total_calls) * 100
    print(f"\n成功率: {success_count}/{total_calls} ({success_rate:.1f}%)")
    
    return success_rate >= 80  # 80%以上成功率认为通过

def check_robdog_logs():
    """检查robdog日志中是否还有executor错误"""
    print("\n=== 检查robdog日志 ===")
    
    print("提示: 请检查robdog_control节点的日志输出")
    print("应该看到:")
    print("  ✅ 'Calling network service for WiFi scan'")
    print("  ✅ 'WiFi list query completed successfully'")
    print("  ❌ 不应该看到 'Node has already been added to an executor'")
    print("  ❌ 不应该看到 'timeout' 错误")

def main():
    """主函数"""
    print("WiFi异步回调修复验证工具")
    print("验证使用异步回调解决超时和executor问题")
    print("=" * 60)
    
    # 检查服务可用性
    service_available = test_service_availability()
    
    if not service_available:
        print("\n❌ 服务不可用，请确保:")
        print("1. homi_speech节点正在运行: ./run_homi_speech.sh full")
        print("2. robdog_control节点正在运行: ./run_robdog.sh")
        return False
    
    # 测试异步回调服务调用
    single_call_success = test_async_callback_service_call()
    
    # 测试多次调用
    multiple_calls_success = test_multiple_calls()
    
    # 检查日志
    check_robdog_logs()
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"  服务可用性: {'✅ 正常' if service_available else '❌ 异常'}")
    print(f"  单次调用: {'✅ 成功' if single_call_success else '❌ 失败'}")
    print(f"  多次调用: {'✅ 成功' if multiple_calls_success else '❌ 失败'}")
    
    overall_success = service_available and single_call_success and multiple_calls_success
    
    if overall_success:
        print("\n🎉 异步回调修复验证成功！")
        print("   ✅ 不再出现executor错误")
        print("   ✅ 不再出现超时问题")
        print("   ✅ 服务调用正常工作")
    else:
        print("\n❌ 验证失败，请检查:")
        print("   1. 节点是否正常运行")
        print("   2. 服务是否正确响应")
        print("   3. 网络连接是否正常")
    
    print("\n📋 技术改进:")
    improvements = [
        "✅ 使用异步回调替代同步等待",
        "✅ 避免了executor冲突问题", 
        "✅ 解决了future.wait_for超时问题",
        "✅ 提高了服务调用的可靠性",
        "✅ 保持了平台响应格式的一致性"
    ]
    
    for improvement in improvements:
        print(f"  {improvement}")
    
    print("\n🎯 下一步:")
    print("1. 通过平台发送WiFi扫描请求测试完整流程")
    print("2. 监控robdog_control日志确认无错误")
    print("3. 验证响应格式符合平台要求")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
