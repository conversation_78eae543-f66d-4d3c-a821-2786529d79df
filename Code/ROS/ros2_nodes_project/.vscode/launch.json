{"version": "0.2.0", "configurations": [{"name": "Debug robdog_control_node", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/install/robdog_control/lib/robdog_control/robdog_control_node", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "ROS_DOMAIN_ID", "value": "0"}, {"name": "AMENT_PREFIX_PATH", "value": "${workspaceFolder}/install/robdog_control:${workspaceFolder}/install/homi_speech:${workspaceFolder}/install/homi_speech_interface:/opt/ros/humble"}, {"name": "COLCON_PREFIX_PATH", "value": "${workspaceFolder}/install"}, {"name": "LD_LIBRARY_PATH", "value": "${workspaceFolder}/install/lib:${workspaceFolder}/install/robdog_control/lib:${workspaceFolder}/install/homi_speech/lib:${workspaceFolder}/install/homi_speech_interface/lib:/opt/ros/humble/lib"}, {"name": "PYTHONPATH", "value": "${workspaceFolder}/install/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages"}, {"name": "CMAKE_PREFIX_PATH", "value": "${workspaceFolder}/install:/opt/ros/humble"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "build", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "Debug homi_speech_node", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/install/homi_speech/lib/homi_speech/speech_core", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "ROS_DOMAIN_ID", "value": "0"}, {"name": "AMENT_PREFIX_PATH", "value": "${workspaceFolder}/install/robdog_control:${workspaceFolder}/install/homi_speech:${workspaceFolder}/install/homi_speech_interface:/opt/ros/humble"}, {"name": "COLCON_PREFIX_PATH", "value": "${workspaceFolder}/install"}, {"name": "LD_LIBRARY_PATH", "value": "${workspaceFolder}/install/lib:${workspaceFolder}/install/robdog_control/lib:${workspaceFolder}/install/homi_speech/lib:${workspaceFolder}/install/homi_speech_interface/lib:/opt/ros/humble/lib"}, {"name": "PYTHONPATH", "value": "${workspaceFolder}/install/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages"}, {"name": "CMAKE_PREFIX_PATH", "value": "${workspaceFolder}/install:/opt/ros/humble"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "build", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "Launch robdog_control with params", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/install/robdog_control/lib/robdog_control/robdog_control_node", "args": ["--ros-args", "-p", "debug:=true"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "ROS_DOMAIN_ID", "value": "0"}, {"name": "RCUTILS_LOGGING_SEVERITY", "value": "DEBUG"}, {"name": "AMENT_PREFIX_PATH", "value": "${workspaceFolder}/install/robdog_control:${workspaceFolder}/install/homi_speech:${workspaceFolder}/install/homi_speech_interface:/opt/ros/humble"}, {"name": "COLCON_PREFIX_PATH", "value": "${workspaceFolder}/install"}, {"name": "LD_LIBRARY_PATH", "value": "${workspaceFolder}/install/lib:${workspaceFolder}/install/robdog_control/lib:${workspaceFolder}/install/homi_speech/lib:${workspaceFolder}/install/homi_speech_interface/lib:/opt/ros/humble/lib"}, {"name": "PYTHONPATH", "value": "${workspaceFolder}/install/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages"}, {"name": "CMAKE_PREFIX_PATH", "value": "${workspaceFolder}/install:/opt/ros/humble"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "build", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "Attach to running node", "type": "cppdbg", "request": "attach", "program": "${workspaceFolder}/install/robdog_control/lib/robdog_control/robdog_control_node", "processId": "${command:pickProcess}", "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "miDebuggerPath": "/usr/bin/gdb"}]}