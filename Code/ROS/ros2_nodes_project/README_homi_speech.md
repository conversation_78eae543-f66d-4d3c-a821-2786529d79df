# homi_speech 独立运行方案

## 概述

为了方便单独测试和运行 `homi_speech` 语音模块，我创建了专门的 launch 文件和启动脚本。

## 文件结构

```
/mine/note/Code/ROS/ros2_nodes_project/
├── src/launch_package/launch/
│   ├── homi_speech_only.launch.py      # 完整版本launch文件
│   └── homi_speech_minimal.launch.py   # 最小版本launch文件
├── run_homi_speech.sh                   # 便捷启动脚本
├── homi_speech_launch_guide.md          # 详细使用指南
└── README_homi_speech.md               # 本文档
```

## 快速开始

### 方法1：使用便捷脚本（推荐）

```bash
# 进入项目目录
cd /mine/note/Code/ROS/ros2_nodes_project

# 查看帮助
./run_homi_speech.sh help

# 启动完整版本（默认）
./run_homi_speech.sh

# 启动完整版本（显式指定）
./run_homi_speech.sh full

# 启动最小版本
./run_homi_speech.sh minimal
```

### 方法2：直接使用ROS2命令

```bash
# 设置环境
cd /mine/note/Code/ROS/ros2_nodes_project
source ./setup_env.sh

# 启动完整版本
ros2 launch launch_package homi_speech_only.launch.py

# 启动最小版本
ros2 launch launch_package homi_speech_minimal.launch.py
```

## 版本说明

### 完整版本 (homi_speech_only.launch.py)
**包含组件：**
- ✅ homi_speech 语音处理模块
- ✅ audio_recorder 音频录制节点
- ✅ audio_player 音频播放节点
- ✅ homi_audio_player 音频播放器
- ✅ 自动重启机制

**适用场景：**
- 完整的语音交互测试
- 需要音频录制和播放功能
- 生产环境部署

### 最小版本 (homi_speech_minimal.launch.py)
**包含组件：**
- ✅ homi_speech 语音处理模块

**适用场景：**
- 快速功能验证
- 调试语音处理逻辑
- 资源受限环境

## 验证运行

### 检查节点状态
```bash
# 查看运行中的节点
ros2 node list

# 查看语音相关话题
ros2 topic list | grep -E "(audio|speech|homi)"

# 实时监控话题数据
ros2 topic echo /audio_node/audio_data  # 示例话题
```

### 检查系统资源
```bash
# 查看进程状态
ps aux | grep -E "(homi_speech|audio_recorder|audio_player)"

# 查看资源使用
top -p $(pgrep -f "homi_speech")
```

## 故障排除

### 常见问题及解决方案

1. **环境变量未设置**
   ```bash
   # 确保运行了环境设置脚本
   source ./setup_env.sh
   ```

2. **音频设备问题**
   ```bash
   # 检查音频设备
   aplay -l && arecord -l
   
   # 测试音频播放
   speaker-test -t wav -c 2
   ```

3. **权限问题**
   ```bash
   # 添加用户到音频组
   sudo usermod -a -G audio $USER
   # 重新登录生效
   ```

4. **包依赖缺失**
   ```bash
   # 重新编译相关包
   colcon build --packages-select homi_speech audio_recorder audio_player homi_audio_player launch_package
   ```

## 配置自定义

### 修改音频参数
编辑文件：`audio_player/config/param.yaml`

### 修改语音参数
编辑文件：`homi_speech/launch/speech_helper_near.launch.py`

### 添加自定义节点
在 launch 文件中添加新的节点配置：

```python
custom_node = Node(
    package="your_package",
    executable="your_executable",
    name="your_node_name",
    output='log',
    respawn=True
)
```

## 性能优化

### 资源监控
```bash
# 监控CPU使用率
htop -p $(pgrep -f "homi_speech")

# 监控内存使用
free -h && ps aux --sort=-%mem | head
```

### 网络优化
```bash
# 设置合适的ROS_DOMAIN_ID避免冲突
export ROS_DOMAIN_ID=42

# 使用高性能RMW实现
export RMW_IMPLEMENTATION=rmw_fastrtps_cpp
```

## 集成到其他系统

这些 launch 文件可以作为子模块集成到更大的系统中：

```python
# 在其他launch文件中包含
homi_speech_include = IncludeLaunchDescription(
    PythonLaunchDescriptionSource([os.path.join(
        get_package_share_directory('launch_package'), 'launch'),
        '/homi_speech_only.launch.py'])
)
```

## 开发建议

1. **测试流程**：先使用最小版本验证核心功能，再使用完整版本测试集成
2. **日志管理**：定期清理日志文件，避免磁盘空间不足
3. **版本控制**：修改配置文件时做好备份
4. **性能测试**：在目标硬件上进行充分的性能测试

## 技术支持

如遇到问题，请检查：
1. 环境变量设置是否正确
2. 依赖包是否完整编译
3. 音频设备是否正常工作
4. 系统资源是否充足

详细的故障排除指南请参考：`homi_speech_launch_guide.md`
