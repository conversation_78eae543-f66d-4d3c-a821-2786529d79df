#!/usr/bin/env python3
"""
测试WiFi服务调用
验证修改后的handleWifiListQuery方法是否正确调用ROS2服务
"""

import json
import time
import subprocess
import sys

def test_network_service():
    """测试网络服务是否可用"""
    print("=== 测试网络服务可用性 ===")
    
    try:
        # 检查服务是否存在
        result = subprocess.run([
            'ros2', 'service', 'list'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            services = result.stdout.strip().split('\n')
            network_service = '/homi_speech/network_service'
            
            if network_service in services:
                print(f"✓ 网络服务 {network_service} 已找到")
                return True
            else:
                print(f"✗ 网络服务 {network_service} 未找到")
                print("可用服务列表:")
                for service in services:
                    if 'network' in service.lower() or 'homi_speech' in service:
                        print(f"  - {service}")
                return False
        else:
            print(f"✗ 无法获取服务列表: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 服务列表查询超时")
        return False
    except Exception as e:
        print(f"✗ 查询服务时出错: {e}")
        return False

def test_service_call():
    """测试服务调用"""
    print("\n=== 测试WiFi扫描服务调用 ===")
    
    try:
        # 调用网络服务进行WiFi扫描
        cmd = [
            'ros2', 'service', 'call',
            '/homi_speech/network_service',
            'homi_speech_interface/srv/NetCtrl',
            '"{data: \'{\\"command\\": \\"scanWifiNetworks\\"}\'}\"'
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ 服务调用成功")
            print("响应内容:")
            print(result.stdout)
            
            # 尝试解析响应
            try:
                # 提取响应中的result字段
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if 'result:' in line:
                        result_data = line.split('result:', 1)[1].strip().strip('"\'')
                        if result_data:
                            parsed_result = json.loads(result_data)
                            print("\n解析后的结果:")
                            print(json.dumps(parsed_result, indent=2, ensure_ascii=False))
                        break
            except Exception as parse_error:
                print(f"解析响应时出错: {parse_error}")
            
            return True
        else:
            print(f"✗ 服务调用失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 服务调用超时")
        return False
    except Exception as e:
        print(f"✗ 服务调用时出错: {e}")
        return False

def test_wifi_list_format():
    """测试WiFi列表格式"""
    print("\n=== 测试WiFi列表格式 ===")
    
    # 模拟服务响应格式
    mock_service_response = {
        "wifiNetworks": [
            {
                "ssid": "MyHome_WiFi",
                "signalStrength": -45,
                "security": "WPA2"
            },
            {
                "ssid": "Office_Network",
                "signalStrength": -67,
                "security": "WPA3"
            },
            {
                "ssid": "Open_Network",
                "signalStrength": -55,
                "security": "none"
            }
        ]
    }
    
    print("模拟服务响应:")
    print(json.dumps(mock_service_response, indent=2, ensure_ascii=False))
    
    # 模拟处理逻辑
    wifi_list = []
    if "wifiNetworks" in mock_service_response:
        for network in mock_service_response["wifiNetworks"]:
            if "ssid" in network and "signalStrength" in network and "security" in network:
                wifi_info = {
                    "wifiName": network["ssid"],
                    "signalStrength": network["signalStrength"],
                    "encryptState": 1 if network["security"] not in ["none", "", "--"] else 0
                }
                
                # 过滤未加密的WiFi
                if wifi_info["encryptState"] == 1:
                    wifi_list.append(wifi_info)
    
    print("\n处理后的WiFi列表 (过滤未加密):")
    print(json.dumps(wifi_list, indent=2, ensure_ascii=False))
    
    # 构建最终响应格式
    final_response = {
        "deviceId": "10012",
        "domain": "DEVICE_PROPERTIES",
        "event": "wifi_list_query",
        "eventId": "test_001",
        "seq": str(int(time.time() * 1000)),
        "body": {
            "queryStatus": 1,
            "wifiList": wifi_list
        }
    }
    
    print("\n最终响应格式:")
    print(json.dumps(final_response, indent=2, ensure_ascii=False))

def check_ros_environment():
    """检查ROS环境"""
    print("=== 检查ROS环境 ===")
    
    try:
        # 检查ROS_DOMAIN_ID
        result = subprocess.run(['printenv', 'ROS_DOMAIN_ID'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ ROS_DOMAIN_ID: {result.stdout.strip()}")
        else:
            print("⚠ ROS_DOMAIN_ID 未设置")
        
        # 检查节点列表
        result = subprocess.run(['ros2', 'node', 'list'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            nodes = result.stdout.strip().split('\n')
            print(f"✓ 发现 {len(nodes)} 个节点")
            
            # 查找homi_speech相关节点
            homi_nodes = [node for node in nodes if 'homi_speech' in node]
            if homi_nodes:
                print("homi_speech相关节点:")
                for node in homi_nodes:
                    print(f"  - {node}")
            else:
                print("⚠ 未找到homi_speech相关节点")
        else:
            print(f"✗ 无法获取节点列表: {result.stderr}")
            
    except Exception as e:
        print(f"✗ 检查ROS环境时出错: {e}")

def main():
    """主函数"""
    print("WiFi服务调用测试工具")
    print("验证修改后的handleWifiListQuery方法")
    print("=" * 50)
    
    # 检查ROS环境
    check_ros_environment()
    
    # 测试服务可用性
    service_available = test_network_service()
    
    if service_available:
        # 测试服务调用
        test_service_call()
    else:
        print("\n⚠ 网络服务不可用，跳过服务调用测试")
        print("请确保homi_speech节点正在运行:")
        print("  ./run_homi_speech.sh minimal")
    
    # 测试格式处理
    test_wifi_list_format()
    
    print("\n" + "=" * 50)
    print("测试完成")
    
    if not service_available:
        print("\n建议:")
        print("1. 启动homi_speech节点: ./run_homi_speech.sh minimal")
        print("2. 确认网络服务正常运行")
        print("3. 重新运行此测试脚本")

if __name__ == "__main__":
    main()
