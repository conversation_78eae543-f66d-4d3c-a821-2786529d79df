# WiFi管理接口文档

本文档描述了在`RobdogCenter::handleEvent`方法中新增的三个WiFi管理接口，严格按照13.2信令定义实现。

## 13.2 信令定义

### 13.2.1 获取WiFi列表【平台→本体、response=true】

**功能描述：** 扫描并返回可用的WiFi网络列表（本体过滤未加密的WiFi）

**请求格式：**
```json
{
    "deviceId": "10012",
    "domain": "DEVICE_PROPERTIES",
    "event": "wifi_list_query",
    "eventId": "唯一id",
    "seq": "时间戳",
    "response": true,
    "body": {
    }
}
```

**响应格式：**
```json
{
    "deviceId": "10012",
    "domain": "DEVICE_PROPERTIES",
    "event": "wifi_list_query",
    "eventId": "随意",
    "seq": "时间戳",
    "body": {
        "queryStatus": 0,
        "wifiList": [{
            "wifiName": "xxx",
            "signalStrength": -25,
            "encryptState": 0
        }]
    }
}
```

**字段说明：**
- `queryStatus`: 查询状态
  - 0: 查询中
  - 1: 查询成功
  - 2: 查询失败
- `wifiName`: WiFi名称
- `signalStrength`: 信号强度数值（最小值-120、最大值-10）
- `encryptState`: 是否加密
  - 0: 未加密
  - 1: 已加密（本体过滤未加密的WiFi）

### 13.2.2 设置WiFi【平台→本体】

**功能描述：** 连接到指定的WiFi网络（异步操作，无响应）

**请求格式：**
```json
{
    "deviceId": "10012",
    "domain": "DEVICE_PROPERTIES",
    "event": "wifi_set",
    "eventId": "唯一id",
    "seq": "时间戳",
    "response": false,
    "body": {
        "deviceId": "10012",
        "wifiName": "xxx",
        "wifiPassword": "xxxxxx"
    }
}
```

**字段说明：**
- `deviceId`: 设备id
- `wifiName`: WiFi名称
- `wifiPassword`: WiFi密码（平台侧打印日志时需注意）

**注意：** 此接口为异步操作，不返回响应（response=false）

### 13.2.3 查询设置WiFi结果【平台→本体、response=true】

**功能描述：** 查询WiFi设置结果状态

**请求格式：**
```json
{
    "deviceId": "10012",
    "domain": "DEVICE_PROPERTIES",
    "event": "wifi_set_result_query",
    "eventId": "唯一id",
    "seq": "时间戳",
    "response": true,
    "body": {
    }
}
```

**响应格式：**
```json
{
    "deviceId": "10012",
    "domain": "DEVICE_PROPERTIES",
    "event": "wifi_set_result_query",
    "eventId": "随意",
    "seq": "时间戳",
    "body": {
        "wifiSetResult": 0
    }
}
```

**字段说明：**
- `wifiSetResult`: WiFi设置结果
  - 0: 连接中
  - 1: 连接成功
  - 2: 连接失败（超时）
  - 3: 连接失败（密码错误）

## 实现特性

### 技术实现
- 使用`nmcli`命令行工具进行WiFi管理
- 支持开放网络和加密网络连接
- 实时获取WiFi信号强度信息
- 完整的错误处理和异常捕获
- 严格按照13.2信令定义实现

### 安全特性
- 密码参数安全处理
- 命令注入防护
- 详细的日志记录
- 本体过滤未加密的WiFi网络

### 错误处理
- 网络扫描失败处理（queryStatus=2）
- 连接命令执行失败处理
- JSON解析错误处理
- 系统命令执行异常处理
- 智能判断连接失败原因（超时/密码错误）

## 使用示例

### 示例1：扫描WiFi网络
**请求：**
```json
{
    "deviceId": "10012",
    "domain": "DEVICE_PROPERTIES",
    "event": "wifi_list_query",
    "eventId": "req001",
    "seq": "1640995200000",
    "response": true,
    "body": {
    }
}
```

**响应：**
```json
{
    "deviceId": "10012",
    "domain": "DEVICE_PROPERTIES",
    "event": "wifi_list_query",
    "eventId": "req001",
    "seq": "1640995201000",
    "body": {
        "queryStatus": 1,
        "wifiList": [{
            "wifiName": "MyHome_WiFi",
            "signalStrength": -45,
            "encryptState": 1
        }, {
            "wifiName": "Office_Network",
            "signalStrength": -67,
            "encryptState": 1
        }]
    }
}
```

### 示例2：连接WiFi网络
**请求：**
```json
{
    "deviceId": "10012",
    "domain": "DEVICE_PROPERTIES",
    "event": "wifi_set",
    "eventId": "req002",
    "seq": "1640995202000",
    "response": false,
    "body": {
        "deviceId": "10012",
        "wifiName": "MyHome_WiFi",
        "wifiPassword": "mypassword123"
    }
}
```

### 示例3：查询连接状态
**请求：**
```json
{
    "deviceId": "10012",
    "domain": "DEVICE_PROPERTIES",
    "event": "wifi_set_result_query",
    "eventId": "req003",
    "seq": "1640995203000",
    "response": true,
    "body": {
    }
}
```

**响应：**
```json
{
    "deviceId": "10012",
    "domain": "DEVICE_PROPERTIES",
    "event": "wifi_set_result_query",
    "eventId": "req003",
    "seq": "1640995204000",
    "body": {
        "wifiSetResult": 1
    }
}
```

## 协议规范

### 信令格式要求
1. **严格按照13.2信令定义**：所有字段名称和格式必须完全一致
2. **时间戳格式**：使用毫秒级时间戳
3. **响应类型**：严格遵循response字段定义
4. **错误处理**：使用标准的状态码和错误信息

### 数据过滤规则
1. **WiFi列表过滤**：本体过滤未加密的WiFi网络（encryptState=0）
2. **信号强度范围**：-120dBm到-10dBm
3. **状态码标准化**：使用预定义的状态码值

## 注意事项

1. **权限要求：** 需要系统具有执行`nmcli`命令的权限
2. **网络管理器：** 系统需要安装并运行NetworkManager服务
3. **响应时间：** WiFi扫描可能需要几秒钟时间
4. **连接状态：** WiFi连接是异步过程，建议使用`wifi_set_result_query`查询最终连接状态
5. **隐私保护：** 平台侧打印日志时需注意WiFi密码的隐私保护

## 依赖项

- NetworkManager (`nmcli`命令)
- 系统权限：网络配置权限
- C++标准库：`<sstream>`, `<cstdlib>`, `<chrono>`, `<thread>`
- JSON库：jsoncpp
- ROS2环境：正确的类型支持库配置
