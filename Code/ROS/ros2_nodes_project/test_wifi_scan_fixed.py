#!/usr/bin/env python3
"""
测试修复后的WiFi扫描功能
验证executor错误已修复
"""

import json
import time
import subprocess
import sys

def test_wifi_scan_service():
    """测试WiFi扫描服务调用"""
    print("=== 测试WiFi扫描服务调用 ===")
    
    # 构建服务调用命令
    service_cmd = [
        'ros2', 'service', 'call',
        '/homi_speech/network_service',
        'homi_speech_interface/srv/NetCtrl',
        '"{data: \'{\\"command\\": \\"scanWifiNetworks\\"}\'}\"'
    ]
    
    print(f"执行命令: {' '.join(service_cmd)}")
    
    try:
        # 执行服务调用
        result = subprocess.run(
            service_cmd,
            capture_output=True,
            text=True,
            timeout=15
        )
        
        print(f"返回码: {result.returncode}")
        print(f"标准输出:\n{result.stdout}")
        
        if result.stderr:
            print(f"标准错误:\n{result.stderr}")
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ 服务调用超时")
        return False
    except Exception as e:
        print(f"❌ 服务调用异常: {e}")
        return False

def simulate_platform_request():
    """模拟平台WiFi扫描请求"""
    print("\n=== 模拟平台WiFi扫描请求 ===")
    
    # 平台请求格式
    platform_request = {
        "deviceId": "10012",
        "domain": "DEVICE_PROPERTIES", 
        "event": "wifi_list_query",
        "eventId": "test_001",
        "seq": str(int(time.time() * 1000)),
        "response": True,
        "body": {}
    }
    
    print("平台请求格式:")
    print(json.dumps(platform_request, indent=2, ensure_ascii=False))
    
    # 期望的响应格式
    expected_responses = [
        {
            "name": "查询中状态",
            "queryStatus": 0,
            "wifiList": []
        },
        {
            "name": "查询成功状态", 
            "queryStatus": 1,
            "wifiList": [
                {
                    "wifiName": "TestWiFi",
                    "signalStrength": -45,
                    "encryptState": 1
                }
            ]
        },
        {
            "name": "查询失败状态",
            "queryStatus": 2,
            "wifiList": []
        }
    ]
    
    print("\n期望的响应格式:")
    for response in expected_responses:
        print(f"\n{response['name']}:")
        platform_response = {
            "deviceId": "10012",
            "domain": "DEVICE_PROPERTIES",
            "event": "wifi_list_query", 
            "eventId": "test_001",
            "seq": str(int(time.time() * 1000)),
            "body": {
                "queryStatus": response["queryStatus"],
                "wifiList": response["wifiList"]
            }
        }
        print(json.dumps(platform_response, indent=2, ensure_ascii=False))

def check_service_availability():
    """检查服务可用性"""
    print("\n=== 检查服务可用性 ===")
    
    try:
        # 检查服务列表
        result = subprocess.run(
            ['ros2', 'service', 'list'],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if '/homi_speech/network_service' in result.stdout:
            print("✅ /homi_speech/network_service 服务可用")
            return True
        else:
            print("❌ /homi_speech/network_service 服务不可用")
            print("可用服务列表:")
            print(result.stdout)
            return False
            
    except Exception as e:
        print(f"❌ 检查服务可用性失败: {e}")
        return False

def check_nodes():
    """检查节点状态"""
    print("\n=== 检查节点状态 ===")
    
    try:
        # 检查节点列表
        result = subprocess.run(
            ['ros2', 'node', 'list'],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        print("当前运行的节点:")
        print(result.stdout)
        
        # 检查关键节点
        required_nodes = ['/robdog_control_node']
        missing_nodes = []
        
        for node in required_nodes:
            if node not in result.stdout:
                missing_nodes.append(node)
        
        if missing_nodes:
            print(f"❌ 缺少节点: {missing_nodes}")
            return False
        else:
            print("✅ 所有必需节点都在运行")
            return True
            
    except Exception as e:
        print(f"❌ 检查节点状态失败: {e}")
        return False

def main():
    """主函数"""
    print("WiFi扫描功能修复验证工具")
    print("验证executor错误已修复")
    print("=" * 50)
    
    # 检查节点状态
    nodes_ok = check_nodes()
    
    # 检查服务可用性
    service_ok = check_service_availability()
    
    if not nodes_ok or not service_ok:
        print("\n❌ 前置条件检查失败")
        print("\n请确保:")
        print("1. homi_speech节点正在运行: ./run_homi_speech.sh full")
        print("2. robdog_control节点正在运行: ./run_robdog.sh")
        return False
    
    # 模拟平台请求
    simulate_platform_request()
    
    # 测试服务调用
    print("\n" + "=" * 50)
    service_test_ok = test_wifi_scan_service()
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"  节点状态: {'✅ 正常' if nodes_ok else '❌ 异常'}")
    print(f"  服务可用性: {'✅ 正常' if service_ok else '❌ 异常'}")
    print(f"  服务调用: {'✅ 成功' if service_test_ok else '❌ 失败'}")
    
    if nodes_ok and service_ok and service_test_ok:
        print("\n🎉 WiFi扫描功能修复验证成功！")
        print("   executor错误已修复，服务调用正常")
    else:
        print("\n❌ 验证失败，请检查错误信息")
    
    print("\n📋 下一步:")
    print("1. 如果验证成功，可以通过平台发送WiFi扫描请求")
    print("2. 检查robdog_control日志确认没有executor错误")
    print("3. 验证返回的响应格式符合平台要求")
    
    return nodes_ok and service_ok and service_test_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
