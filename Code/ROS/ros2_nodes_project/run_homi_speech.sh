#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    homi_speech 语音模块启动脚本${NC}"
echo -e "${BLUE}========================================${NC}"

# 显示使用说明
show_usage() {
    echo -e "${YELLOW}使用方法:${NC}"
    echo -e "  $0 [选项]"
    echo -e ""
    echo -e "${YELLOW}选项:${NC}"
    echo -e "  ${GREEN}full${NC}     - 启动完整版本 (包含音频录制和播放)"
    echo -e "  ${GREEN}minimal${NC}  - 启动最小版本 (仅语音处理模块)"
    echo -e "  ${GREEN}help${NC}     - 显示此帮助信息"
    echo -e ""
    echo -e "${YELLOW}示例:${NC}"
    echo -e "  $0 full      # 启动完整版本"
    echo -e "  $0 minimal   # 启动最小版本"
    echo -e "  $0           # 默认启动完整版本"
}

# 检查参数
MODE=${1:-full}

case $MODE in
    "help"|"-h"|"--help")
        show_usage
        exit 0
        ;;
    "full")
        LAUNCH_FILE="homi_speech_only.launch.py"
        echo -e "${GREEN}启动模式: 完整版本${NC}"
        echo -e "${YELLOW}包含: 语音处理 + 音频录制 + 音频播放${NC}"
        ;;
    "minimal")
        LAUNCH_FILE="homi_speech_minimal.launch.py"
        echo -e "${GREEN}启动模式: 最小版本${NC}"
        echo -e "${YELLOW}包含: 仅语音处理模块${NC}"
        ;;
    *)
        echo -e "${RED}错误: 未知选项 '$MODE'${NC}"
        echo -e ""
        show_usage
        exit 1
        ;;
esac

echo -e ""

# 检查当前目录
if [ ! -f "setup_env.sh" ]; then
    echo -e "${RED}错误: 请在项目根目录运行此脚本${NC}"
    echo -e "${YELLOW}当前目录: $(pwd)${NC}"
    echo -e "${YELLOW}期望目录: /mine/note/Code/ROS/ros2_nodes_project${NC}"
    exit 1
fi

# 设置环境
echo -e "${YELLOW}正在设置环境...${NC}"

# 设置基础环境变量
export ROS_DOMAIN_ID=0
export RMW_IMPLEMENTATION=rmw_fastrtps_cpp

# 获取工作空间路径
WORKSPACE_DIR=$(pwd)

# 设置ROS2基础环境
source /opt/ros/humble/setup.bash 2>/dev/null || echo -e "${YELLOW}警告: ROS2环境设置可能有问题${NC}"

# 设置包路径
export AMENT_PREFIX_PATH="$WORKSPACE_DIR/install:$WORKSPACE_DIR/install/homi_speech:$WORKSPACE_DIR/install/launch_package:$WORKSPACE_DIR/install/homi_speech_interface:$WORKSPACE_DIR/install/robdog_control:$AMENT_PREFIX_PATH"

# 设置库路径
export LD_LIBRARY_PATH="$WORKSPACE_DIR/install/homi_speech_interface/lib:$WORKSPACE_DIR/install/homi_speech/lib:$WORKSPACE_DIR/install/robdog_control/lib:$LD_LIBRARY_PATH"

# 设置Python路径
if [ -d "$WORKSPACE_DIR/install/homi_speech_interface/local/lib/python3.10/site-packages" ]; then
    export PYTHONPATH="$WORKSPACE_DIR/install/homi_speech_interface/local/lib/python3.10/site-packages:$PYTHONPATH"
fi

# 检查launch文件是否存在
LAUNCH_PATH="src/launch_package/launch/$LAUNCH_FILE"
if [ ! -f "$LAUNCH_PATH" ]; then
    echo -e "${RED}错误: Launch文件不存在: $LAUNCH_PATH${NC}"
    exit 1
fi

echo -e "${GREEN}环境设置完成${NC}"
echo -e ""

# 显示启动信息
echo -e "${BLUE}启动信息:${NC}"
echo -e "  Launch文件: ${GREEN}$LAUNCH_FILE${NC}"
echo -e "  ROS_DOMAIN_ID: ${GREEN}$ROS_DOMAIN_ID${NC}"
echo -e "  RMW_IMPLEMENTATION: ${GREEN}$RMW_IMPLEMENTATION${NC}"
echo -e ""

# 提示用户
echo -e "${YELLOW}即将启动 homi_speech 模块...${NC}"
echo -e "${YELLOW}按 Ctrl+C 停止运行${NC}"
echo -e ""

# 等待2秒
sleep 2

# 启动launch文件
echo -e "${GREEN}正在启动...${NC}"

# 使用完整路径启动launch文件
LAUNCH_FULL_PATH="$WORKSPACE_DIR/install/launch_package/share/launch_package/launch/$LAUNCH_FILE"
ros2 launch "$LAUNCH_FULL_PATH"

# 捕获退出状态
EXIT_CODE=$?

echo -e ""
if [ $EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}homi_speech 模块已正常退出${NC}"
else
    echo -e "${RED}homi_speech 模块异常退出 (退出码: $EXIT_CODE)${NC}"
fi

echo -e "${BLUE}========================================${NC}"
