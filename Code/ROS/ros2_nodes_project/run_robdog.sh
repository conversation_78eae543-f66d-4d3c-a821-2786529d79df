#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}启动 RobDog Control Node...${NC}"

# 使用环境设置脚本
source ./setup_env.sh

# 检查可执行文件是否存在
if [ ! -f "./install/robdog_control/lib/robdog_control/robdog_control_node" ]; then
    echo -e "${RED}错误: 可执行文件不存在，请先编译项目${NC}"
    echo -e "${YELLOW}运行: colcon build --packages-select homi_speech_interface robdog_control --symlink-install${NC}"
    exit 1
fi

echo -e "${GREEN}启动节点...${NC}"

# 启动节点
./install/robdog_control/lib/robdog_control/robdog_control_node
