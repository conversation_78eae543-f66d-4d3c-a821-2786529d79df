#!/usr/bin/env python3

import launch
from launch import LaunchDescription
from launch.actions import LogInfo, IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
import os
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    """
    启动 homi_speech 语音模块的 launch 文件

    这个 launch 文件启动 homi_speech 语音处理模块
    包含：helper、speech_core、upload_image 节点
    """

    # ==================== homi_speech 语音模块 ====================
    homi_speech_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([os.path.join(
            get_package_share_directory('homi_speech'), 'launch'),
            '/speech_helper_near.launch.py'])
    )

    return LaunchDescription([
        # 添加启动信息
        LogInfo(msg='Starting homi_speech module...'),

        # 启动 homi_speech 语音处理模块
        homi_speech_launch,

        LogInfo(msg='homi_speech module started successfully.'),
    ])
