#!/usr/bin/env python3

import launch
from launch import LaunchDescription
from launch.actions import LogInfo, IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
import os
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    """
    最小化启动 homi_speech 语音模块的 launch 文件
    
    这个 launch 文件只启动核心的语音处理模块，不包含其他依赖节点。
    适用于测试或独立运行语音功能。
    """
    
    # ==================== homi_speech 语音模块 ====================
    homi_speech_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([os.path.join(
            get_package_share_directory('homi_speech'), 'launch'),
            '/speech_helper_near.launch.py'])
    )

    return LaunchDescription([
        # 添加启动信息
        LogInfo(msg='Starting minimal homi_speech module...'),
        
        # 启动 homi_speech 语音处理模块
        homi_speech_launch,
        
        LogInfo(msg='homi_speech module started successfully.'),
    ])
