#pragma once
#include <bits/stdint-intn.h>
#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <geometry_msgs/msg/pose.hpp>
#include <std_msgs/msg/string.hpp>
#include <std_msgs/msg/int64.hpp>
#include <tf2_ros/transform_broadcaster.h>
#include <tf2_ros/transform_listener.h>
#include <tf2/LinearMath/Quaternion.h>

#include <homi_speech_interface/msg/sigc_event.hpp>
#include <homi_speech_interface/msg/robdog_action.hpp>
#include <homi_speech_interface/srv/sigc_data.hpp>
#include <homi_speech_interface/srv/net_ctrl.hpp>
#include <homi_speech_interface/msg/propriety_set.hpp>
#include <homi_speech_interface/msg/proper_to_app.hpp>
#include <homi_speech_interface/msg/continue_move.hpp>
#include <homi_speech_interface/msg/assistant_event.hpp>
#include <homi_speech_interface/msg/wakeup.hpp>
#include <homi_speech_interface/srv/assistant_speech_text.hpp>
#include <homi_speech_interface/srv/assistant_abort.hpp>
#include <homi_speech_interface/srv/assistant_take_photo.hpp>
#include <homi_speech_interface/srv/set_wake_event.hpp>
#include <homi_speech_interface/srv/iot_control.hpp>
#include <homi_speech_interface/srv/set_diy_word.hpp>
#include <homi_speech_interface/msg/live_stream_task.hpp>
#include <homi_speech_interface/srv/phone_call.hpp>
#include <homi_speech_interface/srv/ntrip_account.hpp>

#include <xiaoli_com/deep_cmd.h>
#include <jsoncpp/json/json.h>
#include <fstream>
#include <unordered_set>
#include "public/litedb.h"
#include "public/audio_ctrl.h"
#include <homi_com/singleton.hpp>
#include <xiaoli_com/xiaoli_pub_def.h>
#include "sensor_msgs/msg/nav_sat_fix.hpp"
#include <std_srvs/srv/trigger.hpp>
#include <xiaoli_com/xiaoli_action_audio.h>
#include <nav_msgs/msg/odometry.hpp>
#include <mutex>

#define START_MOVE 1
#define GO_HOME_MOVE 2
#define GO_START_MOVE 3
#define RESTART_MOVE 4


#define GOING    1
#define STOP     2
#define FINISH     2
#define PAUSE    3
#define CANCLE   4
#define ABNORMAL   5
#define FINISH_TIMES     1
#define TRACE_GOING 11




#define LOW_BATTERY_CODE    224001
#define NOW_POSITION_NOT_NAVIGATION_CODE  224002
#define NOW_POSITION_NOT_MOVE_CODE  224003
#define DF_FSPEED_X 0.6
#define DF_FSPEED_Y 0.6

#if defined(YSC1_0) || defined(YSC1_1)
    #define DF_FSPEED_Z 0.5
#elif defined(UNITREE)
    #define DF_FSPEED_Z -0.5
#else
    #define DF_FSPEED_Z 0.5
#endif

#define ALARMLEVEL_1  1    //紧急告警
#define ALARMLEVEL_2  2    //重要告警
#define ALARMLEVEL_3  3    //一般告警
#define ALARMLEVEL_4  4    /
#define ALARMLEVEL_5  5

#define RAW_FREQUENCY 5
#define FREQUENCY 1

//定义来自CONTROL_NODE的命令

enum cmdFromNode{
    POWER_LEVEL_FROM_NODE=1,
    POWER_CHARGE_FROM_NODE,
    WIFI_NAME_FROM_NODE,
    IS_WIFI_CONNECT,
};

struct MapConfig {
    int img_height;
    int img_width;
    double resolution;
    double origin_x;
    double origin_y;
};

struct MonitorStatus {
  struct {
    int current = -1;
    int last = -1;
  } rtk;
  struct {
    std::string current;
    std::string last;
  } temperature;
  struct {
    int current = -1;
    int last = -1;
  } indoor;
};
enum class NetState { IDLE, WAITING_1SEC, WAITING_9SEC };

class RobdogCtrlNode;

class RobdogCenter : public base::singleton<RobdogCenter>  {
public:
    // RobdogCenter(rclcpp::Node::SharedPtr node, const std::string &configPath);
    RobdogCenter();
    ~RobdogCenter();
    void init(RobdogCtrlNode* node);

    // ********************** 其他函数
    void Proceationtype(int actiontype, int step);
    void checkStatusWatchdog();
    // void loadConfig(const std::string& configFilePath); 
    // void loadConfig();     
    void deep_ctl(int cmdID,int cmdValue); //灯光、语音、动作等交互
    void handle_UDP_data(char *data, size_t length);

    // ************************ 和定点移动有关的
    void playAudio (const std::string& filePath);
    bool isAtTarget (const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void checkTargetStatus();
    void checkTargetStatus_brocast();
    void checkPatrolStatus(const Json::Value &jBody);
    void checkGoHomeStatus(const Json::Value &jBody);
    void checkDeliverCakeStatus(const Json::Value &jBody);
    void checkBatteryStatus(const Json::Value &jBody);
    void moveToTarget (const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void moveToTargetAndPlayAudio (const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void moveToPatrolPoint (const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void moveToHomePoint (const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void moveTodeliverCakePoint (const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void moveToBatteryPoint (const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void readMappingPoints();
    void updateMapPoints(const std::string& event, const Json::Value& points);
    int mapCodeToStatus(int code);
    std::string getCodeName(int code);
    std::string getDescription(int code);

    // ************************ 和定时器相关的操作
    void setTotalCount(int count); // 设置发送次数的函数
    void triggerTimerCallback(); // 公开的函数用于手动调用定时器回调
    void timerCallback();
    void timerRobotPoseCallback();
    void timerRobotPathCallback();
    void sleepForDuration();  // 暂停函数 double seconds
    // void startTimer();
    void heartbeatTimerCallback();
    void initTimerCallback();
    void internetTimerCallback();
    void tripTimerCallback();
    void processNetworkStatusData(); // 处理网络状态数据

    void timerRobotCoordReport();
    // void ActionSuspend();
    // ************************ 超时处理相关函数
    void startTimeout(std::function<void()> timeoutCallback, std::function<void()> changeCallback, std::atomic<bool>& waiting_flag, std::chrono::seconds timeout_duration); // 超时处理基础函数
    void mapTimeoutHandler();  // 建图初始化超时处理
    void disconnectTimeoutHandler(); // 断连超时处理
    void navTimeoutHandler(const Json::Value &jBody,const std::string& topic_name);
    // ************************ 和设备信息相关的
    void deepStatusCallback(const homi_speech_interface::msg::ProprietySet::SharedPtr msg);
    void utStatusCallback();
    std::string get_robot_properties(const Json::Value &inValue);
    std::string get_connect_info_request(const Json::Value &inValue);
    void setProperties(const Json::Value& request);

    void targetPoseCallBack(const std_msgs::msg::String::SharedPtr msg);
    
    // ************************ 上报给平台的响应 *************************
    void sendRequestData(const std::string &data);
    void sendMapInitialResponse(int code, const std::string& msg);
    void sendMapCompleteResponse(int code,long mapId);
    void sendMapRepositionResponse(int code,const Json::Value &jBody);
    void sendChargeMarkResponse(int code, const std::string& msg);
    void deviceAlarmReport(int code);
    void processRepositioningResult(int taskStatusCode, const Json::Value &jBody);

    // ************************ 和感知主机相关的操作
    void SendtoNvOrin(const char* message, int nInd);
    // void navPositionCallback(const geometry_msgs::msg::Pose::SharedPtr msg);
    void navPositionCallback(const nav_msgs::msg::Odometry::SharedPtr odom);
    static std::array<double, 3> pixelToMap(double pixel_x, double pixel_y, double angle_deg,
                                 double origin_x, double origin_y, double resolution, int img_height);
    static std::array<double, 3> mapToPixel(double map_x, double map_y, double yaw,
                                               double origin_x, double origin_y, double resolution, int img_height);
    bool loadMapConfig(const std::string& filePath, MapConfig& config);
    void sendPoint(double x, double y, double angle);
    void pointTransformResultCallback(const std_msgs::msg::String::SharedPtr msg);
    rclcpp::TimerBase::SharedPtr nav_wait_timer_;
    rclcpp::TimerBase::SharedPtr move_point_timer_;
    std::chrono::steady_clock::time_point nav_wait_start_time_;
    const std::chrono::seconds nav_wait_timeout_{30};
    void navStatusCallback(const std_msgs::msg::String::SharedPtr msg);
    void navStatusNotifyCallback(const std_msgs::msg::String::SharedPtr msg);
    void actionFollow(int status); //0-停止跟随 1-开启跟随 2-开启UWB跟随
    void sendNavigationAction(int action,std::string mapId); //0: 终止导航 1: 启动导航 11: 执行重定位 
    void sendMapAction(int action,std::string url,std::string mapId); //建图操作
    void sendChargeAction(int action,std::string mapId,const Json::Value& jBody); //充电桩操作
    void sendCommandToUSLAM(const std::string& command);
    void sendLoaclizationAction(const std::string& action);
    // void sendChargeAction(const std::string& action);
    void sendNavigationAction(const std::string& action);
    void sendPatrolAction(const std::string& action);
    void checkNvidiaServiceStatus(bool openNavigationIfClosed, std::string mapId);
    void checkNvidia_srv_status(bool openNavigationIfClosed, std::string mapId);
    void handleTaskStatusCode(int taskStatusCode, const Json::Value& msgValue);
    void handleMapCompleteStatus(int taskStatusCode, const Json::Value& msgValue);
    void handleMapInitialStatus(int taskStatusCode, const Json::Value& msgValue);
    void handleChargeMarkStatus(int taskStatusCode, const Json::Value& msgValue);
    void handleChargeActionStatus(int taskStatusCode, const Json::Value& msgValue);
    bool isChargeMarkFailure(int taskStatusCode);
    bool isInitializationFailure(int taskStatusCode);
    void handleNavBeforeRepositioning(int taskStatusCode, const Json::Value& msgValue);
    bool isRepositioningFailure(int taskStatusCode);
    void handleNavigationStatus(int taskStatusCode,const Json::Value& jBody);
    void sendNavigationReport(int status);
    void handleNavigationTaskCompletion();
    bool isRepositioningResult(int taskStatusCode);
    // ************************  和拍照有关
    void callHelperPhoto();
    void takePhotoService();

    // ************************ 智能播报相关
    void BrocastIfAbortCallBack(const homi_speech_interface::msg::AssistantEvent::SharedPtr msg);
    void SendBrocastCallback();
    void sendStringToBrocast(const std::string& message);
    void timerRobotBroadcastCallback(); 
    void RobotBroadcastStatusToPlat(int status);   
    void moveToTargetAndBrocast(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);

    // ************************ 和平台消息有关的处理
    void handleTakePhotos();
    void handleDeliverExpress();
    void handleFetchExpress();
    void handleReportFamilyMovePoint();
    void handleParkPatrol();
    void handlegoHome();
    void handledeliverCake();
    void handleCancelMovement();
    void handleBatteryChargingPoint();

    void handleGestRec(const Json::Value &jBody);
    void handleFollowMe(const Json::Value &jBody);
    void handleSportMode(const Json::Value &jBody);
    void handleMotorSkill(const Json::Value &jBody);
    void handleRLSkill(const Json::Value &jBody);
    void handleGeneralAction(const Json::Value &jBody);
    void handleSpecificAction(const Json::Value &jBody);
    void handleRobotAction(const Json::Value &jBody);
    void handleRobotSkillOrganize(const Json::Value &jBody);
    void handleInteractionSkills(const Json::Value &jBody);
    void handleDevelopMode(const Json::Value &jBody);
    void handleGuardInstruction(const Json::Value &jBody);
    
    void handleRobotMove(const Json::Value &inValue, const Json::Value &jBody);
    void handleRobotView(const Json::Value &inValue, const Json::Value &jBody);
    void handleAcccompany(const Json::Value &jBody);
    int checkTripReady();
    void handleTripReady(const Json::Value &inValue);
    void handleTripAbnormal(int errorCode);
    void handleTripStart(const Json::Value &jBody);
    void handleTripPause();
    void handleTripCancel();
    
    void handleModeSet(const Json::Value &jBody);
    void handlePropertiesWrite(const Json::Value &inValue);
    void handlePropertiesRead(const Json::Value &inValue);
    void handleFollowMeStatus(const Json::Value &inValue);
    void handleConnectInfoRequest(const Json::Value &inValue);
    void handlePhoneCall(const Json::Value &jBody);
    void handleUserConnectChange(const Json::Value &jBody,const std::string& topic_name);
    void handleMapDraw(const Json::Value &jBody);
    void handleDataUpdate(const Json::Value &inValue, const Json::Value &jBody);
    void handleMovePoints(const Json::Value &jBody);
    void handleRepositioningTask(const Json::Value& jBody, const std::function<void()>& onSuccess);
    void handlePointReport(const Json::Value &jBody);
    void handleRemindOnTime(const Json::Value &jBody);
    void handleNavigationNotify(const Json::Value &jBody);
    void handleUnbindNotify(const Json::Value &jBody);
    void handleUnbindNotifyVoice(const Json::Value &jBody);
    void handlebindNotify(const Json::Value &jBody);    
    void handleUserInteraction(const Json::Value &jBody);
    void handleFinishTask(const Json::Value &jBody);
    void handleVoiceResponseNluRaw(const Json::Value &jBody);
    int  handleNavigationCheckStatus();
    void handleNavigationRequest(const Json::Value &jBody);
    void handleTripSimpleQuery(const Json::Value &jBody);
    void handleBindStatusQuery();
    void handleBindStatusResponse(const Json::Value &jBody);
    void handleMapReposition(const Json::Value &inValue);
    void handleMapRepositionManual(const Json::Value &inValue);
    void RepositioningTimeout();
    void handleChargingAction(const Json::Value &jBody);
    Json::Value  getGaoDeNavigation(float originLatitude, float originLongitude, float destinationLatitude, float destinationLongitude);
    void actionNavigationResponse(const std::string msg3);
    void handleStripPathReport(const Json::Value &inValue);
    void actionTripAbnormal(int checkResultFail);
    void actionRobdogPose(const Json::Value &params);
    void handleDeviceSettingQuery();
    void handleDeviceSettingResponse(const Json::Value &jBody);
    void handleChargeMark(const Json::Value &jBody);
    void handleGuardDone(const Json::Value &jBody);
    void GuardDone();

    void handleEvent(const std::string &eventType, const Json::Value &jBody,  const Json::Value &value,const std::string &name);
    
    // ************************  接收到服务器数据后的异步回调
    void plat_srv_callback(rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedFuture response);
    // void plat_srv_callback(std::shared_ptr<homi_speech_interface::srv::SIGCData::Request> response);
    void takephoto_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantTakePhoto>::SharedFuture response);
    void net_srv_callback(rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedFuture response);
    void brocast_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedFuture response);

    // ************************  一些发布函数
    void publishVelocity(const geometry_msgs::msg::Twist::SharedPtr velocity); // 发布者功能：发布速度命令
    void publishAction(const homi_speech_interface::msg::RobdogAction::SharedPtr robdogAction);// 发布特定运动信息
    // void publishStatusCtrl(int cmd,int value,int exvalue,std::string::SharedPtr exmsg);
    void publishStatusCtrl(int cmd,int value,int exvalue);
    void publishProperties2APP();    

    // ************************ 处理平台消息的回调函数
    void robctrlCallback(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg,const std::string& topic_name);
    void robctrlCallback_past(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void update_emergency_contacts(int updateType,const std::string& entityId, const Json::Value& emergencyContact) ;
    // ************************ 处理APP消息的回调函数   
    //处理webSocket消息
    void parseWsActionMsg(Json::Value& valueMsg);
    // 休眠处理
    void resetInactivityTimer();
    void handleInactivityTimeout();
    void handleSleepTimeout();
    void activeDog();
    void updateLastActiveTime();
    //**************************告警
    void devAlarmReportCallback(const std_msgs::msg::String::SharedPtr msg);

    int64_t getTripId() { return tripId_; };
    void setTripId(int64_t id) { tripId_ = id; };
    std::string getUserPhone() { return userPhone_; };
    void setUserPhone(const std::string& phone) { userPhone_ = phone; };

    long long  getTripStatus() { return tripStatus_; };
    void setTripStatus(int status) { tripStatus_ = status; };


    std::string getEventId() { return eventId_; };
    void setEventId(const std::string& id) { eventId_ = id; };

    int getDeviceMode() { return deviceMode_; };
    void setDeviceMode(int mode) { deviceMode_ = mode; };

    float getLatitude() { return latitude_; };
    void setLatitude(float lat) { latitude_ = lat; };
    float getLongitude() { return longitude_; };
    void setLongitude(float lon) { longitude_ = lon; };

    void setNavigationPath(const Json::Value& param) { navigationPath_ = param; };
    Json::Value getNavigationPath() const { return navigationPath_; };

    int getInitFinishStatus() { return initFinishStatus_; };
    void setInitFinishStatus(int status){initFinishStatus_ = status;};

    int getBindStatus() { return bindStatus_; };
    void setBindStatus(int status) { bindStatus_ = status; };
    
    int getDistance() { return distance_; };
    void setDistance(int distance) { distance_ = distance; };
    int getDuration() { return duration_; };
    void setDuration(int duration) { duration_ = duration; };
    int getRtkStatus(){ return rtkStatus_;};
    void setRtkStatus(int rtkStatus) { rtkStatus_ = rtkStatus; };
    int getTripDistance() { return tripDistance_; };
    void setTripDistance(int distance) { tripDistance_ = distance; };
    int getTripDuration() { return tripDuration_; };
    void setTripDuration(int duration) { tripDuration_ = duration; };
    
    int getTripStartBattery() { return tripStartBattery_; };
    void setTripStartBattery(int battery) { tripStartBattery_ = battery; };
    
    int getTripErrorCode() { return tripErrorCode_; };
    void setTripErrorCode(int errorCode) { tripErrorCode_ = errorCode; };

    int getTripAvoidStatus() { return tripAvoidCode_; };
    void setTripAvoidStatus(int avoidStatus) { tripAvoidCode_ = avoidStatus; };

    int getCoordReportStatus() { return coordReportStatus_; };
    void setCoordReportStatus(int status) { coordReportStatus_ = status; };

    void handleCoordReportRequest(const Json::Value &inValue);
    
    double haversine(double lat1, double lon1, double lat2, double lon2);
    double calculateRemainTotalDistance(const Json::Value& jsonData);
    std::string getSingleMessage(const std::vector<std::string>& messages);
    void checkInternetConnectStatus(const std_msgs::msg::String::SharedPtr msg);
    void navSatStatusCallback(const sensor_msgs::msg::NavSatFix::SharedPtr msg);
    void tripDynamicReport();
    int  checkDynamic();
    void navTripStatusCallback(const std_msgs::msg::String::SharedPtr msg);
    void check_pos_timeout() ;
    void odom_callback(const nav_msgs::msg::Odometry::SharedPtr msg) ;
    void netMonitorCallback(const std_msgs::msg::String::SharedPtr msg);
    void handle1SecTimeout();
    void handle9SecTimeout();
    void trip_abnormal_monitor();
    unsigned int generateLightCommand(const std::string& light_color, const std::string& light_style);
    void uslamServerLogCallback(const std_msgs::msg::String::SharedPtr msg);
    std::string getStatusDescription(const std::string& input);
    int getCodeForStatus(const std::string& status);
    void handleTraceTrip(const Json::Value &inValue);
    void handleDataReportCtl(const Json::Value &inValue);
    void check_pos_and_temp_status(const std::string& device_id);
    void traceRecordDynamicRep();
    void traceRecordPathRep();
    void handleTracetripNaviRequest(const Json::Value &inValue);
    void checkInternetConflictStatus(const std_msgs::msg::String::SharedPtr msg);    

    int readBindStatusFromConfig();
    std::string trim(const std::string& str);

    void ntripExpireDayCallback(const std_msgs::msg::String::SharedPtr msg);
    void ntripStatusCallback(const std_msgs::msg::Int64::SharedPtr msg);
    void ntripAccountCallback(const std_msgs::msg::String::SharedPtr msg);
    void rtkAccountReportTimerCallback();
    void handleRtkAccountRead(const Json::Value &jBody);

    // WiFi相关处理方法
    void handleWifiListQuery(const Json::Value &inValue, const Json::Value &jBody);
    void handleWifiSet(const Json::Value &inValue, const Json::Value &jBody);
    void handleWifiSetResultQuery(const Json::Value &inValue, const Json::Value &jBody);

    // WiFi查询状态枚举
    enum class WifiQueryStatus {
        IN_PROGRESS = 0,  // 查询进行中
        SUCCESS = 1,      // 查询成功
        FAILED = 2        // 查询失败
    };

    // WiFi数据转换方法
    Json::Value convertServiceResponseToPlatformFormat(const Json::Value &inValue, const std::string &serviceResponse);

    // WiFi响应封装统一函数
    void sendWifiResponse(const Json::Value &inValue, WifiQueryStatus queryStatus, const Json::Value &wifiList = Json::Value(Json::arrayValue));

    void RobotMoveProc(const Json::Value &jBody);
    void RobotViewProc(const Json::Value &jBody);

private:
    RobdogCtrlNode* node_ = nullptr;
    // 步进次数
    int total_count_ = 0;  // 总共发送的次数
    int send_count_ = 0;   // 已发送的次数
    // 智能播报次数
    int brocast_send_count_ = 0;
    int brocast_total_count_ = 0;
    std::string brocast_text; // 当前智能播报的文本
    bool at_target_ = false;    // 设置初始状态为未到达目标
    bool atSinglePoint = false;      // 初始化为 false 表示尚未到达单个点位
    bool moveCancel_ = false;// 设置初始状态为导航任务未被取消
    bool moveCannotArri_ = false;// 设置初始状态为点位可达
    
    bool map_node = false;    // 设置初始状态为建图节点未开启
    // bool isMapping_ = false; // 设置初始状态为不在建图
    std::atomic<int> taskStatusCode{0}; // 设置初始状态为任务状态码为0
    std::atomic<bool> repositioning{false};    // 设置初始状态为非自动重定位过程
    std::atomic<bool> repositioning_manual{false}; // 设置初始状态为非手动重定位过程
    std::atomic<bool> Navigation_node{false};    // 设置初始状态为导航节点未开启
    std::atomic<bool> waiting_for_response{false}; // 设置初始状态为不需等待回复，初始化中收到初始化指令及断连重连时需要超时等待回复
    std::atomic<bool> waiting_for_reconnect{false}; // 设置初始状态为不需等待断连回复
    std::atomic<bool> navHeartbeat{false}; // 设置初始状态为未收到监听档心跳
    std::atomic<bool> repositioningResult{false}; // 设置初始状态为重定位结果为false
    std::atomic<bool> isChargeMarking = false;
    std::atomic<bool> is_charging = false;
    std::mutex resultMutex;
    std::condition_variable resultCV;
    std::chrono::steady_clock::time_point response_start_time;
    std::chrono::steady_clock::time_point lastCancelMovementTime;

    bool is_internet_connected_ = true;//默认设置网络已连接
    bool is_wifi_connected_ = true;
    int current_mode_ = 0;  //默认宅家模式
    bool rtk_status_flag = false;

    bool inactivity_mode_ = true; // true表示当前是亲密互动模式
    bool asked_in_last_hour_ = false; // true表示最近一小时内已经主动求陪伴
    bool quiet_for_three_hours_ = false; // true表示3小时内保持安静，不再主动求陪伴
    std::string uid; // 导航任务下发点位对应id
    geometry_msgs::msg::Twist current_twist_msg_;  // 保存的消息（要传给控制节点的速度【语音控制】）
    homi_speech_interface::msg::ContinueMove current_continue_msg_; // 要传给控制节点的持续移动信息【摇杆控制】
    rclcpp::Time last_heartbeat_time;  // 上次接收心跳的时间
    rclcpp::Time lastMoveMessageTime; // 更新最后收到action消息的时间
    
    rclcpp::Time last_active_time_; // 记录上次活动的时间
    rclcpp::Time last_asking_time_; // 记录上次主动求陪伴的时间
    std::time_t quiet_for_three_hours_until_;//用于判断距离上次主动求陪伴是否已超过3小时

    std::shared_ptr<AudioPlayer> audioCtrl;
    std::chrono::time_point<std::chrono::system_clock> tripStartTime_;

    int64_t tripId_ = 0; // 用于记录行程id
    std::string userPhone_; // 用于记录用户电话号码
    int tripStatus_ = 0; // 用于记录行程状态

    std::string eventId_; // 用于记录事件id

    bool rtkAccountStatusReport = true;
    std::string rtkAccount = "";
    std::string rtkPass = "";
    int64_t rtkAccountStatus = 1000;
    std::string rtkAccountExipreDay = "";

    int deviceMode_ = 0;

    float latitude_ = 0;
    float longitude_ = 0;
    // float destinationlatitude = 0;
    // float destinationlongitude = 0;

    Json::Value navigationPath_ = Json::Value(Json::arrayValue);

    int initFinishStatus_ = 0;

    int bindStatus_ = -1;
    
    int distance_ = 0; // 剩余距离
    int duration_ = 0; // 剩余时长
                       // 
    int tripDistance_ = 0; // 行程距离
    int tripDuration_ = 0; // 行程时长
                           // 
    int tripStartBattery_ = 0; // 使用电量
                               // 
    int tripErrorCode_ = 0; // 用于记录行程错误码
    int tripAvoidCode_=0; //避障状态
    int coordReportStatus_ = 0;
    int counterFreq=0;
    // ************************ 用到的yaml的数据
    double fspeed_x = DF_FSPEED_X;
    double fspeed_y = DF_FSPEED_Y;
    double fspeed_z = DF_FSPEED_Z; // 大约是15度
    int timer_interval = 1;
    double resting_time = 0.5;
    bool  move_status_flag = false;
    int  expresstion_count = 0;
    bool watchDogMonitor=false; // 启动监测
    int rtkStatus_;
    /********自主出行位置相关******/
    nav_msgs::msg::Odometry latest_odom_;
    geometry_msgs::msg::Point ref_position_;
    bool has_new_data_ = false;
    using Clock = std::chrono::steady_clock;  // 使用稳定时钟（不受系统时间调整影响）
    std::chrono::time_point<Clock> last_move_time_=Clock::now();  // 替换 rclcpp::Time
    int prev_trip_status_=STOP;
    std::string robotdog_file_path ;
    std::string map_points_path ;

    std::map<std::string,int> sportModeMap={
        {"walk",DEEP_CMD_ACTION_WALK},
        {"run",DEEP_CMD_ACTION_RUN},
        {"stairClimbe",DEEP_CMD_ACTION_STAIR},
        {"climbe",DEEP_CMD_ACTION_CLIMBE},
        {"traction",DEEP_CMD_ACTION_WALK},
        {"emergencyStop",DEEP_CMD_ACTION_SOFTSTOP},
        {"AIClassic",DEEP_CMD_ACTION_CLIMBE},
        {"AINimble",DEEP_CMD_ACTION_CLIMBE},
        {"jumpRun",DEEP_CMD_ACTION_CLIMBE},
        {"runSide",DEEP_CMD_ACTION_CLIMBE}
    };  //平台运动模式字段

    // 动作映射
    std::map<std::string, std::string> actionMap = {
        {"standUp", "standUp"},
        {"getDown", "getDown"},
        {"greeting", "greeting"},
        {"twistBody", "twistBody"},
        {"backflip", "backflip"},
        {"jumpForward", "jumpForward"},
        {"turnOver", "turnOver"},
        {"twistJump", "twistJump"},
        {"sitDown", "sitDown"},
        {"fingerHeart", "fingerHeart"},
        {"makeBow", "makeBow"},
        {"dance", "dance"},
        {"shakeBody", "shakeBody"},
        {"twistAss", "twistAss"},
        {"twistBody_emergency", "twistBody_emergency"},
        {"greeting_emergency", "greeting_emergency"},
        {"stretch","stretch"},
        {"chestOut","chestOut"},
        {"newYearCall","newYearCall"},
        {"fastShakeBody","fastShakeBody"},
        {"happy","happy"},
        {"leap","leap"},
        {"danceV2","danceV2"},
        {"pose","pose"},
        {"walkUpsideDown","walkUpsideDown"},
        {"standErect","standErect"}
    };

    std::vector<std::string> actionVector = {
        "standUp",
        "getDown",
        "greeting",
        "twistBody",
        "backflip",
        "jumpForward",
        "turnOver",
        "twistJump",
        "sitDown",
        "fingerHeart",
        "makeBow",
        "dance",
        "shakeBody",
        "twistAss",
        "twistBody_emergency",
        "greeting_emergency"
    };
    sensor_msgs::msg::NavSatFix rtkMsg;
    // 使用 std::map 来关联动作和时间
    std::map<std::string, RobotActionTime> action_times = {
        {"standUp", ACTION_STAND_UP},
        {"getDown", ACTION_GET_DOWN},
        {"greeting", ACTION_GREETING},
        {"twistBody", ACTION_TWIST_BODY},
        {"dance", ACTION_DANCE},
        {"twistAss", ACTION_TWIST_ASS},
        {"shakeBody", ACTION_SHAKE_BODY},
        {"chestOut", ACTION_CHEST_OUT},
        {"stretch", ACTION_STRETCH},
        {"fastShakeBody", ACTION_FAST_SHAKE_BODY},
        {"sitDown", ACTION_SIT_DOWN},
        {"newYearCall", ACTION_NEW_YEAR_CALL},
        {"fingerHeart", ACTION_FINGER_HEART}
    };
    // 燈光映射
    std::map<std::string, int> colorMap = {
        {"white", 0x04},
        {"orange", 0x02},
        {"pink", 0x03},
        {"yellow", 0x01},
        {"blue", 0x05}
    };

    std::map<std::string, int> styleMap = {
        {"keepAwake", 0x01},  // 常亮
        {"blinking", 0x02},   // 闪烁
        {"breathing", 0x03},  // 呼吸灯
        {"running", 0x04}     // 流水灯
    };
    // 订阅器
    rclcpp::Subscription<homi_speech_interface::msg::SIGCEvent>::SharedPtr platCmd_sub_;
    rclcpp::Subscription<homi_speech_interface::msg::SIGCEvent>::SharedPtr app_sub;
    rclcpp::Subscription<homi_speech_interface::msg::ProprietySet>::SharedPtr deepCtrl_sub_;
    rclcpp::Subscription<geometry_msgs::msg::Pose>::SharedPtr navPosition_sub_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr navStatus_sub_;
    rclcpp::Subscription<homi_speech_interface::msg::AssistantEvent>::SharedPtr brocast_sub;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr targetPose_sub;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr devAlarmRep_sub_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr internet_connect_status_sub_;
    rclcpp::Subscription<sensor_msgs::msg::NavSatFix>::SharedPtr rtk_status_sub_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr navTripStatus_sub_; //出行时避障服务状态
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr odom_sub_;//odom里程计订阅
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr net_sub_ ;//网络状态监测
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr unitreeServerLog_sub_;
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr unitree_odom_sub_;//unitree_odom_sub_里程计订阅
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr point_transform_result_sub_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr internet_conflict_sub_;

    // rtk Account
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr ntrip_expire_day_sub_;
    rclcpp::Subscription<std_msgs::msg::Int64>::SharedPtr ntrip_status_sub_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr ntrip_account_sub_;
    rclcpp::TimerBase::SharedPtr rtkAccountReportTimer_;

    // 发布器
    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr velCmd_pub;
    rclcpp::Publisher<homi_speech_interface::msg::RobdogAction>::SharedPtr actionCmd_pub;
    rclcpp::Publisher<homi_speech_interface::msg::ContinueMove>::SharedPtr continueMoveCmd_pub;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr actionPlanningMove_pub;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr mappingControl_pub;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr publishVirtualWall;
    rclcpp::Publisher<homi_speech_interface::msg::ProperToApp>::SharedPtr prope2app_pub;
    rclcpp::Publisher<homi_speech_interface::msg::ProprietySet>::SharedPtr status_pub_;
    rclcpp::Publisher<homi_speech_interface::msg::Wakeup>::SharedPtr wake_pub_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr developer_mode_pub_;
    rclcpp::Publisher<homi_speech_interface::msg::LiveStreamTask>::SharedPtr  liveStreamTask_pub;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr client_command_publisher_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr unitreeCmdClient_pub_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr follow_control_pub_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr adjust_distance_publisher_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr point_transform_publisher_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr flashlight_control_pub_;

    // 服务器
    rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedPtr platform_client;
    rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedPtr app_client;
    rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedPtr net_client;
    rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedPtr brocast_client;
    rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedPtr endTask_client;
    rclcpp::Client<homi_speech_interface::srv::AssistantAbort>::SharedPtr brocast_abort_client;
    rclcpp::Client<homi_speech_interface::srv::SetWakeEvent>::SharedPtr set_wake_client;
    rclcpp::Client<homi_speech_interface::srv::IotControl>::SharedPtr iot_control_client_;
    rclcpp::Client<homi_speech_interface::srv::AssistantTakePhoto>::SharedPtr take_photo_client_;
    rclcpp::Client<std_srvs::srv::Trigger>::SharedPtr nvidiaService_client;
    rclcpp::Client<homi_speech_interface::srv::SetDiyWord>::SharedPtr set_diyWakeup_client_;
    rclcpp::Client<homi_speech_interface::srv::PhoneCall>::SharedPtr phone_call_client_;
    rclcpp::Client<homi_speech_interface::srv::NtripAccount>::SharedPtr ntrip_account_client_;

    // 定时器
    rclcpp::TimerBase::SharedPtr ws_heartbeat_timer_;
    rclcpp::TimerBase::SharedPtr init_timer_;
    rclcpp::TimerBase::SharedPtr internet_timer_;
    rclcpp::TimerBase::SharedPtr timer_2;
    rclcpp::TimerBase::SharedPtr timer_;
    rclcpp::TimerBase::SharedPtr timer_robMove;
    rclcpp::TimerBase::SharedPtr robPoseStatusTimer_;
    rclcpp::TimerBase::SharedPtr timer_brocast;
    rclcpp::TimerBase::SharedPtr timerDog;
    rclcpp::TimerBase::SharedPtr robMoveStatusTimer_;
    rclcpp::TimerBase::SharedPtr robMoveStatusTimer_brocast;
    rclcpp::TimerBase::SharedPtr robActionCheckTimer_;
    rclcpp::TimerBase::SharedPtr robPatrolStatusTimer_;
    rclcpp::TimerBase::SharedPtr robPathStatusTimer_;
    rclcpp::TimerBase::SharedPtr inactivity_timer_; // 10分钟定时器，用于检测是否需要主动求陪伴
    rclcpp::TimerBase::SharedPtr sleep_timer_;       // 5分钟定时器，用于检测是否进入休眠状态
    rclcpp::TimerBase::SharedPtr trip_timer_;       // 自主出行的定时器
    rclcpp::TimerBase::SharedPtr robActionSuspendTimer_; // 间隔固定的时延
    rclcpp::TimerBase::SharedPtr utStateTimer_;
    rclcpp::TimerBase::SharedPtr check_pos_timer_;  //位置监测定时器
    rclcpp::TimerBase::SharedPtr abnormal_monitor_trip;  //出行异常监测定时器
    rclcpp::TimerBase::SharedPtr pos_tem_rep_timer_;//定位、温度上报
    rclcpp::TimerBase::SharedPtr repositioning_timer_; 

    NetState net_state_ = NetState::IDLE;
    MonitorStatus status_;
    std::unordered_set<std::string> active_types_;

    std::mutex net_state_mutex_;
    rclcpp::TimerBase::SharedPtr timer_1sec_;
    rclcpp::TimerBase::SharedPtr timer_9sec_;

    int unitreeMappingStatus = -1;
    int unitreeNavigationStatus = -1;
    int unitreeLocalizationStatus = -1;
    std::string unitreeUsingMapId = "";


};
