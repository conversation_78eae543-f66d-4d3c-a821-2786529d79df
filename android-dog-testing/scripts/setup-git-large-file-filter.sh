#!/bin/bash

# Git大文件过滤器配置脚本
# 功能：配置Git仓库忽略大于50M的文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置参数
MAX_FILE_SIZE="50M"
MAX_FILE_SIZE_BYTES=52428800  # 50MB in bytes

echo -e "${BLUE}=== Git大文件过滤器配置脚本 ===${NC}"
echo -e "${YELLOW}最大文件大小限制: ${MAX_FILE_SIZE}${NC}"
echo ""

# 检查是否在Git仓库中
check_git_repo() {
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        echo -e "${RED}错误: 当前目录不是Git仓库${NC}"
        echo "请在Git仓库根目录下运行此脚本"
        exit 1
    fi
    echo -e "${GREEN}✓ 检测到Git仓库${NC}"
}

# 创建pre-commit钩子
create_pre_commit_hook() {
    local hooks_dir=".git/hooks"
    local pre_commit_file="${hooks_dir}/pre-commit"
    
    echo -e "${BLUE}创建pre-commit钩子...${NC}"
    
    # 确保hooks目录存在
    mkdir -p "${hooks_dir}"
    
    # 创建pre-commit钩子脚本
    cat > "${pre_commit_file}" << 'EOF'
#!/bin/bash

# Git pre-commit钩子：检查文件大小
# 阻止提交大于50MB的文件

MAX_SIZE=52428800  # 50MB in bytes
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo "检查文件大小..."

# 获取即将提交的文件列表
files=$(git diff --cached --name-only --diff-filter=ACM)

large_files=()
total_large_files=0

for file in $files; do
    if [ -f "$file" ]; then
        file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo 0)
        if [ "$file_size" -gt "$MAX_SIZE" ]; then
            large_files+=("$file")
            file_size_mb=$(echo "scale=2; $file_size / 1024 / 1024" | bc -l 2>/dev/null || echo "$(($file_size / 1024 / 1024))")
            echo -e "${RED}✗ 文件过大: $file (${file_size_mb}MB)${NC}"
            total_large_files=$((total_large_files + 1))
        fi
    fi
done

if [ $total_large_files -gt 0 ]; then
    echo ""
    echo -e "${RED}发现 $total_large_files 个大于50MB的文件，提交被阻止！${NC}"
    echo ""
    echo -e "${YELLOW}解决方案:${NC}"
    echo "1. 将大文件添加到 .gitignore"
    echo "2. 使用 Git LFS 管理大文件"
    echo "3. 压缩或分割文件"
    echo "4. 移除大文件后重新提交"
    echo ""
    echo -e "${YELLOW}如需强制提交，使用: git commit --no-verify${NC}"
    exit 1
fi

echo "✓ 所有文件大小检查通过"
exit 0
EOF

    # 设置执行权限
    chmod +x "${pre_commit_file}"
    echo -e "${GREEN}✓ pre-commit钩子创建成功${NC}"
}

# 创建大文件检查脚本
create_check_script() {
    local script_file="scripts/check-large-files.sh"
    
    echo -e "${BLUE}创建大文件检查脚本...${NC}"
    
    # 确保scripts目录存在
    mkdir -p "scripts"
    
    cat > "${script_file}" << 'EOF'
#!/bin/bash

# 检查仓库中的大文件脚本

MAX_SIZE=52428800  # 50MB in bytes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== 检查仓库中的大文件 ===${NC}"
echo "最大文件大小限制: 50MB"
echo ""

large_files_found=0

# 检查工作目录中的所有文件
while IFS= read -r -d '' file; do
    if [ -f "$file" ]; then
        file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo 0)
        if [ "$file_size" -gt "$MAX_SIZE" ]; then
            file_size_mb=$(echo "scale=2; $file_size / 1024 / 1024" | bc -l 2>/dev/null || echo "$(($file_size / 1024 / 1024))")
            echo -e "${RED}✗ 大文件: $file (${file_size_mb}MB)${NC}"
            large_files_found=$((large_files_found + 1))
        fi
    fi
done < <(find . -type f -not -path './.git/*' -print0)

if [ $large_files_found -eq 0 ]; then
    echo -e "${GREEN}✓ 未发现大于50MB的文件${NC}"
else
    echo ""
    echo -e "${YELLOW}发现 $large_files_found 个大文件${NC}"
    echo ""
    echo -e "${YELLOW}建议操作:${NC}"
    echo "1. 将大文件添加到 .gitignore"
    echo "2. 使用 Git LFS 管理大文件"
    echo "3. 压缩或分割文件"
fi
EOF

    chmod +x "${script_file}"
    echo -e "${GREEN}✓ 大文件检查脚本创建成功: ${script_file}${NC}"
}

# 更新.gitignore文件
update_gitignore() {
    echo -e "${BLUE}更新.gitignore文件...${NC}"
    
    # 检查.gitignore是否存在大文件规则
    if ! grep -q "# 大文件过滤规则" .gitignore 2>/dev/null; then
        cat >> .gitignore << 'EOF'

# 大文件过滤规则 (>50MB)
# 常见大文件类型
*.zip
*.tar.gz
*.tar.bz2
*.rar
*.7z
*.iso
*.dmg
*.pkg
*.deb
*.rpm

# 视频文件
*.mp4
*.avi
*.mkv
*.mov
*.wmv
*.flv
*.webm

# 音频文件
*.mp3
*.wav
*.flac
*.aac
*.ogg

# 数据库文件
*.db
*.sqlite
*.mdb

# 日志文件
*.log

# 临时文件
*.tmp
*.temp
*.cache

# 编译产物
*.o
*.so
*.dylib
*.dll
*.exe

# 备份文件
*.bak
*.backup
EOF
        echo -e "${GREEN}✓ .gitignore文件已更新${NC}"
    else
        echo -e "${YELLOW}! .gitignore文件已包含大文件规则${NC}"
    fi
}

# 创建Git配置
setup_git_config() {
    echo -e "${BLUE}配置Git设置...${NC}"
    
    # 设置文件大小警告
    git config core.bigFileThreshold 50m
    
    echo -e "${GREEN}✓ Git配置完成${NC}"
}

# 创建清理脚本
create_cleanup_script() {
    local script_file="scripts/cleanup-large-files.sh"
    
    echo -e "${BLUE}创建大文件清理脚本...${NC}"
    
    cat > "${script_file}" << 'EOF'
#!/bin/bash

# 清理Git历史中的大文件

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Git大文件清理工具 ===${NC}"
echo -e "${RED}警告: 此操作会重写Git历史，请确保已备份！${NC}"
echo ""

# 查找历史中的大文件
echo -e "${BLUE}查找历史中的大文件...${NC}"
git rev-list --objects --all | \
git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | \
awk '/^blob/ {print substr($0,6)}' | \
sort --numeric-sort --key=2 | \
tail -20 | \
while read size name; do
    if [ "$size" -gt 52428800 ]; then  # 50MB
        size_mb=$(echo "scale=2; $size / 1024 / 1024" | bc -l)
        echo -e "${RED}大文件: $name (${size_mb}MB)${NC}"
    fi
done

echo ""
echo -e "${YELLOW}如需清理大文件，请手动使用以下命令:${NC}"
echo "git filter-branch --tree-filter 'rm -f path/to/large/file' HEAD"
echo "git for-each-ref --format='delete %(refname)' refs/original | git update-ref --stdin"
echo "git reflog expire --expire=now --all"
echo "git gc --prune=now"
EOF

    chmod +x "${script_file}"
    echo -e "${GREEN}✓ 大文件清理脚本创建成功: ${script_file}${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}开始配置Git大文件过滤器...${NC}"
    echo ""
    
    # 检查Git仓库
    check_git_repo
    
    # 创建各种脚本和配置
    create_pre_commit_hook
    create_check_script
    update_gitignore
    setup_git_config
    create_cleanup_script
    
    echo ""
    echo -e "${GREEN}=== 配置完成 ===${NC}"
    echo ""
    echo -e "${YELLOW}已创建的文件:${NC}"
    echo "• .git/hooks/pre-commit - 提交前检查钩子"
    echo "• scripts/check-large-files.sh - 大文件检查脚本"
    echo "• scripts/cleanup-large-files.sh - 大文件清理脚本"
    echo "• .gitignore - 更新了大文件过滤规则"
    echo ""
    echo -e "${YELLOW}使用方法:${NC}"
    echo "• 运行检查: ./scripts/check-large-files.sh"
    echo "• 正常提交时会自动检查文件大小"
    echo "• 强制提交: git commit --no-verify"
    echo ""
    echo -e "${GREEN}✓ Git仓库已配置为忽略大于50MB的文件${NC}"
}

# 运行主函数
main "$@"
