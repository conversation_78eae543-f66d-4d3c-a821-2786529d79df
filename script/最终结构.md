# 最终目录结构

## 📁 精简后的目录结构

```
script/
├── auto_install/          # 🚀 自动化安装脚本 (主入口)
├── dev/                   # 💻 开发环境
│   ├── docker/           # 🐳 Docker相关
│   ├── git/              # 📝 Git工具
│   ├── go/               # 🔷 Go语言
│   ├── ros/              # 🤖 ROS系统
│   └── vim/              # ✏️ 编辑器配置
├── config/                # ⚙️ 系统配置
│   ├── ubuntu/           # 🐧 Ubuntu配置
│   ├── ssh/              # 🔐 SSH配置
│   ├── net/              # 🌐 网络配置
│   ├── wsl/              # 🪟 WSL配置
│   ├── system/           # 🔧 系统脚本
│   └── hardware/         # 🔧 硬件相关
├── services/              # 🌐 网络服务
│   ├── network/          # 代理和VPN
│   └── ddns/             # 动态DNS
├── tools/                 # 🛠️ 工具集合
│   ├── terminal/         # 💻 终端工具
│   ├── software/         # 💾 软件安装
│   ├── ffmpeg/           # 🎬 多媒体
│   ├── rsync/            # 🔄 同步工具
│   └── fonts/            # 🔤 字体文件
├── robot/                 # 🤖 机器人开发
├── zsh/                   # 🐚 Zsh配置
└── scripts/               # 📜 独立脚本
```

## 🎯 目录功能说明

### 🚀 auto_install/ - 主入口
所有自动化安装脚本的集合，这是主要的使用入口。

### 💻 dev/ - 开发环境
所有开发相关的工具和配置，按语言和工具分类。

### ⚙️ config/ - 系统配置
系统级别的配置文件和脚本，包括Ubuntu、SSH、网络等。

### 🌐 services/ - 网络服务
网络相关的服务和工具，如代理、DDNS等。

### 🛠️ tools/ - 工具集合
各种实用工具，包括终端、软件、多媒体处理等。

### 🤖 robot/ - 机器人开发
专门的机器人开发脚本和配置。

### 🐚 zsh/ - Shell配置
Zsh shell的配置文件。

### 📜 scripts/ - 独立脚本
不属于特定分类的独立脚本。

## 🚀 使用指南

### 快速开始
```bash
cd auto_install
./ubuntu_auto_setup.sh
```

### 开发环境
```bash
cd dev/docker    # Docker相关
cd dev/git       # Git工具
cd dev/go        # Go开发
```

### 系统配置
```bash
cd config/ubuntu # Ubuntu配置
cd config/ssh    # SSH配置
```

### 工具使用
```bash
cd tools/terminal    # 终端工具
cd tools/software    # 软件安装
```

## 📊 优化效果

- **根目录文件夹**: 从 20+ 减少到 8 个
- **分类更清晰**: 按功能逻辑分组
- **查找更容易**: 目录名称直观明了
- **维护更简单**: 结构层次分明
