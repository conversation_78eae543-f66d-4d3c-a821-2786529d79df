# Ubuntu自动化配置脚本集合

这个仓库包含了完整的Ubuntu系统自动化配置和开发环境安装脚本。经过精心整理，现在拥有清晰的8个主要目录结构。

## 📁 精简后的目录结构

```
script/
├── auto_install/          # 🚀 自动化安装脚本 (主入口)
├── dev/                   # 💻 开发环境
│   ├── docker/           # 🐳 Docker相关
│   ├── git/              # 📝 Git工具
│   ├── go/               # 🔷 Go语言
│   ├── ros/              # 🤖 ROS系统
│   └── vim/              # ✏️ 编辑器配置
├── config/                # ⚙️ 系统配置
│   ├── ubuntu/           # 🐧 Ubuntu配置
│   ├── ssh/              # 🔐 SSH配置
│   ├── net/              # 🌐 网络配置
│   ├── wsl/              # 🪟 WSL配置
│   ├── system/           # 🔧 系统脚本
│   └── hardware/         # 🔧 硬件相关
├── services/              # 🌐 网络服务
│   ├── network/          # 代理和VPN
│   └── ddns/             # 动态DNS
├── tools/                 # 🛠️ 工具集合
│   ├── terminal/         # 💻 终端工具
│   ├── software/         # 💾 软件安装
│   ├── ffmpeg/           # 🎬 多媒体
│   ├── rsync/            # 🔄 同步工具
│   └── fonts/            # 🔤 字体文件
├── robot/                 # 🤖 机器人开发
├── zsh/                   # 🐚 Zsh配置
└── scripts/               # 📜 独立脚本
```

## 🚀 快速开始

### 1. 完整系统配置（推荐）

```bash
cd auto_install
chmod +x ubuntu_auto_setup.sh
./ubuntu_auto_setup.sh
```

### 2. 快速开发环境

```bash
cd auto_install
chmod +x quick_dev_setup.sh
./quick_dev_setup.sh
```

### 3. 高级配置（支持配置文件）

```bash
cd auto_install
chmod +x ubuntu_auto_setup_advanced.sh
./ubuntu_auto_setup_advanced.sh --help
```

## 🔧 问题修复

如果遇到shell配置问题（zsh启动错误等）：

```bash
cd auto_install
./fix_shell_config.sh
./verify_zsh_fix.sh
```

## 📋 主要功能

### ✅ 系统配置
- 系统更新和基础软件包安装
- 中文环境配置
- 用户权限和安全配置
- 防火墙和SSH配置

### ✅ 开发环境
- **Zsh + Oh My Zsh** - 增强的shell环境
- **Docker** - 容器化平台
- **Node.js** - JavaScript运行时
- **Python** - Python开发环境
- **Rust** - Rust编程语言
- **Go** - Go语言环境
- **Neovim + LazyVim** - 现代化编辑器

### ✅ 终端工具
- **fzf** - 模糊搜索工具
- **autojump** - 智能目录跳转
- **x-cmd** - 命令行工具集
- **zellij** - 终端复用器
- **fd, rg, bat** - 现代化命令行工具

### ✅ 配置优化
- Git配置优化
- Shell配置修复
- 自动化配置清理
- 错误处理和恢复

## 🎯 目录功能说明

### 🚀 auto_install/ - 主入口
所有自动化安装脚本的集合，这是主要的使用入口。

### 💻 dev/ - 开发环境
所有开发相关的工具和配置，按语言和工具分类。

### ⚙️ config/ - 系统配置
系统级别的配置文件和脚本，包括Ubuntu、SSH、网络等。

### 🌐 services/ - 网络服务
网络相关的服务和工具，如代理、DDNS等。

### 🛠️ tools/ - 工具集合
各种实用工具，包括终端、软件、多媒体处理等。

### 🤖 robot/ - 机器人开发
专门的机器人开发脚本和配置。

### 🐚 zsh/ - Shell配置
Zsh shell的配置文件。

### 📜 scripts/ - 独立脚本
不属于特定分类的独立脚本。

## 📖 使用说明

### 新用户推荐流程

1. **克隆仓库**
```bash
git clone <repository-url>
cd script
```

2. **运行基础安装**
```bash
cd auto_install
./ubuntu_auto_setup.sh
```

3. **验证安装**
```bash
./verify_zsh_fix.sh
./test_shell_tools.sh
```

4. **重新登录或重启**
```bash
# 使配置生效
sudo reboot
```

### 特定功能使用

```bash
# 开发环境
cd dev/docker    # Docker相关
cd dev/git       # Git工具
cd dev/go        # Go开发

# 系统配置
cd config/ubuntu # Ubuntu配置
cd config/ssh    # SSH配置

# 工具使用
cd tools/terminal    # 终端工具
cd tools/software    # 软件安装

# 网络服务
cd services/network  # 网络代理
cd services/ddns     # 动态DNS
```

## 🔍 故障排除

### 常见问题

1. **Shell配置错误**
   ```bash
   cd auto_install
   ./fix_shell_config.sh
   ```

2. **权限问题**
   ```bash
   sudo usermod -aG sudo $USER
   ```

3. **网络问题**
   ```bash
   ping -c 4 *******
   ```

### 获取帮助

- 查看详细文档：`auto_install/README_Ubuntu_Setup.md`
- Shell问题修复：`auto_install/SHELL_CONFIG_FIXES.md`
- 运行诊断工具：`auto_install/test_shell_tools.sh`
- 目录结构说明：`FINAL_STRUCTURE.md`
- 整理报告：`FINAL_CLEANUP_REPORT.md`

## 🎉 支持的系统

- Ubuntu 18.04 LTS
- Ubuntu 20.04 LTS  
- Ubuntu 22.04 LTS
- Ubuntu 24.04 LTS

## 📊 优化效果

- **根目录文件夹**: 从 20+ 减少到 8 个 (减少60%)
- **分类更清晰**: 按功能逻辑分组
- **查找更容易**: 目录名称直观明了
- **维护更简单**: 结构层次分明

## 📝 更新日志

### 最新更新 (2025-07-12)
- ✅ 完成两轮深度文件整理
- ✅ 根目录从20+个精简到8个
- ✅ 按功能重新分类所有文件
- ✅ 修复了所有shell配置冲突问题
- ✅ 创建完整的文档体系
- ✅ 建立安全的备份机制

### 主要改进
- 🔧 Shell配置问题自动修复
- 📁 脚本分类和目录整理
- 🛡️ 安全的配置管理
- 📊 详细的日志和诊断
- 🎯 用户友好的错误提示

---

**开始使用：** `cd auto_install && ./ubuntu_auto_setup.sh`

**获取帮助：** 查看 `auto_install/README.md` 和 `FINAL_STRUCTURE.md`
