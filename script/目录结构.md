# 目录结构说明

## 📁 整理后的目录结构

```
script/
├── auto_install/          # 🚀 自动化安装脚本
├── zsh/                   # 🐚 Zsh配置
├── ssh/                   # 🔐 SSH配置
├── robot/                 # 🤖 机器人相关脚本
├── docker/                # 🐳 Docker相关脚本
├── git/                   # 📝 Git相关脚本
├── go/                    # 🔷 Go语言相关
├── ros/                   # 🤖 ROS相关脚本
├── ubuntu/                # 🐧 Ubuntu系统配置
├── vim/                   # ✏️ Vim/Neovim配置
├── software/              # 💾 软件安装脚本
├── network/               # 🌐 网络相关脚本
├── system/                # ⚙️ 系统配置脚本
├── hardware/              # 🔧 硬件相关脚本
├── Windows-Fonts/         # 🔤 Windows字体
├── windterm/              # 💻 WindTerm终端
├── alacritty/             # 💻 Alacritty终端
├── ddns/                  # 🌐 DDNS配置
├── net/                   # 🌐 网络配置
├── rsync/                 # 🔄 同步脚本
├── ffmpeg/                # 🎬 多媒体处理
├── wsl/                   # 🪟 WSL相关
└── syncthing.sh           # 🔄 文件同步
```

## 📋 主要改进

1. **分类整理** - 按功能分类所有脚本
2. **去重合并** - 删除重复文件，合并相同内容
3. **备份保护** - 重要文件已备份到backup目录
4. **结构清晰** - 目录层次分明，易于查找

## 🚀 使用建议

- **自动化安装**: 使用 `auto_install/` 目录中的脚本
- **特定功能**: 到对应的功能目录查找脚本
- **备份恢复**: 如需恢复，查看backup目录
