{"name": "精简", "settings": "{\"settings\":\"{\\n  \\\"vscode-edge-devtools.webhint\\\": false,\\n  \\\"git.enableSmartCommit\\\": true,\\n  //     // \\\"editor.fontFamily\\\": \\\"微软雅黑\\\",\\n  \\\"editor.fontFamily\\\": \\\"Hack Nerd Font Mono\\\",\\n  \\\"editor.fontWeight\\\": \\\"550\\\",\\n  \\\"editor.mouseWheelZoom\\\": true,\\n  \\\"terminal.integrated.fontWeight\\\": \\\"400\\\",\\n  \\\"terminal.integrated.fontWeightBold\\\": \\\"700\\\",\\n  \\\"window.title\\\": \\\"${dirty}${activeEditorShort}${separator}${separator}${remoteName}${rootPath}${separator}${separator}${rootName}\\\",\\n  //     // \\\"editor.wordWrap\\\": \\\"on\\\",\\n  \\\"vim.easymotionDimBackground\\\": false,\\n  \\\"vim.easymotion\\\": true,\\n  \\\"vim.incsearch\\\": true,\\n  \\\"vim.hlsearch\\\": true,\\n  \\\"vim.flash.enable\\\": true,\\n  \\\"vim.insertModeKeyBindings\\\": [\\n    {\\n      \\\"before\\\": [\\\"j\\\", \\\"j\\\"],\\n      \\\"after\\\": [\\\"<Esc>\\\"]\\n    }\\n  ],\\n  \\\"vim.normalModeKeyBindingsNonRecursive\\\": [\\n    {\\n      \\\"before\\\": [\\\"<leader>\\\", \\\"d\\\"],\\n      \\\"after\\\": [\\\"d\\\", \\\"d\\\"]\\n    },\\n    {\\n      \\\"before\\\": [\\\"<C-n>\\\"],\\n      \\\"commands\\\": [\\\":nohl\\\"]\\n    },\\n    {\\n      // 取消最近的撤销\\n      \\\"before\\\": [\\\"<leader>\\\", \\\"u\\\"],\\n      \\\"after\\\": [\\\"<C-r>\\\"]\\n    },\\n    {\\n      \\\"before\\\": [\\\"K\\\"],\\n      \\\"commands\\\": [\\\"lineBreakInsert\\\"],\\n      \\\"silent\\\": true\\n    }\\n  ],\\n  \\\"vim.leader\\\": \\\"<space>\\\",\\n  \\\"vim.handleKeys\\\": {\\n    \\\"<C-x>\\\": false,\\n    \\\"<C-c>\\\": false,\\n    \\\"<C-v>\\\": false,\\n    \\\"<C-a>\\\": false,\\n    \\\"<C-k>\\\": false,\\n    \\\"<C-z>\\\": false,\\n    \\\"<C-y>\\\": false,\\n    \\\"<C-p>\\\": false,\\n    // \\\"<C-f>\\\": false,\\n    \\\"<C-,>\\\": false\\n  },\\n  \\\"tabnine.experimentalAutoImports\\\": true,\\n  \\\"editor.insertSpaces\\\": false,\\n  \\\"clangd.detectExtensionConflicts\\\": false,\\n  \\\"search.actionsPosition\\\": \\\"auto\\\",\\n  \\\"search.collapseResults\\\": \\\"auto\\\",\\n  \\\"search.defaultViewMode\\\": \\\"tree\\\",\\n  \\\"search.experimental.notebookSearch\\\": true,\\n  \\\"search.searchEditor.doubleClickBehaviour\\\": \\\"openLocationToSide\\\",\\n  \\\"search.searchEditor.reusePriorSearchConfiguration\\\": true,\\n  \\\"search.showLineNumbers\\\": true,\\n  \\\"terminal.external.linuxExec\\\": \\\"\\\",\\n  \\\"terminal.integrated.copyOnSelection\\\": true,\\n  // \\\"terminal.integrated.cursorBlinking\\\": true,\\n  \\\"terminal.integrated.defaultProfile.linux\\\": \\\"zsh (2)\\\",\\n  // \\\"terminal.integrated.rightClickBehavior\\\": \\\"copyPaste\\\",\\n  \\\"terminal.integrated.scrollback\\\": 10000000,\\n  \\\"terminal.integrated.shellIntegration.history\\\": 9999,\\n  \\\"terminal.integrated.shellIntegration.suggestEnabled\\\": true,\\n  \\\"editor.glyphMargin\\\": true,\\n  // \\\"editor.foldingImportsByDefault\\\": true,\\n  \\\"editor.unfoldOnClickAfterEndOfLine\\\": true,\\n  \\\"debug.allowBreakpointsEverywhere\\\": true,\\n  \\\"debug.console.closeOnEnd\\\": true,\\n  \\\"debug.openExplorerOnEnd\\\": true,\\n  // \\\"workbench.colorCustomizations\\\": {\\n  //     \\\"editor.selectionBackground\\\": \\\"#f54813\\\",\\n  //     \\\"editor.selectionHighlightBackground\\\": \\\"#139bf5\\\",\\n  //     \\\"statusBar.noFolderBackground\\\": \\\"#5f00af\\\",\\n  //     \\\"statusBar.debuggingBackground\\\": \\\"#5f00af\\\",\\n  //     \\\"statusBar.debuggingForeground\\\": \\\"#ffffff\\\",\\n  //     \\\"statusBar.background\\\": \\\"#5f00af\\\",\\n  //     \\\"statusBar.foreground\\\": \\\"#ffffff\\\"\\n  // },\\n  \\\"extensions.ignoreRecommendations\\\": true,\\n  // \\\"debug.onTaskErrors\\\": \\\"showErrors\\\",\\n  // \\\"debug.showBreakpointsInOverviewRuler\\\": true,\\n  \\\"highlightLine.borderColor\\\": \\\"#65EAB9\\\",\\n  \\\"highlightLine.borderWidth\\\": \\\"1px\\\",\\n  // \\\"vscodeGoogleTranslate.preferredLanguage\\\": \\\"Chinese (Simplified)\\\",\\n  \\\"git.inputValidationSubjectLength\\\": 10000,\\n  \\\"gnuGlobal.debugMode\\\": \\\"Enabled\\\",\\n  /*editor*/\\n  \\\"editor.cursorBlinking\\\": \\\"smooth\\\", //敲完一行代码自动格式化\\n  \\\"editor.smoothScrolling\\\": true, //使编辑器滚动变平滑\\n  \\\"editor.tabCompletion\\\": \\\"on\\\", //启用Tab补全\\n  // \\\"editor.fontFamily\\\": \\\"'Jetbrains Mono', '思源黑体'\\\",//字体设置，个人喜欢Jetbrains Mono作英文字体，思源黑体作中文字体\\n  \\\"editor.fontLigatures\\\": true, //复制代码时复制纯文本而不是连语法高亮都复制了\\n  \\\"editor.suggest.snippetsPreventQuickSuggestions\\\": false, //这个开不开效果好像都一样，据说是因为一个bug，建议关掉\\n  \\\"editor.stickyTabStops\\\": true, //在缩进上移动光标时四个空格一组来移动，就仿佛它们是制表符(\\\\t)一样\\n  \\\"editor.wordBasedSuggestions\\\": \\\"off\\\", //关闭基于文件中单词来联想的功能（语言自带的联想就够了，开了这个会导致用vscode写MarkDown时的中文引号异常联想）\\n  \\\"editor.linkedEditing\\\": true, //在文件内容溢出vscode显示区域时自动折行\\n  \\\"editor.cursorSmoothCaretAnimation\\\": \\\"on\\\", //让光标移动、插入变得平滑\\n  \\\"editor.renderControlCharacters\\\": true, //编辑器中显示不可见的控制字符\\n  \\\"editor.renderWhitespace\\\": \\\"boundary\\\", //除了两个单词之间用于分隔单词的一个空格，以一个小灰点的样子使空格可见\\n  /*terminal*/\\n  \\\"terminal.integrated.defaultProfile.windows\\\": \\\"Ubuntu-20.04 (WSL)\\\", //将终端设为cmd，个人比较喜欢cmd作为终端\\n  \\\"terminal.integrated.cursorBlinking\\\": true, //终端光标闪烁\\n  \\\"terminal.integrated.rightClickBehavior\\\": \\\"copyPaste\\\", //在终端中右键时显示菜单而不是粘贴（个人喜好）\\n  /*files*/\\n  \\\"files.autoGuessEncoding\\\": true, //让VScode自动猜源代码文件的编码格式\\n  \\\"files.autoSave\\\": \\\"onFocusChange\\\", //在编辑器失去焦点时自动保存，这使自动保存近乎达到“无感知”的体验\\n  \\\"files.exclude\\\": {\\n    \\\".stfolder\\\": true,\\n    \\\"**/.cache\\\": true,\\n    \\\"**/.DS_Store\\\": true,\\n    \\\"**/.git\\\": false,\\n    \\\"**/.hg\\\": true,\\n    \\\"**/.idea\\\": true,\\n    \\\"**/.lh\\\": true,\\n    \\\"**/.svn\\\": true,\\n    \\\"**/.vscode-ctags\\\": true,\\n    \\\"**/*.depend\\\": true,\\n    \\\"**/*.o\\\": true,\\n    \\\"**/bower_components\\\": true,\\n    \\\"**/CVS\\\": true,\\n    \\\"**/deeprobots_application_ros1\\\": true,\\n    \\\"**/GPATH\\\": true,\\n    \\\"**/GRTAGS\\\": true,\\n    \\\"**/GTAGS\\\": true,\\n    \\\"**/node_modules\\\": true\\n  },\\n  \\\"files.watcherExclude\\\": {\\n    //不索引一些不必要索引的大文件夹以减少内存和CPU消耗\\n    \\\"**/.git/objects/**\\\": true,\\n    \\\"**/.git/subtree-cache/**\\\": true,\\n    \\\"**/node_modules/**\\\": true,\\n    \\\"**/tmp/**\\\": true,\\n    \\\"**/bower_components/**\\\": true,\\n    \\\"**/dist/**\\\": true,\\n    \\\"**/.history\\\": true\\n  },\\n  //     \\\"files.exclude\\\": {\\n  //   \\\"**/*.depend\\\": true,\\n  //   \\\"**/*.o\\\": true,\\n  //   \\\"**/build_dir/toolchain-aarch64_cortex-a53_gcc-8.4.0_musl\\\": true\\n  // },\\n  \\\"search.exclude\\\": {\\n    \\\"**/.history\\\": true,\\n    \\\"**/deeprobots_application_ros1\\\": true\\n  },\\n  /*workbench*/\\n  \\\"workbench.list.smoothScrolling\\\": true, //打开文件时不是“预览”模式，即在编辑一个文件时打开编辑另一个文件不会覆盖当前编辑的文件而是新建一个标签页\\n  \\\"workbench.editor.wrapTabs\\\": true, //隐藏新建无标题文件时的“选择语言？”提示（个人喜好，可以删掉此行然后Ctrl+N打开无标题新文件看看不hidden的效果）\\n  /*explorer*/\\n  \\\"explorer.confirmDelete\\\": false, //删除文件时不弹出确认弹窗（因为很烦）\\n  \\\"explorer.confirmDragAndDrop\\\": false, //往左边文件资源管理器拖动东西来移动/复制时不显示确认窗口（因为很烦）\\n  /*search*/\\n  \\\"search.followSymlinks\\\": false, //据说可以减少vscode的CPU和内存占用\\n  /*window*/\\n  \\\"window.menuBarVisibility\\\": \\\"visible\\\", //在全屏模式下仍然显示窗口顶部菜单（没有菜单很难受）\\n  \\\"window.dialogStyle\\\": \\\"custom\\\", //每次调试都打开调试控制台，方便调试\\n  \\\"debug.showBreakpointsInOverviewRuler\\\": true, //固定调试时工具条的位置，防止遮挡代码内容（个人喜好）\\n  \\\"debug.saveBeforeStart\\\": \\\"nonUntitledEditorsInActiveGroup\\\", //在启动调试会话前保存除了无标题文档以外的文档（毕竟你创建了无标题文档就说明你根本没有想保存它的意思（至少我是这样的。））\\n  \\\"debug.onTaskErrors\\\": \\\"showErrors\\\", //预启动任务出错后显示错误，并不启动调试\\n  /*html*/\\n  \\\"html.format.indentHandlebars\\\": true,\\n  \\\"gitlens.showWelcomeOnInstall\\\": false,\\n  \\\"workbench.startupEditor\\\": \\\"none\\\",\\n  \\\"lldb.suppressUpdateNotifications\\\": true,\\n  \\\"terminal.integrated.enableMultiLinePasteWarning\\\": false,\\n  \\\"merge-conflict.autoNavigateNextConflict.enabled\\\": true,\\n  \\\"problems.decorations.enabled\\\": false,\\n  \\\"problems.defaultViewMode\\\": \\\"table\\\",\\n  \\\"editor.definitionLinkOpensInPeek\\\": true,\\n  \\\"markdown.preview.breaks\\\": true,\\n  \\\"files.associations\\\": {\\n    \\\"*.cmake\\\": \\\"makefile\\\",\\n    \\\"*.vim\\\": \\\"lua\\\"\\n  },\\n  \\\"projectManager.groupList\\\": true,\\n  \\\"window.customMenuBarAltFocus\\\": false,\\n  // 头部注释\\n  \\\"fileheader.customMade\\\": {\\n    // Author字段是文件的创建者 可以在specialOptions中更改特殊属性\\n    // 公司项目和个人项目可以配置不同的用户名与邮箱 搜索: gitconfig includeIf  比如: https://ayase.moe/2021/03/09/customized-git-config/\\n    // 自动提取当前git config中的: 用户名、邮箱\\n    \\\"Author\\\": \\\"git config user.name && git config user.email\\\", // 同时获取用户名与邮箱\\n    // \\\"Author\\\": \\\"git config user.name\\\", // 仅获取用户名\\n    // \\\"Author\\\": \\\"git config user.email\\\", // 仅获取邮箱\\n    // \\\"Author\\\": \\\"OBKoro1\\\", // 写死的固定值 不从git config中获取\\n    \\\"Date\\\": \\\"Do not edit\\\", // 文件创建时间(不变)\\n    // LastEditors、LastEditTime、FilePath将会自动更新 如果觉得时间更新的太频繁可以使用throttleTime(默认为1分钟)配置更改更新时间。\\n    \\\"LastEditors\\\": \\\"git config user.name && git config user.email\\\", // 文件最后编辑者 与Author字段一致\\n    // 由于编辑文件就会变更最后编辑时间，多人协作中合并的时候会导致merge\\n    // 可以将时间颗粒度改为周、或者月，这样冲突就减少很多。搜索变更时间格式: dateFormat\\n    \\\"LastEditTime\\\": \\\"Do not edit\\\", // 文件最后编辑时间\\n    // 输出相对路径，类似: /文件夹名称/src/index.js\\n    \\\"FilePath\\\": \\\"Do not edit\\\", // 文件在项目中的相对路径 自动更新\\n    // 插件会自动将光标移动到Description选项中 方便输入 Description字段可以在specialOptions更改\\n    \\\"Description\\\": \\\"\\\", // 介绍文件的作用、文件的入参、出参。\\n    // custom_string_obkoro1~custom_string_obkoro100都可以输出自定义信息\\n    // 可以设置多条自定义信息 设置个性签名、留下QQ、微信联系方式、输入空行等\\n    \\\"custom_string_obkoro1\\\": \\\"\\\",\\n    // 版权声明 保留文件所有权利 自动替换年份 获取git配置的用户名和邮箱\\n    // 版权声明获取git配置, 与Author字段一致: ${git_name} ${git_email} ${git_name_email}\\n    \\\"custom_string_obkoro1_copyright\\\": \\\"Copyright (c) ${now_year} by NevinXu, All Rights Reserved. \\\"\\n    // \\\"custom_string_obkoro1_copyright\\\": \\\"Copyright (c) ${now_year} by 写死的公司名/用户名, All Rights Reserved. \\\"\\n  },\\n  // 函数注释\\n  \\\"fileheader.cursorMode\\\": {\\n    \\\"description\\\": \\\"\\\", // 函数注释生成之后，光标移动到这里\\n    \\\"param\\\": \\\"\\\", // param 开启函数参数自动提取 需要将光标放在函数行或者函数上方的空白行\\n    \\\"return\\\": \\\"\\\"\\n  },\\n  // 插件配置项\\n  \\\"fileheader.configObj\\\": {\\n    \\\"autoAdd\\\": true, // 检测文件没有头部注释，自动添加文件头部注释\\n    \\\"autoAddLine\\\": 100, // 文件超过多少行数 不再自动添加头部注释\\n    \\\"autoAlready\\\": true, // 只添加插件支持的语言以及用户通过`language`选项自定义的注释\\n    \\\"supportAutoLanguage\\\": [], // 设置之后，在数组内的文件才支持自动添加\\n    // 自动添加头部注释黑名单\\n    \\\"prohibitAutoAdd\\\": [\\\"json\\\"],\\n    \\\"prohibitItemAutoAdd\\\": [\\n      \\\"项目的全称禁止项目自动添加头部注释, 使用快捷键自行添加\\\"\\n    ],\\n    \\\"folderBlacklist\\\": [\\\"node_modules\\\"], // 文件夹或文件名禁止自动添加头部注释\\n    \\\"wideSame\\\": false, // 头部注释等宽设置\\n    \\\"wideNum\\\": 13, // 头部注释字段长度 默认为13\\n    \\\"functionWideNum\\\": 0, // 函数注释等宽设置 设为0 即为关闭\\n    // 头部注释第几行插入\\n    \\\"headInsertLine\\\": {\\n      \\\"php\\\": 2 // php文件 插入到第二行\\n    },\\n    \\\"beforeAnnotation\\\": {}, // 头部注释之前插入内容\\n    \\\"afterAnnotation\\\": {}, // 头部注释之后插入内容\\n    \\\"specialOptions\\\": {}, // 特殊字段自定义 比如: Author、LastEditTime、LastEditors、FilePath、Description、Date等\\n    \\\"switch\\\": {\\n      \\\"newlineAddAnnotation\\\": true // 默认遇到换行符(\\\\r\\\\n \\\\n \\\\r)添加注释符号\\n    },\\n    \\\"moveCursor\\\": true, // 自动移动光标到Description所在行\\n    \\\"dateFormat\\\": \\\"YYYY-MM-DD HH:mm:ss\\\",\\n    \\\"atSymbol\\\": [\\\"@\\\", \\\"@\\\"], // 更改所有文件的自定义注释中的@符号\\n    \\\"atSymbolObj\\\": {}, //  更改单独语言/文件的@\\n    \\\"colon\\\": [\\\": \\\", \\\": \\\"], // 更改所有文件的注释冒号\\n    \\\"colonObj\\\": {}, //  更改单独语言/文件的冒号\\n    \\\"filePathColon\\\": \\\"路径分隔符替换\\\", // 默认值： mac: / window是: \\\\\\n    \\\"showErrorMessage\\\": false, // 是否显示插件错误通知 用于debugger\\n    \\\"writeLog\\\": false, // 错误日志生成\\n    \\\"CheckFileChange\\\": false, // 单个文件保存时进行diff检查\\n    \\\"createHeader\\\": false, // 新建文件自动添加头部注释\\n    \\\"useWorker\\\": false, // 是否使用工作区设置\\n    \\\"designAddHead\\\": false, // 添加注释图案时添加头部注释\\n    \\\"headDesignName\\\": \\\"random\\\", // 图案注释使用哪个图案\\n    \\\"headDesign\\\": false, // 是否使用图案注释替换头部注释\\n    // 自定义配置是否在函数内生成注释 不同文件类型和语言类型\\n    \\\"cursorModeInternalAll\\\": {}, // 默认为false 在函数外生成函数注释\\n    \\\"openFunctionParamsCheck\\\": true, // 开启关闭自动提取添加函数参数\\n    \\\"functionParamsShape\\\": [\\\"{\\\", \\\"}\\\"], // 函数参数外形自定义\\n    // \\\"functionParamsShape\\\": \\\"no type\\\" 函数参数不需要类型\\n    \\\"functionBlankSpaceAll\\\": {}, // 函数注释空格缩进 默认为空对象 默认值为0 不缩进\\n    \\\"functionTypeSymbol\\\": \\\"*\\\", // 参数没有类型时的默认值\\n    \\\"typeParamOrder\\\": \\\"type param\\\", // 参数类型 和 参数的位置自定义\\n    \\\"NoMatchParams\\\": \\\"no show param\\\", // 没匹配到函数参数，是否显示@param与@return这两行 默认不显示param\\n    \\\"functionParamAddStr\\\": \\\"\\\", // 在 type param 后面增加字符串 可能是冒号，方便输入参数描述\\n    // 自定义语言注释，自定义取消 head、end 部分\\n    // 不设置自定义配置language无效 默认都有head、end\\n    \\\"customHasHeadEnd\\\": {}, // \\\"cancel head and function\\\" | \\\"cancel head\\\" | \\\"cancel function\\\"\\n    \\\"throttleTime\\\": 60000, // 对同一个文件 需要过1分钟再次修改文件并保存才会更新注释\\n    // 自定义语言注释符号，覆盖插件的注释格式\\n    \\\"language\\\": {\\n      // js后缀文件\\n      \\\"js\\\": {\\n        \\\"head\\\": \\\"/$$\\\",\\n        \\\"middle\\\": \\\" $ @\\\",\\n        \\\"end\\\": \\\" $/\\\",\\n        // 函数自定义注释符号：如果有此配置 会默认使用\\n        \\\"functionSymbol\\\": {\\n          \\\"head\\\": \\\"/******* \\\", // 统一增加几个*号\\n          \\\"middle\\\": \\\" * @\\\",\\n          \\\"end\\\": \\\" */\\\"\\n        },\\n        \\\"functionParams\\\": \\\"typescript\\\" // 函数注释使用ts语言的解析逻辑\\n      },\\n      // 一次匹配多种文件后缀文件 不用重复设置\\n      \\\"h/hpp/cpp\\\": {\\n        \\\"head\\\": \\\"/*** \\\", // 统一增加几个*号\\n        \\\"middle\\\": \\\" * @\\\",\\n        \\\"end\\\": \\\" */\\\"\\n      },\\n      // 针对有特殊要求的文件如：test.blade.php\\n      \\\"blade.php\\\": {\\n        \\\"head\\\": \\\"<!--\\\",\\n        \\\"middle\\\": \\\" * @\\\",\\n        \\\"end\\\": \\\"-->\\\"\\n      }\\n    },\\n    // 默认注释  没有匹配到注释符号的时候使用。\\n    \\\"annotationStr\\\": {\\n      \\\"head\\\": \\\"/*\\\",\\n      \\\"middle\\\": \\\" * @\\\",\\n      \\\"end\\\": \\\" */\\\",\\n      \\\"use\\\": false\\n    }\\n  },\\n  \\\"git.openRepositoryInParentFolders\\\": \\\"always\\\",\\n  \\\"alias-skip.mappings\\\": {\\n    \\\"@\\\": \\\"/src\\\"\\n  },\\n  \\\"git.autofetch\\\": true,\\n  \\\"cmake.configureOnOpen\\\": false,\\n  \\\"git.ignoreRebaseWarning\\\": true,\\n  \\\"gitlens.graph.layout\\\": \\\"editor\\\",\\n  \\\"epitech-c-cpp-headers.username\\\": \\\"nevinxu\\\",\\n  \\\"epitech-c-cpp-headers.login\\\": \\\"\\\",\\n  \\\"epitech-c-cpp-headers.headerType\\\": \\\"post2017\\\",\\n  \\\"epitech-c-cpp-headers.usePragmaOnce\\\": false,\\n  \\\"epitech-c-cpp-headers.autoGenerateClasses\\\": true,\\n  \\\"epitech-c-cpp-headers.indentAccessSpecifiers\\\": true,\\n  \\\"git.fetchOnPull\\\": true,\\n  \\\"git.ignoreLimitWarning\\\": true,\\n  \\\"git.mergeEditor\\\": true,\\n  \\\"git.statusLimit\\\": 0,\\n  \\\"git.supportCancellation\\\": true,\\n  \\\"git.confirmSync\\\": false,\\n  \\\"todo-tree.filtering.ignoreGitSubmodules\\\": true,\\n  \\\"git.rememberPostCommitCommand\\\": true,\\n  \\\"doxdocgen.generic.useGitUserEmail\\\": true,\\n  \\\"doxdocgen.generic.useGitUserName\\\": true,\\n  \\\"gnuGlobal.autoUpdate\\\": \\\"Enabled\\\",\\n  \\\"editor.defaultFormatter\\\": \\\"xaver.clang-format\\\",\\n  \\\"alias-skip.allowedsuffix\\\": [\\\"js\\\", \\\"vue\\\", \\\"jsx\\\", \\\"ts\\\"],\\n  \\\"sync-rsync.exclude\\\": [\\\".git\\\"],\\n  \\\"local-history.daysLimit\\\": 0,\\n  \\\"local-history.maxDisplay\\\": 9999999999,\\n  \\\"gitlens.gitCommands.skipConfirmations\\\": [\\n    \\\"fetch:command\\\",\\n    \\\"stash-push:command\\\",\\n    \\\"switch:command\\\",\\n    \\\"push:command\\\"\\n  ],\\n  \\\"local-history.saveDelay\\\": 60,\\n  \\\"security.workspace.trust.untrustedFiles\\\": \\\"open\\\",\\n  \\\"sync-rsync.autoShowOutputOnError\\\": false,\\n  \\\"sync-rsync.local\\\": \\\"\\\",\\n  \\\"workbench.localHistory.maxFileEntries\\\": 1000000000000000000,\\n  \\\"workbench.localHistory.maxFileSize\\\": 1000000000000000000,\\n  \\\"local-history.treeLocation\\\": \\\"localHistory\\\",\\n  \\\"local-history.path\\\": \\\"${workspaceFolder}/.vscode\\\",\\n  \\\"workbench.commandPalette.preserveInput\\\": true,\\n  \\\"workbench.experimental.cloudChanges.autoStore\\\": \\\"onShutdown\\\",\\n  \\\"workbench.experimental.cloudChanges.partialMatches.enabled\\\": true,\\n  \\\"workbench.editor.limit.enabled\\\": true,\\n  \\\"workbench.settings.useSplitJSON\\\": true,\\n  \\\"sftp.debug\\\": true,\\n  \\\"sftp.printDebugLog\\\": true,\\n  \\\"localHistory.daysLimit\\\": 0,\\n  \\\"localHistory.exclude\\\": [\\n    \\\"**/.history/**\\\",\\n    \\\"**/node_modules/**\\\",\\n    \\\"**/typings/**\\\",\\n    \\\"**/out/**\\\",\\n    \\\"**/Code/User/**\\\"\\n  ],\\n  \\\"localHistory.maxDisplay\\\": 9999,\\n  \\\"C_Cpp.hover\\\": \\\"disabled\\\",\\n  // \\\"C_Cpp.intelliSenseEngine\\\": \\\"disabled\\\",\\n  \\\"C_Cpp.formatting\\\": \\\"disabled\\\",\\n  \\\"C_Cpp.doxygen.generateOnType\\\": false,\\n  \\\"C_Cpp.simplifyStructuredComments\\\": false,\\n  \\\"C_Cpp.codeAnalysis.clangTidy.codeAction.formatFixes\\\": false,\\n  \\\"C_Cpp.loggingLevel\\\": \\\"None\\\",\\n  \\\"timeline.pageOnScroll\\\": true,\\n  \\\"Codegeex.Privacy\\\": true,\\n  \\\"projectManager.openInNewWindowWhenClickingInStatusBar\\\": true,\\n  \\\"gitlens.graph.minimap.additionalTypes\\\": [\\n    \\\"localBranches\\\",\\n    \\\"remoteBranches\\\",\\n    \\\"tags\\\"\\n  ],\\n  \\\"remote.SSH.remotePlatform\\\": {\\n    \\\"192.168.12.131\\\": \\\"linux\\\",\\n    \\\"192.168.12.134\\\": \\\"linux\\\",\\n    \\\"192.168.12.100\\\": \\\"linux\\\",\\n    \\\"192.168.12.128\\\": \\\"linux\\\",\\n    \\\"nevinxu.top\\\": \\\"linux\\\",\\n    \\\"192.168.2.4\\\": \\\"linux\\\",\\n    \\\"192.168.11.250\\\": \\\"linux\\\",\\n    \\\"192.168.11.223\\\": \\\"linux\\\",\\n    \\\"192.168.11.11\\\": \\\"linux\\\",\\n    \\\"11022.nevinxu.top\\\": \\\"linux\\\",\\n    \\\"192.168.11.132\\\": \\\"linux\\\",\\n    \\\"10022.nevinxu.top\\\": \\\"linux\\\",\\n    \\\"20022.nevinxu.top\\\": \\\"linux\\\",\\n    \\\"127.0.0.1\\\": \\\"linux\\\",\\n    \\\"192.168.11.4\\\": \\\"linux\\\",\\n    \\\"nevin.email\\\": \\\"linux\\\",\\n    \\\"192.168.11.208\\\": \\\"linux\\\",\\n    \\\"192.168.1.130\\\": \\\"linux\\\",\\n    \\\"192.168.2.1\\\": \\\"linux\\\",\\n    \\\"48022.nevin.email\\\": \\\"linux\\\",\\n    \\\"50022.nevin.email\\\": \\\"linux\\\",\\n    \\\"ubuntu2204.nevin.email\\\": \\\"linux\\\",\\n    \\\"ubuntu2004.nevin.email\\\": \\\"linux\\\",\\n    \\\"192.168.15.128\\\": \\\"linux\\\",\\n    \\\"46022.nevin.email\\\": \\\"linux\\\",\\n    \\\"47022.nevin.email\\\": \\\"linux\\\",\\n    \\\"45022.nevin.email\\\": \\\"linux\\\",\\n    \\\"49022.nevin.email\\\": \\\"linux\\\",\\n    \\\"43022.nevin.email\\\": \\\"linux\\\",\\n    \\\"44022.nevin.email\\\": \\\"linux\\\",\\n    \\\"54022.nevin.email\\\": \\\"linux\\\",\\n    \\\"52022.nevin.email\\\": \\\"linux\\\",\\n    \\\"51022.nevin.email\\\": \\\"linux\\\",\\n    \\\"pve.nevin.email\\\": \\\"linux\\\",\\n    \\\"58022.nevin.email\\\": \\\"linux\\\",\\n    \\\"192.168.99.2\\\": \\\"linux\\\",\\n    \\\"10522.nevin.email\\\": \\\"linux\\\",\\n    \\\"20322.nevin.email\\\": \\\"linux\\\",\\n    \\\"192.168.51.14\\\": \\\"linux\\\",\\n    \\\"pve-remote\\\": \\\"linux\\\",\\n    \\\"work-remote\\\": \\\"linux\\\",\\n    \\\"192.168.99.109\\\": \\\"linux\\\",\\n    \\\"192.168.123.99\\\": \\\"linux\\\",\\n    \\\"192.168.1.120\\\": \\\"linux\\\",\\n    \\\"192.168.10.148\\\": \\\"linux\\\",\\n    \\\"192.168.10.217\\\": \\\"linux\\\"\\n  },\\n  \\\"debug.showSubSessionsInToolBar\\\": true,\\n  \\\"debug.inlineValues\\\": \\\"on\\\",\\n  \\\"debug.showInStatusBar\\\": \\\"always\\\",\\n  \\\"search.useGlobalIgnoreFiles\\\": true,\\n  \\\"codestream.email\\\": \\\"<EMAIL>\\\",\\n  \\\"editor.bracketPairColorization.independentColorPoolPerBracketType\\\": true,\\n  \\\"editor.renderLineHighlightOnlyWhenFocus\\\": true,\\n  \\\"diffEditor.experimental.showMoves\\\": true,\\n  \\\"window.enableMenuBarMnemonics\\\": false,\\n  \\\"editor.codeActionsOnSave\\\": {},\\n  \\\"debug.internalConsoleOptions\\\": \\\"neverOpen\\\",\\n  \\\"todo-tree.general.debug\\\": true,\\n  \\\"editor.experimental.asyncTokenizationLogging\\\": true,\\n  \\\"debug.openDebug\\\": \\\"neverOpen\\\",\\n  \\\"markdown.trace.extension\\\": \\\"verbose\\\",\\n  \\\"settingsSync.ignoredSettings\\\": [\\\"-vim.autoSwitchInputMethod.switchIMCmd\\\"],\\n  \\\"workbench.editor.preferHistoryBasedLanguageDetection\\\": true,\\n  \\\"localHistory.saveDelay\\\": 60,\\n  \\\"localHistory.suppressErrors\\\": true,\\n  \\\"localHistory.alwaysExpand\\\": true,\\n  \\\"localHistory.dateLocale\\\": \\\"zh-CN\\\",\\n  \\\"git.timeline.showUncommitted\\\": true,\\n  \\\"clangd.checkUpdates\\\": false,\\n  \\\"editor.accessibilitySupport\\\": \\\"off\\\",\\n  \\\"dashboard.openOnStartup\\\": \\\"never\\\",\\n  \\\"marquee.configuration.workspaceLaunch\\\": true,\\n  \\\"marquee.widgets.github.since\\\": \\\"Daily\\\",\\n  \\\"marquee.widgets.weather.scale\\\": \\\"Celsius\\\",\\n  \\\"marquee.widgets.weather.city\\\": \\\"hangzhou\\\",\\n  \\\"diffEditor.maxComputationTime\\\": 0,\\n  /// clangd\\n  \\\"clangd.checkUpdates\\\": true,\\n  \\\"clangd.arguments\\\": [\\n    \\\"--background-index\\\",\\n    \\\"--compile-commands-dir=${workspaceFolder}\\\",\\n    \\\"-j=12\\\",\\n    \\\"--clang-tidy\\\",\\n    \\\"--clang-tidy-checks=performance-*,bugprone-*\\\",\\n    \\\"--all-scopes-completion\\\",\\n    \\\"--completion-style=detailed\\\",\\n    \\\"--header-insertion=iwyu\\\",\\n    \\\"--pch-storage=disk\\\"\\n  ],\\n  \\\"diffEditor.renderSideBySide\\\": false,\\n  \\\"workbench.settings.applyToAllProfiles\\\": [\\n    \\\"editor.defaultFormatter\\\",\\n    \\\"editor.formatOnSave\\\",\\n    \\\"editor.formatOnType\\\",\\n    \\\"C_Cpp.codeFolding\\\",\\n    \\\"C_Cpp.configurationWarnings\\\",\\n    \\\"C_Cpp.formatting\\\",\\n    \\\"C_Cpp.vcFormat.newLine.beforeOpenBrace.function\\\",\\n    \\\"C_Cpp.vcFormat.newLine.beforeOpenBrace.block\\\",\\n    \\\"C_Cpp.vcFormat.newLine.beforeOpenBrace.lambda\\\",\\n    \\\"C_Cpp.vcFormat.newLine.beforeOpenBrace.namespace\\\",\\n    \\\"C_Cpp.vcFormat.newLine.beforeOpenBrace.type\\\",\\n    \\\"C_Cpp.vcFormat.newLine.beforeWhileInDoWhile\\\",\\n    \\\"C_Cpp.vcFormat.newLine.scopeBracesOnSeparateLines\\\",\\n    \\\"vim.statusBarColorControl\\\",\\n    \\\"clangd.arguments\\\",\\n    \\\"clang-format.assumeFilename\\\",\\n    \\\"editor.fontFamily\\\",\\n    \\\"terminal.integrated.fontFamily\\\"\\n  ],\\n  \\\"highlightLine.borderStyle\\\": \\\"dashed\\\",\\n  \\\"Codegeex.Explanation.LanguagePreference\\\": \\\"zh-CN\\\",\\n  \\\"makefile.buildBeforeLaunch\\\": false,\\n  \\\"cmake.allowCommentsInPresetsFile\\\": true,\\n  \\\"cmake.automaticReconfigure\\\": false,\\n  \\\"cmake.ctest.allowParallelJobs\\\": true,\\n  \\\"cmake.parseBuildDiagnostics\\\": false,\\n  \\\"editor.formatOnSaveMode\\\": \\\"modificationsIfAvailable\\\",\\n  \\\"editor.minimap.autohide\\\": true,\\n  \\\"editor.minimap.size\\\": \\\"fill\\\",\\n  \\\"editor.cursorStyle\\\": \\\"block\\\",\\n  \\\"editor.autoIndent\\\": \\\"advanced\\\",\\n  \\\"editor.defaultColorDecorators\\\": \\\"auto\\\",\\n  \\\"editor.foldingImportsByDefault\\\": true,\\n  \\\"editor.inlayHints.padding\\\": true,\\n  \\\"editor.stickyScroll.enabled\\\": true,\\n  \\\"editor.wrappingIndent\\\": \\\"indent\\\",\\n  \\\"files.readonlyFromPermissions\\\": true,\\n  \\\"workbench.editor.defaultBinaryEditor\\\": \\\"hexEditor.hexedit\\\",\\n  \\\"workbench.editor.enablePreviewFromCodeNavigation\\\": true,\\n  \\\"workbench.editor.enablePreviewFromQuickOpen\\\": true,\\n  \\\"application.experimental.rendererProfiling\\\": true,\\n  \\\"notebook.outline.showCodeCells\\\": true,\\n  \\\"outline.collapseItems\\\": \\\"alwaysCollapse\\\",\\n  \\\"vim.useSystemClipboard\\\": true,\\n  \\\"vim.cursorStylePerMode.insert\\\": \\\"line\\\",\\n  \\\"[json]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"esbenp.prettier-vscode\\\"\\n  },\\n  \\\"debug.console.acceptSuggestionOnEnter\\\": \\\"on\\\",\\n  \\\"debug.terminal.clearBeforeReusing\\\": true,\\n  \\\"vim.highlightedyank.enable\\\": true,\\n  \\\"colorTabs.activityBarBackground\\\": true,\\n  \\\"colorTabs.titleBackground\\\": true,\\n  \\\"colorTabs.titleLabel\\\": true,\\n  \\\"colorTabs.tabBackground\\\": true,\\n  \\\"colorTabs.ignoreCase\\\": true,\\n  \\\"localHistory.absolute\\\": true,\\n  \\\"[cmake]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"cheshirekow.cmake-format\\\"\\n  },\\n  \\\"remote.autoForwardPorts\\\": false,\\n  \\\"cmake.buildDirectory\\\": \\\"${workspaceFolder}/build/vscode\\\",\\n  \\\"remote.forwardOnOpen\\\": false,\\n  \\\"remote.restoreForwardedPorts\\\": false,\\n  \\\"workbench.editor.empty.hint\\\": \\\"hidden\\\",\\n  \\\"dotnetAcquisitionExtension.existingDotnetPath\\\": [\\\"/usr/bin/dotnet\\\"],\\n  \\\"EnglishChineseDictionary.enableHover\\\": true,\\n  \\\"cmake.format.allowOptionalArgumentIndentation\\\": true,\\n  \\\"cmake.format.spaceAfterCommandName\\\": true,\\n  \\\"cmake.format.spaceInParentheses\\\": true,\\n  \\\"json.format.enable\\\": false,\\n  \\\"vim.autoSwitchInputMethod.enable\\\": true,\\n\\n  \\\"workbench.editorAssociations\\\": {\\n    \\\"*.copilotmd\\\": \\\"vscode.markdown.preview.editor\\\",\\n    \\\"*.png\\\": \\\"default\\\",\\n    \\\"*.docx\\\": \\\"default\\\"\\n  },\\n  \\\"diffEditor.ignoreTrimWhitespace\\\": true,\\n  \\\"clang-format.assumeFilename\\\": \\\"/mine/AOS-NET/.clang-format\\\",\\n  \\\"vscode-office.editorLanguage\\\": \\\"zh_CN\\\",\\n  \\\"sync-rsync.autoHideOutput\\\": true,\\n  \\\"prettier.proseWrap\\\": \\\"never\\\",\\n  \\\"[javascript]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"esbenp.prettier-vscode\\\"\\n  },\\n  \\\"cmake.showOptionsMovedNotification\\\": false,\\n  \\\"codeium.enableConfig\\\": {\\n    \\\"*\\\": true\\n  },\\n\\n  \\\"colon\\\": [0, 1], // The first number specify how much space to add to the left, can be negative.\\n  // The second number is how much space to the right, can be negative.\\n  \\\"assignment\\\": [1, 1], // The same as above.\\n  \\\"arrow\\\": [1, 1], // The same as above.\\n  \\\"comment\\\": 2, // Special how much space to add between the trailing comment and the code.\\n  // If this value is negative, it means don't align the trailing comment.\\n\\n  \\\"dotnetAcquisitionExtension.installTimeoutValue\\\": 60000,\\n\\\"vim.cursorStylePerMode.normal\\\": \\\"block\\\",\\n  \\\"clangd.multiProject.enabled\\\": true,\\n  \\\"Codegeex.Comment.LanguagePreference\\\": \\\"中文\\\",\\n  \\\"C_Cpp.intelliSenseEngine\\\": \\\"disabled\\\",\\n  \\\"searchEverywhere.shouldInitOnStartup\\\": true,\\n  \\\"searchEverywhere.shouldHighlightSymbol\\\": true,\\n  \\\"searchEverywhere.shouldDisplayNotificationInStatusBar\\\": true,\\n  \\\"[c]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"xaver.clang-format\\\"\\n  },\\n  \\\"gitHistory.sideBySide\\\": true,\\n  \\\"C_Cpp.autocomplete\\\": \\\"disabled\\\",\\n  \\\"extensions.experimental.affinity\\\": {\\n    \\\"asvetliakov.vscode-neovim\\\": 1\\n  },\\n  \\\"editor.wordWrap\\\": \\\"on\\\",\\n  \\\"vim.statusBarColorControl\\\": true,\\n  \\\"C_Cpp.configurationWarnings\\\": \\\"disabled\\\",\\n  \\\"C_Cpp.codeFolding\\\": \\\"disabled\\\",\\n  \\\"scm.alwaysShowActions\\\": true,\\n  \\\"terminal.integrated.accessibleViewPreserveCursorPosition\\\": true,\\n  \\\"ime-and-cursor.EnglishIM\\\": \\\"1\\\",\\n  \\\"ime-and-cursor.obtainIMCmd\\\": \\\"fcitx5-remote\\\",\\n  \\\"ime-and-cursor.switchIMCmd\\\": \\\"fcitx5-remote  -t {im}\\\",\\n  \\\"ime-and-cursor.ChineseIM\\\": \\\"2\\\",\\n  \\\"ime-and-cursor.useWithVim\\\": true,\\n  \\\"ime-and-cursor.helpVim\\\": true,\\n  \\\"ime-and-cursor.keepChecking\\\": 2000,\\n  \\\"terminal.integrated.fontFamily\\\": \\\"Hack Nerd Font Mono\\\",\\n  \\\"accessibility.hideAccessibleView\\\": true,\\n  \\\"[jsonc]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"esbenp.prettier-vscode\\\"\\n  },\\n  \\\"terminal.integrated.stickyScroll.enabled\\\": true,\\n  \\\"debug.closeReadonlyTabsOnEnd\\\": true,\\n  \\\"terminal.explorerKind\\\": \\\"external\\\",\\n  \\\"scm.alwaysShowRepositories\\\": true,\\n\\\"Lingma.LocalStoragePath\\\": \\\"/home/<USER>/.lingma\\\",\\n  \\\"terminal.integrated.allowChords\\\": false,\\n  \\\"makefile.configureOnOpen\\\": false,\\n  \\\"cmake.pinnedCommands\\\": [\\n    \\\"workbench.action.tasks.configureTaskRunner\\\",\\n    \\\"workbench.action.tasks.runTask\\\"\\n  ],\\n  \\\"workbench.colorCustomizations\\\": {\\n    \\\"statusBar.noFolderBackground\\\": \\\"#005f5f\\\",\\n    \\\"statusBar.debuggingBackground\\\": \\\"#005f5f\\\",\\n    \\\"statusBar.debuggingForeground\\\": \\\"#ffffff\\\",\\n    \\\"editor.selectionBackground\\\": \\\"#f54813\\\",\\n    \\\"editor.selectionHighlightBackground\\\": \\\"#139bf5\\\",\\n    \\\"editorCursor.foreground\\\": \\\"#ff0015\\\",\\n    \\\"terminalCursor.foreground\\\": \\\"#FF0000\\\",\\n    \\\"statusBar.background\\\": \\\"#005f5f\\\",\\n    \\\"statusBar.foreground\\\": \\\"#ffffff\\\"\\n  },\\n  \\\"codestream.serverUrl\\\": \\\"https://codestream-api-v2-us1.service.newrelic.com\\\",\\n  \\\"vscode-office.editorTheme\\\": \\\"Dracula\\\",\\n  \\\"terminal.integrated.ignoreBracketedPasteMode\\\": true,\\n  \\\"git.autoStash\\\": true,\\n  \\\"git.ignoreMissingGitWarning\\\": true,\\n  \\\"Codegeex.License\\\": \\\"\\\",\\n  \\\"window.confirmSaveUntitledWorkspace\\\": false,\\n  \\\"vim.smartRelativeLine\\\": true,\\n  \\\"gitlens.views.scm.grouped.views\\\": {\\n    \\\"commits\\\": true,\\n    \\\"branches\\\": true,\\n    \\\"remotes\\\": true,\\n    \\\"stashes\\\": false,\\n    \\\"tags\\\": true,\\n    \\\"worktrees\\\": true,\\n    \\\"contributors\\\": true,\\n    \\\"repositories\\\": false,\\n    \\\"searchAndCompare\\\": false,\\n    \\\"launchpad\\\": false\\n  },\\n  \\\"[python]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"ms-python.autopep8\\\"\\n  },\\n  \\\"clangd.path\\\": \\\"/home/<USER>/.config/Code/User/globalStorage/llvm-vs-code-extensions.vscode-clangd/install/17.0.3/clangd_17.0.3/bin/clangd\\\",\\n  \\\"redhat.telemetry.enabled\\\": true,\\n  \\\"outline-map.debug\\\": true,\\n  \\\"outline-map.findRef.enabled\\\": true,\\n  \\\"outline-map.findRef.uesFindImpl\\\": true,\\n  \\\"[cpp]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"llvm-vs-code-extensions.vscode-clangd\\\"\\n  },\\n  \\\"ros.distro\\\": \\\"humble\\\",\\n  // \\\"vim.enableNeovim\\\": true,\\n  \\\"vim.history\\\": 500,\\n  \\\"iHomeCoder.AutoTriggerCompletion\\\": true,\\n  \\\"iHomeCoder.AccessKey\\\": \\\"8AX92OD8B1RK73WDAZJJ\\\",\\n  \\\"github.copilot.enable\\\": {\\n    \\\"*\\\": false\\n  },\\n  \\\"augment.chat.userGuidelines\\\": \\\"## 概述\\\\n\\\\n- 你是Augment Code的AI编程助手，专门协助XXX的开发工作\\\\n- **必须使用Claude 4.0模型**：确保具备最新的代码理解和生成能力\\\\n- 严格遵循核心工作流程，使用中文与专业程序员交互，保持简洁专业的沟通风格\\\\n\\\\n## AI模型要求\\\\n\\\\n- **基础模型**：Claude 4.0 (Claude Sonnet 4)\\\\n- **开发商**：Anthropic\\\\n- **版本要求**：必须使用Claude 4.0或更高版本\\\\n- **能力要求**：支持代码生成、分析、调试和优化功能\\\\n\\\\n## 工作模式定义\\\\n\\\\n- Augment Code的工作模式分为6种，分别对应不同的工作阶段和任务类型\\\\n- 每种模式下，AI助手的响应内容和行为都有严格的规定\\\\n- 必须严格按照模式要求进行工作，不得擅自越界\\\\n\\\\n### [模式：研究] - 需求分析阶段\\\\n\\\\n- 使用`codebase-retrieval`工具深入理解现有代码结构\\\\n- 使用`context7-mcp`查询相关技术文档和最佳实践\\\\n- 使用`deepwiki-mcp`快速获取背景知识和技术原理\\\\n- 使用`sequential-thinking`分析复杂需求的技术可行性\\\\n- 分析用户需求的技术可行性和影响范围\\\\n- 识别相关的文件、类、方法和数据库表\\\\n\\\\n### [模式：构思] - 方案设计阶段\\\\n\\\\n- 使用`sequential-thinking`进行复杂方案的深度思考和设计\\\\n- 使用`context7-mcp`获取最新的技术方案和示例代码\\\\n- 使用`deepwiki-mcp`获取成熟设计范式与领域通识\\\\n- 提供可行的技术方案\\\\n- 方案包含：实现思路、技术栈、优缺点分析、工作量评估\\\\n- 格式：`[简要描述] - 优点：[...] 缺点：[...] 工作量：[...]`\\\\n\\\\n### [模式：计划] - 详细规划阶段\\\\n\\\\n- 使用`sequential-thinking`制定复杂项目的详细执行计划\\\\n- 使用`mcp-shrimp-task-manager`拆解任务并管理依赖关系\\\\n- 将选定方案分解为具体的执行步骤\\\\n- 每个步骤包含：操作的具体文件路径、涉及的类/方法/属性名称、修改的代码行数范围、预期的功能结果、依赖的外部库\\\\n- 创建任务文档：`./issues/[任务名称].md`\\\\n\\\\n### [模式：执行] - 代码实现阶段\\\\n\\\\n- 严格按照计划顺序执行每个步骤\\\\n- 使用`str-replace-editor`工具进行代码修改（每次不超过500行）\\\\n- 使用`desktop-commander`进行文件系统操作和命令执行\\\\n- 使用`mcp-shrimp-task-manager`跟踪任务执行状态与依赖关系\\\\n- 使用`sequential-thinking`分析和解决复杂的技术问题\\\\n- 遇到问题时请全面的分析，定位到原因后修复\\\\n\\\\n### [模式：评审] - 质量检查阶段\\\\n\\\\n- 对照原计划检查所有功能是否正确实现\\\\n- 使用`desktop-commander`运行编译测试，确保无语法错误\\\\n- 使用`sequential-thinking`进行全面的质量分析\\\\n- 总结完成的工作和遗留问题\\\\n- 使用`mcp-feedback-enhanced`请求用户最终确认\\\\n\\\\n### [模式：快速] - 紧急响应模式\\\\n\\\\n- 跳过完整工作流程，直接处理简单问题\\\\n- 适用于：bug修复、小幅调整、配置更改\\\\n- 可根据需要使用任何相关工具快速解决问题\\\\n\\\\n## 开发工作流程\\\\n\\\\n- **代码检索**：使用`codebase-retrieval`工具获取模板文件信息\\\\n- **代码编辑**：使用`str-replace-editor`工具进行代码修改和优化\\\\n- **文件操作**：使用`desktop-commander`进行系统级文件操作和命令执行\\\\n- **复杂分析**：使用`sequential-thinking`进行深度问题分析和方案设计\\\\n- **技术查询**：使用`context7-mcp`获取最新的技术文档和示例\\\\n- **知识背景补充**：使用`deepwiki-mcp`补充架构知识和行业术语\\\\n- **任务管理**：使用`mcp-shrimp-task-manager`进行任务拆分与状态追踪\\\\n- **自检验证**：在提交文件或解决方案前，必须先进行自检以确保其功能正常\\\\n- **分步执行**：大型文件处理应采用分步执行策略，确保操作不会因文件大小而中断\\\\n\\\\n## MCP服务优先级\\\\n\\\\n1. `mcp-feedback-enhanced` - 用户交互和确认\\\\n2. `sequential-thinking` - 复杂问题分析和深度思考\\\\n3. `context7-mcp` - 查询最新库文档和示例\\\\n4. `deepwiki-mcp` - 获取背景知识和领域概念\\\\n5. `mcp-shrimp-task-manager` - 拆分与管理任务依赖\\\\n6. `codebase-retrieval` - 分析现有代码结构\\\\n7. `desktop-commander` - 系统文件操作和命令执行\\\\n\\\\n## 工具使用指南\\\\n\\\\n### Sequential Thinking\\\\n\\\\n- **用途**：复杂问题的逐步分析\\\\n- **适用场景**：需求分析、方案设计、问题排查\\\\n- **使用时机**：遇到复杂逻辑或多步骤问题时\\\\n\\\\n### Context 7\\\\n\\\\n- **用途**：查询最新的技术文档、API参考和代码示例\\\\n- **适用场景**：技术调研、最佳实践获取\\\\n- **使用时机**：需要了解新技术或验证实现方案时\\\\n\\\\n### DeepWiki MCP\\\\n\\\\n- **用途**：检索背景知识、行业术语、常见架构和设计模式\\\\n- **适用场景**：研究、构思阶段需要理解技术原理和通识\\\\n- **使用时机**：遇到术语不清、原理未知、需引入通用范式时\\\\n\\\\n### MCP Shrimp Task Manager\\\\n\\\\n- **用途**：任务拆解、依赖管理、任务进度跟踪\\\\n- **适用场景**：详细计划阶段与执行阶段\\\\n- **使用时机**：任务过多需管理依赖、跟踪状态、建立任务树时\\\\n\\\\n### Desktop Commander\\\\n\\\\n- **用途**：执行系统命令、文件操作、运行测试\\\\n- **适用场景**：项目管理、测试执行、文件处理\\\\n- **使用时机**：需要进行系统级操作时\\\\n\\\\n## 工作流程控制\\\\n\\\\n- **强制反馈**：每个阶段完成后必须使用`mcp-feedback-enhanced`\\\\n- **任务结束**：持续调用`mcp-feedback-enhanced`直到用户反馈为空\\\\n- **代码复用**：优先使用现有代码结构，避免重复开发\\\\n- **文件位置**：所有项目文件必须在项目目录内部\\\\n- **工具协同**：根据任务复杂度合理组合使用多个MCP工具\\\\n\\\\n## 执行原则\\\\n\\\\n每次响应必须以当前模式标签开始，严格按照工作流程推进，确保代码质量和项目一致性。\\\",\\n  \\\"workbench.colorTheme\\\": \\\"C/C++ Theme\\\",\\n  \\\"update.mode\\\": \\\"none\\\",\\n  \\\"clangd.onConfigChanged\\\": \\\"restart\\\",\\n  \\\"codingcopilot.enableAutoCompletions\\\": false,\\n  \\\"codingcopilot.enableNextEditSuggestions\\\": false,\\n  \\\"codingcopilot.enableFloatShortcut\\\": false,\\n  \\\"codingcopilot.enableInlineChatAutoFormatCode\\\": false,\\n  \\\"codingcopilot.enableInlineChatShortcutsTip\\\": false,\\n  \\\"codingcopilot.enableCodelens\\\": false,\\n\\n}\"}", "keybindings": "{\"keybindings\":\"[\\n    {\\n        \\\"key\\\": \\\"ctrl+shift+o\\\",\\n        \\\"command\\\": \\\"workbench.action.toggleAuxiliaryBar\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+enter\\\",\\n\\t\\t\\\"command\\\": \\\"-codegeex.interactive-mode\\\",\\n\\t\\t\\\"when\\\": \\\"editorFocus && !editorReadonly\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+shift+c\\\",\\n\\t\\t\\\"command\\\": \\\"editor.action.clipboardCopyAction\\\",\\n\\t\\t\\\"when\\\": \\\"editorFocus\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+shift+v\\\",\\n\\t\\t\\\"command\\\": \\\"editor.action.clipboardPasteAction\\\",\\n\\t\\t\\\"when\\\": \\\"editorFocus\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+win+y\\\",\\n\\t\\t\\\"command\\\": \\\"extension.cursorTip\\\",\\n\\t\\t\\\"when\\\": \\\"editorTextFocus\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+shift+c\\\",\\n\\t\\t\\\"command\\\": \\\"-workbench.action.terminal.openNativeConsole\\\",\\n\\t\\t\\\"when\\\": \\\"!terminalFocus\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+f\\\",\\n\\t\\t\\\"command\\\": \\\"actions.find\\\",\\n\\t\\t\\\"when\\\": \\\"editorFocus || editorIsOpen\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+f\\\",\\n\\t\\t\\\"command\\\": \\\"-actions.find\\\",\\n\\t\\t\\\"when\\\": \\\"editorFocus || editorIsOpen\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"shift+alt+f\\\",\\n\\t\\t\\\"command\\\": \\\"workbench.action.findInFiles\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+shift+f\\\",\\n\\t\\t\\\"command\\\": \\\"-workbench.action.findInFiles\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+e\\\",\\n\\t\\t\\\"command\\\": \\\"-workbench.action.quickOpen\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+t ctrl+j\\\",\\n\\t\\t\\\"command\\\": \\\"workbench.action.terminal.moveToEditor\\\",\\n\\t\\t\\\"when\\\": \\\"terminalFocus\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+t ctrl+k\\\",\\n\\t\\t\\\"command\\\": \\\"workbench.action.terminal.moveToTerminalPanel\\\",\\n\\t\\t\\\"when\\\": \\\"terminalFocus\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+k ctrl+g\\\",\\n\\t\\t\\\"command\\\": \\\"workbench.scm.focus\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+k ctrl+e\\\",\\n\\t\\t\\\"command\\\": \\\"workbench.files.action.focusFilesExplorer\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+shift+g\\\",\\n\\t\\t\\\"command\\\": \\\"-workbench.view.scm\\\",\\n\\t\\t\\\"when\\\": \\\"workbench.scm.active\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+shift+g\\\",\\n\\t\\t\\\"command\\\": \\\"workbench.view.scm\\\",\\n\\t\\t\\\"when\\\": \\\"workbench.scm.active && !gitlens:disabled && config.gitlens.keymap == 'chorded'\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+shift+g g\\\",\\n\\t\\t\\\"command\\\": \\\"-workbench.view.scm\\\",\\n\\t\\t\\\"when\\\": \\\"workbench.scm.active && !gitlens:disabled && config.gitlens.keymap == 'chorded'\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+k ctrl+m\\\",\\n\\t\\t\\\"command\\\": \\\"bookmarks.toggle\\\",\\n\\t\\t\\\"when\\\": \\\"editorTextFocus\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+alt+k\\\",\\n\\t\\t\\\"command\\\": \\\"-bookmarks.toggle\\\",\\n\\t\\t\\\"when\\\": \\\"editorTextFocus\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+alt+k\\\",\\n\\t\\t\\\"command\\\": \\\"bookmarks.jumpToNext\\\",\\n\\t\\t\\\"when\\\": \\\"editorTextFocus\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+alt+l\\\",\\n\\t\\t\\\"command\\\": \\\"-bookmarks.jumpToNext\\\",\\n\\t\\t\\\"when\\\": \\\"editorTextFocus\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"alt+f\\\",\\n\\t\\t\\\"command\\\": \\\"workbench.action.terminal.focusFind\\\",\\n\\t\\t\\\"when\\\": \\\"terminalFindFocused && terminalHasBeenCreated || terminalFindFocused && terminalProcessSupported || terminalFocusInAny && terminalHasBeenCreated || terminalFocusInAny && terminalProcessSupported\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"ctrl+f\\\",\\n\\t\\t\\\"command\\\": \\\"-workbench.action.terminal.focusFind\\\",\\n\\t\\t\\\"when\\\": \\\"terminalFindFocused && terminalHasBeenCreated || terminalFindFocused && terminalProcessSupported || terminalFocusInAny && terminalHasBeenCreated || terminalFocusInAny && terminalProcessSupported\\\"\\n\\t},\\n\\t{\\n\\t\\t\\\"key\\\": \\\"alt+v\\\",\\n\\t\\t\\\"command\\\": \\\"workbench.action.terminal.paste\\\",\\n\\t\\t\\\"when\\\": \\\"terminalFocus && terminalHasBeenCreated || terminalFocus && terminalProcessSupported\\\"\\n\\t}\\n]\",\"platform\":3}", "extensions": "[{\"identifier\":{\"id\":\"anjali.clipboard-history\",\"uuid\":\"c529c9f4-005a-45b0-9fb9-89e0d9b8b990\"},\"displayName\":\"Clipboard History\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"anthropic.claude-code\",\"uuid\":\"3c13ae49-babe-45fe-8c48-5e45077a62bf\"},\"displayName\":\"Claude Code for VSCode\",\"version\":\"1.0.61\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"augment.vscode-augment\",\"uuid\":\"fc0e137d-e132-47ed-9455-c4636fa5b897\"},\"displayName\":\"Augment\",\"preRelease\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"codezombiech.gitignore\",\"uuid\":\"3e891cf9-53cb-49a3-8d01-8f0b1f0afb29\"},\"displayName\":\"gitignore\",\"version\":\"0.10.0\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"cweijan.vscode-office\",\"uuid\":\"936b1be7-8595-4f76-b102-aa6bb915da73\"},\"displayName\":\"Office Viewer\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"github.copilot\",\"uuid\":\"23c4aeee-f844-43cd-b53e-1113e483f1a6\"},\"displayName\":\"GitHub Copilot\",\"applicationScoped\":true},{\"identifier\":{\"id\":\"github.copilot-chat\",\"uuid\":\"7ec7d6e6-b89e-4cc5-a59b-d6c4d238246f\"},\"displayName\":\"GitHub Copilot Chat\",\"applicationScoped\":true},{\"identifier\":{\"id\":\"hybridtalentcomputing.cline-chinese\",\"uuid\":\"95de4cdd-27fe-4a49-84d7-78934e31de5b\"},\"displayName\":\"Cline Chinese\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-ceintl.vscode-language-pack-zh-hans\",\"uuid\":\"e4ee7751-6514-4731-9cdb-7580ffa9e70b\"},\"displayName\":\"Chinese (Simplified) (简体中文) Language Pack for Visual Studio Code\",\"applicationScoped\":true},{\"identifier\":{\"id\":\"saoudrizwan.claude-dev\",\"uuid\":\"ad94c633-a9d5-4f78-b85f-c664e7d91a0f\"},\"displayName\":\"Cline\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"tencent-cloud.coding-copilot\",\"uuid\":\"f505f28f-327d-4169-9890-6fda3c27f66c\"},\"displayName\":\"腾讯云代码助手 CodeBuddy\",\"version\":\"3.2.2489424\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.js-debug-companion\"},\"displayName\":\"JavaScript Debugger Companion Extension\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ajshort.msg\",\"uuid\":\"37da4556-ca94-4d5a-8b9a-84c480a57fb3\"},\"displayName\":\"Msg Language Support\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"alanmbarr.quotify\",\"uuid\":\"24333107-fe83-459b-8678-27faa19b2d96\"},\"displayName\":\"quotify\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"alefragnani.project-manager\",\"uuid\":\"1b747f06-3789-4ebd-ac99-f1fe430c3347\"},\"displayName\":\"Project Manager\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"bdavs.expect\",\"uuid\":\"c02af6e9-b7f3-4d81-b6ba-14583080ee28\"},\"displayName\":\"Expect\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"bierner.markdown-mermaid\",\"uuid\":\"f8d0ffc4-66bb-4a9c-8149-ef8f043691a1\"},\"displayName\":\"Markdown Preview Mermaid Support\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"chouzz.vscode-better-align\",\"uuid\":\"f5a6ecde-96ce-4fde-8744-ab88c7727069\"},\"displayName\":\"Better Align\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"christian-kohler.path-intellisense\",\"uuid\":\"a41c1549-4053-44d4-bf30-60fc809b4a86\"},\"displayName\":\"Path Intellisense\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ctf0.local-history-new\",\"uuid\":\"2374c478-3694-46bf-a207-7421a2633ba0\"},\"displayName\":\"Local History (New)\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"darkriszty.markdown-table-prettify\",\"uuid\":\"136682fc-7ac4-43b7-a50a-bb7890c39f25\"},\"displayName\":\"Markdown Table Prettifier\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"donjayamanne.githistory\",\"uuid\":\"5960f38e-0bbe-4644-8f9c-9c8824e82511\"},\"displayName\":\"Git History\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"dzhavat.bracket-pair-toggler\",\"uuid\":\"e052b2e6-71ab-4cb7-8a29-75d6e38ecb8d\"},\"displayName\":\"Bracket Pair Colorization Toggler\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"esbenp.prettier-vscode\",\"uuid\":\"96fa4707-6983-4489-b7c5-d5ffdfdcce90\"},\"displayName\":\"Prettier - Code formatter\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"golang.go\",\"uuid\":\"d6f6cfea-4b6f-41f4-b571-6ad2ab7918da\"},\"displayName\":\"Go\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"gruntfuggly.todo-tree\",\"uuid\":\"261cac81-cd7b-4555-bb41-0c2d2bcd3e70\"},\"displayName\":\"Todo Tree\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ibm.output-colorizer\",\"uuid\":\"113b22c8-8125-42ec-8c6b-80c3f5d5fa5f\"},\"displayName\":\"Output Colorizer\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"lihuiwang.vue-alias-skip\",\"uuid\":\"e50baa47-85ae-4615-af4a-4d5de18b19b7\"},\"displayName\":\"别名路径跳转\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"mhutchie.git-graph\",\"uuid\":\"438221f8-1107-4ccd-a6fe-f3b7fe0856b7\"},\"displayName\":\"Git Graph\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"mkxml.vscode-filesize\",\"uuid\":\"21b3e09e-b3f7-4e20-9302-50039286650d\"},\"displayName\":\"filesize\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"mohsen1.prettify-json\",\"uuid\":\"67e66172-30c7-4478-8f5d-6eac4ae755dc\"},\"displayName\":\"Prettify JSON\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"moshfeu.compare-folders\",\"uuid\":\"03241f90-1c77-402a-b17f-1d3cee943969\"},\"displayName\":\"Compare Folders\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-dotnettools.vscode-dotnet-runtime\",\"uuid\":\"1aab81a1-b3d9-4aef-976b-577d5d90fe3f\"},\"displayName\":\".NET Install Tool\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-containers\",\"uuid\":\"93ce222b-5f6f-49b7-9ab1-a0463c6238df\"},\"displayName\":\"开发容器\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-ssh\",\"uuid\":\"607fd052-be03-4363-b657-2bd62b83d28a\"},\"displayName\":\"Remote - SSH\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-ssh-edit\",\"uuid\":\"bfeaf631-bcff-4908-93ed-fda4ef9a0c5c\"},\"displayName\":\"Remote - SSH: Editing Configuration Files\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-wsl\",\"uuid\":\"f0c5397b-d357-4197-99f0-cb4202f22818\"},\"displayName\":\"WSL\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.vscode-remote-extensionpack\",\"uuid\":\"23d72dfc-8dd1-4e30-926e-8783b4378f13\"},\"displayName\":\"远程开发\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.hexeditor\",\"uuid\":\"cc7d2112-5178-4472-8e0e-25dced95e7f0\"},\"displayName\":\"十六进制编辑器\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.remote-explorer\",\"uuid\":\"11858313-52cc-4e57-b3e4-d7b65281e34b\"},\"displayName\":\"远程资源管理器\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.remote-repositories\",\"uuid\":\"cf5142f0-3701-4992-980c-9895a750addf\"},\"displayName\":\"远程存储库\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.remote-server\",\"uuid\":\"105c0b3c-07a9-4156-a4fc-4141040eb07e\"},\"displayName\":\"Remote - Tunnels\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"orepor.color-tabs-vscode-ext\",\"uuid\":\"f266b05e-14dc-4514-a584-36ecd07305ba\"},\"displayName\":\"ColorTabs\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"redhat.vscode-yaml\",\"uuid\":\"2061917f-f76a-458a-8da9-f162de22b97e\"},\"displayName\":\"YAML\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"richterger.perl\",\"uuid\":\"effbf376-b9ad-4395-9b0e-8cf0537fbf04\"},\"displayName\":\"Perl\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"scootersoftware.bcompare-vscode\",\"uuid\":\"ee6f8a8d-2194-430a-bb92-b6b0d4d60a2b\"},\"displayName\":\"Beyond Compare\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"shardulm94.trailing-spaces\",\"uuid\":\"6ad45f5a-09ec-44e5-b363-867ddc1ec674\"},\"displayName\":\"Trailing Spaces\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"sirtori.indenticator\",\"uuid\":\"fcbdb08e-4048-40e8-a674-fecc476f4b93\"},\"displayName\":\"Indenticator\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"sleistner.vscode-fileutils\",\"uuid\":\"d637104e-1fd7-4063-98fc-8afe46012c9b\"},\"displayName\":\"File Utils\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"tadayosi.vscode-makefile-outliner\",\"uuid\":\"ed100a48-ec9e-4306-bc9d-1f7bd42d814d\"},\"displayName\":\"Makefile Outliner\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"taiyuuki.chinese-color\",\"uuid\":\"06c55939-21c6-4304-9841-7945fc109936\"},\"displayName\":\"Chinese Colors\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"tdennis4496.cmantic\",\"uuid\":\"0e95c0fe-a577-4091-8339-9faece454473\"},\"displayName\":\"C-mantic\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"vadimcn.vscode-lldb\",\"uuid\":\"bee31e34-a44b-4a76-9ec2-e9fd1439a0f6\"},\"displayName\":\"CodeLLDB\",\"disabled\":true,\"version\":\"1.11.4\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscodevim.vim\",\"uuid\":\"d96e79c6-8b25-4be3-8545-0e0ecefcae03\"},\"displayName\":\"Vim\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"xen.ccpp-theme\",\"uuid\":\"affe3756-3e43-4e57-930b-e3e3e7c44fa4\"},\"displayName\":\"C/C++ Theme\",\"applicationScoped\":true},{\"identifier\":{\"id\":\"yzhang.markdown-all-in-one\",\"uuid\":\"98790d67-10fa-497c-9113-f6c7489207b2\"},\"displayName\":\"Markdown All in One\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"zainchen.json\",\"uuid\":\"311c4d7f-e81f-47f8-9956-6a9919ddef43\"},\"displayName\":\"json\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"zhaouv.vscode-markdown-everywhere\",\"uuid\":\"f56661a9-0f9e-4d0d-ba0b-4b1054b9ec61\"},\"displayName\":\"Markdown Everywhere\",\"disabled\":true,\"applicationScoped\":false}]", "globalState": "{\"storage\":{\"workbench.panel.chat.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"augment-chat\\\",\\\"isHidden\\\":false}]\",\"workbench.explorer.views.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.openEditorsView\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.explorer.emptyView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"npm\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.explorer.fileView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"commitViewProvider\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"compareCommitViewProvider\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"clangd.typeHierarchyView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"clangd.memoryUsage\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"clangd.ast\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"treeLocalHistoryExplorer\\\",\\\"isHidden\\\":false}]\",\"workbench.view.search.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.search\\\",\\\"isHidden\\\":false}]\",\"workbench.scm.views.state.hidden\":\"[{\\\"id\\\":\\\"workbench.scm.repositories\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.scm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.repositories\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.commits\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.branches\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.remotes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.stashes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.tags\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.worktrees\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.contributors\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.scm.history\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.grouped\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.scm.grouped\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.markers.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.markers.view\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.output.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"isHidden\\\":false}]\",\"terminal.hidden\":\"[{\\\"id\\\":\\\"terminal\\\",\\\"isHidden\\\":false}]\",\"workbench.activityBar.location\":\"default\",\"workbench.activity.pinnedViewlets2\":\"[{\\\"id\\\":\\\"workbench.view.explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.view.debug\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.search\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.view.scm\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.view.extensions\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extension.github-pull-requests\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":17},{\\\"id\\\":\\\"workbench.view.extension.github-pull-request\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":18},{\\\"id\\\":\\\"workbench.view.extension.local-history\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":24},{\\\"id\\\":\\\"workbench.view.extension.coding-copilot-chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.remote\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extension.test\\\",\\\"pinned\\\":false,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.references-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.augment-chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.iHomeCoder_sidebar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.github-copilot-completions-debugger-panel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.dockerView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.copilot-chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.azALDevTools_duplicateSearchView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.cline-chinese-ActivityBar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.azALDevTools_warningDirectivesView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.hexExplorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.augment-infinity-sidebar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.gitlens\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.gitlensInspect\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.gitlensPatch\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.project-manager\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.trae\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.TongyiLingma\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.codegeex-sidebar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.project-dashboard\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.marscode\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":17},{\\\"id\\\":\\\"workbench.view.extension.codestream-activitybar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.krinql\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.vscode-edge-devtools-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.cspell-info-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.localHistory\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.liveshare\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":21},{\\\"id\\\":\\\"workbench.view.extension.coding-copilot\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.coding-copilot-webviews-login\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":21},{\\\"id\\\":\\\"workbench.view.extension.cspell-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":27},{\\\"id\\\":\\\"workbench.view.extension.todo\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.todo-tree-container\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.coder-maker-sidebar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":16},{\\\"id\\\":\\\"workbench.view.extension.searchResults\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.tabnine\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":20},{\\\"id\\\":\\\"workbench.view.extension.mintdocs\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.zhihu-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":37},{\\\"id\\\":\\\"workbench.view.extension.sourcegraph-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.view.extension.chatgpt-china-view-container\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":22},{\\\"id\\\":\\\"workbench.view.extension.cloudmusic\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":24},{\\\"id\\\":\\\"workbench.panel.chatSidebar\\\",\\\"pinned\\\":false,\\\"visible\\\":false,\\\"order\\\":100},{\\\"id\\\":\\\"userDataProfiles\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.sync\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.editSessions\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\",\"workbench.panel.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.panel.markers\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.panel.repl\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.view.extension.augment-panel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.gitlensPanel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.cspellPanel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.panel.testResults\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"~remote.forwardedPortsContainer\\\",\\\"pinned\\\":false,\\\"visible\\\":true,\\\"order\\\":5},{\\\"id\\\":\\\"workbench.view.extension.jupyter-variables\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"terminal\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.extension.azurePanel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"refactorPreview\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\",\"workbench.auxiliarybar.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.view.extension.outline-map\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.views.service.auxiliarybar.ab051650-a8ed-47eb-9f38-fe25e83a4070\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.panel.chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100},{\\\"id\\\":\\\"workbench.views.service.auxiliarybar.dc317488-ed68-4856-acfa-2043ecae1350\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.extension.claude-dev-ActivityBar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13}]\",\"colorThemeData\":\"{\\\"id\\\":\\\"vs-dark Xen-ccpp-theme-themes-ccpp_theme-json\\\",\\\"label\\\":\\\"C/C++ Theme\\\",\\\"settingsId\\\":\\\"C/C++ Theme\\\",\\\"themeTokenColors\\\":[{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"emphasis\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"strong\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"header\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6272A4\\\"},\\\"scope\\\":[\\\"meta.diff\\\",\\\"meta.diff.header\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"markup.inserted\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\"},\\\"scope\\\":[\\\"markup.deleted\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"markup.changed\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\",\\\"fontStyle\\\":\\\"underline italic\\\"},\\\"scope\\\":[\\\"invalid\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\",\\\"fontStyle\\\":\\\"underline italic\\\"},\\\"scope\\\":[\\\"invalid.deprecated\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"entity.name.filename\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\"},\\\"scope\\\":[\\\"markup.error\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"underline\\\"},\\\"scope\\\":[\\\"markup.underline\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"markup.bold\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"markup.heading\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"markup.italic\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"beginning.punctuation.definition.list.markdown\\\",\\\"beginning.punctuation.definition.quote.markdown\\\",\\\"punctuation.definition.link.restructuredtext\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"markup.inline.raw\\\",\\\"markup.raw.restructuredtext\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"markup.underline.link\\\",\\\"markup.underline.link.image\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"meta.link.reference.def.restructuredtext\\\",\\\"punctuation.definition.directive.restructuredtext\\\",\\\"string.other.link.description\\\",\\\"string.other.link.title\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"entity.name.directive.restructuredtext\\\",\\\"markup.quote\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6272A4\\\"},\\\"scope\\\":[\\\"meta.separator.markdown\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"fenced_code.block.language\\\",\\\"markup.raw.inner.restructuredtext\\\",\\\"markup.fenced_code.block.markdown punctuation.definition.markdown\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"punctuation.definition.constant.restructuredtext\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"markup.heading.markdown punctuation.definition.string.begin\\\",\\\"markup.heading.markdown punctuation.definition.string.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"meta.paragraph.markdown punctuation.definition.string.begin\\\",\\\"meta.paragraph.markdown punctuation.definition.string.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"markup.quote.markdown meta.paragraph.markdown punctuation.definition.string.begin\\\",\\\"markup.quote.markdown meta.paragraph.markdown punctuation.definition.string.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\",\\\"fontStyle\\\":\\\"normal\\\"},\\\"scope\\\":[\\\"entity.name.type.class\\\",\\\"entity.name.class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BD93F9\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"keyword.expressions-and-types.swift\\\",\\\"keyword.other.this\\\",\\\"variable.language\\\",\\\"variable.language punctuation.definition.variable.php\\\",\\\"variable.other.readwrite.instance.ruby\\\",\\\"variable.parameter.function.language.special\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"entity.other.inherited-class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6272A4\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"comment\\\",\\\"punctuation.definition.comment\\\",\\\"unused.comment\\\",\\\"wildcard.comment\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"comment keyword.codetag.notation\\\",\\\"comment.block.documentation keyword\\\",\\\"comment.block.documentation storage.type.class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"comment.block.documentation entity.name.type\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"comment.block.documentation entity.name.type punctuation.definition.bracket\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FFB86C\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"comment.block.documentation variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"constant\\\",\\\"variable.other.constant\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"constant.character.escape\\\",\\\"constant.character.string.escape\\\",\\\"constant.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"entity.name.tag\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"entity.other.attribute-name.parent-selector\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"entity.other.attribute-name\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"entity.name.function\\\",\\\"meta.function-call.object\\\",\\\"meta.function-call.php\\\",\\\"meta.function-call.static\\\",\\\"meta.method-call.java meta.method\\\",\\\"meta.method.groovy\\\",\\\"support.function.any-method.lua\\\",\\\"keyword.operator.function.infix\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"entity.name.variable.parameter\\\",\\\"meta.at-rule.function variable\\\",\\\"meta.at-rule.mixin variable\\\",\\\"meta.function.arguments variable.other.php\\\",\\\"meta.selectionset.graphql meta.arguments.graphql variable.arguments.graphql\\\",\\\"variable.parameter\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"meta.decorator variable.other.readwrite\\\",\\\"meta.decorator variable.other.property\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"meta.decorator variable.other.object\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"keyword\\\",\\\"punctuation.definition.keyword\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"keyword.control.new\\\",\\\"keyword.operator.new\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"meta.selector\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"support\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"regular\\\",\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"support.function.magic\\\",\\\"support.variable\\\",\\\"variable.other.predefined\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"support.function\\\",\\\"support.type.property-name\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"constant.other.symbol.hashkey punctuation.definition.constant.ruby\\\",\\\"entity.other.attribute-name.placeholder punctuation\\\",\\\"entity.other.attribute-name.pseudo-class punctuation\\\",\\\"entity.other.attribute-name.pseudo-element punctuation\\\",\\\"meta.group.double.toml\\\",\\\"meta.group.toml\\\",\\\"meta.object-binding-pattern-variable punctuation.destructuring\\\",\\\"punctuation.colon.graphql\\\",\\\"punctuation.definition.block.scalar.folded.yaml\\\",\\\"punctuation.definition.block.scalar.literal.yaml\\\",\\\"punctuation.definition.block.sequence.item.yaml\\\",\\\"punctuation.definition.entity.other.inherited-class\\\",\\\"punctuation.function.swift\\\",\\\"punctuation.separator.dictionary.key-value\\\",\\\"punctuation.separator.hash\\\",\\\"punctuation.separator.inheritance\\\",\\\"punctuation.separator.key-value\\\",\\\"punctuation.separator.key-value.mapping.yaml\\\",\\\"punctuation.separator.namespace\\\",\\\"punctuation.separator.pointer-access\\\",\\\"punctuation.separator.slice\\\",\\\"string.unquoted.heredoc punctuation.definition.string\\\",\\\"support.other.chomping-indicator.yaml\\\",\\\"punctuation.separator.annotation\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"keyword.operator.other.powershell\\\",\\\"keyword.other.statement-separator.powershell\\\",\\\"meta.brace.round\\\",\\\"meta.function-call punctuation\\\",\\\"punctuation.definition.arguments.begin\\\",\\\"punctuation.definition.arguments.end\\\",\\\"punctuation.definition.entity.begin\\\",\\\"punctuation.definition.entity.end\\\",\\\"punctuation.definition.tag.cs\\\",\\\"punctuation.definition.type.begin\\\",\\\"punctuation.definition.type.end\\\",\\\"punctuation.section.scope.begin\\\",\\\"punctuation.section.scope.end\\\",\\\"punctuation.terminator.expression.php\\\",\\\"storage.type.generic.java\\\",\\\"string.template meta.brace\\\",\\\"string.template punctuation.accessor\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"meta.string-contents.quoted.double punctuation.definition.variable\\\",\\\"punctuation.definition.interpolation.begin\\\",\\\"punctuation.definition.interpolation.end\\\",\\\"punctuation.definition.template-expression.begin\\\",\\\"punctuation.definition.template-expression.end\\\",\\\"punctuation.section.embedded.begin\\\",\\\"punctuation.section.embedded.coffee\\\",\\\"punctuation.section.embedded.end\\\",\\\"punctuation.section.embedded.end source.php\\\",\\\"punctuation.section.embedded.end source.ruby\\\",\\\"punctuation.definition.variable.makefile\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"entity.name.function.target.makefile\\\",\\\"entity.name.section.toml\\\",\\\"entity.name.tag.yaml\\\",\\\"variable.other.key.toml\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"constant.other.date\\\",\\\"constant.other.timestamp\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic underline\\\",\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"variable.other.alias.yaml\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"regular\\\",\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"storage\\\",\\\"meta.implementation storage.type.objc\\\",\\\"meta.interface-or-protocol storage.type.objc\\\",\\\"source.groovy storage.type.def\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"entity.name.type\\\",\\\"keyword.primitive-datatypes.swift\\\",\\\"keyword.type.cs\\\",\\\"meta.protocol-list.objc\\\",\\\"meta.return-type.objc\\\",\\\"source.go storage.type\\\",\\\"source.groovy storage.type\\\",\\\"source.java storage.type\\\",\\\"source.powershell entity.other.attribute-name\\\",\\\"storage.class.std.rust\\\",\\\"storage.type.attribute.swift\\\",\\\"storage.type.c\\\",\\\"storage.type.core.rust\\\",\\\"storage.type.cs\\\",\\\"storage.type.groovy\\\",\\\"storage.type.objc\\\",\\\"storage.type.php\\\",\\\"storage.type.haskell\\\",\\\"storage.type.ocaml\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"entity.name.type.type-parameter\\\",\\\"meta.indexer.mappedtype.declaration entity.name.type\\\",\\\"meta.type.parameters entity.name.type\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"storage.modifier\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"string.regexp\\\",\\\"constant.other.character-class.set.regexp\\\",\\\"constant.character.escape.backslash.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"punctuation.definition.group.capture.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\"},\\\"scope\\\":[\\\"string.regexp punctuation.definition.string.begin\\\",\\\"string.regexp punctuation.definition.string.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"punctuation.definition.character-class.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"punctuation.definition.group.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\"},\\\"scope\\\":[\\\"punctuation.definition.group.assertion.regexp\\\",\\\"keyword.operator.negation.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"meta.assertion.look-ahead.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"string\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E9F284\\\"},\\\"scope\\\":[\\\"punctuation.definition.string.begin\\\",\\\"punctuation.definition.string.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FE\\\"},\\\"scope\\\":[\\\"punctuation.support.type.property-name.begin\\\",\\\"punctuation.support.type.property-name.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6272A4\\\"},\\\"scope\\\":[\\\"string.quoted.docstring.multi\\\",\\\"string.quoted.docstring.multi.python punctuation.definition.string.begin\\\",\\\"string.quoted.docstring.multi.python punctuation.definition.string.end\\\",\\\"string.quoted.docstring.multi.python constant.character.escape\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"variable\\\",\\\"constant.other.key.perl\\\",\\\"support.variable.property\\\",\\\"variable.other.constant.js\\\",\\\"variable.other.constant.ts\\\",\\\"variable.other.constant.tsx\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"meta.import variable.other.readwrite\\\",\\\"meta.variable.assignment.destructured.object.coffee variable\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"normal\\\",\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"meta.import variable.other.readwrite.alias\\\",\\\"meta.export variable.other.readwrite.alias\\\",\\\"meta.variable.assignment.destructured.object.coffee variable variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"meta.selectionset.graphql variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"meta.selectionset.graphql meta.arguments variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"entity.name.fragment.graphql\\\",\\\"variable.fragment.graphql\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"constant.other.symbol.hashkey.ruby\\\",\\\"keyword.operator.dereference.java\\\",\\\"keyword.operator.navigation.groovy\\\",\\\"meta.scope.for-loop.shell punctuation.definition.string.begin\\\",\\\"meta.scope.for-loop.shell punctuation.definition.string.end\\\",\\\"meta.scope.for-loop.shell string\\\",\\\"storage.modifier.import\\\",\\\"punctuation.section.embedded.begin.tsx\\\",\\\"punctuation.section.embedded.end.tsx\\\",\\\"punctuation.section.embedded.begin.jsx\\\",\\\"punctuation.section.embedded.end.jsx\\\",\\\"punctuation.separator.list.comma.css\\\",\\\"constant.language.empty-list.haskell\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"source.shell variable.other\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"normal\\\",\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"support.constant\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"meta.scope.prerequisites.makefile\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"meta.attribute-selector.scss\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"punctuation.definition.attribute-selector.end.bracket.square.scss\\\",\\\"punctuation.definition.attribute-selector.begin.bracket.square.scss\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6272A4\\\"},\\\"scope\\\":[\\\"meta.preprocessor.haskell\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\",\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"log.error\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\",\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"log.warning\\\"]},{},{},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"storage.modifier\\\",\\\"storage.type\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"storage.type.built-in\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"keyword\\\",\\\"entity.name.operatorv\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"keyword.operator.logical\\\",\\\"keyword.operator.comparison\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"punctuation.separator.pointer-access\\\",\\\"punctuation.separator.dot-access\\\",\\\"punctuation.section\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"constant.numeric\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"string\\\",\\\"string.quoted.double\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\",\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"entity.name.namespace\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\",\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"meta.preprocessor\\\",\\\"meta.preprocessor.macro\\\"]},{},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"meta.function-call.c\\\",\\\"meta.function.definition.c\\\",\\\"entity.name.function.c\\\",\\\"entity.name.function.definition.cpp\\\",\\\"entity.name.function.call.cpp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"entity.name.function.member.cpp\\\"]}],\\\"semanticTokenRules\\\":[{\\\"_selector\\\":\\\"TODO: ccpp_comment1\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff0000\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"memberOperatorOverload\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"operatorOverload\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"type\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#8be9fd\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"class\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#8be9fd\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"enum\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#8be9fd\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"macro\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff79c6\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"enumMember\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#8be9fd\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"label\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":false,\\\"_underline\\\":true,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"TODO.1: ccpp_comment1\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff0000\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"TODO.0.1: ccpp_comment1\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff0000\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"method.classScope.virtual\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":true,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"method.classScope.declaration.virtual\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":true,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"method.classScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"method.classScope.declaration\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.fileScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.declaration.fileScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"method\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.classScope.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.classScope.declaration.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.globalScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.declaration.globalScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.defaultLibrary\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"TODO.1.1: ccpp_comment1\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff0000\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"method.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"TODO.2: ccpp_comment1\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff0000\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"property\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#bcaaa4\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"parameter\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ffb86c\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"parameter.readonly\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ffb86c\\\",\\\"_bold\\\":false,\\\"_underline\\\":true,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.functionScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ffb86c\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.local\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ffb86c\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.functionScope.readonly\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ffb86c\\\",\\\"_bold\\\":false,\\\"_underline\\\":true,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.functionScope.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.classScope.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.fileScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"property.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.classScope.readonly.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":true,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.fileScope.readonly\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":true,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.globalScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.global\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.globalScope.readonly\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":true,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}}],\\\"extensionData\\\":{\\\"_extensionId\\\":\\\"Xen.ccpp-theme\\\",\\\"_extensionIsBuiltin\\\":false,\\\"_extensionName\\\":\\\"ccpp-theme\\\",\\\"_extensionPublisher\\\":\\\"Xen\\\"},\\\"themeSemanticHighlighting\\\":true,\\\"colorMap\\\":{\\\"terminal.background\\\":\\\"#282a36\\\",\\\"terminal.foreground\\\":\\\"#f5f5ef\\\",\\\"terminal.ansiBrightBlack\\\":\\\"#6272a4\\\",\\\"terminal.ansiBrightRed\\\":\\\"#ff6e6e\\\",\\\"terminal.ansiBrightGreen\\\":\\\"#69ff94\\\",\\\"terminal.ansiBrightYellow\\\":\\\"#ffffa5\\\",\\\"terminal.ansiBrightBlue\\\":\\\"#d6acff\\\",\\\"terminal.ansiBrightMagenta\\\":\\\"#ff92df\\\",\\\"terminal.ansiBrightCyan\\\":\\\"#a4ffff\\\",\\\"terminal.ansiBrightWhite\\\":\\\"#ffffff\\\",\\\"terminal.ansiBlack\\\":\\\"#21222c\\\",\\\"terminal.ansiRed\\\":\\\"#ff5555\\\",\\\"terminal.ansiGreen\\\":\\\"#5fea77\\\",\\\"terminal.ansiYellow\\\":\\\"#eaf48c\\\",\\\"terminal.ansiBlue\\\":\\\"#bd93f9\\\",\\\"terminal.ansiMagenta\\\":\\\"#ff79c6\\\",\\\"terminal.ansiCyan\\\":\\\"#8be9fd\\\",\\\"terminal.ansiWhite\\\":\\\"#f5f5ef\\\",\\\"focusBorder\\\":\\\"#6272a4\\\",\\\"foreground\\\":\\\"#f5f5ef\\\",\\\"selection.background\\\":\\\"#bd93f9\\\",\\\"errorForeground\\\":\\\"#ff5555\\\",\\\"button.background\\\":\\\"#44475a\\\",\\\"button.foreground\\\":\\\"#f5f5ef\\\",\\\"button.secondaryBackground\\\":\\\"#282a36\\\",\\\"button.secondaryForeground\\\":\\\"#f5f5ef\\\",\\\"button.secondaryHoverBackground\\\":\\\"#343746\\\",\\\"dropdown.background\\\":\\\"#343746\\\",\\\"dropdown.border\\\":\\\"#191a21\\\",\\\"dropdown.foreground\\\":\\\"#f5f5ef\\\",\\\"input.background\\\":\\\"#282a36\\\",\\\"input.foreground\\\":\\\"#f5f5ef\\\",\\\"input.border\\\":\\\"#191a21\\\",\\\"input.placeholderForeground\\\":\\\"#6272a4\\\",\\\"inputOption.activeBorder\\\":\\\"#bd93f9\\\",\\\"inputValidation.infoBorder\\\":\\\"#ff79c6\\\",\\\"inputValidation.warningBorder\\\":\\\"#ffb86c\\\",\\\"inputValidation.errorBorder\\\":\\\"#ff5555\\\",\\\"badge.foreground\\\":\\\"#f5f5ef\\\",\\\"badge.background\\\":\\\"#44475a\\\",\\\"progressBar.background\\\":\\\"#ff79c6\\\",\\\"list.activeSelectionBackground\\\":\\\"#44475a\\\",\\\"list.activeSelectionForeground\\\":\\\"#f5f5ef\\\",\\\"list.dropBackground\\\":\\\"#44475a\\\",\\\"list.focusBackground\\\":\\\"#44475a75\\\",\\\"list.highlightForeground\\\":\\\"#8be9fd\\\",\\\"list.hoverBackground\\\":\\\"#44475a75\\\",\\\"list.inactiveSelectionBackground\\\":\\\"#5fea7750\\\",\\\"list.warningForeground\\\":\\\"#ffb86c\\\",\\\"list.errorForeground\\\":\\\"#ff5555\\\",\\\"activityBar.background\\\":\\\"#191a21\\\",\\\"activityBar.inactiveForeground\\\":\\\"#6272a4\\\",\\\"activityBar.foreground\\\":\\\"#f5f5ef\\\",\\\"activityBar.activeBorder\\\":\\\"#ff79c680\\\",\\\"activityBar.activeBackground\\\":\\\"#bd93f910\\\",\\\"activityBarBadge.background\\\":\\\"#ff79c6\\\",\\\"activityBarBadge.foreground\\\":\\\"#f5f5ef\\\",\\\"sideBar.background\\\":\\\"#21222c\\\",\\\"sideBarTitle.foreground\\\":\\\"#f5f5ef\\\",\\\"sideBarSectionHeader.background\\\":\\\"#282a36\\\",\\\"sideBarSectionHeader.border\\\":\\\"#191a21\\\",\\\"editorGroup.border\\\":\\\"#bd93f9\\\",\\\"editorGroup.dropBackground\\\":\\\"#44475a70\\\",\\\"editorGroupHeader.tabsBackground\\\":\\\"#191a21\\\",\\\"tab.activeBackground\\\":\\\"#424450\\\",\\\"tab.activeForeground\\\":\\\"#f5f5ef\\\",\\\"tab.border\\\":\\\"#191a21\\\",\\\"tab.activeBorderTop\\\":\\\"#ff79c680\\\",\\\"tab.inactiveBackground\\\":\\\"#21222c\\\",\\\"tab.inactiveForeground\\\":\\\"#6272a4\\\",\\\"editor.foreground\\\":\\\"#f5f5ef\\\",\\\"editor.background\\\":\\\"#282a36\\\",\\\"editorLineNumber.foreground\\\":\\\"#6272a4\\\",\\\"editor.selectionBackground\\\":\\\"#5fea7740\\\",\\\"editor.selectionHighlightBackground\\\":\\\"#5fea7780\\\",\\\"editor.foldBackground\\\":\\\"#21222c80\\\",\\\"editor.wordHighlightBackground\\\":\\\"#8be9fd50\\\",\\\"editor.wordHighlightStrongBackground\\\":\\\"#5fea7750\\\",\\\"editor.findMatchBackground\\\":\\\"#ffb86cd0\\\",\\\"editor.findMatchHighlightBackground\\\":\\\"#ffffff40\\\",\\\"editor.findRangeHighlightBackground\\\":\\\"#44475a75\\\",\\\"editor.hoverHighlightBackground\\\":\\\"#8be9fd50\\\",\\\"editor.lineHighlightBorder\\\":\\\"#5fea7740\\\",\\\"editorLink.activeForeground\\\":\\\"#8be9fd\\\",\\\"editor.rangeHighlightBackground\\\":\\\"#bd93f915\\\",\\\"editor.snippetTabstopHighlightBackground\\\":\\\"#282a36\\\",\\\"editor.snippetTabstopHighlightBorder\\\":\\\"#6272a4\\\",\\\"editor.snippetFinalTabstopHighlightBackground\\\":\\\"#282a36\\\",\\\"editor.snippetFinalTabstopHighlightBorder\\\":\\\"#5fea77\\\",\\\"editorWhitespace.foreground\\\":\\\"#ffffff1a\\\",\\\"editorIndentGuide.background\\\":\\\"#ffffff1a\\\",\\\"editorIndentGuide.activeBackground\\\":\\\"#ffffff45\\\",\\\"editorRuler.foreground\\\":\\\"#ffffff1a\\\",\\\"editorCodeLens.foreground\\\":\\\"#6272a4\\\",\\\"editorBracketHighlight.foreground1\\\":\\\"#f5f5ef\\\",\\\"editorBracketHighlight.foreground2\\\":\\\"#ff79c6\\\",\\\"editorBracketHighlight.foreground3\\\":\\\"#8be9fd\\\",\\\"editorBracketHighlight.foreground4\\\":\\\"#5fea77\\\",\\\"editorBracketHighlight.foreground5\\\":\\\"#bd93f9\\\",\\\"editorBracketHighlight.foreground6\\\":\\\"#ffb86c\\\",\\\"editorBracketHighlight.unexpectedBracket.foreground\\\":\\\"#ff5555\\\",\\\"editorOverviewRuler.border\\\":\\\"#191a21\\\",\\\"editorOverviewRuler.selectionHighlightForeground\\\":\\\"#ffb86c\\\",\\\"editorOverviewRuler.wordHighlightForeground\\\":\\\"#8be9fd\\\",\\\"editorOverviewRuler.wordHighlightStrongForeground\\\":\\\"#5fea77\\\",\\\"editorOverviewRuler.modifiedForeground\\\":\\\"#8be9fd80\\\",\\\"editorOverviewRuler.addedForeground\\\":\\\"#5fea7780\\\",\\\"editorOverviewRuler.deletedForeground\\\":\\\"#ff555580\\\",\\\"editorOverviewRuler.errorForeground\\\":\\\"#ff555580\\\",\\\"editorOverviewRuler.warningForeground\\\":\\\"#ffb86c80\\\",\\\"editorOverviewRuler.infoForeground\\\":\\\"#8be9fd80\\\",\\\"editorError.foreground\\\":\\\"#ff5555\\\",\\\"editorWarning.foreground\\\":\\\"#8be9fd\\\",\\\"editorGutter.modifiedBackground\\\":\\\"#8be9fd80\\\",\\\"editorGutter.addedBackground\\\":\\\"#5fea7780\\\",\\\"editorGutter.deletedBackground\\\":\\\"#ff555580\\\",\\\"gitDecoration.modifiedResourceForeground\\\":\\\"#8be9fd\\\",\\\"gitDecoration.deletedResourceForeground\\\":\\\"#ff5555\\\",\\\"gitDecoration.untrackedResourceForeground\\\":\\\"#5fea77\\\",\\\"gitDecoration.ignoredResourceForeground\\\":\\\"#6272a4\\\",\\\"gitDecoration.conflictingResourceForeground\\\":\\\"#ffb86c\\\",\\\"diffEditor.insertedTextBackground\\\":\\\"#5fea7720\\\",\\\"diffEditor.removedTextBackground\\\":\\\"#ff555550\\\",\\\"editorWidget.background\\\":\\\"#21222c\\\",\\\"editorSuggestWidget.background\\\":\\\"#21222c\\\",\\\"editorSuggestWidget.foreground\\\":\\\"#f5f5ef\\\",\\\"editorSuggestWidget.selectedBackground\\\":\\\"#44475a\\\",\\\"editorHoverWidget.background\\\":\\\"#282a36\\\",\\\"editorHoverWidget.border\\\":\\\"#6272a4\\\",\\\"editorMarkerNavigation.background\\\":\\\"#21222c\\\",\\\"peekView.border\\\":\\\"#44475a\\\",\\\"peekViewEditor.background\\\":\\\"#282a36\\\",\\\"peekViewEditor.matchHighlightBackground\\\":\\\"#f1fa8c80\\\",\\\"peekViewResult.background\\\":\\\"#21222c\\\",\\\"peekViewResult.fileForeground\\\":\\\"#f5f5ef\\\",\\\"peekViewResult.lineForeground\\\":\\\"#f5f5ef\\\",\\\"peekViewResult.matchHighlightBackground\\\":\\\"#f1fa8c80\\\",\\\"peekViewResult.selectionBackground\\\":\\\"#44475a\\\",\\\"peekViewResult.selectionForeground\\\":\\\"#f5f5ef\\\",\\\"peekViewTitle.background\\\":\\\"#191a21\\\",\\\"peekViewTitleDescription.foreground\\\":\\\"#6272a4\\\",\\\"peekViewTitleLabel.foreground\\\":\\\"#f5f5ef\\\",\\\"merge.currentHeaderBackground\\\":\\\"#5fea7790\\\",\\\"merge.incomingHeaderBackground\\\":\\\"#bd93f990\\\",\\\"editorOverviewRuler.currentContentForeground\\\":\\\"#5fea77\\\",\\\"editorOverviewRuler.incomingContentForeground\\\":\\\"#bd93f9\\\",\\\"panel.background\\\":\\\"#282a36\\\",\\\"panel.border\\\":\\\"#bd93f9\\\",\\\"panelTitle.activeBorder\\\":\\\"#ff79c6\\\",\\\"panelTitle.activeForeground\\\":\\\"#f5f5ef\\\",\\\"panelTitle.inactiveForeground\\\":\\\"#6272a4\\\",\\\"statusBar.background\\\":\\\"#191a21\\\",\\\"statusBar.foreground\\\":\\\"#f5f5ef\\\",\\\"statusBar.debuggingBackground\\\":\\\"#ff5555\\\",\\\"statusBar.debuggingForeground\\\":\\\"#191a21\\\",\\\"statusBar.noFolderBackground\\\":\\\"#191a21\\\",\\\"statusBar.noFolderForeground\\\":\\\"#f5f5ef\\\",\\\"statusBarItem.prominentBackground\\\":\\\"#ff5555\\\",\\\"statusBarItem.prominentHoverBackground\\\":\\\"#ffb86c\\\",\\\"statusBarItem.remoteForeground\\\":\\\"#282a36\\\",\\\"statusBarItem.remoteBackground\\\":\\\"#bd93f9\\\",\\\"titleBar.activeBackground\\\":\\\"#191a21\\\",\\\"titleBar.activeForeground\\\":\\\"#69ff94\\\",\\\"titleBar.inactiveBackground\\\":\\\"#44475c\\\",\\\"titleBar.inactiveForeground\\\":\\\"#69ff94\\\",\\\"extensionButton.prominentForeground\\\":\\\"#f5f5ef\\\",\\\"extensionButton.prominentBackground\\\":\\\"#5fea7790\\\",\\\"extensionButton.prominentHoverBackground\\\":\\\"#5fea7760\\\",\\\"pickerGroup.border\\\":\\\"#bd93f9\\\",\\\"pickerGroup.foreground\\\":\\\"#8be9fd\\\",\\\"debugToolBar.background\\\":\\\"#21222c\\\",\\\"walkThrough.embeddedEditorBackground\\\":\\\"#21222c\\\",\\\"settings.headerForeground\\\":\\\"#f5f5ef\\\",\\\"settings.modifiedItemIndicator\\\":\\\"#ffb86c\\\",\\\"settings.dropdownBackground\\\":\\\"#21222c\\\",\\\"settings.dropdownForeground\\\":\\\"#f5f5ef\\\",\\\"settings.dropdownBorder\\\":\\\"#191a21\\\",\\\"settings.checkboxBackground\\\":\\\"#21222c\\\",\\\"settings.checkboxForeground\\\":\\\"#f5f5ef\\\",\\\"settings.checkboxBorder\\\":\\\"#191a21\\\",\\\"settings.textInputBackground\\\":\\\"#21222c\\\",\\\"settings.textInputForeground\\\":\\\"#f5f5ef\\\",\\\"settings.textInputBorder\\\":\\\"#191a21\\\",\\\"settings.numberInputBackground\\\":\\\"#21222c\\\",\\\"settings.numberInputForeground\\\":\\\"#f5f5ef\\\",\\\"settings.numberInputBorder\\\":\\\"#191a21\\\",\\\"breadcrumb.foreground\\\":\\\"#6272a4\\\",\\\"breadcrumb.background\\\":\\\"#282a36\\\",\\\"breadcrumb.focusForeground\\\":\\\"#f5f5ef\\\",\\\"breadcrumb.activeSelectionForeground\\\":\\\"#f5f5ef\\\",\\\"breadcrumbPicker.background\\\":\\\"#191a21\\\",\\\"listFilterWidget.background\\\":\\\"#343746\\\",\\\"listFilterWidget.outline\\\":\\\"#424450\\\",\\\"listFilterWidget.noMatchesOutline\\\":\\\"#ff5555\\\"},\\\"watch\\\":false}\",\"workbench.view.debug.state.hidden\":\"[{\\\"id\\\":\\\"workbench.debug.welcome\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.variablesView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.watchExpressionsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.callStackView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.loadedScriptsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.breakPointsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsBrowserBreakpoints\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsExcludedCallers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"CppSshTargetsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"lldb.loadedModules\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"lldb.excludedCallers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsDebugNetworkTree\\\",\\\"isHidden\\\":false}]\",\"nps/lastSessionDate\":\"Thu Jul 25 2024\",\"nps/sessionCount\":\"9\",\"~remote.forwardedPortsContainer.hidden\":\"[{\\\"id\\\":\\\"~remote.forwardedPorts\\\",\\\"isHidden\\\":false}]\",\"cpp.1.lastSessionDate\":\"Tue May 13 2025\",\"cpp.1.sessionCount\":\"25\",\"java.2.lastSessionDate\":\"Tue May 13 2025\",\"java.2.sessionCount\":\"25\",\"javascript.1.lastSessionDate\":\"Tue May 13 2025\",\"javascript.1.sessionCount\":\"25\",\"typescript.1.lastSessionDate\":\"Tue May 13 2025\",\"typescript.1.sessionCount\":\"25\",\"csharp.1.lastSessionDate\":\"Tue May 13 2025\",\"csharp.1.sessionCount\":\"25\",\"workbench.telemetryOptOutShown\":\"true\",\"no-updates-running-as-admin\":\"true\",\"workbench.panel.alignment\":\"center\",\"themeUpdatedNotificationShown\":\"true\",\"workbench.view.extensions.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.extensions.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchOutdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.workspaceRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.popular\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchRecentlyUpdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.otherRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"extensions.recommendedList\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.enabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.disabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.marketplace\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchInstalled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchEnabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchDisabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchBuiltin\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchWorkspaceUnsupported\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinFeatureExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinThemeExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinProgrammingLanguageExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.deprecatedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.local.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.remote.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.mcp.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.mcp.default.marketplace\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.mcp.marketplace\\\",\\\"isHidden\\\":false}]\",\"Comments.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"isHidden\\\":false}]\",\"ces/skipSurvey\":\"1.92.2\",\"encryption.migratedToGnomeLibsecret\":\"true\",\"fileBasedRecommendations/promptedRecommendations\":\"{\\\"c\\\":[\\\"ms-vscode.cpptools-extension-pack\\\"]}\",\"http.linkProtectionTrustedDomains\":\"[\\\"https://www.codeium.com\\\",\\\"*\\\"]\",\"memento/gettingStartedService\":\"{\\\"pickColorTheme\\\":{\\\"done\\\":true},\\\"pickColorThemeWeb\\\":{\\\"done\\\":true},\\\"newPickColorTheme\\\":{\\\"done\\\":true},\\\"terminal\\\":{\\\"done\\\":true},\\\"installGit\\\":{\\\"done\\\":true}}\",\"<EMAIL>\":\"[{\\\"id\\\":\\\"ms-vscode.remote-server\\\",\\\"name\\\":\\\"Remote - Tunnels\\\",\\\"allowed\\\":true}]\",\"terminal.integrated.showTerminalConfigPrompt\":\"false\",\"views.customizations\":\"{\\\"viewContainerLocations\\\":{\\\"workbench.views.service.auxiliarybar.ab051650-a8ed-47eb-9f38-fe25e83a4070\\\":2,\\\"workbench.view.extension.outline-map\\\":2,\\\"workbench.views.service.auxiliarybar.dc317488-ed68-4856-acfa-2043ecae1350\\\":2,\\\"workbench.view.extension.claude-dev-ActivityBar\\\":2},\\\"viewLocations\\\":{\\\"outline\\\":\\\"workbench.views.service.auxiliarybar.ab051650-a8ed-47eb-9f38-fe25e83a4070\\\",\\\"augment-chat\\\":\\\"workbench.panel.chat\\\",\\\"workbench.panel.chat.view.copilot\\\":\\\"workbench.views.service.auxiliarybar.dc317488-ed68-4856-acfa-2043ecae1350\\\"},\\\"viewContainerBadgeEnablementStates\\\":{}}\",\"workbench.panel.chatSidebar.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.repl.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.repl.view\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.TongyiLingma.state.hidden\":\"[{\\\"id\\\":\\\"TongyiLingMa.Chat\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.bookmarks.state.hidden\":\"[{\\\"id\\\":\\\"bookmarksExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"bookmarksHelpAndFeedback\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.chatgpt-china-view-container.state.hidden\":\"[{\\\"id\\\":\\\"chatgpt-china\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.cloudmusic.state.hidden\":\"[{\\\"id\\\":\\\"cloudmusic-account\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudmusic-local\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudmusic-playlist\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudmusic-radio\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudmusic-queue\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.cmake-view.state.hidden\":\"[{\\\"id\\\":\\\"cmake.projectStatus\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cmake.outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cmake.pinnedCommands\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.codegeex-sidebar.state.hidden\":\"[{\\\"id\\\":\\\"codegeex-qa\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.codeium.state.hidden\":\"[{\\\"id\\\":\\\"codeium.chatPanelView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"codeium.searchPanelView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.coder-maker-sidebar.state.hidden\":\"[{\\\"id\\\":\\\"coder-maker-npm-manager\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"coder-maker-bk\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.codestream-activitybar.state.hidden\":\"[{\\\"id\\\":\\\"activitybar.codestream\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.dockerView.state.hidden\":\"[{\\\"id\\\":\\\"dockerContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerImages\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerRegistries\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerNetworks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerVolumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.dockerContexts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.help\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.foldersCompare.state.hidden\":\"[{\\\"id\\\":\\\"foldersCompareAppService\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"foldersCompareAppServiceOnlyA\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"foldersCompareAppServiceOnlyB\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"foldersCompareAppServiceIdenticals\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-pull-request.state.hidden\":\"[{\\\"id\\\":\\\"github:createPullRequestWebview\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChangesFiles\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChangesCommits\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"prStatus:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest:welcome\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-pull-requests.state.hidden\":\"[{\\\"id\\\":\\\"github:login\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"pr:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"issues:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:conflictResolution\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"notifications:github\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlens.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.home\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.drafts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.workspaces\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.account\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.launchpad\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlensInspect.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.commitDetails\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.lineHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.fileHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.searchAndCompare\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.pullRequest\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlensPanel.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.graph\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.graphDetails\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.json-views.state.hidden\":\"[{\\\"id\\\":\\\"jsonOutline\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.krinql.state.hidden\":\"[{\\\"id\\\":\\\"loginView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sideBarView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.local-history.state.hidden\":\"[{\\\"id\\\":\\\"localHistoryFileBrowser\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"localHistoryDiffBrowser\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.makefile-command-runner.state.hidden\":\"[{\\\"id\\\":\\\"makefile\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.makefile__viewContainer.state.hidden\":\"[{\\\"id\\\":\\\"makefile.outline\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.marquee.state.hidden\":\"[{\\\"id\\\":\\\"marquee\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.mintdocs.state.hidden\":\"[{\\\"id\\\":\\\"docs\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"formatOptions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"progress\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"languageOptions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"team\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"hotkeyOptions\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.outline-map.state.hidden\":\"[{\\\"id\\\":\\\"outline-map-view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"outline-map-workspace\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.package-explorer.state.hidden\":\"[{\\\"id\\\":\\\"workspaceEnvironments\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"pythonEnvironments\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.project-manager.state.hidden\":\"[{\\\"id\\\":\\\"projectsExplorerFavorites\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerGit\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerSVN\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerAny\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerMercurial\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerVSCode\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectManagerHelpAndFeedback\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.sourcegraph-view.state.hidden\":\"[{\\\"id\\\":\\\"sourcegraph.searchSidebar\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sourcegraph.files\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sourcegraph.helpSidebar\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sourcegraph.authSidebar\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.tabnine.state.hidden\":\"[{\\\"id\\\":\\\"tabnine.chat\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"tabnine.chat.preview_ended\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"tabnine.chat.authenticate\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"tabnine.chat.not_part_of_a_team\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"tabnine.loading\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.thunder-client.state.hidden\":\"[{\\\"id\\\":\\\"thunder-client-sidebar\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.todo-tree-container.state.hidden\":\"[{\\\"id\\\":\\\"todo-tree-view\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.todo.state.hidden\":\"[{\\\"id\\\":\\\"todo.views.1files\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"todo.views.2embedded\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.vscode-edge-devtools-view.state.hidden\":\"[{\\\"id\\\":\\\"vscode-edge-devtools-view.targets\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-edge-devtools-view.help-links\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.zhihu-explorer.state.hidden\":\"[{\\\"id\\\":\\\"zhihu-feed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"zhihu-hotStories\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"zhihu-collection\\\",\\\"isHidden\\\":false}]\",\"workbench.view.remote.state.hidden\":\"[{\\\"id\\\":\\\"github.codespaces.explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github.codespaces.warnExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github.codespaces.performanceExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"targetsContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"detailsContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"devVolumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"remoteTargets\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"targetsWsl\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"remoteHub.views.workspaceRepositories\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"~remote.helpPanel\\\",\\\"isHidden\\\":true}]\",\"workbench.views.service.auxiliarybar.ab051650-a8ed-47eb-9f38-fe25e83a4070.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false}]\",\"workbench.welcomePage.walkthroughMetadata\":\"[[\\\"alefragnani.project-manager#projectManagerWelcome\\\",{\\\"firstSeen\\\":1753744762464,\\\"stepIDs\\\":[\\\"saveYourFavoriteProjects\\\",\\\"autoDetectGitRepositories\\\",\\\"findAndOpenProjects\\\",\\\"organizeWithTags\\\",\\\"exclusiveSideBar\\\",\\\"workingWithRemotes\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-vscode-remote.remote-wsl#wslWalkthrough\\\",{\\\"firstSeen\\\":1753744762464,\\\"stepIDs\\\":[\\\"explore.commands\\\",\\\"open.wslwindow\\\",\\\"create.project\\\",\\\"open.project\\\",\\\"linux.environment\\\",\\\"install.tools\\\",\\\"run.debug\\\",\\\"come.back\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-vscode.remote-repositories#remoteRepositoriesWalkthrough\\\",{\\\"firstSeen\\\":1753744762464,\\\"stepIDs\\\":[\\\"editCommitRepo\\\",\\\"createGitHubPullRequest\\\",\\\"continueOn\\\",\\\"openRepo\\\",\\\"remoteIndicator\\\"],\\\"manaullyOpened\\\":false}],[\\\"GitHub.copilot-chat#copilotWelcome\\\",{\\\"firstSeen\\\":1753744772899,\\\"stepIDs\\\":[\\\"copilot.setup.signIn\\\",\\\"copilot.setup.signInNoAction\\\",\\\"copilot.setup.signUp\\\",\\\"copilot.setup.signUpNoAction\\\",\\\"copilot.panelChat\\\",\\\"copilot.edits\\\",\\\"copilot.firstSuggest\\\",\\\"copilot.inlineChatNotMac\\\",\\\"copilot.inlineChatMac\\\",\\\"copilot.sparkle\\\"],\\\"manaullyOpened\\\":false}],[\\\"saoudrizwan.claude-dev#ClineWalkthrough\\\",{\\\"firstSeen\\\":1753744772899,\\\"stepIDs\\\":[\\\"welcome\\\",\\\"learn\\\",\\\"advanced-features\\\",\\\"mcp\\\",\\\"getting-started\\\"],\\\"manaullyOpened\\\":false}]]\",\"codegeexLogin-NevinXuHui\":\"[{\\\"id\\\":\\\"aminer.codegeex\\\",\\\"name\\\":\\\"CodeGeeX: AI Code AutoComplete, Chat, Auto Comment\\\",\\\"allowed\\\":true}]\",\"codegeexLogin-徐辉\":\"[{\\\"id\\\":\\\"aminer.codegeex\\\",\\\"name\\\":\\\"CodeGeeX: AI Code AutoComplete, Chat, Auto Comment\\\",\\\"allowed\\\":true}]\",\"codeium_auth-hui xu\":\"[{\\\"id\\\":\\\"codeium.codeium\\\",\\\"name\\\":\\\"Codeium: AI Coding Autocomplete and Chat for Python, Javascript, Typescript, Java, Go, and more\\\",\\\"allowed\\\":true}]\",\"commandPalette.mru.cache\":\"{\\\"usesLRU\\\":true,\\\"entries\\\":[{\\\"key\\\":\\\"tabnine.signInUsingAuthToken\\\",\\\"value\\\":17},{\\\"key\\\":\\\"go.tools.install\\\",\\\"value\\\":53},{\\\"key\\\":\\\"clangd.install\\\",\\\"value\\\":73},{\\\"key\\\":\\\"clangd.restart\\\",\\\"value\\\":74}]}\",\"commandPalette.mru.counter\":\"75\",\"extensionTips/promptedExecutableTips\":\"{\\\"wsl\\\":[\\\"ms-vscode-remote.remote-wsl\\\"]}\",\"https://sourcegraph.com-SOURCEGRAPH_AUTH\":\"[{\\\"id\\\":\\\"sourcegraph.sourcegraph\\\",\\\"name\\\":\\\"Sourcegraph\\\",\\\"allowed\\\":true}]\",\"memento/workbench.editor.keybindings\":\"{\\\"searchHistory\\\":[\\\"fu zu\\\",\\\"辅助\\\",\\\"复制\\\",\\\"qi\\\",\\\" \\\",\\\" qian jin\\\",\\\" 前进\\\",\\\"ch\\\",\\\"查找\\\",\\\"nian\\\",\\\"nian'tia\\\",\\\"粘\\\",\\\"粘贴\\\"]}\",\"mergeEditorCloseWithConflicts\":\"true\",\"remote.explorerType\":\"wsl\",\"snippets.usageTimestamps\":\"[[\\\"snippets/javascript.code-snippets/Function Statement\\\",1707722118917],[\\\"snippets/c.json/else\\\",1709191647378],[\\\"snippets/snippets.json/misc.animation-frame\\\",1727500270886],[\\\"snippets/snippets.json/input.password\\\",1730980012153],[\\\"snippets/builtins.json/HOSTFILE\\\",1731067981210],[\\\"snippets/snippets.json/else\\\",1731501001449],[\\\"snippets/ros2_rclcpp_snippets.json/rclcpp_info_OOP\\\",1739961999862]]\",\"<EMAIL>\":\"[{\\\"id\\\":\\\"tabnine.tabnine-vscode\\\",\\\"name\\\":\\\"Tabnine AI\\\",\\\"allowed\\\":true}]\",\"workbench.statusbar.hidden\":\"[\\\"eamodio.gitlens.gitlens.plus.subscription\\\",\\\"GitHub.copilot.status\\\"]\",\"workbench.view.extension.PowerShell.state.hidden\":\"[{\\\"id\\\":\\\"PowerShellCommands\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.fnMapExplorer.state.hidden\":\"[{\\\"id\\\":\\\"fnMapView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"fnMapWebView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.localHistory.state.hidden\":\"[{\\\"id\\\":\\\"treeLocalHistory\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.project-dashboard.state.hidden\":\"[{\\\"id\\\":\\\"projectDashboard.dashboard\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.test.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.testing\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.view.testCoverage\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"go.test.profile\\\",\\\"isHidden\\\":false}]\",\"workbench.view.sync.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.sync.conflicts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.sync.remoteActivity\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.sync.machines\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.sync.localActivity\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.sync.troubleshoot\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.sync.externalActivity\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.a3e2775a-da79-4fe8-b19d-0640cb4eeae2.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false}]\",\"nps/isCandidate\":\"false\",\"nps/skipVersion\":\"1.91.1\",\"tabs-list-width-horizontal\":\"120\",\"extensions.donotAutoUpdate\":\"[\\\"codezombiech.gitignore\\\"]\",\"workbench.welcomePage.hiddenCategories\":\"[\\\"ms-vscode-remote.remote-wsl#wslWalkthrough\\\",\\\"GitHub.copilot-chat#copilotWelcome\\\"]\",\"workbench.chat.hideMovedChatWelcomeView\":\"true\",\"workbench.panel.chatEditing.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.edits\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.cspell-info-explorer.state.hidden\":\"[{\\\"id\\\":\\\"cSpellInfoView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cSpellRegExpView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.cspellPanel.state.hidden\":\"[{\\\"id\\\":\\\"cSpellIssuesViewByFile\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cSpellIssuesViewByIssue\\\",\\\"isHidden\\\":false}]\",\"extension.features.state\":\"{\\\"github.copilot-chat\\\":{\\\"copilot\\\":{\\\"disabled\\\":false,\\\"accessTimes\\\":[1753494273121,1753494320663,1753583352293,1753670672914,1753677143502,1753680381686]}}}\",\"languageModelAccess.gpt-4o\":\"[\\\"github.copilot-chat\\\"]\",\"languageModelStats.gpt-4o\":\"{\\\"extensions\\\":[{\\\"extensionId\\\":\\\"GitHub.copilot-chat\\\",\\\"requestCount\\\":16,\\\"tokenCount\\\":15906,\\\"participants\\\":[]}]}\",\"workbench.view.extension.outlinePlusExplorer.state.hidden\":\"[{\\\"id\\\":\\\"outlinePlusWebView\\\",\\\"isHidden\\\":false}]\",\"languageModelAccess.gpt-4o-mini\":\"[\\\"github.copilot-chat\\\"]\",\"languageModelStats.gpt-4o-mini\":\"{\\\"extensions\\\":[{\\\"extensionId\\\":\\\"GitHub.copilot-chat\\\",\\\"requestCount\\\":18,\\\"tokenCount\\\":21875,\\\"participants\\\":[]}]}\",\"menu.hiddenCommands\":\"{\\\"ViewTitle\\\":[\\\"api:outlinePlus.viewTitle\\\"]}\",\"workbench.view.extension.ros2View.state.hidden\":\"[{\\\"id\\\":\\\"ros2TopicsView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.marscode.state.hidden\":\"[{\\\"id\\\":\\\"MarsCodeChatView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.vscode-serial-monitor-tools.state.hidden\":\"[{\\\"id\\\":\\\"vscode-serial-monitor.monitor0\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-serial-monitor.monitor1\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-serial-monitor.monitor2\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.liveshare.state.hidden\":\"[{\\\"id\\\":\\\"liveshare.session\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.help\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.devtools\\\",\\\"isHidden\\\":false}]\",\"github-NevinXuHui\":\"[]\",\"auth0-\":\"[{\\\"id\\\":\\\"marscode.marscode-extension\\\",\\\"name\\\":\\\"MarsCode AI: Coding Assistant\\\",\\\"allowed\\\":true}]\",\"workbench.view.extension.references-view.state.hidden\":\"[{\\\"id\\\":\\\"references-view.tree\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"CppReferencesView\\\",\\\"isHidden\\\":false}]\",\"extensions.trustedPublishers\":\"{\\\"003\\\":{\\\"publisher\\\":\\\"003\\\",\\\"publisherDisplayName\\\":\\\"SuperMonster003\\\"},\\\"1nvitr0\\\":{\\\"publisher\\\":\\\"1nvitr0\\\",\\\"publisherDisplayName\\\":\\\"Aram Becker\\\"},\\\"aaron-bond\\\":{\\\"publisher\\\":\\\"aaron-bond\\\",\\\"publisherDisplayName\\\":\\\"Aaron Bond\\\"},\\\"adam-bender\\\":{\\\"publisher\\\":\\\"adam-bender\\\",\\\"publisherDisplayName\\\":\\\"Adam Bender\\\"},\\\"adriano-markovic\\\":{\\\"publisher\\\":\\\"adriano-markovic\\\",\\\"publisherDisplayName\\\":\\\"Adriano Markovic\\\"},\\\"ajshort\\\":{\\\"publisher\\\":\\\"ajshort\\\",\\\"publisherDisplayName\\\":\\\"ajshort\\\"},\\\"akiramiyakoda\\\":{\\\"publisher\\\":\\\"akiramiyakoda\\\",\\\"publisherDisplayName\\\":\\\"Akira Miyakoda\\\"},\\\"alanmbarr\\\":{\\\"publisher\\\":\\\"alanmbarr\\\",\\\"publisherDisplayName\\\":\\\"Alan Barr\\\"},\\\"alefragnani\\\":{\\\"publisher\\\":\\\"alefragnani\\\",\\\"publisherDisplayName\\\":\\\"Alessandro Fragnani\\\"},\\\"alessandrosofia\\\":{\\\"publisher\\\":\\\"alessandrosofia\\\",\\\"publisherDisplayName\\\":\\\"Alessandro Sofia\\\"},\\\"alexclewontin\\\":{\\\"publisher\\\":\\\"alexclewontin\\\",\\\"publisherDisplayName\\\":\\\"Alex C. Lewontin\\\"},\\\"alexey-strakh\\\":{\\\"publisher\\\":\\\"alexey-strakh\\\",\\\"publisherDisplayName\\\":\\\"Alexey-Strakh\\\"},\\\"alibaba-cloud\\\":{\\\"publisher\\\":\\\"alibaba-cloud\\\",\\\"publisherDisplayName\\\":\\\"Alibaba-Cloud\\\"},\\\"aminer\\\":{\\\"publisher\\\":\\\"aminer\\\",\\\"publisherDisplayName\\\":\\\"Zhipu AI\\\"},\\\"anjali\\\":{\\\"publisher\\\":\\\"anjali\\\",\\\"publisherDisplayName\\\":\\\"Anjali\\\"},\\\"antiantisepticeye\\\":{\\\"publisher\\\":\\\"antiantisepticeye\\\",\\\"publisherDisplayName\\\":\\\"AntiAntiSepticeye\\\"},\\\"asurance\\\":{\\\"publisher\\\":\\\"asurance\\\",\\\"publisherDisplayName\\\":\\\"asurance\\\"},\\\"asvetliakov\\\":{\\\"publisher\\\":\\\"asvetliakov\\\",\\\"publisherDisplayName\\\":\\\"Alexey Svetliakov\\\"},\\\"batisteo\\\":{\\\"publisher\\\":\\\"batisteo\\\",\\\"publisherDisplayName\\\":\\\"Baptiste Darthenay\\\"},\\\"bdavs\\\":{\\\"publisher\\\":\\\"bdavs\\\",\\\"publisherDisplayName\\\":\\\"bdavs\\\"},\\\"beishanyufu\\\":{\\\"publisher\\\":\\\"beishanyufu\\\",\\\"publisherDisplayName\\\":\\\"beishanyufu\\\"},\\\"benrogerswpg\\\":{\\\"publisher\\\":\\\"benrogerswpg\\\",\\\"publisherDisplayName\\\":\\\"Ben Rogers\\\"},\\\"bierner\\\":{\\\"publisher\\\":\\\"bierner\\\",\\\"publisherDisplayName\\\":\\\"Matt Bierner\\\"},\\\"bukas\\\":{\\\"publisher\\\":\\\"bukas\\\",\\\"publisherDisplayName\\\":\\\"bukas\\\"},\\\"buuug7\\\":{\\\"publisher\\\":\\\"buuug7\\\",\\\"publisherDisplayName\\\":\\\"buuug7\\\"},\\\"cheshirekow\\\":{\\\"publisher\\\":\\\"cheshirekow\\\",\\\"publisherDisplayName\\\":\\\"cheshirekow\\\"},\\\"chouzz\\\":{\\\"publisher\\\":\\\"chouzz\\\",\\\"publisherDisplayName\\\":\\\"Chouzz\\\"},\\\"chris-noring\\\":{\\\"publisher\\\":\\\"chris-noring\\\",\\\"publisherDisplayName\\\":\\\"Chris Noring\\\"},\\\"christian-kohler\\\":{\\\"publisher\\\":\\\"christian-kohler\\\",\\\"publisherDisplayName\\\":\\\"Christian Kohler\\\"},\\\"cliffordfajardo\\\":{\\\"publisher\\\":\\\"cliffordfajardo\\\",\\\"publisherDisplayName\\\":\\\"Clifford Fajardo\\\"},\\\"codeinchinese\\\":{\\\"publisher\\\":\\\"codeinchinese\\\",\\\"publisherDisplayName\\\":\\\"中文编程\\\"},\\\"codeium\\\":{\\\"publisher\\\":\\\"codeium\\\",\\\"publisherDisplayName\\\":\\\"Codeium\\\"},\\\"codestream\\\":{\\\"publisher\\\":\\\"codestream\\\",\\\"publisherDisplayName\\\":\\\"New Relic\\\"},\\\"codezombiech\\\":{\\\"publisher\\\":\\\"codezombiech\\\",\\\"publisherDisplayName\\\":\\\"CodeZombie\\\"},\\\"coolchyni\\\":{\\\"publisher\\\":\\\"coolchyni\\\",\\\"publisherDisplayName\\\":\\\"coolchyni\\\"},\\\"cschlosser\\\":{\\\"publisher\\\":\\\"cschlosser\\\",\\\"publisherDisplayName\\\":\\\"Christoph Schlosser\\\"},\\\"ctf0\\\":{\\\"publisher\\\":\\\"ctf0\\\",\\\"publisherDisplayName\\\":\\\"ctf0\\\"},\\\"cuixiaorui\\\":{\\\"publisher\\\":\\\"cuixiaorui\\\",\\\"publisherDisplayName\\\":\\\"cuixiaorui\\\"},\\\"cweijan\\\":{\\\"publisher\\\":\\\"cweijan\\\",\\\"publisherDisplayName\\\":\\\"Weijan Chen\\\"},\\\"damiankoper\\\":{\\\"publisher\\\":\\\"damiankoper\\\",\\\"publisherDisplayName\\\":\\\"DamianKoper\\\"},\\\"danielpinto8zz6\\\":{\\\"publisher\\\":\\\"danielpinto8zz6\\\",\\\"publisherDisplayName\\\":\\\"danielpinto8zz6\\\"},\\\"darkriszty\\\":{\\\"publisher\\\":\\\"darkriszty\\\",\\\"publisherDisplayName\\\":\\\"Krisztian Daroczi\\\"},\\\"david-rickard\\\":{\\\"publisher\\\":\\\"david-rickard\\\",\\\"publisherDisplayName\\\":\\\"David Rickard\\\"},\\\"davidanson\\\":{\\\"publisher\\\":\\\"davidanson\\\",\\\"publisherDisplayName\\\":\\\"David Anson\\\"},\\\"deitry\\\":{\\\"publisher\\\":\\\"deitry\\\",\\\"publisherDisplayName\\\":\\\"deitry\\\"},\\\"donjayamanne\\\":{\\\"publisher\\\":\\\"donjayamanne\\\",\\\"publisherDisplayName\\\":\\\"Don Jayamanne\\\"},\\\"dotiful\\\":{\\\"publisher\\\":\\\"dotiful\\\",\\\"publisherDisplayName\\\":\\\"Art Dev\\\"},\\\"draivin\\\":{\\\"publisher\\\":\\\"draivin\\\",\\\"publisherDisplayName\\\":\\\"draivin\\\"},\\\"dzhavat\\\":{\\\"publisher\\\":\\\"dzhavat\\\",\\\"publisherDisplayName\\\":\\\"Dzhavat Ushev\\\"},\\\"eamodio\\\":{\\\"publisher\\\":\\\"eamodio\\\",\\\"publisherDisplayName\\\":\\\"GitKraken\\\"},\\\"editorconfig\\\":{\\\"publisher\\\":\\\"editorconfig\\\",\\\"publisherDisplayName\\\":\\\"EditorConfig\\\"},\\\"emmanuelbeziat\\\":{\\\"publisher\\\":\\\"emmanuelbeziat\\\",\\\"publisherDisplayName\\\":\\\"Emmanuel Béziat\\\"},\\\"esbenp\\\":{\\\"publisher\\\":\\\"esbenp\\\",\\\"publisherDisplayName\\\":\\\"Prettier\\\"},\\\"exiasr\\\":{\\\"publisher\\\":\\\"exiasr\\\",\\\"publisherDisplayName\\\":\\\"Michael Lin\\\"},\\\"extr0py\\\":{\\\"publisher\\\":\\\"extr0py\\\",\\\"publisherDisplayName\\\":\\\"extr0py\\\"},\\\"fabiospampinato\\\":{\\\"publisher\\\":\\\"fabiospampinato\\\",\\\"publisherDisplayName\\\":\\\"Fabio Spampinato\\\"},\\\"felipecaputo\\\":{\\\"publisher\\\":\\\"felipecaputo\\\",\\\"publisherDisplayName\\\":\\\"Felipe Caputo\\\"},\\\"formulahendry\\\":{\\\"publisher\\\":\\\"formulahendry\\\",\\\"publisherDisplayName\\\":\\\"Jun Han\\\"},\\\"foxundermoon\\\":{\\\"publisher\\\":\\\"foxundermoon\\\",\\\"publisherDisplayName\\\":\\\"foxundermoon\\\"},\\\"franneck94\\\":{\\\"publisher\\\":\\\"franneck94\\\",\\\"publisherDisplayName\\\":\\\"franneck94\\\"},\\\"gengjian1203\\\":{\\\"publisher\\\":\\\"gengjian1203\\\",\\\"publisherDisplayName\\\":\\\"gengjian1203\\\"},\\\"george-alisson\\\":{\\\"publisher\\\":\\\"george-alisson\\\",\\\"publisherDisplayName\\\":\\\"George Oliveira\\\"},\\\"gerrnperl\\\":{\\\"publisher\\\":\\\"gerrnperl\\\",\\\"publisherDisplayName\\\":\\\"Gerrnperl\\\"},\\\"go2sh\\\":{\\\"publisher\\\":\\\"go2sh\\\",\\\"publisherDisplayName\\\":\\\"Christoph Seitz\\\"},\\\"golang\\\":{\\\"publisher\\\":\\\"golang\\\",\\\"publisherDisplayName\\\":\\\"Go Team at Google\\\"},\\\"gruntfuggly\\\":{\\\"publisher\\\":\\\"gruntfuggly\\\",\\\"publisherDisplayName\\\":\\\"Gruntfuggly\\\"},\\\"guyutongxue\\\":{\\\"publisher\\\":\\\"guyutongxue\\\",\\\"publisherDisplayName\\\":\\\"Guyutongxue\\\"},\\\"hancel\\\":{\\\"publisher\\\":\\\"hancel\\\",\\\"publisherDisplayName\\\":\\\"Hancel.Lin\\\"},\\\"hars\\\":{\\\"publisher\\\":\\\"hars\\\",\\\"publisherDisplayName\\\":\\\"Harsh\\\"},\\\"hnw\\\":{\\\"publisher\\\":\\\"hnw\\\",\\\"publisherDisplayName\\\":\\\"hnw\\\"},\\\"howardzuo\\\":{\\\"publisher\\\":\\\"howardzuo\\\",\\\"publisherDisplayName\\\":\\\"Howard.Zuo\\\"},\\\"huizhou\\\":{\\\"publisher\\\":\\\"huizhou\\\",\\\"publisherDisplayName\\\":\\\"Hui Zhou\\\"},\\\"ibm\\\":{\\\"publisher\\\":\\\"ibm\\\",\\\"publisherDisplayName\\\":\\\"IBM\\\"},\\\"in4margaret\\\":{\\\"publisher\\\":\\\"in4margaret\\\",\\\"publisherDisplayName\\\":\\\"in4margaret\\\"},\\\"ionutvmi\\\":{\\\"publisher\\\":\\\"ionutvmi\\\",\\\"publisherDisplayName\\\":\\\"Mihai Vilcu\\\"},\\\"jacobdufault\\\":{\\\"publisher\\\":\\\"jacobdufault\\\",\\\"publisherDisplayName\\\":\\\"jacobdufault\\\"},\\\"jaehyunshim\\\":{\\\"publisher\\\":\\\"jaehyunshim\\\",\\\"publisherDisplayName\\\":\\\"JaehyunShim\\\"},\\\"jannchie\\\":{\\\"publisher\\\":\\\"jannchie\\\",\\\"publisherDisplayName\\\":\\\"Jannchie\\\"},\\\"jannek-aalto\\\":{\\\"publisher\\\":\\\"jannek-aalto\\\",\\\"publisherDisplayName\\\":\\\"jannek-aalto\\\"},\\\"jeff-hykin\\\":{\\\"publisher\\\":\\\"jeff-hykin\\\",\\\"publisherDisplayName\\\":\\\"Jeff Hykin\\\"},\\\"josetr\\\":{\\\"publisher\\\":\\\"josetr\\\",\\\"publisherDisplayName\\\":\\\"Jose Torres\\\"},\\\"jsshou\\\":{\\\"publisher\\\":\\\"jsshou\\\",\\\"publisherDisplayName\\\":\\\"JSShou\\\"},\\\"julianiaquinandi\\\":{\\\"publisher\\\":\\\"julianiaquinandi\\\",\\\"publisherDisplayName\\\":\\\"Julian Iaquinandi\\\"},\\\"kbysiec\\\":{\\\"publisher\\\":\\\"kbysiec\\\",\\\"publisherDisplayName\\\":\\\"Kamil Bysiec\\\"},\\\"kevinrose\\\":{\\\"publisher\\\":\\\"kevinrose\\\",\\\"publisherDisplayName\\\":\\\"Kevin Rose\\\"},\\\"keyring\\\":{\\\"publisher\\\":\\\"keyring\\\",\\\"publisherDisplayName\\\":\\\"keyring\\\"},\\\"kr4is\\\":{\\\"publisher\\\":\\\"kr4is\\\",\\\"publisherDisplayName\\\":\\\"Kr4is\\\"},\\\"krinql\\\":{\\\"publisher\\\":\\\"krinql\\\",\\\"publisherDisplayName\\\":\\\"Krinql\\\"},\\\"kruemelkatze\\\":{\\\"publisher\\\":\\\"kruemelkatze\\\",\\\"publisherDisplayName\\\":\\\"Kruemelkatze\\\"},\\\"l-i-v\\\":{\\\"publisher\\\":\\\"l-i-v\\\",\\\"publisherDisplayName\\\":\\\"L-I-V\\\"},\\\"leojhonsong\\\":{\\\"publisher\\\":\\\"leojhonsong\\\",\\\"publisherDisplayName\\\":\\\"LeoJhonSong\\\"},\\\"lglong519\\\":{\\\"publisher\\\":\\\"lglong519\\\",\\\"publisherDisplayName\\\":\\\"Glenn (lglong519)\\\"},\\\"lihuiwang\\\":{\\\"publisher\\\":\\\"lihuiwang\\\",\\\"publisherDisplayName\\\":\\\"lihuiwang\\\"},\\\"llvm-vs-code-extensions\\\":{\\\"publisher\\\":\\\"llvm-vs-code-extensions\\\",\\\"publisherDisplayName\\\":\\\"LLVM\\\"},\\\"madmous\\\":{\\\"publisher\\\":\\\"madmous\\\",\\\"publisherDisplayName\\\":\\\"madmous\\\"},\\\"mads-hartmann\\\":{\\\"publisher\\\":\\\"mads-hartmann\\\",\\\"publisherDisplayName\\\":\\\"Mads Hartmann\\\"},\\\"maelvalais\\\":{\\\"publisher\\\":\\\"maelvalais\\\",\\\"publisherDisplayName\\\":\\\"maelvalais\\\"},\\\"maketes\\\":{\\\"publisher\\\":\\\"maketes\\\",\\\"publisherDisplayName\\\":\\\"maketes\\\"},\\\"marscode\\\":{\\\"publisher\\\":\\\"marscode\\\",\\\"publisherDisplayName\\\":\\\"MarsCode\\\"},\\\"mccarter\\\":{\\\"publisher\\\":\\\"mccarter\\\",\\\"publisherDisplayName\\\":\\\"McCarter\\\"},\\\"meronz\\\":{\\\"publisher\\\":\\\"meronz\\\",\\\"publisherDisplayName\\\":\\\"meronz\\\"},\\\"mhutchie\\\":{\\\"publisher\\\":\\\"mhutchie\\\",\\\"publisherDisplayName\\\":\\\"mhutchie\\\"},\\\"microhobby\\\":{\\\"publisher\\\":\\\"microhobby\\\",\\\"publisherDisplayName\\\":\\\"MicroHobby\\\"},\\\"mintlify\\\":{\\\"publisher\\\":\\\"mintlify\\\",\\\"publisherDisplayName\\\":\\\"Mintlify\\\"},\\\"mishkinf\\\":{\\\"publisher\\\":\\\"mishkinf\\\",\\\"publisherDisplayName\\\":\\\"Mishkin Faustini\\\"},\\\"mitaki28\\\":{\\\"publisher\\\":\\\"mitaki28\\\",\\\"publisherDisplayName\\\":\\\"Yasuaki MITANI\\\"},\\\"mkxml\\\":{\\\"publisher\\\":\\\"mkxml\\\",\\\"publisherDisplayName\\\":\\\"Matheus Kautzmann\\\"},\\\"mohsen1\\\":{\\\"publisher\\\":\\\"mohsen1\\\",\\\"publisherDisplayName\\\":\\\"Mohsen Azimi\\\"},\\\"moshfeu\\\":{\\\"publisher\\\":\\\"moshfeu\\\",\\\"publisherDisplayName\\\":\\\"MoshFeu\\\"},\\\"natizyskunk\\\":{\\\"publisher\\\":\\\"natizyskunk\\\",\\\"publisherDisplayName\\\":\\\"Natizyskunk\\\"},\\\"naumovs\\\":{\\\"publisher\\\":\\\"naumovs\\\",\\\"publisherDisplayName\\\":\\\"Sergii N\\\"},\\\"nicolaspolomack\\\":{\\\"publisher\\\":\\\"nicolaspolomack\\\",\\\"publisherDisplayName\\\":\\\"Nicolas Polomack\\\"},\\\"niudai\\\":{\\\"publisher\\\":\\\"niudai\\\",\\\"publisherDisplayName\\\":\\\"niudai\\\"},\\\"nonanonno\\\":{\\\"publisher\\\":\\\"nonanonno\\\",\\\"publisherDisplayName\\\":\\\"nonanonno\\\"},\\\"obkoro1\\\":{\\\"publisher\\\":\\\"obkoro1\\\",\\\"publisherDisplayName\\\":\\\"OBKoro1\\\"},\\\"oderwat\\\":{\\\"publisher\\\":\\\"oderwat\\\",\\\"publisherDisplayName\\\":\\\"oderwat\\\"},\\\"orangex4\\\":{\\\"publisher\\\":\\\"orangex4\\\",\\\"publisherDisplayName\\\":\\\"OrangeX4\\\"},\\\"orepor\\\":{\\\"publisher\\\":\\\"orepor\\\",\\\"publisherDisplayName\\\":\\\"Ore Poran\\\"},\\\"paragdiwan\\\":{\\\"publisher\\\":\\\"paragdiwan\\\",\\\"publisherDisplayName\\\":\\\"Parag Diwan\\\"},\\\"parthr2031\\\":{\\\"publisher\\\":\\\"parthr2031\\\",\\\"publisherDisplayName\\\":\\\"Parth Rastogi\\\"},\\\"penagos\\\":{\\\"publisher\\\":\\\"penagos\\\",\\\"publisherDisplayName\\\":\\\"Luis Penagos\\\"},\\\"pierre-payen\\\":{\\\"publisher\\\":\\\"pierre-payen\\\",\\\"publisherDisplayName\\\":\\\"Pierre Payen\\\"},\\\"pkief\\\":{\\\"publisher\\\":\\\"pkief\\\",\\\"publisherDisplayName\\\":\\\"Philipp Kief\\\"},\\\"rajivshah01234\\\":{\\\"publisher\\\":\\\"rajivshah01234\\\",\\\"publisherDisplayName\\\":\\\"Rajiv Sah\\\"},\\\"rangav\\\":{\\\"publisher\\\":\\\"rangav\\\",\\\"publisherDisplayName\\\":\\\"Thunder Client\\\"},\\\"redhat\\\":{\\\"publisher\\\":\\\"redhat\\\",\\\"publisherDisplayName\\\":\\\"Red Hat\\\"},\\\"redjue\\\":{\\\"publisher\\\":\\\"redjue\\\",\\\"publisherDisplayName\\\":\\\"redjue\\\"},\\\"remisa\\\":{\\\"publisher\\\":\\\"remisa\\\",\\\"publisherDisplayName\\\":\\\"Remisa\\\"},\\\"richterger\\\":{\\\"publisher\\\":\\\"richterger\\\",\\\"publisherDisplayName\\\":\\\"Gerald Richter\\\"},\\\"rlivings39\\\":{\\\"publisher\\\":\\\"rlivings39\\\",\\\"publisherDisplayName\\\":\\\"rlivings39\\\"},\\\"rogalmic\\\":{\\\"publisher\\\":\\\"rogalmic\\\",\\\"publisherDisplayName\\\":\\\"rogalmic\\\"},\\\"rpinski\\\":{\\\"publisher\\\":\\\"rpinski\\\",\\\"publisherDisplayName\\\":\\\"Andreas Weizel\\\"},\\\"rsbondi\\\":{\\\"publisher\\\":\\\"rsbondi\\\",\\\"publisherDisplayName\\\":\\\"Richard Bondi\\\"},\\\"ryu1kn\\\":{\\\"publisher\\\":\\\"ryu1kn\\\",\\\"publisherDisplayName\\\":\\\"Ryuichi Inagaki\\\"},\\\"sandcastle\\\":{\\\"publisher\\\":\\\"sandcastle\\\",\\\"publisherDisplayName\\\":\\\"sandcastle\\\"},\\\"scootersoftware\\\":{\\\"publisher\\\":\\\"scootersoftware\\\",\\\"publisherDisplayName\\\":\\\"Scooter Software\\\"},\\\"sensecoder\\\":{\\\"publisher\\\":\\\"sensecoder\\\",\\\"publisherDisplayName\\\":\\\"SenseCoder\\\"},\\\"shakram02\\\":{\\\"publisher\\\":\\\"shakram02\\\",\\\"publisherDisplayName\\\":\\\"Ahmed Hamdy\\\"},\\\"shardulm94\\\":{\\\"publisher\\\":\\\"shardulm94\\\",\\\"publisherDisplayName\\\":\\\"Shardul Mahadik\\\"},\\\"shd101wyy\\\":{\\\"publisher\\\":\\\"shd101wyy\\\",\\\"publisherDisplayName\\\":\\\"Yiyi Wang\\\"},\\\"shopify\\\":{\\\"publisher\\\":\\\"shopify\\\",\\\"publisherDisplayName\\\":\\\"Shopify\\\"},\\\"sibvic\\\":{\\\"publisher\\\":\\\"sibvic\\\",\\\"publisherDisplayName\\\":\\\"Victor Tereschenko\\\"},\\\"sirtori\\\":{\\\"publisher\\\":\\\"sirtori\\\",\\\"publisherDisplayName\\\":\\\"SirTori\\\"},\\\"sleistner\\\":{\\\"publisher\\\":\\\"sleistner\\\",\\\"publisherDisplayName\\\":\\\"Steffen Leistner\\\"},\\\"slhsxcmy\\\":{\\\"publisher\\\":\\\"slhsxcmy\\\",\\\"publisherDisplayName\\\":\\\"slhsxcmy\\\"},\\\"sourcegraph\\\":{\\\"publisher\\\":\\\"sourcegraph\\\",\\\"publisherDisplayName\\\":\\\"Sourcegraph\\\"},\\\"stannum\\\":{\\\"publisher\\\":\\\"stannum\\\",\\\"publisherDisplayName\\\":\\\"Tin Lam\\\"},\\\"stateful\\\":{\\\"publisher\\\":\\\"stateful\\\",\\\"publisherDisplayName\\\":\\\"stateful\\\"},\\\"streetsidesoftware\\\":{\\\"publisher\\\":\\\"streetsidesoftware\\\",\\\"publisherDisplayName\\\":\\\"Street Side Software\\\"},\\\"sumneko\\\":{\\\"publisher\\\":\\\"sumneko\\\",\\\"publisherDisplayName\\\":\\\"sumneko\\\"},\\\"syw-sustech\\\":{\\\"publisher\\\":\\\"syw-sustech\\\",\\\"publisherDisplayName\\\":\\\"SiyuanWang\\\"},\\\"tabnine\\\":{\\\"publisher\\\":\\\"tabnine\\\",\\\"publisherDisplayName\\\":\\\"TabNine\\\"},\\\"tadayosi\\\":{\\\"publisher\\\":\\\"tadayosi\\\",\\\"publisherDisplayName\\\":\\\"Tadayoshi Sato\\\"},\\\"taiyuuki\\\":{\\\"publisher\\\":\\\"taiyuuki\\\",\\\"publisherDisplayName\\\":\\\"taiyuuki\\\"},\\\"tatosjb\\\":{\\\"publisher\\\":\\\"tatosjb\\\",\\\"publisherDisplayName\\\":\\\"Altair J. Fernandes\\\"},\\\"tdennis4496\\\":{\\\"publisher\\\":\\\"tdennis4496\\\",\\\"publisherDisplayName\\\":\\\"Tyler Dennis\\\"},\\\"tetradresearch\\\":{\\\"publisher\\\":\\\"tetradresearch\\\",\\\"publisherDisplayName\\\":\\\"tetradresearch\\\"},\\\"timonwong\\\":{\\\"publisher\\\":\\\"timonwong\\\",\\\"publisherDisplayName\\\":\\\"Timon Wong\\\"},\\\"truman\\\":{\\\"publisher\\\":\\\"truman\\\",\\\"publisherDisplayName\\\":\\\"truman\\\"},\\\"twxs\\\":{\\\"publisher\\\":\\\"twxs\\\",\\\"publisherDisplayName\\\":\\\"twxs\\\"},\\\"uctakeoff\\\":{\\\"publisher\\\":\\\"uctakeoff\\\",\\\"publisherDisplayName\\\":\\\"Kentaro Ushiyama\\\"},\\\"unbug\\\":{\\\"publisher\\\":\\\"unbug\\\",\\\"publisherDisplayName\\\":\\\"unbug\\\"},\\\"usernamehw\\\":{\\\"publisher\\\":\\\"usernamehw\\\",\\\"publisherDisplayName\\\":\\\"Alexander\\\"},\\\"vadimcn\\\":{\\\"publisher\\\":\\\"vadimcn\\\",\\\"publisherDisplayName\\\":\\\"Vadim Chugunov\\\"},\\\"vincaslt\\\":{\\\"publisher\\\":\\\"vincaslt\\\",\\\"publisherDisplayName\\\":\\\"vincaslt\\\"},\\\"vinirossa\\\":{\\\"publisher\\\":\\\"vinirossa\\\",\\\"publisherDisplayName\\\":\\\"Vinícius Pereira\\\"},\\\"vscode-ext\\\":{\\\"publisher\\\":\\\"vscode-ext\\\",\\\"publisherDisplayName\\\":\\\"vscode-ext\\\"},\\\"vscode-icons-team\\\":{\\\"publisher\\\":\\\"vscode-icons-team\\\",\\\"publisherDisplayName\\\":\\\"VSCode Icons Team\\\"},\\\"vscodevim\\\":{\\\"publisher\\\":\\\"vscodevim\\\",\\\"publisherDisplayName\\\":\\\"vscodevim\\\"},\\\"vsls-contrib\\\":{\\\"publisher\\\":\\\"vsls-contrib\\\",\\\"publisherDisplayName\\\":\\\"Jonathan Carter\\\"},\\\"webfreak\\\":{\\\"publisher\\\":\\\"webfreak\\\",\\\"publisherDisplayName\\\":\\\"WebFreak\\\"},\\\"wenhaohuang\\\":{\\\"publisher\\\":\\\"wenhaohuang\\\",\\\"publisherDisplayName\\\":\\\"黄文浩\\\"},\\\"whensunset\\\":{\\\"publisher\\\":\\\"whensunset\\\",\\\"publisherDisplayName\\\":\\\"WhenSunset\\\"},\\\"wholroyd\\\":{\\\"publisher\\\":\\\"wholroyd\\\",\\\"publisherDisplayName\\\":\\\"wholroyd\\\"},\\\"wintersteiger\\\":{\\\"publisher\\\":\\\"wintersteiger\\\",\\\"publisherDisplayName\\\":\\\"Christoph M. Wintersteiger\\\"},\\\"wlhe\\\":{\\\"publisher\\\":\\\"wlhe\\\",\\\"publisherDisplayName\\\":\\\"wlhe\\\"},\\\"woozy-masta\\\":{\\\"publisher\\\":\\\"woozy-masta\\\",\\\"publisherDisplayName\\\":\\\"WoozyMasta\\\"},\\\"xaver\\\":{\\\"publisher\\\":\\\"xaver\\\",\\\"publisherDisplayName\\\":\\\"Xaver Hellauer\\\"},\\\"xen\\\":{\\\"publisher\\\":\\\"xen\\\",\\\"publisherDisplayName\\\":\\\"Xen\\\"},\\\"xpo\\\":{\\\"publisher\\\":\\\"xpo\\\",\\\"publisherDisplayName\\\":\\\"Xpo Development\\\"},\\\"xshrim\\\":{\\\"publisher\\\":\\\"xshrim\\\",\\\"publisherDisplayName\\\":\\\"xshrim\\\"},\\\"xyz\\\":{\\\"publisher\\\":\\\"xyz\\\",\\\"publisherDisplayName\\\":\\\"xyz\\\"},\\\"yxl\\\":{\\\"publisher\\\":\\\"yxl\\\",\\\"publisherDisplayName\\\":\\\"yxl\\\"},\\\"yzhang\\\":{\\\"publisher\\\":\\\"yzhang\\\",\\\"publisherDisplayName\\\":\\\"Yu Zhang\\\"},\\\"zainchen\\\":{\\\"publisher\\\":\\\"zainchen\\\",\\\"publisherDisplayName\\\":\\\"ZainChen\\\"},\\\"zchrissirhcz\\\":{\\\"publisher\\\":\\\"zchrissirhcz\\\",\\\"publisherDisplayName\\\":\\\"zchrissirhcz\\\"},\\\"zenor\\\":{\\\"publisher\\\":\\\"zenor\\\",\\\"publisherDisplayName\\\":\\\"Antoine aka. Zenor\\\"},\\\"zhaouv\\\":{\\\"publisher\\\":\\\"zhaouv\\\",\\\"publisherDisplayName\\\":\\\"zhaouv\\\"},\\\"zhuangtongfa\\\":{\\\"publisher\\\":\\\"zhuangtongfa\\\",\\\"publisherDisplayName\\\":\\\"binaryify\\\"},\\\"ziyasal\\\":{\\\"publisher\\\":\\\"ziyasal\\\",\\\"publisherDisplayName\\\":\\\"ziyasal\\\"},\\\"zobo\\\":{\\\"publisher\\\":\\\"zobo\\\",\\\"publisherDisplayName\\\":\\\"Damjan Cvetko\\\"},\\\"tencent-cloud\\\":{\\\"publisher\\\":\\\"Tencent-Cloud\\\",\\\"publisherDisplayName\\\":\\\"Tencent Cloud\\\"},\\\"docker\\\":{\\\"publisher\\\":\\\"docker\\\",\\\"publisherDisplayName\\\":\\\"Docker\\\"},\\\"ihomecoder\\\":{\\\"publisher\\\":\\\"iHomeCoder\\\",\\\"publisherDisplayName\\\":\\\"iHomeCoder\\\"},\\\"augment\\\":{\\\"publisher\\\":\\\"augment\\\",\\\"publisherDisplayName\\\":\\\"Augment Computing\\\"},\\\"hybridtalentcomputing\\\":{\\\"publisher\\\":\\\"HybridTalentComputing\\\",\\\"publisherDisplayName\\\":\\\"HybridTalentComputing\\\"},\\\"saoudrizwan\\\":{\\\"publisher\\\":\\\"saoudrizwan\\\",\\\"publisherDisplayName\\\":\\\"Cline\\\"},\\\"anthropic\\\":{\\\"publisher\\\":\\\"anthropic\\\",\\\"publisherDisplayName\\\":\\\"Anthropic\\\"}}\",\"workbench.view.extension.coding-copilot-chat.state.hidden\":\"[{\\\"id\\\":\\\"coding-copilot.webviews.chat\\\",\\\"isHidden\\\":false}]\",\"extensions.dismissedNotifications\":\"[]\",\"chatEditsView.hideMovedEditsView\":\"true\",\"chat.currentLanguageModel.panel\":\"github.copilot-chat/claude-3.5-sonnet\",\"workbench.view.extension.trae.state.hidden\":\"[{\\\"id\\\":\\\"TraeChatView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.containersView.state.hidden\":\"[{\\\"id\\\":\\\"vscode-containers.views.containers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.images\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.registries\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.networks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.volumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.dockerContexts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.help\\\",\\\"isHidden\\\":false}]\",\"chat.currentLanguageModel.panel.isDefault\":\"false\",\"workbench.view.extension.iHomeCoder_sidebar.state.hidden\":\"[{\\\"id\\\":\\\"iHomeCoder.chatView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.augment-chat.state.hidden\":\"[{\\\"id\\\":\\\"augment-chat\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.augment-panel.state.hidden\":\"[{\\\"id\\\":\\\"augment-next-edit\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.dc317488-ed68-4856-acfa-2043ecae1350.state.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false}]\",\"chat.lastChatMode\":\"agent\",\"workbench.view.extension.cline-chinese-ActivityBar.state.hidden\":\"[{\\\"id\\\":\\\"clineChinese.SidebarProvider\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.claude-dev-ActivityBar.state.hidden\":\"[{\\\"id\\\":\\\"claude-dev.SidebarProvider\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.augment-infinity-sidebar.state.hidden\":\"[{\\\"id\\\":\\\"augment-infinity-activation\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.python.state.hidden\":\"[{\\\"id\\\":\\\"python-projects\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"env-managers\\\",\\\"isHidden\\\":false}]\"}}"}